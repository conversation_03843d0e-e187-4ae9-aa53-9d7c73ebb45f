module.exports = {
    root: true,
    parser: 'babel-eslint',
    parserOptions: {
        ecmaVersion: 6,
        ecmaFeatures: {
            experimentalObjectRestSpread: true,
            jsx: true,
        },
        sourceType: 'module',
    },
    env: {
        browser: true,
        node: true,
        es6: true,
    },
    // extends: 'standard',
    extends: [
        'plugin:vue/essential'
    ],

    // required to lint *.vue files
    plugins: ['html'],
    // add your custom rules here
    // it is base on https://github.com/vuejs/eslint-config-vue
    rules: {

    },
}
