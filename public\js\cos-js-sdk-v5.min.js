!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.COS=t():e.COS=t()}(this,function(){return function(e){function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/dist/",t(t.s=3)}([function(e,t,n){"use strict";(function(t){function r(e){return encodeURIComponent(e).replace(/!/g,"%21").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}function o(e){return c(e,function(e){return"object"==typeof e?o(e):e})}function i(e,t){return u(t,function(n,r){e[r]=t[r]}),e}function a(e){return e instanceof Array}function s(e,t){for(var n=!1,r=0;r<e.length;r++)if(t===e[r]){n=!0;break}return n}function u(e,t){for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)}function c(e,t){var n=a(e)?[]:{};for(var r in e)e.hasOwnProperty(r)&&(n[r]=t(e[r],r));return n}function l(e,t){var n=a(e),r=n?[]:{};for(var o in e)e.hasOwnProperty(o)&&t(e[o],o)&&(n?r.push(e[o]):r[o]=e[o]);return r}var d=n(6),f=n(7),p=n(8),h=n(11),g=function(e){e=e||{};var t=e.SecretId,n=e.SecretKey,i=(e.method||e.Method||"get").toLowerCase(),a=e.pathname||e.Key||"/",s=o(e.Query||e.params||{}),u=o(e.Headers||e.headers||{});if(0!==a.indexOf("/")&&(a="/"+a),!t)return console.error("missing param SecretId");if(!n)return console.error("missing param SecretKey");var c=function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t.sort(function(e,t){return e=e.toLowerCase(),t=t.toLowerCase(),e===t?0:e>t?1:-1})},l=function(e){var t,n,o,i=[],a=c(e);for(t=0;t<a.length;t++)n=a[t],o=void 0===e[n]||null===e[n]?"":""+e[n],n=n.toLowerCase(),n=r(n),o=r(o)||"",i.push(n+"="+o);return i.join("&")},d=parseInt((new Date).getTime()/1e3)-1,p=d,h=e.Expires||e.expires;p+=void 0===h?900:1*h||0;var g=t,m=d+";"+p,y=d+";"+p,v=c(u).join(";").toLowerCase(),C=c(s).join(";").toLowerCase(),x=f.HmacSHA1(y,n).toString(),b=[i,a,l(s),l(u),""].join("\n"),k=["sha1",m,f.SHA1(b).toString(),""].join("\n");return["q-sign-algorithm=sha1","q-ak="+g,"q-sign-time="+m,"q-key-time="+y,"q-header-list="+v,"q-url-param-list="+C,"q-signature="+f.HmacSHA1(k,x).toString()].join("&")},m=function(){},y=function(e){var t={};for(var n in e)e.hasOwnProperty(n)&&void 0!==e[n]&&null!==e[n]&&(t[n]=e[n]);return t},v=function(e,t){var n,r=new FileReader;FileReader.prototype.readAsBinaryString?(n=FileReader.prototype.readAsBinaryString,r.onload=function(){t(this.result)}):FileReader.prototype.readAsArrayBuffer?n=function(e){var n="",r=new FileReader;r.onload=function(e){for(var o=new Uint8Array(r.result),i=o.byteLength,a=0;a<i;a++)n+=String.fromCharCode(o[a]);t(n)},r.readAsArrayBuffer(e)}:console.error("FileReader not support readAsBinaryString"),n.call(r,e)},C=function(e,t){v(e,function(e){var n=d(e,!0);t(null,n)})},x=function(e){var t,n,r,o="";for(t=0,n=e.length/2;t<n;t++)r=parseInt(e[2*t]+e[2*t+1],16),o+=String.fromCharCode(r);return btoa(o)},b=function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},k=function(e,t){var n=t.Bucket,r=t.Region,o=t.Key;if(e.indexOf("Bucket")>-1||"deleteMultipleObject"===e||"multipartList"===e||"listObjectVersions"===e){if(!n)return"Bucket";if(!r)return"Region"}else if(e.indexOf("Object")>-1||e.indexOf("multipart")>-1||"sliceUploadFile"===e||"abortUploadTask"===e){if(!n)return"Bucket";if(!r)return"Region";if(!o)return"Key"}return!1},S=function(e,t){return function(n,r){if("function"==typeof n&&(r=n,n={}),n=i({},n),"getAuth"!==e&&"getObjectUrl"!==e){var o=n.Headers||{};n&&"object"==typeof n&&(!function(){for(var e in n)n.hasOwnProperty(e)&&e.indexOf("x-cos-")>-1&&(o[e]=n[e])}(),o["x-cos-mfa"]=n.MFA,o["Content-MD5"]=n.ContentMD5,o["Content-Length"]=n.ContentLength,o["Content-Type"]=n.ContentType,o.Expect=n.Expect,o.Expires=n.Expires,o["Cache-Control"]=n.CacheControl,o["Content-Disposition"]=n.ContentDisposition,o["Content-Encoding"]=n.ContentEncoding,o.Range=n.Range,o["If-Modified-Since"]=n.IfModifiedSince,o["If-Unmodified-Since"]=n.IfUnmodifiedSince,o["If-Match"]=n.IfMatch,o["If-None-Match"]=n.IfNoneMatch,o["x-cos-copy-source"]=n.CopySource,o["x-cos-copy-source-Range"]=n.CopySourceRange,o["x-cos-metadata-directive"]=n.MetadataDirective,o["x-cos-copy-source-If-Modified-Since"]=n.CopySourceIfModifiedSince,o["x-cos-copy-source-If-Unmodified-Since"]=n.CopySourceIfUnmodifiedSince,o["x-cos-copy-source-If-Match"]=n.CopySourceIfMatch,o["x-cos-copy-source-If-None-Match"]=n.CopySourceIfNoneMatch,o["x-cos-acl"]=n.ACL,o["x-cos-grant-read"]=n.GrantRead,o["x-cos-grant-write"]=n.GrantWrite,o["x-cos-grant-full-control"]=n.GrantFullControl,o["x-cos-grant-read-acp"]=n.GrantReadAcp,o["x-cos-grant-write-acp"]=n.GrantWriteAcp,o["x-cos-storage-class"]=n.StorageClass,o["x-cos-server-side-encryption-customer-algorithm"]=n.SSECustomerAlgorithm,o["x-cos-server-side-encryption-customer-key"]=n.SSECustomerKey,o["x-cos-server-side-encryption-customer-key-MD5"]=n.SSECustomerKeyMD5,o["x-cos-server-side-encryption"]=n.ServerSideEncryption,o["x-cos-server-side-encryption-cos-kms-key-id"]=n.SSEKMSKeyId,o["x-cos-server-side-encryption-context"]=n.SSEContext,n.Headers=y(o))}var a=function(e){return e&&e.headers&&(e.headers["x-cos-version-id"]&&(e.VersionId=e.headers["x-cos-version-id"]),e.headers["x-cos-delete-marker"]&&(e.DeleteMarker=e.headers["x-cos-delete-marker"])),e},s=function(e,t){r&&r(a(e),a(t))};if("getService"!==e&&"abortUploadTask"!==e){var u;if(u=k(e,n))return void s({error:"missing param "+u});if(n.Region){if(n.Region.indexOf("cos.")>-1)return void s({error:'param Region should not be start with "cos."'});if(!/^([a-z\d-]+)$/.test(n.Region))return void s({error:"Region format error."});this.options.CompatibilityMode||-1!==n.Region.indexOf("-")||"yfb"===n.Region||"default"===n.Region||console.warn("param Region format error, find help here: https://cloud.tencent.com/document/product/436/6224")}if(n.Bucket){if(!/^([a-z\d-]+)-(\d+)$/.test(n.Bucket))if(n.AppId)n.Bucket=n.Bucket+"-"+n.AppId;else{if(!this.options.AppId)return void s({error:'Bucket should format as "test-1250000000".'});n.Bucket=n.Bucket+"-"+this.options.AppId}n.AppId&&(console.warn('warning: AppId has been deprecated, Please put it at the end of parameter Bucket(E.g Bucket:"test-1250000000" ).'),delete n.AppId)}n.Key&&"/"===n.Key.substr(0,1)&&(n.Key=n.Key.substr(1))}var c=t.call(this,n,s);if("getAuth"===e||"getObjectUrl"===e)return c}},w=function(e,t){function n(){if(o=0,t&&"function"==typeof t){r=Date.now();var n,i=Math.max(0,Math.round((s-a)/((r-u)/1e3)*100)/100);n=0===s&&0===e?1:Math.round(s/e*100)/100||0,u=r,a=s;try{t({loaded:s,total:e,speed:i,percent:n})}catch(e){}}}var r,o,i=this,a=0,s=0,u=Date.now();return function(t,r){if(t&&(s=t.loaded,e=t.total),r)clearTimeout(o),n();else{if(o)return;o=setTimeout(n,i.options.ProgressInterval)}}},T=function(e,n,r){var o;if(R.isBrowser){if("string"==typeof n.Body&&(n.Body=new t.Blob([n.Body])),!(n.Body instanceof t.File||n.Body instanceof t.Blob))return void r({error:"params body format error, Only allow File|Blob|String."});o=n.Body.size}else{if("sliceUploadFile"===e)return n.FilePath?void fs.stat(n.FilePath,function(e,t){if(e){if(void 0===n.ContentLength)return void r(e);o=n.ContentLength}else n.FileStat=t,n.FileStat.FilePath=n.FilePath,o=t.size;n.ContentLength=o=o||0,r(null,o)}):void r({error:"missing param FilePath"});if(void 0===n.Body)return void r({error:"missing param Body"});if("string"==typeof n.Body&&(n.Body=t.Buffer.from(n.Body)),n.Body instanceof t.Buffer)o=n.Body.length;else{if("function"!=typeof n.Body.pipe)return void r({error:"params Body format error, Only allow Buffer|Stream|String."});o=void 0===n.ContentLength?void 0:n.ContentLength}}n.ContentLength=o,r(null,o)},R={noop:m,apiWrapper:S,getAuth:g,xml2json:p,json2xml:h,md5:d,clearKey:y,getFileMd5:C,binaryBase64:x,extend:i,isArray:a,isInArray:s,each:u,map:c,filter:l,clone:o,uuid:b,throttleOnProgress:w,getFileSize:T,isBrowser:!!t.document};R.localStorage=t.localStorage,R.fileSlice=function(e,t,n){return e.slice?e.slice(t,n):e.mozSlice?e.mozSlice(t,n):e.webkitSlice?e.webkitSlice(t,n):void 0},R.getFileUUID=function(e,t){return e.name&&e.size&&e.lastModifiedDate&&t?R.md5([e.name,e.size,e.lastModifiedDate,t].join("::")):null},e.exports=R}).call(t,n(5))},function(e,t){function n(e,t){for(var n in e)t[n]=e[n]}function r(e,t){function r(){}var o=e.prototype;if(Object.create){var i=Object.create(t.prototype);o.__proto__=i}o instanceof t||(r.prototype=t.prototype,r=new r,n(o,r),e.prototype=o=r),o.constructor!=e&&("function"!=typeof e&&console.error("unknow Class:"+e),o.constructor=e)}function o(e,t){if(t instanceof Error)var n=t;else n=this,Error.call(this,oe[e]),this.message=oe[e],Error.captureStackTrace&&Error.captureStackTrace(this,o);return n.code=e,t&&(this.message=this.message+": "+t),n}function i(){}function a(e,t){this._node=e,this._refresh=t,s(this)}function s(e){var t=e._node._inc||e._node.ownerDocument._inc;if(e._inc!=t){var r=e._refresh(e._node);H(e,"length",r.length),n(r,e),e._inc=t}}function u(){}function c(e,t){for(var n=e.length;n--;)if(e[n]===t)return n}function l(e,t,n,r){if(r?t[c(t,r)]=n:t[t.length++]=n,e){n.ownerElement=e;var o=e.ownerDocument;o&&(r&&v(o,e,r),y(o,e,n))}}function d(e,t,n){var r=c(t,n);if(!(r>=0))throw o(ae,new Error(e.tagName+"@"+n));for(var i=t.length-1;r<i;)t[r]=t[++r];if(t.length=i,e){var a=e.ownerDocument;a&&(v(a,e,n),n.ownerElement=null)}}function f(e){if(this._features={},e)for(var t in e)this._features=e[t]}function p(){}function h(e){return"<"==e&&"&lt;"||">"==e&&"&gt;"||"&"==e&&"&amp;"||'"'==e&&"&quot;"||"&#"+e.charCodeAt()+";"}function g(e,t){if(t(e))return!0;if(e=e.firstChild)do{if(g(e,t))return!0}while(e=e.nextSibling)}function m(){}function y(e,t,n){e&&e._inc++,"http://www.w3.org/2000/xmlns/"==n.namespaceURI&&(t._nsMap[n.prefix?n.localName:""]=n.value)}function v(e,t,n,r){e&&e._inc++,"http://www.w3.org/2000/xmlns/"==n.namespaceURI&&delete t._nsMap[n.prefix?n.localName:""]}function C(e,t,n){if(e&&e._inc){e._inc++;var r=t.childNodes;if(n)r[r.length++]=n;else{for(var o=t.firstChild,i=0;o;)r[i++]=o,o=o.nextSibling;r.length=i}}}function x(e,t){var n=t.previousSibling,r=t.nextSibling;return n?n.nextSibling=r:e.firstChild=r,r?r.previousSibling=n:e.lastChild=n,C(e.ownerDocument,e),t}function b(e,t,n){var r=t.parentNode;if(r&&r.removeChild(t),t.nodeType===te){var o=t.firstChild;if(null==o)return t;var i=t.lastChild}else o=i=t;var a=n?n.previousSibling:e.lastChild;o.previousSibling=a,i.nextSibling=n,a?a.nextSibling=o:e.firstChild=o,null==n?e.lastChild=i:n.previousSibling=i;do{o.parentNode=e}while(o!==i&&(o=o.nextSibling));return C(e.ownerDocument||e,e),t.nodeType==te&&(t.firstChild=t.lastChild=null),t}function k(e,t){var n=t.parentNode;if(n){var r=e.lastChild;n.removeChild(t);var r=e.lastChild}var r=e.lastChild;return t.parentNode=e,t.previousSibling=r,t.nextSibling=null,r?r.nextSibling=t:e.firstChild=t,e.lastChild=t,C(e.ownerDocument,e,t),t}function S(){this._nsMap={}}function w(){}function T(){}function R(){}function E(){}function N(){}function A(){}function _(){}function B(){}function I(){}function D(){}function P(){}function O(){}function L(e,t){var n=[],r=9==this.nodeType?this.documentElement:this,o=r.prefix,i=r.namespaceURI;if(i&&null==o){var o=r.lookupPrefix(i);if(null==o)var a=[{namespace:i,prefix:null}]}return M(this,n,e,t,a),n.join("")}function j(e,t,n){var r=e.prefix||"",o=e.namespaceURI;if(!r&&!o)return!1;if("xml"===r&&"http://www.w3.org/XML/1998/namespace"===o||"http://www.w3.org/2000/xmlns/"==o)return!1;for(var i=n.length;i--;){var a=n[i];if(a.prefix==r)return a.namespace!=o}return!0}function M(e,t,n,r,o){if(r){if(!(e=r(e)))return;if("string"==typeof e)return void t.push(e)}switch(e.nodeType){case G:o||(o=[]);var i=(o.length,e.attributes),a=i.length,s=e.firstChild,u=e.tagName;n=z===e.namespaceURI||n,t.push("<",u);for(var c=0;c<a;c++){var l=i.item(c);"xmlns"==l.prefix?o.push({prefix:l.localName,namespace:l.value}):"xmlns"==l.nodeName&&o.push({prefix:"",namespace:l.value})}for(var c=0;c<a;c++){var l=i.item(c);if(j(l,n,o)){var d=l.prefix||"",f=l.namespaceURI,p=d?" xmlns:"+d:" xmlns";t.push(p,'="',f,'"'),o.push({prefix:d,namespace:f})}M(l,t,n,r,o)}if(j(e,n,o)){var d=e.prefix||"",f=e.namespaceURI,p=d?" xmlns:"+d:" xmlns";t.push(p,'="',f,'"'),o.push({prefix:d,namespace:f})}if(s||n&&!/^(?:meta|link|img|br|hr|input)$/i.test(u)){if(t.push(">"),n&&/^script$/i.test(u))for(;s;)s.data?t.push(s.data):M(s,t,n,r,o),s=s.nextSibling;else for(;s;)M(s,t,n,r,o),s=s.nextSibling;t.push("</",u,">")}else t.push("/>");return;case Z:case te:for(var s=e.firstChild;s;)M(s,t,n,r,o),s=s.nextSibling;return;case V:return t.push(" ",e.name,'="',e.value.replace(/[<&"]/g,h),'"');case W:return t.push(e.data.replace(/[<&]/g,h));case X:return t.push("<![CDATA[",e.data,"]]>");case Y:return t.push("\x3c!--",e.data,"--\x3e");case ee:var g=e.publicId,m=e.systemId;if(t.push("<!DOCTYPE ",e.name),g)t.push(' PUBLIC "',g),m&&"."!=m&&t.push('" "',m),t.push('">');else if(m&&"."!=m)t.push(' SYSTEM "',m,'">');else{var y=e.internalSubset;y&&t.push(" [",y,"]"),t.push(">")}return;case Q:return t.push("<?",e.target," ",e.data,"?>");case $:return t.push("&",e.nodeName,";");default:t.push("??",e.nodeName)}}function U(e,t,n){var r;switch(t.nodeType){case G:r=t.cloneNode(!1),r.ownerDocument=e;case te:break;case V:n=!0}if(r||(r=t.cloneNode(!1)),r.ownerDocument=e,r.parentNode=null,n)for(var o=t.firstChild;o;)r.appendChild(U(e,o,n)),o=o.nextSibling;return r}function F(e,t,n){var r=new t.constructor;for(var o in t){var a=t[o];"object"!=typeof a&&a!=r[o]&&(r[o]=a)}switch(t.childNodes&&(r.childNodes=new i),r.ownerDocument=e,r.nodeType){case G:var s=t.attributes,c=r.attributes=new u,l=s.length;c._ownerElement=r;for(var d=0;d<l;d++)r.setAttributeNode(F(e,s.item(d),!0));break;case V:n=!0}if(n)for(var f=t.firstChild;f;)r.appendChild(F(e,f,n)),f=f.nextSibling;return r}function H(e,t,n){e[t]=n}function K(e){switch(e.nodeType){case G:case te:var t=[];for(e=e.firstChild;e;)7!==e.nodeType&&8!==e.nodeType&&t.push(K(e)),e=e.nextSibling;return t.join("");default:return e.nodeValue}}var z="http://www.w3.org/1999/xhtml",q={},G=q.ELEMENT_NODE=1,V=q.ATTRIBUTE_NODE=2,W=q.TEXT_NODE=3,X=q.CDATA_SECTION_NODE=4,$=q.ENTITY_REFERENCE_NODE=5,J=q.ENTITY_NODE=6,Q=q.PROCESSING_INSTRUCTION_NODE=7,Y=q.COMMENT_NODE=8,Z=q.DOCUMENT_NODE=9,ee=q.DOCUMENT_TYPE_NODE=10,te=q.DOCUMENT_FRAGMENT_NODE=11,ne=q.NOTATION_NODE=12,re={},oe={},ie=(re.INDEX_SIZE_ERR=(oe[1]="Index size error",1),re.DOMSTRING_SIZE_ERR=(oe[2]="DOMString size error",2),re.HIERARCHY_REQUEST_ERR=(oe[3]="Hierarchy request error",3)),ae=(re.WRONG_DOCUMENT_ERR=(oe[4]="Wrong document",4),re.INVALID_CHARACTER_ERR=(oe[5]="Invalid character",5),re.NO_DATA_ALLOWED_ERR=(oe[6]="No data allowed",6),re.NO_MODIFICATION_ALLOWED_ERR=(oe[7]="No modification allowed",7),re.NOT_FOUND_ERR=(oe[8]="Not found",8)),se=(re.NOT_SUPPORTED_ERR=(oe[9]="Not supported",9),re.INUSE_ATTRIBUTE_ERR=(oe[10]="Attribute in use",10));re.INVALID_STATE_ERR=(oe[11]="Invalid state",11),re.SYNTAX_ERR=(oe[12]="Syntax error",12),re.INVALID_MODIFICATION_ERR=(oe[13]="Invalid modification",13),re.NAMESPACE_ERR=(oe[14]="Invalid namespace",14),re.INVALID_ACCESS_ERR=(oe[15]="Invalid access",15);o.prototype=Error.prototype,n(re,o),i.prototype={length:0,item:function(e){return this[e]||null},toString:function(e,t){for(var n=[],r=0;r<this.length;r++)M(this[r],n,e,t);return n.join("")}},a.prototype.item=function(e){return s(this),this[e]},r(a,i),u.prototype={length:0,item:i.prototype.item,getNamedItem:function(e){for(var t=this.length;t--;){var n=this[t];if(n.nodeName==e)return n}},setNamedItem:function(e){var t=e.ownerElement;if(t&&t!=this._ownerElement)throw new o(se);var n=this.getNamedItem(e.nodeName);return l(this._ownerElement,this,e,n),n},setNamedItemNS:function(e){var t,n=e.ownerElement;if(n&&n!=this._ownerElement)throw new o(se);return t=this.getNamedItemNS(e.namespaceURI,e.localName),l(this._ownerElement,this,e,t),t},removeNamedItem:function(e){var t=this.getNamedItem(e);return d(this._ownerElement,this,t),t},removeNamedItemNS:function(e,t){var n=this.getNamedItemNS(e,t);return d(this._ownerElement,this,n),n},getNamedItemNS:function(e,t){for(var n=this.length;n--;){var r=this[n];if(r.localName==t&&r.namespaceURI==e)return r}return null}},f.prototype={hasFeature:function(e,t){var n=this._features[e.toLowerCase()];return!(!n||t&&!(t in n))},createDocument:function(e,t,n){var r=new m;if(r.implementation=this,r.childNodes=new i,r.doctype=n,n&&r.appendChild(n),t){var o=r.createElementNS(e,t);r.appendChild(o)}return r},createDocumentType:function(e,t,n){var r=new A;return r.name=e,r.nodeName=e,r.publicId=t,r.systemId=n,r}},p.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(e,t){return b(this,e,t)},replaceChild:function(e,t){this.insertBefore(e,t),t&&this.removeChild(t)},removeChild:function(e){return x(this,e)},appendChild:function(e){return this.insertBefore(e,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(e){return F(this.ownerDocument||this,this,e)},normalize:function(){for(var e=this.firstChild;e;){var t=e.nextSibling;t&&t.nodeType==W&&e.nodeType==W?(this.removeChild(t),e.appendData(t.data)):(e.normalize(),e=t)}},isSupported:function(e,t){return this.ownerDocument.implementation.hasFeature(e,t)},hasAttributes:function(){return this.attributes.length>0},lookupPrefix:function(e){for(var t=this;t;){var n=t._nsMap;if(n)for(var r in n)if(n[r]==e)return r;t=t.nodeType==V?t.ownerDocument:t.parentNode}return null},lookupNamespaceURI:function(e){for(var t=this;t;){var n=t._nsMap;if(n&&e in n)return n[e];t=t.nodeType==V?t.ownerDocument:t.parentNode}return null},isDefaultNamespace:function(e){return null==this.lookupPrefix(e)}},n(q,p),n(q,p.prototype),m.prototype={nodeName:"#document",nodeType:Z,doctype:null,documentElement:null,_inc:1,insertBefore:function(e,t){if(e.nodeType==te){for(var n=e.firstChild;n;){var r=n.nextSibling;this.insertBefore(n,t),n=r}return e}return null==this.documentElement&&e.nodeType==G&&(this.documentElement=e),b(this,e,t),e.ownerDocument=this,e},removeChild:function(e){return this.documentElement==e&&(this.documentElement=null),x(this,e)},importNode:function(e,t){return U(this,e,t)},getElementById:function(e){var t=null;return g(this.documentElement,function(n){if(n.nodeType==G&&n.getAttribute("id")==e)return t=n,!0}),t},createElement:function(e){var t=new S;return t.ownerDocument=this,t.nodeName=e,t.tagName=e,t.childNodes=new i,(t.attributes=new u)._ownerElement=t,t},createDocumentFragment:function(){var e=new D;return e.ownerDocument=this,e.childNodes=new i,e},createTextNode:function(e){var t=new R;return t.ownerDocument=this,t.appendData(e),t},createComment:function(e){var t=new E;return t.ownerDocument=this,t.appendData(e),t},createCDATASection:function(e){var t=new N;return t.ownerDocument=this,t.appendData(e),t},createProcessingInstruction:function(e,t){var n=new P;return n.ownerDocument=this,n.tagName=n.target=e,n.nodeValue=n.data=t,n},createAttribute:function(e){var t=new w;return t.ownerDocument=this,t.name=e,t.nodeName=e,t.localName=e,t.specified=!0,t},createEntityReference:function(e){var t=new I;return t.ownerDocument=this,t.nodeName=e,t},createElementNS:function(e,t){var n=new S,r=t.split(":"),o=n.attributes=new u;return n.childNodes=new i,n.ownerDocument=this,n.nodeName=t,n.tagName=t,n.namespaceURI=e,2==r.length?(n.prefix=r[0],n.localName=r[1]):n.localName=t,o._ownerElement=n,n},createAttributeNS:function(e,t){var n=new w,r=t.split(":");return n.ownerDocument=this,n.nodeName=t,n.name=t,n.namespaceURI=e,n.specified=!0,2==r.length?(n.prefix=r[0],n.localName=r[1]):n.localName=t,n}},r(m,p),S.prototype={nodeType:G,hasAttribute:function(e){return null!=this.getAttributeNode(e)},getAttribute:function(e){var t=this.getAttributeNode(e);return t&&t.value||""},getAttributeNode:function(e){return this.attributes.getNamedItem(e)},setAttribute:function(e,t){var n=this.ownerDocument.createAttribute(e);n.value=n.nodeValue=""+t,this.setAttributeNode(n)},removeAttribute:function(e){var t=this.getAttributeNode(e);t&&this.removeAttributeNode(t)},appendChild:function(e){return e.nodeType===te?this.insertBefore(e,null):k(this,e)},setAttributeNode:function(e){return this.attributes.setNamedItem(e)},setAttributeNodeNS:function(e){return this.attributes.setNamedItemNS(e)},removeAttributeNode:function(e){return this.attributes.removeNamedItem(e.nodeName)},removeAttributeNS:function(e,t){var n=this.getAttributeNodeNS(e,t);n&&this.removeAttributeNode(n)},hasAttributeNS:function(e,t){return null!=this.getAttributeNodeNS(e,t)},getAttributeNS:function(e,t){var n=this.getAttributeNodeNS(e,t);return n&&n.value||""},setAttributeNS:function(e,t,n){var r=this.ownerDocument.createAttributeNS(e,t);r.value=r.nodeValue=""+n,this.setAttributeNode(r)},getAttributeNodeNS:function(e,t){return this.attributes.getNamedItemNS(e,t)},getElementsByTagName:function(e){return new a(this,function(t){var n=[];return g(t,function(r){r===t||r.nodeType!=G||"*"!==e&&r.tagName!=e||n.push(r)}),n})},getElementsByTagNameNS:function(e,t){return new a(this,function(n){var r=[];return g(n,function(o){o===n||o.nodeType!==G||"*"!==e&&o.namespaceURI!==e||"*"!==t&&o.localName!=t||r.push(o)}),r})}},m.prototype.getElementsByTagName=S.prototype.getElementsByTagName,m.prototype.getElementsByTagNameNS=S.prototype.getElementsByTagNameNS,r(S,p),w.prototype.nodeType=V,r(w,p),T.prototype={data:"",substringData:function(e,t){return this.data.substring(e,e+t)},appendData:function(e){e=this.data+e,this.nodeValue=this.data=e,this.length=e.length},insertData:function(e,t){this.replaceData(e,0,t)},appendChild:function(e){throw new Error(oe[ie])},deleteData:function(e,t){this.replaceData(e,t,"")},replaceData:function(e,t,n){n=this.data.substring(0,e)+n+this.data.substring(e+t),this.nodeValue=this.data=n,this.length=n.length}},r(T,p),R.prototype={nodeName:"#text",nodeType:W,splitText:function(e){var t=this.data,n=t.substring(e);t=t.substring(0,e),this.data=this.nodeValue=t,this.length=t.length;var r=this.ownerDocument.createTextNode(n);return this.parentNode&&this.parentNode.insertBefore(r,this.nextSibling),r}},r(R,T),E.prototype={nodeName:"#comment",nodeType:Y},r(E,T),N.prototype={nodeName:"#cdata-section",nodeType:X},r(N,T),A.prototype.nodeType=ee,r(A,p),_.prototype.nodeType=ne,r(_,p),B.prototype.nodeType=J,r(B,p),I.prototype.nodeType=$,r(I,p),D.prototype.nodeName="#document-fragment",D.prototype.nodeType=te,r(D,p),P.prototype.nodeType=Q,r(P,p),O.prototype.serializeToString=function(e,t,n){return L.call(e,t,n)},p.prototype.toString=L;try{Object.defineProperty&&(Object.defineProperty(a.prototype,"length",{get:function(){return s(this),this.$$length}}),Object.defineProperty(p.prototype,"textContent",{get:function(){return K(this)},set:function(e){switch(this.nodeType){case G:case te:for(;this.firstChild;)this.removeChild(this.firstChild);(e||String(e))&&this.appendChild(this.ownerDocument.createTextNode(e));break;default:this.data=e,this.value=e,this.nodeValue=e}}}),H=function(e,t,n){e["$$"+t]=n})}catch(e){}t.DOMImplementation=f,t.XMLSerializer=O},function(e,t){var n=function(e){var t={},n=function(e){return!t[e]&&(t[e]=[]),t[e]};e.on=function(e,t){"task-list-update"===e&&console.warn('warning: Event "'+e+'" has been deprecated. Please use "list-update" instead.'),n(e).push(t)},e.off=function(e,t){for(var r=n(e),o=r.length-1;o>=0;o--)t===r[o]&&r.splice(o,1)},e.emit=function(e,t){for(var r=n(e).map(function(e){return e}),o=0;o<r.length;o++)r[o](t)}},r=function(){n(this)};e.exports.init=n,e.exports.EventProxy=r},function(e,t,n){var r=n(4);e.exports=r},function(e,t,n){"use strict";var r=n(0),o=n(2),i=n(12),a=n(13),s=n(18),u={AppId:"",SecretId:"",SecretKey:"",FileParallelLimit:3,ChunkParallelLimit:3,ChunkRetryTimes:3,ChunkSize:1048576,SliceSize:1048576,CopyChunkParallelLimit:20,CopyChunkSize:10485760,CopySliceSize:10485760,ProgressInterval:1e3,UploadQueueSize:1e4,Domain:"",ServiceDomain:"",Protocol:"",CompatibilityMode:!1,ForcePathStyle:!1,UploadIdCacheLimit:50},c=function(e){this.options=r.extend(r.clone(u),e||{}),this.options.FileParallelLimit=Math.max(1,this.options.FileParallelLimit),this.options.ChunkParallelLimit=Math.max(1,this.options.ChunkParallelLimit),this.options.ChunkRetryTimes=Math.max(0,this.options.ChunkRetryTimes),this.options.ChunkSize=Math.max(1048576,this.options.ChunkSize),this.options.CopyChunkParallelLimit=Math.max(1,this.options.CopyChunkParallelLimit),this.options.CopyChunkSize=Math.max(1048576,this.options.CopyChunkSize),this.options.CopySliceSize=Math.max(0,this.options.CopySliceSize),this.options.AppId&&console.warn('warning: AppId has been deprecated, Please put it at the end of parameter Bucket(E.g: "test-1250000000").'),o.init(this),i.init(this)};r.extend(c.prototype,a),r.extend(c.prototype,s),c.getAuthorization=r.getAuth,c.version="0.4.20",e.exports=c},function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t){function n(e,t){var n=e[0],r=e[1],u=e[2],c=e[3];n=o(n,r,u,c,t[0],7,-680876936),c=o(c,n,r,u,t[1],12,-389564586),u=o(u,c,n,r,t[2],17,606105819),r=o(r,u,c,n,t[3],22,-1044525330),n=o(n,r,u,c,t[4],7,-176418897),c=o(c,n,r,u,t[5],12,1200080426),u=o(u,c,n,r,t[6],17,-1473231341),r=o(r,u,c,n,t[7],22,-45705983),n=o(n,r,u,c,t[8],7,1770035416),c=o(c,n,r,u,t[9],12,-1958414417),u=o(u,c,n,r,t[10],17,-42063),r=o(r,u,c,n,t[11],22,-1990404162),n=o(n,r,u,c,t[12],7,1804603682),c=o(c,n,r,u,t[13],12,-40341101),u=o(u,c,n,r,t[14],17,-1502002290),r=o(r,u,c,n,t[15],22,1236535329),n=i(n,r,u,c,t[1],5,-165796510),c=i(c,n,r,u,t[6],9,-1069501632),u=i(u,c,n,r,t[11],14,643717713),r=i(r,u,c,n,t[0],20,-373897302),n=i(n,r,u,c,t[5],5,-701558691),c=i(c,n,r,u,t[10],9,38016083),u=i(u,c,n,r,t[15],14,-660478335),r=i(r,u,c,n,t[4],20,-405537848),n=i(n,r,u,c,t[9],5,568446438),c=i(c,n,r,u,t[14],9,-1019803690),u=i(u,c,n,r,t[3],14,-187363961),r=i(r,u,c,n,t[8],20,1163531501),n=i(n,r,u,c,t[13],5,-1444681467),c=i(c,n,r,u,t[2],9,-51403784),u=i(u,c,n,r,t[7],14,1735328473),r=i(r,u,c,n,t[12],20,-1926607734),n=a(n,r,u,c,t[5],4,-378558),c=a(c,n,r,u,t[8],11,-2022574463),u=a(u,c,n,r,t[11],16,1839030562),r=a(r,u,c,n,t[14],23,-35309556),n=a(n,r,u,c,t[1],4,-1530992060),c=a(c,n,r,u,t[4],11,1272893353),u=a(u,c,n,r,t[7],16,-155497632),r=a(r,u,c,n,t[10],23,-1094730640),n=a(n,r,u,c,t[13],4,681279174),c=a(c,n,r,u,t[0],11,-358537222),u=a(u,c,n,r,t[3],16,-722521979),r=a(r,u,c,n,t[6],23,76029189),n=a(n,r,u,c,t[9],4,-640364487),c=a(c,n,r,u,t[12],11,-421815835),u=a(u,c,n,r,t[15],16,530742520),r=a(r,u,c,n,t[2],23,-995338651),n=s(n,r,u,c,t[0],6,-198630844),c=s(c,n,r,u,t[7],10,1126891415),u=s(u,c,n,r,t[14],15,-1416354905),r=s(r,u,c,n,t[5],21,-57434055),n=s(n,r,u,c,t[12],6,1700485571),c=s(c,n,r,u,t[3],10,-1894986606),u=s(u,c,n,r,t[10],15,-1051523),r=s(r,u,c,n,t[1],21,-2054922799),n=s(n,r,u,c,t[8],6,1873313359),c=s(c,n,r,u,t[15],10,-30611744),u=s(u,c,n,r,t[6],15,-1560198380),r=s(r,u,c,n,t[13],21,1309151649),n=s(n,r,u,c,t[4],6,-145523070),c=s(c,n,r,u,t[11],10,-1120210379),u=s(u,c,n,r,t[2],15,718787259),r=s(r,u,c,n,t[9],21,-343485551),e[0]=g(n,e[0]),e[1]=g(r,e[1]),e[2]=g(u,e[2]),e[3]=g(c,e[3])}function r(e,t,n,r,o,i){return t=g(g(t,e),g(r,i)),g(t<<o|t>>>32-o,n)}function o(e,t,n,o,i,a,s){return r(t&n|~t&o,e,t,i,a,s)}function i(e,t,n,o,i,a,s){return r(t&o|n&~o,e,t,i,a,s)}function a(e,t,n,o,i,a,s){return r(t^n^o,e,t,i,a,s)}function s(e,t,n,o,i,a,s){return r(n^(t|~o),e,t,i,a,s)}function u(e){var t,r=e.length,o=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=e.length;t+=64)n(o,c(e.substring(t-64,t)));e=e.substring(t-64);var i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<e.length;t++)i[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(i[t>>2]|=128<<(t%4<<3),t>55)for(n(o,i),t=0;t<16;t++)i[t]=0;return i[14]=8*r,n(o,i),o}function c(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return n}function l(e){for(var t="",n=0;n<4;n++)t+=h[e>>8*n+4&15]+h[e>>8*n&15];return t}function d(e){for(var t=0;t<e.length;t++)e[t]=l(e[t]);return e.join("")}function f(e){e=e.replace(/\r\n/g,"\n");for(var t="",n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t+=String.fromCharCode(r):r>127&&r<2048?(t+=String.fromCharCode(r>>6|192),t+=String.fromCharCode(63&r|128)):(t+=String.fromCharCode(r>>12|224),t+=String.fromCharCode(r>>6&63|128),t+=String.fromCharCode(63&r|128))}return t}function p(e,t){return t||(e=f(e)),d(u(e))}var h="0123456789abcdef".split(""),g=function(e,t){return e+t&4294967295};"5d41402abc4b2a76b9719d911017c592"!=p("hello")&&(g=function(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}),e.exports=p},function(e,t,n){var r=r||function(e,t){var n={},r=n.lib={},o=function(){},i=r.Base={extend:function(e){o.prototype=this;var t=new o;return e&&t.mixIn(e),t.hasOwnProperty("init")||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},a=r.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes;if(e=e.sigBytes,this.clamp(),r%4)for(var o=0;o<e;o++)t[r+o>>>2]|=(n[o>>>2]>>>24-o%4*8&255)<<24-(r+o)%4*8;else if(65535<n.length)for(o=0;o<e;o+=4)t[r+o>>>2]=n[o>>>2];else t.push.apply(t,n);return this.sigBytes+=e,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n=[],r=0;r<t;r+=4)n.push(4294967296*e.random()|0);return new a.init(n,t)}}),s=n.enc={},u=s.Hex={stringify:function(e){var t=e.words;e=e.sigBytes;for(var n=[],r=0;r<e;r++){var o=t[r>>>2]>>>24-r%4*8&255;n.push((o>>>4).toString(16)),n.push((15&o).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new a.init(n,t/2)}},c=s.Latin1={stringify:function(e){var t=e.words;e=e.sigBytes;for(var n=[],r=0;r<e;r++)n.push(String.fromCharCode(t[r>>>2]>>>24-r%4*8&255));return n.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new a.init(n,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},d=r.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new a.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,o=n.sigBytes,i=this.blockSize,s=o/(4*i),s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0);if(t=s*i,o=e.min(4*t,o),t){for(var u=0;u<t;u+=i)this._doProcessBlock(r,u);u=r.splice(0,t),n.sigBytes-=o}return new a.init(u,o)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});r.Hasher=d.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new f.HMAC.init(e,n).finalize(t)}}});var f=n.algo={};return n}(Math);!function(){var e=r,t=e.lib,n=t.WordArray,o=t.Hasher,i=[],t=e.algo.SHA1=o.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],o=n[1],a=n[2],s=n[3],u=n[4],c=0;80>c;c++){if(16>c)i[c]=0|e[t+c];else{var l=i[c-3]^i[c-8]^i[c-14]^i[c-16];i[c]=l<<1|l>>>31}l=(r<<5|r>>>27)+u+i[c],l=20>c?l+(1518500249+(o&a|~o&s)):40>c?l+(1859775393+(o^a^s)):60>c?l+((o&a|o&s|a&s)-1894007588):l+((o^a^s)-899497514),u=s,s=a,a=o<<30|o>>>2,o=r,r=l}n[0]=n[0]+r|0,n[1]=n[1]+o|0,n[2]=n[2]+a|0,n[3]=n[3]+s|0,n[4]=n[4]+u|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=Math.floor(n/4294967296),t[15+(r+64>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});e.SHA1=o._createHelper(t),e.HmacSHA1=o._createHmacHelper(t)}(),function(){var e=r,t=e.enc.Utf8;e.algo.HMAC=e.lib.Base.extend({init:function(e,n){e=this._hasher=new e.init,"string"==typeof n&&(n=t.parse(n));var r=e.blockSize,o=4*r;n.sigBytes>o&&(n=e.finalize(n)),n.clamp();for(var i=this._oKey=n.clone(),a=this._iKey=n.clone(),s=i.words,u=a.words,c=0;c<r;c++)s[c]^=1549556828,u[c]^=909522486;i.sigBytes=a.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher;return e=t.finalize(e),t.reset(),t.finalize(this._oKey.clone().concat(e))}})}(),function(){var e=r,t=e.lib,n=t.WordArray,o=e.enc;o.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var o=[],i=0;i<n;i+=3)for(var a=t[i>>>2]>>>24-i%4*8&255,s=t[i+1>>>2]>>>24-(i+1)%4*8&255,u=t[i+2>>>2]>>>24-(i+2)%4*8&255,c=a<<16|s<<8|u,l=0;l<4&&i+.75*l<n;l++)o.push(r.charAt(c>>>6*(3-l)&63));var d=r.charAt(64);if(d)for(;o.length%4;)o.push(d);return o.join("")},parse:function(e){var t=e.length,r=this._map,o=r.charAt(64);if(o){var i=e.indexOf(o);-1!=i&&(t=i)}for(var a=[],s=0,u=0;u<t;u++)if(u%4){var c=r.indexOf(e.charAt(u-1))<<u%4*2,l=r.indexOf(e.charAt(u))>>>6-u%4*2;a[s>>>2]|=(c|l)<<24-s%4*8,s++}return n.create(a,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),e.exports=r},function(e,t,n){var r=n(9).DOMParser,o=function(){this.version="1.3.5";var e={mergeCDATA:!0,normalize:!0,stripElemPrefix:!0},t=new RegExp(/(?!xmlns)^.*:/);new RegExp(/^\s+|\s+$/g);return this.grokType=function(e){return/^\s*$/.test(e)?null:/^(?:true|false)$/i.test(e)?"true"===e.toLowerCase():isFinite(e)?parseFloat(e):e},this.parseString=function(e,t){if(e){var n=this.stringToXML(e);return n.getElementsByTagName("parsererror").length?null:this.parseXML(n,t)}return null},this.parseXML=function(n,r){for(var i in r)e[i]=r[i];var a={},s=0,u="";if(n.childNodes.length)for(var c,l,d,f=0;f<n.childNodes.length;f++)c=n.childNodes.item(f),4===c.nodeType?e.mergeCDATA&&(u+=c.nodeValue):3===c.nodeType?u+=c.nodeValue:1===c.nodeType&&(0===s&&(a={}),l=e.stripElemPrefix?c.nodeName.replace(t,""):c.nodeName,d=o.parseXML(c),a.hasOwnProperty(l)?(a[l].constructor!==Array&&(a[l]=[a[l]]),a[l].push(d)):(a[l]=d,s++));return Object.keys(a).length||(a=u||""),a},this.xmlToString=function(e){try{return e.xml?e.xml:(new XMLSerializer).serializeToString(e)}catch(e){return null}},this.stringToXML=function(e){try{var t=null;if(window.DOMParser){return t=(new r).parseFromString(e,"text/xml")}return t=new ActiveXObject("Microsoft.XMLDOM"),t.async=!1,t.loadXML(e),t}catch(e){return null}},this}.call({}),i=function(e){return o.parseString(e)};e.exports=i},function(e,t,n){function r(e){this.options=e||{locator:{}}}function o(e,t,n){function r(t){var r=e[t];!r&&a&&(r=2==e.length?function(n){e(t,n)}:e),o[t]=r&&function(e){r("[xmldom "+t+"]\t"+e+s(n))}||function(){}}if(!e){if(t instanceof i)return t;e=t}var o={},a=e instanceof Function;return n=n||{},r("warning"),r("error"),r("fatalError"),o}function i(){this.cdata=!1}function a(e,t){t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber}function s(e){if(e)return"\n@"+(e.systemId||"")+"#[line:"+e.lineNumber+",col:"+e.columnNumber+"]"}function u(e,t,n){return"string"==typeof e?e.substr(t,n):e.length>=t+n||t?new java.lang.String(e,t,n)+"":e}function c(e,t){e.currentElement?e.currentElement.appendChild(t):e.doc.appendChild(t)}r.prototype.parseFromString=function(e,t){var n=this.options,r=new l,a=n.domBuilder||new i,s=n.errorHandler,u=n.locator,c=n.xmlns||{},d={lt:"<",gt:">",amp:"&",quot:'"',apos:"'"};return u&&a.setDocumentLocator(u),r.errorHandler=o(s,a,u),r.domBuilder=n.domBuilder||a,/\/x?html?$/.test(t)&&(d.nbsp=" ",d.copy="©",c[""]="http://www.w3.org/1999/xhtml"),c.xml=c.xml||"http://www.w3.org/XML/1998/namespace",e?r.parse(e,c,d):r.errorHandler.error("invalid doc source"),a.doc},i.prototype={startDocument:function(){this.doc=(new d).createDocument(null,null,null),this.locator&&(this.doc.documentURI=this.locator.systemId)},startElement:function(e,t,n,r){var o=this.doc,i=o.createElementNS(e,n||t),s=r.length;c(this,i),this.currentElement=i,this.locator&&a(this.locator,i);for(var u=0;u<s;u++){var e=r.getURI(u),l=r.getValue(u),n=r.getQName(u),d=o.createAttributeNS(e,n);this.locator&&a(r.getLocator(u),d),d.value=d.nodeValue=l,i.setAttributeNode(d)}},endElement:function(e,t,n){var r=this.currentElement;r.tagName;this.currentElement=r.parentNode},startPrefixMapping:function(e,t){},endPrefixMapping:function(e){},processingInstruction:function(e,t){var n=this.doc.createProcessingInstruction(e,t);this.locator&&a(this.locator,n),c(this,n)},ignorableWhitespace:function(e,t,n){},characters:function(e,t,n){if(e=u.apply(this,arguments)){if(this.cdata)var r=this.doc.createCDATASection(e);else var r=this.doc.createTextNode(e);this.currentElement?this.currentElement.appendChild(r):/^\s*$/.test(e)&&this.doc.appendChild(r),this.locator&&a(this.locator,r)}},skippedEntity:function(e){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(e){(this.locator=e)&&(e.lineNumber=0)},comment:function(e,t,n){e=u.apply(this,arguments);var r=this.doc.createComment(e);this.locator&&a(this.locator,r),c(this,r)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(e,t,n){var r=this.doc.implementation;if(r&&r.createDocumentType){var o=r.createDocumentType(e,t,n);this.locator&&a(this.locator,o),c(this,o)}},warning:function(e){console.warn("[xmldom warning]\t"+e,s(this.locator))},error:function(e){console.error("[xmldom error]\t"+e,s(this.locator))},fatalError:function(e){throw console.error("[xmldom fatalError]\t"+e,s(this.locator)),e}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,function(e){i.prototype[e]=function(){return null}});var l=n(10).XMLReader,d=t.DOMImplementation=n(1).DOMImplementation;t.XMLSerializer=n(1).XMLSerializer,t.DOMParser=r},function(e,t){function n(){}function r(e,t,n,r,c){function p(e){if(e>65535){e-=65536;var t=55296+(e>>10),n=56320+(1023&e);return String.fromCharCode(t,n)}return String.fromCharCode(e)}function h(e){var t=e.slice(1,-1);return t in n?n[t]:"#"===t.charAt(0)?p(parseInt(t.substr(1).replace("x","0x"))):(c.error("entity not found:"+e),e)}function g(t){if(t>S){var n=e.substring(S,t).replace(/&#?\w+;/g,h);x&&m(S),r.characters(n,0,t-S),S=t}}function m(t,n){for(;t>=v&&(n=C.exec(e));)y=n.index,v=y+n[0].length,x.lineNumber++;x.columnNumber=t-y+1}for(var y=0,v=0,C=/.*(?:\r\n?|\n)|.*$/g,x=r.locator,b=[{currentNSMap:t}],k={},S=0;;){try{var w=e.indexOf("<",S);if(w<0){if(!e.substr(S).match(/^\s*$/)){var T=r.doc,R=T.createTextNode(e.substr(S));T.appendChild(R),r.currentElement=R}return}switch(w>S&&g(w),e.charAt(w+1)){case"/":var E=e.indexOf(">",w+3),N=e.substring(w+2,E),A=b.pop();E<0?(N=e.substring(w+2).replace(/[\s<].*/,""),c.error("end tag name: "+N+" is not complete:"+A.tagName),E=w+1+N.length):N.match(/\s</)&&(N=N.replace(/[\s<].*/,""),c.error("end tag name: "+N+" maybe not complete"),E=w+1+N.length);var _=A.localNSMap,B=A.tagName==N;if(B||A.tagName&&A.tagName.toLowerCase()==N.toLowerCase()){if(r.endElement(A.uri,A.localName,N),_)for(var I in _)r.endPrefixMapping(I);B||c.fatalError("end tag name: "+N+" is not match the current start tagName:"+A.tagName)}else b.push(A);E++;break;case"?":x&&m(w),E=d(e,w,r);break;case"!":x&&m(w),E=l(e,w,r,c);break;default:x&&m(w);var D=new f,P=b[b.length-1].currentNSMap,E=i(e,w,D,P,h,c),O=D.length;if(!D.closed&&u(e,E,D.tagName,k)&&(D.closed=!0,n.nbsp||c.warning("unclosed xml attribute")),x&&O){for(var L=o(x,{}),j=0;j<O;j++){var M=D[j];m(M.offset),M.locator=o(x,{})}r.locator=L,a(D,r,P)&&b.push(D),r.locator=x}else a(D,r,P)&&b.push(D);"http://www.w3.org/1999/xhtml"!==D.uri||D.closed?E++:E=s(e,E,D.tagName,h,r)}}catch(e){c.error("element parse error: "+e),E=-1}E>S?S=E:g(Math.max(w,S)+1)}}function o(e,t){return t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber,t}function i(e,t,n,r,o,i){for(var a,s,u=++t,c=v;;){var l=e.charAt(u);switch(l){case"=":if(c===C)a=e.slice(t,u),c=b;else{if(c!==x)throw new Error("attribute equal must after attrName");c=b}break;case"'":case'"':if(c===b||c===C){if(c===C&&(i.warning('attribute value must after "="'),a=e.slice(t,u)),t=u+1,!((u=e.indexOf(l,t))>0))throw new Error("attribute value no end '"+l+"' match");s=e.slice(t,u).replace(/&#?\w+;/g,o),n.add(a,s,t-1),c=S}else{if(c!=k)throw new Error('attribute value must after "="');s=e.slice(t,u).replace(/&#?\w+;/g,o),n.add(a,s,t),i.warning('attribute "'+a+'" missed start quot('+l+")!!"),t=u+1,c=S}break;case"/":switch(c){case v:n.setTagName(e.slice(t,u));case S:case w:case T:c=T,n.closed=!0;case k:case C:case x:break;default:throw new Error("attribute invalid close char('/')")}break;case"":return i.error("unexpected end of input"),c==v&&n.setTagName(e.slice(t,u)),u;case">":switch(c){case v:n.setTagName(e.slice(t,u));case S:case w:case T:break;case k:case C:s=e.slice(t,u),"/"===s.slice(-1)&&(n.closed=!0,s=s.slice(0,-1));case x:c===x&&(s=a),c==k?(i.warning('attribute "'+s+'" missed quot(")!!'),n.add(a,s.replace(/&#?\w+;/g,o),t)):("http://www.w3.org/1999/xhtml"===r[""]&&s.match(/^(?:disabled|checked|selected)$/i)||i.warning('attribute "'+s+'" missed value!! "'+s+'" instead!!'),n.add(s,s,t));break;case b:throw new Error("attribute value missed!!")}return u;case"":l=" ";default:if(l<=" ")switch(c){case v:n.setTagName(e.slice(t,u)),c=w;break;case C:a=e.slice(t,u),c=x;break;case k:var s=e.slice(t,u).replace(/&#?\w+;/g,o);i.warning('attribute "'+s+'" missed quot(")!!'),n.add(a,s,t);case S:c=w}else switch(c){case x:n.tagName;"http://www.w3.org/1999/xhtml"===r[""]&&a.match(/^(?:disabled|checked|selected)$/i)||i.warning('attribute "'+a+'" missed value!! "'+a+'" instead2!!'),n.add(a,a,t),t=u,c=C;break;case S:i.warning('attribute space is required"'+a+'"!!');case w:c=C,t=u;break;case b:c=k,t=u;break;case T:throw new Error("elements closed character '/' and '>' must be connected to")}}u++}}function a(e,t,n){for(var r=e.tagName,o=null,i=e.length;i--;){var a=e[i],s=a.qName,u=a.value,l=s.indexOf(":");if(l>0)var d=a.prefix=s.slice(0,l),f=s.slice(l+1),p="xmlns"===d&&f;else f=s,d=null,p="xmlns"===s&&"";a.localName=f,!1!==p&&(null==o&&(o={},c(n,n={})),n[p]=o[p]=u,a.uri="http://www.w3.org/2000/xmlns/",t.startPrefixMapping(p,u))}for(var i=e.length;i--;){a=e[i];var d=a.prefix;d&&("xml"===d&&(a.uri="http://www.w3.org/XML/1998/namespace"),"xmlns"!==d&&(a.uri=n[d||""]))}var l=r.indexOf(":");l>0?(d=e.prefix=r.slice(0,l),f=e.localName=r.slice(l+1)):(d=null,f=e.localName=r);var h=e.uri=n[d||""];if(t.startElement(h,f,r,e),!e.closed)return e.currentNSMap=n,e.localNSMap=o,!0;if(t.endElement(h,f,r),o)for(d in o)t.endPrefixMapping(d)}function s(e,t,n,r,o){if(/^(?:script|textarea)$/i.test(n)){var i=e.indexOf("</"+n+">",t),a=e.substring(t+1,i);if(/[&<]/.test(a))return/^script$/i.test(n)?(o.characters(a,0,a.length),i):(a=a.replace(/&#?\w+;/g,r),o.characters(a,0,a.length),i)}return t+1}function u(e,t,n,r){var o=r[n];return null==o&&(o=e.lastIndexOf("</"+n+">"),o<t&&(o=e.lastIndexOf("</"+n)),r[n]=o),o<t}function c(e,t){for(var n in e)t[n]=e[n]}function l(e,t,n,r){switch(e.charAt(t+2)){case"-":if("-"===e.charAt(t+3)){var o=e.indexOf("--\x3e",t+4);return o>t?(n.comment(e,t+4,o-t-4),o+3):(r.error("Unclosed comment"),-1)}return-1;default:if("CDATA["==e.substr(t+3,6)){var o=e.indexOf("]]>",t+9);return n.startCDATA(),n.characters(e,t+9,o-t-9),n.endCDATA(),o+3}var i=h(e,t),a=i.length;if(a>1&&/!doctype/i.test(i[0][0])){var s=i[1][0],u=a>3&&/^public$/i.test(i[2][0])&&i[3][0],c=a>4&&i[4][0],l=i[a-1];return n.startDTD(s,u&&u.replace(/^(['"])(.*?)\1$/,"$2"),c&&c.replace(/^(['"])(.*?)\1$/,"$2")),n.endDTD(),l.index+l[0].length}}return-1}function d(e,t,n){var r=e.indexOf("?>",t);if(r){var o=e.substring(t,r).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);if(o){o[0].length;return n.processingInstruction(o[1],o[2]),r+2}return-1}return-1}function f(e){}function p(e,t){return e.__proto__=t,e}function h(e,t){var n,r=[],o=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;for(o.lastIndex=t,o.exec(e);n=o.exec(e);)if(r.push(n),n[1])return r}var g=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,m=new RegExp("[\\-\\.0-9"+g.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),y=new RegExp("^"+g.source+m.source+"*(?::"+g.source+m.source+"*)?$"),v=0,C=1,x=2,b=3,k=4,S=5,w=6,T=7;n.prototype={parse:function(e,t,n){var o=this.domBuilder;o.startDocument(),c(t,t={}),r(e,t,n,o,this.errorHandler),o.endDocument()}},f.prototype={setTagName:function(e){if(!y.test(e))throw new Error("invalid tagName:"+e);this.tagName=e},add:function(e,t,n){if(!y.test(e))throw new Error("invalid attribute:"+e);this[this.length++]={qName:e,value:t,offset:n}},length:0,getLocalName:function(e){return this[e].localName},getLocator:function(e){return this[e].locator},getQName:function(e){return this[e].qName},getURI:function(e){return this[e].uri},getValue:function(e){return this[e].value}},p({},p.prototype)instanceof p||(p=function(e,t){function n(){}n.prototype=t,n=new n;for(t in e)n[t]=e[t];return n}),t.XMLReader=n},function(e,t){function n(e){return(""+e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&apos;").replace(/"/g,"&quot;").replace(o,"")}var r=new RegExp("^([^a-zA-Z_À-ÖØ-öø-ÿͰ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿿、-퟿豈-﷏ﷰ-�])|^((x|X)(m|M)(l|L))|([^a-zA-Z_À-ÖØ-öø-ÿͰ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿿、-퟿豈-﷏ﷰ-�-.0-9·̀-ͯ‿⁀])","g"),o=/[^\x09\x0A\x0D\x20-\xFF\x85\xA0-\uD7FF\uE000-\uFDCF\uFDE0-\uFFFD]/gm,i=function(e){var t=[];if(e instanceof Object)for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t},a=function(e,t){var o=function(e,n,o,i,a){var s=void 0!==t.indent?t.indent:"\t",u=t.prettyPrint?"\n"+new Array(i).join(s):"";t.removeIllegalNameCharacters&&(e=e.replace(r,"_"));var c=[u,"<",e,o||""];return n&&n.length>0?(c.push(">"),c.push(n),a&&c.push(u),c.push("</"),c.push(e),c.push(">")):c.push("/>"),c.join("")};return function e(r,a,s){var u=typeof r;switch((Array.isArray?Array.isArray(r):r instanceof Array)?u="array":r instanceof Date&&(u="date"),u){case"array":var c=[];return r.map(function(t){c.push(e(t,1,s+1))}),t.prettyPrint&&c.push("\n"),c.join("");case"date":return r.toJSON?r.toJSON():r+"";case"object":var l=[];for(var d in r)if(r.hasOwnProperty(d))if(r[d]instanceof Array)for(var f=0;f<r[d].length;f++)r[d].hasOwnProperty(f)&&l.push(o(d,e(r[d][f],0,s+1),null,s+1,i(r[d][f]).length));else l.push(o(d,e(r[d],0,s+1),null,s+1));return t.prettyPrint&&l.length>0&&l.push("\n"),l.join("");case"function":return r();default:return t.escape?n(r):""+r}}(e,0,0)},s=function(e){var t=['<?xml version="1.0" encoding="UTF-8"'];return e&&t.push(' standalone="yes"'),t.push("?>"),t.join("")};e.exports=function(e,t){if(t||(t={xmlHeader:{standalone:!0},prettyPrint:!0,indent:"  ",escape:!0}),"string"==typeof e)try{e=JSON.parse(e.toString())}catch(e){return!1}var n="",r="";return t&&("object"==typeof t?(t.xmlHeader&&(n=s(!!t.xmlHeader.standalone)),void 0!==t.docType&&(r="<!DOCTYPE "+t.docType+">")):n=s()),t=t||{},[n,t.prettyPrint&&r?"\n":"",r,a(e,t)].join("").replace(/\n{2,}/g,"\n").replace(/\s+$/g,"")}},function(e,t,n){var r=n(0),o=function(e){var t=[],n={},o=0,i=0,a={};r.each(["putObject","sliceUploadFile"],function(t){a[t]=e[t],e[t]=function(n,r){e._addTask(t,n,r)}});var s=function(e){var t={id:e.id,Bucket:e.Bucket,Region:e.Region,Key:e.Key,FilePath:e.FilePath,state:e.state,loaded:e.loaded,size:e.size,speed:e.speed,percent:e.percent,hashPercent:e.hashPercent,error:e.error};return e.FilePath&&(t.FilePath=e.FilePath),t},u=function(){e.emit("task-list-update",{list:r.map(t,s)}),e.emit("list-update",{list:r.map(t,s)})},c=function(){if(i<t.length&&o<e.options.FileParallelLimit){var n=t[i];"waiting"===n.state&&(o++,n.state="checking",!n.params.UploadData&&(n.params.UploadData={}),a[n.api].call(e,n.params,function(t,r){e._isRunningTask(n.id)&&("checking"!==n.state&&"uploading"!==n.state||(n.state=t?"error":"success",t&&(n.error=t),o--,u(),c(e),n.callback&&n.callback(t,r),"success"===n.state&&(delete n.params,delete n.callback)))}),u()),i++,c(e)}},l=function(t,r){var i=n[t];if(i){var a=i&&"waiting"===i.state,s=i&&("checking"===i.state||"uploading"===i.state);if("canceled"===r&&"canceled"!==i.state||"paused"===r&&a||"paused"===r&&s){if("paused"===r&&i.params.Body&&"function"==typeof i.params.Body.pipe)return void console.error("stream not support pause");i.state=r,e.emit("inner-kill-task",{TaskId:t,toState:r}),u(),s&&(o--,c(e)),"canceled"===r&&(delete i.params,delete i.callback)}}};e._addTasks=function(t){r.each(t,function(t){e._addTask(t.api,t.params,t.callback,!0)}),u()},e._addTask=function(o,a,s,l){a=r.extend({},a);var d=r.uuid();a.TaskId=d,a.TaskReady&&a.TaskReady(d);var f={params:a,callback:s,api:o,index:t.length,id:d,Bucket:a.Bucket,Region:a.Region,Key:a.Key,FilePath:a.FilePath||"",state:"waiting",loaded:0,size:0,speed:0,percent:0,hashPercent:0,error:null},p=a.onHashProgress;a.onHashProgress=function(t){e._isRunningTask(f.id)&&(f.hashPercent=t.percent,p&&p(t),u())};var h=a.onProgress;return a.onProgress=function(t){e._isRunningTask(f.id)&&("checking"===f.state&&(f.state="uploading"),f.loaded=t.loaded,f.speed=t.speed,f.percent=t.percent,h&&h(t),u())},r.getFileSize(o,a,function(r,o){if(n[d]=f,t.push(f),t.length>e.options.UploadQueueSize){var a=t.length-e.options.UploadQueueSize;t.splice(0,a),i-=a}if(r)return void s(r);f.size=o,!l&&u(),c(e)}),d},e._isRunningTask=function(e){var t=n[e];return!(!t||"checking"!==t.state&&"uploading"!==t.state)},e.getTaskList=function(){return r.map(t,s)},e.cancelTask=function(e){l(e,"canceled")},e.pauseTask=function(e){l(e,"paused")},e.restartTask=function(e){var t=n[e];!t||"paused"!==t.state&&"error"!==t.state||(t.state="waiting",u(),i=Math.min(i,t.index),c())}};e.exports.init=o},function(e,t,n){function r(e,t){J.call(this,{Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"HEAD"},function(e,n){t(e,n)})}function o(e,t){var n={};n.prefix=e.Prefix,n.delimiter=e.Delimiter,n.marker=e.Marker,n["max-keys"]=e.MaxKeys,n["encoding-type"]=e.EncodingType,J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:n},function(e,n){if(e)return t(e);var r=n.ListBucketResult||{},o=r.Contents||[],i=r.CommonPrefixes||[];o=Z.isArray(o)?o:[o],i=Z.isArray(i)?i:[i];var a=Z.clone(r);Z.extend(a,{Contents:o,CommonPrefixes:i,statusCode:n.statusCode,headers:n.headers}),t(null,a)})}function i(e,t){J.call(this,{Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"DELETE"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}function a(e,t){J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"acl"},function(e,n){if(e)return t(e);var r=n.AccessControlPolicy||{},o=r.Owner||{},i=r.AccessControlList.Grant||[];i=Z.isArray(i)?i:[i];var a=V(r);n.headers&&n.headers["x-cos-acl"]&&(a.ACL=n.headers["x-cos-acl"]),a=Z.extend(a,{Owner:o,Grants:i,statusCode:n.statusCode,headers:n.headers}),t(null,a)})}function s(e,t){var n=e.Headers,r="";if(e.AccessControlPolicy){var o=Z.clone(e.AccessControlPolicy||{}),i=o.Grants||o.Grant;i=Z.isArray(i)?i:[i],delete o.Grant,delete o.Grants,o.AccessControlList={Grant:i},r=Z.json2xml({AccessControlPolicy:o}),n["Content-Type"]="application/xml",n["Content-MD5"]=Z.binaryBase64(Z.md5(r))}Z.each(n,function(e,t){0===t.indexOf("x-cos-grant-")&&(n[t]=W(n[t]))}),J.call(this,{method:"PUT",Bucket:e.Bucket,Region:e.Region,headers:n,action:"acl",body:r},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})}function u(e,t){J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors"},function(e,n){if(e)if(404===e.statusCode&&e.error&&"NoSuchCORSConfiguration"===e.error.Code){var r={CORSRules:[],statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else t(e);else{var o=n.CORSConfiguration||{},i=o.CORSRules||o.CORSRule||[];i=Z.clone(Z.isArray(i)?i:[i]),Z.each(i,function(e){Z.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],function(t,n){var r=t+"s",o=e[r]||e[t]||[];delete e[t],e[r]=Z.isArray(o)?o:[o]})}),t(null,{CORSRules:i,statusCode:n.statusCode,headers:n.headers})}})}function c(e,t){var n=e.CORSConfiguration||{},r=n.CORSRules||e.CORSRules||[];r=Z.clone(Z.isArray(r)?r:[r]),Z.each(r,function(e){Z.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],function(t,n){var r=t+"s",o=e[r]||e[t]||[];delete e[r],e[t]=Z.isArray(o)?o:[o]})});var o=Z.json2xml({CORSConfiguration:{CORSRule:r}}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=Z.binaryBase64(Z.md5(o)),J.call(this,{method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"cors",headers:i},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})}function l(e,t){J.call(this,{method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode||e.statusCode,headers:n.headers})})}function d(e,t){var n=e.Policy,r=n;try{"string"==typeof n?n=JSON.parse(r):r=JSON.stringify(n)}catch(e){t({error:"Policy format error"})}var o=e.Headers;o["Content-Type"]="application/json",o["Content-MD5"]=Z.binaryBase64(Z.md5(r)),J.call(this,{method:"PUT",Bucket:e.Bucket,Region:e.Region,action:"policy",body:Z.isBrowser?r:n,headers:o,json:!0},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}function f(e,t){J.call(this,{method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode||e.statusCode,headers:n.headers})})}function p(e,t){J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"location"},function(e,n){if(e)return t(e);t(null,n)})}function h(e,t){J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy",rawBody:!0},function(e,n){if(e)return t(e.statusCode&&403===e.statusCode?{ErrorStatus:"Access Denied"}:e.statusCode&&405===e.statusCode?{ErrorStatus:"Method Not Allowed"}:e.statusCode&&404===e.statusCode?{ErrorStatus:"Policy Not Found"}:e);var r={};try{r=JSON.parse(n.body)}catch(e){}t(null,{Policy:r,statusCode:n.statusCode,headers:n.headers})})}function g(e,t){J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging"},function(e,n){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"NoSuchTagSet"!==e.error.Code)t(e);else{var r={Tags:[],statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else{var o=[];try{o=n.Tagging.TagSet.Tag||[]}catch(e){}o=Z.clone(Z.isArray(o)?o:[o]),t(null,{Tags:o,statusCode:n.statusCode,headers:n.headers})}})}function m(e,t){var n=e.Tagging||{},r=n.TagSet||n.Tags||e.Tags||[];r=Z.clone(Z.isArray(r)?r:[r]);var o=Z.json2xml({Tagging:{TagSet:{Tag:r}}}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=Z.binaryBase64(Z.md5(o)),J.call(this,{method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"tagging",headers:i},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}function y(e,t){J.call(this,{method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}function v(e,t){var n=e.LifecycleConfiguration||{},r=n.Rules||[];r=Z.clone(r);var o=Z.json2xml({LifecycleConfiguration:{Rule:r}}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=Z.binaryBase64(Z.md5(o)),J.call(this,{method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"lifecycle",headers:i},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}function C(e,t){J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle"},function(e,n){if(e)if(404===e.statusCode&&e.error&&"NoSuchLifecycleConfiguration"===e.error.Code){var r={Rules:[],statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else t(e);else{var o=[];try{o=n.LifecycleConfiguration.Rule||[]}catch(e){}o=Z.clone(Z.isArray(o)?o:[o]),t(null,{Rules:o,statusCode:n.statusCode,headers:n.headers})}})}function x(e,t){J.call(this,{method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}function b(e,t){if(!e.VersioningConfiguration)return void t({error:"missing param VersioningConfiguration"});var n=e.VersioningConfiguration||{},r=Z.json2xml({VersioningConfiguration:n}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=Z.binaryBase64(Z.md5(r)),J.call(this,{method:"PUT",Bucket:e.Bucket,Region:e.Region,body:r,action:"versioning",headers:o},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}function k(e,t){J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"versioning"},function(e,n){e||!n.VersioningConfiguration&&(n.VersioningConfiguration={}),t(e,n)})}function S(e,t){var n=Z.clone(e.ReplicationConfiguration),r=Z.json2xml({ReplicationConfiguration:n});r=r.replace(/<(\/?)Rules>/gi,"<$1Rule>"),r=r.replace(/<(\/?)Tags>/gi,"<$1Tag>");var o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=Z.binaryBase64(Z.md5(r)),J.call(this,{method:"PUT",Bucket:e.Bucket,Region:e.Region,body:r,action:"replication",headers:o},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}function w(e,t){J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication"},function(e,n){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"ReplicationConfigurationnotFoundError"!==e.error.Code)t(e);else{var r={ReplicationConfiguration:{Rules:[]},statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else e||!n.ReplicationConfiguration&&(n.ReplicationConfiguration={}),t(e,n)})}function T(e,t){J.call(this,{method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}function R(e,t){J.call(this,{method:"HEAD",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers},function(n,r){if(n){var o=n.statusCode;return e.Headers["If-Modified-Since"]&&o&&304===o?t(null,{NotModified:!0,statusCode:o}):t(n)}r.headers&&r.headers.etag&&(r.ETag=r.headers&&r.headers.etag),t(null,r)})}function E(e,t){var n={};n.prefix=e.Prefix,n.delimiter=e.Delimiter,n["key-marker"]=e.KeyMarker,n["version-id-marker"]=e.VersionIdMarker,n["max-keys"]=e.MaxKeys,n["encoding-type"]=e.EncodingType,J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:n,action:"versions"},function(e,n){if(e)return t(e);var r=n.ListVersionsResult||{},o=r.DeleteMarker||[];o=Z.isArray(o)?o:[o];var i=r.Version||[];i=Z.isArray(i)?i:[i];var a=Z.clone(r);delete a.DeleteMarker,delete a.Version,Z.extend(a,{DeleteMarkers:o,Versions:i,statusCode:n.statusCode,headers:n.headers}),t(null,a)})}function N(e,t){var n={};n["response-content-type"]=e.ResponseContentType,n["response-content-language"]=e.ResponseContentLanguage,n["response-expires"]=e.ResponseExpires,n["response-cache-control"]=e.ResponseCacheControl,n["response-content-disposition"]=e.ResponseContentDisposition,n["response-content-encoding"]=e.ResponseContentEncoding;J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers,qs:n,rawBody:!0},function(n,r){if(n){var o=n.statusCode;return e.Headers["If-Modified-Since"]&&o&&304===o?t(null,{NotModified:!0}):t(n)}var i={};i.Body=r.body,r.headers&&r.headers.etag&&(i.ETag=r.headers&&r.headers.etag),Z.extend(i,{statusCode:r.statusCode,headers:r.headers}),t(null,i)})}function A(e,t){var n=this,r=e.ContentLength,o=Z.throttleOnProgress.call(n,r,e.onProgress);J.call(n,{TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,body:e.Body,onProgress:o},function(i,a){if(i)return o(null,!0),t(i);if(o({loaded:r,total:r},!0),a&&a.headers&&a.headers.etag){var s=X({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key});return s=s.substr(s.indexOf("://")+3),t(null,{Location:s,ETag:a.headers.etag,statusCode:a.statusCode,headers:a.headers})}t(null,a)})}function _(e,t){J.call(this,{method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,VersionId:e.VersionId},function(e,n){if(e){var r=e.statusCode;return r&&204===r?t(null,{statusCode:r}):r&&404===r?t(null,{BucketNotFound:!0,statusCode:r}):t(e)}t(null,{statusCode:n.statusCode,headers:n.headers})})}function B(e,t){J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"acl"},function(e,n){if(e)return t(e);var r=n.AccessControlPolicy||{},o=r.Owner||{},i=r.AccessControlList&&r.AccessControlList.Grant||[];i=Z.isArray(i)?i:[i];var a=V(r);n.headers&&n.headers["x-cos-acl"]&&(a.ACL=n.headers["x-cos-acl"]),a=Z.extend(a,{Owner:o,Grants:i,statusCode:n.statusCode,headers:n.headers}),t(null,a)})}function I(e,t){var n=e.Headers,r="";if(e.AccessControlPolicy){var o=Z.clone(e.AccessControlPolicy||{}),i=o.Grants||o.Grant;i=Z.isArray(i)?i:[i],delete o.Grant,delete o.Grants,o.AccessControlList={Grant:i},r=Z.json2xml({AccessControlPolicy:o}),n["Content-Type"]="application/xml",n["Content-MD5"]=Z.binaryBase64(Z.md5(r))}Z.each(n,function(e,t){0===t.indexOf("x-cos-grant-")&&(n[t]=W(n[t]))}),J.call(this,{method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"acl",headers:n,body:r},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})}function D(e,t){var n=e.Headers;n.Origin=e.Origin,n["Access-Control-Request-Method"]=e.AccessControlRequestMethod,n["Access-Control-Request-Headers"]=e.AccessControlRequestHeaders,J.call(this,{method:"OPTIONS",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:n},function(e,n){if(e)return e.statusCode&&403===e.statusCode?t(null,{OptionsForbidden:!0,statusCode:e.statusCode}):t(e);var r=n.headers||{};t(null,{AccessControlAllowOrigin:r["access-control-allow-origin"],AccessControlAllowMethods:r["access-control-allow-methods"],AccessControlAllowHeaders:r["access-control-allow-headers"],AccessControlExposeHeaders:r["access-control-expose-headers"],AccessControlMaxAge:r["access-control-max-age"],statusCode:n.statusCode,headers:n.headers})})}function P(e,t){J.call(this,{method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers},function(e,n){if(e)return t(e);var r=Z.clone(n.CopyObjectResult||{});Z.extend(r,{statusCode:n.statusCode,headers:n.headers}),t(null,r)})}function O(e,t){J.call(this,{method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,qs:{partNumber:e.PartNumber,uploadId:e.UploadId},headers:e.Headers},function(e,n){if(e)return t(e);var r=Z.clone(n.CopyPartResult||{});Z.extend(r,{statusCode:n.statusCode,headers:n.headers}),t(null,r)})}function L(e,t){var n=e.Objects||{},r=e.Quiet,o=Z.json2xml({Delete:{Object:n,Quiet:r||!1}}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=Z.binaryBase64(Z.md5(o)),J.call(this,{method:"POST",Bucket:e.Bucket,Region:e.Region,body:o,action:"delete",headers:i},function(e,n){if(e)return t(e);var r=n.DeleteResult||{},o=r.Deleted||[],i=r.Error||[];o=Z.isArray(o)?o:[o],i=Z.isArray(i)?i:[i];var a=Z.clone(r);Z.extend(a,{Error:i,Deleted:o,statusCode:n.statusCode,headers:n.headers}),t(null,a)})}function j(e,t){var n=e.Headers;if(!e.RestoreRequest)return void t({error:"missing param RestoreRequest"});var r=e.RestoreRequest||{},o=Z.json2xml({RestoreRequest:r});n["Content-Type"]="application/xml",n["Content-MD5"]=Z.binaryBase64(Z.md5(o)),J.call(this,{method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,body:o,action:"restore",headers:n},function(e,n){t(e,n)})}function M(e,t){J.call(this,{method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"uploads",headers:e.Headers},function(e,n){return e?t(e):(n=Z.clone(n||{}))&&n.InitiateMultipartUploadResult?t(null,Z.extend(n.InitiateMultipartUploadResult,{statusCode:n.statusCode,headers:n.headers})):void t(null,n)})}function U(e,t){var n=this;Z.getFileSize("multipartUpload",e,function(){J.call(n,{TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{partNumber:e.PartNumber,uploadId:e.UploadId},headers:e.Headers,onProgress:e.onProgress,body:e.Body||null},function(e,n){if(e)return t(e);n.headers=n.headers||{},t(null,{ETag:n.headers.etag||"",statusCode:n.statusCode,headers:n.headers})})})}function F(e,t){for(var n=this,r=e.UploadId,o=e.Parts,i=0,a=o.length;i<a;i++)0!==o[i].ETag.indexOf('"')&&(o[i].ETag='"'+o[i].ETag+'"');var s=Z.json2xml({CompleteMultipartUpload:{Part:o}}),u=e.Headers;u["Content-Type"]="application/xml",u["Content-MD5"]=Z.binaryBase64(Z.md5(s)),J.call(this,{method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{uploadId:r},body:s,headers:u},function(r,o){if(r)return t(r);var i=X({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key,isLocation:!0}),a=o.CompleteMultipartUploadResult||{},s=Z.extend(a,{Location:i,statusCode:o.statusCode,headers:o.headers});t(null,s)})}function H(e,t){var n={};n.delimiter=e.Delimiter,n["encoding-type"]=e.EncodingType,n.prefix=e.Prefix,n["max-uploads"]=e.MaxUploads,n["key-marker"]=e.KeyMarker,n["upload-id-marker"]=e.UploadIdMarker,n=Z.clearKey(n),J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:n,action:"uploads"},function(e,n){if(e)return t(e);if(n&&n.ListMultipartUploadsResult){var r=n.ListMultipartUploadsResult.Upload||[],o=n.ListMultipartUploadsResult.CommonPrefixes||[];o=Z.isArray(o)?o:[o],r=Z.isArray(r)?r:[r],n.ListMultipartUploadsResult.Upload=r,n.ListMultipartUploadsResult.CommonPrefixes=o}var i=Z.clone(n.ListMultipartUploadsResult||{});Z.extend(i,{statusCode:n.statusCode,headers:n.headers}),t(null,i)})}function K(e,t){var n={};n.uploadId=e.UploadId,n["encoding-type"]=e.EncodingType,n["max-parts"]=e.MaxParts,n["part-number-marker"]=e.PartNumberMarker,J.call(this,{method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:n},function(e,n){if(e)return t(e);var r=n.ListPartsResult||{},o=r.Part||[];o=Z.isArray(o)?o:[o],r.Part=o;var i=Z.clone(r);Z.extend(i,{statusCode:n.statusCode,headers:n.headers}),t(null,i)})}function z(e,t){var n={};n.uploadId=e.UploadId,J.call(this,{method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:n},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})}function q(e){return Z.getAuth({SecretId:e.SecretId||this.options.SecretId||"",SecretKey:e.SecretKey||this.options.SecretKey||"",Method:e.Method,Key:e.Key,Query:e.Query,Headers:e.Headers,Expires:e.Expires})}function G(e,t){var n=this,r=X({ForcePathStyle:n.options.ForcePathStyle,protocol:e.Protocol||n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key});if(void 0!==e.Sign&&!e.Sign)return t(null,{Url:r}),r;var o=$.call(this,{Bucket:e.Bucket||"",Region:e.Region||"",Method:e.Method||"get",Key:e.Key,Expires:e.Expires},function(e){if(t){var n=r;n+="?sign="+encodeURIComponent(e.Authorization),e.XCosSecurityToken&&(n+="&x-cos-security-token="+e.XCosSecurityToken),e.ClientIP&&(n+="&clientIP="+e.ClientIP),e.ClientUA&&(n+="&clientUA="+e.ClientUA),e.Token&&(n+="&token="+e.Token),setTimeout(function(){t(null,{Url:n})})}});return o?r+"?sign="+encodeURIComponent(o):r}function V(e){var t={GrantFullControl:[],GrantWrite:[],GrantRead:[],GrantReadAcp:[],GrantWriteAcp:[],ACL:""},n={FULL_CONTROL:"GrantFullControl",WRITE:"GrantWrite",READ:"GrantRead",READ_ACP:"GrantReadAcp",WRITE_ACP:"GrantWriteAcp"},r=e.AccessControlList.Grant;r&&(r=Z.isArray(r)?r:[r]);var o={READ:0,WRITE:0,FULL_CONTROL:0};return r.length&&Z.each(r,function(r){"qcs::cam::anyone:anyone"===r.Grantee.ID||"http://cam.qcloud.com/groups/global/AllUsers"===r.Grantee.URI?o[r.Permission]=1:r.Grantee.ID!==e.Owner.ID&&t[n[r.Permission]].push('id="'+r.Grantee.ID+'"')}),o.FULL_CONTROL||o.WRITE&&o.READ?t.ACL="public-read-write":o.READ?t.ACL="public-read":t.ACL="private",Z.each(n,function(e){t[e]=W(t[e].join(","))}),t}function W(e){var t,n,r=e.split(","),o={};for(t=0;t<r.length;)n=r[t].trim(),o[n]?r.splice(t,1):(o[n]=!0,r[t]=n,t++);return r.join(",")}function X(e){var t=e.bucket,n=t.substr(0,t.lastIndexOf("-")),r=t.substr(t.lastIndexOf("-")+1),o=e.domain,i=e.region,a=e.object,s=e.protocol||(Z.isBrowser&&"http:"===location.protocol?"http:":"https:");o||(o=["cn-south","cn-south-2","cn-north","cn-east","cn-southwest","sg"].indexOf(i)>-1?"{Region}.myqcloud.com":"cos.{Region}.myqcloud.com",e.ForcePathStyle||(o="{Bucket}."+o)),o=o.replace(/\{\{AppId\}\}/gi,r).replace(/\{\{Bucket\}\}/gi,n).replace(/\{\{Region\}\}/gi,i).replace(/\{\{.*?\}\}/gi,""),o=o.replace(/\{AppId\}/gi,r).replace(/\{BucketName\}/gi,n).replace(/\{Bucket\}/gi,t).replace(/\{Region\}/gi,i).replace(/\{.*?\}/gi,""),/^[a-zA-Z]+:\/\//.test(o)||(o=s+"//"+o),"/"===o.slice(-1)&&(o=o.slice(0,-1));var u=o;return e.ForcePathStyle&&(u+="/"+t),u+="/",a&&(u+=encodeURIComponent(a).replace(/%2F/g,"/")),e.isLocation&&(u=u.replace(/^https?:\/\//,"")),u}function $(e,t){var n=this,r=e.Bucket||"",o=e.Region||"";n._StsMap=n._StsMap||{};var i=n._StsMap[r+"."+o]||{},a=e.Key||"";n.options.ForcePathStyle&&r&&(a=r+"/"+a);var s=function(){var n=Z.getAuth({SecretId:i.TmpSecretId,SecretKey:i.TmpSecretKey,Method:e.Method,Key:a,Query:e.Query,Headers:e.Headers}),r={Authorization:n,XCosSecurityToken:i.XCosSecurityToken||"",Token:i.Token||"",ClientIP:i.ClientIP||"",ClientUA:i.ClientUA||""};t&&t(r)};if(i.ExpiredTime&&i.ExpiredTime-Date.now()/1e3>60)s();else if(n.options.getAuthorization)n.options.getAuthorization.call(n,{Bucket:r,Region:o,Method:e.Method,Key:a,Query:e.Query,Headers:e.Headers},function(e){"string"==typeof e&&(e={Authorization:e}),e.TmpSecretId&&e.TmpSecretKey&&e.XCosSecurityToken&&e.ExpiredTime?(i=n._StsMap[r+"."+o]=e,s()):t&&t(e)});else{if(!n.options.getSTS){var u=Z.getAuth({SecretId:e.SecretId||n.options.SecretId,SecretKey:e.SecretKey||n.options.SecretKey,Method:e.Method,Key:a,Query:e.Query,Headers:e.Headers,Expires:e.Expires});return t&&t({Authorization:u}),u}n.options.getSTS.call(n,{Bucket:r,Region:o},function(e){i=n._StsMap[r+"."+o]=e||{},i.TmpSecretId=i.SecretId,i.TmpSecretKey=i.SecretKey,s()})}return""}function J(e,t){var n=this;!e.headers&&(e.headers={}),!e.qs&&(e.qs={}),e.VersionId&&(e.qs.versionId=e.VersionId),e.qs=Z.clearKey(e.qs),e.headers&&(e.headers=Z.clearKey(e.headers)),e.qs&&(e.qs=Z.clearKey(e.qs));var r=Z.clone(e.qs);e.action&&(r[e.action]=""),$.call(n,{Bucket:e.Bucket||"",Region:e.Region||"",Method:e.method,Key:e.Key,Query:r,Headers:e.headers},function(r){var o=r.Authorization,i=!1;if(o)if(o.indexOf(" ")>-1)i=!1;else if(o.indexOf("q-sign-algorithm=")>-1&&o.indexOf("q-ak=")>-1&&o.indexOf("q-sign-time=")>-1&&o.indexOf("q-key-time=")>-1&&o.indexOf("q-url-param-list=")>-1)i=!0;else try{o=atob(o),o.indexOf("a=")>-1&&o.indexOf("k=")>-1&&o.indexOf("t=")>-1&&o.indexOf("r=")>-1&&o.indexOf("b=")>-1&&(i=!0)}catch(e){}if(!i)return void t("authorization error");e.AuthData=r,Q.call(n,e,t)})}function Q(e,t){var n=this,r=e.TaskId;if(!r||n._isRunningTask(r)){var o=e.Bucket,i=e.Region,a=e.Key,s=e.method||"GET",u=e.url,c=e.body,l=e.json,d=e.rawBody;u=u||X({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:o,region:i,object:a}),e.action&&(u=u+"?"+e.action);var f={method:s,url:u,headers:e.headers,qs:e.qs,body:c,json:l};if(f.headers.Authorization=e.AuthData.Authorization,e.AuthData.Token&&(f.headers.token=e.AuthData.Token),e.AuthData.ClientIP&&(f.headers.clientIP=e.AuthData.ClientIP),e.AuthData.ClientUA&&(f.headers.clientUA=e.AuthData.ClientUA),e.AuthData.XCosSecurityToken&&(f.headers["x-cos-security-token"]=e.AuthData.XCosSecurityToken),f.headers&&(f.headers=Z.clearKey(f.headers)),f=Z.clearKey(f),e.onProgress&&"function"==typeof e.onProgress){var p=c&&(c.size||c.length)||0;f.onProgress=function(t){if(!r||n._isRunningTask(r)){var o=t?t.loaded:0;e.onProgress({loaded:o,total:p})}}}n.emit("before-send",f);var h=Y(f,function(e,o,i){var a,s=function(e,i){if(r&&n.off("inner-kill-task",g),!a){a=!0;var s={};o&&o.statusCode&&(s.statusCode=o.statusCode),o&&o.headers&&(s.headers=o.headers),e?(e=Z.extend(e||{},s),t(e,null)):(i=Z.extend(i||{},s),t(null,i))}};if(e)return void s({error:e});var u;try{u=Z.xml2json(i)||{}}catch(e){u=i||{}}var c=o.statusCode;return 2!==Math.floor(c/100)?void s({error:u.Error||u}):(d&&(u={},u.body=i),u.Error?void s({error:u.Error}):void s(null,u))}),g=function(e){e.TaskId===r&&(h&&h.abort&&h.abort(),n.off("inner-kill-task",g))};r&&n.on("inner-kill-task",g)}}var Y=n(14),Z=n(0),ee={getBucket:o,headBucket:r,deleteBucket:i,getBucketAcl:a,putBucketAcl:s,getBucketCors:u,putBucketCors:c,deleteBucketCors:l,getBucketLocation:p,putBucketTagging:m,getBucketTagging:g,deleteBucketTagging:y,getBucketPolicy:h,putBucketPolicy:d,deleteBucketPolicy:f,getBucketLifecycle:C,putBucketLifecycle:v,deleteBucketLifecycle:x,putBucketVersioning:b,getBucketVersioning:k,putBucketReplication:S,getBucketReplication:w,deleteBucketReplication:T,getObject:N,headObject:R,listObjectVersions:E,putObject:A,deleteObject:_,getObjectAcl:B,putObjectAcl:I,optionsObject:D,putObjectCopy:P,deleteMultipleObject:L,restoreObject:j,uploadPartCopy:O,multipartInit:M,multipartUpload:U,multipartComplete:F,multipartList:H,multipartListPart:K,multipartAbort:z,getObjectUrl:G,getAuth:q};Z.each(ee,function(e,n){t[n]=Z.apiWrapper(n,e)})},function(e,t,n){var r=n(15),o=function(){function e(e){var t=e.length,n=N.type(e);return"function"!==n&&!N.isWindow(e)&&(!(1!==e.nodeType||!t)||("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e))}function t(e){var t=j[e]={};return N.each(e.match(L)||[],function(e,n){t[n]=!0}),t}function n(){P.addEventListener?(P.removeEventListener("DOMContentLoaded",r,!1),window.removeEventListener("load",r,!1)):(P.detachEvent("onreadystatechange",r),window.detachEvent("onload",r))}function r(){(P.addEventListener||"load"===event.type||"complete"===P.readyState)&&(n(),N.ready())}function o(e,t,n){if(void 0===n&&1===e.nodeType){var r="data-"+t.replace(H,"-$1").toLowerCase();if("string"==typeof(n=e.getAttribute(r))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:F.test(n)?N.parseJSON(n):n)}catch(e){}N.data(e,t,n)}else n=void 0}return n}function i(e){var t;for(t in e)if(("data"!==t||!N.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}function a(e,t,n,r){if(N.acceptData(e)){var o,i,a=N.expando,s=e.nodeType,u=s?N.cache:e,c=s?e[a]:e[a]&&a;if(c&&u[c]&&(r||u[c].data)||void 0!==n||"string"!=typeof t)return c||(c=s?e[a]=v.pop()||N.guid++:a),u[c]||(u[c]=s?{}:{toJSON:N.noop}),"object"!=typeof t&&"function"!=typeof t||(r?u[c]=N.extend(u[c],t):u[c].data=N.extend(u[c].data,t)),i=u[c],r||(i.data||(i.data={}),i=i.data),void 0!==n&&(i[N.camelCase(t)]=n),"string"==typeof t?null==(o=i[t])&&(o=i[N.camelCase(t)]):o=i,o}}function s(e,t,n){if(N.acceptData(e)){var r,o,a=e.nodeType,s=a?N.cache:e,u=a?e[N.expando]:N.expando;if(s[u]){if(t&&(r=n?s[u]:s[u].data)){N.isArray(t)?t=t.concat(N.map(t,N.camelCase)):t in r?t=[t]:(t=N.camelCase(t),t=t in r?[t]:t.split(" ")),o=t.length;for(;o--;)delete r[t[o]];if(n?!i(r):!N.isEmptyObject(r))return}(n||(delete s[u].data,i(s[u])))&&(a?N.cleanData([e],!0):R.deleteExpando||s!=s.window?delete s[u]:s[u]=null)}}}function u(){return!0}function c(){return!1}function l(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,o=0,i=t.toLowerCase().match(L)||[];if(N.isFunction(n))for(;r=i[o++];)"+"===r.charAt(0)?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function d(e,t,n,r){function o(s){var u;return i[s]=!0,N.each(e[s]||[],function(e,s){var c=s(t,n,r);return"string"!=typeof c||a||i[c]?a?!(u=c):void 0:(t.dataTypes.unshift(c),o(c),!1)}),u}var i={},a=e===ae;return o(t.dataTypes[0])||!i["*"]&&o("*")}function f(e,t){var n,r,o=N.ajaxSettings.flatOptions||{};for(r in t)void 0!==t[r]&&((o[r]?e:n||(n={}))[r]=t[r]);return n&&N.extend(!0,e,n),e}function p(e,t,n){for(var r,o,i,a,s=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===o&&(o=e.mimeType||t.getResponseHeader("Content-Type"));if(o)for(a in s)if(s[a]&&s[a].test(o)){u.unshift(a);break}if(u[0]in n)i=u[0];else{for(a in n){if(!u[0]||e.converters[a+" "+u[0]]){i=a;break}r||(r=a)}i=i||r}if(i)return i!==u[0]&&u.unshift(i),n[i]}function h(e,t,n,r){var o,i,a,s,u,c={},l=e.dataTypes.slice();if(l[1])for(a in e.converters)c[a.toLowerCase()]=e.converters[a];for(i=l.shift();i;)if(e.responseFields[i]&&(n[e.responseFields[i]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=i,i=l.shift())if("*"===i)i=u;else if("*"!==u&&u!==i){if(!(a=c[u+" "+i]||c["* "+i]))for(o in c)if(s=o.split(" "),s[1]===i&&(a=c[u+" "+s[0]]||c["* "+s[0]])){!0===a?a=c[o]:!0!==c[o]&&(i=s[0],l.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+u+" to "+i}}}return{state:"success",data:t}}function g(e,t,n,r){var o;if(N.isArray(t))N.each(t,function(t,o){n||ce.test(e)?r(e,o):g(e+"["+("object"==typeof o?t:"")+"]",o,n,r)});else if(n||"object"!==N.type(t))r(e,t);else for(o in t)g(e+"["+o+"]",t[o],n,r)}function m(){try{return new window.XMLHttpRequest}catch(e){}}function y(){try{return new window.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}var v=[],C=v.slice,x=v.concat,b=v.push,k=v.indexOf,S={},w=S.toString,T=S.hasOwnProperty,R={},E="1.11.1 -css,-css/addGetHookIf,-css/curCSS,-css/defaultDisplay,-css/hiddenVisibleSelectors,-css/support,-css/swap,-css/var/cssExpand,-css/var/isHidden,-css/var/rmargin,-css/var/rnumnonpx,-effects,-effects/Tween,-effects/animatedSelector,-effects/support,-dimensions,-offset,-deprecated,-event-alias,-wrap",N=function(e,t){return new N.fn.init(e,t)},A=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,_=/^-ms-/,B=/-([\da-z])/gi,I=function(e,t){return t.toUpperCase()};N.fn=N.prototype={jquery:E,constructor:N,selector:"",length:0,toArray:function(){return C.call(this)},get:function(e){return null!=e?e<0?this[e+this.length]:this[e]:C.call(this)},pushStack:function(e){var t=N.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return N.each(this,e,t)},map:function(e){return this.pushStack(N.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(C.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:b,sort:v.sort,splice:v.splice},N.extend=N.fn.extend=function(){var e,t,n,r,o,i,a=arguments[0]||{},s=1,u=arguments.length,c=!1;for("boolean"==typeof a&&(c=a,a=arguments[s]||{},s++),"object"==typeof a||N.isFunction(a)||(a={}),s===u&&(a=this,s--);s<u;s++)if(null!=(o=arguments[s]))for(r in o)e=a[r],n=o[r],a!==n&&(c&&n&&(N.isPlainObject(n)||(t=N.isArray(n)))?(t?(t=!1,i=e&&N.isArray(e)?e:[]):i=e&&N.isPlainObject(e)?e:{},a[r]=N.extend(c,i,n)):void 0!==n&&(a[r]=n));return a},N.extend({expando:"jQuery"+(E+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===N.type(e)},isArray:Array.isArray||function(e){return"array"===N.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){return!N.isArray(e)&&e-parseFloat(e)>=0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},isPlainObject:function(e){var t;if(!e||"object"!==N.type(e)||e.nodeType||N.isWindow(e))return!1;try{if(e.constructor&&!T.call(e,"constructor")&&!T.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}if(R.ownLast)for(t in e)return T.call(e,t);for(t in e);return void 0===t||T.call(e,t)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?S[w.call(e)]||"object":typeof e},globalEval:function(e){e&&N.trim(e)&&(window.execScript||function(e){window.eval.call(window,e)})(e)},camelCase:function(e){return e.replace(_,"ms-").replace(B,I)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(t,n,r){var o=0,i=t.length,a=e(t);if(r){if(a)for(;o<i&&!1!==n.apply(t[o],r);o++);else for(o in t)if(!1===n.apply(t[o],r))break}else if(a)for(;o<i&&!1!==n.call(t[o],o,t[o]);o++);else for(o in t)if(!1===n.call(t[o],o,t[o]))break;return t},trim:function(e){return null==e?"":(e+"").replace(A,"")},makeArray:function(t,n){var r=n||[];return null!=t&&(e(Object(t))?N.merge(r,"string"==typeof t?[t]:t):b.call(r,t)),r},inArray:function(e,t,n){var r;if(t){if(k)return k.call(t,e,n);for(r=t.length,n=n?n<0?Math.max(0,r+n):n:0;n<r;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){for(var n=+t.length,r=0,o=e.length;r<n;)e[o++]=t[r++];if(n!==n)for(;void 0!==t[r];)e[o++]=t[r++];return e.length=o,e},grep:function(e,t,n){for(var r=[],o=0,i=e.length,a=!n;o<i;o++)!t(e[o],o)!==a&&r.push(e[o]);return r},map:function(t,n,r){var o,i=0,a=t.length,s=e(t),u=[];if(s)for(;i<a;i++)null!=(o=n(t[i],i,r))&&u.push(o);else for(i in t)null!=(o=n(t[i],i,r))&&u.push(o);return x.apply([],u)},guid:1,proxy:function(e,t){var n,r,o;if("string"==typeof t&&(o=e[t],t=e,e=o),N.isFunction(e))return n=C.call(arguments,2),r=function(){return e.apply(t||this,n.concat(C.call(arguments)))},r.guid=e.guid=e.guid||N.guid++,r},now:function(){return+new Date},support:R}),N.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){S["[object "+t+"]"]=t.toLowerCase()});var D,P=window.document,O=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;(N.fn.init=function(e,t){var n,r;if(!e)return this;if("string"==typeof e){if(!(n="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:O.exec(e))||!n[1]&&t)return!t||t.jquery?(t||D).find(e):this.constructor(t).find(e);if(n[1]){if(t=t instanceof N?t[0]:t,N.merge(this,N.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:P,!0)),rsingleTag.test(n[1])&&N.isPlainObject(t))for(n in t)N.isFunction(this[n])?this[n](t[n]):this.attr(n,t[n]);return this}if((r=P.getElementById(n[2]))&&r.parentNode){if(r.id!==n[2])return D.find(e);this.length=1,this[0]=r}return this.context=P,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):N.isFunction(e)?void 0!==D.ready?D.ready(e):e(N):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),N.makeArray(e,this))}).prototype=N.fn,D=N(P);var L=/\S+/g,j={};N.Callbacks=function(e){e="string"==typeof e?j[e]||t(e):N.extend({},e);var n,r,o,i,a,s,u=[],c=!e.once&&[],l=function(t){for(r=e.memory&&t,o=!0,a=s||0,s=0,i=u.length,n=!0;u&&a<i;a++)if(!1===u[a].apply(t[0],t[1])&&e.stopOnFalse){r=!1;break}n=!1,u&&(c?c.length&&l(c.shift()):r?u=[]:d.disable())},d={add:function(){if(u){var t=u.length;!function t(n){N.each(n,function(n,r){var o=N.type(r);"function"===o?e.unique&&d.has(r)||u.push(r):r&&r.length&&"string"!==o&&t(r)})}(arguments),n?i=u.length:r&&(s=t,l(r))}return this},remove:function(){return u&&N.each(arguments,function(e,t){for(var r;(r=N.inArray(t,u,r))>-1;)u.splice(r,1),n&&(r<=i&&i--,r<=a&&a--)}),this},has:function(e){return e?N.inArray(e,u)>-1:!(!u||!u.length)},empty:function(){return u=[],i=0,this},disable:function(){return u=c=r=void 0,this},disabled:function(){return!u},lock:function(){return c=void 0,r||d.disable(),this},locked:function(){return!c},fireWith:function(e,t){return!u||o&&!c||(t=t||[],t=[e,t.slice?t.slice():t],n?c.push(t):l(t)),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!o}};return d},N.extend({Deferred:function(e){var t=[["resolve","done",N.Callbacks("once memory"),"resolved"],["reject","fail",N.Callbacks("once memory"),"rejected"],["notify","progress",N.Callbacks("memory")]],n="pending",r={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},then:function(){var e=arguments;return N.Deferred(function(n){N.each(t,function(t,i){var a=N.isFunction(e[t])&&e[t];o[i[1]](function(){var e=a&&a.apply(this,arguments);e&&N.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[i[0]+"With"](this===r?n.promise():this,a?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?N.extend(e,r):r}},o={};return r.pipe=r.then,N.each(t,function(e,i){var a=i[2],s=i[3];r[i[1]]=a.add,s&&a.add(function(){n=s},t[1^e][2].disable,t[2][2].lock),o[i[0]]=function(){return o[i[0]+"With"](this===o?r:this,arguments),this},o[i[0]+"With"]=a.fireWith}),r.promise(o),e&&e.call(o,o),o},when:function(e){var t,n,r,o=0,i=C.call(arguments),a=i.length,s=1!==a||e&&N.isFunction(e.promise)?a:0,u=1===s?e:N.Deferred(),c=function(e,n,r){return function(o){n[e]=this,r[e]=arguments.length>1?C.call(arguments):o,r===t?u.notifyWith(n,r):--s||u.resolveWith(n,r)}};if(a>1)for(t=new Array(a),n=new Array(a),r=new Array(a);o<a;o++)i[o]&&N.isFunction(i[o].promise)?i[o].promise().done(c(o,r,i)).fail(u.reject).progress(c(o,n,t)):--s;return s||u.resolveWith(r,i),u.promise()}});var M;N.fn.ready=function(e){return N.ready.promise().done(e),this},N.extend({isReady:!1,readyWait:1,holdReady:function(e){e?N.readyWait++:N.ready(!0)},ready:function(e){if(!0===e?!--N.readyWait:!N.isReady){if(!P.body)return setTimeout(N.ready);N.isReady=!0,!0!==e&&--N.readyWait>0||(M.resolveWith(P,[N]),N.fn.triggerHandler&&(N(P).triggerHandler("ready"),N(P).off("ready")))}}}),N.ready.promise=function(e){if(!M)if(M=N.Deferred(),"complete"===P.readyState)setTimeout(N.ready);else if(P.addEventListener)P.addEventListener("DOMContentLoaded",r,!1),window.addEventListener("load",r,!1);else{P.attachEvent("onreadystatechange",r),window.attachEvent("onload",r);var t=!1;try{t=null==window.frameElement&&P.documentElement}catch(e){}t&&t.doScroll&&function e(){if(!N.isReady){try{t.doScroll("left")}catch(t){return setTimeout(e,50)}n(),N.ready()}}()}return M.promise(e)};var U;for(U in N(R))break;R.ownLast="0"!==U,R.inlineBlockNeedsLayout=!1,N(function(){var e,t,n,r;(n=P.getElementsByTagName("body")[0])&&n.style&&(t=P.createElement("div"),r=P.createElement("div"),r.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(r).appendChild(t),void 0!==t.style.zoom&&(t.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",R.inlineBlockNeedsLayout=e=3===t.offsetWidth,e&&(n.style.zoom=1)),n.removeChild(r))}),function(){var e=P.createElement("div");if(null==R.deleteExpando){R.deleteExpando=!0;try{delete e.test}catch(e){R.deleteExpando=!1}}e=null}(),N.acceptData=function(e){var t=N.noData[(e.nodeName+" ").toLowerCase()],n=+e.nodeType||1;return(1===n||9===n)&&(!t||!0!==t&&e.getAttribute("classid")===t)};var F=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,H=/([A-Z])/g;N.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(e){return!!(e=e.nodeType?N.cache[e[N.expando]]:e[N.expando])&&!i(e)},data:function(e,t,n){return a(e,t,n)},removeData:function(e,t){return s(e,t)},_data:function(e,t,n){return a(e,t,n,!0)},_removeData:function(e,t){return s(e,t,!0)}}),N.fn.extend({data:function(e,t){var n,r,i,a=this[0],s=a&&a.attributes;if(void 0===e){if(this.length&&(i=N.data(a),1===a.nodeType&&!N._data(a,"parsedAttrs"))){for(n=s.length;n--;)s[n]&&(r=s[n].name,0===r.indexOf("data-")&&(r=N.camelCase(r.slice(5)),o(a,r,i[r])));N._data(a,"parsedAttrs",!0)}return i}return"object"==typeof e?this.each(function(){N.data(this,e)}):arguments.length>1?this.each(function(){N.data(this,e,t)}):a?o(a,e,N.data(a,e)):void 0},removeData:function(e){return this.each(function(){N.removeData(this,e)})}}),N.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=N._data(e,t),n&&(!r||N.isArray(n)?r=N._data(e,t,N.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=N.queue(e,t),r=n.length,o=n.shift(),i=N._queueHooks(e,t),a=function(){N.dequeue(e,t)};"inprogress"===o&&(o=n.shift(),r--),o&&("fx"===t&&n.unshift("inprogress"),delete i.stop,o.call(e,a,i)),!r&&i&&i.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return N._data(e,n)||N._data(e,n,{empty:N.Callbacks("once memory").add(function(){N._removeData(e,t+"queue"),N._removeData(e,n)})})}}),N.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?N.queue(this[0],e):void 0===t?this:this.each(function(){var n=N.queue(this,e,t);N._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&N.dequeue(this,e)})},dequeue:function(e){return this.each(function(){N.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,o=N.Deferred(),i=this,a=this.length,s=function(){--r||o.resolveWith(i,[i])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(n=N._data(i[a],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(s));return s(),o.promise(t)}}),N.event={global:{},add:function(e,t,n,r,o){var i,a,s,u,c,l,d,f,p,h,g,m=N._data(e);if(m){for(n.handler&&(u=n,n=u.handler,o=u.selector),n.guid||(n.guid=N.guid++),(a=m.events)||(a=m.events={}),(l=m.handle)||(l=m.handle=function(e){return void 0===N||e&&N.event.triggered===e.type?void 0:N.event.dispatch.apply(l.elem,arguments)},l.elem=e),t=(t||"").match(L)||[""],s=t.length;s--;)i=V.exec(t[s])||[],p=g=i[1],h=(i[2]||"").split(".").sort(),p&&(c=N.event.special[p]||{},p=(o?c.delegateType:c.bindType)||p,c=N.event.special[p]||{},d=N.extend({type:p,origType:g,data:r,handler:n,guid:n.guid,selector:o,needsContext:o&&N.expr.match.needsContext.test(o),namespace:h.join(".")},u),(f=a[p])||(f=a[p]=[],f.delegateCount=0,c.setup&&!1!==c.setup.call(e,r,h,l)||(e.addEventListener?e.addEventListener(p,l,!1):e.attachEvent&&e.attachEvent("on"+p,l))),c.add&&(c.add.call(e,d),d.handler.guid||(d.handler.guid=n.guid)),o?f.splice(f.delegateCount++,0,d):f.push(d),N.event.global[p]=!0);e=null}},remove:function(e,t,n,r,o){var i,a,s,u,c,l,d,f,p,h,g,m=N.hasData(e)&&N._data(e);if(m&&(l=m.events)){for(t=(t||"").match(L)||[""],c=t.length;c--;)if(s=V.exec(t[c])||[],p=g=s[1],h=(s[2]||"").split(".").sort(),p){for(d=N.event.special[p]||{},p=(r?d.delegateType:d.bindType)||p,f=l[p]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),u=i=f.length;i--;)a=f[i],!o&&g!==a.origType||n&&n.guid!==a.guid||s&&!s.test(a.namespace)||r&&r!==a.selector&&("**"!==r||!a.selector)||(f.splice(i,1),a.selector&&f.delegateCount--,d.remove&&d.remove.call(e,a));u&&!f.length&&(d.teardown&&!1!==d.teardown.call(e,h,m.handle)||N.removeEvent(e,p,m.handle),delete l[p])}else for(p in l)N.event.remove(e,p+t[c],n,r,!0);N.isEmptyObject(l)&&(delete m.handle,N._removeData(e,"events"))}},trigger:function(e,t,n,r){var o,i,a,s,u,c,l,d=[n||P],f=T.call(e,"type")?e.type:e,p=T.call(e,"namespace")?e.namespace.split("."):[];if(a=c=n=n||P,3!==n.nodeType&&8!==n.nodeType&&!G.test(f+N.event.triggered)&&(f.indexOf(".")>=0&&(p=f.split("."),f=p.shift(),p.sort()),i=f.indexOf(":")<0&&"on"+f,e=e[N.expando]?e:new N.Event(f,"object"==typeof e&&e),e.isTrigger=r?2:3,e.namespace=p.join("."),e.namespace_re=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:N.makeArray(t,[e]),u=N.event.special[f]||{},r||!u.trigger||!1!==u.trigger.apply(n,t))){if(!r&&!u.noBubble&&!N.isWindow(n)){for(s=u.delegateType||f,G.test(s+f)||(a=a.parentNode);a;a=a.parentNode)d.push(a),c=a;c===(n.ownerDocument||P)&&d.push(c.defaultView||c.parentWindow||window)}for(l=0;(a=d[l++])&&!e.isPropagationStopped();)e.type=l>1?s:u.bindType||f,o=(N._data(a,"events")||{})[e.type]&&N._data(a,"handle"),o&&o.apply(a,t),(o=i&&a[i])&&o.apply&&N.acceptData(a)&&(e.result=o.apply(a,t),!1===e.result&&e.preventDefault());if(e.type=f,!r&&!e.isDefaultPrevented()&&(!u._default||!1===u._default.apply(d.pop(),t))&&N.acceptData(n)&&i&&n[f]&&!N.isWindow(n)){c=n[i],c&&(n[i]=null),N.event.triggered=f;try{n[f]()}catch(e){}N.event.triggered=void 0,c&&(n[i]=c)}return e.result}},dispatch:function(e){e=N.event.fix(e);var t,n,r,o,i,a=[],s=C.call(arguments),u=(N._data(this,"events")||{})[e.type]||[],c=N.event.special[e.type]||{};if(s[0]=e,e.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,e)){for(a=N.event.handlers.call(this,e,u),t=0;(o=a[t++])&&!e.isPropagationStopped();)for(e.currentTarget=o.elem,i=0;(r=o.handlers[i++])&&!e.isImmediatePropagationStopped();)e.namespace_re&&!e.namespace_re.test(r.namespace)||(e.handleObj=r,e.data=r.data,void 0!==(n=((N.event.special[r.origType]||{}).handle||r.handler).apply(o.elem,s))&&!1===(e.result=n)&&(e.preventDefault(),e.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,r,o,i,a=[],s=t.delegateCount,u=e.target;if(s&&u.nodeType&&(!e.button||"click"!==e.type))for(;u!=this;u=u.parentNode||this)if(1===u.nodeType&&(!0!==u.disabled||"click"!==e.type)){for(o=[],i=0;i<s;i++)r=t[i],n=r.selector+" ",void 0===o[n]&&(o[n]=r.needsContext?N(n,this).index(u)>=0:N.find(n,this,null,[u]).length),o[n]&&o.push(r);o.length&&a.push({elem:u,handlers:o})}return s<t.length&&a.push({elem:this,handlers:t.slice(s)}),a},fix:function(e){if(e[N.expando])return e;var t,n,r,o=e.type,i=e,a=this.fixHooks[o];for(a||(this.fixHooks[o]=a=q.test(o)?this.mouseHooks:z.test(o)?this.keyHooks:{}),r=a.props?this.props.concat(a.props):this.props,e=new N.Event(i),t=r.length;t--;)n=r[t],e[n]=i[n];return e.target||(e.target=i.srcElement||P),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,a.filter?a.filter(e,i):e},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,o,i=t.button,a=t.fromElement;return null==e.pageX&&null!=t.clientX&&(r=e.target.ownerDocument||P,o=r.documentElement,n=r.body,e.pageX=t.clientX+(o&&o.scrollLeft||n&&n.scrollLeft||0)-(o&&o.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(o&&o.scrollTop||n&&n.scrollTop||0)-(o&&o.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&a&&(e.relatedTarget=a===e.target?t.toElement:a),e.which||void 0===i||(e.which=1&i?1:2&i?3:4&i?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==safeActiveElement()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){if(this===safeActiveElement()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if(N.nodeName(this,"input")&&"checkbox"===this.type&&this.click)return this.click(),!1},_default:function(e){return N.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,r){var o=N.extend(new N.Event,n,{type:e,isSimulated:!0,originalEvent:{}});r?N.event.trigger(o,null,t):N.event.dispatch.call(t,o),o.isDefaultPrevented()&&n.preventDefault()}},N.removeEvent=P.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var r="on"+t;e.detachEvent&&(void 0===e[r]&&(e[r]=null),e.detachEvent(r,n))},N.Event=function(e,t){if(!(this instanceof N.Event))return new N.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?u:c):this.type=e,t&&N.extend(this,t),this.timeStamp=e&&e.timeStamp||N.now(),this[N.expando]=!0};var K=/^(?:input|select|textarea)$/i,z=/^key/,q=/^(?:mouse|pointer|contextmenu)|click/,G=/^(?:focusinfocus|focusoutblur)$/,V=/^([^.]*)(?:\.(.+)|)$/;N.Event.prototype={isDefaultPrevented:c,isPropagationStopped:c,isImmediatePropagationStopped:c,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=u,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=u,e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=u,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},R.submitBubbles||(N.event.special.submit={setup:function(){if(N.nodeName(this,"form"))return!1;N.event.add(this,"click._submit keypress._submit",function(e){var t=e.target,n=N.nodeName(t,"input")||N.nodeName(t,"button")?t.form:void 0;n&&!N._data(n,"submitBubbles")&&(N.event.add(n,"submit._submit",function(e){e._submit_bubble=!0}),N._data(n,"submitBubbles",!0))})},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&N.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){if(N.nodeName(this,"form"))return!1;N.event.remove(this,"._submit")}}),R.changeBubbles||(N.event.special.change={setup:function(){if(K.test(this.nodeName))return"checkbox"!==this.type&&"radio"!==this.type||(N.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._just_changed=!0)}),N.event.add(this,"click._change",function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),N.event.simulate("change",this,e,!0)})),!1;N.event.add(this,"beforeactivate._change",function(e){var t=e.target;K.test(t.nodeName)&&!N._data(t,"changeBubbles")&&(N.event.add(t,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||N.event.simulate("change",this.parentNode,e,!0)}),N._data(t,"changeBubbles",!0))})},handle:function(e){var t=e.target;if(this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type)return e.handleObj.handler.apply(this,arguments)},teardown:function(){return N.event.remove(this,"._change"),!K.test(this.nodeName)}}),R.focusinBubbles||N.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){N.event.simulate(t,e.target,N.event.fix(e),!0)};N.event.special[t]={setup:function(){var r=this.ownerDocument||this,o=N._data(r,t);o||r.addEventListener(e,n,!0),N._data(r,t,(o||0)+1)},teardown:function(){var r=this.ownerDocument||this,o=N._data(r,t)-1;o?N._data(r,t,o):(r.removeEventListener(e,n,!0),N._removeData(r,t))}}}),N.fn.extend({on:function(e,t,n,r,o){var i,a;if("object"==typeof e){"string"!=typeof t&&(n=n||t,t=void 0);for(i in e)this.on(i,t,n,e[i],o);return this}if(null==n&&null==r?(r=t,n=t=void 0):null==r&&("string"==typeof t?(r=n,n=void 0):(r=n,n=t,t=void 0)),!1===r)r=c;else if(!r)return this;return 1===o&&(a=r,r=function(e){return N().off(e),a.apply(this,arguments)},r.guid=a.guid||(a.guid=N.guid++)),this.each(function(){N.event.add(this,e,r,n,t)})},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,t,n){var r,o;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,N(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(o in e)this.off(o,t,e[o]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=c),this.each(function(){N.event.remove(this,e,n,t)})},trigger:function(e,t){return this.each(function(){N.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return N.event.trigger(e,t,n,!0)}}),N.fn.delay=function(e,t){return e=N.fx?N.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var r=setTimeout(t,e);n.stop=function(){clearTimeout(r)}})};var W=N.now(),X=/\?/,$=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;N.parseJSON=function(e){if(window.JSON&&window.JSON.parse)return window.JSON.parse(e+"");var t,n=null,r=N.trim(e+"");return r&&!N.trim(r.replace($,function(e,r,o,i){return t&&r&&(n=0),0===n?e:(t=o||r,n+=!i-!o,"")}))?Function("return "+r)():N.error("Invalid JSON: "+e)},N.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{window.DOMParser?(n=new DOMParser,t=n.parseFromString(e,"text/xml")):(t=new ActiveXObject("Microsoft.XMLDOM"),t.async="false",t.loadXML(e))}catch(e){t=void 0}return t&&t.documentElement&&!t.getElementsByTagName("parsererror").length||N.error("Invalid XML: "+e),t};var J,Q,Y=/#.*$/,Z=/([?&])_=[^&]*/,ee=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,te=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,ne=/^(?:GET|HEAD)$/,re=/^\/\//,oe=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,ie={},ae={},se="*/".concat("*");try{Q=location.href}catch(e){Q=P.createElement("a"),Q.href="",Q=Q.href}J=oe.exec(Q.toLowerCase())||[],N.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Q,type:"GET",isLocal:te.test(J[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":se,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":N.parseJSON,"text xml":N.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?f(f(e,N.ajaxSettings),t):f(N.ajaxSettings,e)},ajaxPrefilter:l(ie),ajaxTransport:l(ae),ajax:function(e,t){function n(e,t,n,r){var o,l,d,x,b,S=t;2!==k&&(k=2,s&&clearTimeout(s),c=void 0,a=r||"",w.readyState=e>0?4:0,o=e>=200&&e<300||304===e,n&&(x=p(f,w,n)),x=h(f,x,w,o),o?(f.ifModified&&(b=w.getResponseHeader("Last-Modified"),b&&(N.lastModified[i]=b),(b=w.getResponseHeader("etag"))&&(N.etag[i]=b)),204===e||"HEAD"===f.type?S="nocontent":304===e?S="notmodified":(S=x.state,l=x.data,d=x.error,o=!d)):(d=S,!e&&S||(S="error",e<0&&(e=0))),w.status=e,w.statusText=(t||S)+"",o?y.resolveWith(g,[l,S,w]):y.rejectWith(g,[w,S,d]),w.statusCode(C),C=void 0,u&&m.trigger(o?"ajaxSuccess":"ajaxError",[w,f,o?l:d]),v.fireWith(g,[w,S]),u&&(m.trigger("ajaxComplete",[w,f]),--N.active||N.event.trigger("ajaxStop")))}"object"==typeof e&&(t=e,e=void 0),t=t||{};var r,o,i,a,s,u,c,l,f=N.ajaxSetup({},t),g=f.context||f,m=f.context&&(g.nodeType||g.jquery)?N(g):N.event,y=N.Deferred(),v=N.Callbacks("once memory"),C=f.statusCode||{},x={},b={},k=0,S="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(2===k){if(!l)for(l={};t=ee.exec(a);)l[t[1].toLowerCase()]=t[2];t=l[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===k?a:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return k||(e=b[n]=b[n]||e,x[e]=t),this},overrideMimeType:function(e){return k||(f.mimeType=e),this},statusCode:function(e){var t;if(e)if(k<2)for(t in e)C[t]=[C[t],e[t]];else w.always(e[w.status]);return this},abort:function(e){var t=e||S;return c&&c.abort(t),n(0,t),this}};if(y.promise(w).complete=v.add,w.success=w.done,w.error=w.fail,f.url=((e||f.url||Q)+"").replace(Y,"").replace(re,J[1]+"//"),f.type=t.method||t.type||f.method||f.type,f.dataTypes=N.trim(f.dataType||"*").toLowerCase().match(L)||[""],null==f.crossDomain&&(r=oe.exec(f.url.toLowerCase()),f.crossDomain=!(!r||r[1]===J[1]&&r[2]===J[2]&&(r[3]||("http:"===r[1]?"80":"443"))===(J[3]||("http:"===J[1]?"80":"443")))),f.data&&f.processData&&"string"!=typeof f.data&&(f.data=N.param(f.data,f.traditional)),d(ie,f,t,w),2===k)return w;u=f.global,u&&0==N.active++&&N.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!ne.test(f.type),i=f.url,f.hasContent||(f.data&&(i=f.url+=(X.test(i)?"&":"?")+f.data,delete f.data),!1===f.cache&&(f.url=Z.test(i)?i.replace(Z,"$1_="+W++):i+(X.test(i)?"&":"?")+"_="+W++)),f.ifModified&&(N.lastModified[i]&&w.setRequestHeader("If-Modified-Since",N.lastModified[i]),N.etag[i]&&w.setRequestHeader("If-None-Match",N.etag[i])),(f.data&&f.hasContent&&!1!==f.contentType||t.contentType)&&w.setRequestHeader("Content-Type",f.contentType);for(o in f.headers)w.setRequestHeader(o,f.headers[o]);if(f.beforeSend&&(!1===f.beforeSend.call(g,w,f)||2===k))return w.abort();S="abort";for(o in{success:1,error:1,complete:1})w[o](f[o]);if(c=d(ae,f,t,w)){w.readyState=1,u&&m.trigger("ajaxSend",[w,f]),f.async&&f.timeout>0&&(s=setTimeout(function(){w.abort("timeout")},f.timeout));try{k=1,c.send(x,n)}catch(e){if(!(k<2))throw e;n(-1,e)}}else n(-1,"No Transport");return w},getJSON:function(e,t,n){return N.get(e,t,n,"json")},getScript:function(e,t){return N.get(e,void 0,t,"script")}}),N.each(["get","post"],function(e,t){N[t]=function(e,n,r,o){return N.isFunction(n)&&(o=o||r,r=n,n=void 0),N.ajax({url:e,type:t,dataType:o,data:n,success:r})}}),N.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){N.fn[t]=function(e){return this.on(t,e)}}),N._evalUrl=function(e){return N.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})};var ue=/%20/g,ce=/\[\]$/,le=/\r?\n/g,de=/^(?:submit|button|image|reset|file)$/i,fe=/^(?:input|select|textarea|keygen)/i;N.param=function(e,t){var n,r=[],o=function(e,t){t=N.isFunction(t)?t():null==t?"":t,r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=N.ajaxSettings&&N.ajaxSettings.traditional),N.isArray(e)||e.jquery&&!N.isPlainObject(e))N.each(e,function(){o(this.name,this.value)});else for(n in e)g(n,e[n],t,o);return r.join("&").replace(ue,"+")},N.fn.extend({serialize:function(){return N.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=N.prop(this,"elements");return e?N.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!N(this).is(":disabled")&&fe.test(this.nodeName)&&!de.test(e)&&(this.checked||!rcheckableType.test(e))}).map(function(e,t){var n=N(this).val();return null==n?null:N.isArray(n)?N.map(n,function(e){return{name:t.name,value:e.replace(le,"\r\n")}}):{name:t.name,value:n.replace(le,"\r\n")}}).get()}}),N.ajaxSettings.xhr=void 0!==window.ActiveXObject?function(){return!this.isLocal&&/^(get|post|head|put|delete|options)$/i.test(this.type)&&m()||y()}:m;var pe=0,he={},ge=N.ajaxSettings.xhr();window.ActiveXObject&&N(window).on("unload",function(){for(var e in he)he[e](void 0,!0)}),R.cors=!!ge&&"withCredentials"in ge,ge=R.ajax=!!ge,ge&&N.ajaxTransport(function(e){if(!e.crossDomain||R.cors){var t;return{send:function(n,r){var o,i=e.xhr(),a=++pe;if(i.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(o in e.xhrFields)i[o]=e.xhrFields[o];e.mimeType&&i.overrideMimeType&&i.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest");for(o in n)void 0!==n[o]&&i.setRequestHeader(o,n[o]+"");i.upload&&e.progress&&(i.upload.onprogress=e.progress),i.send(e.hasContent&&(e.body||e.data)||null),t=function(n,o){var s,u,c;if(t&&(o||4===i.readyState))if(delete he[a],t=void 0,i.onreadystatechange=N.noop,o)4!==i.readyState&&i.abort();else{c={},s=i.status,"string"==typeof i.responseText&&(c.text=i.responseText);try{u=i.statusText}catch(e){u=""}s||!e.isLocal||e.crossDomain?1223===s&&(s=204):s=c.text?200:404}c&&r(s,u,c,i.getAllResponseHeaders())},e.async?4===i.readyState?setTimeout(t):i.onreadystatechange=he[a]=t:t()},abort:function(){t&&t(void 0,!0)}}}}),N.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return N.globalEval(e),e}}}),N.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),N.ajaxTransport("script",function(e){if(e.crossDomain){var t,n=P.head||N("head")[0]||P.documentElement;return{send:function(r,o){t=P.createElement("script"),t.async=!0,e.scriptCharset&&(t.charset=e.scriptCharset),t.src=e.url,t.onload=t.onreadystatechange=function(e,n){(n||!t.readyState||/loaded|complete/.test(t.readyState))&&(t.onload=t.onreadystatechange=null,t.parentNode&&t.parentNode.removeChild(t),t=null,n||o(200,"success"))},n.insertBefore(t,n.firstChild)},abort:function(){t&&t.onload(void 0,!0)}}}});var me=[],ye=/(=)\?(?=&|$)|\?\?/;return N.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=me.pop()||N.expando+"_"+W++;return this[e]=!0,e}}),N.ajaxPrefilter("json jsonp",function(e,t,n){var r,o,i,a=!1!==e.jsonp&&(ye.test(e.url)?"url":"string"==typeof e.data&&!(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&ye.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=N.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(ye,"$1"+r):!1!==e.jsonp&&(e.url+=(X.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return i||N.error(r+" was not called"),i[0]},e.dataTypes[0]="json",o=window[r],window[r]=function(){i=arguments},n.always(function(){window[r]=o,e[r]&&(e.jsonpCallback=t.jsonpCallback,me.push(r)),i&&N.isFunction(o)&&o(i[0]),i=o=void 0}),"script"}),N.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||P;var r=rsingleTag.exec(e),o=!n&&[];return r?[t.createElement(r[1])]:(r=N.buildFragment([e],t,o),o&&o.length&&N(o).remove(),N.merge([],r.childNodes))},N}(),i=function(e,t){if(e=o.extend(!0,{headers:{},qs:{}},e),e.type=e.method,delete e.method,e.onProgress&&(e.progress=e.onProgress,delete e.onProgress),e.qs){var n=r.stringify(e.qs);n&&(e.url+=(-1===e.url.indexOf("?")?"?":"&")+n),delete e.qs}if(e.json&&(e.data=e.body,delete e.json,delete e.body,!e.headers&&(e.headers={}),e.headers["Content-Type"]="application/json"),e.body&&(e.body instanceof window.Blob||(e.data=e.body,delete e.body)),e.headers){var i=e.headers;delete e.headers,e.beforeSend=function(e){for(var t in i)i.hasOwnProperty(t)&&"content-length"!==t.toLowerCase()&&"user-agent"!==t.toLowerCase()&&"origin"!==t.toLowerCase()&&"host"!==t.toLowerCase()&&e.setRequestHeader(t,i[t])}}var a=function(e){var t={};return e.getAllResponseHeaders().trim().split("\n").forEach(function(e){if(e){var n=e.indexOf(":"),r=e.substr(0,n).trim().toLowerCase(),o=e.substr(n+1).trim();t[r]=o}}),{statusCode:e.status,statusMessage:e.statusText,headers:t}};return e.success=function(e,n,r){t(null,a(r),e)},e.error=function(e){e.responseText?t(null,a(e),e.responseText):t(e.statusText,a(e),e.responseText)},e.dataType="text",o.ajax(e)};e.exports=i},function(e,t,n){"use strict";t.decode=t.parse=n(16),t.encode=t.stringify=n(17)},function(e,t,n){"use strict";function r(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.exports=function(e,t,n,i){t=t||"&",n=n||"=";var a={};if("string"!=typeof e||0===e.length)return a;var s=/\+/g;e=e.split(t);var u=1e3;i&&"number"==typeof i.maxKeys&&(u=i.maxKeys);var c=e.length;u>0&&c>u&&(c=u);for(var l=0;l<c;++l){var d,f,p,h,g=e[l].replace(s,"%20"),m=g.indexOf(n);m>=0?(d=g.substr(0,m),f=g.substr(m+1)):(d=g,f=""),p=decodeURIComponent(d),h=decodeURIComponent(f),r(a,p)?o(a[p])?a[p].push(h):a[p]=[a[p],h]:a[p]=h}return a};var o=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},function(e,t,n){"use strict";function r(e,t){if(e.map)return e.map(t);for(var n=[],r=0;r<e.length;r++)n.push(t(e[r],r));return n}var o=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,t,n,s){return t=t||"&",n=n||"=",null===e&&(e=void 0),"object"==typeof e?r(a(e),function(a){var s=encodeURIComponent(o(a))+n;return i(e[a])?r(e[a],function(e){return s+encodeURIComponent(o(e))}).join(t):s+encodeURIComponent(o(e[a]))}).join(t):s?encodeURIComponent(o(s))+n+encodeURIComponent(o(e)):""};var i=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},a=Object.keys||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t}},function(e,t,n){function r(e,t){var n,r,o=new b,s=e.TaskId,c=e.Bucket,l=e.Region,f=e.Key,h=e.Body,g=e.ChunkSize||e.SliceSize||this.options.ChunkSize,m=e.AsyncLimit,y=e.StorageClass||"Standard",v=e.ServerSideEncryption,C=this,x=e.onHashProgress;o.on("error",function(e){if(C._isRunningTask(s))return t(e)}),o.on("upload_complete",function(e){t(null,e)}),o.on("upload_slice_complete",function(e){p.call(C,{Bucket:c,Region:l,Key:f,UploadId:e.UploadId,SliceList:e.SliceList},function(t,i){if(C._isRunningTask(s)){if(delete S[e.UploadId],t)return r(null,!0),o.emit("error",t);r({loaded:n,total:n},!0),a.call(C,e.UploadId),o.emit("upload_complete",i)}})}),o.on("get_upload_data_finish",function(t){var a=k.getFileUUID(h,e.ChunkSize);a&&i.call(C,a,t.UploadId),S[t.UploadId]=!0,s&&C.on("inner-kill-task",function(e){e.TaskId===s&&"canceled"===e.toState&&delete S[t.UploadId]}),d.call(C,{TaskId:s,Bucket:c,Region:l,Key:f,Body:h,FileSize:n,SliceSize:g,AsyncLimit:m,ServerSideEncryption:v,UploadData:t,onProgress:r},function(e,t){if(C._isRunningTask(s))return e?(r(null,!0),o.emit("error",e)):void o.emit("upload_slice_complete",t)})}),o.on("get_file_size_finish",function(){if(r=k.throttleOnProgress.call(C,n,e.onProgress),e.UploadData.UploadId)o.emit("get_upload_data_finish",e.UploadData);else{var t=k.extend({TaskId:s,Bucket:c,Region:l,Key:f,Headers:e.Headers,StorageClass:y,Body:h,FileSize:n,SliceSize:g,onHashProgress:x},e);u.call(C,t,function(t,n){if(C._isRunningTask(s)){if(t)return o.emit("error",t);e.UploadData.UploadId=n.UploadId,e.UploadData.PartList=n.PartList,o.emit("get_upload_data_finish",e.UploadData)}})}}),n=e.ContentLength,delete e.ContentLength,!e.Headers&&(e.Headers={}),k.each(e.Headers,function(t,n){"content-length"===n.toLowerCase()&&delete e.Headers[n]}),function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],r=1048576,o=0;o<t.length&&(r=1024*t[o]*1024,!(n/r<=1024));o++);e.ChunkSize=e.SliceSize=g=Math.max(g,r)}(),r=k.throttleOnProgress.call(C,n,e.onProgress),0===n?(e.Body="",C.putObject(e,function(e,o){if(e)return r(null,!0),t(e);r({loaded:n,total:n},!0),t(null,o)})):o.emit("get_file_size_finish")}function o(){var e=this.options.UploadIdCacheLimit;if(!C){if(e)try{C=JSON.parse(k.localStorage.getItem(w))||[]}catch(e){}C||(C=[])}}function i(e,t,n){o.call(this);for(var r=C.length-1;r>=0;r--)C[r][0]===e&&C[r][1]===t&&C.splice(r,1);C.unshift([e,t]);var i=this.options.UploadIdCacheLimit;C.length>i&&C.splice(i),i&&setTimeout(function(){try{k.localStorage.setItem(w,JSON.stringify(C))}catch(e){}})}function a(e){o.call(this),delete S[e];for(var t=C.length-1;t>=0;t--)C[t][1]===e&&C.splice(t,1);var n=this.options.UploadIdCacheLimit;C.length>n&&C.splice(n),n&&setTimeout(function(){try{C.length?k.localStorage.setItem(w,JSON.stringify(C)):k.localStorage.removeItem(w)}catch(e){}})}function s(e){o.call(this);for(var t=[],n=0;n<C.length;n++)C[n][0]===e&&t.push(C[n][1]);return t.length?t:null}function u(e,t){var n=e.TaskId,r=e.Bucket,o=e.Region,i=e.Key,u=e.StorageClass,d=this,f={},p=e.FileSize,h=e.SliceSize,g=Math.ceil(p/h),m=0,y=0,v=k.throttleOnProgress.call(d,p,e.onHashProgress),C=function(t,n){var r=h*(t-1),o=Math.min(r+h,p),i=o-r;if(f[t])n(null,{PartNumber:t,ETag:f[t],Size:i});else{var a=k.fileSlice(e.Body,r,o);k.getFileMd5(a,function(e,r){if(e)return n(e);var o='"'+r+'"';f[t]=o,m+=1,y+=i,n(e,{PartNumber:t,ETag:o,Size:i}),v({loaded:y,total:p})})}},w=function(e,t){var n=e.length;if(0===n)return t(null,!0);if(n>g)return t(null,!1);if(n>1){if(Math.max(e[0].Size,e[1].Size)!==h)return t(null,!1)}var r=function(o){if(o<n){var i=e[o];C(i.PartNumber,function(e,n){n&&n.ETag===i.ETag&&n.Size===i.Size?r(o+1):t(null,!1)})}else t(null,!0)};r(0)},T=new b;T.on("error",function(e){if(d._isRunningTask(n))return t(e)}),T.on("upload_id_ready",function(e){var n={},r=[];k.each(e.PartList,function(e){n[e.PartNumber]=e});for(var o=1;o<=g;o++){var i=n[o];i?(i.PartNumber=o,i.Uploaded=!0):i={PartNumber:o,ETag:null,Uploaded:!1},r.push(i)}e.PartList=r,t(null,e)}),T.on("no_available_upload_id",function(){if(d._isRunningTask(n)){var a=k.extend({Bucket:r,Region:o,Key:i,Headers:e.Headers,StorageClass:u},e);d.multipartInit(a,function(e,r){if(d._isRunningTask(n)){if(e)return T.emit("error",e);var o=r.UploadId;if(!o)return t({Message:"no upload id"});T.emit("upload_id_ready",{UploadId:o,PartList:[]})}})}}),T.on("has_upload_id",function(e){e=e.reverse(),x.eachLimit(e,1,function(e,t){if(d._isRunningTask(n))return S[e]?void t():void l.call(d,{Bucket:r,Region:o,Key:i,UploadId:e},function(r,o){if(d._isRunningTask(n)){if(r)return a.call(d,e),T.emit("error",r);var i=o.PartList;i.forEach(function(e){e.PartNumber*=1,e.Size*=1,e.ETag=e.ETag||""}),w(i,function(r,o){if(d._isRunningTask(n))return r?T.emit("error",r):void(o?t({UploadId:e,PartList:i}):t())})}})},function(e){d._isRunningTask(n)&&(v(null,!0),e&&e.UploadId?T.emit("upload_id_ready",e):T.emit("no_available_upload_id"))})}),T.on("seek_local_avail_upload_id",function(t){var u,c=k.getFileUUID(e.Body,e.ChunkSize);if(c&&(u=s.call(d,c))){var f=function(e){if(e>=u.length)return void T.emit("has_upload_id",t);var s=u[e];return k.isInArray(t,s)?S[s]?void f(e+1):void l.call(d,{Bucket:r,Region:o,Key:i,UploadId:s},function(t,r){d._isRunningTask(n)&&(t?(a.call(d,s),f(e+1)):T.emit("upload_id_ready",{UploadId:s,PartList:r.PartList}))}):(a.call(d,s),void f(e+1))};f(0)}else T.emit("has_upload_id",t)}),T.on("get_remote_upload_id_list",function(t){c.call(d,{Bucket:r,Region:o,Key:i},function(t,r){if(d._isRunningTask(n)){if(t)return T.emit("error",t);var o=k.filter(r.UploadList,function(e){return e.Key===i&&(!u||e.StorageClass.toUpperCase()===u.toUpperCase())}).reverse().map(function(e){return e.UploadId||e.UploadID});if(o.length)T.emit("seek_local_avail_upload_id",o);else{var c,l=k.getFileUUID(e.Body,e.ChunkSize);l&&(c=s.call(d,l))&&k.each(c,function(e){a.call(d,e)}),T.emit("no_available_upload_id")}}})}),T.emit("get_remote_upload_id_list")}function c(e,t){var n=this,r=[],o={Bucket:e.Bucket,Region:e.Region,Prefix:e.Key},i=function(){n.multipartList(o,function(e,n){if(e)return t(e);r.push.apply(r,n.Upload||[]),"true"==n.IsTruncated?(o.KeyMarker=n.NextKeyMarker,o.UploadIdMarker=n.NextUploadIdMarker,i()):t(null,{UploadList:r})})};i()}function l(e,t){var n=this,r=[],o={Bucket:e.Bucket,Region:e.Region,Key:e.Key,UploadId:e.UploadId},i=function(){n.multipartListPart(o,function(e,n){if(e)return t(e);r.push.apply(r,n.Part||[]),"true"==n.IsTruncated?(o.PartNumberMarker=n.NextPartNumberMarker,i()):t(null,{PartList:r})})};i()}function d(e,t){var n=this,r=e.TaskId,o=e.Bucket,i=e.Region,a=e.Key,s=e.UploadData,u=e.FileSize,c=e.SliceSize,l=Math.min(e.AsyncLimit||n.options.ChunkParallelLimit||1,256),d=e.Body,p=Math.ceil(u/c),h=0,g=e.ServerSideEncryption,m=k.filter(s.PartList,function(e){return e.Uploaded&&(h+=e.PartNumber>=p?u%c||c:c),!e.Uploaded}),y=e.onProgress;x.eachLimit(m,l,function(e,t){if(n._isRunningTask(r)){var l=e.PartNumber,p=Math.min(u,e.PartNumber*c)-(e.PartNumber-1)*c,m=0;f.call(n,{TaskId:r,Bucket:o,Region:i,Key:a,SliceSize:c,FileSize:u,PartNumber:l,ServerSideEncryption:g,Body:d,UploadData:s,onProgress:function(e){h+=e.loaded-m,m=e.loaded,y({loaded:h,total:u})}},function(o,i){n._isRunningTask(r)&&(!k.isBrowser||o||i.ETag||(o='get ETag error, please add "ETag" to CORS ExposeHeader setting.'),o?h-=m:(h+=p-m,e.ETag=i.ETag),t(o||null,i))})}},function(e){if(n._isRunningTask(r))return e?t(e):void t(null,{UploadId:s.UploadId,SliceList:s.PartList})})}function f(e,t){var n=this,r=e.TaskId,o=e.Bucket,i=e.Region,a=e.Key,s=e.FileSize,u=e.Body,c=1*e.PartNumber,l=e.SliceSize,d=e.ServerSideEncryption,f=e.UploadData,p=n.options.ChunkRetryTimes+1,h=l*(c-1),g=l,m=h+l;m>s&&(m=s,g=m-h);var y=f.PartList[c-1];x.retry(p,function(t){if(n._isRunningTask(r)){var s=k.fileSlice(u,h,m);n.multipartUpload({TaskId:r,Bucket:o,Region:i,Key:a,ContentLength:g,PartNumber:c,UploadId:f.UploadId,ServerSideEncryption:d,Body:s,onProgress:e.onProgress},function(e,o){if(n._isRunningTask(r))return e?t(e):(y.Uploaded=!0,t(null,o))})}},function(e,o){if(n._isRunningTask(r))return t(e,o)})}function p(e,t){var n=e.Bucket,r=e.Region,o=e.Key,i=e.UploadId,a=e.SliceList,s=this,u=this.options.ChunkRetryTimes+1,c=a.map(function(e){return{PartNumber:e.PartNumber,ETag:e.ETag}});x.retry(u,function(e){s.multipartComplete({Bucket:n,Region:r,Key:o,UploadId:i,Parts:c},e)},function(e,n){t(e,n)})}function h(e,t){var n=e.Bucket,r=e.Region,o=e.Key,i=e.UploadId,a=e.Level||"task",s=e.AsyncLimit,u=this,l=new b;if(l.on("error",function(e){return t(e)}),l.on("get_abort_array",function(i){g.call(u,{Bucket:n,Region:r,Key:o,Headers:e.Headers,AsyncLimit:s,AbortArray:i},function(e,n){if(e)return t(e);t(null,n)})}),"bucket"===a)c.call(u,{Bucket:n,Region:r},function(e,n){if(e)return t(e);l.emit("get_abort_array",n.UploadList||[])});else if("file"===a){if(!o)return t({error:"abort_upload_task_no_key"});c.call(u,{Bucket:n,Region:r,Key:o},function(e,n){if(e)return t(e);l.emit("get_abort_array",n.UploadList||[])})}else{if("task"!==a)return t({error:"abort_unknown_level"});if(!i)return t({error:"abort_upload_task_no_id"});if(!o)return t({error:"abort_upload_task_no_key"});l.emit("get_abort_array",[{Key:o,UploadId:i}])}}function g(e,t){var n=e.Bucket,r=e.Region,o=e.Key,i=e.AbortArray,a=e.AsyncLimit||1,s=this,u=0,c=new Array(i.length);x.eachLimit(i,a,function(t,i){var a=u;if(o&&o!=t.Key)return i(null,{KeyNotMatch:!0});var l=t.UploadId||t.UploadID;s.multipartAbort({Bucket:n,Region:r,Key:t.Key,Headers:e.Headers,UploadId:l},function(e,o){var s={Bucket:n,Region:r,Key:t.Key,UploadId:l};c[a]={error:e,task:s},i(null)}),u++},function(e){if(e)return t(e);for(var n=[],r=[],o=0,i=c.length;o<i;o++){var a=c[o];a.task&&(a.error?r.push(a.task):n.push(a.task))}return t(null,{successList:n,errorList:r})})}function m(e,t){var n=this,r=void 0===e.SliceSize?n.options.SliceSize:e.SliceSize,o=0,i=0,a=k.throttleOnProgress.call(n,i,e.onProgress),s=e.files.length,u=e.onFileFinish,c=Array(s),l=function(e,n,r){a(null,!0),u&&u(e,n,r),c[r.Index]={options:r,error:e,data:n},--s<=0&&t&&t(null,{files:c})},d=[];k.each(e.files,function(e,t){!function(){var n=e.Body,s=n.size||n.length||0,u={Index:t,TaskId:""};o+=s,k.each(e,function(e,t){"object"!=typeof e&&"function"!=typeof e&&(u[t]=e)});var c=e.TaskReady,f=function(e){u.TaskId=e,c&&c(e)};e.TaskReady=f;var p=0,h=e.onProgress,g=function(e){i=i-p+e.loaded,p=e.loaded,h&&h(e),a({loaded:i,total:o})};e.onProgress=g;var m=e.onFileFinish,y=function(e,t){m&&m(e,t),l&&l(e,t,u)},v=s>=r?"sliceUploadFile":"putObject";d.push({api:v,params:e,callback:y})}()}),n._addTasks(d)}function y(e,t){var n=new b,r=this,o=e.Bucket,i=e.Region,a=e.Key,s=e.CopySource,u=s.match(/^([^.]+-\d+)\.cos\.([^.]+)\.[^\/]+\/(.+)$/);if(!u)return void t({error:"CopySource format error"});var c=u[1],l=u[2],d=decodeURIComponent(u[3]),f=void 0===e.SliceSize?r.options.CopySliceSize:e.SliceSize;f=Math.max(0,Math.min(f,5368709120));var p,h,g=e.ChunkSize||this.options.ChunkSize,m=this.options.CopyChunkParallelLimit,y=0;n.on("copy_slice_complete",function(e){r.multipartComplete({Bucket:o,Region:i,Key:a,UploadId:e.UploadId,Parts:e.PartList},function(e,n){if(e)return h(null,!0),t(e);h({loaded:p,total:p},!0),t(null,n)})}),n.on("get_copy_data_finish",function(e){x.eachLimit(e.PartList,m,function(n,u){var c=n.PartNumber,l=n.CopySourceRange,d=n.end-n.start,f=0;v.call(r,{Bucket:o,Region:i,Key:a,CopySource:s,UploadId:e.UploadId,PartNumber:c,CopySourceRange:l,onProgress:function(e){y+=e.loaded-f,f=e.loaded,h({loaded:y,total:p})}},function(e,r){if(e)return t(e);h({loaded:y,total:p}),y+=d-f,n.ETag=r.ETag,u(e||null,r)})},function(r){if(r)return h(null,!0),t(r);n.emit("copy_slice_complete",e)})}),n.on("get_file_size_finish",function(){!function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],n=1048576,r=0;r<t.length&&(n=1024*t[r]*1024,!(p/n<=1024));r++);e.ChunkSize=g=Math.max(g,n);for(var o=Math.ceil(p/g),i=[],a=1;a<=o;a++){var s=(a-1)*g,u=a*g<p?a*g-1:p-1,c={PartNumber:a,start:s,end:u,CopySourceRange:"bytes="+s+"-"+u};i.push(c)}e.PartList=i}(),r.multipartInit({Bucket:o,Region:i,Key:a},function(r,o){if(r)return t(r);e.UploadId=o.UploadId,n.emit("get_copy_data_finish",e)})}),r.headObject({Bucket:c,Region:l,Key:d},function(o,i){return o?o.statusCode&&404===o.statusCode?t({ErrorStatus:d+" Not Exist"}):void t(o):void 0!==(p=e.FileSize=i.headers["content-length"])&&p?(h=k.throttleOnProgress.call(r,p,e.onProgress),void(p<=f?r.putObjectCopy(e,function(e,n){if(e)return h(null,!0),t(e);h({loaded:p,total:p},!0),t(e,n)}):n.emit("get_file_size_finish"))):void t({error:'get Content-Length error, please add "Content-Length" to CORS ExposeHeader setting.'})})}function v(e,t){var n=e.TaskId,r=e.Bucket,o=e.Region,i=e.Key,a=e.CopySource,s=e.UploadId,u=1*e.PartNumber,c=e.CopySourceRange,l=this.options.ChunkRetryTimes+1,d=this;x.retry(l,function(t){d.uploadPartCopy({TaskId:n,Bucket:r,Region:o,Key:i,CopySource:a,UploadId:s,PartNumber:u,CopySourceRange:c,onProgress:e.onProgress},function(e,n){t(e||null,n)})},function(e,n){return t(e,n)})}var C,x=n(19),b=n(2).EventProxy,k=n(0),S={},w="cos_sdk_upload_cache",T={sliceUploadFile:r,abortUploadTask:h,uploadFiles:m,sliceCopyFile:y};k.each(T,function(e,n){t[n]=k.apiWrapper(n,e)})},function(e,t){var n=function(e,t,n,r){if(r=r||function(){},!e.length||t<=0)return r();var o=0,i=0,a=0;!function s(){if(o>=e.length)return r();for(;a<t&&i<e.length;)i+=1,a+=1,n(e[i-1],function(t){t?(r(t),r=function(){}):(o+=1,a-=1,o>=e.length?r():s())})}()},r=function(e,t,n){var r=function(o){t(function(t,i){t&&o<e?r(o+1):n(t,i)})};e<1?n():r(1)},o={eachLimit:n,retry:r};e.exports=o}])});