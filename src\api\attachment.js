import fetch from '@/utils/request'

/**
 * 合同上传
 * @param {*} data
 * @returns
 */
export function pageMenu(data) {
    return fetch({
        url: '/cwe/a/attachment/upload',
        method: 'post',
        data
    })
}

/**
 * 合同删除
 * @param {*} id 附件Id
 * @returns
 */
export function attachmentDelete(id) {
    return fetch({
        url: '/cwe/a/attachment/delete',
        method: 'post',
        data: { id }
    })
}


// 删除付款申请的附件

export function applyPayDelete(id) {
    return fetch({
        url: '/cwe/a/applyPay/delete',
        method: 'post',
        data: { id }
    })
}

// 车牌列表
export async function listAnalysisNumberplate(format = true) {
    const resp = await fetch({
        //   url: '/quality/analysis/numberplate/list',
        url: '/cwe/a/applyPay/delete',
        method: 'get'
    })
    if (format) {
        resp.data = resp.data.map(val => {
            return { name: val, id: val }
        })
    }
    return resp
}
