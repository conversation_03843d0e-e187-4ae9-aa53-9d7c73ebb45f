import fetch from '@/utils/request'

// 角色列表接口
export function pageMenu () {
    return fetch({
        url: '/cwe/a/menu/findAllTreeMenus',
        method: 'get',
        params: {}
    })
}

export function saveRole (object) {
    return fetch({
        url: '/cwe/a/role/saveWithMultiResources',
        method: 'post',
        data: {
            ContentType: 'json',
            ...object
        }
    })
}

export function getById (id) {
    return fetch({
        url: '/cwe/a/role/getById',
        method: 'get',
        params: { id: id }
    })
}

export function addRoleUser ({ roleCode, userCodeList }) {
    return fetch({
        url: '/cwe/a/user/addRoleUser',
        method: 'post',
        data: { roleCode, userCodeList }
    })
}

export function delRoleUser ({ roleCode, userCodeList }) {
    return fetch({
        url: '/cwe/a/user/delRoleUser',
        method: 'post',
        data: { roleCode, userCodeList }
    })
}

export function findByRoleCode (searchForm) {
    return fetch({
        url: '/cwe/a/user/findByRoleCode',
        method: 'get',
        params: searchForm
    })
}

export function findNotInRoleCode (searchForm) {
    return fetch({
        url: '/cwe/a/user/findNotInRoleCode',
        method: 'get',
        params: searchForm
    })
}

// 字典列表接口
export function saveDict (dict) {
    return fetch({
        url: '/cwe/a/dict/save',
        method: 'post',
        data: {
            ...dict,
            sort: 50
        }
    })
}

// 资源管理接口

export function deleteMenuById (id) {
    return fetch({
        url: '/cwe/a/menu/delete',
        method: 'post',
        data: {
            id
        }
    })
}

export function saveMenu (object) {
    return fetch({
        url: '/cwe/a/menu/save',
        method: 'post',
        data: object
    })
}

export function deleteById (id) {
    return fetch({
        url: '/cwe/a/resource/delete',
        method: 'post',
        data: { id: id }
    })
}

export function saveResource (object) {
    return fetch({
        url: '/cwe/a/resource/save',
        method: 'post',
        data: object
    })
}

export function findByKeyword (keyword) {
    return fetch({
        url: '/cwe/a/menu/findByKeyword',
        method: 'get',
        params: {
            keyword
        }
    })
}
