import request from "@/utils/request";
import fetch from "@/utils/request";
import { getDate } from "@/utils/dateUtils";

export function listDemo(params) {
  return request({
    url: "/team/list",
    method: "get",
    params,
  });
}

/*
 * 获取会员列表
 * */
export function listFactory(params) {
  return request({
    url: "/a/factory/findByKeyword",
    method: "get",
    params: params,
  });
}

export function customerList(params) {
  return request({
    url: "/a/customer/list",
    method: "get",
    params: {
      ...params,
      orderBy: "createDate",
      orderDir: "desc",
    },
  });
}

export function getlistCoalName(params) {
  return request({
    url: "/a/coalSourceInside/list-coalName",
    method: "get",
    params: {
      ...params,
      orderBy: "createDate",
      orderDir: "desc",
    },
  });
}

/*
 * 获取会员列表
 * */
export function listCoalName(params) {
  return request({
    url: "/a/coalName/list",
    method: "get",
    params: params,
  });
}

export function listCategoryNames(params) {
  return fetch({
    url: "/cwe/a/coalDatabase/data/categoryNames",
    method: "get",
    params: params,
  });
}

export async function listCoalWashingTargetValueConfig(query) {
  const resp = await request({
    url: "/cwe/a/coalWashingTargetValueConfig/listConfig",
    method: "get",
    params: query,
  });
  // 日期倒叙
  resp.data.sort((a, b) => new Date(b.date) * 1 - new Date(a.date) * 1);
  resp.data.forEach((v) => {
    v.date = getDate(v.date, "YYYY-MM-DD");
  });
  return resp;
}

export function listRole(params) {
  return fetch({
    url: "/cwe/a/role/list",
    method: "get",
    params: params,
  });
}

export function listFactoryName(params) {
  return fetch({
    url: "/cwe/a/factory/page",
    method: "get",
    params: params,
  });
}

export function listProductName(params) {
  return fetch({
    url: "/cwe/a/product/list",
    method: "get",
    params: params,
  });
}
export function listSupplierName(params) {
  return fetch({
    url: "/cwe/a/supplier/list",
    method: "get",
    params: params,
  });
}
export function listCustomerName(params) {
  return fetch({
    url: "/cwe/a/customer/list",
    method: "get",
    params: params,
  });
}
