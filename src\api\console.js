import fetch from '@/utils/request'

export function supplierBuySum(date) {
    return fetch({
        url: `/cwe/a/weightHouseIn/findSupplierBuySum`,
        method: 'get',
        params: { date }
    })
}

export function customerSellSum(date) {
    return fetch({
        url: `/cwe/a/weightHouseOut/findCustomerSellSum`,
        method: 'get',
        params: { date }
    })
}

export function productionUsedData(date) {
    return fetch({
        url: `/cwe/a/blendingProduction/getUsedData`,
        method: 'get',
        params: { date }
    })
}

export function get7DayOutput(beginDate, endDate) {
    return fetch({
        url: `/cwe/a/blendingProduction/get7DayOutput`,
        method: 'get',
        params: { beginDate, endDate }
    })
}

export function getPayRecvHomeSum(date) {
    return fetch({
        url: `/cwe/a/applyPay/getPayRecvHomeSum`,
        method: 'get',
        params: { date }
    })
}


