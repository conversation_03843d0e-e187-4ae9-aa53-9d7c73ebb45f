import fetch from '@/utils/request'

export function info() {
  return fetch({
    method: 'get',
    data: {
      name: 'admin.dashboard'
    }
  })
}

export function tdHomeInfo(data) {
  return fetch({
    url: `/cwe/a/home/<USER>
    method: 'get',
    params: {...data}
  })
}

//获取待审核事项的列表数据
export function getlistAllReviews(data) {
  return fetch({
    url: `/cwe/a/reviewAll/listAllReviews`,
    method: 'get',
    params: {...data}
  })
}

//获取库存煤成本的列表数据
export function getpagelist(params = {}) {
  return fetch({
    url: `/cwe/a/stock/page`,
    method: 'get',
    params: params
  })
}

// async page(params = {}) {
//     const res = await fetch({
//         url: `${name}/page`,
//         method: 'get',
//         params: params
//     })
//     const typeName = {
//         YM: '原煤',
//         BCPJM: '半成品精煤',
//         CPJM: '成品精煤',
//         ZM: '中煤',
//         GS: '矸石'
//     }
//     res.data.records.forEach(item => {
//         item.date = today
//         item.typeName = typeName[item.type]
//         item.editStock = ''
//         item._stock = 0 // 传入服务端的库存
//         item.stockBak = item.stock * 1
//     })
//     return res
// }

export function getmixCoalYearMonthDaySum(date) {//首页---配煤汇总接口
  return fetch({
    url: `/cwe/a/mixCoal/getYearMonthDaySum`,
    method: 'get',
    params: {date}
  })
}


export function getwashCoalYearMonthDaySum(date) {//首页--洗煤生产数据接口
  return fetch({
    url: `/cwe/a/washCoal/getYearMonthDaySum`,
    method: 'get',
    params: {date}
  })
}


export function weightHouseIn(data) {
  return fetch({
    url: `/cwe/a/weightHouseIn/page`,
    method: 'get',
    params: {...data}
  })
}

const pageSize = {size: 4, current: 1}

export function weightHouseOut(data) {
  return fetch({
    url: `/cwe/a/weightHouseOut/page`,
    method: 'get',
    params: {...data, ...pageSize}
  })
}

export function buyIn(data) {
  return fetch({
    url: `/cwe/a/buyIn/page`,
    method: 'get',
    params: {...data, ...pageSize}
  })
}

export function sellOut(data) {
  return fetch({
    url: `/cwe/a/sellOut/page`,
    method: 'get',
    params: {...data, ...pageSize}
  })
}

export function semiFinishedCoalStock(data) {
  return fetch({
    url: `/cwe/a/stockDay/page`,
    method: 'get',
    params: {...data, filter_EQS_type: 'BCPJM', ...pageSize}
  })
}

export function finishedCoalStock(data) {
  return fetch({
    url: `/cwe/a/stockDay/page`,
    method: 'get',
    params: {...data, filter_EQS_type: 'CPJM', ...pageSize}
  })
}

// 小样返回最近加上的三条数据
export function getRecentList(params = {}) {
  return fetch({
    url: `/cwe/a/coalSample/getRecentList`,
    method: 'get',
    params
  })
}

export function getQualPileWarn(params = {}) {
  return fetch({
    url: `/cwe/a/qualPile/getWarn`,
    method: 'get',
    params
  })
}

export function getQualBuyInWarn(params = {}) {
  return fetch({
    url: `/cwe/a/qualBuyIn/getWarn`,
    method: 'get',
    params
  })
}

export function getQualSellOutWarn(params = {}) {
  return fetch({
    url: `/cwe/a/qualSellOut/getWarn`,
    method: 'get',
    params
  })
}

export function getCustomerFeedbackWarn(params = {}) {
  return fetch({
    url: `/cwe/a/customerFeedback/getWarn`,
    method: 'get',
    params
  })
}
