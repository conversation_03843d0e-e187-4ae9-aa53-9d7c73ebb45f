import fetch from '@/utils/request'

const urlPrefix = '/cwe/a/home'

/**
 * 指标
 * @param date
 */
export function orderCount (date) {
    return fetch({
        url: urlPrefix + '/getHomeSummary',
        method: 'get',
        params: {
            date
        }
    })
}

export function orderPie () {
    return fetch({
        url: urlPrefix + '/consumerTrend',
        method: 'get',
        params: {}
    })
}

export function inOrderStat ({ dateBegin, dateEnd }) {
    return fetch({
        url: urlPrefix + '/inOrderStat',
        method: 'get',
        params: { dateBegin, dateEnd }
    })
}

export function entryOrderStat ({ dateBegin, dateEnd }) {
    return fetch({
        url: urlPrefix + '/entryOrderStat',
        method: 'get',
        params: { dateBegin, dateEnd }
    })
}

export function deliveryOrderStat ({ dateBegin, dateEnd }) {
    return fetch({
        url: urlPrefix + '/deliveryOrderStat',
        method: 'get',
        params: { dateBegin, dateEnd }
    })
}

export function orderStatAll ({ dateBegin, dateEnd }) {
    return fetch({
        url: urlPrefix + '/assayApplyTrend',
        method: 'get',
        params: {
            beginDate: dateBegin,
            endDate: dateEnd
        }
    })
}
