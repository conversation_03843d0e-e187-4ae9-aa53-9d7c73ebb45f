import fetch from '@/utils/request'

export async function loginByUsername (username, password) {
    const data = {
        tenantCode: 'sn',
        loginName: username,
        password
    }
    return fetch({
        url: 'cwe/n/user/login',
        method: 'get',
        params: {
            ...data
        }
    })
}

export function getUserInfo (token) {
    return fetch({
        url: '/cwe/a/user/getUserInfo',
        method: 'get',
        params: { token }
    })
}
