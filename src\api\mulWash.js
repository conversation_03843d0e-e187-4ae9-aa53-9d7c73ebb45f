import fetch from '@/utils/request'

/**
 * 列表数据
 * @param filter
 */

export function pageList(filter) {
  return fetch({
    url: '/cwe/a/coalWashingProject/pageCoalProject',
    method: 'get',
    params: filter
  })
}

export function getDetail(filter) {
  return fetch({
    url: '/cwe/a/coalWashingProject/get',
    method: 'get',
    params: filter
  })
}

export function saveProject(filter) {
  return fetch({
    url: '/cwe/a/userWorkspaceCoalRockResult/updateActualInfo',
    method: 'post',
    data: filter
  })
}

export function saveProjectApi(filter) {
  return fetch({
    url: '/cwe/a/coalWashingProject/save',
    method: 'post',
    data: filter
  })
}

export function deleteItem(filter) {
  return fetch({
    url: '/cwe/a/coalWashingProject/deleteById',
    method: 'post',
    data: filter
  })
}

export function sendEmail(filter) {
  return fetch({
    url: '/cwe/a/coalWashingProject/sendEmail',
    method: 'post',
    data: filter
  })
}

export function saveCoalByCoalWashingProject(filter) {
  return fetch({
    url: '/cwe/a/coalWashingProject/saveCoalByCoalWashingProject',
    method: 'post',
    data: filter
  })
}
