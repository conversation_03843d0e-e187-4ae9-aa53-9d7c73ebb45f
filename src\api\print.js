import fetch from '@/utils/request'

export function getPrint (data) {
    return fetch({
        url: '/cwe/a/printTemplate/page',
        method: 'get',
        params: { ...data }
    })
}

export function setData (data) {
    return fetch({
        url: '/cwe/a/printTemplate/save',
        method: 'post',
        data: { ...data }
    })
}

export function dowloadPrint1 (data) {
    return fetch({
        url: '/cwe/a/printTemplate/getSysCommonAssayTemplate',
        method: 'get',
        params: data
    })
}

export function dowloadPrint2 (data) {
    return fetch({
        url: '/cwe/a/printTemplate/getSysCommonAssayContractTemplate',
        method: 'get',
        params: data
    })
}
