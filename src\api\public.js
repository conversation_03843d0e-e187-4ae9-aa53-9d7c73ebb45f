import fetch from '@/utils/request'

export function findNewsPage(searchForm) {
    return fetch({
        url: '/cwe/a/news/findPage',
        method: 'get',
        params: searchForm
    })
}

export function findPage(searchForm) {
    return fetch({
        url: '/cwe/a/scheduleNode/page',
        method: 'get',
        params: searchForm
    })
}

export function uploadFile(searchForm) {
    return fetch({
        url: '/cwe/a/attachment/upload',
        method: 'post',
        data: {...searchForm}
    })
}

export function coalType(searchForm) {
    return fetch({
        url: '/cwe/a/dataType/list',
        method: 'get',
        params: {...searchForm}
    })
}


// 物资列表
export async function materialList(keyword = null) {
    try {
        let result = await fetch({
            url: 'wms/a/item/findByKeyword',
            method: 'get',
            params: {keyword}
        })
        const records = result.data.map(({id, name, code, coalCategory}) => ({id, name, code, coalCategory}))
        return records
    } catch (error) {
        return []
    }
}

// 物资分类列表
export async function categoryNameList(keyword = null) {
    try {
        let result = await fetch({
            url: 'wms/a/category/findByKeyword',
            method: 'get',
            params: {keyword}
        })
        const records = result.data.map(({id, name, code, coalCategory}) => ({id, name, code, coalCategory}))
        return records
    } catch (error) {
        return []
    }
}

/**
 * 品名下拉列表API
 * @returns {id:品名ID,name:品名,code:品名编码,coalCategory:煤种}
 */
export async function productList(keyword = null) {
    try {
        let result = await fetch({
            url: 'cwe/a/product/findByKeyword',
            method: 'get',
            params: {keyword}
        })
        const records = result.data.map(({id, name, code, coalCategory}) => ({id, name, code, coalCategory}))
        return records
    } catch (error) {
        return []
    }
}

/**
 * 配煤品名下拉列表API
 * @returns {id:品名ID,name:品名,code:品名编码,coalCategory:煤种}
 */
export async function coalproductList(keyword = null) {
    try {
        let result = await fetch({
            url: 'cwe/a/mixCoal/findProductByKeyword',
            method: 'get',
            params: {keyword}
        })
        const records = result.data.map(({id, name, code, coalCategory}) => ({id, name, code, coalCategory}))
        return records
    } catch (error) {
        return []
    }
}

/**
 * 洗煤品名下拉列表API
 * @returns {id:品名ID,name:品名,code:品名编码,coalCategory:煤种}
 */
export async function washproductList(keyword = null) {
    try {
        let result = await fetch({
            url: 'cwe/a/washMixCoal/findProductByKeyword',
            method: 'get',
            params: {keyword}
        })
        const records = result.data.map(({id, name, code, coalCategory}) => ({id, name, code, coalCategory}))
        return records
    } catch (error) {
        return []
    }
}


/**
 * 发货单位下拉列表API
 * @return {id:品名ID,name:品名,code:品名编码,coalCategory:煤种}
 */
export async function supplierList(params) {
    try {
        let result = await fetch({
            url: 'cwe/a/supplier/page',
            method: 'get',
            params: {size: 999, ...params}
        })
        const records = result.data.records.map(item => {
            const {name, id} = item
            return {name, id}
        })
        return records
    } catch (error) {
        return []
    }
}


/**
 * 入库明细 送货单位下拉列表API
 * @return {id:品名ID,name:品名,code:品名编码,coalCategory:煤种}
 */
export async function supplierListV(params) {
    try {
        let result = await fetch({
            url: '/wms/a/inOrderItem/findSupplierNameByKey',
            method: 'get',
            params: {}
        })
        const records = result.data.map(item => {
            const name = item
            return {name: name}
        })
        return records
    } catch (error) {
        return []
    }
}

//获取商品列表
export async function gettype(params) {
    try {
        let result = await fetch({
            url: `/wms/a/item/page`,
            method: 'get',
            params: {}
        })
        const records = result.data.records.map(item => {
            const {name, id} = item
            return {name, id}
        })
        return records
    } catch (error) {
        return []
    }
}

//获取领用部门
export async function findUsedDeptByKey(params) {
    try {
        let result = await fetch({
            url: `/wms/a/outOrderItem/findUsedDeptByKey`,
            method: 'get',
            params: {}
        })
        console.log(result)
        const records = result.data.map(item => {
            const name = item
            return {name: name}
        })
        return records
    } catch (error) {
        return []
    }
}

//获取领用人
export async function findUsedManByKey(params) {
    try {
        let result = await fetch({
            url: `/wms/a/outOrderItem/findUsedManByKey`,
            method: 'get',
            params: {}
        })
        console.log(result)
        const records = result.data.map(item => {
            const name = item
            return {name: name}
        })
        return records
    } catch (error) {
        return []
    }
}


//运费明细 所有公司列表
export async function getAllCompanyList(params) {
    try {
        let result = await fetch({
            url: '/cwe/a/weightHouseLog/findByKeyword',
            method: 'get',
            params: {size: 999, ...params}
        })
        // console.log(result)
        const records = result.data.map(item => {
            const {name, id} = item
            return {name, id}
        })
        return records
    } catch (error) {
        return []
    }
}

//运费明细 用公司名称取获取亏吨的数据列表
export async function getfindLoseNull(params) {
    try {
        let result = await fetch({
            url: '/cwe/a/weightHouseLog/findLoseNull',
            method: 'get',
            params
        })
        // const records = result.data.map(item => {
        //     console.log(item)
        //     console.log(item.lossNull)
        //     let jj = item.lossNull + '车数：' + item.truckCount
        //     console.log(jj)
        //     const { lossNull } = item
        //     console.log(lossNull)
        //     return { lossNull }
        // })
        const records = result.data.map(({lossNull, truckCount}) => ({lossNull, truckCount}))
        return records
    } catch (error) {
        return []
    }
}


/**
 * 收货单位下拉列表API
 * @returns {id:品名ID,name:品名,code:品名编码,coalCategory:煤种}
 */
export async function customerList(params) {
    try {
        let result = await fetch({
            url: 'cwe/a/customer/page',
            method: 'get',
            params: {size: 999, ...params}
        })
        const records = result.data.records.map(item => {
            const {name, id} = item
            return {name, id}
        })
        return records
    } catch (error) {
        return []
    }
}

/**
 * 获取供应商合同
 * @param {*} params
 * @returns
 */
export function supplierContract(params = {}) {
    return fetch({
        url: '/cwe/a/contractBuy/getSupplierContract',
        method: 'get',
        params: params
    })
}

/**
 * 获取客户合同
 * @param {*} params
 * @returns
 */
export function customerContract(params = {}) {
    return fetch({
        url: '/cwe/a/contractSell/getCustomerContract',
        method: 'get',
        params: params
    })
}
