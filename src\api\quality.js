import fetch from '@/utils/request'

export function chooseContract(params = {}) {
    return fetch({
        url: '/cwe/a/contractBuy/getSupplierContract',
        method: 'get',
        params: params
    })
}
//财务管理-结算--付款结算获取合同列表
export function chooseContractNotSettle(params = {}) {
    return fetch({
        url: '/cwe/a/contractBuy/getSupplierContractNotSettle',
        method: 'get',
        params: params
    })
}
//财务管理-结算--收款结算获取合同列表
export function getCustomerContractNotSettle(params = {}) {
    return fetch({
        url: '/cwe/a/contractSell/getCustomerContractNotSettle',
        method: 'get',
        params: params
    })
}

export function contractList(params = {}) {
    return fetch({
        url: '/cwe/a/supplier/list',
        method: 'get',
        params: params
    })
}

export function contractSell(params = {}) {
    return fetch({
        url: '/cwe/a/contractSell',
        method: 'get',
        params: params
    })
}

export function getCustomerContract(params = {}) {
    return fetch({
        url: '/cwe/a/contractSell/getCustomerContract',
        method: 'get',
        params: params
    })
}

export function chartData(date) {
    return fetch({
        url: '/cwe/a/qualPile/getChartData',
        method: 'get',
        params: { date }
    })
}

//采购合同
export function getContractBuy(params = {}) {
    return fetch({
        url: '/cwe/a/contractBuy/page',
        method: 'get',
        params: params
    })
}

//销售合同
export function getContractSell(params = {}) {
    return fetch({
        url: '/cwe/a/contractSell/page',
        method: 'get',
        params: params
    })
}


