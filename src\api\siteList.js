import fetch from '@/utils/request'

const prefix = '/cwe/a/site'

/*
* 查询
* @param searchForm
* */
export function pageSite (searchForm) {
    return fetch({
        url: prefix + '/page',
        method: 'get',
        params: searchForm
    })
}

/*
* 删除列表
* @param id
* */
export function deleteSite (id) {
    return fetch({
        url: prefix + '/delete',
        method: 'post',
        data: { id: id }
    })
}

/*
* 提交列表
* @param data
* */
export function saveSite (data) {
    return fetch({
        url: prefix + '/saveSite',
        method: 'post',
        data: data
    })
}

/**
 * 根据keyword查询网点和分拨中心
 * @param searchForm
 **/
export function findByKeyword (keyword) {
    return fetch({
        url: '/cwe/a/site/findByKeyword',
        method: 'get',
        params: {
            keyword
        }
    })
}

/**
 * 根据keyword查询网点
 * @param searchForm
 */
export function findSiteByKeyword (keyword) {
    return fetch({
        url: '/cwe/a/site/findSiteByKeyword',
        method: 'get',
        params: {
            keyword
        }
    })
}

/**
 * 查询分拨中心
 * @param searchForm
 */
export function findSortCenter (searchForm) {
    return fetch({
        url: '/cwe/a/site/findSortCenter',
        method: 'get',
        params: searchForm === void 0 ? {} : searchForm
    })
}

/**
 * 查询所有网点
 * @param searchForm
 */
export function findAllSiteByKeyword (searchForm) {
    return fetch({
        url: prefix + '/findByKeywordAll',
        method: 'get',
        params: searchForm === void 0 ? {} : searchForm
    })
}

/*
*查询网点树结构
* */
export function findAllWithTreeFormatted () {
    return fetch({
        url: '/cwe/a/site/findAllWithTreeFormatted',
        method: 'get'
    })
}

/*
*查询业务区信息
* */
export function findYwqByKeyword (searchForm) {
    return fetch({
        url: prefix + '/findYwqByKeyword',
        method: 'get',
        params: searchForm === void 0 ? {} : searchForm
    })
}

/*
*查询业务区树
* */
export function findYwqWithTreeFormatted () {
    return fetch({
        url: prefix + '/findYwqWithTreeFormatted',
        method: 'get'
    })
}

/*
*高级匹配(业务区域查询网点)
* */
export function findByBizRegion (search) {
    return fetch({
        url: prefix + '/findByBizRegion',
        method: 'get',
        params: search
    })
}

/*
* excel导入
* @param data
* */
export function importSite (data) {
    return fetch({
        url: prefix + '/importSite',
        method: 'post',
        data: data
    })
}

export function pageSites () {
    return fetch({
        url: '/cwe/a/site/findAllWithTreeFormatted',
        method: 'get',
        params: {}
    })
}

export function findOtherSiteZone (data) {
    return fetch({
        url: '/cwe/a/siteZone/findOtherSiteZone',
        method: 'get',
        params: data
    })
}
