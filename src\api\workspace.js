import fetch from '@/utils/request'

const urlPrefix = '/cwe/a/workspace'

/*
* 清空煤种列表
* */
export function removeAllCoal(params) {
    return fetch({
        url: urlPrefix + '/removeAllCoal',
        method: 'post',
        data: params
    })
}

export function addRockCoal(params) {
    return fetch({
        url: '/a/workspaceCoalRock/addCoals',
        method: 'post',
        data: params
    })
}

/**
 * 添加煤矿数据
 * @param spaceType
 * @param coalId
 */
export function addCoal({spaceType, coalId}) {
    return fetch({
        url: urlPrefix + '/addCoal',
        method: 'post',
        data: {spaceType, coalId}
    })
}

/*
* 添加多条煤矿数据
* @param spaceType coalIdList
* */
export function addCoals(data) {
    return fetch({
        url: urlPrefix + '/addCoals',
        method: 'post',
        data: {...data}
    })
}

/**
 * 计算
 * @param data
 */
export function forcast(data) {
    return fetch({
        url: urlPrefix + '/forcast',
        method: 'post',
        data: data
    })
}

/**
 * 配煤方案优化计算
 * @param data
 */
export function optimize(data) {
    return fetch({
        url: urlPrefix + '/optimize',
        method: 'post',
        data: data
    })
}

/**
 * 通过type配煤方案优化计算
 * @param data
 */
export function optimizeByType(data) {
    return fetch({
        url: urlPrefix + '/optimizeByType',
        method: 'post',
        data: data
    })
}

/*
* 获取数据
* */
export function getMySpace(type) {
    return fetch({
        url: urlPrefix + '/getMySpace',
        method: 'get',
        params: {
            type
        }
    })
}

/*
* 删除API
* */
export function deleteMySpaceById({id}) {
    return fetch({
        url: urlPrefix + '/delete',
        method: 'post',
        data: {id: id}
    })
}

export function syncCoal(workspace) {
    return fetch({
        url: urlPrefix + '/syncCoal',
        method: 'post',
        data: {...workspace}
    })
}
