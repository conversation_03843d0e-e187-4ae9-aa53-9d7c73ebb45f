<template>
  <el-autocomplete class="inline-input" v-model="val" :fetch-suggestions="gainRemoteData" :placeholder="placeholder" clearable
                   :disabled="disabled" @input="handleChange">
    <slot />
  </el-autocomplete>
</template>
<script>
import { coalType } from '@/api/public'
export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '', // 映射值
      list: []
    }
  },
  props: {
    value: {
      required: true
    },
    ext: {
      type: Object
    },
    type: {
      type: String,
      default: 'COAL_TYPE',
      required: false
    },
    placeholder: {
      type: String,
      default: '煤种类型'
    },
    disabled: {
      type: [Bo<PERSON>an],
      default: false
    }
  },
  async created() {
    if (this.type === 'teshu') {
      let data = window.dict['coal_type'] || []
      const gather = data.map((v) => {
        return { value: v.name, id: v.code }
      })
      this.list = gather
    } else {
      const res = await coalType({
        filter_EQS_category: this.type,
        orderBy: 'createDate',
        orderDir: 'DESC'
      })
      const gather = res.data.map((v) => {
        return { value: v.value, id: v.id }
      })
      this.list = gather
    }
  },
  methods: {
    createFilter(query) {
      return (restaurant) => {
        return restaurant.value.indexOf(query) === 0
      }
    },
    async gainRemoteData(query = '', cb) {
      const results = query ? this.list.filter(this.createFilter(query)) : this.list

      cb(results)
    },

    handleChange(val) {
      this.$emit('update:value', val)
      this.$emit('triggerChange', val)
    }
  },
  watch: {
    value: {
      handler(v) {
        this.val = v ? v + '' : ''
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss" scoped>
.inline-input {
  width: 100%;
}
</style>
