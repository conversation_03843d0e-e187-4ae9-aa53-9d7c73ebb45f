<template>
  <!--  <div>-->
  <!--      <div>{{impChartData}}</div>-->
  <div :class="id" :id="id" ref="columnChart"></div>
  <!--  </div>-->
</template>

<script>
import echarts from 'echarts'

const list = []
export default {
  props: {
    impChartData: {
      type: Object,
    },
    id: {
      type: String,
      default: 'columnChart',
    },
    isShowLabel: {
      type: Boolean,
    },
  },
  data() {
    return {
      myLineChart: null,
    }
  },
  created() {
    // const newImportData = {
    //   name: this.impChartData.name
    // }

    this.$nextTick(() => {
      this.myLineChart = echarts.init(this.$refs.columnChart)
      this.drawLine(
        this.impChartData.name,
        this.impChartData.rangeAllList,
        this.impChartData.proportionAllList,
        '',
        '',
        '',
        'bar',
        this.isShowLabel
      )
      list.push(this.myLineChart)
    })
  },
  watch: {
    impChartData() {
      this.$nextTick(() => {
        this.drawLine(
          this.impChartData.name,
          this.impChartData.rangeAllList,
          this.impChartData.proportionAllList,
          '',
          '',
          '',
          'bar',
          this.isShowLabel
        )
      })
    },
  },
  methods: {
    drawLine(name, xData, yData, mark, xName, yName, type, isShowLabel) {
      // console.log(this.impChartData, 'impChartData')
      if (!isShowLabel) {
        this.myLineChart.setOption({
          animation: false,
          // title: {
          //   text: name
          // },
          // tooltip: {
          //   trigger: 'axis',
          //   axisPointer: { // 坐标轴指示器，坐标轴触发有效
          //     type: 'none' // 默认为直线，可选为：'line' | 'shadow'
          //   }
          // },
          xAxis: [
            {
              name: xName,
              type: 'category',
              data: xData,
            },
          ],
          yAxis: {
            scale: true,
            name: yName,
          },
          grid: {
            left: 0,
            top: 0,
            right: 0,
            bottom: 0,
          },
          series: [
            {
              label: {
                show: true,
                color: '#000',
                position: 'top',
              },
              smooth: true,
              name: yName,
              type: type,
              data: yData,
              barGap: '0%',
              barCategoryGap: '0%',
            },
          ],
        })
      } else {
        this.myLineChart.setOption({
          animation: false,
          grid: {
            // left: '15%',
            // right: '15%',
            // top: '15%',
            // bottom: '10%'
          },
          color: ['#f8a20f'],
          title: {
            text: name,
            x: '50%',
            y: '0px',
            textStyle: {
              fontSize: 22,
              fontWeight: 300,
            },
            textAlign: 'center',
          },
          // tooltip: {
          //   trigger: 'axis',
          //   axisPointer: { // 坐标轴指示器，坐标轴触发有效
          //     type: 'none' // 默认为直线，可选为：'line' | 'shadow'
          //   }
          // },
          xAxis: [
            {
              name: xName,
              type: 'category',
              data: xData,
              axisLabel: {
                // 坐标轴刻度标签的相关设置
                color: '#949393',
                fontSize: 13,
              },
              axisLine: {
                // 坐标轴轴线相关设置
                show: true,
                lineStyle: {
                  // 坐标轴线线的颜色
                  color: '#E3E7F0',
                  type: 'solid',
                },
              },
            },
          ],
          yAxis: {
            scale: true,
            name: yName,
            axisLabel: {
              // 坐标轴刻度标签的相关设置
              color: '#949393',
              fontSize: 13,
            },
            axisLine: {
              // 坐标轴轴线相关设置
              show: true,
              lineStyle: {
                // 坐标轴线线的颜色
                color: '#E3E7F0',
                type: 'solid',
              },
            },
          },
          series: [
            {
              label: {
                show: false,
                color: '#ccc',
                position: 'top',
              },
              // smooth: true,
              show: true,
              position: 'top',
              name: yName,
              type: type,
              data: yData,
              barGap: '0%',
              barCategoryGap: '0%',
              // itemStyle: {
              //   normal: {
              //     barBorderRadius: [5, 5, 0, 0] // 配置样式
              //   }
              // }
            },
          ],
        })
      }
      // window.onresize = list[i].resize()
      // window.onresize = this.myLineChart.resize  单个e-chart图标使用 样式自适应
      window.onresize = function () {
        // 多个e-chart图标使用 样式自适应
        for (let i = 0; i < list.length; i++) {
          list[i].resize()
        }
      }
    },
  },
}
</script>

<style scoped lang="scss">
.columnChart {
  width: 100%;
  height: 300px;
}
</style>

