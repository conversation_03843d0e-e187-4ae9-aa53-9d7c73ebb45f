<template>
  <!--  <div>-->
  <!--      <div>{{impChartData}}</div>-->
  <div :id="id" ref="columnChart" :class="id"></div>
  <!--  </div>-->
</template>

<script>
import echarts from 'echarts'

const list = []
export default {
  props: {
    impChartData: {
      type: Object
    },
    id: {
      type: String,
      default: 'columnChart'
    }
  },
  data() {
    return {
      myLineChart: null
    }
  },
  created() {
    this.$nextTick(() => {
      this.myLineChart = echarts.init(this.$refs.columnChart)
      this.drawLine(this.impChartData.name, this.impChartData.xData, this.impChartData.yData, this.impChartData.xName, this.impChartData.yName, this.impChartData.type, this.impChartData.color)
      list.push(this.myLineChart)
    })
  },
  watch: {
    'impChartData': {
      handler() {
        this.$nextTick(() => {
          this.drawLine(this.impChartData.name, this.impChartData.xData, this.impChartData.yData, this.impChartData.xName, this.impChartData.yName, this.impChartData.type, this.impChartData.color)
        })
      },
      deep: true
    }
  },
  methods: {
    drawLine(name, xData, yData, xName, yName, type, color) {
      this.myLineChart.setOption({
        color: color,
        title: {
          text: name,
          x: 'center',
          y: '15px',
          textStyle: {
            fontSize: 16,
            fontWeight: 400
          },
          textAlign: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'none' // 默认为直线，可选为：'line' | 'shadow'
          }
        },

        xAxis: [{
          name: xName,
          type: 'category',
          data: xData,
          axisLabel: { // 坐标轴刻度标签的相关设置
            color: '#949393',
            fontSize: 13
          },
          axisLine: { // 坐标轴轴线相关设置
            show: true,
            lineStyle: { // 坐标轴线线的颜色
              color: '#E3E7F0',
              type: 'solid'
            }
          }
        }],
        yAxis: {
          type: 'value',
          scale: true,
          name: yName,
          axisLabel: { // 坐标轴刻度标签的相关设置
            color: '#949393',
            fontSize: 13
          },
          axisLine: { // 坐标轴轴线相关设置
            show: true,
            lineStyle: { // 坐标轴线线的颜色
              color: '#E3E7F0',
              type: 'solid'
            }
          }
        },
        series: [{
          label: {
            show: true,
            color: '#949393',
            position: 'top'
          },
          show: true,
          position: 'top',
          smooth: true,
          name: yName,
          type: type,
          data: yData,
          barGap: '0%',
          barCategoryGap: '0%'
        }]
      })
      // window.onresize = list[i].resize()
      // window.onresize = this.myLineChart.resize  单个e-chart图标使用 样式自适应
      window.onresize = function () { // 多个e-chart图标使用 样式自适应
        for (let i = 0; i < list.length; i++) {
          list[i].resize()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.columnChart {
  width: 100%;
  height: 300px;
}
</style>

