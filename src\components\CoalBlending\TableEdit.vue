<template>
  <div ref="editBox" class="editBox" v-show="visible">
    <el-input v-if="type==='input'" ref="input" v-model="row[column.property]" type="number"
              @blur="onBlur"></el-input>
    <el-select v-else-if="type==='select'" ref="select" v-model="row[column.property]" placeholder=""
               @visible-change="onVisibleChange" :clearable="true">
      <el-option label="≤" value="le"/>
      <el-option label="≥" value="ge"/>
      <el-option label="=" value="eq"/>
    </el-select>
  </div>
</template>
<script>
export default {
  name: 'TableEdit',
  props: {},
  data() {
    return {
      visible: false,
      row: {},
      column: {},
      value: '',
      type: 'input',
      show: ['macR0', 'macS', 'cleanAd', 'cleanVdaf', 'cleanStd', 'arrivePrice', 'procG', 'procY', 'procX', 'percent1', 'percent2', 'percentCond', 'quantity']
    }
  },
  methods: {
    /**
     * @param row
     * @param column
     * @param {HTMLTableCellElement} cell
     */
    open(row, column, cell) {
      console.log(row[column.property])
      if (this.show.indexOf(column.property) < 0) return
      if (column.property !== 'arrivePrice' &&
        column.property !== 'percentCond' &&
        column.property !== 'quantity' &&
        column.property !== 'percent1') {
        if (row.source === 'ZS') return
        if (row.source === 'NB') return
      }
      this.type = 'input'
      if (column.property === 'percentCond') {
        this.type = 'select'
      }
      this.row = row
      this.column = column
      this.value = row[column.property]
      this.visible = true
      const left = this.$refs.editBox.parentNode.getBoundingClientRect().left
      const top = this.$refs.editBox.parentNode.getBoundingClientRect().top
      this.$refs.editBox.style.left = (cell.getBoundingClientRect().left - left) + 'px'
      this.$refs.editBox.style.top = (cell.getBoundingClientRect().top - top) + 'px'
      this.$refs.editBox.style.width = cell.offsetWidth + 'px'
      this.$refs.editBox.style.height = cell.offsetHeight + 'px'
      this.$nextTick(() => {
        if (this.type === 'input') {
          this.$refs.input.focus()
        } else {
          this.$refs.select.toggleMenu()
        }
      })
    },
    onVisibleChange(visible) {
      if (visible === false) {
        this.$nextTick(() => {
          this.visible = false
          this.row = {}
          this.column = {}
        })
      }
    },
    onBlur() {
      this.visible = false
      this.row = {}
      this.column = {}
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.editBox {
  position: absolute;
  left: 200px;
  width: 100px;
  height: 100px;
  z-index: 1000;
}

</style>
