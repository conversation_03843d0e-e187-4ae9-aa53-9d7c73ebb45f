<template>
  <div>
    <el-form ref="entityForm" :model="entityForm" label-width="80px">
      <div class="content-title">
        基础信息
      </div>
      <el-row>
        <el-col :span="6">
          <el-form-item label="名称" prop="name" :rules="[{required:true, message:'请输入名称'}]">
            <el-input v-model="entityForm.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="煤种" prop="type" :rules="[{required:true, message:'请选择煤种'}]">
            <dict-select v-model="entityForm.type" type="coal_type" clearable></dict-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="省市区" prop="province" :rules="[{required:true, message:'请输入省市'}]">
            <region-select :value="region" @input="value=>((entityForm.location=value.location)||1)&&(region=value.val)">
            </region-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="batchCode" label="批次号" :rules="[{required:true, message:'请输入批次号'}]">
            <el-input :value="entityForm.batchCode" type="number"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <div class="content-title">
          原煤指标
        </div>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="全水" prop="rawMt" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.rawMt" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="全灰" prop="rawAd" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.rawAd" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="-0.5" prop="rawPointFive" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.rawPointFive" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="-1.4" prop="rawOnePointFour" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.rawOnePointFour" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="内灰" prop="rawAdIn" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.rawAdIn" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="硫" prop="rawStd" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.rawStd" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="挥发分" prop="rawVdaf">
            <el-input v-model="entityForm.rawVdaf" type="number">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="粘结" prop="rawG" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.rawG" type="number"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="arrivePrice" label="到厂价" :rules="[{required:true, message:'请输入到厂价'}]">
            <el-input v-model="entityForm.arrivePrice" type="number">
              <template slot="append">(元/吨)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <div class="content-title">
          精煤指标
        </div>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="cleanVdaf" label="Vdaf"
                        :rules="[{required:true, message:'请输入数值'},{validator: this.RangerValidate}]">
            <el-input @input="handleVdaf" v-model="entityForm.cleanVdaf" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="cleanAd" label="Ad" :rules="[{required:true, message:'请输入数值'},{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.cleanAd" type="number" @input="handleAd">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="cleanStd" label="St,d" :rules="[{required:true, message:'请输入数值'},{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.cleanStd" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="cleanMt" label="Mt" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.cleanMt" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="cleanP" label="P" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.cleanP" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="cleanVd" label="Vd">
            <el-input v-model="entityForm.cleanVd" type="number" @input="handleVd">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="content-title">
        工艺性能
      </div>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="procG" label="G" :rules="[{required:true, message:'请输入数值'}]">
            <el-input v-model="entityForm.procG" type="number">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="procY" label="Y">
            <el-input v-model="entityForm.procY" type="number">
              <template slot="append">(mm)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="procX" label="X">
            <el-input v-model="entityForm.procX" type="number">
              <template slot="append">(mm)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="procMf" label="MF">
            <el-input v-model="entityForm.procMf" type="number">
              <template slot="append">(ddpm)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="procTp" label="Tp">
            <el-input v-model="entityForm.procTp" type="number">
              <template slot="append">(℃)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="procTmax" label="Tmax">
            <el-input v-model="entityForm.procTmax" type="number">
              <template slot="append">(℃)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="procTk" label="Tk">
            <el-input v-model="entityForm.procTk" type="number">
              <template slot="append">(℃)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="procCrc" label="特征">
            <el-input v-model="entityForm.procCrc" type="number">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="a" prop="procA" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.procA" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="b" prop="procB">
            <el-input v-model="entityForm.procB" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="content-title">
        煤岩组分
      </div>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="macR0" label="反射率(R0)">
            <el-input v-model="entityForm.macR0" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="macS" label="标准差(S)">
            <el-input v-model="entityForm.macS" type="number">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="macV" label="镜质组(V)" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.macV" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="macI" label="丝质组(I)" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.macI" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="macE" label="稳定组(E)" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.macE" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="macE" label="活性物">
            <el-input v-model="entityForm.active" type="number" @input="handleActive">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="activeInertiaRatio" label="活惰比">
            <el-input v-model="entityForm.activeInertiaRatio" type="number">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="content-title">
        煤灰成分
        <el-button type="primary" size="mini" @click="handleCoalAsh">获取灰成分参考值</el-button>
      </div>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="comSiO2">
            <span slot="label">SiO<sub>2</sub></span>
            <el-input v-model="entityForm.comSiO2" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="comAl2O3">
            <span slot="label">Al<sub>2</sub>O<sub>3</sub></span>
            <el-input v-model="entityForm.comAl2O3" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="Fe2O3" prop="comFe2O3">
            <span slot="label">Fe<sub>2</sub>O<sub>3</sub></span>
            <el-input v-model="entityForm.comFe2O3" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="CaO" prop="comCaO">
            <el-input v-model="entityForm.comCaO" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="comMgO" label="MgO">
            <el-input v-model="entityForm.comMgO" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="comNa2O" label="Na2O">
            <span slot="label">Na<sub>2</sub>O</span>
            <el-input v-model="entityForm.comNa2O" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="comK2O" label="K2O">
            <span slot="label">K<sub>2</sub>O</span>
            <el-input v-model="entityForm.comK2O" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="comTiO2" label="TiO2">
            <span slot="label">TiO<sub>2</sub></span>
            <el-input v-model="entityForm.comTiO2" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="comP2O5" label="P2O5">
            <span slot="label">P<sub>2</sub>O<sub>5</sub></span>
            <el-input v-model="entityForm.comP2O5" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="comSO3" label="SO3">
            <span slot="label">SO<sub>3</sub></span>
            <el-input v-model="entityForm.comSO3" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="content-title">
        成煤环境
      </div>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="cfeCp" label="成煤期">
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="cfeCe" label="成煤环境">
          </el-form-item>
        </el-col>
      </el-row>
      <div class="content-title">
        质量指标
      </div>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="qualScon" label="硫转换率" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.qualScon" type="number"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="qualM40" label="M40" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.qualM40" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="qualM10" label="M10" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.qualM10" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="qualCsr" label="CSR" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.qualCsr" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="qualCri" label="CRI" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.qualCri" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="qualTestCond" label="实验条件">
          </el-form-item>
        </el-col>
      </el-row>
      <div class="content-title">
        化产回收
      </div>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="crGk" label="煤气产量">
            <el-input v-model="entityForm.crGk" type="number">
              <template slot="append">(Nm<sup>3</sup>)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="crBk" label="粗苯产率" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.crBk" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="crTk" label="焦油产率" :rules="[{validator: this.RangerValidate}]">
            <el-input v-model="entityForm.crTk" type="number">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="content-title">
        反射率区间
        <span>(当前反射率之和为{{entityForm.rate}}，反射率要求在99.5到100.5之间)</span>
      </div>
      <el-row>
        <el-col :span="6" v-for="item in entityForm.coalRockList" :key="item.sort">
          <el-form-item :label="item.rangeName">
            <el-input v-model="item.proportion" type="number" @input="changeRate">
              <template slot="append">(%)</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="content-title" style="margin-top: 10px">
        备注
      </div>
      <el-row style="margin-top: 30px">
        <el-col :span="24">
          <el-form-item prop="remarks" label="备注">
            <el-input v-model="entityForm.remarks" type="textarea" :rows="6">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <new-column-chart v-if="entityForm" :impChartData="entityForm" :isShowLabel="true" style="width: 100%; height: 300px;">
      </new-column-chart>
      <el-table :data="entityForm.coalTypeProportionList" border :show-header="false" style="width: 100%">
        <el-table-column prop="brownCoal">
        </el-table-column>
        <el-table-column prop="longFlame">
        </el-table-column>
        <el-table-column prop="gasCoal">
        </el-table-column>
        <el-table-column prop="thirdCokingCoal">
        </el-table-column>
        <el-table-column prop="fatCoal">
        </el-table-column>
        <el-table-column prop="cokingCoal">
        </el-table-column>
        <el-table-column prop="leanCoal">
        </el-table-column>
        <el-table-column prop="meagerLeanCoal">
        </el-table-column>
        <el-table-column prop="meagerCoal">
        </el-table-column>
        <el-table-column prop="smokelessCoal">
        </el-table-column>
      </el-table>
    </el-form>
    <el-dialog title="选择坐标" :visible.sync="dialogVisible" fullscreen :modal="false" append-to-body :before-close="handleClose">
      <BMap :position.sync="position" ref="BMap" type="position" v-if="dialogVisible"></BMap>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmitPosition">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Func from '@/utils/func'
import RegionSelect from '@/components/RegionSelect'
import { dateFormat } from '@/filters'
import NewColumnChart from '@/components/Chart/NewColumnChart'
import { coalAshList, saveAttachment } from '@/api/coal'

const entityForm = {
  'id': '',
  'userId': '',
  'name': '',
  'batchCode': dateFormat(new Date(), 'yyyymmdd'),
  'type': '',
  'province': '',
  'factoryPrice': '',
  'transitFee': '',
  'roadCost': '',
  'arrivePrice': '',
  'arriveFactory': 'JT',
  'mineDepth': '',
  'rawMt': '',
  'rawAd': '',
  'rawPointFive': '',
  'rawOnePointFour': '',
  'rawAdIn': '',
  'rawStd': '',
  'rawVdaf': '',
  'rawG': '',
  'cleanVdaf': '',
  'cleanAd': '',
  'cleanStd': '',
  'cleanMt': '',
  'cleanP': '',
  'cleanMci': '',
  'procG': '',
  'procY': '',
  'procX': '',
  'procMf': '',
  'procTp': '',
  'procTmax': '',
  'procTk': '',
  'procCrc': '',
  'procA': '',
  'procB': '',
  'macR0': '',
  'macS': '',
  'macV': '',
  'macI': '',
  'macE': '',
  'comSiO2': '',
  'comAl2O3': '',
  'comFe2O3': '',
  'comCaO': '',
  'comMgO': '',
  'comNa2O': '',
  'comK2O': '',
  'comTiO2': '',
  'comP2O5': '',
  'comSO3': '',
  'cfeCp': '',
  'cfeCe': '',
  'qualScon': '',
  'qualPcon': '',
  'qualM40': '',
  'qualM10': '',
  'qualCsr': '',
  'qualCri': '',
  'qualTestCond': '',
  'crGk': '',
  'crBk': '',
  'crTk': '',
  'city': '',
  'dataType': '',
  'createBy': '',
  'createDate': '',
  'updateBy': '',
  'updateDate': '',
  'remarks': '',
  'ext': '',
  'location': '',
  'area': '',
  'longitude': '',
  'latitude': '',
  'isFavorite': '-'
}
const components = {
  NewColumnChart,
  RegionSelect
}
export default {
  name: 'index',
  data() {
    return {
      active: false,
      rate: 0,
      uploadFunc: saveAttachment,
      entityFormLoading: false,
      arriveFactory: void 0,
      dialogVisible: false,
      coalDialogVisible: false,
      position: {},
      coalData: [],
      tableData: []
    }
  },
  props: {
    entityForm: { // 实体数据
      // type: [Object],
      default: { ...entityForm }
    },
    isAdd: { // 是否添加页面
      type: [Boolean],
      default: false
    },
    isAssay: { //  是否是优化
      type: [Boolean],
      default: false
    }
  },
  components: {
    ...components
  },
  computed: {
    ...mapGetters([
      'factoryList',
      'dict'
    ]),
    'region': {
      set(val) {
        this.entityForm = Object.assign(this.entityForm, {
          province: val[0],
          city: val[1],
          area: val[2]
        })
      },
      get() {
        return [this.entityForm.province, this.entityForm.city, this.entityForm.area]
      }
    },
    'dynamicArriveFactory'() {
      // 是添加页面
      if (this.isAdd) {
        // 如果是煤质数据库
        if (!this.isAssay) {
          this.arriveFactory = 'JT'
          return '亚鑫集团'
        }
        // 如果是优化系统coalRockProject
        if (this.isAssay) {
          const currentFactoryCode = localStorage.getItem('currentFactoryCode')
          const index = this.factoryList.findIndex(val => val.code === currentFactoryCode)
          const { code, name } = this.factoryList[index]
          this.arriveFactory = code
          if (name === '集团公司') {
            return '亚鑫集团'
          } else {
            return name
          }
        }
      }
      // 不是添加页面
      if (!this.isAdd) {
        const { arriveFactory } = this.entityForm
        const index = this.factoryList.findIndex(val => val.code === arriveFactory)
        const { name } = this.factoryList[index]
        if (name === '集团公司') {
          return '亚鑫集团'
        } else {
          return name
        }
      }
    },
    'location'() {
      if (!this.entityForm.longitude || !this.entityForm.latitude) {
        return ''
      }
      return `${this.entityForm.longitude},${this.entityForm.latitude}`
    }
  },
  watch: {
    entityForm(val) {
      if (val.latitude && val.longitude) {
        this.$set(this.position, 'lng', val.longitude)
        this.$set(this.position, 'lat', val.latitude)
      }
    }
  },
  mounted() {
  },
  methods: {
    handleActive(e) {
      this.entityForm.activeInertiaRatio = (e / (100 - e)).toFixed(2)
    },
    handleVdaf(e) {
      if (this.entityForm.cleanAd) {
        this.entityForm.cleanVd = (e * (100 - this.entityForm.cleanAd) / 100).toFixed(2)
      } else {
        this.entityForm.cleanVd = e
      }
    },
    handleAd(e) {
      if (this.entityForm.cleanVdaf) {
        this.entityForm.cleanVd = (this.entityForm.cleanVdaf * (100 - e) / 100).toFixed(2)
      } else {
        this.entityForm.cleanVd = this.entityForm.cleanVdaf
      }
    },
    handleVd(e) {
      if (this.entityForm.cleanAd) {
        this.entityForm.cleanVdaf = (100 * e / (100 - this.entityForm.cleanAd)).toFixed(2)
      } else {
        this.entityForm.cleanVdaf = e
      }
    },
    changeRate() {
      this.entityForm.rate = 0
      this.entityForm.coalRockList.map(item => {
        if (item.proportion) {
          this.entityForm.rate = Number(item.proportion) + this.entityForm.rate
        }
      })
    },
    /*
    * 数值范围验证器
    * */
    RangerValidate(rules, value, cb) {
      if (+value < 0) {
        cb(new Error('数值不能小于0'))
      } else if (+value > 100) {
        cb(new Error('数值不能大于100'))
      } else {
        cb()
      }
    },
    handleClose() {
      this.dialogVisible = false
      this.position = {}
      this.$refs['BMap'].$emit('refresh')
    },
    handleSubmitPosition() {
      if (Object.keys(this.position).length === 0) {
        return
      }
      this.$set(this.entityForm, 'longitude', this.position.lng)
      this.$set(this.entityForm, 'latitude', this.position.lat)
      this.handleClose()
    },
    /**
     * 获取煤灰成份
     */

    async handleCoalAsh() {
      const location = { filter_EQS_area: this.entityForm.area }
      const res = await Func.fetch(coalAshList, location)
      if (res.data.records.length > 0) {
        delete res.data.records[0].id
        this.$emit('update:entityForm', { ...this.entityForm, ...res.data.records[0] })
      } else {
        this.$message({
          showClose: true,
          message: '无灰成分参考数据，请化验',
          type: 'error'
        })
      }
    },
    /**
     * 关闭煤灰成分弹框
     * @param done
     */
    handleCoalClose(done) {
      done()
    }
  }
}
</script>

<style scoped lang="scss">
.btn {
  color: #909399;
  background: none;
  border: none;
  cursor: pointer;
  outline: none;
}

.dialog-content {
  margin: 5px 10px;
}
</style>
