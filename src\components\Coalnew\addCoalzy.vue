<template>
  <el-dialog class="add" :title="details.name || '新增'" width="80%" top="3vh" :visible.sync="visible"
             :append-to-body="isSourceWash" :before-close="()=>handleClose('full')">
    <div class="coal">
      <el-form ref="entityForm" :model="entityForm" label-width="80px" :disabled="dialogStatus==='watch'">
        <indicators-card>
          <template #title>
            <div class="title-content">
              <span>基础信息</span>
              <!-- <div>
                <el-button type="primary" class="filter-itembtn" @click="handleRefreshindex(entityForm.id)">刷新库存价</el-button>
                <el-button type="primary" class="filter-itembtn" @click="handleRefreshPrice(entityForm.id)">刷新煤质</el-button>
              </div> -->
            </div>
          </template>
          <!-- <el-row>
            <el-col :span="6">
              <el-form-item label="品名" prop="name">
                <el-input v-model="entityForm.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="矿井名称" prop="mineName">
                <el-input v-model="entityForm.mineName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="库存量" prop="stock">
                <el-input v-model="entityForm.stock"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="price" label="库存价">
                <el-input :value="entityForm.price" type="number"></el-input>
              </el-form-item>
            </el-col>
          </el-row> -->

          <el-row type="flex" :gutter="50">
            <!-- <el-col>
              <el-table :data="entityForm.Basicinformation" stripe :header-cell-style="headClass">
                <el-table-column label="名称" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.name"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="矿井名称" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.mineName"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="库存价" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.price"></el-input>
                  </template>
                </el-table-column>
              </el-table>
            </el-col> -->

            <el-col v-if="entityForm.Basicinformation">
              <EditTable ref="editTable" @inited="handleEditTableInitInfo" :columns="getColumnsInfo" :rowHeaders="true"
                         v-model="entityForm.Basicinformation">
              </EditTable>
            </el-col>
          </el-row>

        </indicators-card>
        <indicators-card title="指标">
          <!-- <el-row>
            <el-col :span="6">
              <el-form-item label="水分" prop="mt">
                <el-input v-model="entityForm.mt" type="number"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="灰分" prop="ad">
                <el-input v-model="entityForm.ad" type="number">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="硫分" prop="std">
                <el-input v-model="entityForm.std" type="number"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="挥发分" prop="vdaf">
                <el-input v-model="entityForm.vdaf" type="number">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="G" prop="g">
                <el-input v-model="entityForm.g" type="number">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="Y" prop="y">
                <el-input v-model="entityForm.y" type="number">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="CRC" prop="crc">
                <el-input v-model="entityForm.crc" type="number">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="反射率" prop="macR0">
                <el-input v-model="entityForm.macR0" type="number">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="macS" label="标准差">
                <el-input v-model="entityForm.macS" type="number"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="recovery" label="回收">
                <el-input v-model="entityForm.recovery" type="number"> </el-input>
              </el-form-item>
            </el-col>
          </el-row> -->

          <!-- <el-row type="flex" :gutter="50">
            <el-col>
              <el-table :data="entityForm.indexlist" stripe :header-cell-style="headClass">
                <el-table-column label="灰分" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.ad"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="硫分" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.std"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="挥发分" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.vdaf"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="CRC" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.crc"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="G" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.g"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="Y" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.y"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="水分" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.mt"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="反射率" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.macR0"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="标准差" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.macS"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="回收" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.recovery"></el-input>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row> -->

          <el-col v-if="entityForm.indexlist">
            <EditTable ref="editTable2" @inited="handleEditTableInit" :columns="getColumns" :rowHeaders="true"
                       v-model="entityForm.indexlist">
            </EditTable>
          </el-col>
        </indicators-card>

        <indicators-card title="反射率分布表" :subtitle="`(当前反射率之和为${entityForm.rate>0?entityForm.rate:'空'}，反射率要求在99.5到100.5之间)`">
          <!-- <el-row>
            <el-col :span="6" v-for="item in entityForm.rangeDtoList" :key="item.sort">
              <el-form-item :label="item.rangeName">
                <el-input v-model="item.proportion" type="number" @input="changeRate">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row> -->

          <el-row type="flex" :gutter="80">
            <el-col v-if="entityForm.rangeDtoList">
              <EditTable :colHeaders="true" ref="editTable1" @inited="handleEditTableInit1" :columns="getColumns1"
                         v-model="editData1"></EditTable>
            </el-col>
          </el-row>
        </indicators-card>

        <indicators-card v-if="details.id" title="文件">
          <el-col :span="12">
            <div class="form-layout">
              <el-row>
                <el-col>
                  <div class="form-title">煤岩报告上传</div>
                  <div class="form-layout">
                    <div class="upload">
                      <div class="upload-list">
                        <div class="up-load-warp " v-for="(item,index) in uploadList" :key="index">
                          <el-image class="img active" :title="item.display" :src="item.uri" fit="fill"
                                    :preview-src-list="uploadList.map(v=>v.uri)" />
                          <img src="@/assets/close.png" class="icon" @click="handleRemoveUpload(index,'uploadList')">
                        </div>
                        <!-- <upload-attachment ref="upload" class="upload-attachment"
                                           :uploadData="{refId: '', refType: 'ContractBuy'}" source="ContractBuy"
                                           :size="uploadList.length" :limitNum="50" accept=".jpg,.jpeg,.png" :isShowFile="false"
                                           @successNotify="handleUploadSuccess($event,'uploadList')"
                                           @deleteNotify="handleUploadDelete($event,'uploadList')" listType="picture" /> -->
                      </div>
                    </div>
                  </div>

                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col :span="12">
            <div class="form-layout">
              <el-row>
                <el-col>
                  <div class="form-title">CSR报告上传</div>
                  <div class="form-layout">
                    <div class="upload">
                      <div class="upload-list">
                        <div class="up-load-warp " v-for="(item,index) in uploadList1" :key="index">
                          <el-image class="img active" :title="item.display" :src="item.uri" fit="fill"
                                    :preview-src-list="uploadList1.map(v=>v.uri)" />
                          <img src="@/assets/close.png" class="icon" @click="handleRemoveUpload(index,'uploadList1')">
                        </div>
                        <!-- <upload-attachment ref="upload1" class="upload-attachment"
                                           :uploadData="{refId: '', refType: 'ContractBuy'}" source="ContractBuy"
                                           :size="uploadList1.length" :limitNum="50" accept=".jpg,.jpeg,.png" :isShowFile="false"
                                           @successNotify="handleUploadSuccess($event,'uploadList1')"
                                           @deleteNotify="handleUploadDelete($event,'uploadList1')" listType="picture" /> -->
                      </div>
                    </div>
                  </div>

                </el-col>
              </el-row>
            </div>
          </el-col>
        </indicators-card>

        <indicators-card v-if="details.id" title="图表信息">
          <template v-if="entityForm && entityForm.coalTypeProportionList.length">
            <new-column-chart :impChartData="entityForm" isShowLabel style="width: 100%; height: 300px;" />
            <el-table :data="entityForm.coalTypeProportionList" border :show-header="false" class="chart-table">
              <el-table-column prop="brownCoal" />
              <el-table-column prop="longFlame" />
              <el-table-column prop="gasCoal" />
              <el-table-column prop="thirdCokingCoal" />
              <el-table-column prop="fatCoal" />
              <el-table-column prop="cokingCoal" />
              <el-table-column prop="leanCoal" />
              <el-table-column prop="meagerLeanCoal" />
              <el-table-column prop="meagerCoal" />
              <el-table-column prop="smokelessCoal" />
            </el-table>
          </template>
        </indicators-card>
        <indicators-card v-if="details.id" title="备注">
          <el-form-item prop="remarks" class="textarea">
            <el-input v-model="entityForm.remarks" type="textarea" :rows="6" placeholder="请填写备注信息~">
            </el-input>
          </el-form-item>
        </indicators-card>
      </el-form>
      <!-- <el-dialog append-to-body title="煤种不一致" :visible.sync="isChangeCoal" width="80%" :before-close="handleClose">
        <div style="display: flex;font-size: 14px;">
          当前您选择的煤种是
          <span style="color: #A50D0F;font-size: 16px;">{{ entityForm.type }}</span>
          ,系统判断煤种是
          <span style="color: #A50D0F;font-size: 16px;">{{ type }}</span>
          ,是否修改煤种?
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="isChangeCoal = false">取 消</el-button>
          <el-button class="dialog-footer-btns" @click="makeSure('')" type="primary" :loading="entityFormLoading">直接保存</el-button>
          <el-button class="dialog-footer-btns" style="width: 126px;" type="primary" @click="makeSure(type)"
                     :loading="entityFormLoading">确定修改并保存</el-button>
        </span>
      </el-dialog> -->
    </div>
    <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
      <el-button class="dialog-footer-btns" @click="handleCloseV">
        取消
      </el-button>
      <el-button class="dialog-footer-btns" :loading="entityFormLoading" type="primary" @click="save"
                 style="width:60px">保存</el-button>
    </div>
  </el-dialog>

</template>

<script>
import Func from '@/utils/func'
import { dateFormat } from '@/filters'
import NewColumnChart from '@/components/Chart/NewColumnChart'
import { IndicatorsCard, IndicatorsTable } from '@/components/Indicators/index'
import { getDetail, coalAshList, getRangeListApi, updateStock, refreshIndicators, refreshPrice } from '@/api/coal'
import { string } from 'jszip/lib/support'
import { deepClone } from '@/utils/index'
import Handsontable from 'handsontable'
import { forEach } from 'jszip/lib/object'
const entityForm = {
  id: '',
  userId: '',
  name: '',
  // Basicinformation: [],
  // indexlist:[],
  batchCode: dateFormat(new Date(), 'yyyymmdd'),
  type: '',
  province: '',
  factoryPrice: '',
  transitFee: '',
  roadCost: '',
  arrivePrice: '',
  arriveFactory: 'JT',
  mineDepth: '',
  rawMt: '',
  rawAd: '',
  rawPointFive: '',
  rawOnePointFour: '',
  rawAdIn: '',
  rawStd: '',
  rawVdaf: '',
  rawG: '',
  cleanVdaf: '',
  cleanVd: '',
  cleanAd: '',
  cleanStd: '',
  cleanMt: '',
  cleanP: '',
  cleanMci: '',
  procG: '',
  procY: '',
  procX: '',
  procMf: '',
  procTp: '',
  procTmax: '',
  procTk: '',
  procCrc: '',
  procA: '',
  procB: '',
  macR0: '',
  macS: '',
  macV: '',
  macI: '',
  macE: '',
  comSiO2: '',
  comAl2O3: '',
  comFe2O3: '',
  comCaO: '',
  comMgO: '',
  comNa2O: '',
  comK2O: '',
  comTiO2: '',
  comP2O5: '',
  comSO3: '',
  cfeCp: '',
  cfeCe: '',
  qualScon: '',
  qualPcon: '',
  qualM40: '',
  qualM10: '',
  qualCsr: '',
  qualCri: '',
  qualTestCond: '',
  crGk: '',
  crBk: '',
  crTk: '',
  city: '',
  dataType: '',
  createBy: '',
  createDate: '',
  updateBy: '',
  updateDate: '',
  remarks: '',
  ext: '',
  location: '',
  area: '',
  longitude: '',
  latitude: '',
  isFavorite: '-',
  rawPrice: '',
  coalTypeProportionList: []
}
export default {
  name: 'coal',
  components: {
    IndicatorsCard,
    IndicatorsTable,
    NewColumnChart
  },
  data() {
    return {
      entityForm: { ...entityForm },
      entityFormLoading: false,
      // isChangeCoal: false,
      type: '',
      visible: false,
      uploadList: [],
      uploadList1: [],
      editData: [
        ...Array(1)
          .fill(null)
          .map(() => ({}))
      ],
      editData1: [
        ...Array(10)
          .fill(null)
          .map(() => ({}))
      ]

      // dialogStatus: 'watch'
    }
  },
  computed: {
    region: {
      set(val) {
        this.entityForm = Object.assign(this.entityForm, {
          province: val[0],
          city: val[1],
          area: val[2]
        })
      },
      get() {
        return [this.entityForm.province, this.entityForm.city, this.entityForm.area]
      }
    },
    getColumnsInfo() {
      const dialogStatus = this.dialogStatus
      const len = this.entityForm.Basicinformation.length
      const watchArr =
        dialogStatus === 'watch'
          ? [
              {
                data: null,
                title: '',
                width: 30,
                //只读
                readOnly: true,
                renderer: function (instance, td, row, col, prop, value, cellProperties) {
                  Handsontable.renderers.TextRenderer.apply(this, arguments)
                  const index = row + 1
                  td.style.textAlign = 'center'
                  // td.innerHTML = index == len ? '平均' : index
                }
              }
            ]
          : []
      return [
        ...watchArr,
        {
          title: '名称',
          width: 60,
          data: 'name',
          type: 'text'
        },
        {
          title: '矿井名称',
          width: 60,
          data: 'mineName',
          type: 'text'
        },
        {
          title: '库存价',
          width: 60,
          data: 'price',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        }
      ]
    },

    getColumns() {
      const dialogStatus = this.dialogStatus
      const len = this.entityForm.indexlist.length
      const watchArr =
        dialogStatus === 'watch'
          ? [
              {
                data: null,
                title: '',
                width: 30,
                //只读
                readOnly: true,
                renderer: function (instance, td, row, col, prop, value, cellProperties) {
                  Handsontable.renderers.TextRenderer.apply(this, arguments)
                  const index = row + 1
                  td.style.textAlign = 'center'
                  // td.innerHTML = index == len ? '平均' : index
                }
              }
            ]
          : []
      return [
        ...watchArr,
        {
          title: 'Ad',
          width: 60,
          data: 'ad',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: 'St,d',
          width: 60,
          data: 'std',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },

        {
          title: 'Vdaf',
          width: 60,
          data: 'vdaf',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },

        {
          title: '焦渣',
          width: 60,
          data: 'crc',
          type: 'numeric',
          numericFormat: {
            pattern: '0'
          }
        },
        {
          title: 'G',
          width: 60,
          data: 'g',
          type: 'numeric',
          numericFormat: {
            pattern: '0'
          }
        },
        {
          title: 'Y',
          width: 60,
          data: 'y',
          type: 'numeric',
          numericFormat: {
            pattern: '0.0'
          }
        },
        {
          title: 'Mt',
          width: 60,
          data: 'mt',
          type: 'numeric',
          numericFormat: {
            pattern: '0.0'
          }
        },
        {
          title: '反射率',
          width: 60,
          data: 'macR0',
          type: 'numeric',
          numericFormat: {
            pattern: '0.0'
          }
        },
        {
          title: '标准差',
          width: 60,
          data: 'macS',
          type: 'numeric',
          numericFormat: {
            pattern: '0.0'
          }
        },
        {
          title: '回收',
          width: 60,
          data: 'recovery',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        }
      ]
    },

    getColumns1() {
      const createNames = (index) => {
        const res = {
          0: 'Rran范围',
          1: '频率%',
          2: 'Rran范围',
          3: '频率%',
          4: 'Rran范围',
          5: '频率%',
          6: 'Rran范围',
          7: '频率%',
          8: 'Rran范围',
          9: '频率%'
        }
        return res[index] || 'default'
      }
      return [
        ...Array(10)
          .fill(null)
          .map((val, index) => {
            // index 是偶数
            const dataProp = index + 1
            if (index % 2 == 1) {
              return {
                width: 80,
                title: createNames(index),
                data: dataProp,
                type: 'numeric',
                numericFormat: {
                  pattern: '0.00'
                }
              }
            }
            return {
              width: 90,
              title: createNames(index),
              data: dataProp,
              // 禁用列
              readOnly: true
            }
          })
      ]
    }
  },
  props: {
    details: {
      type: Object,
      default() {
        return {}
      }
    },
    addVisible: {
      type: Boolean,
      default: false
    },
    isSourceWash: {
      type: Boolean,
      default: false
    },
    dialogStatus: {
      type: String,
      default: 'update'
    }
  },
  created() {
    // this.getCreateData()
  },
  methods: {
    async getCreateData(obj = {}) {
      // console.log(obj)
      try {
        let data = []
        const result = [
          // {
          //   1: 'Rran范围',
          //   2: '频率%',
          //   3: 'Rran范围',
          //   4: '频率%',
          //   5: 'Rran范围',
          //   6: '频率%',
          //   7: 'Rran范围',
          //   8: '频率%',
          //   9: 'Rran范围',
          //   10: '频率%'
          // }
        ]
        if (obj.list) {
          data = obj.list
        } else {
          const res = await Func.fetch(getRangeListApi)
          data = res.data
        }
        // console.log(data)

        function chunkArray(arr, chunkSize) {
          const chunkedArray = []
          for (let i = 0; i < arr.length; i += chunkSize) {
            chunkedArray.push(arr.slice(i, i + chunkSize))
          }
          return chunkedArray
        }

        chunkArray(data, 5).forEach((list) => {
          // console.log(list)
          const obj = {}
          let i = 1
          list.forEach((v) => {
            obj[i] = v.rangeName
            obj[i + 1] = v.proportion
            i += 2
          })
          // console.log(obj)
          result.push(obj)
        })
        this.editData1 = [...result]
        this.$refs.editTable1.getHotInstance().loadData([...result])
        // console.log('9999')
        // console.log(this.editData1)
      } catch (e) {
        console.log(e, 'e')
      }
    },
    async handleEditTableInit1(instance) {
      const getSetting = () => {
        if (this.dialogStatus === 'watch') {
          return {
            readOnly: true,
            contextMenu: false,
            data: this.editData1
          }
        }
        return {
          contextMenu: false,
          data: this.editData1,
          afterChange: (changes, source) => {
            changes?.forEach(([row, prop, oldValue, newValue]) => {
              var total = 0
              this.editData1.forEach((value, index) => {
                if (index !== 0) {
                  for (var i in value) {
                    if (i % 2 == 0) {
                      total += value[i]
                    }
                  }
                }
              })
              // changes.forEach((value) => {
              //   total += value[3]
              // })
              // Some logic...
              // if (row !== 0) {
              //   console.log(prop)
              //   this.$refs.editTable1.getHotInstance().setDataAtCell(0, 7, total)
              // }
            })
          }
        }
      }
      instance.updateSettings({
        ...getSetting(),
        async cells(row, col) {
          if (row === 0) {
            this.numericFormat = {
              pattern: '0.00'
            }
            // if (col !== 7) {
            //   this.numericFormat = {
            //     pattern: '0.000'
            //   }
            // } else {
            //   this.numericFormat = {
            //     pattern: '0.00'
            //   }
            // }
          }
        }
      })
    },

    // 指标表格
    async handleEditTableInit(instance) {
      const getSetting = () => {
        if (this.dialogStatus === 'watch') {
          return {
            readOnly: true,
            // height: '350',
            contextMenu: undefined,
            data: this.entityForm.indexlist,
            rowHeaders: false
          }
        }
        return {
          contextMenu: {
            items: {
              // row_above: { name: '向上插入一行' },
              // row_below: { name: '向下插入一行' },
              // remove_row: { name: '删除行' },
              // clear_custom: {
              //   name: '清空所有单元格数据',
              //   callback() {
              //     this.clear()
              //   }
              // }
            }
          },
          data: this.entityForm.indexlist
        }
      }
      instance.updateSettings({
        columns: this.getColumns,
        ...getSetting(),
        async cells(row, col) {}
      })
    },

    //  基本信息表格
    async handleEditTableInitInfo(instance) {
      const getSetting = () => {
        if (this.dialogStatus === 'watch') {
          return {
            readOnly: true,
            contextMenu: undefined,
            data: this.entityForm.Basicinformation,
            rowHeaders: false
          }
        }
        return {
          contextMenu: {
            items: {}
          },
          data: this.entityForm.Basicinformation
        }
      }
      instance.updateSettings({
        columns: this.getColumnsInfo,
        ...getSetting(),
        async cells(row, col) {}
      })
    },

    async handleRemoveUpload(index, type) {
      if (type == 'uploadList') {
        const { id } = this.uploadList[index]
        this.$refs.upload.attachmentDelete({ id, index })
      }
      if (type == 'uploadList1') {
        const { id } = this.uploadList1[index]
        this.$refs.upload1.attachmentDelete({ id, index })
      }
    },

    handleUploadSuccess(res, type) {
      if (type == 'uploadList') {
        this.entityForm.attachmentList = []
        this.uploadList = [...this.uploadList, res]
        // 上传完毕后更新展示的列表和把新增的附件id塞入到
        this.entityForm.attachmentList.push({ id: res.id })
      }
      if (type == 'uploadList1') {
        this.entityForm.attachmentCsrList = []
        this.uploadList1 = [...this.uploadList1, res]
        // 上传完毕后更新展示的列表和把新增的附件id塞入到
        this.entityForm.attachmentCsrList.push({ id: res.id })
      }
    },
    handleUploadDelete({ status, index }, type) {
      if (type == 'uploadList') {
        this.uploadList.splice(index, 1)
      }

      if (type == 'uploadList1') {
        this.uploadList1.splice(index, 1)
      }
    },

    async getRangerList() {
      const res = await Func.fetch(getRangeListApi)
      let rangeDtoList = []
      res.data.forEach((item) => {
        let range = {
          rangeName: '',
          proportion: '',
          sort: '',
          begin: '',
          end: ''
        }
        range.rangeName = item.rangeName
        range.sort = item.sort
        range.proportion = item.proportion
        range.begin = item.begin
        range.end = item.end
        rangeDtoList.push(range)
      })

      await this.getCreateData({
        list: rangeDtoList
      })

      this.entityForm = { ...entityForm, rangeDtoList, indexlist, Basicinformation }
      // this.entityForm = { ...entityForm, rangeDtoList }
    },
    handleCloseV() {
      this.addVisible = false
    },
    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },
    /**
     * 过滤掉空数据，校验数据
     * @param target 提供的车牌列表
     * @param isSubmit 是否提交 主要用于控制是否校验车牌号 true时抛出Error
     */
    getFormatEditData(target = [], columns, require = true) {
      const getExcludeDirtyList = (target = []) => {
        const list = deepClone(target)
        list.forEach((v) => {
          for (const key in v) {
            if ([null, ''].includes(v[key])) {
              delete v[key]
            }
          }
        })
        return list
      }
      const list = getExcludeDirtyList(target)
      const data = []
      // 获取需要校验的字段
      const validateList = columns
        .filter((col) => col.formRequire)
        .map((v) => {
          return { key: v.data, msg: v.title }
        })

      list.forEach((item, index) => {
        // 过滤掉空数据 但在编辑的时候会保留id所以不能被删除只能修改当前这条数据
        if (item && Object.keys(item).length) {
          validateList.forEach((v) => {
            if (!item[v.key]) {
              this.$message(`表格第${index + 1}行${v.msg}不能为空`)
              throw new Error('数据不能为空')
            }
          })
          data.push(item)
        }
      })

      if (!data.length && require) {
        this.$message({ message: '表格至少添加一条数据', type: 'warning' })
        throw new Error('数据不能为空')
      }

      return data
    },

    // 保存数据
    async save() {
      const valid = await this.$refs.entityForm.validate()
      if (valid) {
        this.entityFormLoading = true
        const status = await this.$refs['editTable'].validate()
        if (!status) return
        const formatEditData1 = () => {
          console.log(this.editData1)
          console.log(this.getColumns1)
          const [...rest] = this.getFormatEditData(this.editData1, this.getColumns1, true)
          console.log('rest')
          console.log(rest)
          let list = []
          rest.forEach((item) => {
            list.push({ rangeName: item[1], proportion: item[2] })
            list.push({ rangeName: item[3], proportion: item[4] })
            list.push({ rangeName: item[5], proportion: item[6] })
            list.push({ rangeName: item[7], proportion: item[8] })
            list.push({ rangeName: item[9], proportion: item[10] })
          })
          list = list.filter((item) => item.rangeName)
          return {
            // rRan: first[2],
            // macR0: first[4],
            // macS: first[6],
            rangeDtoList: list
          }
        }
        formatEditData1()
        this.entityForm.Basicinformation.forEach((item) => {
          this.entityForm.name = item.name
          this.entityForm.mineName = item.mineName
          this.entityForm.price = item.price
        })
        this.entityForm.indexlist.forEach((item) => {
          this.entityForm.ad = item.ad
          this.entityForm.std = item.std
          this.entityForm.vdaf = item.vdaf
          this.entityForm.crc = item.crc
          this.entityForm.g = item.g
          this.entityForm.y = item.y
          this.entityForm.mt = item.mt
          this.entityForm.macR0 = item.macR0
          this.entityForm.macS = item.macS
          this.entityForm.recovery = item.recovery
        })
        const form = {
          ...this.entityForm,
          ...formatEditData1()
        }
        const res = await Func.fetch(updateStock, form)
        if (res.data) {
          this.entityFormLoading = false
          // if (res.data.name !== this.entityForm.type) {
          //   this.isChangeCoal = true
          //   this.type = res.data.name
          // } else {
          //  基础信息
          let ting = this.entityForm.Basicinformation
          for (var i = 0; i < ting.length; i++) {
            this.entityForm.name = ting[i].name
            this.entityForm.mineName = ting[i].mineName
            this.entityForm.price = ting[i].price
          }
          // 指标信息
          let Things = this.entityForm.indexlist
          for (var i = 0; i < Things.length; i++) {
            this.entityForm.ad = Things[i].ad
            this.entityForm.std = Things[i].std
            this.entityForm.vdaf = Things[i].vdaf
            this.entityForm.crc = Things[i].crc
            this.entityForm.g = Things[i].g
            this.entityForm.y = Things[i].y
            this.entityForm.mt = Things[i].mt
            this.entityForm.macR0 = Things[i].macR0
            this.entityForm.macS = Things[i].macS
            this.entityForm.recovery = Things[i].recovery
          }
          // console.log(this.entityForm)
          this.$message({ showClose: true, message: '提交成功', type: 'success' })
          this.closeDialog('full', true)
        } else {
          this.entityFormLoading = false
        }
      }
    },
    // async makeSure(coalType) {
    //   const { arriveFactory } = this.$refs.entityForm

    //   //  基础信息
    //   let ting = this.entityForm.Basicinformation
    //   for (var i = 0; i < ting.length; i++) {
    //     this.entityForm.name = ting[i].name
    //     this.entityForm.mineName = ting[i].mineName
    //     this.entityForm.price = ting[i].price
    //   }
    //   // 指标信息
    //   let Things = this.entityForm.indexlist
    //   for (var i = 0; i < Things.length; i++) {
    //     this.entityForm.ad = Things[i].ad
    //     this.entityForm.std = Things[i].std
    //     this.entityForm.vdaf = Things[i].vdaf
    //     this.entityForm.crc = Things[i].crc
    //     this.entityForm.g = Things[i].g
    //     this.entityForm.y = Things[i].y
    //     this.entityForm.mt = Things[i].mt
    //     this.entityForm.macR0 = Things[i].macR0
    //     this.entityForm.macS = Things[i].macS
    //     this.entityForm.recovery = Things[i].recovery
    //   }
    //   console.log(this.entityForm)

    //   this.entityForm.type = coalType || this.entityForm.type
    //   this.entityFormLoading = true
    //   const entityFormData = { ...this.entityForm, dataType: this.datatype, arriveFactory }
    //   const saveRes = await Func.fetch(saveCoal, entityFormData)
    //   this.entityFormLoading = false
    //   if (saveRes.data) {
    //     this.$message({ showClose: true, message: '提交成功', type: 'success' })
    //     // this.$router.back()
    //     this.closeDialog('full', true)
    //   }
    // },
    closeDialog(type = false) {
      this.dialogFormVisible = false
      this.chartVisible = false
      this.entityForm = { ...entityForm }
      this.isChangeCoal = false
      this.handleClose('full', type)
      // this.$refs.entityForm.$children[0].resetFields()
    },
    async getEntityForm() {
      await this.$nextTick()
      const { id } = this.details
      try {
        const res = await getDetail({ id })
        if (res.data) {
          let rate = 0
          res.data.rangeDtoList.map((item) => {
            if (item.proportion) {
              rate = Number(item.proportion) + rate
            }
          })
          rate = rate.toFixed(1)

          let Basicinformation = []
          let obj = {
            name: res.data.name,
            mineName: res.data.mineName,
            price: res.data.price
          }
          Basicinformation.push(obj)
          res.data.Basicinformation = Basicinformation

          let indexlist = []
          let objv = {
            ad: res.data.ad,
            std: res.data.std,
            vdaf: res.data.vdaf,
            crc: res.data.crc,
            g: res.data.g,
            y: res.data.y,
            mt: res.data.mt,
            macR0: res.data.macR0,
            macS: res.data.macS,
            recovery: res.data.recovery
          }
          indexlist.push(objv)
          res.data.indexlist = indexlist
          this.uploadList1 = res.data.attachmentCsrList
          this.uploadList = res.data.attachmentList

          this.entityForm = {
            ...res.data,
            rate
          }

          await this.getCreateData({
            list: res.data.rangeDtoList
          })

          // console.log(this.entityForm)
        }
      } catch (error) {}
    },

    handleActive(e) {
      // this.entityForm.activeInertiaRatio = (e / (100 - e)).toFixed(2)
      this.entityForm = { ...this.entityForm, activeInertiaRatio: (e / (100 - e)).toFixed(2) }
    },

    handleVdaf(e) {
      if (this.entityForm.cleanAd) {
        this.entityForm = { ...this.entityForm, cleanVd: ((e * (100 - this.entityForm.cleanAd)) / 100).toFixed(2) }
        // this.entityForm.cleanVd = ((e * (100 - this.entityForm.cleanAd)) / 100).toFixed(2)
      } else {
        this.entityForm = { ...this.entityForm, cleanVd: 0 }
        // this.entityForm.cleanVd = 0
      }
    },

    handleAd(e) {
      if (this.entityForm.cleanVdaf) {
        // this.entityForm.cleanVd = ((this.entityForm.cleanVdaf * (100 - e)) / 100).toFixed(2)
        this.entityForm = { ...this.entityForm, cleanVd: ((this.entityForm.cleanVdaf * (100 - e)) / 100).toFixed(2) }
      } else {
        // this.entityForm.cleanVd = this.entityForm.cleanVdaf
        this.entityForm = { ...this.entityForm, cleanVd: 0 }
      }
    },
    handleVd(e) {
      if (this.entityForm.cleanAd) {
        // this.entityForm.cleanVdaf = ((100 * e) / (100 - this.entityForm.cleanAd)).toFixed(2)
        this.entityForm = { ...this.entityForm, cleanVdaf: ((100 * e) / (100 - this.entityForm.cleanAd)).toFixed(2) }
      } else {
        // this.entityForm.cleanVdaf = e
        this.entityForm = { ...this.entityForm, cleanVdaf: 0 }
      }
    },
    // changeRate() {
    //   this.entityForm.rate = 0
    //   this.entityForm.coalRockList.map((item) => {
    //     if (item.proportion) {
    //       this.entityForm.rate = Number(item.proportion) + this.entityForm.rate
    //     }
    //   })
    // },
    /*
     * 数值范围验证器
     * */
    RangerValidate(rules, value, cb) {
      if (+value < 0) {
        cb(new Error('数值不能小于0'))
      } else if (+value > 100) {
        cb(new Error('数值不能大于100'))
      } else {
        cb()
      }
    },
    handleClose(type = '', isSave = false) {
      if (type === 'full') {
        this.dialogFormVisible = false
        this.chartVisible = false
        this.entityForm = { ...entityForm }
        this.isChangeCoal = false
        this.$emit('closeVisible', { isSave: isSave })
      } else {
        this.dialogVisible = false
        this.position = {}
        this.isChangeCoal = false
      }
    },
    handleSubmitPosition() {
      if (Object.keys(this.position).length === 0) {
        return
      }
      this.$set(this.entityForm, 'longitude', this.position.lng)
      this.$set(this.entityForm, 'latitude', this.position.lat)
      this.handleClose()
    },
    /**
     * 获取煤灰成份
     */

    async handleCoalAsh() {
      const location = { filter_EQS_area: this.entityForm.area }
      const res = await Func.fetch(coalAshList, location)
      if (res.data.records.length > 0) {
        delete res.data.records[0].id
        // this.$emit('update:entityForm', { ...this.entityForm, ...res.data.records[0] })
        this.entityForm = { ...this.entityForm, ...res.data.records[0] }
      } else {
        this.$message({
          showClose: true,
          message: '无灰成分参考数据，请化验',
          type: 'error'
        })
      }
    },
    /**
     * 关闭煤灰成分弹框
     * @param done
     */
    handleCoalClose(done) {
      done()
    },
    async handleRefreshindex(id) {
      //刷新指标
      this.$confirm('是否确认刷新指标', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await Func.fetch(refreshIndicators, { id: id })
          if (res) {
            this.$message({ type: 'success', message: '刷新成功!' })
            this.getEntityForm()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '刷新取消' }))
    },
    async handleRefreshPrice(id) {
      //刷新库存价
      this.$confirm('是否确认刷新库存价', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await Func.fetch(refreshPrice, { id: id })
          if (res) {
            this.$message({ type: 'success', message: '刷新成功!' })
            this.getEntityForm()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '刷新取消' }))
    }
  },
  watch: {
    addVisible(v) {
      // console.log(v)
      this.visible = v
    },
    'details.id'(v) {
      if (v) {
        this.getEntityForm()
      } else {
        if (this.dialogStatus === 'create') {
          this.getRangerList()
        }
      }
    },

    details: {
      handler(value) {
        // console.log(value, 'v')
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style scoped lang='scss' >
@import '@/styles/router-page.scss';
.filter-itembtn {
  border: 1px solid #ff9639 !important;
  background: #ff9639 !important;
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;

  .upload-list {
    flex: 1;
    display: flex;
    flex-flow: wrap;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      margin-bottom: 10px;
      width: 75px;
      height: 75px;
      position: relative;

      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }

      .img {
        border-radius: 4px;
        // border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}

.add {
  ::v-deep .el-dialog__body {
    padding: 0 35px;
    height: 80vh;
  }
}

::v-deep .el-form-item__label {
  font-weight: 400;
}

::v-deep .indicators .title {
  display: block;
}

.textarea {
  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }
}

.title-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title_ad {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-col-6 {
  display: flex;
  justify-content: flex-start;
}

.saveBtn {
  display: flex;
  justify-content: flex-end;
}

.dictSelected {
  max-width: 100%;

  ::v-deep .el-input__inner {
    max-width: 100%;
  }
}

.chart-table {
  width: 80%;
  margin: 0 auto;
  margin-bottom: 10px;
  // display: flex;
  // justify-content: center;
}
</style>
