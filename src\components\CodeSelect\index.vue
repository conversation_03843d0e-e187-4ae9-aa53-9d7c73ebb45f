<template>
  <el-autocomplete class="inline-input" v-model="val" :fetch-suggestions="gainRemoteData" placeholder="" clearable
                   :disabled="disabled" @input="handleInputChange" @blur="handleChange">
  </el-autocomplete>
</template>
<script>

// import { coalType } from '@/api/public'

import { findByInnerCode } from '@/api/stock'
export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '', // 映射值
      list: []
    }
  },
  props: {
    value: {
      required: true
    },
    ext: {
      type: Object
    },
    type: {
      type: String,
      default: 'COAL_TYPE',
      required: false
    },
    disabled: {
      type: [Boolean],
      default: false
    }
  },
  async created() {
    const res = await findByInnerCode()
    const gather = res.data.map(v => {
      return { value: v.code, id: v.id }
    })
    this.list = gather
  },
  methods: {
    createFilter(query) {
      return (restaurant) => {
        return (restaurant.value.indexOf(query) === 0)
      }
    },
    async gainRemoteData(query = '', cb) {
      const results = query ? this.list.filter(this.createFilter(query)) : this.list
      cb(results)
    },
    handleInputChange(val) {
      this.$emit('update:value', val)
    },
    handleChange(v) {
      this.$emit('blur', v)
    }
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true
    }

  }
}
</script>
<style lang="scss" scoped>
.inline-input {
  width: 100%;
}
</style>
