<template>
  <div :class="className" :style="{ height, width }"/>
</template>

<script>
import echarts from 'echarts'
import resize from './resize'
import _ from 'lodash'

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Array,
      default: () => [],
      required: true
    },
    option: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: String,
      default: 'bar'
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    autoResize: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.$nextTick(() => {
          this.setOptions(val)
          this.chart.resize()
        })
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) return
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.setOptions(this.chartData)
    },
    setOptions(chartData) {
      let option = {
        legend: {
          bottom: '0'
        },
        grid: {
          left: 10,
          right: 10,
          bottom: 40,
          top: 30,
          containLabel: true
        },
        xAxis: {
          axisLine: {
            // 坐标轴轴线相关设置
            lineStyle: {
              // 坐标轴线线的颜色
              color: '#D9D7D7', // 坐标轴线的颜色修改--文字也同步修改
              type: 'dashed'
            }
          },
          axisTick: {
            // 坐标轴刻度相关设置
            alignWithLabel: true // 可以保证刻度线和标签对齐
          },
          axisLabel: {
            color: '#9B9B9B'
          },

          type: 'category',
          data: []
        },
        yAxis: {
          splitLine: {
            // 颜色
            lineStyle: {
              color: '#E3F0FF',
              type: 'dashed'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#D9D7D7',
              type: 'dashed'
            }
          },
          axisTick: {
            alignWithLabel: true
          },
          axisLabel: {
            color: '#9B9B9B'
          },
          type: 'value'
        },
        series: []
      }

      const initCharts = () => {
        if (this.option) {
          _.merge(option, this.option)
        }
        // 图例类型
        let typeArr = Array.from(new Set(this.chartData.map((item) => item.type)))
        // 轴数据
        let xAxisData = Array.from(new Set(this.chartData.map((item) => item.name)))
        let seriesData = []
        typeArr.forEach((type) => {
          let obj = {
            name: type,
            type: this.type,
            ...(this.option.seriesConfig || {})
          }
          let chartArr = this.chartData.filter((item) => type === item.type)
          // data数据
          obj['data'] = chartArr.map((item) => {
            return {
              ...item,
              value: item.value
            }
          })
          obj = {
            ...obj,
            ...(this.option.seriesFunc ? this.option.seriesFunc(obj, option) : {})
          }
          if (obj.name) {
            seriesData.push(obj)
          }
        })

        option.series = seriesData

        option.xAxis.data = xAxisData
      }

      initCharts()
      this.chart.setOption(option, true)
    }
  }
}
</script>
