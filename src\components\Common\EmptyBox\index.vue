<template>
  <div class="empty-container">
    <div class="default-content" :style="{ width, height }" v-show="!showEmpty">
      <slot />
    </div>
    <el-empty :style="{ width, height }" :description="text" v-show="showEmpty">
      <template #image>
        <slot name="empty-image"></slot>
      </template>
    </el-empty>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    text: {
      type: String,
      default: '暂无数据'
    },
    showEmpty: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {}
  },

  components: {}
}
</script>

<style scoped lang="scss">
.empty-container {
  width: 100%;
  height: 100%;
}
</style>
