<template>
    <div class="common--api-autocomplete">
        <el-autocomplete
                v-model="val"
                :clearable="clearable"
                :fetch-suggestions="querySearch"
                :value-key="props.value"
                class="input"
                v-bind="$attrs"
                @select="handleSelect"
                v-on="$listeners"
        >
            <template #default="{ item }">
                <slot v-bind="{ item }"/>
            </template>
        </el-autocomplete>
    </div>
</template>

<script>
import * as Api from '@/api/common/listAllSimple'
import {isUnDef} from '@/utils/is'

export default {
    name: 'SnApiAutocomplete',
    inheritAttrs: false,
    data() {
        return {
            loading: false,
            list: []
        }
    },
    props: {
        field: {
            type: [String, Number]
        },
        value: {
            type: String,
            require: true
        },
        apiName: {
            type: String,
            require: true
        },
        props: {
            type: Object,
            default: () => ({
                value: 'name',
                emitField: 'id'
            })
        },
        fetchedData: {
            type: Function
        },
        clearable: {
            type: Boolean,
            default: true
        }
    },
    computed: {
        val: {
            set(v) {
                this.$emit(`update:field`, '')
                this.$emit('input', v)
            },
            get() {
                return this.value
            }
        }
    },
    watch: {
        apiName: {
            handler(name) {
                this.getListSimple(name)
            },
            immediate: true
        }
    },
    methods: {
        async getListSimple(apiName, params = {}) {
            try {
                if (isUnDef(Api[apiName])) {
                    throw new Error('传递的ApiName和ApiModel中没匹配到')
                }
                this.loading = true
                const res = await Api[apiName](params)
                if (res) {
                    this.list = this.fetchedData ? this.fetchedData(res.data) : res.data
                }
            } catch (error) {
                console.error(error)
            } finally {
                this.loading = false
            }
        },
        querySearch(queryString, cb) {
            const createFilter = (queryString) => {
                return (item) => {
                    return item[this.props.value].toLowerCase().includes(queryString.toLowerCase())
                }
            }
            const list = this.list
            const results = queryString ? list.filter(createFilter(queryString)) : list
            cb(results)
        },
        handleSelect(val) {
            this.$emit('select', val)
            this.$emit(`update:field`, val[this.props.emitField])
        }
    },
    components: {}
}
</script>

<style lang="scss">
.common--api-autocomplete {
  width: 100%;

  .input {
    width: 100%;
  }
}
</style>
