<template>
  <el-select
    v-model="val"
    :filterable="filterable"
    :loading="loading"
    :multiple="multiple"
    :remote="!!remoteSearchKey"
    :remote-method="remoteMethod"
    class="common-api-page-select"
    popper-class="page-down-scrollbar"
    v-bind="$attrs"
    @change="handleChange"
    @visible-change="handleVisibleChange"
  >
    <el-option v-for="item in list" :key="item[props.key || props.value]" :label="item[props.label]" :value="item[props.value]">
      <slot v-bind="item"></slot>
    </el-option>
    <Pagination
      :limit.sync="pagination.pageSize"
      :page.sync="pagination.pageNo"
      class="pagination"
      v-bind="pagination"
      @pagination="getDataList"
    />
  </el-select>
</template>

<script>
import * as Api from '@/api/common/pageAll'
import { isUnDef } from '@/utils/is'
import Pagination from '@/components/Pagination'
import { fetchSetting } from '@/const'

const createPagination = () => ({
  small: true,
  background: false,
  pagerCount: 5,
  [fetchSetting.totalField]: 0,
  [fetchSetting.pageField]: 1,
  pageSize: 100,
  layout: 'prev, pager, next' // total,
})

export default {
  name: 'ApiPageSelect',
  inheritAttrs: false,
  components: { Pagination },
  data() {
    return {
      loading: false,
      list: [],
      pagination: {
        ...createPagination()
      }
    }
  },
  computed: {
    val: {
      set(v) {
        this.$emit('input', v)
      },
      get() {
        const value = this.value
        if (Array.isArray(value)) {
          return value.map((item) => {
            return typeof item === 'number' ? item + '' : item
          })
        }
        return typeof this.value === 'number' ? value + '' : value
      }
    }
  },
  props: {
    value: {
      type: [String, Number, Array],
      require: true
    },
    apiName: {
      type: [String, Function],
      require: true
    },
    remoteSearchKey: {
      type: String
    },
    defaultConfig: {
      type: Object,
      default: () => ({})
    },
    filterable: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => ({})
    },
    props: {
      type: Object,
      default: () => ({
        value: 'id',
        label: 'name'
      })
    },
    fetchedData: {
      type: Function
    }
  },
  watch: {
    params: {
      handler(val) {
        this.getDataList()
      },
      immediate: true
    }
  },
  methods: {
    async getDataList(params = {}, callback = this.fetchedData) {
      await this.$nextTick()
      const { apiName, params: propsParams } = this
      try {
        this.loading = true
        const requestParams = { ...propsParams, ...params, ...this.pagination }
        let res = typeof apiName === 'function' ? await apiName(requestParams) : await Api[apiName](requestParams)
        res.data[fetchSetting.listField].forEach((item) => {
          item[this.props.value] = item[this.props.value] + ''
        })

        const result = callback ? callback(res.data) : res.data
        this.pagination = {
          ...this.pagination,
          [fetchSetting.totalField]: result[fetchSetting.totalField]
        }
        this.list = result[fetchSetting.listField]
      } catch (error) {
        console.error(error, 'getDataList')
      } finally {
        this.loading = false
      }
    },

    async remoteMethod(keyword) {
      try {
        const params = { [this.remoteSearchKey]: keyword }
        this.getDataList(params)
      } catch (error) {
        console.error(error, 'remoteMethod')
      }
    },

    handleChange(val) {
      if (!this.multiple) {
        const target = this.list.find((item) => item[this.props.value] === val) || {}
        this.$emit('select', { val, item: target })
      } else {
        const target = this.list.filter((item) => val.includes(item[this.props.value])) || []
        this.$emit('select', { val, item: target })
      }
    },
    handleVisibleChange(flag) {
      if (flag) {
        this.getDataList()
      } else {
        this.pagination = { ...createPagination() }
      }
      this.$emit('visible-change', flag)
    }
  }
}
</script>
<style lang="scss">
.page-down-scrollbar {
  .el-select-dropdown__item {
    &:last-of-type {
      margin-bottom: 30px;
    }
  }
}
</style>

<style lang="scss" scoped>
.common-api-page-select {
  width: 100%;
}

.pagination {
  margin-bottom: 0px;
  margin-top: 0;
  padding: 0px 20px !important;
  position: absolute;
  z-index: 99;
  bottom: 0;
  width: 100%;
  height: 35px;
  display: flex;
  align-items: center;

  ::v-deep .el-pagination--small .btn-next {
    width: 23px !important;
  }
}
</style>
