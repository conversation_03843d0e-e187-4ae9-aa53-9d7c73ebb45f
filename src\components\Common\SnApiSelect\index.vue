<template>
    <div class="ApiSelectContainer">
        <el-select
                v-if="isSelect"
                v-model="val"
                :clearable="clearable"
                :filterable="filterable"
                :loading="loading"
                :multiple="multiple"
                :remote-method="remoteMethod"
                class="common-api-select"
                v-bind="$attrs"
                @change="handleChange"
                @visible-change="handleVisibleChange"
        >
            <el-option v-for="item in list" :key="item[props.key || props.value]" :label="item[props.label]"
                       :value="item[props.value]">
                <slot v-bind="item"></slot>
            </el-option>
        </el-select>

        <el-checkbox-group v-if="isCheckbox" v-model="val" class="common-api-type-checkbox" v-bind="$attrs"
                           @change="handleChange">
            <el-checkbox-button v-for="item in list" :key="item[props.key || props.value]" :label="item[props.label]">
                {{ item[props.label] }}
            </el-checkbox-button>
        </el-checkbox-group>

        <SimpleRadio v-if="isRadiobutton" v-model="val" :list="list" :props="props" @input="handleChange"/>
    </div>
</template>

<script>
import * as Api from '@/api/common/listAllSimple'
import {isUnDef} from '@/utils/is'
import _ from 'lodash'

export default {
    name: 'SnApiSelect',
    inheritAttrs: false,
    data() {
        return {
            loading: false,
            list: []
        }
    },
    components: {},
    computed: {
        isSelect() {
            return this.type === 'select'
        },
        isCheckbox() {
            return this.type === 'checkbox'
        },
        isRadio() {
            return this.type === 'radio'
        },
        isRadiobutton() {
            return this.type === 'radiobutton'
        },
        val: {
            set(v) {
                this.$emit('input', v)
            },
            get() {
                const value = this.value
                if (Array.isArray(value)) {
                    return value.map((item) => {
                        return typeof item === 'number' ? item + '' : item
                    })
                }
                return typeof this.value === 'number' ? value + '' : value
            }
        }
    },
    props: {
        value: {
            type: [String, Number, Array],
            require: true
        },
        apiName: {
            type: String,
            require: true
        },
        type: {
            type: String,
            default: 'select'
        },
        params: {
            type: Object,
            default: () => ({})
        },
        filterable: {
            type: Boolean,
            default: true
        },
        clearable: {
            type: Boolean,
            default: true
        },
        multiple: {
            type: Boolean,
            default: false
        },
        remoteKey: {
            type: String,
            default: 'keyword'
        },
        props: {
            type: Object,
            default: () => ({
                value: 'id',
                label: 'name'
            })
        },
        fetchedData: {
            type: Function
        }
    },
    watch: {
        params: {
            handler: _.debounce(async function (v) {
                this.list = await this.getListSimple({
                    apiName: this.apiName,
                    params: this.params,
                    callback: this.fetchedData
                })
            }, 500),
            deep: true,
            immediate: true
        }
    },
    methods: {
        async handleVisibleChange(val) {
            // if (val) {
            //     this.list = await this.getListSimple({
            //         apiName: this.apiName,
            //         params: this.params,
            //         callback: this.fetchedData
            //     })
            // }
        },
        async reload() {
            this.list = await this.getListSimple({
                apiName: this.apiName,
                params: this.params,
                callback: this.fetchedData
            })
        },

        async getListSimple({apiName, params = {}, callback = undefined}) {
            await this.$nextTick()
            try {
                if (isUnDef(Api[apiName])) {
                    throw new Error('传递的ApiName和ApiModel中没匹配到')
                }
                this.loading = true
                const res = await Api[apiName](params)
                res.data.forEach((item) => {
                    item[this.props.value] = item[this.props.value] + ''
                })
                return callback ? callback(res.data) : res.data
            } catch (error) {
                console.error(error)
                return []
            } finally {
                this.loading = false
            }
        },
        async remoteMethod(query) {
            try {
                const list = await this.getListSimple({
                    apiName: this.apiName,
                    params: {
                        ...this.params,
                        [this.remoteKey]: query
                    }
                })
                this.list = list
            } catch (error) {
            }
        },
        handleChange(val) {
            let target
            if (!this.multiple || this.isCheckbox) {
                target = this.list.find((item) => item[this.props.value] === val) || {}
            } else {
                target = this.list.filter((item) => val.includes(item[this.props.value])) || []
            }
            this.$emit('select', {val, target})
            // console.log(target, 'target')
            this.$emit('change', val, target)
        }
    }
}
</script>

<style lang="scss" scoped>
.ApiSelectContainer {
  width: 100%;
  height: auto;

  .common-api-select {
    width: 100%;
  }

  .common-api-type-checkbox {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .el-checkbox-button {
      margin-bottom: 5px;
    }
  }
}
</style>
