<template>
  <div class="common-date-ranger">
    <el-date-picker ref="range" v-model="val" :default-time="defaultTime"
                    :end-placeholder="endPlaceholder" :format="format"
                    :picker-options="pickerOptions" :start-placeholder="startPlaceholder"
                    :type="type" :value-format="valueFormat"
                    range-separator="-" style="width: 100%" v-bind="$attrs" @change="handleChange"
                    @toggleDateChange="handleToggleDateChange" v-on="$listeners"
                    @click.native="handleClick">
    </el-date-picker>
    <slot name="other"></slot>
  </div>
</template>

<script>
import {dateUtil} from '@/utils/dateUtils'
import dayjs from 'dayjs'

export default {
  name: 'SnDateRanger',
  data() {
    return {
      panelRanger: [],
      currentDataList: []
    }
  },
  components: {
    // SnDatePicker: DatePicker
  },
  props: {
    startPlaceholder: {
      type: String,
      default: '开始日期'
    },
    endPlaceholder: {
      type: String,
      default: '结束日期'
    },

    type: {
      type: String,
      default: 'daterange'
    },
    value: {
      type: [Array, String],
      require: true
    },
    max: {
      type: Number,
      default: 365
    },
    // value
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd HH:mm:ss'
    },
    // 显示的日期
    format: {
      type: String,
      default: 'yyyy-MM-dd'
    },
    defaultTime: {
      type: Array,
      default: () => []
    },
    apiName: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: () => {
      }
    },
    useValueField: {
      type: Boolean,
      default: true
    },

    start: {
      type: [String],
      default: undefined
    },
    end: {
      type: [String],
      default: undefined
    }
  },
  watch: {
    val: {
      handler(value) {
        if (this.apiName) {
          if (value && value.length) {
            this.setPanelRanger(value)
          } else {
            this.setPanelRanger([])
          }
        }
      },
      deep: true,
      immediate: true
    },
    params: {
      handler() {
        if (this.panelRanger.length) {
          this.getExistenceDataDays()
        }
      },
      deep: true
    },
    panelRanger(v) {
      if (v.length) {
        this.getExistenceDataDays()
      }
    }
  },

  computed: {
    val: {
      set(val) {
        if (this.useValueField) {
          return this.$emit('input', val)
        }
        const start = val ? val[0] : ''
        const end = val ? val[1] : ''
        this.$emit('update:start', start)
        this.$emit('update:end', end)
      },
      get() {
        if (this.useValueField) return this.value
        const start = this.start || ''
        const end = this.end || ''
        return [start, end]
      }
    },
    pickerOptions() {
      if (this.type === 'daterange') {
        const getRanger = this.getRanger
        const max = this.max * 100
        return {
          disabledDate(time) {
            return time.getTime() > Date.now() + 3600 * 1000 * 24 * 30 * max || time.getTime() < Date.now() - 3600 * 1000 * 24 * 15 * max
          },
          shortcuts: [
            {
              text: '今天',
              onClick(picker) {
                picker.$emit('pick', getRanger(0, true))
              }
            },
            {
              text: '昨天',
              onClick(picker) {
                picker.$emit('pick', getRanger(1, true))
              }
            },
            {
              text: '前天',
              onClick(picker) {
                picker.$emit('pick', getRanger(2, true))
              }
            },
            {
              text: '近三日',
              onClick(picker) {
                picker.$emit('pick', getRanger(2)) // 今天到前两日
              }
            },
            {
              text: '近七日',
              onClick(picker) {
                picker.$emit('pick', getRanger(6))
              }
            },
            {
              text: '近三十日',
              onClick(picker) {
                picker.$emit('pick', getRanger(29))
              }
            },
            {
              text: '近六十日',
              onClick(picker) {
                picker.$emit('pick', getRanger(59))
              }
            },
            {
              text: '近九十日',
              onClick(picker) {
                picker.$emit('pick', getRanger(89))
              }
            },
            {
              text: '近半年',
              onClick(picker) {
                picker.$emit('pick', getRanger(179))
              }
            },
            {
              text: '近一年',
              onClick(picker) {
                picker.$emit('pick', getRanger(364))
              }
            }
          ],
          cellClassName: (date) => {
            const dateStr = dayjs(date).format('YYYY-MM-DD')
            return this.currentDataList.includes(dateStr) ? 'date-current' : ''
          }
        }
      }
      return {}
    }
  },
  methods: {
    getRanger(days = 0, onlyDay = false, valueFormat = this.valueFormat) {
      const state = valueFormat.includes('HH:mm:ss')

      let startSuffix = state ? ' 00:00:00' : ''
      let endSuffix = state ? ' 23:59:59' : ''

      let normalFormat = 'yyyy-MM-DD'
      if (onlyDay) {
        const start = dateUtil().subtract(days, 'days').format(normalFormat) + startSuffix

        const end = dateUtil().subtract(days, 'days').format(normalFormat) + endSuffix

        return [start, end]
      }

      const end = dateUtil().format(normalFormat) + endSuffix

      const start = dateUtil().subtract(days, 'days').format(normalFormat) + startSuffix

      return [start, end]
    },

    showPicker() {
      this.$refs.range.showPicker()
    },

    async getExistenceDataDays() {
      this.currentDataList = []
    },
    setPanelRanger(value, format = 'YYYY-MM-DD') {
      this.panelRanger = [dayjs(value[0]).format(format), dayjs(value[1]).format(format)]
    },
    handleToggleDateChange(info) {
      const {value} = info
      this.setPanelRanger(value)
    },

    handleClick(event) {
      this.$emit('panel', event)
    },

    handleChange(val) {
      // daterange选择时endDate的后缀需要替换成23:59：59
      const state = this.valueFormat.includes('HH:mm:ss')

      // !state &&
      if (state && val && Array.isArray(val)) {
        if (this.type === 'daterange') {
          if (!val[1].includes('23:59:59')) {
            val[1] = val[1].replace('00:00:00', '23:59:59')
          }

          console.log(this.type, val, state, '1111')
        }
        if (this.type === 'monthrange') {
          val[1] = dateUtil(val[1]).endOf('month').format('YYYY-MM-DD 23:59:59')
        }
        if (this.type === 'yearrange') {
          val[1] = dateUtil(val[1]).endOf('year').format('YYYY-MM-DD 23:59:59')
        }
      }
    }
  }
}
</script>

<style lang="scss">
.date-current {
  span {
    background-color: #08b9a2;
    border-radius: 50%;
    color: #fff;
    opacity: 0.8;
    text-align: center;
    transition: all 0.3s;

    &:hover {
      opacity: 1;
    }
  }
}

.common-date-ranger {
  width: 100%;
  display: flex;
  align-content: center;
  //height: 32px !important;
}
</style>
