<template>
    <el-date-picker
            ref="dateSelect"
            v-model="pickerDate"
            :format="format"
            :type="type"
            :value-format="valueFormat"
            class="common-date-select"
            v-bind="$attrs"
            @change="handleChange"
            v-on="$listeners"
    >
    </el-date-picker>
</template>

<script>
export default {
    name: 'SnDateSelect',
    data() {
        return {
            pickerDate: ''
        }
    },
    props: {
        type: {
            type: String,
            default: 'date'
        },
        value: {
            type: [String],
            require: true
        },
        // value type
        valueFormat: {
            type: String,
            default: 'yyyy-MM-dd' // HH:mm:ss
        },
        // label text
        format: {
            type: String,
            default: 'yyyy-MM-dd'
        },
        isshowmodel: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        value: {
            handler(v) {
                this.pickerDate = v
            },
            immediate: true
        }
    },
    methods: {
        focus() {
            this.$refs.dateSelect.focus()
        },
        handleChange(val) {
            this.$emit('input', val)
            this.$emit('change', val)
            if (this.isshowmodel) {
                this.focus()
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.common-date-select {
  width: 100%;
  overflow: hidden;

  ::v-deep {
    .el-icon-date {
      display: none;
    }
  }
}
</style>
