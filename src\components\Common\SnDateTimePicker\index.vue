<template>
  <div class="common-date-ranger">
    <el-date-picker
      ref="range"
      v-model="val"
      :default-time="defaultTime"
      :end-placeholder="endPlaceholder"
      :pickerOptions="pickerOptions"
      :start-placeholder="startPlaceholder"
      :type="type"
      :value-format="valueFormat"
      range-separator="-"
      style="width: 100%"
      v-bind="$attrs"
      v-on="$listeners"
      @click.native="handleClick">
    </el-date-picker>
    <slot name="other"></slot>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import {dateUtil} from '@/utils/dateUtils'

export default {
  name: 'SnDateTimePicker',
  data() {
    return {}
  },
  components: {},
  props: {
    startPlaceholder: {
      type: String,
      default: '开始日期'
    },
    endPlaceholder: {
      type: String,
      default: '结束日期'
    },

    type: {
      type: String,
      default: 'daterange'
    },
    value: {
      type: [Array, String],
      require: true
    },
    max: {
      type: Number,
      default: 365
    },
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd HH:mm'
    },
    // 显示的日期
    format: {
      type: String,
      default: 'yyyy-MM-dd'
    },
    defaultTime: {
      type: Array
      // default: () => []
    },
    apiName: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: () => {
      }
    },
    useValueField: {
      type: Boolean,
      default: true
    },

    start: {
      type: [String],
      default: undefined
    },
    end: {
      type: [String],
      default: undefined
    },
    pickerOptionsObj: {
      type: Object,
      default: () => {
      }
    }
  },
  watch: {},
  computed: {
    pickerOptions() {
      if (this.type === 'datetimerange') {
        const getRanger = this.getRanger
        const max = this.max * 100
        return {
          disabledDate(time) {
            return time.getTime() > Date.now() + 3600 * 1000 * 24 * 30 * max || time.getTime() < Date.now() - 3600 * 1000 * 24 * 15 * max
          },
          shortcuts: [
            {
              text: '今天',
              onClick(picker) {
                picker.$emit('pick', getRanger(0, true))
              }
            },
            {
              text: '昨天',
              onClick(picker) {
                picker.$emit('pick', getRanger(1, true))
              }
            },
            {
              text: '前天',
              onClick(picker) {
                picker.$emit('pick', getRanger(2, true))
              }
            },
            {
              text: '近三日',
              onClick(picker) {
                picker.$emit('pick', getRanger(2)) // 今天到前两日
              }
            },
            {
              text: '近七日',
              onClick(picker) {
                picker.$emit('pick', getRanger(6))
              }
            },
            {
              text: '近三十日',
              onClick(picker) {
                picker.$emit('pick', getRanger(29))
              }
            },
            {
              text: '近六十日',
              onClick(picker) {
                picker.$emit('pick', getRanger(59))
              }
            },
            {
              text: '近九十日',
              onClick(picker) {
                picker.$emit('pick', getRanger(89))
              }
            },
            {
              text: '近半年',
              onClick(picker) {
                picker.$emit('pick', getRanger(179))
              }
            },
            {
              text: '近一年',
              onClick(picker) {
                picker.$emit('pick', getRanger(364))
              }
            }
          ],
          ...this.pickerOptionsObj
        }
      }
      return {}
    },
    val: {
      set(val) {
        if (this.useValueField) {
          return this.$emit('input', val)
        }
        const start = val ? val[0] : ''
        const end = val ? val[1] : ''
        this.$emit('update:start', start)
        this.$emit('update:end', end)
      },
      get() {
        if (this.useValueField) return this.value
        const start = this.start || ''
        const end = this.end || ''
        return [start, end]
      }
    }
  },
  methods: {
    getRanger(days = 0, onlyDay = false, valueFormat = this.valueFormat) {
      const defaultTime = this.defaultTime || ['', '']
      let startSuffix = defaultTime[0] ? ' ' + defaultTime[0] : ''
      let endSuffix = defaultTime[1] ? ' ' + defaultTime[1] : ''
      if (onlyDay) {
        const start = dateUtil().subtract(days, 'days').format('yyyy-MM-DD') + startSuffix
        const end = dateUtil().subtract(days, 'days').format('yyyy-MM-DD') + endSuffix
        return [start, end]
      }
      const end = dateUtil().format('yyyy-MM-DD') + endSuffix
      const start = dateUtil().subtract(days, 'days').format('yyyy-MM-DD') + startSuffix
      return [start, end]
    },

    showPicker() {
      this.$refs.range.showPicker()
    },
    handleClick(event) {
      this.$emit('panel', event)
    },
    focus() {
      console.log('获取焦点')
      this.$refs.range.focus()
    },
    blur() {
      console.log('失去焦点')
      this.$refs.range.focus()
    }
  }
}
</script>

<style lang="scss">
.date-current {
  span {
    background-color: #08b9a2;
    border-radius: 50%;
    color: #fff;
    opacity: 0.8;
    text-align: center;
    transition: all 0.3s;

    &:hover {
      opacity: 1;
    }
  }
}

.common-date-ranger {
  width: 100%;
  display: flex;
  align-content: center;
  //height: 32px !important;
}
</style>
