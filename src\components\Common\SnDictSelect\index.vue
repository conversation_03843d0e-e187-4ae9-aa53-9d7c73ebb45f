<template>
  <el-select
    v-if="name === 'select'"
    ref="select"
    :clearable="clearable"
    :filterable="filterable"
    :popperAppendToBody="popperAppendToBody"
    :value="val"
    class="common-select"
    v-bind="$attrs"
    @change="handleChange"
  >
    <el-option
      v-for="item in options"
      :key="item[getDictProps.key]"
      :label="item[getDictProps.label]"
      :value="item[getDictProps.value]"
    >
      <slot v-bind="item"/>
    </el-option>
  </el-select>

  <el-radio-group v-else-if="name === 'radio'" :value="val" v-bind="$attrs" @input="handleChange">
    <template v-if="!useGroupButton">
      <el-radio v-for="item in options" :key="item[getDictProps.key]"
                :label="item[getDictProps.value]" :value="item[getDictProps.value]">
        <slot v-bind="item">
          <span>{{ item[getDictProps.label] }}</span>
        </slot>
      </el-radio>
    </template>
    <template v-else>
      <el-radio-button
        v-for="item in options"
        :key="item[getDictProps.key]"
        :label="item[getDictProps.value]"
        :value="item[getDictProps.value]"
        class="common-group"
      >
        <slot v-bind="item">
          <span>{{ item[getDictProps.label] }}</span>
        </slot>
      </el-radio-button>
    </template>
  </el-radio-group>

  <SimpleRadio v-else-if="name === 'customRadio'" v-model="val" :list="options"
               :props="getDictProps" @input="handleChange"/>

  <el-switch
    v-else-if="name === 'switch'"
    :active-value="options[0][getDictProps.value]"
    :inactive-value="options[1][getDictProps.value]"
    :value="val"
    v-bind="$attrs"
    @change="handleChange"
  >
    <!--
         :active-text="options[0][getDictProps.label]"
                    :inactive-text="options[1][getDictProps.label]"-->
  </el-switch>

  <el-checkbox-group v-else-if="name === 'checkbox'" v-model="value" v-bind="$attrs">
    <template v-if="!useGroupButton">
      <el-checkbox
        v-for="item in options"
        :key="item[getDictProps.key]"
        :label="item[getDictProps.value]"
        :value="item[getDictProps.value]"
        disabled
        @change="handleChange(item[getDictProps.value])"
      >
        <slot v-bind="item">
          <span>{{ item[getDictProps.label] }}</span>
        </slot>
      </el-checkbox>
    </template>
    <template v-else>
      <el-checkbox-button
        v-for="item in options"
        :key="item[getDictProps.key]"
        :label="item[getDictProps.value]"
        :value="item[getDictProps.value]"
        class="common-group"
        disabled
        @change="handleChange(item[getDictProps.value])"
      >
        <slot v-bind="item">
          <span>{{ item[getDictProps.label] }} </span>
        </slot>
      </el-checkbox-button>
    </template>
  </el-checkbox-group>
</template>

<script>
import {DICT_PROPS} from '@/const'
import {getDictDatas} from '@/utils/dict'
import {isDef} from '@/utils/is'

export default {
  name: 'SnDictSelect',
  data() {
    return {}
  },
  computed: {
    getListeners() {
      console.log(this.$listeners)
      return {
        ...this.$listeners
      }
    },
    val: {
      set(v) {
        this.$emit('input', v)
      },
      get() {
        const value = this.value
        if (Array.isArray(value)) {
          return value.map((item) => {
            return typeof item === 'number' ? item + '' : item
          })
        }
        return typeof this.value === 'number' ? value + '' : value
      }
    },
    getList() {
      return this.list.map((item) => {
        return {
          ...item,
          [this.getDictProps.value]: item[this.getDictProps.value] + ''
        }
      })
    },
    options() {
      if (isDef(this.list)) {
        return this.getList
      }
      if (!this.type) return []
      return this.useDefault
        ? [
          {
            [this.getDictProps.label]: '全部',
            [this.getDictProps.value]: ''
          },
          ...getDictDatas(this.type)
        ]
        : [...getDictDatas(this.type)]
    },

    getDictProps() {
      return this.props
        ? {
          ...this.props,
          key: this.props.key ? this.props.key : this.props.value
        }
        : DICT_PROPS
    }
  },
  props: {
    props: {
      type: Object
    },
    list: {
      type: Array
    },
    name: {
      type: String,
      default: 'select'
    },
    value: {
      type: [String, Number, Array],
      require: true
    },
    popperAppendToBody: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    useGroupButton: {
      type: Boolean,
      default: true
    },
    useDefault: {
      type: Boolean,
      default: false
    },

    type: {
      type: String,
      require: true
    },
    filterable: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    current() {
      return this.options.find((item) => item[this.getDictProps.value] === this.val)
    },
    focus() {
      this.$refs.select.focus()
    },
    handleChange(val) {
      const info = this.options.find((item) => item[this.getDictProps.value] === val) || {}
      this.$emit('input', val)
      this.$emit('change', val, {...info})
    }
  }
}
</script>

<style lang="scss" scoped>
.el-radio-group {
  height: 32px;
}

.common-select {
  width: 100%;
}

::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #000;
}

::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
  border-color: #1890ff;
}
</style>
