<template>
  <span>
    <template v-for="(dict, index) in this.getDictDatas2(type, value)">
      <span
        v-if="dict.colorType === 'default' || dict.colorType === '' || dict.colorType === undefined"
        :key="dict.value"
        :class="dict.cssClass"
        :index="index"
      >{{ dict.label }}</span
      >
      <el-tag v-else :key="dict.value" :class="dict.cssClass" :disable-transitions="true" :index="index"
              :type="dict.colorType">
        {{ dict.label }}
      </el-tag>
    </template>
  </span>
</template>

<script>
import {getDictDatas2} from '@/utils/dict'

export default {
  name: 'SnDictTag',
  props: {
    type: String,
    value: [Number, String, Boolean, Array]
  },
  computed: {},
  data() {
    return {}
  },
  methods: {
    getDictDatas2
  }
}
</script>
<style lang="scss" scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}

.dict {
  &-orange {
    color: #ff8e5c;
    border: 1px solid #ff8e5c;
    padding: 2px 10px;
    font-size: 1rem;
    border-radius: 4px;
  }

  &-green {
    color: #08b681;
    border: 1px solid #08b681;
    padding: 2px 10px;
    font-size: 1rem;
    border-radius: 4px;
  }

  &-purple {
    color: #604bba;
    border: 1px solid #604bba;
    padding: 2px 10px;
    font-size: 1rem;
    border-radius: 4px;
  }

  &-yellow {
    color: #f8c65d;
    border: 1px solid #f8c65d;
    padding: 2px 10px;
    font-size: 1rem;
    border-radius: 4px;
  }
}
</style>
