<template>
  <div :style="{ height: height }" class="card">
    <div class="card-header">
      <div :class="getTitle" class="title">
        <slot name="title">
          <i v-if="!isTitlePrefixIcon" :class="[titlePrefixIcon]" class="title-prefix-icon"></i>
          {{ title }}
          <el-tooltip v-if="showTipIcon" :content="tipIconText">
            <i v-if="tipIcon && showTipIcon" :class="[tipIcon]" class="tip"></i>
          </el-tooltip>
        </slot>
      </div>
      <div class="action">
        <slot name="action"></slot>
      </div>
    </div>

    <div class="card-content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SnFormCard',
  data() {
    return {}
  },
  props: {
    titlePrefixIcon: {
      type: String,
      default: 'default'
    },

    tipIcon: {
      type: String,
      default: 'el-icon-warning'
    },
    showTipIcon: {
      type: Boolean,
      default: false
    },
    tipIconText: {
      type: String,
      default: ''
    },
    height: {
      type: String,
      default: 'inherit'
    },
    title: {
      type: String,
      default: ''
    },
    panel: {
      type: Object,
      default: () => {
        return {
          left: {},
          right: {}
        }
      }
    }
  },
  computed: {
    isTitlePrefixIcon() {
      return this.titlePrefixIcon === 'default'
    },

    getTitle() {
      return {
        'title-prefix': this.isTitlePrefixIcon
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';

.title-prefix-icon {
  padding-right: 5px;
}

.title-prefix {
  padding-left: 10px;

  &::before {
    position: absolute;
    content: '';
    width: 3px;
    left: 0px;
    border-radius: 3px;
    height: 80%;
    top: 50%;
    transform: translateY(-50%);
    background-color: $menuBg;
  }
}

.card {
  width: 100%;
  display: flex;
  flex-flow: column;
  margin-bottom: 15px;

  .card-header {
    font-size: 1rem;
    display: flex;
    margin-bottom: 15px;
    width: 100%;


    .title {
      margin-right: auto;

      position: relative;
      color: #272727;
      font-size: 16px;
      display: flex;
      align-items: center;

      i {
        margin-left: 3px;
      }
    }

    .action {
      flex: 1;
      display: flex;
      //color: #0288f6;
      //padding: 10px 0 10px 10px;
    }
  }

  .card-content {
    flex: 1;
  }
}
</style>
