<template>
  <div class="common--input">
    <el-input v-model="val" :clearable="clearable" class="input" v-bind="$attrs" v-on="$listeners">
      <template #default="scope">
        <slot v-bind="scope" />
      </template>
      <template #prepend>
        <slot name="prepend">
          <el-select v-if="isSearchForm" v-model="filterKeyVal" class="prepend-select" placeholder="选择" style="width: 75px">
            <el-option v-for="item in filterList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </slot>
      </template>
    </el-input>
  </div>
</template>

<script>
export default {
  name: 'SnInput',
  inheritAttrs: false,
  data() {
    return {
      loading: false,
      list: []
    }
  },
  props: {
    value: {
      type: String,
      require: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    isSearchForm: {
      type: <PERSON><PERSON>an,
      default: true
    },
    filterKey: {
      type: String
    }
  },
  computed: {
    filterList() {
      return [
        {
          label: '模糊',
          value: 'filter_LIKES_'
        },
        {
          label: '精确',
          value: 'filter_EQS_'
        }
      ]
    },
    val: {
      set(v) {
        this.$emit('input', v)
      },
      get() {
        return this.value
      }
    },
    filterKeyVal: {
      set(v) {
        this.$emit('update:filterKey', v)
      },
      get() {
        return this.filterKey
      }
    }
  },

  methods: {},
  components: {}
}
</script>

<style lang="scss">
.common--input {
  width: 100%;

  .input {
    width: 100%;

    .el-input-group__prepend {
      background-color: #fff;
    }
  }
}
</style>
