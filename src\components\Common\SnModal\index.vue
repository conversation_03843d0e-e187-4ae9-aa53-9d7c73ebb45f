<template>
    <el-dialog
            ref="dialog"
            v-el-drag-dialog
            :appendToBody="appendToBody"
            :before-close="handleCancel"

            :custom-class="isFullscreen ? 'full-modal' : 'common--modal'"
            :fullscreen="isFullscreen"
            :top="top"
            :visible.sync="visible"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :width="width"
            class="common--modal"
            v-bind="$attrs"
            @dragDialog="onDragDialogChange"
    >
        <template #title>
      <span class="el-dialog__title" @dblclick.stop="toggleFullScreen">
        <slot name="title">
          {{ $attrs.title }}
        </slot>
        <span class="title-action">
          <span class="title-action-icon">
            <el-tooltip :content="isFullscreen ? '取消全屏' : '全屏'" class="item" effect="dark"
                        placement="top">
              <i class="el-icon-full-screen" @click="toggleFullScreen"></i>
            </el-tooltip>
          </span>
        </span>
      </span>
        </template>
        <slot></slot>
        <slot v-if="showFooter" name="footer">
      <span slot="footer" class="common--modal-footer">
        <div style="margin-right: auto">
          <slot name="left"></slot>
        </div>
        <slot name="modal-left"></slot>
        <el-button :loading="loading" type="modal-save" @click="handleOk">{{
            submitText
            }}</el-button>
        <slot name="modal-center"></slot>
        <el-button type="modal-cancel" @click="handleCancel">{{ cancelText }}</el-button>
        <slot name="modal-right"></slot>
      </span>
        </slot>
    </el-dialog>
</template>

<script>
import {isBoolean, isFunction} from '@/utils/is'
import elDragDialog from '@/directive/el-drag-dialog/index'

export default {
    name: 'SnModal',
    inheritAttrs: false,
    data() {
        return {
            loading: false,
            isFullscreen: false,
            moveStyle: {
                top: '0px',
                left: '0px'
            }
        }
    },
    directives: {
        elDragDialog
    },
    computed: {
        visible: {
            set(val) {
                this.$emit('input', val)
            },
            get() {
                return this.value
            }
        }
    },
    props: {
        submitText: {
            type: String,
            default: '保 存'
        },
        cancelText: {
            type: String,
            default: '取 消'
        },
        width: {
            type: String,
            default: '50vw'
        },
        value: {
            type: Boolean,
            require: true
        },
        appendToBody: {
            type: Boolean,
            default: true
        },
        showFooter: {
            type: Boolean,
            default: true
        },
        fullscreen: {
            type: Boolean,
            default: false
        },
        cancel: {
            type: Function
        },
        ok: {
            type: Function
        },
        top: {
            type: String,
            default: '6vh'
        }
    },
    watch: {
        fullscreen: {
            handler(val) {
                this.isFullscreen = val
            },
            immediate: true
        }
    },
    methods: {
        onDragDialogChange() {
            // if (!this.isFullscreen) {
            //   const commonModalEl = this.getTargetEl()
            //   const left = commonModalEl.style.left
            //   const top = commonModalEl.style.top
            //   this.moveStyle = {
            //     left,
            //     top
            //   }
            // }
        },

        getTargetEl() {
            return this.$refs.dialog.$el.querySelector('.common--modal')
        },
        toggleFullScreen() {
            this.isFullscreen = !this.isFullscreen
            const commonModalEl = this.getTargetEl()
            if (this.isFullscreen) {
                commonModalEl.style.left = '0px'
                commonModalEl.style.top = '0px'
            } else {
                // commonModalEl.style.left = this.moveStyle.left
                // commonModalEl.style.top = this.moveStyle.top
            }
            this.$emit('update:fullscreen', this.isFullscreen)
        },
        async handleCancel() {
            if (this.cancel && isFunction(this.cancel)) {
                const status = await this.cancel(this.visible)
                if (!isBoolean(status)) return false
                this.visible = status
            } else {
                this.visible = false
            }
        },
        async handleOk() {
            try {
                this.loading = true
                if (this.ok && isFunction(this.ok)) {
                    const status = await this.ok(this.visible)
                    if (!isBoolean(status)) return false
                    this.visible = status
                } else {
                    this.visible = false
                }
            } catch (error) {
                console.log(error, 'error')
            } finally {
                this.loading = false
            }
        }
    }
}
</script>

<style lang="scss">
.common--modal.el-dialog__wrapper {
  overflow: hidden !important;
}

.full-modal {
  .el-dialog__body {
    height: calc(100vh - 44px - 64px) !important;
    overflow: auto;
  }
}

.common--modal {
  .el-dialog {
    .el-form--label-top .el-form-item__label {
      padding: 0;
    }
  }

  .el-dialog__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 5px 5px 0 0;

    padding: 0;
    background-color: #256DDF;

    .el-dialog__headerbtn {
      top: inherit;
    }

    .el-dialog__title {
      padding: 10px 15px;
      height: 44px;
      color: #fff;
      font-size: 14px;
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-action {
        cursor: pointer !important;
        padding-right: 35px;

        .title-action-icon {
          padding: 0 5px;
        }
      }
    }

    .el-icon-close {
      margin-top: 1px;
      color: #fff;
    }
  }

  .el-dialog__body {
    padding: 25px 25px;
    height: 70vh;
    overflow: auto;
  }

  .el-dialog__footer {
    padding: 18px 20px;
  }

  &-footer {
    display: flex;
    align-items: center;
  }
}
</style>
