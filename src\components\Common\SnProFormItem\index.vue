<template>
  <el-col v-if="useCol" v-bind="getColConfig">
    <el-form-item
      :class="[showLabel ? '' : 'label-hide']"
      v-bind="{ ...$attrs, ...getLabelConfig, rules: getRules, prop }"
      v-on="$listeners"
    >
      <template #label>
        <slot v-if="$slots.label" name="label" />
        <span v-else v-show="showLabel" :style="{ width: getLabelConfig.labelWidth }" class="label-wrap">
          <span class="label-text"> {{ label }}</span>
          <el-tooltip v-if="showTipIcon" :content="tipIconText">
            <i v-if="tipIcon && showTipIcon" :class="[tipIcon]" class="tip"></i>
          </el-tooltip>
        </span>
      </template>
      <div style="display: flex">
        <div style="width: 100%">
          <slot v-bind="{ bindField: prop, placeholder: getPlaceholder }"></slot>
        </div>
      </div>
    </el-form-item>
  </el-col>
  <el-form-item
    v-else
    :class="[showLabel ? '' : 'label-hide']"
    v-bind="{ ...$attrs, ...getLabelConfig, rules: getRules, prop }"
    v-on="$listeners"
  >
    <template #label>
      <slot v-if="$slots.label" name="label" />
      <span v-else v-show="showLabel" :style="{ width: getLabelConfig.labelWidth }" class="label-wrap">
        <span class="label-text"> {{ label }}</span>
        <el-tooltip v-if="showTipIcon" :content="tipIconText">
          <i v-if="tipIcon && showTipIcon" :class="[tipIcon]" class="tip"></i>
        </el-tooltip>
      </span>
    </template>
    <div style="display: flex">
      <div style="width: 100%">
        <slot v-bind="{ bindField: prop, placeholder: getPlaceholder }"></slot>
      </div>
    </div>
  </el-form-item>
</template>

<script>
import { createPlaceholderMessage } from '@/components/Common/SnProTable/helper'
import { deepClone } from '@/utils'
import { isNull } from '@/utils/is'

export default {
  name: 'SnProFormItem',
  data() {
    return {}
  },
  props: {
    labelWidth: {
      type: String
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    label: {
      type: String,
      default: ''
    },
    prop: {
      type: [String],
      default: ''
    },
    comp: {
      type: String,
      default: 'Input'
    },
    required: {
      type: Boolean,
      default: false
    },
    rules: {
      type: Array
    },
    tipIcon: {
      type: String,
      default: 'el-icon-warning'
    },
    showTipIcon: {
      type: Boolean,
      default: false
    },
    tipIconText: {
      type: String,
      default: ''
    },
    useCol: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    getPlaceholder() {
      const { comp, label } = this
      return `${createPlaceholderMessage(comp)}${label}`
    },
    getColConfig() {
      const { span = 24, offset, push, pull } = this.$attrs
      return {
        span: span * 1,
        offset: offset * 1,
        push: push * 1,
        pull: pull * 1
      }
    },
    getLabelConfig() {
      const { showLabel, labelWidth } = this
      return {
        labelWidth: showLabel ? labelWidth : '0px'
      }
    },
    getRules() {
      return this.createRules()
    }
  },

  methods: {
    createRules() {
      const { rules: defRules = [], required, comp } = this
      let rules = deepClone(defRules)
      const defaultMsg = this.getPlaceholder
      const validator = (rule, value, callback) => {
        const msg = rule.message || defaultMsg
        if (value === undefined || isNull(value)) {
          // 空值
          return callback(new Error(msg))
        } else if (Array.isArray(value) && value.length === 0) {
          // 数组类型
          return callback(new Error(msg))
        } else if (typeof value === 'string' && value.trim() === '') {
          // 空字符串
          return callback(new Error(msg))
        }
        return callback()
      }

      if (required) {
        if (!rules || rules.length === 0) {
          rules = [{ required: true, validator }]
        } else {
          rules.push({ required: true, validator })
        }
      }

      const requiredRuleIndex = rules.findIndex((rule) => Reflect.has(rule, 'required') && !Reflect.has(rule, 'validator'))
      if (requiredRuleIndex !== -1) {
        const rule = rules[requiredRuleIndex]
        if (comp) {
          if (!Reflect.has(rule, 'type')) {
            rule.type = comp === 'InputNumber' ? 'number' : 'string'
          }
          rule.message = rule.message || defaultMsg
          if (comp.includes('Input') || comp.includes('Textarea')) {
            rule.whitespace = true
          }
        }
      }
      return rules
    }
  }
}
</script>

<style lang="scss" scoped>
.label-hide {
  ::v-deep {
    .el-form-item__label {
      display: none;
    }
  }
}

.label-wrap {
  .tip {
    margin-left: 3px;
  }
}

::v-deep {
  .el-form-item__label {
    //height: auto !important;
  }
}
</style>
