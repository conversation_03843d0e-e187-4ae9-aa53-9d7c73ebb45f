<template>
    <el-row v-show="!!actions.length" :gutter="10" :style="{ ...getActionStyle }" class="page-action">
        <el-col :span="1.5" class="col">
            <template v-for="(item, index) in actions">
                <el-popconfirm v-if="item.usePopConfirm" :key="index" v-bind="item.popConfirm"
                               @confirm="handleClickBtn(item)">
                    <el-button
                            v-if="checkPermission([...item.hasPermission])"
                            :key="index"
                            slot="reference"
                            :class="[item.class && item.class, actionToContent ? 'btn' : 'form-btn']"
                            :icon="item.icon"
                            :loading="loadings[item.uuid]"
                            :type="item.type"
                            size="mini"
                            v-bind="$attrs"
                    >
                        {{ item.text }}
                    </el-button>
                </el-popconfirm>

                <template v-else>
                    <el-button
                            v-if="checkPermission([...item.hasPermission])"
                            v-show="!item.hide"
                            :class="[item.class && item.class, actionToContent ? 'btn' : 'form-btn']"
                            :icon="item.icon"
                            :loading="loadings[item.uuid]"
                            :type="item.type"
                            size="mini"
                            v-bind="$attrs"
                            @click="handleClickBtn(item)"
                    >
                        {{ item.text }}
                    </el-button>
                </template>
            </template>
        </el-col>
        <slot/>
        <Toolbar v-if="showToolbar" :columns="columns" :showSearch.sync="show" style="margin-left:auto"
                 @queryTable="getList"></Toolbar>
    </el-row>
</template>

<script>
import checkPermission from '@/utils/permission'
import Toolbar from './toolbar'

export default {
    name: 'PageAction',
    data() {
        return {
            loadings: {}
        }
    },
    inject: ['getList'],
    props: {
        showSearch: {
            type: Boolean,
            default: true
        },
        actions: {
            type: Array,
            default: () => []
        },
        columns: {
            type: Array,
            default: () => []
        },
        showToolbar: {
            type: Boolean,
            default: true
        },
        actionToContent: {
            type: Boolean
        }
    },
    computed: {
        show: {
            get() {
                return this.showSearch
            },
            set(v) {
                this.$emit('update:showSearch', v)
            }
        },
        getActionStyle() {
            if (this.actionToContent) {
                return this.actions.length
                    ? {
                        'margin-bottom': '10px'
                    }
                    : {}
            } else {
                return {
                    'margin-bottom': '0'
                }
            }
        },
        getBtnClass() {
            if (this.actionToContent) {
                return {
                    btn: true
                }
            }
        }
    },
    watch: {
        showSearch: {
            handler(v) {
                this.show = v
            },
            immediate: true
        },
        actions: {
            handler(v) {
                this.initLoadingAll(v)
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        checkPermission,
        async handleClickBtn(item) {
            if (item.onClick) {
                this.loadings[item.uuid] = true
                try {
                    // 普通函数和promise函数await添加await不会报错
                    await item.onClick(item)
                } catch (error) {
                } finally {
                    setTimeout(() => {
                        this.loadings[item.uuid] = false
                    }, 200)
                }
            }
        },
        initLoadingAll(actions) {
            const loadings = {}
            actions.forEach((item) => {
                loadings[item.uuid] = false
            })
            this.loadings = loadings
        }
    },

    components: {Toolbar}
}
</script>

<style lang="scss" scoped>
@import '@/styles/btn.scss'; // global css
.page-action {
  display: flex;
  margin-bottom: 10px;

  .col {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .btn {
    margin-right: 10px;
    padding: 5px 8px;
    font-size: 13px;
    border-radius: 3px;
    height: 28px;
    line-height: 1;
    vertical-align: middle;
  }

  .form-btn {
    margin-left: 10px;
    font-size: 14px;
    padding: 5px 8px;
    border-radius: 5px;
    height: 26px;
    line-height: 1;
    vertical-align: middle;
  }
}

//::v-deep .el-button + .el-button {
//  margin-left: 0;
//}
</style>
