<template>
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="searchForm" class="search-form-wrapper"
             size="small"
             @keyup.enter.native="handlerSearchQuery(searchForm)">
        <div class="search-form-item-line">
            <slot name="searchTopExtra"/>
        </div>

        <template v-for="filter in formConfig.filters">
            <div v-show="!filter.hide" :style="{ ...(filter.itemStyle || {}) }" class="search-form-item">
                <div v-if="!formConfig.labelHideAll && !filter.hideLabel"
                     :style="{ ...getSearchFormItemStyles(filter) }" class="label">
                    {{ filter.label }}{{ formConfig.labelColon ? ':' : '' }}
                </div>

                <div class="content">
                    <slot v-if="filter.slot" :name="filter.slot" v-bind="{
              isSearchForm: true,
              searchForm,
              prop: filter.prop,
              componentProp: filter.componentProp,
              filter,
              filterKeys,
              style: { ...defaultFilterCompStyles(filter.component), ...filter.style }
            }"/>

                    <component :is="filter.component" v-else-if="filter.component" v-model="searchForm[filter.prop]"
                               v-bind.sync="{
              isSearchForm: true,
              ...filter.componentProp
            }" :filterKey.sync="filterKeys[filter.prop]"
                               :style="{ ...defaultFilterCompStyles(filter.component), ...filter.style }"/>

                    <el-input v-else v-model="searchForm[filter.prop]" v-bind.sync="{
              isSearchForm: true,
              ...filter.componentProp
            }" :filterKey.sync="filterKeys[filter.prop]" :style="{ ...defaultFilterCompStyles(), ...filter.style }"
                              clearable
                              @clear="handlerSearchQuery(searchForm)"/>
                </div>
            </div>
        </template>
        <div :style="{ ...formConfig.searchItemStyle }" class="search-form-item">
            <slot name="searchButtonLeft"/>
            <el-button :loading="searchLoading" class="search--button" type="search"
                       @click="handlerSearchQuery(searchForm)"
                       v-text="'查询'"/>
            <slot name="searchButtonCenter"/>
            <el-button :loading="resetLoading" class="search--button" type="reset" @click="handleSearchReset"
                       v-text="'重置'"/>
            <slot name="searchButtonRight"/>
        </div>

        <div class="search-form-item-line">
            <slot name="searchExtra"/>
        </div>
    </el-form>
</template>

<script>
import {defaultFilterCompStyles, initSearchForm} from '../../helper'
import {isString, isObject, isDef} from '@/utils/is'

export default {
    name: 'PageForm',
    data() {
        return {
            defaultForm: {},
            searchForm: {},
            defaultFilterKeys: {},
            filterKeys: {},
            searchLoading: false,
            resetLoading: false,
            initialized: false
        }
    },
    inject: ['searchQuery', 'resetQuery'],
    props: {
        tableOption: {
            type: Object,
            default: () => ({})
        },
        showSearch: {
            type: Boolean
            // default: true
        },
        mountedQuery: {
            type: Boolean,
            default: true
        },
        formConfig: {
            type: Object,
            default: () => {
                return {
                    filters: []
                }
            }
        }
    },
    watch: {
        'formConfig.filters': {
            handler(v) {
                if (v.length && !this.initialized) {
                    this.initQueryDefault(v)
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        getSearchFormItemStyles(filter) {
            const width = filter.labelWidth
            return {
                width: isDef(width) ? `${isString(width) ? width : width + 'px'}` : `70px`
            }
        },
        getSearchForm() {
            return {...this.searchForm}
        },

        getFilterKeys() {
            return {...this.filterKeys}
        },

        isString,
        defaultFilterCompStyles,

        async setSearchForm(values, isRefresh = false, target = 'searchForm') {
            if (!isObject(values)) {
                throw new Error('请传入一个对象')
            }
            let searchForm = {}
            for (const [key, val] of Object.entries(values)) {
                searchForm[key] = val
            }
            this[target] = {...this[target], ...searchForm}
            if (isRefresh) {
                await this.resetQuery({
                    resetQuery: {...this[target]},
                    emit: false
                })
            }
        },

        async handleSearchReset() {
            this.resetLoading = true
            this.searchForm = {...this.defaultForm}
            // 重置filterKeys
            this.filterKeys = {...this.defaultFilterKeys}
            try {
                await this.resetQuery({
                    resetQuery: {...this.searchForm},
                    emit: true
                })
            } catch (error) {
            } finally {
                this.resetLoading = false
            }
        },

        initQueryDefault(filters = this.formConfig.filters) {
            this.initialized = true
            const searchForm = initSearchForm(filters)
            this.defaultForm = {...searchForm}
            this.searchForm = searchForm

            function initFilterKey() {
                const obj = {}
                filters.forEach((item) => {
                    if (item.filterKey) {
                        obj[item.prop] = item.filterKey
                    }
                })
                return obj
            }

            this.defaultFilterKeys = {
                ...initFilterKey()
            }

            this.filterKeys = {...this.defaultFilterKeys}

            this.$emit('initFilterQuery', {...searchForm})
        },

        async handlerSearchQuery(searchForm) {
            if (this.searchLoading) return
            this.searchLoading = true
            try {
                await this.searchQuery({
                    searchForm,
                    isRefresh: false,
                    emit: true
                })
            } catch (error) {
            } finally {
                this.searchLoading = false
            }
        }
    }
}
</script>

<style lang="scss">
.search-form-wrapper {
  padding: 8px 10px 0px;
  margin-bottom: 10px;
  background: #fff;
  //padding: 20px 20px 5px;
  display: flex;
  flex-flow: wrap;

  .search-form-item-line {
    width: 100%;
    display: flex;
    align-items: center;
    font-size: 14px;
    margin: 0px 0 0px 10px;

  }

  .search-form-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    margin: 3px 0 10px 10px;
    line-height: 30px;
    height: 30px;

    .label {
      margin-right: 3px;
      height: 100%;
      color: #adadad;
    }

    .content {
      //flex: 1;
      min-height: 30px;
      display: flex;
      align-items: center;
      padding: 7px 0px;
      //height: auto;
    }

    .search--button {
    }
  }
}
</style>
