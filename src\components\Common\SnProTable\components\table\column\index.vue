<template>
  <el-table-column v-if="isArray(col.children) && col.children.length" v-bind="getColumnProp(col)">
    <template #header="scope">
      <slot v-if="col.headerSlot" :name="col.headerSlot" v-bind="{ col, ...scope }"/>
      <span v-else> {{ col.label }}</span>
    </template>

    <template v-for="child in col.children">
      <template v-if="!child.hide">
        <slot v-if="child.slot" :col="{ align: defaultAlign, ...child }" :name="child.slot"/>
        <PageColumn v-else :key="child.prop" :col="child"
                    :defaultAlign="col.align"
                    :getSlotsCols="getSlotsCols"
                    :getSlotsHeaderCols="getSlotsHeaderCols"
        >
          <template v-for="filter in getSlotsCols" #[filter.slot]="data">
            <slot :name="filter.slot" v-bind="data || {}"></slot>
          </template>

          <template v-for="filter in getSlotsHeaderCols" #[filter.headerSlot]="data">
            <slot :name="filter.headerSlot" v-bind="data || {}"></slot>
          </template>
        </PageColumn>
      </template>
    </template>
  </el-table-column>

  <el-table-column v-else-if="col.format" v-bind="getColumnProp(col)">
    <template #header="scope">
      <slot v-if="col.headerSlot" :name="col.headerSlot" v-bind="{ col, ...scope }"/>
      <span v-else> {{ col.label }}</span>
    </template>

    <template #default="scope">
      <template v-if="checkFormatType(col.format, DICT_PREFIX)">
        <DictTag v-if="isDef(scope.row[col.prop])" :type="col.format.replace(DICT_PREFIX, '')"
                 :value="scope.row[col.prop]"/>
      </template>
      <template v-else-if="checkFormatType(col.format, DATE_PREFIX)">
        <span v-if="isDef(scope.row[col.prop])">
          {{
            formatToDate(scope.row[col.prop], col.format.replace(DATE_PREFIX, '') || 'YYYY-MM-DD')
          }}
        </span>
      </template>
      <template v-else-if="isFunction(col.format)">
        {{ col.format({value: scope.row[col.prop], col, row: scope.row}) }}
      </template>
    </template>
  </el-table-column>

  <el-table-column v-else v-bind="getColumnProp(col)">
    <template #header="scope">
      <slot v-if="col.headerSlot" :name="col.headerSlot" v-bind="{ col, ...scope }"/>
      <span v-else> {{ col.label }}</span>
    </template>
  </el-table-column>
</template>

<script>
import {DICT_PREFIX, DATE_PREFIX} from '@/const'
import {formatToDate} from '@/utils/dateUtils'
import {isFunction, isArray, isDef} from '@/utils/is'
import DictTag from '@/components/Common/SnDictTag'

export default {
  name: 'PageColumn',
  components: {
    DictTag
  },
  data() {
    return {
      DICT_PREFIX,
      DATE_PREFIX
    }
  },
  inject: ['model', 'tableAction'],
  props: {
    defaultAlign: {
      type: String,
      default: 'center'
    },
    col: {
      type: Object,
      default: {}
    },
    getSlotsCols: {
      type: Array,
      default: () => []
    },
    getSlotsHeaderCols: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    isDef,
    isArray,
    getColumnProp(col) {
      return {
        key: col.prop,
        align: col.align || this.defaultAlign,
        showOverflowTooltip: !!col.showOverflowTooltip,
        ...col
      }
    },
    formatToDate,
    isFunction,
    checkFormatType(format, prefix) {
      if (typeof format === 'string') {
        return format.startsWith(prefix)
      }
      return false
    }
  }
}
</script>

<style lang="scss">
//.text-danger {
//  background: rebeccapurple;
//}
</style>
