<template>
    <div id="wrapper" ref="wrapper" class="table-container">
        <div class="content">
            <el-table
                    v-show="!showOtherContent"
                    ref="innerTable"
                    v-loading="loading"
                    :cell-class-name="cellClassName"
                    :cell-style="getCellStyle"
                    :data="data"
                    :header-cell-style="getHeaderCellStyle"
                    :height="`${layoutHeight}px`"
                    :row-class-name="rowClassName"
                    :row-style="getRowStyle"
                    :stripe="stripe"
                    border
                    class="common-table"
                    v-bind="$attrs"
                    v-on="excludeExistedListeners($listeners, existedListeners)"
                    @selection-change="handleSelectionChange"
                    @sort-change="handleSortChange"
            >
                <slot></slot>
                <template #append>
                    <slot name="append"></slot>
                </template>
            </el-table>
            <div v-show="showOtherContent" v-loading="loading">
                <slot
                        name="otherContent"
                        v-bind="{
            loading: loading,
            data: data,
            height: `${layoutHeight}px`,
            cellStyle: getCellStyle,
            rowStyle: getRowStyle,
            headerCellStyle: getHeaderCellStyle
          }"
                />
            </div>
            <div id="summaryTable">
                <slot v-if="useTableSummary" name="summaryTable"></slot>
            </div>
            <Pagination
                    v-if="showPagination"
                    id="page"
                    :limit.sync="pageConfig.pageSize"
                    :page.sync="pageConfig.pageNo"
                    v-bind="pageConfig"
                    @pagination="getData"
            >
                <slot :config="pageConfig" name="pagination"></slot>
            </Pagination>
        </div>
    </div>
</template>

<script>
// eslint-disable-next-line no-unused-vars
import {excludeExistedListeners, handleRangeValue} from '../../helper'
import {isBoolean, isDef, isFunction} from '@/utils/is'
import Pagination from './pagination'
import _ from 'lodash'
import projectStyles from '@/styles/project.scss'
import {deepClone, flatTree} from '@/utils'
import {fetchSetting} from '@/const'
import {removeNoExportCols, transExportList} from '@/utils/excelUtils'

export default {
    inheritAttrs: false,
    components: {
        Pagination
    },
    name: 'PageTable',
    inject: ['model', 'tableAction', 'getFilterKeys', 'getList'],
    props: {
        tableName: {
            type: String
        },
        data: {
            type: Array,
            default: () => []
        },
        option: {
            type: Object,
            default: () => ({
                columns: []
            })
        },
        query: {
            type: Object,
            default() {
                return {}
            }
        },
        useTableSummary: {
            type: Boolean,
            default: false
        },
        showSearch: {
            type: Boolean
        },
        afterFetch: {
            type: Function
        },
        errorFetch: {
            type: Function
        },
        summarySpanMethod: {
            type: Function
        },
        beforeFetch: {
            type: Function
        },
        stripe: {
            type: Boolean,
            default: true
        },
        formatCols: {
            type: Array,
            default: []
        },
        showOtherContent: {
            type: Boolean,
            default: false
        },

        apiPageName: {
            type: String,
            default: ''
        },
        layoutHeightOther: {
            type: Number,
            default: 0
        }
    },
    computed: {
        layoutHeight() {
            const clientHeight = document.documentElement.clientHeight
            const wrapperPadding = 20 // body 拥有padding20 计算不到padding值所以手动设置
            return clientHeight - this.otherHeight - wrapperPadding - this.layoutHeightOther
        },
        listeners() {
            return this.$listeners
        },
        showPagination() {
            return !this.useApiList && !this.hidePagination
        },
        useApiList() {
            return this.option.useApiList
        },

        getOptionPageConfig() {
            return {...this.option.pagination}
        },

        hidePagination() {
            return this.option.hidePagination
        },
        getRowStyle() {
            return {
                // 'min-height': '40px',
                height: '28px',
                'font-size:': '0.875rem',
                ...this.option.rowStyle,
                background: '#fff'
            }
        },
        getCellStyle() {
            return {padding: '0', ...this.option.cellStyle}
        },
        getHeaderCellStyle() {
            const styles = this.option.headerCellStyle || {}
            return {
                height: '28px',
                background: projectStyles.projectTheme,
                color: '#fff',
                padding: '0',
                'font-size:': '0.875rem',
                ...styles
            }
        }
    },
    updated() {
        this.$nextTick(() => {
            this.doLayout()
        })
    },

    data() {
        return {
            rowHeight: 140,
            loading: false,
            otherHeight: 0,
            existedListeners: ['selection-change', 'sort-change'],
            selectionList: [],
            pageConfig: {pageNo: 1, total: 0},
            projectStyles
        }
    },
    watch: {
        data: {
            handler(val) {
                this.$emit('dataChange', val)
                if (isDef(this.$attrs['show-summary']) || isDef(this.$attrs['showSummary'])) {
                    const $el = this.$refs.innerTable.$el
                    const tds = $el?.querySelectorAll('.el-table__footer-wrapper tr>td')
                    this.summarySpanMethod && this.summarySpanMethod(tds)
                }
            }
            // immediate: true
        },
        'pageConfig.total': {
            handler(val) {
                this.$emit('totalChange', val)
            },
            immediate: true
        },
        showSearch() {
            this.otherHeight = this.getOtherHeight()
        },

        getOptionPageConfig: {
            handler(val) {
                this.pageConfig = {...this.pageConfig, ...val}
            },
            immediate: true
        }
    },
    async mounted() {
        if (this.option.mountedQuery) {
            await this.getData()
        }
        // 宏任务调用
        setTimeout(() => {
            this.otherHeight = this.getOtherHeight()
        }, 300)
        window.addEventListener('resize', this.otherHeightResizeHandler)
    },
    beforeDestroy() {
        // 不一定能清除掉 每次返回的都是一个新的函数
        window.removeEventListener('resize', this.otherHeightResizeHandler)
    },
    methods: {
        getSelectionList() {
            return this.selectionList
        },
        clearTable() {
            this.$refs.innerTable.clearSort()
        },
        toggleRowExpansion(row, expanded) {
            this.$refs.innerTable.toggleRowExpansion(row, expanded)
        },
        getPageConfig() {
            return this.pageConfig
        },
        // 这样写可能有点问题
        otherHeightResizeHandler: _.debounce(function () {
            this.otherHeight = this.getOtherHeight()
        }, 40),
        toggleRowSelection(row, selected = true) {
            this.$refs.innerTable.toggleRowSelection(row, selected)
        },

        clearSelection() {
            this.$refs.innerTable.clearSelection()
        },
        doLayout() {
            this.$refs.innerTable.doLayout()
        },
        cellClassName({row, rowIndex, columnIndex, column}) {
            const fn = this.listeners['cell-class-name']
            if (fn && isFunction(fn)) {
                return fn({row, rowIndex, columnIndex, column})
            } else {
                return ''
            }
        },
        rowClassName({row, rowIndex, columnIndex, column}) {
            const fn = this.listeners['row-class-name']
            if (fn && isFunction(fn)) {
                return fn({row, rowIndex, columnIndex, column})
            } else {
                if (rowIndex % 2 === 0) {
                    return 'highlight-row'
                }
                return 'common-row'
            }
        },

        excludeExistedListeners,
        getOtherHeight() {
            const wrapper = document.querySelector('#wrapper') // table 容器
            const pageWrapper = document.querySelector('#pageWrapper') // 页面容器
            const page = document.querySelector('#page') || {offsetHeight: 0} // 分页的高度
            let offsetHeight = page.offsetHeight
            let otherHeight = 0
            // 分页padding值
            if (page.offsetHeight !== 0 && getComputedStyle(page).marginTop) {
                otherHeight += parseFloat(getComputedStyle(page).marginTop)
            }
            // 页面容器的高度计算
            if (pageWrapper && getComputedStyle(pageWrapper).padding) {
                otherHeight += parseFloat(getComputedStyle(pageWrapper).padding) // 去除px
            }
            let top = 0
            if (wrapper) {
                top = wrapper.getBoundingClientRect().top
            }
            // console.log(top, offsetHeight, otherHeight)
            return top + offsetHeight + otherHeight

            // const mainHeight = document.querySelector('.app-main')?.getBoundingClientRect()?.height || 0
            // const formHeight = document.querySelector('.common--page')
            // console.log(mainHeight, formHeight)
            // return 0
        },

        handleSelectionChange(selection) {
            const fn = this.listeners['selection-change']
            if (fn && isFunction(fn)) {
                const list = fn(selection) || selection
                this.selectionList = list
            } else {
                this.selectionList = selection
            }
        },

        async getData(isRefresh = false) {
            await this.$nextTick()
            const {setTableData} = this.tableAction
            try {
                this.loading = true
                const params = this.beforeFetch ? this.beforeFetch(this.buildQuery(isRefresh)) : this.buildQuery(isRefresh)
                let data
                // list接口
                if (this.useApiList) {
                    const res = this.apiPageName ? await this.model[this.apiPageName](params) : await this.model.list(params)
                    if (isFunction(this.afterFetch)) {
                        const formatData = await this.afterFetch(res.data, fetchSetting.listField) // 这里的afterFetch返回的是一个数组
                        if (formatData === false) return // 如果返回false，就不做处理
                        data = formatData.list
                    } else {
                        data = res.data
                    }
                    setTableData(data)
                    return true
                }
                // page接口
                const res = this.apiPageName ? await this.model[this.apiPageName](params) : await this.model.page(params)
                if (isFunction(this.afterFetch)) {
                    const formatData = await this.afterFetch(res.data, fetchSetting.listField) // 这里的afterFetch返回的是一个对象，还需要做其处理
                    // 如果返回false，就不做处理
                    if (formatData === false) return

                    setTableData(formatData[fetchSetting.listField])
                    this.pageConfig.total = formatData[fetchSetting.totalField] || 0
                  console.log(formatData)

                    return true
                } else {
                    data = res.data[fetchSetting.listField]
                    setTableData(data)
                    this.pageConfig.total = res.data[fetchSetting.totalField] || 0
                }
            } catch (error) {
                let data = []
                if (isFunction(this.errorFetch)) {
                    const callBackData = await this.errorFetch(error)
                    data = callBackData
                }
                setTableData(data)
                console.error('请求失败', error)
            } finally {
                this.loading = false
            }
        },

        handleSortChange({column, prop, order}) {
            const fn = this.listeners['sort-change']
            if (fn && isFunction(fn)) {
                fn({column, prop, order})
            } else {
                if (!prop && order) {
                    return false
                }
                if (prop) {
                    let orderBy = prop ? prop.split('_')[0] : ''
                    const orderDir = order ? (order === 'descending' ? 'DESC' : 'ASC') : undefined
                    orderBy = !orderDir ? undefined : orderBy
                    this.pageConfig = {...this.pageConfig, orderBy, orderDir}
                    this.getData()
                }
            }
        },

        /** 构建过滤条件 */
        buildQuery(isRefresh = false) {
            const buildFilterQuery = (queryRes = {}) => {
                const filterKeys = this.getFilterKeys()
                Object.keys(queryRes).forEach((key) => {
                    if (isDef(filterKeys[key])) {
                        queryRes[filterKeys[key] + key] = queryRes[key]
                        delete queryRes[key]
                    }
                })
                return queryRes
            }

            const getQuery = () => {
                const target = deepClone(this.query)
                return buildFilterQuery(handleRangeValue(this.model.formConfig, target))
            }

            const query = getQuery()

            if (this.useApiList) {
                return {...query}
            }

            if (isBoolean(isRefresh) && isRefresh) {
                this.pageConfig = {
                    ...this.getOptionPageConfig,
                    total: this.pageConfig.total,
                    pageNo: 1
                }
            }
            const {pageNo, pageSize, orderBy, orderDir} = this.pageConfig

            return {
                ...query,
                [fetchSetting.pageField]: pageNo,
                [fetchSetting.sizeField]: pageSize,
                [fetchSetting.sortField]: orderBy || query[fetchSetting.sortField],
                [fetchSetting.sortOrder]: orderDir || query[fetchSetting.sortOrder]
            }
        },

        async getExportData() {
            // 导出的是所有数据
            const params = this.beforeFetch ? this.beforeFetch(this.buildQuery(false)) : this.buildQuery(false)
            try {
                let resp
                if (!this.apiPageName) {
                    resp = this.useApiList ? await this.model.list(params) : await this.model.page(params)
                } else {
                    resp = await this.model[this.apiPageName](params)
                }
                let data = resp.data
                if (this.afterFetch) {
                    const formatData = await this.afterFetch(data, fetchSetting.listField)
                    return formatData[this.useApiList ? 'list' : fetchSetting.listField] || []
                } else {
                    return resp.data || []
                }
            } catch (e) {
                return []
            }
        },

        async handleExport() {
            const columns = deepClone(this.option.columns)
            removeNoExportCols(columns)
            const list = JSON.parse(JSON.stringify(await this.getExportData()))

            // console.log(list, 'list')
            const flatCols = flatTree(columns)
                .filter((col) => !col.noExport)
                .filter((col) => col.prop)
                .filter((col) => col.format)
            const exportList = transExportList(list, flatCols)
            exportList.forEach((item) => {
                for (const [k, v] of Object.entries(item)) {
                    if ([null, undefined].includes(v)) {
                        item[k] = ''
                    }
                }
            })
            return {
                data: exportList,
                columns
            }
        }
    }
}
</script>

<style lang="scss">
@import '~@/styles/project.scss';

.common-table {
  .el-table-column--selection {
    .cell {
      padding-left: 0;
    }
  }

  .el-table th.el-table__cell > .cell {
    padding: 0;
  }
}

.unqualified {
  color: #ff726b;
}

.el-table--enable-row-hover .el-table__body tr:hover > td {
  transition: 0.3s all;
  opacity: 0.9 !important;
  color: #2c2c2c;
}

.el-table {
  overflow: visible !important;

  .green-row {
    background: #24c1ab;
    color: #fff;
  }

  .gap-row {
    background: #f3f7fb;
    color: #2c2c2c;
  }

  .common-row {
    color: #2c2c2c;
  }

  .mark-red-cell {
    background: rgba(247, 96, 99, 0.2) !important;
  }

  .mark-border-none {
    border: none !important;
  }

  .mark-blue-cell {
    background: rgba(5, 89, 168, 0.06) !important;
  }

  .mark-avg-row {
    color: #FF3636 !important;
    //font-size: 16px !important;
    font-weight: bold !important;
    background: #F1F1F1 !important;
  }

  .mark-base-row {
    background: #fff1e4 !important;
    color: #2c2c2c !important;
  }

  .highlight-row {
    background: #37c2ce0f;
    color: #2c2c2c;
  }

  .total-row {
    background: #f1f1f1;
  }
}
</style>
