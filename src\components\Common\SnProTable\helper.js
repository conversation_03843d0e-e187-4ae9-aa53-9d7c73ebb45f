import {isDef, isFunction} from '@/utils/is'
import _ from 'lodash'
import {dateUtil} from '@/utils/dateUtils'

/**
 * 排除已经存在的监听事件
 */
export function excludeExistedListeners(listeners, props) {
  return _.omit(listeners, props)
}

/**
 * 获取列表带插槽带cols
 */
export function getSlotCols(cols, prop) {
  const result = []
  cols.forEach((item) => {
    if (item[prop]) {
      result.push(item)
    }
    if (item.children) {
      result.push(...getSlotCols(item.children, prop))
    }
  })

  return result
}

export function defaultFilterCompStyles(component = '') {
  if (component.includes('DateRanger')) {
    return {width: '100%', height: 'auto'}
  } else {
    return {width: '100%', height: 'auto'}
  }
}

export function sleep(timeout = 300) {
  return new Promise((resolve) => {
    if (process.env.NODE_ENV !== 'production') resolve()
    setTimeout(() => {
      resolve()
    }, timeout)
  })
}

export function initSearchForm(filters, field = 'defaultValue') {
  const searchForm = {}
  const dateComp = ['DateRanger']
  filters.forEach((col) => {
    if (isDef(col[field])) {
      const defaultValue = col[field] || (dateComp.includes(col.component) && []) || ''
      searchForm[col.prop] = defaultValue
    }
  })

  return searchForm
}

export function handleRangeValue(props, values) {
  // 判断是否配置并处理fieldMapToNumber
  const fieldMapToTime = props?.fieldMapToTime
  fieldMapToTime && (values = handleRangeTimeValue(props, values))
  const fieldMapToNumber = props?.fieldMapToNumber
  fieldMapToNumber && (values = handleRangeNumberValue(props, values))
  return values
}

/**
 * 处理时间转换成2个字段
 * @param props
 * @param values
 */
export function handleRangeTimeValue(props, values) {
  const fieldMapToTime = props.fieldMapToTime
  if (!fieldMapToTime || !Array.isArray(fieldMapToTime)) {
    return values
  }
  for (const [field, [startTimeKey, endTimeKey], format = 'YYYY-MM-DD'] of fieldMapToTime) {
    if (!field || !startTimeKey || !endTimeKey || !values[field] || !values[field]?.length) {
      continue
    }

    let timeValue = values[field]
    if (!Array.isArray(timeValue)) {
      timeValue = timeValue.split(',')
    }
    const [startTime, endTime] = timeValue
    values[startTimeKey] = dateUtil(startTime).format(format)
    values[endTimeKey] = dateUtil(endTime).format(format)
    Reflect.deleteProperty(values, field)
  }
  return values
}

/**
 * 处理数字转换成2个字段
 * @param props
 * @param values
 */
export function handleRangeNumberValue(props, values) {
  const fieldMapToNumber = props.fieldMapToNumber
  if (!fieldMapToNumber || !Array.isArray(fieldMapToNumber)) {
    return values
  }
  for (const [field, [startNumberKey, endNumberKey]] of fieldMapToNumber) {
    if (!field || !startNumberKey || !endNumberKey || !values[field] || !values[field]?.length) {
      continue
    }
    let temp = values[field]
    if (typeof temp === 'string') {
      temp = temp.split(',')
    }
    const [startNumber, endNumber] = temp
    values[startNumberKey] = startNumber
    values[endNumberKey] = endNumber
    Reflect.deleteProperty(values, field)
  }
  return values
}

export function getSlot(slots, slot = 'default', data) {
  if (!slots || !Reflect.has(slots, slot)) {
    return null
  }
  if (!isFunction(slots[slot])) {
    console.error(`${slot} is not a function!`)
    return null
  }
  const slotFn = slots[slot]

  if (!slotFn) return null
  return slotFn(data)
}

export function createPlaceholderMessage(component) {
  if (component.includes('Input') || component.includes('Complete') || component.includes('ApiAutocomplete')) {
    return '请输入'
  }

  if (
    component.includes('DateSelect') ||
    component.includes('DictSelect') ||
    component.includes('ApiSelect') ||
    component.includes('ApiTreeSelect') ||
    component.includes('AreasSelect')
  ) {
    return '请选择'
  }

  if (
    component.includes('Picker') ||
    component.includes('DateRanger') ||
    component.includes('TimePicker') ||
    component.includes('DatePicker')
  ) {
    return '请选择'
  }

  if (
    component.includes('Select') ||
    component.includes('Cascader') ||
    component.includes('Checkbox') ||
    component.includes('Radio') ||
    component.includes('Switch')
  ) {
    return '请选择'
  }
  return ''
}

export const DATE_TYPE_COMPONENTS = ['DateRanger', 'TimePicker', 'DatePicker']
