<template>
    <div class="common--page">
        <Form ref="form" :formConfig="formConfig" :showSearch="showSearch" :tableOption="tableOption"
              @initFilterQuery="setFilterQuery">
            <template #searchButtonLeft>
                <slot name="searchButtonLeft"/>
            </template>
            <template #searchButtonCenter>
                <slot name="searchButtonCenter"/>
            </template>
            <template #searchButtonRight>
                <slot name="searchButtonRight"/>
                <el-divider v-if="getActions.length" class="action-divider"
                            direction="vertical"></el-divider>

                <Action v-if="!actionToContent" :actionToContent="actionToContent" :actions="getActions"
                        :columns="tableOption.columns"
                        :showSearch.sync="showSearch" :showToolbar="showToolbar">
                    <slot name="actionOther"/>
                </Action>
            </template>
            <template #searchExtra>
                <slot name="searchExtra"/>
            </template>
            <template #searchTopExtra>
                <slot name="searchTopExtra"/>
            </template>
            <template v-for="filter in getSlotsFilter" #[filter.slot]="data">
                <slot :name="filter.slot" v-bind="data || {}"></slot>
            </template>
        </Form>
        <div class="common--page-content">
            <Action v-if="actionToContent" ref="action" :actionToContent="actionToContent"
                    :actions="getActions"
                    :columns="tableOption.columns" :showSearch.sync="showSearch"
                    :showToolbar="showToolbar">
                <slot name="actionOther"/>
            </Action>
            <Other ref="other">
                <div v-if="tableOption.showSelection" v-show="false" class="select-data-tips">
                    <div class="icon">
                        <i class="el-icon-warning-outline"></i>
                    </div>
                    <div class="content">已选中 {{ selectionDataLen }} 条记录(可跨页)</div>
                    <el-divider class="divider" direction="vertical"></el-divider>
                    <span class="clear" type="text" @click="() => clearSelect(false)">清空</span>
                </div>
                <slot name="other"/>
            </Other>
            <Table ref="table" :afterFetch="afterFetch" :apiPageName="apiPageName"
                   :beforeFetch="beforeFetch"
                   :data="tableData" :errorFetch="errorFetch"
                   :formatCols="getFormatTableCols" :layoutHeightOther="layoutHeightOther"
                   :option="tableOption"
                   :query="query" :rowKey="rowKey"
                   :showOtherContent="showOtherContent" :showSearch="showSearch" :stripe="stripe"
                   :summarySpanMethod="summarySpanMethod"
                   :tableName="tableName" :useTableSummary="useTableSummary" v-bind="$attrs"
                   @totalChange="(total) => $emit('totalChange', total)" v-on="$listeners"
                   @selection-change="objhandleSelectionChange">

                <!--        <slot name="expand"></slot>-->
                <el-table-column v-if="tableOption.showSelection" :align="defaultAlign"
                                 :selectable="selectable"
                                 type="selection" v-bind="tableOption.selectionConfig"/>
                <el-table-column v-if="tableOption.showIndex" :align="defaultAlign" fixed="left"
                                 type="index"
                                 v-bind="tableOption.indexConfig"/>

                <template v-for="col in tableOption.columns">
                    <template v-if="!col.hide">
                        <slot v-if="col.slot" :col="{ align: defaultAlign, ...col }" :name="col.slot"/>
                        <Column v-else :key="col.prop" :col="col" :defaultAlign="defaultAlign"
                                :getSlotsCols="getSlotsCols"
                                :getSlotsHeaderCols="getSlotsHeaderCols">
                            <template v-for="filter in getSlotsCols" #[filter.slot]="data">
                                <slot :name="filter.slot" v-bind="data || {}"></slot>
                            </template>

                            <template v-for="filter in getSlotsHeaderCols" #[filter.headerSlot]="data">
                                <slot :name="filter.headerSlot" v-bind="data || {}"></slot>
                            </template>
                        </Column>
                    </template>
                </template>

                <slot name="bodyCell"/>

                <template #append>
                    <slot name="append"></slot>
                </template>

                <el-table-column v-if="alwaysOpt && tableOption.showOpt"
                                 :align="tableOption.align || defaultAlign"
                                 class-name="table-col-opt" v-bind="tableOption.optConfig">
                    <template slot-scope="scope">
                        <slot name="opt" v-bind="scope"></slot>
                    </template>
                </el-table-column>
                <template #otherContent="config">
                    <slot name="otherContent" v-bind="config"/>
                </template>
                <template #summaryTable>
                    <slot :tableOption="tableOption" name="summaryTable"></slot>
                </template>
            </Table>
        </div>
    </div>
</template>

<script>
import _ from 'lodash'
import {getUuid} from '@/utils'
import {dateUtil, formatToDateRange} from '@/utils/dateUtils'
import BaseModel from './model'
import * as Components from './components'
import {isDef, isArray, isObject, isFunction} from '@/utils/is'
import {initSearchForm, createPlaceholderMessage, getSlotCols} from './helper'

export default {
    name: 'SnProTable',
    components: {
        ...Components
    },
    props: {
        stripe: {
            type: Boolean,
            default: true
        },
        actionToContent: {
            type: Boolean,
            default: false
        },
        layoutHeightOther: {
            type: Number,
            default: 0
        },
        rowKey: {
            type: String,
            default: 'id'
        },
        tableName: {
            type: String,
            default: 'table'
        },
        model: {
            type: BaseModel
        },
        searchPrefix: {
            type: String,
            default: ''
        },
        actions: {
            type: Array,
            default: () => []
        },
        afterFetch: {
            type: Function
        },
        errorFetch: {
            type: Function
        },
        summarySpanMethod: {
            type: Function
        },
        beforeQuery: {
            type: Function
        },
        showSearchForm: {
            type: Boolean,
            default: true
        },
        showToolbar: {
            type: Boolean,
            default: false
        },
        useTableSummary: {
            type: Boolean,
            default: false
        },
        showOtherContent: {
            type: Boolean,
            default: false
        },
        apiPageName: {
            type: String,
            default: ''
        },
        beforeFetch: {
            type: Function
        },
        selectable: {
            type: Function
        },

        alwaysOpt: {
            type: Boolean,
            default: true
        },
        handleSelectionChange: {
            type: Function
        },
        isSelectionChange: {
            type: Boolean,
            default: false
        },
        searchQuerySync: {
            type: Function
        }
    },
    provide() {
        return {
            model: this.model,
            tableAction: this.getTableAction,
            searchQuery: this.searchQuery,
            resetQuery: this.resetQuery,
            getList: this.getList,
            getFilterKeys: this.getFilterKeys
        }
    },
    data() {
        return {
            tableOption: {
                columns: [] // 表格列
            },
            tableData: [],
            formConfig: {
                filters: []
            },
            query: {},
            loading: true,
            showSearch: true,
            selectionDataLen: 0,
            selectlist: []

        }
    },

    computed: {
        defaultAlign() {
            return this.tableOption?.defaultAlign || 'center'
        },
        getActions() {
            return this.actions.map((item) => {
                return {...item, uuid: getUuid()}
            })
        },
        getTableAction() {
            return {
                setTableData: this.setTableData
            }
        },
        getSlotsFilter() {
            return this.formConfig.filters.filter((item) => isDef(item.slot))
        },
        getSlotsCols() {
            // 递归获取slot
            return getSlotCols(this.tableOption.columns, 'slot')
        },
        getSlotsHeaderCols() {
            return getSlotCols(this.tableOption.columns, 'headerSlot')
        },
        getFormatTableCols() {
            return this.tableOption.columns.filter((f) => f.format)
        }
    },
    created() {
        this.initFormatConfig()
        this.$on('summary-span-method', (data) => {
            this.summarySpanMethod && this.summarySpanMethod(data)
        })
    },
    mounted() {
        this.getTableHeight()
    },
    watch: {
        showSearchForm: {
            handler(v) {
                this.showSearch = v
            },
            immediate: true
        }
    },
    methods: {

        getTableHeight() {
            const getElHeight = (el, keys = ['height', 'marginTop', 'marginBottom', 'paddingTop', 'paddingBottom']) => {
                let obj =
                    el != null
                        ? window.getComputedStyle(el)
                        : {
                            height: 0,
                            marginTop: 0,
                            marginBottom: 0,
                            paddingTop: 0,
                            paddingBottom: 0
                        }
                return keys.reduce((total, key) => total + parseFloat(obj[key]), 0)
            }
            const mainHeight = getElHeight(document.querySelector('.app-main'))
            const formHeight = getElHeight(document.querySelector('.search-form-wrapper'))
            const actionHeight = getElHeight(this.$refs.action?.$el)
            const otherHeight = getElHeight(this.$refs.other?.$el)
            const ctxPadding = getElHeight(document.querySelector('.common--page-content'), ['paddingTop', 'paddingBottom'])
            const pageHeight = getElHeight(document.querySelector('#page'))

            // console.log(mainHeight - (formHeight + actionHeight + otherHeight + pageHeight + ctxPadding))
            return mainHeight - (formHeight + actionHeight + otherHeight + pageHeight + ctxPadding)
        },
        isFunction,
        getTableData() {
            return [...this.tableData]
        },
        clearSelect(clear = true) {
            this.selectionDataLen = 0
            this.$refs.table.clearSelection()
            clear && this.getList()
        },

        getSelectRows() {
            return this.$refs.table.getSelectionList()
        },
        getSelectIds() {
            return this.$refs.table.getSelectionList().map((item) => item[this.rowKey])
        },

        setSelectionByIds(ids = [], selected = true) {
            const list = [...new Set(ids)]
            list.forEach((id) => {
                const row = this.tableData.find((item) => item[this.rowKey] === id)
                row &&
                this.$refs.table.toggleRowSelection([
                    {
                        row: row,
                        selected: selected
                    }
                ])
            })
        },
        // toggleRowSelection(rows) {
        //   console.log('jjjjjjj')
        //   this.$refs.table.toggleRowSelection(rows[rows.length - 1])
        // },
        // 回调不能是promise
        objhandleSelectionChange(rows) {
            if (this.isSelectionChange) {
                if (rows.length > 1) {
                    this.$refs.table.clearSelection()
                    this.$refs.table.toggleRowSelection(rows[rows.length - 1])
                }
                this.selectlist = rows[rows.length - 1] ? [rows[rows.length - 1]] : []
                console.log(this.selectlist)
                this.$emit('selectlist', this.selectlist)
            }

            // if (rows.length > 1) {
            //   this.$refs.table.clearSelection()
            //   this.$refs.table.toggleRowSelection(rows[rows.length - 1])
            // }
            // let multipleSelection = rows[rows.length - 1] ? [rows[rows.length - 1]] : []
            // console.log(multipleSelection)

            // rows 如果太大选中就会很慢
            // this.selectionDataLen = rows.length
        },

        tableLayout() {
            this.$refs.table.doLayout()
        },

        setFilterQuery(query) {
            this.query = {...query}
        },

        // 设置filters
        async setFormConfigFilters(data) {
            let updateData = []
            if (isObject(data)) {
                updateData.push(data)
            }
            if (isArray(data)) {
                updateData = [...data]
            }
            // 空数组则直接返回，不然执行到底下会有意外到bug 比如之前默认显示到filter不显示
            if (!updateData.length) return false
            const schema = []
            updateData.forEach((item) => {
                this.formConfig.filters.forEach((val) => {
                    if (val.prop === item.prop) {
                        const newSchema = _.merge(val, item)
                        schema.push(newSchema)
                    } else {
                        schema.push(val)
                    }
                })
            })
            const filters = _.uniqBy(schema, 'prop')
            this.formConfig.filters = filters

            const defaultValue = initSearchForm(filters)
            await this.setSearchForm(defaultValue, false, 'defaultForm')
        },

        async setColumns(data) {
            let updateData = []
            if (isObject(data)) {
                updateData.push(data)
            }
            if (isArray(data)) {
                updateData = [...data]
            }
            const schema = []
            updateData.forEach((item) => {
                this.tableOption.columns.forEach((val) => {
                    if (val.prop === item.prop) {
                        const newSchema = _.merge(val, item)
                        schema.push(newSchema)
                    } else {
                        schema.push(val)
                    }
                })
            })
            const columns = _.uniqBy(schema, 'prop')
            this.tableOption.columns = columns
        },

        // 设置from搜索
        setSearchForm(values, isRefresh, target) {
            this.$refs.form && this.$refs.form.setSearchForm(values, isRefresh, target)
        },

        initFormatConfig() {
            const model = this.model
            if (typeof model === 'undefined' || !model.initialize) throw new Error('初始化model失败,请检查参数')
            this._formatFormFilter(_.cloneDeep(model.formConfig))
            this.tableOption = _.cloneDeep(model.tableConfig)
        },

        _formatFormFilter(formConfig) {
            const {autoPlaceholder, formatDateTypeAll} = formConfig

            function formatPlaceholder(col) {
                if (isDef(col.componentProp) && col.componentProp.placeholder) return
                if (autoPlaceholder) {
                    const placeholder = createPlaceholderMessage(col.component || 'Input') + (col.label ? col.label : '')
                    col.componentProp = {...col.componentProp, placeholder}
                }
            }

            function formatDateComp(col) {
                const dateComp = ['DateSelect', 'DateRanger']
                if (dateComp.includes(col.component) && typeof col.defaultValue !== 'undefined') {
                    const format = col.formatDate || formatDateTypeAll || 'YYYY-MM-DD'
                    if (Array.isArray(col.defaultValue)) {
                        const startDate = dateUtil(col.defaultValue[0]).format(format)
                        const endDate = dateUtil(col.defaultValue[1]).format(format)
                        if (col.formatDate === 'YYYY-MM-DD') {
                            col.defaultValue[0] = startDate
                            col.defaultValue[1] = endDate
                        } else {
                            // 格式化与后端规范相同的时间格式
                            if (col.componentProp && col.componentProp.type === 'monthrange') {
                                col.defaultValue = formatToDateRange(col.defaultValue, 'month')
                            } else if (col.componentProp && col.componentProp.type === 'yearrange') {
                                col.defaultValue = formatToDateRange(col.defaultValue, 'year')
                            } else {
                                col.defaultValue = formatToDateRange(col.defaultValue)
                            }
                        }
                    } else {
                        col.defaultValue = dateUtil(col.defaultValue).format(format)
                    }
                } else {
                    console.warn('日期组件传参有问题')
                }
            }

            // function init
            for (const col of formConfig.filters) {
                formatPlaceholder(col)
                formatDateComp(col)
            }
            this.formConfig = formConfig
        },

        setTableData(data, cb) {
            this.$emit('tableDataChange', data)
            if (cb) {
                // 这个cb是给外部使用的
                this.tableData = cb(this.tableData)
                return
            }
            this.tableData = data
        },

        async getList(isRefresh = false, emit = true, useLoading = false) {
            try {
                if (this.searchQuerySync) {
                    await this.searchQuerySync({...this.query})
                }
                emit && this.$emit('search-query-sync', {...this.query})
                await this.$refs.table.getData(isRefresh)
                emit && this.$emit('search-query', {...this.query})
            } catch (error) {
                console.log(error)
            } finally {
            }
        },

        getFilterKeys() {
            return this.$refs.form.getFilterKeys()
        },

        /** 搜索按钮操作 */
        async searchQuery({searchForm = {}, isRefresh = false, emit = false}) {
            let additionalQuery = {}
            if (this.beforeQuery) {
                additionalQuery = await this.beforeQuery(searchForm) || {}
            }
            this.query = {...searchForm, ...additionalQuery}
            await this.getList(isRefresh, emit)
        },

        /** 重置按钮操作 */
        async resetQuery({resetQuery, emit = false}) {
            const buildQuery = resetQuery
            emit && this.$emit('before-reset-query', buildQuery)
            await this.searchQuery({
                searchForm: buildQuery,
                isRefresh: true,
                emit: false
            })
            emit && this.$emit('reset-query', buildQuery)
            this.$refs.table.clearTable()
        },

        // // 外部调用搜索
        // handleQueryBySearchForm(isRefresh = false) {,
        //   const searchForm = this.getSearchForm()
        //   this.searchQuery({
        //     searchForm,
        //     isRefresh,
        //     emit: false
        //   })
        // },

        /**
         * 搜索之后的query
         */
        buildQuery() {
            return this.$refs.table.buildQuery()
        },

        toggleRowExpansion(row, expanded) {
            this.$refs.table.toggleRowExpansion(row, expanded)
        },

        /**
         * 实时获取搜索表单的值
         */
        getSearchForm() {
            return this.$refs.form.getSearchForm()
        },
        getPageConfig() {
            return this.$refs.table.getPageConfig()
        },
        handleExport() {
            return this.$refs.table.handleExport()
        }
    }
}
</script>

<style lang="scss">
.common--page {
  width: 100%;
}

.common--page-content {
  height: 100%;
  padding: 10px 20px 10px 20px;
  background-color: #fff;
  box-sizing: border-box;
}
</style>

<style lang="scss" scoped>
::v-deep {
  .action-divider {
    .el-divider--vertical {
      margin-left: 16px;
    }
  }

  .table-col-opt {
    //background: #fff !important;
    //.cell {
    //}

    td {
      background: #ffff !important;
    }
  }
}

.select-data-tips {
  display: flex;
  align-items: center;
  height: 30px;
  background-color: #e6f7ff;
  border-color: #91d5ff;
  font-size: 12px;
  line-height: 30px;
  padding: 0 20px;

  .clear {
    height: 100%;
    cursor: pointer;
    color: #4f91f2;
  }

  .icon {
    margin-right: 10px;
  }
}
</style>
