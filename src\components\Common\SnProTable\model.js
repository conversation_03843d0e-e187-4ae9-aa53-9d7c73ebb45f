import request from '@/utils/request'
import modal from '@/utils/modal'
import { getTablePagination } from '@/const'

/**
 * @class BaseModel 基础模型
 * @param {String} name 模型名称
 * @param {FormConfig} formConfig 表单配置
 * @param {Object} tableConfig 表格配置
 */
class BaseModel {
  constructor(name, formConfig = {}, tableConfig = {}) {
    this.name = name
    this._formConfig = Object.assign(
      {
        fieldMapToTime: undefined, // [['date', ['beginDate', 'endDate'], 'YYYY-MM-DD']]
        fieldMapToNumber: undefined, // [['number', ['startNumber', 'endNumber']]]
        formatDateTypeAll: 'YYYY-MM-DD', // 默认表格格式日期格式化类型
        labelColon: false, // 是否显示label后面的冒号
        searchItemStyle: {}, // 搜索项样式
        autoPlaceholder: true, // 自动设置placeholder,
        labelHideAll: true // 隐藏所有表单项label
      },
      formConfig
    )

    this._tableConfig = Object.assign(
      {
        mountedQuery: true, // 初始化后是否查询
        hidePagination: false, // 隐藏分页
        useApiList: false, // 是否使用Api的list接口
        tooltipAll: true, // 是否开启表格所有列的tooltip
        pagination: { ...getTablePagination() }, // 默认表格分页配置
        indexConfig: { label: '序号', width: '50px' }, // 默认表格序号列配置
        selectionConfig: { width: '50px', reserveSelection: true }, // 默认表格选择列配置
        optConfig: { width: 140, label: '操作', prop: 'opt', fixed: 'right' } // 默认表格操作列配置
      },
      tableConfig
    )

    this._initialize = true
  }

  get initialize() {
    return !!this._initialize
  }

  get formConfig() {
    return this._formConfig
  }

  get tableConfig() {
    return this._tableConfig
  }

  get request() {
    return request
  }

  get modal() {
    return modal
  }

  save(data, optName = 'create') {
    return request({
      url: `${this.name}/save`,
      method: 'post',
      data
    })
  }

  page(query) {
    return request({
      url: `${this.name}/page`,
      method: 'get',
      params: query
    })
  }

  list(query) {
    return request({
      url: `${this.name}/list`,
      method: 'get',
      params: query
    })
  }

  get(id) {
    return request({
      url: `${this.name}/get`,
      method: 'get',
      params: { id }
    })
  }

  async removeBatch({ ids = [], useConfirm = false }) {
    const fetchFn = () =>
      request({
        url: `${this.name}/batchDelete`,
        method: 'post',
        data: { ids: ids.join(',') }
      })

    if (useConfirm) {
      try {
        await modal.confirm(`是否确认删除当前数据?`)
        return fetchFn()
      } catch (error) {
        return Promise.reject(error)
      }
    } else {
      return fetchFn()
    }
  }

  async remove({ id, useConfirm = false }) {
    const fetchFn = () =>
      request({
        url: `${this.name}/delete`,
        method: 'post',
        params: { id }
      })

    if (useConfirm) {
      try {
        const res = await modal.confirm('是否确认删除当前数据?')
        return fetchFn()
      } catch (error) {
        return Promise.reject(error)
      }
    } else {
      return fetchFn()
    }
  }

  async export(params, useConfirm = false) {
    if (useConfirm) {
      try {
        await modal.confirm('是否确认导出所有?')
        return request({
          url: `${this.name}/export`,
          method: 'get',
          params,
          responseType: 'blob'
        })
      } catch (error) {
        // eslint-disable-next-line new-cap
        return new Promise.reject(error)
      }
    } else {
      return request({
        url: `${this.name}/export`,
        method: 'get',
        params,
        responseType: 'blob'
      })
    }
  }
}

export default BaseModel

/**
 * @typedef TableConfigFilters
 * @property {string} label 表单项标题
 * @property {string} prop 表单项字段名
 * @property {string?} labelWidth 表单项标题宽度
 * @property {any?} defaultValue 表单项默认值
 * @property {string?} slot 表单项插槽
 * @property {boolean?} hide 是否隐藏
 * @property {boolean?} hideLabel 是否隐藏label
 * @property {{ width?:string|number}} itemStyle 表单项样式
 * @property {'SnApiAutocomplete'|'SnApiSelect'|'SnDateRanger'|'SnDateSelect' | 'SnDateTimePicker'|'SnDictSelect'|'SnSimpleSelect'|'SnSimpleCascader'|'SnUploadImg'?} component 表单项组件
 * @property {string?} componentProp.placeholder 表单项组件属性
 * @property {boolean?} componentProp.clearable 表单项组件属性
 * @property {{label:string,value:string,key:string}?} componentProp.props 表单项组件属性
 * @property {string?} componentProp.apiName 表单项组件属性
 * @property {string?} componentProp.valueFormat 表单项组件属性
 * @property {string?} componentProp.format 表单项组件属性
 * @property {string?} componentProp.type 表单项组件属性
 * @property {string?} componentProp.name 表单项组件属性
 * @property {string?} componentProp.field 表单项组件属性
 * @property {{[prop:string]:any}} componentProp 表单项组件属性
 */
/**
 * @typedef FormConfig
 * @property {Array?} fieldMapToTime 日期字段映射
 * @property {Array?} fieldMapToNumber 数字字段映射
 * @property {string?} formatDateTypeAll 默认表格格式日期格式化类型
 * @property {boolean?} labelColon 是否显示label后面的冒号
 * @property {{}} searchItemStyle 表单搜索项样式
 * @property {boolean?} autoPlaceholder 自动设置placeholder
 * @property {TableConfigFilters[]} filters // 表单搜索配置
 */
export let FormConfig

/**
 * @typedef TableConfigCols
 * @property {string} label 列标题
 * @property {string} prop 列字段名
 * @property {string?} width 列宽度
 * @property {boolean?} hide 是否隐藏
 * @property {string?} slot // 插槽
 * @property {string?} headerSlot // headerSlot插槽
 * @property {string?} align 对齐方式
 * @property {string?} fixed 固定列
 * @property {string?} minWidth 最小宽度
 * @property {boolean?} sortable 是否排序
 * @property {boolean?} noExport 是否导出
 * @property {(h, { column, $index })=>any?} renderHeader 列标题 Label 区域渲染使用的 Function
 * @property {boolean?} resizable 对应列是否可以通过拖动改变宽度（需要在 el-table 上设置 border 属性为真）
 * @property {string?} headerAlign 表头对齐方式，若不设置该项，则使用表格的对齐方式
 * @property {string?} className 列的 className
 * @property {string?} labelClassName 当前列标题的自定义类名
 * @property {[{text:string,value:any}]?} filters 数据过滤的选项，数组格式，数组中的元素需要有 text 和 value 属性。
 * @property {boolean?} showOverflowTooltip 是否开启tooltip
 * @property {'dict|'|'date|'|(value,col,row)=>any} format 格式化
 * @property {TableConfigCols[]} children 孩子节点
 */
/**
 * @typedef TableConfig
 * @property {boolean?} mountedQuery 初始化后是否查询
 * @property {boolean?} hidePagination 隐藏分页
 * @property {boolean?} useApiList 是否使用Api的list接口
 * @property {boolean?} tooltipAll 是否开启表格所有列的tooltip
 * @property {{}} pagination 默认表格分页配置
 * @property {{}} indexConfig 默认表格序号列配置
 * @property {{}} selectionConfig 默认表格选择列配置
 * @property {{}} optConfig 默认表格操作列配置
 * @property {boolean?} showOpt 显示操作列
 * @property {boolean?} showIndex 显示序号列
 * @property {boolean?} showSelection 显示选择列
 * @property {TableConfigCols[]} columns // 表单搜索配置
 */
export let TableConfig
