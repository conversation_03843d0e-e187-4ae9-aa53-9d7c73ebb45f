<template>
  <el-cascader
    :disabled="disabled"
    :options="option"
    :props="prop"
    :value="value"
    clearable
    style="width: 100%"
    @input="change($event)"
  ></el-cascader>
</template>

<script>
import { dataTree } from '@/components/RegionSelect/regionData'
import { getRegionText } from '@/filters'
import { isArray } from '@/utils/is'
// 数据项
const option = dataTree.map((item) => {
  let { name, id, children } = item
  let tweFieldCont = children
  let tweField = []
  for (let i = 0; i < tweFieldCont.length; i++) {
    let { name, id, children } = tweFieldCont[i]
    let threeFieldCont = children
    let threeField = []
    for (let j = 0; j < threeFieldCont.length; j++) {
      let { name, id } = threeFieldCont[j]
      threeField.push({ label: name, value: id })
    }
    children = [...threeField]
    tweField.push({ label: name, value: id, children })
  }
  return {
    label: name,
    value: id,
    children: tweField
  }
})
export default {
  name: 'SnRegionSelect',
  data() {
    return {
      option
    }
  },
  props: {
    value: {
      type: Array,
      require: true,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    province: {
      default: ''
    },
    city: {
      default: ''
    },
    area: {
      default: ''
    },
    text: {
      default: ''
    },
    prop: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    change(val) {
      let location = ''
      if (isArray(val) && val.length > 0) {
        location = getRegionText(val)
      }
      this.$emit('input', val)
      this.$emit('update:province', val[0] ? val[0] : '')
      this.$emit('update:city', val[1] ? val[1] : '')
      this.$emit('update:area', val[2] ? val[2] : '')
      this.$emit('select', val, location)
    }
  }
}
</script>
<style lang="scss" scoped></style>
