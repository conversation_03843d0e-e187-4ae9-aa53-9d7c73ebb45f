<template>
  <el-cascader
    ref="cascader"
    v-model="val"
    :loading="loading"
    :options="list"
    :props="props"
    class="common-api-select"
    filterable
    v-bind="$attrs"
    v-on="$listeners"
  >
    <template #default="scoped">
      <slot v-bind="scoped" />
    </template>
  </el-cascader>
</template>

<script>
export default {
  name: 'SnSimpleCascader',
  inheritAttrs: false,
  data() {
    return {
      loading: false
    }
  },
  computed: {
    val: {
      set(v) {
        this.$emit('input', v)
      },
      get() {
        return this.value
      }
    }
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    value: {
      type: [Array, String],
      require: true
    },
    props: {
      type: Object,
      default() {
        return {
          value: 'code',
          label: 'name'
        }
      }
    }
  },
  watch: {
    props(v) {}
  },
  methods: {
    handleChange(val) {
      this.$emit('change', val)
    }
  },
  components: {}
}
</script>

<style lang="scss" scoped>
.common-api-select {
  width: 100%;
}
</style>
