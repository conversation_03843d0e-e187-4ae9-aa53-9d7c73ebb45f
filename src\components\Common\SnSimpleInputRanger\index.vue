<template>
  <div class="container">
    <template v-if="useProFormItem">
      <SnProFormItem :showLabel="false" :useCol="false" class="SnProFormItem" v-bind="{ ...$attrs, prop: prop[0] }">
        <el-input
          v-model="leftVal"
          :placeholder="startPlaceholder"
          v-bind="{ ...$attrs }"
          @blur="triggerEvent($event, 'blur', 'left')"
          @change="triggerEvent($event, 'change', 'left')"
          @clear="triggerEvent($event, 'clear', 'left')"
          @focus="triggerEvent($event, 'focus', 'left')"
          @input="triggerEvent($event, 'input', 'left')"
        >
          <template #prepend>
            <span v-if="!$slots.leftPrepend && prependSeparator">{{ prependSeparator }}</span>
            <slot name="prepend" />
          </template>
          <template #append>
            <span v-if="!$slots.leftAppend && appendSeparator">{{ appendSeparator }}</span>
            <slot name="append" />
          </template>
        </el-input>
      </SnProFormItem>
      <span class="rangeSeparator">{{ rangeSeparator }}</span>
      <SnProFormItem :showLabel="false" :useCol="false" class="SnProFormItem" v-bind="{ ...$attrs, prop: prop[1] }">
        <el-input
          v-model="rightVal"
          :placeholder="endPlaceholder"
          v-bind="{ ...$attrs }"
          @blur="triggerEvent($event, 'blur', 'right')"
          @change="triggerEvent($event, 'change', 'right')"
          @clear="triggerEvent($event, 'clear', 'right')"
          @focus="triggerEvent($event, 'focus', 'right')"
          @input="triggerEvent($event, 'input', 'right')"
        >
          <template #prepend>
            <span v-if="!$slots.rightPrepend && prependSeparator">{{ prependSeparator }}</span>
            <slot name="prepend" />
          </template>
          <template #append>
            <span v-if="!$slots.rightAppend && appendSeparator">{{ appendSeparator }}</span>
            <slot name="append" />
          </template>
        </el-input>
      </SnProFormItem>
    </template>
    <template v-else>
      <el-input
        v-model="leftVal"
        :placeholder="startPlaceholder"
        v-bind="{ ...$attrs }"
        @blur="triggerEvent($event, EventName.Blur, Direction.Left)"
        @change="triggerEvent($event, EventName.Change, Direction.Left)"
        @clear="triggerEvent($event, EventName.Clear, Direction.Left)"
        @focus="triggerEvent($event, EventName.Focus, Direction.Left)"
        @input="triggerEvent($event, EventName.Input, Direction.Left)"
      >
        <template #prepend>
          <span v-if="!$slots.leftPrepend && prependSeparator">{{ prependSeparator }}</span>
          <slot name="prepend" />
        </template>
        <template #append>
          <span v-if="!$slots.leftAppend && appendSeparator">{{ appendSeparator }}</span>
          <slot name="append" />
        </template>
      </el-input>
      <span class="rangeSeparator">{{ rangeSeparator }}</span>
      <el-input
        v-model="rightVal"
        :placeholder="endPlaceholder"
        v-bind="{ ...$attrs }"
        @blur="triggerEvent($event, EventName.Blur, Direction.Right)"
        @change="triggerEvent($event, EventName.Change, Direction.Right)"
        @clear="triggerEvent($event, EventName.Clear, Direction.Right)"
        @focus="triggerEvent($event, EventName.Focus, Direction.Right)"
        @input="triggerEvent($event, EventName.Input, Direction.Right)"
      >
        <template #prepend>
          <span v-if="!$slots.rightPrepend && prependSeparator">{{ prependSeparator }}</span>
          <slot name="prepend" />
        </template>
        <template #append>
          <span v-if="!$slots.rightAppend && appendSeparator">{{ appendSeparator }}</span>
          <slot name="append" />
        </template>
      </el-input>
    </template>
  </div>
</template>

<script>
const EventPrefix = 'e'

const Direction = {
  Left: 'left',
  Right: 'right'
}

const EventName = {
  Blur: 'blur',
  Focus: 'focus',
  Change: 'change',
  Input: 'input',
  Clear: 'clear'
}

export default {
  inheritAttrs: false,
  name: 'SimpleInputRanger',
  data() {
    return {}
  },
  computed: {
    Direction() {
      return Direction
    },
    EventName() {
      return EventName
    },

    leftVal: {
      get() {
        return this.useValueField ? this.value[0] : this.start
      },
      set(val) {
        if (this.useValueField) {
          return this.$emit('input', [val, this.rightVal])
        }
        this.$emit('update:start', val)
      }
    },
    rightVal: {
      get() {
        return this.useValueField ? this.value[1] : this.end
      },
      set(val) {
        if (this.useValueField) {
          return this.$emit('input', [this.leftVal, val])
        }
        this.$emit('update:end', val)
      }
    }
  },
  props: {
    useProFormItem: {
      type: Boolean,
      default: true
    },
    useValueField: {
      type: Boolean,
      default: true
    },
    prependSeparator: {
      // 公共输入框前面的符号
      type: String,
      default: ''
    },
    appendSeparator: {
      // 公共输入框后面的符号
      type: String,
      default: ''
    },
    rangeSeparator: {
      // 中间的分隔符
      type: String,
      default: '-'
    },
    startPlaceholder: {
      //
      type: String,
      default: '请输入'
    },
    endPlaceholder: {
      type: String,
      default: '请输入'
    },
    value: {
      type: Array,
      default: () => []
    },
    prop: {
      // 用于item校验
      type: Array,
      default: () => []
    },

    start: {
      type: [Number, String],
      default: undefined
    },
    end: {
      type: [Number, String],
      default: undefined
    }
  },
  methods: {
    triggerEvent($event, eventName, direction) {
      // 自定义事件
      this.$emit(`${EventPrefix}-${eventName}`, direction, $event)
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  display: flex;
  justify-content: center;

  .rangeSeparator {
    //文字不换行
    color: #adadad;
    white-space: nowrap;
    padding: 0 5px;
    font-size: 13px;
    line-height: 32px;
  }
}
</style>
