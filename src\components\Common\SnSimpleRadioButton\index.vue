<template>
  <div class="btn-wrap" :class="!isExpanded?'':'ovblock'">
    <template v-if="list.length">
      <!-- <div class="showbtnbox">
        <el-button size="mini" type="warning" @click="toggleExpand">{{ isExpanded ? '收起' : '展开' }}</el-button>
      </div> -->
      <div class="btnbox">
        <div class="btnminbox">
          <!-- :key="item[props.key || props.value || index]  " -->
          <div v-for="(item,index) in list" :key="index" :class="['radio-item-button', val === item[props.value] ? 'active' : '']"
               :label="item[props.label]" @click="handleClick(item[props.value])">
            <span class="value">{{ item[props.label] }}</span>
          </div>
        </div>
        <div class="rightbtnbox">
          <el-button size="mini" type="warning" @click="toggleExpand">{{ isExpanded ? '收起' : '展开' }}</el-button>
        </div>
      </div>

    </template>

    <template v-else>
      <div class="radio-item-button">
        <span class="value">暂无数据</span>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'SnSimpleRadioButton',
  data() {
    return {
      isExpanded: false
    }
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    value: {
      type: [String, Number, Array],
      require: true
    },
    props: {
      type: Object
    }
  },
  computed: {
    val: {
      set(v) {
        this.$emit('input', v)
      },
      get() {
        return this.value
      }
    }
  },
  methods: {
    handleClick(val) {
      this.val = val
      this.$emit('select', val)
    },
    toggleExpand() {
      this.isExpanded = !this.isExpanded
    }
  },
  components: {}
}
</script>

<style lang="scss" scoped>
.showbtnbox {
  position: absolute;
  right: 10px;
  top: 0px;
  z-index: 9;
}
.empty {
  display: flex;
  color: #4b4b4b;
  align-items: center;
  background-color: #f8f8f8;
  height: 100%;
}

.btn-wrap {
  height: 40px;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
  position: relative;
  .btnbox {
    width: 100%;
    display: flex;
    .btnminbox {
      width: calc(100% - 60px);
      display: flex;
      flex-flow: wrap;
    }
    .rightbtnbox {
      width: 60px;
    }
  }

  .active {
    //border: 1px solid;
    color: #ff9639 !important;
    background-color: #fff1e4 !important;
  }

  .radio-item-button {
    font-weight: 400;
    transition: all 0.3s;
    //border: 1px solid #d0d0d0;
    background-color: #f8f8f8;
    min-width: 7.25rem;
    color: #4b4b4b;
    font-size: 1rem;
    border-radius: 4px;
    // margin-right: 8px;
    display: flex;
    justify-content: center;
    cursor: pointer;
    margin: 0 8px 8px 0;

    .value {
      display: flex;
      align-items: center;
      height: 32px;
      line-height: 32px;
      padding: 4px 10px;
    }
  }
}
</style>

<style>
.ovblock {
  overflow: visible !important;
  height: 100% !important;
}
</style>