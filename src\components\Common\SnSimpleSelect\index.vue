<template>
  <div class="SelectContainer">
    <el-select v-if="isSelect" ref="target" v-model="val" :filterable="filterable" :loading="loading" :multiple="multiple"
               class="select-simple-common" style="width: 100%" v-bind="$attrs" @change="handleChange" v-on="$listeners">
      <el-checkbox v-if="useCheckAll" v-model="checked" class="checkbox-simple-common" @change="selectAll">全选 </el-checkbox>
      <el-option v-for="item in getList" :key="item[props.key || props.value]" :label="item[props.label]"
                 :value="item[props.value]">
        <slot v-bind="item" />
      </el-option>
    </el-select>

    <SnSimpleRadioButton v-if="isRadiobutton" v-model="val" :list="getList" :props="props" @input="handleChange" />
  </div>
</template>

<script>
import SnSimpleRadioButton from '@/components/Common/SnSimpleRadioButton/index.vue'

export default {
  name: 'SnSimpleSelect',
  inheritAttrs: false,
  data() {
    return {
      loading: false,
      checked: false
    }
  },
  computed: {
    isSelect() {
      return this.type === 'select'
    },
    isCheckbox() {
      return this.type === 'checkbox'
    },
    isRadio() {
      return this.type === 'radio'
    },
    isRadiobutton() {
      return this.type === 'radiobutton'
    },
    val: {
      set(v) {
        this.$emit('input', v)
      },
      get() {
        return this.value
      }
    },

    getList() {
      return this.list.map((item) => {
        return {
          ...item,
          [this.props.value]: item[this.props.value] + ''
        }
      })
    }
  },
  props: {
    type: {
      type: String,
      default: 'select'
    },
    useCheckAll: {
      type: Boolean,
      default: false
    },
    filterable: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => []
    },
    value: {
      type: [String, Number, Array],
      require: true
    },
    props: {
      type: Object,
      default: () => ({
        value: 'id',
        label: 'name'
      })
    }
  },
  watch: {
    value: {
      handler(v) {
        if (this.useCheckAll && this.multiple && Array.isArray(v)) {
          // 判断当前数据是否全选
          this.checked = v.length === this.list.length
        }
      },
      immediate: true
    }
  },
  methods: {
    focus() {
      this.$refs.target.focus()
    },
    selectAll() {
      let selectedList = []
      if (this.checked) {
        selectedList = this.list.map((item) => item[this.props.value])
      }
      this.val = selectedList
      this.emitSelectChange(this.val)
    },

    emitSelectChange(val) {
      let target = {}
      if (!this.multiple) {
        target = this.list.find((item) => item[this.props.value] === val) || {}
      } else {
        target = this.list.filter((item) => val.includes(item[this.props.value])) || []
      }
      this.$emit('select', val, target)
    },

    async handleChange(val) {
      this.emitSelectChange(val)
      if (!this.multiple) {
        // const target = this.list.find(item => item[this.props.value] === val) || {}
      } else {
        // const target = this.list.filter(item => val.includes(item[this.props.value])) || []
        // this.$emit('select', { val, item: target })
        // 设置完毕过后 等到下一次在执行
        await this.$nextTick()
        this.checked = this.val.length === this.list.length
      }
    }
  },
  components: {SnSimpleRadioButton}
}
</script>

<style lang="scss">
.SelectContainer {
  width: 100%;
  height: auto !important;
}

.checkbox-simple-common {
  height: 35px;
  line-height: 35px;
  display: flex;
  align-items: center;

  padding: 0 20px;
}
</style>
