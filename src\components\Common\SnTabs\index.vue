<template>
    <div :class="getTypeClass">
        <div class="search-panel">
            <div class="search-tabs">
                <div
                        v-for="tab in tabs"
                        :key="tab.value"
                        :class="[tab.value === val ? `${type}-active` : '', `${type}-tab`]"
                        @click.stop="onTabChange(tab)"
                >
                    {{ tab.label || tab.name }}
                </div>
            </div>
        </div>
        <keep-alive v-if="isKeepAlive">
            <slot></slot>
        </keep-alive>

        <slot v-else></slot>
    </div>
</template>

<script>
export default {
    name: 'SnTabs',
    components: {},
    props: {
        type: {
            type: String,
            default: 'container' // normal
        },
        tabs: {
            type: Array,
            default: () => []
        },
        value: {
            type: String,
            default: ''
        },
        isKeepAlive: {
            type: Boolean,
            default: true
        }
    },
    computed: {
        val: {
            get() {
                return this.value
            },
            set(val) {
                this.$emit('input', val)
            }
        },
        getTypeClass() {
            const result = [`sn-tabs-${this.type}`]
            if (this.type === 'container') {
                result.push(`app-container`)
            }

            return result
        }
    },
    data() {
        return {}
    },
    methods: {
        onTabChange(tab) {
            this.val = tab.value
            this.$emit('change', tab)
        }
    }
}
</script>

<style lang="scss">
@import "~@/styles/project.scss";

//.action {
//  display: flex;
//  align-items: center;
//  justify-content: flex-end;
//  flex: 1;
//  position: absolute;
//}

.sn-tabs-container {
  .search-form {
    margin-bottom: 0 !important;
  }

  .search-panel {
    //margin-bottom: 10px;
    background: #fff;
    display: flex;

    .search-tabs {
      display: flex;
      align-items: center;
      width: 100%;
      color: #272727;
      font-size: 14px;
      height: 45px;

      .container-active {
        color: #2f79e8;

        &:after {
          position: absolute;
          content: '';
          width: 40%;
          height: 3px;
          bottom: 0px;
          background-color: #2f79e8;
          transform: translateX(-50%);
          left: 50%;
        }
      }

      .container-tab {
        font-size: 14px;
        transition: all 0.5s;
        height: 100%;
        position: relative;
        cursor: pointer;
        padding: 0 20px;
        display: flex;
        align-items: center;
      }
    }
  }
}

.sn-tabs-normal {
  margin: 0px 0 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  color: #272727;
  font-size: 1.25rem;
  height: 35px;
  border-bottom: 1px solid #efefef;

  .normal-active {
    color: #ff726b;

    &:after {
      position: absolute;
      content: '';
      width: 55%;
      height: 1.5px;
      bottom: 2px;
      background-color: #ff726b;
      transform: translateX(-50%);
      left: 50%;
    }
  }

  .normal-tab {
    transition: all 0.5s;
    height: 100%;
    position: relative;
    cursor: pointer;
    padding: 0 20px;
    display: flex;
    align-items: center;
  }
}

.sn-tabs-autoCoal {
  position: relative;

  .search-tabs {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 3.575rem;
    font-size: 14px;
    background-color: $project-theme;
    color: #fff;

  }


  .autoCoal-active {
    font-weight: bold;
    color: #FF9639;


    &:after {
      justify-content: center;
      content: '';
      position: absolute;
      bottom: -5px;
      width: 50%;
      height: 3px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #FF9639;
    }
  }

  .autoCoal-tab {
    cursor: pointer;
    padding: 10px 20px;
    position: relative;
  }
}

</style>
