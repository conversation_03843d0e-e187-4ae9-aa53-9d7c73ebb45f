<template>
  <div class="container">
    <div class="file-imgs">
      <el-upload
        ref="upload"
        :action="action"
        :before-upload="handleBeforeUpload"
        :data="uploadData"
        :disabled="disabled"
        :drag="drag"
        :fileList="list"
        :headers="headers"
        :limit="limit"
        :listType="listType"
        :multiple="multiple"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        :on-success="handleSuccess"
        :showFileList="showFileList"
        class="upload"
        v-on="$listeners"
      >
        <i v-if="loading" slot="default" class="el-icon-loading"></i>
        <img v-else slot="default" class="upload-img" src="./img/upload.png" />
      </el-upload>

      <div v-for="(item, index) in list" :key="item.url" class="img-wrap">
        <el-image :src="item.url" class="img" fit="cover" @error="() => (item.hide = true)"></el-image>
        <div v-if="!item.hide" class="actions">
          <i class="el-icon-zoom-in"> </i>
          <el-image :preview-src-list="[item.url]" :src="item.url" class="hide-img"></el-image>
          <i class="el-icon-download" @click.stop="handleOpen(item)"></i>
          <i class="el-icon-delete" @click.stop="handleRemoveItem(item, index)"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAccessToken } from '@/utils/auth'
import { getBaseUrl } from '@/const'

// const UPLOAD_URL = process.env.VUE_APP_CENTER
export default {
  name: 'SnUploadImg',
  data() {
    return {
      upload: {},
      list: [],
      loading: false
    }
  },
  computed: {
    title() {
      return '上传图片'
    },
    action() {
      return getBaseUrl() + this.url
    },
    headers() {
      return { Authorization: getAccessToken() }
    },
    uploadData() {
      return { ...this.params }
    }
  },
  props: {
    url: {
      type: String,
      default: '/a/banner/saveAttachment'
    },
    showFileList: {
      type: Boolean,
      default: false
    },
    drag: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: () => []
    },
    limit: {
      type: Number,
      default: 999
    },
    listType: {
      type: String,
      default: 'picture-card'
    },
    params: {
      type: Object,
      default: () => ({})
    },
    beforeUpload: {
      type: Function
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  watch: {
    value: {
      handler(v) {
        this.list = v || []
      },
      deep: true
    },
    list: {
      handler(val) {
        this.$emit('input', val)
        // this.$emit('update:fileList', val)
      },
      deep: true
    }
  },

  methods: {
    handleOpen(item) {
      window.open(item.url)
    },
    handleRemoveItem(item, index) {
      const targetIndex = this.list.findIndex((val) => val.url === item.url)
      if (targetIndex !== -1) this.list.splice(targetIndex, 1)
    },
    handleBeforeUpload(file) {
      this.loading = true
      if (this.beforeUpload && typeof this.beforeUpload === 'function') {
        return this.beforeUpload(file)
      } else {
        const fileExt = file.name.replace(/.+\./, '')
        if (['jpg', 'png', 'jpeg', 'gif'].indexOf(fileExt.toLowerCase()) === -1) {
          this.$message({ showClose: true, type: 'warning', message: '只能上传图片' })
          return false
        }
      }
    },
    handleSuccess(response, file, fileList) {
      this.list = [...this.list, { url: response.data.url, id: response.data.id }]
      this.$message({ showClose: true, type: 'success', message: '上传成功' })
      this.$emit('upload-success', { file, params: this.uploadData, fileList })
      this.loading = false
    },
    handleUploadError(err, file, fileList) {
      let e = null
      try {
        e = JSON.parse(err.message).message
      } catch (error) {}
      this.$message({ showClose: true, type: 'error', message: e || '上传失败' })
      this.$emit('upload-error', { file, fileList })
      this.loading = false
    },
    handleExceed(files, fileList) {
      this.$message({ showClose: true, type: 'error', message: `文件数量不能超过${this.limit}!` })
      this.$emit('upload-exceed', { files, fileList })
    }
  }
}
</script>

<style lang="scss" scoped>
$width: 90px;
$height: 90px;
$border-radius: 7px;

.container {
  display: flex;

  .upload {
    margin-right: 10px;

    ::v-deep .el-upload--picture-card {
      border: solid 1px transparent;
      width: $width;
      height: $height;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f4f4f4;

      img {
        width: 40px;
        height: 40px;
      }

      i {
        font-size: 32px;
        color: #cdcdcd;
      }
    }
  }

  .file-imgs {
    display: flex;
    flex-flow: wrap;

    .img-wrap {
      position: relative;
      margin-right: 10px;
      width: $width;
      height: $height;
      margin-bottom: 10px;

      &:hover {
        .actions {
          opacity: 1;
          z-index: 99;
        }
      }

      .img {
        background: #f4f4f4;
        border-radius: $border-radius;
        width: 100%;
        height: 100%;
      }

      .actions {
        transition: all 0.5s;
        opacity: 0;
        z-index: -1;
        display: flex;
        border-radius: $border-radius;
        padding: 0 15px;
        position: absolute;
        justify-content: space-between;
        align-items: center;
        left: 0%;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);

        .hide-img {
          position: absolute;
          left: 0%;
          width: 20px;
          height: 20px;
          z-index: 999;
          opacity: 0;
          left: 15px;
          border-radius: $border-radius;
        }

        i {
          cursor: pointer;
          transform: scale(1.5);
          color: #fff;
        }
      }
    }
  }
}
</style>
