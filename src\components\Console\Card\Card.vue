<template>
  <section class="card" :style="{height:height}">
    <slot />
  </section>
</template>

<script>
export default {
  props: {
    height: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.card {
  width: 100%;
  display: flex;
  /*width: 100%;*/
  height: 450px;
  justify-content: space-between;
  margin: 10px 0;
}

.card:last-of-type {
  margin-bottom: 0;
}
</style>