<template>
  <section class="card-item">

    <span class="card-item-title">
      <img v-if="url" :src='url' style="width:20px;height:18px;display:inline-block;vertical-align: top;" />
      <span style="margin-left:7px">{{title}}</span> <span class="sub">{{sub}}</span>
    </span>

    <div class="card-item-slot">
      <slot></slot>
    </div>
  </section>
</template>

<script>
import { string } from 'jszip/lib/support'
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    sub: {
      type: String,
      default: '',
    },
    url: {
      type: String,
      default: '',
    },
  },
}
</script>

<style lang="scss" scoped>
.card-item {
  background: #fff;
  flex: 1;
  display: flex;
  flex-flow: column nowrap;
  padding: 0px 10px;

  &-title {
    color: #3d3d3d;
    font-size: 15px;
    padding: 10px 3px 10px 0px;
    display: inline-block;
    .sub {
      font-size: 12px;
      // margin-left: 3px;
      color: #9f9f9f;
    }
  }

  &:nth-of-type(2) {
    margin-left: 10px;
  }

  &-slot {
    display: flex;
    width: 100%;
    height: 100%;
  }
}
</style>