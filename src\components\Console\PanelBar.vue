<template>
  <div class="panel">
    <div class="panel-title">
      <img v-if="type==='panel'" src="@/assets/console/panel_icon.png" />
      <img v-else-if="type==='pounds'" src="@/assets/console/pounds_icon.png" />
      <img :src="src" v-else />
      <span>{{title}}</span>
      <span>{{subtitle}}</span>
    </div>
    <div class="panel-slot">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: '',
    },
    src: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    subtitle: {
      type: String,
      default: '',
    },
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
.panel {
  // margin-bottom: 5px;
  height: 44px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  padding: 1.25rem;
  align-items: center;
  overflow: hidden;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);

  &-title {
    display: flex;
    align-items: center;
    img {
      width: 19px;
    }
    span {
      margin-left: 10px;

      &:first-of-type {
        font-size: 15px;
      }

      &:last-of-type {
        font-size: 12px;
        color: #9f9f9f;
      }
    }
  }

  &-slot {
    ::v-deep .el-input__prefix {
      right: 5px;
      left: auto;
    }

    ::v-deep .el-input--prefix .el-input__inner {
      padding-left: 10px;
    }
  }
}
</style>