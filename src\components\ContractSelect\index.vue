<template>
  <el-cascader v-model="val" :options="contractEntity.options" :props="contractEntity.props" filterable clearable
               placeholder="请选择合同" @change="handleAreaChange($event)" v-if="type==='采购'">
    <slot />
  </el-cascader>
  <el-cascader v-model="val" :options="contractEntityV.options" :props="contractEntityV.props" filterable clearable
               placeholder="请选择合同" @change="handleAreaChange($event)" v-else>
    <slot />
  </el-cascader>
</template>
<script>
import { chooseContract, getCustomerContract } from '@/api/quality'
export default {
  name: 'CategorySelect',
  data() {
    return {
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      contractEntityV: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      val: ''
    }
  },
  inject: ['filterOption'],
  props: {
    value: {
      required: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    type: {
      type: String,
      default: '采购'
    }
  },
  async created() {
    console.log('99999')
    console.log(this.type)
    // this.list = await productList()
    if (this.type === '采购') {
      this.getContract()
    } else if (this.type === '销售') {
      this.getContractV()
    }
  },
  methods: {
    async handleAreaChange(nowVvlue) {
      //   console.log(nowVvlue)
      //   const form = this.filterContractItem(nowVvlue, 'contractEntity')
      //   console.log(form)
      this.$emit('update:value', nowVvlue[1])
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    // 获取采购合同选择接口
    async getContract() {
      try {
        const { data } = await chooseContract()
        this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'displayName', supplierId: 'id' } })
      } catch (error) {}
    },

    // 获取销售合同选择接口
    async getContractV() {
      const { data } = await getCustomerContract()
      // 供应商名称返回id用来查询
      this.contractEntityV.options = this.formatContractV({ data, key: { customerName: 'displayName', customerId: 'id' } })
      console.log('合同列表')
      console.log(this.contractEntityV.options)
    },
    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContractV({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },

    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     * @target {contract|contractEntityForm}<String>
     */
    filterContractItemV(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },

    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    }
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss" scoped>
.select_input {
  width: 100%;
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }
}
::v-deep .el-input__inner {
  padding-left: 24px !important;
}
::v-deep .el-input__prefix {
  cursor: pointer;
  width: 16px !important;
  line-height: 30px !important;
  height: 16px !important;
  font-size: 16px;
  left: 2px !important;
  right: 0 !important;
}
</style>
