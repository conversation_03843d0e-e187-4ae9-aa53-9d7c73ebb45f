<template>
  <div class="confirm">
    <div class="el-notification right" v-show="visible">
      <i class="el-notification__icon el-icon-bell" style="color: #E6A23C;font-size: 24px;"></i>
      <div class="el-notification__group is-with-icon" style="width:100%;">
        <h2 class="el-notification__title">新消息（ <span>{{ totalCount }}</span> ）</h2>
        <div style="height:50px;overflow: scroll;">
          <div class="el-notification__content">
            <div style="margin:15px 0px;">
              <div v-for="(item,index) in listdata" :key="index" class="contebox" @click="goAlarmList(item)">
                <!-- <div v-if="item.reviewType=='settlePay:isFinancereview'">
                  <el-tag :key="item.typeName" type="" effect="dark">
                    {{item.typeName}}
                  </el-tag>
                </div>
                <div v-if="item.reviewType=='applyPay:pay'">
                  <el-tag :key="item.typeName" type="" effect="dark">
                    {{item.typeName}}
                  </el-tag>
                </div>
                <div v-if="item.reviewType=='contractBuy:isFinancereview'">
                  <el-tag :key="item.typeName" type="" effect="dark">
                    {{item.typeName}}
                  </el-tag>
                </div> -->
                <div>
                  <el-tag :key="item.typeName" type="" size="medium" effect="dark" v-if="item.messageType=='调价'">
                    {{ item.messageType }}
                  </el-tag>
                  <el-tag :key="item.typeName" type="success" size="medium" effect="dark" v-if="item.messageType=='合同'">
                    {{ item.messageType }}
                  </el-tag>
                  <el-tag :key="item.typeName" color="#33cad9" style="border:solid 1px #33cad9" size="medium" effect="dark"
                          v-if="item.messageType=='运费'">
                    {{ item.messageType }}
                  </el-tag>

                  <el-tag :key="item.typeName" type="danger" size="medium" effect="dark" v-if="item.messageType=='付款单'">
                    {{ item.messageType }}
                  </el-tag>
                  <el-tag :key="item.typeName" type="warning" size="medium" effect="dark" v-if="item.messageType=='付款结算'">
                    {{ item.messageType }}
                  </el-tag>
                  <el-tag :key="item.typeName" type="warning" size="medium" effect="dark" v-if="item.messageType=='收款结算'">
                    {{ item.messageType }}
                  </el-tag>

                  <el-tag :key="item.typeName" type="warning" size="medium" effect="dark" v-if="item.messageType=='销售运费结算'">
                    {{ item.messageType }}
                  </el-tag>
                </div>
                <div class="context">{{ item.messageContent }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="el-notification__closeBtn el-icon-close" @click="cancle"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { viewPushMessage } from '@/api/user'
import { getIscl } from '@/utils/auth'

export default {
  data() {
    return {
      listdata: [
        {
          applyDate: '2023-06-09',
          applyName: '',
          applyType: '',
          type: 'SettlePay',
          createBy: '',
          createDate: null,
          ext: '',
          id: '1666975606765588482',
          messageContent: '山西离柳焦煤集团销售有限公司',
          remarks: '',
          reviewType: 'settlePay:isFinancereview',
          state: '',
          typeName: '付款结算-财务负责人',
          updateBy: '',
          updateDate: null
        }
      ],
      visible: false,
      title: '', // 标题
      position: 'top-right', // 自定义弹出位置
      store: '',
      router: '',

      totalCount: '',
      isclear: false
    }
  },
  watch: {
    visible(newval, oldvalue) {
      if (newval == false) {
        // console.log('4444')
        this.cancle()
      }
    },
    isclear(newval, oldvalue) {
      if (newval == null) {
        this.cancle()
      }
    }
  },
  created() {
    // this.visible = localStorage.getItem('visible')
  },
  methods: {
    async goAlarmList(item) {
      if (item.viewType === 'MANUAL') {
        //消息查看
        // console.log('消息查看')
        const res = await viewPushMessage({ id: item.id })
        // console.log(res)
      }
      let type = ''
      if (item.type == 'SettlePay') {
        //付款结算
        if (item.reviewType == 'settlePay:ismanagerreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settlePay:isFinancereview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settlePay:isBossreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == '') {
          type = ''
        }
        let data = { parent: `finance`, child: `settlement`, Pagename: `settlePay`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }

      if (item.type == 'SettleRecv') {
        //收款结算
        // if (item.reviewType == 'settleRecv:ismanagerreview') {
        //   type = 'NEW'
        // } else if (item.reviewType == 'settleRecv:isFinancereview') {
        //   type = 'PASS'
        // } else if (item.reviewType == '') {
        //   type = ''
        // }
        if (item.reviewType == 'settleRecv:ismanagerreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settleRecv:isFinancereview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settleRecv:isBossreview') {
          type = 'NEW,PASS_FINANCE'
        }
        let data = {
          parent: `finance`,
          child: `settlement`,
          Pagename: `settleRecv`,
          params: { id: item.refId, type: type }
        }
        this.handleToNamePage(data)
      }

      if (item.type == 'SettleRecvFreight') {
        //销售运费结算
        if (item.reviewType == 'settleRecvFreight:ismanagerreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settleRecvFreight:isFinancereview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settleRecvFreight:isBossreview') {
          type = 'NEW,PASS_FINANCE'
        }
        let data = {
          parent: `finance`,
          child: `settlement`,
          Pagename: `settleRecvFreight`,
          params: { id: item.refId, type: type }
        }
        this.handleToNamePage(data)
      }

      if (item.type == 'ApplyPay') {
        //付款单
        if (item.reviewType == 'applyPay:ismanagerreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'applyPay:isFinancereview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'applyPay:isBossreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'applyPay:pay') {
          type = 'PASS'
        } else if (item.reviewType == '') {
          type = ''
        }
        let data = { parent: `finance`, child: `payment`, Pagename: `applyPay`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }

      if (item.type == 'ContractBuy') {
        let Auditstatus = ''
        //采购合同
        if (item.reviewType == 'contractBuy:ismanagerreview') {
          //  去审核(总经理)
          Auditstatus = 'MANAGER'
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'contractBuy:isFinancereview') {
          //去审核(财务)
          Auditstatus = 'FINANCE'
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'contractBuy:isFinanceLeaderreview') {
          //去审核(财务负责人)
          Auditstatus = 'FINANCE_LEADER'
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'contractBuy:isBossreview') {
          //     去审核(董事长)
          Auditstatus = 'isBossreview'
          type = 'NEW,PASS_FINANCE'
        } else if (
          item.reviewType == 'contractBuy:priceFactoryManagerWaitforReviewed' ||
          item.reviewType == 'contractBuy:priceManagerWaitforReviewed' ||
          item.reviewType == 'contractBuy:priceBossWaitforReviewed'
        ) {
          Auditstatus = 'AdjustPriceReview'
          type = 'NEW,PASS_FINANCE'
        } else if (
          item.reviewType == 'contractBuy:carriageFactoryManagerWaitforReviewed' ||
          item.reviewType == 'contractBuy:carriageManagerWaitforReviewed' ||
          item.reviewType == 'contractBuy:carriageBossWaitforReviewed'
        ) {
          Auditstatus = 'freightReview'
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == '') {
          Auditstatus = ''
          type = ''
        }

        let data = {
          parent: `base`,
          child: `buy`,
          Pagename: `contractBuy`,
          params: { id: item.refId, type: type, Auditstatus: Auditstatus }
        }
        this.handleToNamePage(data)
      }
      if (item.type == 'ContractSell') {
        //销售合同
        if (item.reviewType == 'contractSell:ismanagerreview') {
          type = 'PASS_FINANCE'
        } else if (item.reviewType == 'contractSell:isFinancereview') {
          type = 'NEW'
        } else if (item.reviewType == '') {
          type = ''
        }

        let data = { parent: `base`, child: `buy`, Pagename: `contractSell`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }
      if (item.type == 'WeightHouseNotice') {
        //销磅房通知
        if (item.reviewType == 'weightHouseNotice:isnoticePr') {
          type = 'NEW'
        } else if (item.reviewType == 'weightHouseNotice:send') {
          type = 'PASS'
        } else if (item.reviewType == '') {
          type = ''
        }
        let data = { parent: `finance`, Pagename: `weightHouseNotice`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }
      if (item.type == 'InOrder') {
        //入库
        if (item.reviewType == 'inOrder:WarehousingWaitforreviewed') {
          type = 'NEW'
        } else if (item.reviewType == '') {
          type = ''
        }
        let data = { parent: `material`, Pagename: `inOrder`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }
      if (item.type == 'OutOrder') {
        //出库
        if (item.reviewType == 'outOrder:ExwarehouseWaitforreviewed') {
          type = 'NEW'
        } else if (item.reviewType == '') {
          type = ''
        }
        let data = { parent: `material`, Pagename: `outOrder`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }
    },

    // 通过name去跳转类似post
    handleToNamePage(option) {
      const { parent, child, Pagename, params } = option
      const route = this.showRoutes.find((route) => route.name === parent)
      route.params = option.params
      this.store.dispatch('changeRoute', route)
      this.store.dispatch('changeChildRoute', { parent: child, child: '' })
      this.router.replace({ name: Pagename, params })
    },
    cancle() {
      this.visible = false
    },
    //初始化弹窗
    openDialog(router, store) {
      this.visible = true
      this.store = store
      this.router = router
    }
  },
  mounted() {
    this.showRoutes = JSON.parse(localStorage.getItem('showRoutes'))
    this.isclear = getIscl()

    let that = this
    setTimeout(function () {
      that.cancle()
    }, 30 * 1000)
  },
  beforeDestroy() {
  }
}
</script>
<style scoped>
.el-notification__group {
  margin-left: 13px;
  margin-right: 8px;
}

.el-notification__content {
  font-size: 14px;
  line-height: 21px;
  margin: 0px 0 0;
  color: #606266;
  text-align: justify;
}

.el-notification__title {
  font-weight: 700;
  font-size: 16px;
  color: #303133;
  margin-top: 5px;
}

.el-notification__closeBtn {
  position: absolute;
  top: 18px;
  right: 15px;
  cursor: pointer;
  color: #909399;
  font-size: 16px;
}

.contebox {
  display: flex;
  padding-bottom: 20px;
  cursor: pointer;
}

.context {
  line-height: 28px;
  padding-left: 10px;
  color: #5194f8;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: Nowrap;
  cursor: pointer;
}

.el-notification.right {
  right: 16px;
}

.el-notification {
  display: -ms-flexbox;
  display: flex;
  width: 750px;
  padding: 14px 26px 14px 13px;
  border-radius: 8px;
  box-sizing: border-box;
  border: 1px solid #ebeef5;
  position: fixed;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s, transform 0.3s, left 0.3s, right 0.3s, top 0.4s, bottom 0.3s;
  overflow: hidden;
  bottom: 16px;
  z-index: 2000;
}
</style>
