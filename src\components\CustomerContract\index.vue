<template>
  <el-cascader v-model="val" :options="options" :props="props" :clearable="clearable" :placeholder="placeholder"
               @change="handleChange" />
</template>
<script>
import { supplierContract } from '@/api/public'

export default {
  name: 'customerContract',
  data() {
    return {
      val: [],
      options: [],
      flag: true
    }
  },
  props: {
    value: {
      type: [Array, String],
      default() {
        return []
      }
    },
    type: {
      type: String,
      default: 'customer'
    },
    clearable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择合同'
    },
    props: {
      type: Object,
      default() {
        return {
          label: 'supplierName',
          value: 'supplierId',
          children: 'contractBuyList'
        }
      }
    }
  },
  created() {

  },
  methods: {
    // 获取合同选择接口
    async getContract() {
      try {
        const { data } = await supplierContract()
        this.options = this.formatContract({ data, key: { supplierName: 'code', supplierId: 'id' } })
      } catch (error) {
        // console.log(error)
      }
    },
    handleChange(ids) {
      const contract = this.searchContract(ids)
      this.$emit('update:value', ids || [])
      this.$emit('change', contract || {})
    },
    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map(list => {
          list.contractBuyList.forEach(item => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    /**
     * @ids {[id:String,id:String]} 第一是父节点id,第二个是子节点id
     * @value 可以是,也可以是supplierId
     * 如果传入数组就会找到合约,如果传入合约id那么生成ids
     */
    searchContract(value) {
      if (Array.isArray(value) && value.length) {
        return this.options.find(item => item.supplierId === value[0]).contractBuyList.find(item => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this.options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },

    filterContract(keywords) {
      if (Array.isArray(keywords)) {
        return keywords
      }
      const val = []
      for (const list of this.options) {
        for (const item of list.contractBuyList) {
          if (item.supplierId === keywords) {
            val.push(list.supplierId, keywords)
            return val
          }
        }
      }
      return val
    }
  },
  watch: {
    value: {
      async handler(v) {
        if (Array.isArray(v)) {
          this.val = v
          return
        }
        const ids = []
        if (!this.options.length) {
          const { data } = await supplierContract()
          this.options = this.formatContract({ data, key: { supplierName: 'code', supplierId: 'id' } })
        }
        for (const list of this.options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === v) {
              ids.push(list.supplierId, v)
            }
          }
        }
        this.val = ids
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss" scoped>
.customerContract {
  width: 100%;
}
</style>
