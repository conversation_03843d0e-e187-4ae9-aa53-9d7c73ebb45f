<template>
  <el-date-picker type="daterange" ref="aim" v-model="pickerDate" :editable="false" unlink-panels range-separator="-"
                  start-placeholder="开始日期" end-placeholder="结束日期" :value-format="valueFormat" :clearable="clearable"
                  @change="change($event)" @click.native="addCoverDefaultHandler" class="customPickerDate">
    <!-- :picker-options="pickerOptions" -->
  </el-date-picker>
</template>

<script>
import { parseTime } from '@/filters'

export default {
  name: 'DateRanger',
  data() {
    return {
      pickerDate: [],
      isLeapYear: false,
    }
  },
  props: {
    start: {
      // 如果默认时间有值 请在父组件传入start
      required: true,
    },
    end: {
      // 如果默认时间有值 请在父组件传入end
      required: true,
    },
    max: {
      type: Number,
      default: 365,
    },
    isDefault: {
      // 是否有默认值
      type: [<PERSON>olean],
      default: true,
    },
    clearable: {
      // 是否清空
      type: [Boolean],
      default: false,
    },
    valueFormat: {
      type: String,
      default: `yyyy-MM-dd`,
    },
  },
  computed: {
    pickerOptions() {
      // const that = this
      const max = this.max * 100
      return {
        disabledDate(time) {
          //  return time.getTime() > Date.now() || time.getTime() < Date.now() - 3600 * 1000 * 24 * max
          // const currentMonth = new Date().getMonth() + 1
          // const map = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
          // let days
          // if (currentMonth === 2 && that.isLeapYear) {
          //     days = 29
          // } else {
          //     days = map[currentMonth - 1]
          // }
          return (
            time.getTime() > Date.now() + 3600 * 1000 * 24 * 30 * max || time.getTime() < Date.now() - 3600 * 1000 * 24 * 15 * max
          )
        },
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              const end = new Date(`${parseTime(new Date(), '{y}-{m}-{d}')}`)
              const start = new Date(`${parseTime(new Date(), '{y}-{m}-{d}')}`)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '昨天',
            onClick(picker) {
              let end = new Date()
              let start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24)
              end.setTime(end.getTime() - 3600 * 1000 * 24)
              end = new Date(`${parseTime(new Date(end), '{y}-{m}-{d}')}`)
              start = new Date(`${parseTime(new Date(start), '{y}-{m}-{d}')}`)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '前天',
            onClick(picker) {
              let end = new Date()
              let start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 2)
              end.setTime(end.getTime() - 3600 * 1000 * 24 * 2)
              end = new Date(`${parseTime(end, '{y}-{m}-{d}')}`)
              start = new Date(`${parseTime(start, '{y}-{m}-{d}')}`)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近三日',
            onClick(picker) {
              let end = new Date()
              let start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 2)
              end.setTime(end.getTime())
              end = new Date(`${parseTime(end, '{y}-{m}-{d}')}`)
              start = new Date(`${parseTime(start, '{y}-{m}-{d}')}`)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近七日',
            onClick(picker) {
              let end = new Date()
              let start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
              end.setTime(end.getTime())
              end = new Date(`${parseTime(end, '{y}-{m}-{d}')}`)
              start = new Date(`${parseTime(start, '{y}-{m}-{d}')}`)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近三十日',
            onClick(picker) {
              let end = new Date()
              let start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29)
              end.setTime(end.getTime())
              end = new Date(`${parseTime(end, '{y}-{m}-{d}')}`)
              start = new Date(`${parseTime(start, '{y}-{m}-{d}')}`)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近六十日',
            onClick(picker) {
              let end = new Date()
              let start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 59)
              end.setTime(end.getTime())
              end = new Date(`${parseTime(end, '{y}-{m}-{d}')}`)
              start = new Date(`${parseTime(start, '{y}-{m}-{d}')}`)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近九十日',
            onClick(picker) {
              let end = new Date()
              let start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 89)
              end.setTime(end.getTime())
              end = new Date(`${parseTime(end, '{y}-{m}-{d}')}`)
              start = new Date(`${parseTime(start, '{y}-{m}-{d}')}`)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '本周',
            onClick(picker) {
              const map = [7, 1, 2, 3, 4, 5, 6]
              let end = new Date()
              let start = new Date()
              start.setTime(new Date().getTime() - 3600 * 1000 * 24 * (map[new Date().getDay()] - 1))
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 6)
              end = new Date(`${parseTime(end, '{y}-{m}-{d}')}`)
              start = new Date(`${parseTime(start, '{y}-{m}-{d}')}`)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '上周',
            onClick(picker) {
              const map = [7, 1, 2, 3, 4, 5, 6]
              let end = new Date()
              let start = new Date()
              start.setTime(new Date().getTime() - 3600 * 1000 * 24 * (map[new Date().getDay()] - 1) - 3600 * 1000 * 24 * 7)
              end.setTime(new Date().getTime() - 3600 * 1000 * 24 * (map[new Date().getDay()] - 1) - 3600 * 1000 * 24)
              end = new Date(`${parseTime(end, '{y}-{m}-{d}')}`)
              start = new Date(`${parseTime(start, '{y}-{m}-{d}')}`)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '本月',
            onClick(picker) {
              const currentMonth = new Date().getMonth() + 1
              const map = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
              let days
              if (currentMonth === 2 && this.isLeapYear) {
                days = 29
              } else {
                days = map[currentMonth - 1]
              }
              const start = new Date(`${new Date().getFullYear()}/${new Date().getMonth() + 1}/1`)
              const end = new Date(`${new Date().getFullYear()}/${new Date().getMonth() + 1}/${days - 1}`)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '上月',
            onClick(picker) {
              const currentMonth = new Date().getMonth() - 1 === -1 ? 12 : new Date().getMonth()
              const map = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
              let days
              if (currentMonth === 2 && this.isLeapYear) {
                days = 29
              } else {
                days = map[currentMonth - 1]
              }
              const start = new Date(`${new Date().getFullYear()}/${currentMonth}/1`)
              const end = new Date(`${new Date().getFullYear()}/${currentMonth}/${days - 1}`)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      }
    },
  },
  mounted() {
    let year = new Date().getFullYear()
    this.isLeapYear = (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0
    if (this.isDefault) {
      const start = ''
      const end = ''
      this.$nextTick(() => {
        this.change([start, end])
      })
    }
    if (this.start && this.end) {
      this.pickerDate = [this.start, this.end]
    }
  },
  beforeDestroy() {
    if (this.$refs['aim'].picker) {
      this.$refs['aim'].picker.$children.slice(0, 2).forEach((vNode) => {
        vNode.$off('pick', this.addCoverDefaultCallBack)
      })
      this.$refs['aim'].picker.$destroy()
    }
  },
  watch: {
    start(val) {
      if (val) {
        this.pickerDate = [val, this.end]
      }
    },
    end(val) {
      if (!val) {
        // 有默认值，不可清空
        if (this.isDefault && !this.clearable) {
          const start = parseTime(new Date() - 3600 * 1000 * 24 * 6, '{y}-{m}-{d}')
          const end = parseTime(new Date(), '{y}-{m}-{d}')
          this.pickerDate = [start, end]
        } else {
          this.pickerDate = []
        }
      } else {
        this.pickerDate = [this.start, this.end]
      }
    },
  },
  methods: {
    change(val) {
      let start = ''
      let end = ''
      if (val) {
        ;[start, end] = val
      }
      if (start) {
        start += ' 00:00:00'
      }
      if (end) {
        end += ' 23:59:59'
      }
      this.$emit('update:start', start)
      this.$emit('update:end', end)
    },
    /**
     *追加覆盖默认操作
     */
    addCoverDefaultHandler() {
      this.$nextTick(() => {
        this.$refs['aim'].picker.$children.slice(0, 2).forEach((vNode) => {
          vNode.$off('pick', this.addCoverDefaultCallBack)
          vNode.$on('pick', this.addCoverDefaultCallBack)
        })
      })
    },
    /**
     *追加覆盖默认回调
     */
    addCoverDefaultCallBack({ minDate, maxDate }) {
      if (minDate && maxDate) {
        const format = parseTime(maxDate)
        const index = format.indexOf(' 00:00:00')
        if (index > -1) {
          setTimeout(() => {
            this.$refs['aim'].picker.maxDate = new Date(`${format.slice(0, index)}`)
          }, 100)
        }
      }
    },
  },
}
</script>
