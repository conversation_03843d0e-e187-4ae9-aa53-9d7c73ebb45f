<template>
  <!-- :picker-options="optionsDisable" -->
  <el-date-picker v-if="!isset" ref="date-select" style="width: 100%" v-model="pickerDate" :value-format="format"
                  :type="type"
                  :format="formats" :disabled="disabled" @change="change($event)" v-bind="$attrs" clearable>
  </el-date-picker>

  <el-date-picker v-else ref="date-select" style="width: 100%" v-model="pickerDate" :value-format="format" :type="type"
                  :format="formats" :picker-options="optionsDisable" :disabled="disabled" @change="change($event)"
                  v-bind="$attrs"
                  clearable>
  </el-date-picker>

</template>

<script>
// import {parseTime} from '@/filters'
// const today = parseTime(new Date(), '{y}-{m}-{d}')
export default {
  name: 'DateSelect',
  data() {
    return {
      pickerDate: '',
      // optionsDisable: {
      //   disabledDate(time) {
      //     return time.getTime() > Date.now()
      //   },
      // },
      optionsDisable: {}
    }
  },
  props: {
    type: {
      type: String,
      default: 'date'
    },
    value: {
      type: String
    },
    disabled: {
      type: [Boolean],
      default: false
    },
    formats: {
      type: String,
      default: 'yyyy-MM-dd'
    },
    format: {
      type: String,
      default: 'yyyy-MM-dd'
    },
    isset: {
      type: Boolean,
      default: false
    },
    defaultvalue: {
      type: String,
      default: ''
    }
  },
  created() {
    if (this.isset) {
      this.optionsDisable = {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  mounted() {
    // console.log(this.defaultvalue)
    // console.log(this.value)

    if (this.defaultvalue) {
      this.pickerDate = this.defaultvalue
    } else {
      if (!this.value) {
        // this.pickerDate = today
        // this.change(this.pickerDate)
      } else {
        if (/T/.test(this.value)) {
          this.pickerDate = this.value.replace(/T/, ' ')
        } else {
          this.pickerDate = this.value
        }
      }
    }
  },

  watch: {
    pickerDate(val) {
      if (!val) {
        this.change(this.pickerDate)
      }
    },
    value(val) {
      console.log('时间')
      console.log(val)
      this.pickerDate = val
    }
    // isset(val) {
    //   console.log(val)
    // },
  },
  methods: {
    change(val) {
      this.$emit('input', val)
    }
  },
  beforeDestroy() {
    if (this.$refs['date-select'].picker) {
      this.$refs['date-select'].picker.$destroy()
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
