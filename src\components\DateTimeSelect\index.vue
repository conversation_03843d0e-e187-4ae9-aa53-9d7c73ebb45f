<template>
  <el-date-picker ref="date-select" style="width: 100%" type="datetime" v-model="pickerDate" :disabled="disabled"
                  :placeholder="placeholder" :default-time="defaultTime" :value-format="format" :format="format"
                  @change="change($event)">
  </el-date-picker>
</template>

<script>
// import {parseTime} from '@/filters'
// const today = parseTime(new Date(), '{y}-{m}-{d}')
export default {
  name: 'DateTime',
  data() {
    return {
      pickerDate: ''
    }
  },
  props: {
    value: {
      type: String
    },
    disabled: {
      type: [Boolean],
      default: false
    },
    format: {
      type: String,
      default: 'yyyy-MM-dd HH:mm:ss'
    },
    placeholder: {
      type: String,
      default: ''
    },
    defaultTime: {
      type: String,
      default: '12:00:00'
    }
  },
  mounted() {
    if (!this.value) {
      // this.pickerDate = today
      // this.change(this.pickerDate)
    } else {
      this.pickerDate = this.value.replace(/T/, ' ')
    }
  },
  watch: {
    'pickerDate'(val) {
      this.$emit('update:value', val)
      if (!val) {
        this.change(this.pickerDate)
      }
    },
    'value'(val) {
      this.pickerDate = this.value
    }
  },
  methods: {
    change(val) {
      this.$emit('input', val)
    }
  },
  beforeDestroy() {
    if (this.$refs['date-select'].picker) {
      this.$refs['date-select'].picker.$destroy()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input__icon {
  transform: scale(1.2);
  line-height: 30px;
}
</style>