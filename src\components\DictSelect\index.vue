<template>
  <el-select v-if="showType === 'select'" class="dictSelected" :value="value" :filterable="filterable" :placeholder="placeholder"
             @input="change($event)" :clearable="clearable" :disabled="disabled" style="flex: 1">
    <el-option v-for="item in list" :key="item.value" :label="item.name" repair :value="item.code" :disabled="check(item.value)">
      <span>{{ item.name }}</span>
    </el-option>
  </el-select>
  <el-radio-group v-else-if="showType === 'radio'" :value="value">
    <el-radio :label="item.value" :key="item.value" @change="change(item.value)" :value="item.name" v-for="item in list">
      <span>{{ item.name }}</span>
    </el-radio>
  </el-radio-group>
  <el-switch v-else-if="showType === 'switch'" :value="value" :size="size" @change="change($event)" :active-value="list[0].value"
             :active-text="list[0].name" :inactive-text="list[1].name" :inactive-value="list[1].value">
  </el-switch>
</template>

<script>
export default {
  name: 'DictSelect',
  computed: {
    list() {
      return window.dict[this.type] || []
    },
    size() {
      return this.list.some((v) => v.name.length > 1) ? 'large' : 'default'
    },
  },
  props: {
    value: {
      required: true,
    },
    name: {
      type: String,
    },
    type: {
      required: true,
      type: String,
    },
    placeholder: {
      type: String,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    selectList: {
      type: Array,
      default: () => {
        return []
      },
    },
    special: {
      type: Boolean,
      default: true,
    },
  },
  mounted() {
    if (this.name) {
      this.showType = this.name
    } else {
      switch (this.list.length) {
        case 2:
          // this.showType = this.multiple ? 'checkbox' : (this.list.some(v => v.name.length <= 2) ? 'switch' : 'radio')
          if (this.special) {
            this.showType = 'select'
          } else {
            this.showType = this.multiple ? 'checkbox' : this.list.some((v) => v.name.length <= 1) ? 'switch' : 'select'
          }

          if (this.value === undefined && this.showType === 'switch') {
            this.change(this.list[1].value)
          }
          break
        case 3:
          // this.showType = this.multiple ? 'checkbox' : 'radio'
          this.showType = 'select'
          break
        default:
          break
      }
    }
  },
  data() {
    return {
      showType: 'select',
    }
  },
  methods: {
    check(item) {
      const index = this.selectList.findIndex((v) => v === item)
      return index > -1
    },
    change(val) {
      let name = ''
      this.list.filter((v) => {
        if (v.value === val) {
          name = v.name
        }
      })
      this.$emit('input', val)
      this.$emit('info', val)
      this.$emit('update:name', name)
      this.$emit('change', val)
      if (this.placeholder === '开票情况') {
        this.$emit('changeisBill', val)
      } else if (this.placeholder === '运输情况') {
        this.$emit('changeCarriageRemarks', val)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.dictSelected {
  width: 100%;

  ::v-deep.el-input__inner {
    width: 100%;
  }
}
</style>


