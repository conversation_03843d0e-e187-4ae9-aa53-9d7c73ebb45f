<template>
    <div :class="className" ref="chart" :style="{height:height,width:width}"></div>
</template>

<script>
    import echarts from 'echarts'
    import {mapGetters} from 'vuex'

    export default {
        props: {
            className: {
                type: String,
                default: 'chart'
            },
            width: {
                type: String,
                default: 'inherit'
            },
            height: {
                type: String,
                default: 'inherit'
            },
            option: {
                type: Object,
                required: true
            }
        },
        data() {
            return {
                chart: null,
                getOption: {}
            }
        },
        ...mapGetters([
            'isFold'
        ]),
        mounted() {
            this.initChart()
        },
        beforeDestroy() {
            if (!this.chart) return
            this.chart.dispose()
            this.chart = null
            window.removeEventListener('resize', this.chartResize)
        },
        methods: {
            async initChart() {
                await this.$nextTick()
                this.chart = echarts.init(this.$refs.chart)
                /**
                 * 为true时则开启合并功能
                 */
                this.chart.setOption(this.getOption, true)
                window.addEventListener('resize', this.chartResize)
            },
            chartResize() {
                this.chart.resize()
            },
            resizeLayout(delay = 500) {
                let timer = setInterval(() => {
                    this.chart.resize()
                }, 10)

                setTimeout(() => {
                    clearInterval(timer)
                }, delay)
            }
        },
        watch: {
            option: {
                handler(val) {
                    this.getOption = {...val}
                    if (this.chart != null) this.chart.setOption(this.getOption, true)
                },
                deep: true,
                immediate: true
            },
            '$store.state.sidebar.isFold'(v) {
                this.resizeLayout()
            }
        }
    }
</script>
