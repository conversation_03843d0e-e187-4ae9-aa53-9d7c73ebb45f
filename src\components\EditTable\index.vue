<template>
  <div class="edit-table">
    <hot-table ref="hot" :after-change="mergeCell" :data="val" :settings="settings" class="hot-table w-100 h-100"
               v-bind="$attrs"></hot-table>
  </div>
</template>
<script>
import { HotTable, HotColumn } from '@handsontable/vue'
import { registerLanguageDictionary, zhCN } from 'handsontable/i18n'
import { registerAllModules } from 'handsontable/registry'
import 'handsontable/dist/handsontable.full.css'

registerAllModules()
registerLanguageDictionary(zhCN)
export default {
  name: 'EditTable',
  data() {
    return {}
  },
  mounted() {
    this.initHotSetting()
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    columns: {
      type: Array,
      default: () => []
    },
    value: {
      type: Array,
      default: () => []
    },
    // defaultRows: {
    //   type: Number,
    //   default: 20,
    // },
    beforePaste: {
      type: Function
    },
    nestedHeaders: {
      type: Array,
      default: () => []
    },
    colHeaders: {
      type: Boolean,
      default: true
    },
    rowHeaders: {
      type: Boolean,
      default: true
    },
    isNestedHeaders: {
      type: Boolean,
      default: false
    }
    // mergeCellfn: {
    //   type: Function,
    // },
  },
  computed: {
    val: {
      set(val) {
        this.$emit('input', val)
      },
      get() {
        return this.value
      }
    },
    settings() {
      return {
        // 隐藏表头
        colHeaders: this.colHeaders,
        rowHeaders: this.rowHeaders,

        // nestedHeaders: [
        //     ['A', {label: 'B', colspan: 8}, 'C'],
        //     ['D', {label: 'E', colspan: 4}, {label: 'F', colspan: 4}, 'G'],
        //     ['H', {label: 'I', colspan: 2}, {label: 'J', colspan: 2}, {label: 'K', colspan: 2}, {
        //         label: 'L',
        //         colspan: 2
        //     }, 'M'],
        //     ['N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W'],
        // ],
        // data: [
        //     ['A1', 'B1', 'C1', 'D1', 'E1', 'F1', 'G1', 'H1', 'I1', 'J1'],
        //     ['A2', 'B2', 'C2', 'D2', 'E2', 'F2', 'G2', 'H2', 'I2', 'J2'],
        //     ['A3', 'B3', 'C3', 'D3', 'E3', 'F3', 'G3', 'H3', 'I3', 'J3'],
        //     ['A4', 'B4', 'C4', 'D4', 'E4', 'F4', 'G4', 'H4', 'I4', 'J4'],
        //     ['A5', 'B5', 'C5', 'D5', 'E5', 'F5', 'G5', 'H5', 'I5', 'J5'],
        // ],

        columns: this.isNestedHeaders ? undefined : this.columns,
        nestedHeaders: this.isNestedHeaders ? this.columns : undefined,
        readOnly: this.disabled,
        className: 'custom-classname',
        language: zhCN.languageCode,
        width: 'auto',
        height: 'auto',
        manualColumnResize: true,
        licenseKey: 'non-commercial-and-evaluation',
        trimWhitespace: false,

        stretchH: 'all'

        // columns: this.columns,
        // readOnly: this.disabled,
        // className: 'custom-classname',
        // language: zhCN.languageCode,
        // rowHeaders: true,
        // colWidth: 140,
        // autoColumnSize: true,
        // manualRowResize: true,
        // manualColumnResize: true,
        // width: 'auto',
        // height: 'auto',
        // manualColumnResize: true,
        // licenseKey: 'non-commercial-and-evaluation',
      }
    },
    settingsV() {
      return {
        startRows: 20,
        columns: this.columns,
        readOnly: this.disabled,
        className: 'custom-classname',
        // className: 'htMiddle htCenter',
        language: zhCN.languageCode,
        rowHeaders: true,
        width: 'auto',
        height: 'auto',
        manualColumnResize: true,
        licenseKey: 'non-commercial-and-evaluation',
        trimWhitespace: false,
        stretchH: 'all',
        nestedHeaders: this.nestedHeaders,
        rowHeights: 28,
        columnHeaderHeight: 32
      }
    }
  },
  methods: {
    mergeCell(changes) {
      if (changes) {
        changes.forEach((change) => {
          if (change[1] === 'customerName') {
            this.$emit('mergeCell', changes)
          }
        })
      }
    },
    async initHotSetting() {
      // console.log('赋值')
      await this.$nextTick()
      this.$emit('inited', this.getHotInstance())
      const _this = this
      this.getHotInstance().addHook('beforePaste', function (data, coords, copiedHeadersCount) {
        // console.log(data)
        // 处理粘贴数据里面有为空值的情况
        data.forEach((valData, i) => {
          valData.forEach((val, index) => {
            if (typeof val === 'string') {
              const newVal = val.replace(/\s+/g, '')
              valData[index] = newVal ? newVal : undefined
            }
          })
        })
        if (_this.beforePaste) {
          data = _this.beforePaste(data, coords, copiedHeadersCount)
        }
        return data
      })
    },
    async validate() {
      //校验表单
      return new Promise((resolve) => {
        const hot = this.getHotInstance()
        // 100毫秒的作用就是等待表格点击后失去焦点
        setTimeout(() => {
          hot.validateCells((result) => {
            if (!result) {
              this.$message.error('表单有中有异常数据，请检查！')
            }
            resolve(result)
          })
        }, 100)
      })
    },
    /**
     * 返回hotTable实例
     * @returns { import('handsontable/core').default }
     */
    getHotInstance() {
      return this.$refs.hot.hotInstance
    },
    addLastRow() {
      this.getHotInstance().alter('insert_row')
    },
    afterLoadData(sourceData, initialLoad, source) {
      this.getHotInstance().afterLoadData(sourceData, initialLoad, source)
    },
    // 更改表格数据
    setData(target = []) {
      this.data.forEach((item) => {
        target.forEach((i) => {
          item[i.key] = i.val
        })
      })
      this.getHotInstance().render()
    },
    // 插入行
    insertRow() {
      const hot = this.getHotInstance()
      const selected = hot.getSelected()
      if (selected) {
        const [startRow, startCol, endRow, endCol] = selected
        hot.alter('insert_row', startRow)
      }
    },
    // 删除行
    removeRow() {
      const hot = this.getHotInstance()
      const selected = hot.getSelected()
      if (selected) {
        const [startRow, startCol, endRow, endCol] = selected
        hot.alter('remove_row', startRow, endRow - startRow + 1)
      }
    }
  },
  components: {
    HotTable,
    HotColumn
  }
}
</script>

<style scoped>
::v-deep .hot-table .handsontable table,
.handsontable tbody,
.handsontable thead,
.handsontable td,
.handsontable th,
.handsontable input,
.handsontable textarea,
.handsontable div {
  position: relative;
}
.hot-table >>> .autocompleteEditor {
  height: 120px !important;
  position: fixed !important;
  z-index: 999999 !important;
  overflow: auto;
}
.edit-table {
  position: relative !important;
}
.h-100 >>> .autocompleteEditor {
  height: 80px !important;
  z-index: 999999 !important;
  overflow: auto;
}
</style>

<style lang="scss">
// hot-table
.htContextMenu {
  z-index: 99999 !important;
}

.custom-classname {
  background-color: #fff;
  width: 100%;

  textarea {
    line-height: 31px;
  }

  th {
    background-color: transparent;
  }

  thead {
    tr {
      height: 30px;

      div {
        height: 100%;
        line-height: 30px;
      }
    }

    th {
      background-color: #24c1ab !important;
      color: #fff !important;
    }
  }

  tbody {
    tr:nth-child(even) td {
      background: #37c2ce0f;
    }

    tr {
      height: 30px;

      td {
        line-height: 30px;
      }

      div {
        height: 100%;
        line-height: 30px;
      }
    }
  }
}

// .handsontable {
//   font-size: 13px;
//   font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Helvetica Neue', Arial, sans-serif;
//   font-weight: 400;
//   z-index: 9999 !important;
//   .htRight .changeType {
//     margin: 3px 1px 0 13px;
//   }
//   .collapsibleIndicator {
//     text-align: center;
//   }
// }
.edit-table {
  //width: 100%;
}
</style>
