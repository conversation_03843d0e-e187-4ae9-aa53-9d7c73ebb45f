<template>
    <div class="VHandsontable">
        <slot></slot>
        <div ref="tablemain"></div>
    </div>
</template>

<script>
import Handsontable from 'handsontable'
import 'handsontable/dist/handsontable.full.min'
import 'handsontable/dist/handsontable.full.min.css'

import {createStore, mapStates} from './store/helper'
import {renderIndexCell} from './vh-column-footer'
import {generateGroup} from './store/group'
import {deepClone} from '@/utils'
import {isDef} from '@/utils/is'
import TipModal from '@/utils/modal'

const lodash = require('lodash')
export default {
    props: {
        /**
         * 用于兼容之前的代码的flag
         */
        useRendererColumnAndEnableTotalLine: {
            type: Boolean,
            default: false
        },
        defaultSort: {
            type: Object,
            default: () => {
                return null
            }
        },
        width: {
            type: String,
            default: '100%'
            // 表格size
        },
        height: {
            type: String,
            default: '100%'
            // 表格size
        },
        rowHeaders: {
            type: Boolean,
            default: false
        },
        rowHeights: {
            // eslint-disable-next-line vue/require-prop-type-constructor
            type: Number | String,
            default: 32
            // 表格行高
        },
        colWidths: {
            // eslint-disable-next-line vue/require-prop-type-constructor
            type: Number | Array,
            default: 32
            // 列宽，数组可以指定列宽
        },
        stretchH: {
            type: String,
            default: 'all'
            // 平分列宽自适应 none|last|all
        },
        className: {
            type: String,
            default: 'htCenter htMiddle'
            // 对齐样式Horizontal: htLeft, htCenter, htRight, htJustify,Vertical: htTop, htMiddle, htBottom
        },
        currentRowClassName: {
            type: String,
            default: 'currentSelRow'
        },
        mergeCellsKey: {
            type: Array,
            default: () => {
                return []
            }
            // 合并key依据
        },
        // 列宽拖动
        manualColumnResize: {
            type: Boolean,
            default: false
        },
        nestedHeaders: {
            type: Array,
            default: () => {
                return []
            }
        },
        fixedRowsBottom: {
            type: Number,
            default: null
        },
        fixedColumnsLeft: {
            type: Number,
            default: null
        },

        countFixedRowsBottom: {
            type: Boolean,
            default: false
        },
        columnsFooter: {
            type: Object,
            default: () => null
        },
        columnSummary: {
            type: Array
        },
        beforeAutofill: {
            type: Function
        },
        afterGetRowHeader: {
            type: Function
        },
        beforePaste: {
            type: Function
        },
        afterGetColHeader: {
            type: Function
        },
        afterRemoveRow: {
            type: Function
        },
        afterSelection: {
            type: Function
        },
        beforeViewportScrollHorizontally: {
            type: Function
        },
        list: {
            type: Array,
            default: () => []
        },
        useFormula: {
            type: Boolean,
            default: false
        },
        afterChange: {
            type: Function
        },

        viewportColumnRenderingOffset: {
            type: Number
        }
    },
    computed: {
        ...mapStates({
            columns: 'columns',
            selectAll: 'selectAll',
            columnsMap: 'columnsMap',
            columnsSort: 'columnsSort',
            nestedHeaderTitle: 'nestedHeaderTitle',
            operateColumns: 'operateColumns',
            columnsFooterMap: 'columnsFooterMap',
            columnsFooterLeftMap: 'columnsFooterLeftMap'
        })
    },
    watch: {
        list: {
            async handler(v) {
                await this.$nextTick()
                this.loadData(v)
            },
            deep: true,
            immediate: true
        },
        columns: {
            handler: function (newVal) {
                if (newVal) {
                    this.initComponent()
                }
            }
        },
        columnsSort: {
            handler: function (newVal) {
                if (newVal) {
                    this.$emit('onColumnsSort', newVal)
                }
            },
            deep: true
        },
        selectAll: {
            handler: function (newVal) {
                this.selectAllChange(newVal)
            }
        },
        columnsFooter: {
            handler: function () {
                this.updateFooter()
            },
            deep: true
        },
        columnsFooterLeftMap: {
            handler: function (newVal) {
                console.log(newVal)
            },
            deep: true
        }
    },
    data() {
        this.store = createStore(this, {
            columns: [],
            selectAll: false,
            columnsMap: {},
            columnsSort: this.defaultSort || null,
            nestedHeaderTitle: [],
            operateColumns: [],
            columnsFooterMap: {},
            columnsFooterLeftMap: {}
        })
        return {
            hot: null,
            mergeCells: [],
            dataMaxNum: 0,
            dispalyNode: [],
            selectionList: []
        }
    },
    methods: {
        getHotInstance() {
            if (!this.hot) {
                throw new Error('当前没有获取到实例～')
            }
            return this.hot
        },
        getColumns() {
            return this.columns
        },
        initComponent() {
            if (this.hot) {
                this.hot.updateSettings({
                    columns: this.columns
                })
                return false
            }

            this.hot = new Handsontable(this.$refs.tablemain, {
                language: 'zh-CN', // 设置语言
                data: [],
                ...this.$props,
                columns: this.columns,
                readOnlyCellClassName: 'is-readOnly',
                cells: (columns, rows, cells) => {
                    if (this.countFixedRowsBottom && this.dataMaxNum === columns + 1) {
                        // 修改最后一行的类型 统一改成text
                        return {
                            type: null,
                            readOnly: true
                        }
                    }
                },
                nestedHeaders: this.nestedHeaders.length > 0 ? [...this.nestedHeaders, this.nestedHeaderTitle] : null,
                mergeCells: this.mergeCellsKey.length > 0,
                viewportColumnRenderingOffset: this.viewportColumnRenderingOffset,
                licenseKey: 'non-commercial-and-evaluation',
                beforeLoadData: this.beforeLoadData,
                afterLoadData: this.afterLoadData,
                afterGetColHeader: this.afterGetColHeaderFunc,
                afterGetRowHeader: this.afterGetRowHeaderFunc,
                afterChange: this.afterChangeFunc,
                afterRender: this.afterRender,
                afterViewRender: this.afterViewRender,
                beforeAutofill: this.beforeAutofillFunc,
                beforePaste: this.beforePasteFunc,
                afterRemoveRow: (...rest) => {
                    this.afterRemoveRow && this.afterRemoveRow(...rest)
                },
                afterSelection: (row, column, row2, column2, preventScrolling, selectionLayerLevel) => {
                    this.afterSelection && this.afterSelection(row, column, row2, column2, preventScrolling, selectionLayerLevel)
                },
                afterSelectionByProp: (row, column, row2, column2, preventScrolling, selectionLayerLevel) => {
                    this.afterSelectionByProp && this.afterSelectionByProp(row, column, row2, column2, preventScrolling, selectionLayerLevel)
                },
                beforeViewportScrollHorizontally: (visualColumn) => {
                    const tableEl = this.$refs.tablemain.querySelector('.wtHolder')
                    return this.beforeViewportScrollHorizontally ? this.beforeViewportScrollHorizontally(visualColumn, tableEl) : visualColumn
                }
            })

            this.$emit('init', this.hot)
            this.$emit('columnChange', this.hot)
        },
        beforePasteFunc(data, coords) {
            // 处理粘贴数据里面有为空值的情况
            data.forEach((valData, i) => {
                valData.forEach((val, index) => {
                    if (typeof val === 'string') {
                        const newVal = val.replace(/\s+/g, '')
                        valData[index] = newVal || null // 如果为空值，就设置为null 不能给undefined
                    }
                })
            })
            this.beforePaste && this.beforePaste(data, coords)
        },
        beforeAutofillFunc(selectionData, sourceRange, targetRange, direction) {
            this.beforeAutofill && this.beforeAutofill(selectionData, sourceRange, targetRange, direction)
        },
        beforeLoadData() {
        },
        afterLoadData(sourceData) {
            if (sourceData.length > 0 && this.hot) {
                let mergeCellIntance = this.hot.getPlugin('mergeCells')
                this.mergeCells.forEach((item) => {
                    mergeCellIntance.merge(item.startRow, item.startCol, item.endRow, item.endCol)
                })
            }
        },
        afterGetRowHeaderFunc(row, TH) {
            this.afterGetRowHeader && this.afterGetRowHeader(row, TH, this.columns)
        },
        afterGetColHeaderFunc(col, TH) {
            let obj = TH.querySelector('._nested-header-title')
            if (!this.nestedHeaders.length > 0 || obj) {
                if (col !== -1 && this.columns && this.columns[col].renderHeader) {
                    this.columns[col].renderHeader(col, TH)
                }
            }
            const target = this.columns[col] || {}
            if (target?.mergeRow) {
                if (TH.getAttribute('data-merge-row')) {
                    TH.style.display = 'none'
                } else {
                    TH.rowSpan = 2
                }
            }
            this.afterGetColHeader && this.afterGetColHeader(col, TH, this.columns[col], this.columns)
        },
        afterChangeFunc(changes, source) {
            this.afterChange && this.afterChange(changes, source, this.hot)
            // 设置全选、半选、全不选
            if (this.hot) {
                const list = this.hot.getSourceData()
                const hasFalse = list.find((item) => !item.seletextction)
                const hasTrue = list.find((item) => item.seletextction)
                if (hasFalse && hasTrue) {
                    this.store.commit('setSelectAll', null)
                    this.emitSelectionChange(list)
                } else if (hasFalse && !hasTrue) {
                    this.store.commit('setSelectAll', false)
                } else if (!hasFalse && hasTrue) {
                    this.store.commit('setSelectAll', true)
                }
            }
        },

        getSelectList(onlyUUid = true) {
            const selectionList = [...this.selectionList]
            if (onlyUUid) {
                return selectionList.map(item => item.uuid)
            }
            return selectionList
        },
        emitSelectionChange(list) {
            const selList = list.filter((item) => {
                return item.seletextction
            })
            this.selectionList = selList
            this.$emit('selection-change', selList)
        },

        afterRender(isForced) {
            /**
             * 底部计算时处理视图逻辑
             * */
            if (isForced && this.countFixedRowsBottom) {
                // let dom = null
                this.dispalyNode.forEach((item) => {
                    item.style.visibility = null
                    if (item.childNodes) {
                        item.childNodes.forEach((_item) => {
                            _item.style.display = null
                        })
                    }
                })
                this.dispalyNode = []
                renderIndexCell(this.$el, this.dataMaxNum, this.dispalyNode, this.store)
                this.updateFooter()
            }
        },
        afterViewRender(isForced) {
            if (this.columnsFooterLeftMap && this.columnsFooterLeftMap._index) {
                this.columnsFooterLeftMap._index.innerText = '合计'
            }
            if (isForced && this.countFixedRowsBottom) {
                this.dispalyNode.forEach((item) => {
                    item.style.visibility = null
                    if (item.childNodes) {
                        item.childNodes.forEach((_item) => {
                            _item.style.display = null
                        })
                    }
                })
                this.dispalyNode = []
                renderIndexCell(this.$el, this.dataMaxNum, this.dispalyNode, this.store)
                this.updateFooter()
            }
        },
        updateFooter() {
            lodash.forIn(this.columnsFooter, (value, key) => {
                !lodash.isEmpty(this.columnsFooterMap) && this.columnsFooterMap[key] && (this.columnsFooterMap[key].innerHTML = value)
                !lodash.isEmpty(this.columnsFooterLeftMap) && this.columnsFooterLeftMap[key] && (this.columnsFooterLeftMap[key].innerText = value)
            })
        },
        loadData(sourceData) {
            if (this.countFixedRowsBottom && !sourceData.find((item) => item.isTotalColumn)) {
                let last = {
                    isTotalColumn: true
                }
                lodash.forIn(sourceData[0], (value, key) => {
                    last[key] = null
                })
                sourceData.push(last)
                // sourceData = [...sourceData, last]
            }

            this.dataMaxNum = sourceData.length
            if (this.mergeCellsKey.length > 0) {
                this.unmerge()
                let list = lodash.sortBy(sourceData, this.mergeCellsKey)

                list.forEach((item, index) => {
                    item['seletextction'] = this.selectAll ? this.selectAll : item['seletextction']
                    item['rowIndex'] = index
                    this.operateColumns.forEach((_key) => {
                        item[_key] = null
                    })
                })

                let mergeCells = []
                if (list.length > 0) {
                    generateGroup(list, this.mergeCellsKey, mergeCells, this.columnsMap, 0)
                    this.mergeCells = mergeCells
                }
                this.hot.loadData(list)
            } else {
                // sourceData.forEach((item, index) => {})
                this.hot.loadData(sourceData)
            }
        },
        unmerge() {
            if (this.mergeCells.length > 0 && this.hot) {
                let mergeCellIntance = this.hot.getPlugin('mergeCells')
                mergeCellIntance.clearCollections()
            }
        },
        async validate() {
            return new Promise((resolve) => {
                const hot = this.getHotInstance()
                // 100毫秒的作用就是等待表格点击后失去焦点
                setTimeout(() => {
                    hot.validateCells((result) => {
                        if (!result) {
                            TipModal.msgError('表单有中有异常数据，请检查！')
                        }
                        resolve(result)
                    })
                }, 100)
            })
        },

        selectAllChange(selectAll) {
            if (selectAll === null) return
            const list = this.hot.getSourceData()
            list.forEach((item) => {
                item['seletextction'] = selectAll
            })
            this.loadData(list)
            this.emitSelectionChange(list)
        },
        getData() {
            const data = this.hot.getSourceData()
            return data.filter((item) => {
                return !item.isTotalColumn
            })
        },

        /**
         * 过滤掉空数据，校验数据
         * @param target 提供的车牌列表
         * @param isSubmit 是否提交 主要用于控制是否校验车牌号 true时抛出Error
         */
        getFormatEditData(targetList = [], targetColumns = [], callback) {
            const getExcludeDirtyList = (target = []) => {
                const list = deepClone(target)
                list.forEach((v) => {
                    for (const key in v) {
                        if ([null, ''].includes(v[key])) {
                            delete v[key]
                        }
                    }
                })
                return list
            }

            const list = getExcludeDirtyList(targetList)

            const resultList = []

            // 获取需要校验的字段
            const validateList = targetColumns
                .filter((col) => isDef(col.formRequire))
                .map((v) => {
                    return {key: v.data, title: v.title}
                })

            list.forEach((item, index) => {
                callback && callback(item, index)
                // 过滤掉空数据 但在编辑的时候会保留id所以不能被删除只能修改当前这条数据
                if (Object.keys(item).length) {
                    validateList.forEach((v) => {
                        if (!isDef(item[v.key])) {
                            TipModal.msgError(`表格第${index + 1}行${v.title}不能为空`)
                            throw new Error('数据不能为空')
                        }
                    })
                    resultList.push(item)
                }
            })

            if (!resultList.length) {
                TipModal.msgError('表格至少添加一条数据。')
                throw new Error('数据不能为空')
            }

            return resultList
        }
    }
}
</script>

<style lang="scss">
td.custom-cell {
  background-color: #5a5ec2;
  border: 1px solid;
  padding: 3px;
  color: #fff;
  text-align: center !important;
  vertical-align: middle !important;
}
</style>

<style lang="scss" scoped>
@import '~@/styles/project.scss';

.VHandsontable {
  display: flex;
  flex-direction: column;

  ::v-deep .show-overflow-tooltip {
    div {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  ::v-deep .show-all {
    div {
      word-break: break-all;
      word-break: keep-all;
      word-wrap: break-word;
    }
  }

  ::v-deep .handsontable {
    .htSelectEditor {
      //overflow: hidden;
    }

    .relative {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      //line-height: 30px;
      height: 100%;
    }

    table thead {
      th {
        background-color: $project-theme;
        color: #fff;
      }

      th.ht__highlight.ht__active_highlight {
        //background-color: #1DC3D1;
      }
    }

    th:first-child {
      border-left: none;
    }

    thead tr:first-child th {
      border-top: none;
    }

    table tbody tr:hover {
      //background: #f7f7f7;

      th,
      td {
        //background: #f7f7f7;
      }
    }

    table tbody tr {
      td.currentSelRow {
        //background-color: #dcdcdc;
      }
    }

    .header-sort {
      display: flex;
    }

    .colHeader {
      height: 28px;
      line-height: 28px;
    }
  }
}
</style>
