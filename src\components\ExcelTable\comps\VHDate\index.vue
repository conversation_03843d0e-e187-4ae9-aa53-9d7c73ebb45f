<template>
  <SnDateSelect
    id="VhComponent"
    ref="target"
    v-model="value"
    className="editors-date"
    v-bind="{ ...$attrs, ...$props }"
    @change="handleChange"
    v-on="$listeners"
  >
  </SnDateSelect>
</template>

<script>
export default {
  name: 'VHDate',
  props: {
    slotProps: Object,
    placeholder: {
      type: String,
      default: '请选择'
    }
  },
  data() {
    return {
      value: ''
    }
  },
  created() {
    this.slotProps['setValue'] = this.setValue
    this.slotProps['getValue'] = this.getValue
  },
  methods: {
    focus() {
      this.$refs.target.focus()
    },
    setValue(value) {
      this.value = value
    },
    getValue() {
      return this.value
    },
    handleChange(e) {
      // 由于element-ui的下拉层是挂在body的，这样点击日历后，table会先取消了变状态，在getValue返回的不是当前选中的值 所以需要这里手动设置值
      this.slotProps.hot.setSourceDataAtCell(this.slotProps.row, this.slotProps.prop, e)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .common-date-select {
  }
}
</style>
