<template>
  <SnSimpleSelect
    ref="target"
    v-model="value"
    :list="list"
    :multiple="multiple"
    :popperAppendToBody="false"
    :props="props"
    class="editorsSel"
    v-bind="$attrs"
    v-on="$listeners"
  >
  </SnSimpleSelect>
</template>

<script>
import { isString } from '@/utils/is'

export default {
  name: 'VHSelect',
  components: {},
  props: {
    slotProps: Object,
    props: {
      type: Object,
      default: () => ({
        value: 'id',
        label: 'name',
        key: 'id'
      })
    },
    list: {
      type: Array,
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      value: '',
      current: null
    }
  },
  computed: {
    getInnerProps() {
      return `${this.slotProps.prop}`
    }
  },
  created() {
    this.slotProps['setValue'] = this.setValue
    this.slotProps['getValue'] = this.getValue
  },
  methods: {
    focus() {
      this.$refs.target.focus()
    },

    setValue(_val) {
      let value = this.slotProps.hot.getDataAtRowProp(this.slotProps.row, this.getInnerProps)
      if (this.multiple) {
        this.value = this.findValue(value).map((v) => v[this.props.value])
        return
      }
      let obj = this.findValue(value)
      this.value = obj[this.props.value] || null
    },
    getValue() {
      if (this.multiple) {
        const objList = this.findValue(this.value)
        const vals = objList.map((v) => v[this.props.value]).join(',')
        this.slotProps.hot.setSourceDataAtCell(this.slotProps.row, this.getInnerProps, vals)
        return vals
      }
      let obj = this.findValue(this.value)
      this.slotProps.hot.setSourceDataAtCell(this.slotProps.row, this.getInnerProps, obj[this.props.value])
      return obj[this.props.value]
    },
    findValue(targetVal) {
      if (this.multiple) {
        const getValue = () => {
          if (Array.isArray(targetVal)) return targetVal
          if (isString(targetVal)) return targetVal.split(',')
          return []
        }
        const tar = getValue()
        return this.list.filter((v) => tar.includes(v[this.props.value])).map((v) => v)
      } else {
        return (
          this.list.find((item) => item[this.props.value] === targetVal) || {
            [this.props.label]: null,
            [this.props.value]: null
          }
        )
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-input__inner {
    padding: 0 0 0 15px;
    border: none;
  }
}

//.editorsSel {
//  width: 100%;
//
//  .el-input__inner {
//    width: calc(100% - 47px) !important;
//    height: 100%;
//
//    padding: 0 8px;
//    border: none;
//    line-height: 1;
//  }
//
//  .el-select {
//    height: 100%;
//    max-width: 100%;
//
//    ::v-deep .el-input {
//      height: 100%;
//
//      .el-input__suffix {
//        .el-input__icon {
//          line-height: 1;
//        }
//      }
//    }
//  }
//}
</style>
