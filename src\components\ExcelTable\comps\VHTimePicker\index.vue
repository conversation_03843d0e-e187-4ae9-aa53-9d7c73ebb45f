<template>
  <el-time-picker
    ref="target"
    v-model="value"
    class="editors-date"
    v-bind="{ ...$attrs, ...$props }"
    @change="handleChange"
    v-on="$listeners"
  >
  </el-time-picker>
</template>

<script>
export default {
  name: 'VHTimePicker',
  props: {
    slotProps: Object,
    arrowControl: {
      type: Boolean,
      default: false
    },
    format: {
      type: String,
      default: 'HH:mm:ss'
    },
    valueFormat: {
      type: String,
      default: 'HH:mm:ss'
    },
    placeholder: {
      type: String,
      default: '请选择'
    }
  },
  data() {
    return {
      value: ''
    }
  },
  created() {
    this.slotProps['setValue'] = this.setValue
    this.slotProps['getValue'] = this.getValue
  },
  methods: {
    focus() {
      this.$refs.target.focus()
    },
    setValue(value) {
      this.value = value
    },
    getValue() {
      return this.value
    },
    handleChange(e) {
      // 由于element-ui的下拉层是挂在body的，这样点击日历后，table会先取消了变状态，在getValue返回的不是当前选中的值 所以需要这里手动设置值
      this.slotProps.hot.setSourceDataAtCell(this.slotProps.row, this.slotProps.prop, e)
    }
  }
}
</script>

<style lang="scss" scoped>


.editors-date {
  height: 100%;
  width: 100%;

  ::v-deep
  input {
    height: 100%;
    padding: 0 8px;
    box-sizing: border-box;
    border: none;
    padding-left: 30px;
  }

  ::v-deep
  .el-input__suffix {
    .el-input__icon {
      line-height: inherit;
    }
  }

  ::v-deep
  .el-input__prefix {
    .el-input__icon {
      line-height: inherit;
    }
  }
}
</style>
