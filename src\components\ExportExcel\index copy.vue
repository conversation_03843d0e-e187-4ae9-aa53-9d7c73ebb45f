<template>
  <el-dialog ref="exportExcel" title="Excel导入" :visible.sync="visible" :before-close="closeExportExcelDialog" width="1060px">
    <div style="padding: 5px;" v-loading="uploading.state">
      <el-row style="margin-bottom: 10px;">
        <el-col>
          <el-upload action="#" ref="upload" :auto-upload="false" :on-change="addFileHandler" :on-remove="removeFileHandler"
                     accept=".xls,.xlsx" :limit="1">
            <el-button type="primary">导入Excel</el-button>
          </el-upload>
        </el-col>
      </el-row>
      <el-row v-if="!settings.data.length">
        <el-col class="exportFileHint">
          <template v-if="!isExistsFile">请先导入Excel文件</template>
          <template v-if="parserLoading">正在解析EXCEL文件...</template>
        </el-col>
      </el-row>
      <hot-table :licenseKey="licenseKey" :settings="settings" v-if="settings.data.length"></hot-table>
    </div>
    <div slot="footer" class="dialog-footer">
      <slot name="handler" :settings="settings" :uploading="uploading"></slot>
    </div>
  </el-dialog>
</template>

<script>
import { importExcel } from '@/utils'
import { HotTable } from '@handsontable/vue'
// import Func from '../../utils/func'

export default {
  name: 'ExportExcel',
  components: {
    HotTable
  },
  data() {
    return {
      licenseKey: '4d522-5237a-55f42-6653a-d1494',
      settings: {
        data: [],
        className: 'htCenter htMiddle',
        width: 850,
        height: 550,
        stretchH: 'all',
        colHeaders: [],
        columns: [],
        rowHeaders: true,
        manualColumnResize: true,
        manualRowResize: true,
        currentRowClassName: 'currentRow',
        currentColClassName: 'currentCol',
        autoWrapRow: true,
        autoRowSize: { syncLimit: 300 },
        manualRowMove: true,
        manualColumnMove: false,
        contextMenu: {
          items: {
            'row_above': { name: '向上插入一行' },
            'row_below': { name: '向下插入一行' },
            'remove_row': { name: '删除行' }
          }
        }
      },
      uploading: {
        state: false
      },
      parserLoading: false,
      isExistsFile: false,
      visible: false
    }
  },
  props: {
    dynamicMaxLength: { // 动态最大长度
      type: [Number],
      default: 20
    },
    entityForm: { // 数据实体
      type: [Object],
      required: true
    },
    traitSettings: {
      type: [Object],
      required: true,
      default: () => ({
        colHeaders: [],
        columns: []
      })
    },
    exportLabelMap: {
      type: [Object],
      required: true
    },
    exportExcelDialogVisible: {
      type: [Boolean],
      default: false,
      required: true
    }
  },
  created() {
    this.settings = { ...this.settings, ...this.traitSettings }
    this.removeFileHandler()
  },
  computed: {},
  watch: {
    'settings.data': {
      handler(val) {
        if (val.length) {
          if (val.length > this.dynamicMaxLength) {
            this.settings.height = 550
          } else {
            this.settings.height = 50 + 24 * val.length
          }
        } else {
          this.settings.height = 0
        }
      },
      immediate: true
    },
    exportExcelDialogVisible: {
      handler(val) {
        this.visible = val
      },
      immediate: true
    }
  },
  methods: {
    /**
     *关闭Excel导入对话框
     */
    closeExportExcelDialog() {
      this.$refs['upload'].uploadFiles = []
      this.$emit('update:exportExcelDialogVisible', false)
      this.settings.data = []
      this.isExistsFile = false
    },
    /**
     *移除文件
     */
    removeFileHandler() {
      this.settings.data = []
      this.isExistsFile = false
    },
    /**
     *添加文件
     */
    async addFileHandler(file) {
      this.isExistsFile = true
      this.parserLoading = true
      const excelArr = await importExcel(file)
      if (!excelArr.length) {
        this.$message({ showClose: true, message: `导入数据不能为空`, type: 'warning' })
        this.settings.data = []
        this.parserLoading = false
        return false
      }
      // let excelDate = Func.excelDateToJSDate(46829)
      // Func.formatDate(excelDate)
      let columnType = {}
      this.traitSettings.columns.map(v => {
        columnType[v.data] = v.type // 初始化属性和值
      })
      // console.log(excelArr, 'excelArr')
      // console.log(columnType, 'columnType')
      // console.log(this.exportLabelMap, 'exportLabelMap')
      this.settings.data = excelArr.map(v => {
        let middle = { ...this.entityForm }
        for (let i in v) {
          // if (columnType[this.exportLabelMap[i]] === 'date' && (!isNaN(v[i]))) {
          //   let excelDate = Func.excelDateToJSDate(v[i])
          //   v[i] = Func.formatDate(excelDate)
          // }
          let afterTrim = ('' + v[i]).trim()
          middle[this.exportLabelMap[i]] = afterTrim
        }
        return middle
      })
      // }
      // catch (e) {
      //   this.$message({showClose: true, message: 'Excel解析失败', type: 'warning'})
      //   this.settings.data = []
      // }
      this.parserLoading = false
      this.isExistsFile = false
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
