<template>
  <div>
    <!--    <el-table :data="fileList" :border="true" style="width: 100%">-->
    <!--      <el-table-column prop="name" align="center" min-width="150" label="文件名"-->
    <!--                       :show-overflow-tooltip="true"></el-table-column>-->
    <!--      <el-table-column prop="percentage" align="center" min-width="100" label="进度">-->
    <!--        <template #default="{row}">-->
    <!--          <span v-if="row.percentage===100">完成</span>-->
    <!--          <span v-else>{{ row.percentage }}%</span>-->
    <!--        </template>-->
    <!--      </el-table-column>-->
    <!--      <el-table-column align="center" min-width="130" label="上传时间">-->
    <!--        <template #default="{row}">-->
    <!--          <span v-if="row.response">{{ row.response.createDate }}</span>-->
    <!--        </template>-->
    <!--      </el-table-column>-->
    <!--      <el-table-column prop="size" align="center" min-width="100" label="操作">-->
    <!--        <template #default="{row}">-->
    <!--          <el-link v-if="row.response" icon="el-icon-view" :href="row.response.uri" target="_blank"-->
    <!--                   style="margin-right: 20px"/>-->
    <!--          <el-link icon="el-icon-delete" @click="deleteDocument(row)"/>-->
    <!--        </template>-->
    <!--      </el-table-column>-->
    <!--    </el-table>-->
    <el-upload v-bind="$attrs" ref="upload" :multiple="true" :action="actionUrl" :on-change="handleChange" :show-file-list="true"
               :file-list.sync="fileList" :headers="uploadHeaders" :on-progress="handleProgress" :on-success="handleSuccess"
               :before-remove="beforeRemove" :on-remove="handleRemove">
      <el-button type="primary" :plain="true">选择附件</el-button>
      <!-- <div slot="tip" class="el-upload__tip">只支持 PNG,JPG格式的图片上传</div> -->

      <!-- 自定义文件列表模板，添加下载按钮 -->
      <div slot="file" slot-scope="{file}">
        <div class="file-item">
          <span class="file-name" @click="handleDownload(file)">{{ file.name }}</span>
          <div class="file-actions">
            <el-link v-if="file.response" icon="el-icon-download" @click.stop="handleDownload(file)"
                     style="margin-right: 10px;"></el-link>
            <el-link icon="el-icon-delete" @click.stop="deleteDocument(file)"></el-link>
          </div>
        </div>
      </div>
      <!-- 自定义文件列表模板 -->
      <div slot="file" slot-scope="{file}">
        <div class="file-item">
          <span class="file-name" @click="handleDownload(file)">
            {{ file.name }}
            <el-tag v-if="file.status === 'uploading'" size="mini" type="info">上传中...</el-tag>
            <!-- <el-tag v-if="file.status === 'success'" size="mini" type="success">上传完成</el-tag> -->
          </span>
          <div class="file-actions">
            <el-link v-if="file.response && file.status === 'success'" icon="el-icon-download" @click.stop="handleDownload(file)"
                     style="margin-right: 10px;"></el-link>
            <el-link icon="el-icon-delete" @click.stop="deleteDocument(file)" :disabled="file.status === 'uploading'"></el-link>
            <el-progress v-if="file.status === 'uploading'" :percentage="file.percentage" :stroke-width="2" :show-text="false"
                         style="width: 100px; margin-left: 10px;"></el-progress>
          </div>
        </div>
      </div>
    </el-upload>
  </div>
</template>

<script>
import Cache from '@/utils/cache'
import { attachmentDelete } from '@/api/attachment'

export default {
  name: 'FileUpload',
  data() {
    return {
      actionUrl: process.env.VUE_APP_CENTER + '/cwe/a/attachment/upload',
      uploadHeaders: { Authorization: Cache.get('SYS_TOKEN') },
      fileList: []
    }
  },
  props: {
    list: {
      type: Array,
      default() {
        return []
      }
    }
  },
  watch: {
    list: {
      handler(newValue, oldValue) {
        this.fileList = newValue.map(item => {
          if (item) {
            return {
              name: item.display,
              url: item.uri,
              uid: item.id,
              response: item,
              status: 'success' // 标记为已上传完成
            }
          }
        })
      },
      immediate: true
    }
  },
  methods: {
    // 新增下载方法
    handleDownload(file) {
      // if (!file.response || !file.response.uri) {
      //   this.$message.error('文件不可下载')
      //   return
      // }

      // 创建下载链接
      const link = document.createElement('a')
      link.href = file.response.uri
      link.download = file.name || 'download'
      link.style.display = 'none'

      document.body.appendChild(link)
      link.click()

      // 清理
      setTimeout(() => {
        document.body.removeChild(link)
        window.URL.revokeObjectURL(file.response.uri)
      }, 100)
    },
    deleteDocument(row) {
      this.$refs.upload.handleRemove(row, row.raw)
    },
    handleChange(files, fileList) {
      this.fileList = fileList
    },
    handleProgress(event, file, fileList) {
      // this.fileList = fileList
      // 更新上传进度
      this.fileList = fileList.map(f => {
        if (f.uid === file.uid) {
          return {
            ...f,
            percentage: event.percent,
            status: 'uploading'
          }
        }
        return f
      })
    },
    handleSuccess(response, file, fileList) {
      this.fileList = fileList.map(f => {
        if (f.uid === file.uid) {
          return {
            ...f,
            status: 'success'
          }
        }
        return f
      })
      this.fileList = fileList.map(f => {
        if (f.uid === file.uid) {
          return {
            ...f,
            status: 'success'
          }
        }
        return f
      })
      this.$emit('update:list', fileList.map(item => {
        return {
          display: item.name,
          ...item.response
        }
      }))
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    handleRemove(file, fileList) {
      this.$emit('update:list', fileList.map(item => {
        return {
          display: item.name,
          ...item.response
        }
      }))
    },
    async beforeRemove(file, fileList) {
      const before = await this.$confirm(`确定移除 ${file.name}？`)
      const status = await attachmentDelete(file.response.id)
      return before && status
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep.el-upload-list__item {
  transition: none !important;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 0;

  .file-name {
    cursor: pointer;
    flex: 1;
    margin-right: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &:hover {
      color: #409eff;
      text-decoration: underline;
    }
  }

  .file-actions {
    display: flex;
    align-items: center;
  }
}
</style>
