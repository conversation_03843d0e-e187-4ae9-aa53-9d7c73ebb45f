<template>
  <section class="filter">
    <el-form v-if="showForm" :inline="true" :model="searchForm" class="filter-form" ref="filterForm">
      <template v-if="options.showMore">
        <template v-for="item in options.columns">
          <slot class="form-item" v-if="item.slot" :name="item.slot" :style="{ width: item.width + 'px' }" />
          <el-form-item v-if="item.component" :label="item.label||``" :prop="item.prop" :key="item.prop">
            <component class="form-item" :is="item.component" :value.sync="searchForm[item.prop]" v-model="searchForm[item.prop]"
                       v-bind="item.props" @getlossNullList="getlossNullList" @changeisBill="changeisBill"
                       @getlossNullListV="getlossNullListV" @changeCarriageRemarks="changeCarriageRemarks"
                       :style="{ width: item.width + 'px' }" />

          </el-form-item>
          <el-form-item class="form-item" v-else :label="item.label" :prop="item.prop" :key="item.prop"
                        :style="{ width: item.width + 'px' }">

            <el-input :v-bind="item.props" v-model="searchForm[item.prop]" :placeholder="item.props.placeholder" clearable>
              <slot v-if="item.props.slot" :name="item.props.slot" />
            </el-input>
          </el-form-item>
        </template>
      </template>

      <el-form-item>
        <template>
          <div :class="[changeactions!='' && actions!=''?'class1':'' ]" style="display: flex;">
            <div v-if="changeactions">
              <div class="actionList" style="position: relative;margin-right:20px">
                <el-button class="filter-item" :class="action.active == true ? 'activeClass' : ''" type="danger"
                           v-for="(action, index) in changeactions" :key="index" v-bind="action.config"
                           style="position: relative;color:#fff;border:none" @click="action.click(selectList, action.type)">
                  {{ action.label }}
                </el-button>
              </div>
            </div>
            <div v-if="actions" style="margin-right:20px">
              <el-button class="filter-item" v-for="(action, index) in actions" :key="index" v-bind="action.config"
                         :class="[action.isborder==true?'filter-itembtn':'',action.ismargin==true?'itembtn':'']"
                         @click="action.click(selectList, action.type)">
                {{ action.label }}
              </el-button>
            </div>
          </div>
        </template>
      </el-form-item>

      <el-form-item>
        <template v-if="buttomlist">
          <el-button class="filter-item" style="  margin-right: 20px;" v-for="(action, index) in buttomlist" :key="index"
                     :class="action.type == 'payapply' ? 'yellow' : 'red'" v-bind="action.config"
                     @click="action.click(action.type)">
            {{ action.label }}
          </el-button>
        </template>
      </el-form-item>
      <el-form-item>
      </el-form-item>

      <el-form-item>
        <el-button class="filter-form-button" :loading="loading" @click="handleFilter">搜索</el-button>
        <el-button class="filter-form-button rset" @click="handleReset()">重置</el-button>
        <el-button class="filter-form-button search" @click="handleRefresh()" :loading="loadingV" v-if="showRefresh">刷新
        </el-button>
        <el-button v-if="showWeightRoomImport" type="primary" plain size="small" @click="handleImportProcure">采购日报导入</el-button>
        <el-button v-if="showWeightRoomImport" type="primary" plain size="small" @click="handleImportSale">销售日报导入</el-button>
        <el-button v-if="showWeightRoomImport" type="primary" plain size="small" @click="handleImportProcureDetail">采购明细导入</el-button>
        <el-button v-if="showWeightRoomImport" type="primary" plain size="small" @click="handleImportSaleDetail">销售明细导入</el-button>
        <el-button class="filter-form-button add" @click="handleAdd()" v-if="showAdd">新增</el-button>
        <el-button class="filter-form-button add" @click="handleImport()" v-if="showImport">新增导入</el-button>
      </el-form-item>
    </el-form>
  </section>
</template>

<script>
export default {
  name: 'FilterTable',
  data() {
    return {
      searchForm: {},
      loading: false,
      loadingV: false
    }
  },
  props: {
    showAdd: {
      type: Boolean,
      default: true
    },
    showImport: {
      type: Boolean,
      default: true
    },

    showRefresh: {
      type: Boolean,
      default: true
    },
    options: {
      type: Object,
      default() {
        return {
          showMore: true,
          columns: []
        }
      }
    },
    useColDeep: {
      type: Boolean,
      default: true
    },
    buttomlist: {
      type: Array,
      default() {
        return []
      }
    },
    selectList: {
      type: Array,
      default() {
        return []
      }
    },
    actions: {
      type: Array,
      default() {
        return []
      }
    },
    changeactions: {
      type: Array,
      default() {
        return []
      }
    },
    showWeightRoomImport: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    showForm() {
      if (Object.keys(this.searchForm).length) return true
      return false
    }
  },
  created() {
    // this.initSearchForm()
  },
  watch: {
    'options.columns': {
      handler(val) {
        if (this.useColDeep) {
          this.initSearchForm()
        }
      },
      deep: true,
      immediate: true
    },
  },
  methods: {
    getlossNullList(val) {
      //拿到公司的id，获取亏吨的列表
      // console.log('1111')
      // console.log(val)
      // console.log(this.searchForm)
      let name = ''
      if (val == ' ') {
        name = ''
        this.searchForm['lossNull'] = ''
      } else {
        name = val.name
      }
      // console.log(name)
      this.$emit('titleChanged', name) //自定义事件  传递值“子向父组件传值”
      this.searchForm['companyFullName'] = name
      // this.searchForm['senderName'] = name
      // console.log(this.searchForm)
      // console.log(this.searchForm['companyFullName'])
    },
    getlossNullListV(val) {
      // console.log(this.searchForm)
      let name = ''
      if (val == ' ') {
        name = ''
      } else {
        name = val.name
      }
      this.$emit('titleChanged', name) //自定义事件  传递值“子向父组件传值”
      this.searchForm['senderName'] = name
      this.searchForm['receiverName'] = name
      // console.log(this.searchForm)
    },

    changeisBill(type) {
      // console.log('0000')
      // console.log(type)
      this.$emit('isBillChanged', type)
    },
    changeCarriageRemarks(type) {
      this.$emit('CarriageRemarksChanged', type)
    },

    handleAdd() {
      this.$emit('add')
    },

    handleImport() {
      this.$emit('import')
    },

    handleRefresh() {
      if (this.loadingV) return false
      this.loadingV = true
      setTimeout(() => (this.loadingV = false), 1000)
      //  sbDate: (new Date(new Date().getTime())).Format('yyyy-MM-dd'), // 日期
      const searchValue = this.searchForm['startDate']
      this.$emit('Refresh', searchValue)
    },
    initSearchForm() {
      this.options.columns.forEach((item) => {
        this.searchForm[item.prop] = item.defaultValue ? item.defaultValue : ''
        if (item.resetClearDefault) {
          item.defaultValue = ''
        }
      })
      // 展开是为了重新绑定值，要不双向绑定监听不到新添加的值
      this.searchForm = { ...this.searchForm }
    },
    handleFilter() {
      // console.log(this.searchForm)
      if (this.loading) return false
      this.loading = true
      setTimeout(() => (this.loading = false), 1000)
      const filters = {}
      this.options.columns.forEach((item) => {
        const searchValue = this.searchForm[item.prop]
        if (searchValue) filters[item.filter] = searchValue
      })
      this.$emit('filter', filters)
    },
    handleReset() {
      this.initSearchForm()
      this.$emit('reset')
    },
    handleImportProcure() {
      this.$emit('importProcure')
    },
    handleImportSale() {
      this.$emit('importSale')
    },
    handleImportProcureDetail() {
      this.$emit('importProcureDetail')
    },
    handleImportSaleDetail() {
      this.$emit('importSaleDetail')
    }
  }
}
</script>

<style lang="scss" scoped>
.filter {
  & > div {
    display: flex;
    align-items: center;
  }
}

.action {
  // flex: 1;
  width: 100%;
  display: flex;
  align-items: center;

  & > button {
    margin: 5px 10px 10px 10px;
    border: none;
    display: flex;
    align-items: center;
  }

  &:after {
    content: ' ';
    display: block;
    clear: both;
  }
}

.yellow {
  background-color: #ff9639;
  color: #fff;
}

.red {
  background-color: #ff726b;
  color: #fff;
}

.red:hover {
  background-color: #ff726b;
  color: #fff;
}

.yellow:hover {
  background-color: #ff9639;
  color: #fff;
}

.class1 {
  display: flex;
}

.borderRadio {
  border-radius: 5px;
}

::v-deep .el-button--mini,
.el-button--mini.is-round {
  padding: 0.4vw 0.8vh !important;
}

::v-deep .el-form-item__content {
  width: 100%;
}

.rset {
  border: solid 1px #adadad !important;
  color: #adadad !important;
}

.search {
  border: solid 1px #ff9639 !important;
  color: #ff9639 !important;
}

.add {
  background-color: #ff9639 !important;
  border: solid 1px #ff9639 !important;
  color: #fff !important;
}

::v-deep .el-input__inner {
  padding: 10px;
}

.filter {
  // position: relative;
  // height: 50px;
  // height: 5vh;
  padding-top: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);

  &-form {
    // position: absolute;
    // top: 50%;
    padding-left: 15px;
    // transform: translate(0%, -25%);
    z-index: 999;

    .form-item {
      width: 140px;
      // min-width: 7vw;
      // max-width: 7vh;
      // min-width: 150px;
      // max-width: 160px;
      margin-right: 15px !important;
    }

    .el-form-item {
      margin-right: 0;
    }

    &-button {
      &:first-of-type {
        margin-left: 0px;
      }

      background: #fff;
      // margin-left: 20px;
      margin-left: 20px;
      color: #2f79e8;
      border: 1px solid #2f79e8;
    }
  }
}
</style>
