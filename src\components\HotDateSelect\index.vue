<!--未完善-->
<template>
  <div>
    <slot name="head"></slot>
    <div v-if="isEditor && isVisible" :style="style" @mousedown="stopMousedownPropagation">
      <el-date-picker ref="select" class="select" v-model="value" @change="change"></el-date-picker>
    </div>
    <div v-if="isRenderer">{{ value }}</div>
  </div>
</template>
<script>
import {HotTable, HotColumn, BaseEditorComponent} from '@handsontable/vue'

export default {
  name: 'HotDateSelect',
  extends: BaseEditorComponent,
  props: {},
  data() {
    return {
      /**
       * @type {import('handsontable/core').default}
       */
      hotInstance: null,
      TD: null,
      row: null,
      col: null,
      prop: null,
      value: '',
      isEditor: null,
      isRenderer: null,
      editorElement: null,
      cellProperties: null,
      isVisible: false,
      /**
       * @type {HTMLStyleElement}
       */
      style: {
        position: 'absolute',
        background: '#fff',
        zIndex: 10000,
        // border: '1px solid #000',
        left: '0px',
        top: '0px',
        overflow: 'hidden'
      },
      selectVisible: false,
      isOpen: false,
      disabled: ''
    }
  },
  mounted() {
    console.log(11111)
  },
  methods: {
    stopMousedownPropagation(e) {
      e.stopPropagation()
    },
    prepare(row, col, prop, td, originalValue, cellProperties) {
      BaseEditorComponent.options.methods.prepare.call(
        this,
        row,
        col,
        prop,
        td,
        originalValue,
        cellProperties
      )
      const tdPosition = td.getBoundingClientRect()
      this.style.left = tdPosition.left + window.pageXOffset + 'px'
      this.style.top = tdPosition.top + window.pageYOffset + 'px'
      this.style.width = tdPosition.width + 'px'
      this.style.height = tdPosition.height + 'px'
    },
    change(e) {
      this.isVisible = false
      this.hotInstance.setDataAtRowProp(this.row, this.prop, this.value)
      this.finishEditing()
    },
    open() {
      this.isVisible = true
      this.$nextTick(() => {
        this.isOpen = true
        this.$refs.select.handleClose = () => {
          if (!this.isOpen) {
            this.isVisible = false
          }
          this.isOpen = false
        }
        this.$refs.select.handleFocus()
      })
    },
    close() {
      this.disabled = true
    },
    setValue(value) {
      console.log('setValue', this.value)
      this.value = value
    },
    getValue() {
      console.log('getValue', this.value)
      return this.value
    }
  }
}
</script>
<style scoped lang="scss">
.select {
  width: 100%;
  height: 100%;
  line-height: 100%;
}

:deep(.el-input__inner) {
  height: 100%;
  line-height: 100%;
}

:deep(.el-select>.el-input) {
  height: 100%;
  line-height: 100%;
}
</style>



