<!--未完善-->
<template>
  <div>
    <slot name="head"></slot>
    <div v-if="isEditor && isVisible" :style="style" @mousedown="stopMousedownPropagation">
      <el-select ref="select" class="select" v-model="value" placeholder="请选择" @change="change" clearable>
        <template #default>
          <el-option label="黄金糕" value="选项1"></el-option>
          <el-option label="双皮奶" value="选项2"></el-option>
          <el-option label="蚵仔煎" value="选项3"></el-option>
          <el-option label="龙须面" value="选项4"></el-option>
          <el-option label="北京烤鸭" value="选项5"></el-option>
        </template>
      </el-select>
    </div>
    <div v-if="isRenderer">
      <!--      <span>{{ value }}</span>-->
      <div v-if="value==='选项1'">黄金糕</div>
      <div v-if="value==='选项2'">双皮奶</div>
      <div v-if="value==='选项3'">蚵仔煎</div>
      <div v-if="value==='选项4'">龙须面</div>
      <div v-if="value==='选项5'">北京烤鸭</div>
    </div>
  </div>
</template>
<script>
import {HotTable, HotColumn, BaseEditorComponent} from '@handsontable/vue'

export default {
  name: 'HotSelect',
  extends: BaseEditorComponent,
  props: {},
  data() {
    return {
      /**
       * @type {import('handsontable/core').default}
       */
      hotInstance: null,
      TD: null,
      row: null,
      col: null,
      prop: null,
      value: '',
      isEditor: null,
      isRenderer: null,
      editorElement: null,
      cellProperties: null,
      isVisible: false,
      /**
       * @type {HTMLStyleElement}
       */
      style: {
        position: 'absolute',
        background: '#fff',
        zIndex: 10000,
        // border: '1px solid #000',
        left: '0px',
        top: '0px',
        overflow: 'hidden'
      },
      selectVisible: false,
      isOpen: false
    }
  },
  mounted() {
    console.log(11111)
  },
  methods: {
    stopMousedownPropagation(e) {
      e.stopPropagation()
    },
    prepare(row, col, prop, td, originalValue, cellProperties) {
      BaseEditorComponent.options.methods.prepare.call(this, row, col, prop, td, originalValue, cellProperties)
      const tdPosition = td.getBoundingClientRect()
      this.style.left = tdPosition.left + window.pageXOffset + 'px'
      this.style.top = tdPosition.top + window.pageYOffset + 'px'
      this.style.width = tdPosition.width + 'px'
      this.style.height = tdPosition.height + 'px'
    },
    change(e) {
      this.isVisible = false
      this.hotInstance.setDataAtRowProp(this.row, this.prop, this.value)
      this.finishEditing()
    },
    open() {
      this.isVisible = true
      this.$nextTick(() => {
        this.isOpen = true
        this.$refs.select.handleClose = () => {
          if (!this.isOpen) {
            this.isVisible = false
          }
          this.isOpen = false
        }
        this.$refs.select.toggleMenu()
      })
    },
    close() {
    },
    setValue(value) {
      console.log('setValue', this.value)
      this.value = value
    },
    getValue() {
      console.log('getValue', this.value)
      return this.value
    }
  }
}
</script>
<style scoped lang="scss">
.select {
  width: 100%;
  height: 100%;
  line-height: 100%;
}

:deep(.el-input__inner) {
  height: 100%;
  line-height: 100%;
}

:deep(.el-select>.el-input) {
  height: 100%;
  line-height: 100%;
}
</style>



