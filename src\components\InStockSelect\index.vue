<template>
  <el-autocomplete class="customerSelect" :value="value" :placeholder="`请输入样品名称`" value-key="name"
                   :fetch-suggestions="gainRemoteData" @input="change" @select="otherData" :disabled="disabled">
    <div slot-scope="scope" class="wrap">
      <div style="line-height: 30px;margin-top: 5px">
        <span>{{scope.item.name}}</span>
      </div>
    </div>
  </el-autocomplete>
</template>
<script>
import Model from '@/model/sample/inStock'

export default {
  name: 'CustomerSelect',
  data() {
    return {
      val: void 0, // 映射值
      list: []
    }
  },
  props: {
    value: {
      required: true
    },
    ext: {
      type: Object
    },
    disabled: {
      type: [<PERSON><PERSON>an],
      default: false
    },
    filterType: {
      type: String
    }
  },
  mounted() {
  },
  methods: {
    async gainRemoteData(query = '', cb) {
      let res = await Model.getInStock({ keyword: query })
      const gather = res.data.map(v => {
        return v
      })
      this.list = gather
      cb(gather)
    },
    otherData(item) {
      this.$emit('input', this.val)
      this.$emit('info', item)
    },
    change(val) {
      this.val = val
      this.$emit('input', val)
    }
  }
}
</script>
<style lang="scss" scoped>
.customerSelect {
  width: 100%;
}

.optionContent1 {
  color: #2d8cf0;
  font-size: 12px;
  text-align: end;
  line-height: 20px;
  border-bottom: 1px solid #eee;
}
</style>
