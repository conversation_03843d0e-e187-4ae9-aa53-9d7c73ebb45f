<template>
  <section class="indicators">
    <div class="title">
      <slot name="title"> {{title}} </slot>
      <span class="subtitle" v-if="subtitle">
        <slot name="subtitle">{{subtitle}}</slot>
      </span>
    </div>
    <div :class="[isLine?'under_line':'']" />
    <div class="content">
      <slot />
    </div>
  </section>
</template>

<script>
export default {
  name: 'IndicatorsCard',
  data() {
    return {
    }
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    isLine: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style  scoped lang='scss'>
.indicators {
  display: flex;
  flex-flow: column;
  padding: 10px 0;
  background: #fff;
  //margin: 10px 0;
  margin-bottom: 10px;
  border-radius: 5px;
  .title {
    display: flex;
    margin: 0 0 0 0px;
    font-size: 16px;
    padding: 15px 10px;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      width: 3px;
      height: 30%;
      top: 50%;
      transform: translateY(-50%);
      background-color: #33cad9;
    }
    .subtitle {
      flex: 1;
      margin-left: 5px;
      font-size: 16px;
      color: #494949;
    }
  }
  .under_line {
    margin: 0px 0 5px;
    border: 1px solid #f7f7f7;
  }
  .content {
    width: 100%;
    padding: 15px 20px 10px;
    // display: flex;
    // justify-content: space-evenly;
    margin: 0% auto;
    .table {
      display: flex;
      flex-flow: column;
      border: 1px solid #f3fcfd;
      .table_title {
        display: flex;
        background-color: #33cad9;
        height: 50px;
        align-items: center;
        color: #fff;
        font-size: 16px;
        justify-content: center;
      }
      .table_row {
        width: 100%;
        display: flex;
        height: 45px;
        align-items: center;
        justify-content: center;
        &:nth-of-type(odd) {
          background-color: #f3fcfd;
        }
        span {
          text-indent: 1em;
          flex: 1;
          color: #555555;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
