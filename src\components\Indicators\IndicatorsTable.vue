<template>
  <el-table :data="list" style="width:100%" stripe class="detail_table" :header-cell-style="headerCellStyle" v-bind="$attrs"
            :border="border" :row-class-name="tableRowClassName">
    <el-table-column type="index" align="center" width="40" v-if="showIndex" />
    <template v-for="columns in dataConfig.columns">
      <template v-if="columns.isShow">
        <slot v-if="columns.slot" :name="columns.slot" />
        <component v-else-if="columns.component" :is="columns.component" />
        <el-table-column v-else v-bind="columns" :show-overflow-tooltip="!!columns.showTooltip" v-on="$listeners" />
      </template>
    </template>
    <slot />
  </el-table>
</template>

<script>
export default {
  name: 'IndicatorsTable',
  data() {
    return {
      list: []
    }
  },
  props: {
    headerCellStyle: {
      type: Object,
      default() {
        return { background: '#33cad9', color: '#fff' }
      }
    },
    border: {
      type: Boolean,
      default: false
    },
    stripe: {
      type: Boolean,
      default: true
    },
    showIndex: {
      type: Boolean,
      default: false
    },
    dataConfig: {
      type: Object,
      default() {
        return {
          list: [],
          columns: []
        }
      },
      require: true
    },
    publicConfig: {
      type: Object,
      default() {
        return {
          border: false,
          align: 'center',
          showTooltip: false
        }
      }
    }
  },
  created() {
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex === 1) {
        return 'warning-row'
      } else if (rowIndex === 3) {
        return 'success-row'
      }
      return ''
    }
  },
  watch: {
    dataConfig: {
      handler(value) {
        this.list = value.list
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style  scoped lang='scss'>
.detail_table {
  width: 100%;
}
</style>