<template>
  <indicators-card :title="title" class="CoalProportion">
    <indicators-table :dataConfig="dataConfig" :showIndex="showIndex">

      <el-table-column label="配比(%)" min-width="120" slot="percentRes" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.percentRes}}</span>
        </template>
      </el-table-column>

      <el-table-column label="到厂价（元/吨" min-width="120" slot="arrivePrice">
        <template slot-scope="scope">
          <span style="color:#F8A20F;">{{scope.row.arrivePrice}}</span>
        </template>
      </el-table-column>

    </indicators-table>
  </indicators-card>
</template>

<script>
import { IndicatorsCard, IndicatorsTable } from '@/components/Indicators'
export default {
  name: 'CoalProportion',
  components: { IndicatorsCard, IndicatorsTable },
  props: {
    title: {
      type: String,
      default: '煤种及配比'
    },
    showIndex: {
      type: Boolean,
      default: false
    },
    dataConfig: {
      type: Object,
      default() {
        return {
          list: [],
          columns: []
        }
      },
      require: true
    }
  }
}
</script>
