<template>
  <div class="app-layout">
    <div class="content" v-loading="isLoading" :element-loading-text="loadingText">
      <el-card class="card">
        <div class="steps">
          <div class="step-out">
            <div class="checkBg">1</div>
            <div class="text">添加煤种</div>
            <div class="line"></div>
          </div>
          <div class="step-out">
            <div class="checkBg">2</div>
            <div class="text">指标录入</div>
            <div :class="active >= 3?'line':'gray-line'"></div>
          </div>
          <div class="step-out">
            <div v-show="active >= 3">
              <div class="checkBg">3</div>
            </div>
            <div v-show="active < 3" class="gray-step">3</div>
            <div class="text">计算操作</div>
            <div :class="active >= 4?'line':'gray-line'"></div>
          </div>
          <div class="step-out">
            <div v-show="active >= 4">
              <div class="checkBg">4</div>
            </div>
            <div v-show="active < 4" class="gray-step">4</div>
            <div class="text">配煤结果</div>
          </div>
        </div>
      </el-card>
      <el-card class="card">
        <div slot="header">
          <div style="display: flex;justify-content: space-between; align-items: center">
            <div class="card-title">添加煤种</div>
            <el-button @click="add" type="primary">
              添加煤种
            </el-button>
          </div>
        </div>
        <div class="m5" v-if="space.workspaceCoalRockList.length > 0">
          <el-table :data="space.workspaceCoalRockList" border :row-style="activeRow" :header-cell-style="activeHeader">
            <el-table-column fixed width="44">
              <template slot-scope="scope">
                <div class="tc">{{ scope.$index + 1 }}</div>
              </template>
            </el-table-column>
            <el-table-column fixed prop="name" width="100" label="名称">
              <template slot-scope="scope">
                <div>
                  <span class="under-line" @click="edit(scope.row)">{{ scope.row.name }}</span>
                  <span v-if="scope.row.isCoalRock === 'Y'" style="position: absolute;top: 0px;right: 0;"></span>
                </div>
              </template>
            </el-table-column>
            <el-table-column fixed prop="type" label="煤种" min-width="60"></el-table-column>
            <el-table-column label="人工配比">
              <el-table-column width="60" label="数量">
                <template slot-scope="scope">
                  <el-input class="yellow-input" v-model="scope.row.quantity" placeholder="数量" type="number" @focus="onFcous"
                            @input="loseFcous(scope.$index, scope.row, scope.column)" min="0"></el-input>
                </template>
              </el-table-column>
              <el-table-column width="75" label="占比(%)">
                <template slot-scope="scope">
                  <span style="color:#f8a20f;">{{ scope.row.percent2 }}</span>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column width="150" label="指定配比%">
              <template slot-scope="scope">
                <div style="display:flex;justify-content: space-evenly;">
                  <el-select class="yellow-input" v-model="scope.row.percentCond" style="width: 50px;" placeholder="空">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                  <el-input class="yellow-input" style="width: 60px" v-model="scope.row.percent1" placeholder="配比" type="number"
                            min="0"></el-input>
                </div>

              </template>
            </el-table-column>
            <el-table-column label="R0(%)" min-width="60" prop="macR0">
              <template slot-scope="scope">
                <el-input v-model="scope.row.macR0" placeholder="R0" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="标准差S" prop="macS" min-width="80">
              <template slot-scope="scope">
                <el-input v-model="scope.row.macS" placeholder="输入数值" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="Ad(%)" prop="cleanAd" min-width="60">
              <template slot-scope="scope">
                <el-input v-model="scope.row.cleanAd" placeholder="Ad" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="Vdaf(%)" prop="cleanVdaf" min-width="70">
              <template slot-scope="scope">
                <el-input v-model="scope.row.cleanVdaf" placeholder="Vdaf" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="St,d(%)" prop="cleanStd" min-width="60">
              <template slot-scope="scope">
                <el-input v-model="scope.row.cleanStd" placeholder="St,d" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="G" prop="procG" min-width="55">
              <template slot-scope="scope">
                <el-input v-model="scope.row.procG" placeholder="G" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="Y(mm)" prop="procY" min-width="55">
              <template slot-scope="scope">
                <el-input v-model="scope.row.procY" placeholder="Y" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="到厂价">
              <el-table-column label="元/吨" min-width="80">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.arrivePrice" placeholder="到厂价（元/吨）" type="number"></el-input>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="X(mm)" prop="procX" min-width="55">
              <template slot-scope="scope">
                <el-input v-model="scope.row.procX" placeholder="X" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="130" align="center">
              <template slot-scope="scope">
                <span @click="makeSyncCoal(scope.row)"
                      style="border-radius: 5px;background: #f8a20f; color: #fff; padding:5px 10px">保存</span>
                <span @click="remove(scope.row)"
                      style="border-radius: 5px;background: #EFEFEF; color: #B2B2B2; padding:5px 10px; margin: 0 5px">删除</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-else>
          <el-empty :image="empty" description="请先添加煤种数据"></el-empty>
        </div>
      </el-card>
      <el-form :model="limit">
        <el-card class="card">
          <div slot="header">
            <div class="card-title">指标录入</div>
          </div>
          <div style="display: flex;margin-top: -10px;position: relative;">
            <div class="container">
              <div>
                <p class="single" style="width: 70px"></p>
                <div class="single">
                  <span class="title-text">灰分Ad</span>
                </div>
                <div class="single">
                  <span class="title-text">硫St,d</span>
                </div>
                <div class="single">
                  <span class="title-text">Vdaf</span>
                </div>
                <div class="single">
                  <span class="title-text">粘结G</span>
                </div>
                <div class="single">
                  <span class="title-text">胶质Y</span>
                </div>
              </div>
              <div style="margin:5px 0">
                <p class="single single-wrap" style="width: 70px">上限:</p>
                <div class="single">
                  <span style="width: 20px"></span> <input v-model="limit.cInAdH" style="width: 65px" type="number">
                </div>
                <div class="single">
                  <span style="width: 20px"></span> <input v-model="limit.cInStdH" style="width: 65px" type="number">
                </div>
                <div class="single">
                  <span style="width: 20px"></span> <input v-model="limit.cInVdafH" style="width: 70px" type="number">
                </div>
              </div>
              <div style="margin:5px 0">
                <p class="single single-wrap" style="width: 70px">质量目标:</p>
                <div class="single">
                  <span style="width: 20px"></span> <input v-model="limit.cInAd" style="width: 65px" type="number">
                </div>
                <div class="single">
                  <span style="width: 20px"> </span> <input v-model="limit.cInStd" style="width: 65px" type="number">
                </div>
                <div class="single">
                  <span style="width: 20px"></span> <input v-model="limit.cInVdaf" style="width: 65px" type="number">
                </div>
                <div class="single">
                  <span style="width: 20px"> </span> <input v-model="limit.cInG" style="width: 65px" type="number">
                </div>
                <div class="single">
                  <span style="width: 20px"> </span> <input v-model="limit.cInY" style="width: 65px" type="number">
                </div>
              </div>
              <div style="height: 28px">
                <p class="single single-wrap" style="width: 70px">下限:</p>
                <div class="single">
                  <span style="width: 20px"></span> <input v-model="limit.cInAdL" style="width: 65px" type="number">
                </div>
                <div class="single">
                  <span style="width: 20px"></span> <input v-model="limit.cInStdL" style="width: 65px" type="number">
                </div>
                <div class="single">
                  <span style="width: 20px"></span> <input v-model="limit.cInVdafL" style="width: 65px" type="number">
                </div>
                <div class="single">
                  <span style="width: 20px"></span> <input v-model="limit.cInGL" style="width: 65px" type="number">
                </div>
                <div class="single">
                  <span style="width: 20px"> </span> <input v-model="limit.cInYL" style="width: 65px" type="number">
                </div>
              </div>
            </div>
            <div class="card-wrap">
              <div class="line-box"></div>
            </div>
            <div class="container" style="width: 200px; display: flex; margin-top:30px">
              <div class="single">
                <!--          <el-checkbox v-model="checked">限制标准差</el-checkbox>-->
                <span style="width: 60px;">标准差≤</span> <input v-model="limit.cInSH" style="width: 60px" type="number">
                <span style="color:red">注意:所有煤种都需要有反射率数据</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-form>
      <el-form ref="spaceForm" class="mb0 container" :inline="true" :model="space" :rules="spaceRules">
        <el-card style="min-height: 124px" class="card">
          <div style="width: 100%;display: flex; justify-content: space-between; align-items:center;">
            <div>
              <div class="card-title">计算操作</div>
            </div>
            <div>
              <el-button class="right-btn mr" type="primary" @click="calculate('spaceForm','OptTypeManual')">
                <div style="display: flex;align-items: center">
                  人工计算
                </div>
              </el-button>
              <el-button class="right-btn mr" type="primary" @click="calculate('spaceForm','OptTypeSpecify')">
                智能计算
              </el-button>
              <el-button class="right-btn" type="primary" @click="clearList">
                清 空
              </el-button>
            </div>
          </div>
          <div style="display:flex; justify-content:flex-end; margin-top:5px">
            <el-tag type="danger">
              <i class="el-icon-info"></i>如对自动计算存疑，请指定某些煤比例再点击指定提供方案
            </el-tag>
          </div>
        </el-card>
        <el-card class="card" v-if="active >= 4">
          <div slot="header">
            <div style="display: flex;justify-content: space-between; align-items: center">
              <div class="card-title">配煤结果
                <el-tag type="danger">
                  <i class="el-icon-info"></i> 推荐方案是大数据计算出来的不限制灰硫下限的成本最低方案
                </el-tag>
              </div>
            </div>
          </div>
          <div class="m5 table-content">
            <el-table @row-click="handleRowClick" highlight-current-row :data="space.workspaceCoalWashingResultList" show-summary
                      :summary-method="showDiff" :cell-style="columnStyle" :border="false" :row-style="activeRow"
                      :header-cell-style="activeHeader">
              <el-table-column label="配煤比例(%)" align="center">
                <el-table-column label="方案" prop="projectName" min-width="80">
                  <template slot-scope="scope">
                    <span :class="scope.row.projectName === '推荐方案'?'redColor':'black'">{{
                                                scope.row.projectName
                                            }}</span>
                  </template>
                </el-table-column>
                <el-table-column v-for="(item) in space.workspaceCoalRockList" :key="item.id" :label="`${item.name}`"
                                 :prop="item.id" min-width="80">
                </el-table-column>
              </el-table-column>
              <el-table-column label="配合煤指标" align="center">
                <el-table-column label="Ad(%)" prop="inAd" min-width="50">
                  <template slot-scope="scope">
                    <span>{{ scope.row.inAd ? scope.row.inAd.toFixed(2) : '' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="Vdaf(%)" min-width="60" prop="inVdaf">
                  <template slot-scope="scope">
                    <span>{{ scope.row.inVdaf ? scope.row.inVdaf.toFixed(2) : '' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="St,d(%)" min-width="60" prop="inStd">
                  <template slot-scope="scope">
                    <span>{{ scope.row.inStd ? scope.row.inStd.toFixed(2) : '' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="G" prop="inG" min-width="50">
                  <template slot-scope="scope">
                    <span>{{ scope.row.inG ? scope.row.inG.toFixed(2) : '' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="Y(mm)" min-width="60" prop="inY">
                  <template slot-scope="scope">
                    <span>{{ scope.row.inY ? scope.row.inY.toFixed(2) : '' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="X(mm)" min-width="60" prop="inX">
                  <template slot-scope="scope">
                    <span>{{ scope.row.inX ? scope.row.inX.toFixed(2) : '' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="R0(%)" min-width="60" prop="theoryMacR0">
                  <template slot-scope="scope">
                    <span>{{ scope.row.theoryMacR0 ? scope.row.theoryMacR0.toFixed(3) : '' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="标准差" width="70" prop="theoryMacS">
                  <template slot-scope="scope">
                    <span>{{ scope.row.theoryMacS ? scope.row.theoryMacS.toFixed(3) : '' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="价格(元/吨)" min-width="70" prop="inPrice">
                </el-table-column>
              </el-table-column>

              <el-table-column label="操作" width="100" fixed="right" prop="inPrice" align="center">
                <template slot-scope="scope">
                  <el-button type="reset_production" :loading="isLoading" @click="save(scope.$index)">
                    保存方案
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div v-if="impChartDataList.length>0" style="background:#fff;margin-top:10px;" class="m5">
            <column-chart v-if="impChartDataList[0].xData.length > 0" :impChartData="impChartDataList[0]"></column-chart>
          </div>

          <div style="margin:0 10%;background:#fff;">
            <el-table v-if="leftData.length>0" :data="leftData" :show-header="false" border>
              <el-table-column min-width="40" prop="brownCoal">
              </el-table-column>
              <el-table-column min-width="50" prop="longFlame">
              </el-table-column>
              <el-table-column min-width="60" prop="gasCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="thirdCokingCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="fatCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="cokingCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="leanCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="meagerLeanCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="meagerCoal">
              </el-table-column>
            </el-table>
          </div>

          <div v-if="impChartDataList.length>0" style="background:#fff;margin-top:10px;" class="m5">
            <column-chart v-if="impChartDataList[1].xData.length > 0" :impChartData="impChartDataList[1]"></column-chart>
          </div>

          <div style="margin:0 10%;background:#fff;">
            <el-table v-if="rightData.length>0" :data="rightData" :show-header="false" border>
              <el-table-column min-width="40" prop="brownCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="longFlame">
              </el-table-column>
              <el-table-column min-width="60" prop="gasCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="thirdCokingCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="fatCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="cokingCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="leanCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="meagerLeanCoal">
              </el-table-column>
              <el-table-column min-width="60" prop="meagerCoal">
              </el-table-column>
            </el-table>
          </div>

          <div v-if="active >= 4">
            <div class="res-title">反射率</div>
            <el-table :show-header="false" :data="rateList" border ref="rateTable">
              <el-table-column v-if="coalMacRManualList.length > 1" v-for="(item,index)  in coalMacRManualList" :key="index"
                               width="80">
                <template slot-scope="scope">{{ scope.row[item.rangeName] }}</template>
              </el-table-column>
              <el-table-column v-if="coalMacRSpecifyList.length > 1 && rateList.length === 2"
                               v-for="(item,index) in coalMacRSpecifyList" :key="item.rangeName" width="80">
                <template slot-scope="scope">{{ scope.row[item.rangeName] }}</template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
        <!-- <el-card v-if="active >= 4">
                  <div slot="header">
                    <div style="display: flex;justify-content: space-between; align-items: center">
                      <div class="card-title">反射率</div>
                    </div>
                  </div>
                  <div class="m5 table-content">
                    <el-table :show-header="false" :data="rateList" border ref="rateTable">
                      <el-table-column v-if="coalMacRManualList.length > 1" v-for="(item,index)  in coalMacRManualList" :key="index"
                                       width="80">
                        <template slot-scope="scope">{{scope.row[item.rangeName] }}</template>
                      </el-table-column>
                      <el-table-column v-if="coalMacRSpecifyList.length > 1 && rateList.length === 2"
                                       v-for="(item,index) in coalMacRSpecifyList" :key="item.rangeName" width="80">
                        <template slot-scope="scope">{{scope.row[item.rangeName] }}</template>
                      </el-table-column>
                    </el-table>

                  </div>
                </el-card> -->
      </el-form>
      <el-dialog title="选择煤种" :visible.sync="dialogFormVisible" :close-on-press-escape="false" :close-on-click-modal="false"
                 :modal="false" :before-close="resetChecked" fullscreen>
        <coal isCoalRock="Y" canChoose class="p10" ref="coal" @choose="choose" @multChoose="addCoals" isShowCheckBox></coal>
      </el-dialog>
      <el-dialog :title="entityForm.name" :visible.sync="dialogCoalEntityFormVisible" :before-close="closeDialog"
                 :close-on-press-escape="false" :close-on-click-modal="false" :modal="false" fullscreen>
        <el-row class="app-container">
          <div class="fr">
            <el-button type="primary" :loading="entityFormLoading" @click="entityFormSave">关闭</el-button>
          </div>
        </el-row>
        <coal-entity-form :entityForm="entityForm" ref="entityForm"></coal-entity-form>
      </el-dialog>
      <el-dialog title="请输入方案名称" :visible.sync="dialogProjectVisible" :close-on-press-escape="false" top="5vh"
                 class="saveSchemeDialog" :close-on-click-modal="false" :modal="false" :before-close="closeProjectNameForm"
                 width="500px">
        <el-form ref="projectNameForm" label-width="80px" :model="projectForm" v-if="dialogProjectVisible">
          <el-row>
            <el-col :span="20">
              <el-form-item label="名称" prop="name" :rules="[{required:true, message:'请输入名称 '}]">
                <el-input v-model="projectForm.name" placeholder="请输入内容">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="closeProjectNameForm">取消</el-button>
          <el-button type="primary" :loading="isLoading" @click="saveProject">提交</el-button>
        </div>
      </el-dialog>
    </div>
    <el-dialog class="addCoal" title="选择煤种" width="85%" top="3vh" :visible.sync="chooseVisible" :before-close="handleClose">
      <Coal ref="coal" name="coal" :height="650" canChoose @closeChoose="handleClose(true)"></Coal>
    </el-dialog>

    <AddCoal :add-visible="addVisible" :details="coalDetail" isSourceWash @closeVisible="handleClose(true,'add')"></AddCoal>
  </div>
</template>
<script>
import {
  getNewCoalRock,
  getWashName,
  deleteWashingById,
  saveWashProjectApi,
  coalWashingQualityOptimizeByType,
  addCoalWashing,
  remakeNewCoal,
  removeWashAllCoal,
  syncCoal,
} from '@/api/coalWashing'
import Func from '@/utils/func'

import ColumnChart from '@/components/Chart/columnChart'
import AddCoal from '@/components/Coal/addCoal'
import empty from '@/assets/my/empty.png'

const limit = {
  cInAd: null,
  cInAdH: null,
  cInAdL: null,
  cInG: null,
  cInGL: null,
  cInSH: null,
  cInStd: null,
  cInStdH: null,
  cInStdL: null,
  cInVdaf: null,
  cInVdafH: null,
  cInVdafL: null,
  cInXL: null,
  cInY: null,
  cInYL: null,
}
const entityForm = {
  id: '',
  name: '',
  batchCode: '',
  type: 'JM', // 默认焦煤
  province: '',
  factoryPrice: '',
  transitFee: '',
  roadCost: '',
  arrivePrice: '',
  arriveFactory: 'JT',
  mineDepth: '',
  rawMt: '',
  rawAd: '',
  rawPointFive: '',
  rawOnePointFour: '',
  rawAdIn: '',
  rawStd: '',
  rawVdaf: '',
  rawG: '',
  cleanVdaf: '',
  cleanAd: '',
  cleanStd: '',
  cleanMt: '',
  cleanP: '',
  procG: '',
  procY: '',
  procX: '',
  procMf: '',
  procTp: '',
  procTmax: '',
  procTk: '',
  procCrc: '',
  procA: '',
  procB: '',
  macR0: '',
  macS: '',
  macV: '',
  macI: '',
  macE: '',
  comSiO2: '',
  comAl2O3: '',
  comFe2O3: '',
  comCaO: '',
  comMgO: '',
  comNa2O: '',
  comK2O: '',
  comTiO2: '',
  comP2O5: '',
  comSO3: '',
  cfeCp: '',
  cfeCe: '',
  qualScon: '',
  qualPcon: '',
  qualM40: '',
  qualM10: '',
  qualCsr: '',
  qualCri: '',
  qualTestCond: '',
  crGk: '',
  crBk: '',
  crTk: '',
  city: '',
  dataBelong: '',
  dataType: '',
  createBy: '',
  createDate: '',
  updateBy: '',
  updateDate: '',
  remarks: '',
  ext: '',
}
const components = {
  ColumnChart,
  AddCoal,
}
export default {
  components: {
    ...components,
  },
  data() {
    return {
      addVisible: false,
      coalDetail: {},
      chooseVisible: false,
      empty,
      active: 2,
      isAutoSuccess: '',
      isSuccess: '',
      isExit: true,
      isLoading: false,
      loadingText: '',
      dialogFormVisible: false,
      dialogProjectVisible: false,
      projectForm: {
        index: 0,
        name: '',
        projectType: '',
      },
      rateList: [],
      value: '',
      checked: '',
      options: [
        {
          value: 'le',
          label: '≤',
        },
        {
          value: 'ge',
          label: '≥',
        },
        {
          value: 'eq',
          label: '=',
        },
      ],
      spaceRules: {
        bulkDensity: [{ required: true, message: '请输入堆密度', trigger: 'blur' }],
        fineness: [{ required: true, message: '请输入细度', trigger: 'blur' }],
        timeAjust: [{ required: true, message: '请输入结焦时间', trigger: 'blur' }],
        tempeAjust: [{ required: true, message: '请输入火道温度', trigger: 'blur' }],
        waterAjust: [{ required: true, message: '请输入水分', trigger: 'blur' }],
      },
      impChartDataList: [],
      space: {
        bulkDensity: 0.0,
        fineness: 0.0,
        tempeAjust: 0.0,
        timeAjust: 0.0,
        waterAjust: 0.0,
        workspaceCoalRockResultList: [{}, {}, {}],
        workspaceCoalRockList: [],
      },
      favorablePrice: [],
      favorablePriceYear: [],
      entityForm: { ...entityForm },
      dialogCoalEntityFormVisible: false,
      EntityFormIndex: void 0,
      entityFormLoading: false,
      isComputed: false,
      isAddCoal: false,
      coalMatchingData: [{}, {}, {}, {}, {}], // 配煤比例数据
      limit: { ...limit },
      count: 0,
      count1: 0,
      count2: 0,
      count3: 0,
      coalMacRManualList: [],
      coalMacRSpecifyList: [],
      restaurants: [],
      time: '',
      isShow: true,
      config: {},
      activeRowList: [],
      projectType: '',
      leftData: [],
      chartId: '',
      rightData: [],
      rateIndex: 0,
      newRateIndex: 0,
    }
  },
  computed: {
    workspaceCoalRockListLen() {
      return this.space.workspaceCoalRockList.length
    },
  },
  created() {
    if (this.cokeId) {
      this.remakeCoalRock(this.cokeId)
      // console.log(this.cokeId, 'cccc')
    } else {
      this.getNewCoalRock()
    }
  },
  async mounted() {
    this.restaurants = await this.loadingAll()
  },
  methods: {
    handleClose(isRefresh, isAdd) {
      this.chooseVisible = false

      if (isAdd) {
        this.addVisible = false
        this.coalDetail = {}
        this.$refs.coal.getList()
      }

      if (isRefresh) {
        this.getNewCoalRock()
      }
    },
    // 数据同步
    async makeSyncCoal(row) {
      await this.$confirm('保存成功会同步该条数据信息，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      const workspace = { ...row }
      this.isLoading = true
      const res = await Func.fetch(syncCoal, workspace)
      this.isLoading = false
      if (res) {
        this.$message({
          type: 'success',
          message: '该数据信息同步成功',
        })
      }
    },
    // 更改表头颜色
    activeHeader() {
      return 'background: #2F79E8; color:#fff'
    },
    columnStyle({ row, column, rowIndex, columnIndex }) {
      // 加上第一行和动态的workspaceCoalRockListLen长度
      if (columnIndex < this.workspaceCoalRockListLen + 1) {
        return 'background: #fff6e6'
      }
      return ''
    },

    // 更改表体颜色
    activeRow({ row, rowIndex }) {
      if (rowIndex % 2 === 1) {
        return 'background: #F3FCFD'
      } else if (rowIndex % 2 === 0) {
        return 'background: #fff'
      }
      return ''
    },
    async remakeCoalRock(id) {
      this.isLoading = true
      const res = await Func.fetch(remakeNewCoal, { type: 'YH', coalWashingProjectId: id })
      if (res) {
        this.active = 4
        this.isLoading = false
        this.space = { ...res.data }
        this.limit = { ...this.space }
        // 动态显示煤种表头 数据处理
        this.space.workspaceCoalRockList.map((item, index) => {
          let id = item.id
          this.space.workspaceCoalWashingResultList.map((wash) => {
            wash[id] = wash.coalPercentList[index]
            return wash
          })
        })
        // 当页面查询移除或者添加煤种时  重新计算煤种配比
        let number = 0
        if (this.space.workspaceCoalRockList) {
          this.space.workspaceCoalRockList.map((item) => {
            number = Number(item.quantity) + number
          })
          this.space.workspaceCoalRockList.map((item) => {
            if (item.quantity) {
              item.percent2 = ((item.quantity * 100) / number).toFixed(2)
            } else {
              item.percent2 = 0
            }
            return item
          })
        }
        // 添加Echart数据
        this.impChartDataList = []
        let chartObj = {}
        chartObj.id = 'rg'
        chartObj.xName = '区间'
        chartObj.type = 'bar'
        chartObj.color = '#F8A20F'
        chartObj.yData = res.data.proportionManualList
        chartObj.xData = res.data.rangeManualList
        this.impChartDataList.push(chartObj)
        let chartObj1 = {}
        chartObj1.name = '智能'
        chartObj1.xName = '区间'
        chartObj1.type = 'bar'
        chartObj1.color = '#FF726B'
        chartObj1.yData = res.data.proportionSpecifyList
        chartObj1.xData = res.data.rangeSpecifyList
        this.impChartDataList.push(chartObj1)
        // echart下面列表显示
        this.leftData = res.data.coalTypeProportionManualList
        this.rightData = res.data.coalTypeProportionSpecifyList
        // 反射率列表显示
        let obj = {}
        let obj2 = {}
        let obj3 = {}
        let extlist = [
          {
            rangeCount: '人工',
            rangeName: '区间',
            sort: null,
          },
        ]
        let extlist2 = [
          {
            rangeCount: '智能',
            rangeName: '区间',
            sort: null,
          },
        ]
        this.rateList = []
        this.coalMacRManualList = [...extlist, ...res.data.coalMacRManualList]
        this.coalMacRSpecifyList = [...extlist2, ...res.data.coalMacRSpecifyList]
        if (res.data.coalMacRSpecifyList.length > 0) {
          this.coalMacRSpecifyList.map((coal) => {
            obj3[coal.rangeName] = coal.rangeCount
            obj2[coal.rangeName] = coal.rangeName
          })
          this.rateList = [{ ...obj2 }, { ...obj3 }]
        }
        if (res.data.coalMacRManualList.length > 0) {
          this.coalMacRManualList.map((coal) => {
            obj2[coal.rangeName] = coal.rangeName
            obj[coal.rangeName] = coal.rangeCount
          })
          this.rateList = [{ ...obj2 }, { ...obj }]
        }
        // if (obj2 && obj && obj3) {
        //   this.rateList = [{...obj2}, {...obj}, {...obj3}]
        // }
      }
    },
    // 点击行事件
    handleRowClick(row) {
      this.activeRowList = row
      this.isLoading = false
      let chartObj1 = {}
      chartObj1.name = row.projectName
      chartObj1.xName = '区间'
      chartObj1.type = 'bar'
      chartObj1.yData = row.proportionList
      chartObj1.xData = row.rangeList
      this.impChartDataList = [{ ...this.impChartDataList[0] }, { ...chartObj1 }]
      this.rightData = row.coalTypeProportionList
      let obj3 = {}
      // let extlist = [{
      //   rangeCount: '人工',
      //   rangeName: '区间',
      //   sort: null
      // }]
      let extlist2 = [
        {
          rangeCount: '智能',
          rangeName: '区间',
          sort: null,
        },
      ]
      this.coalMacRManualList = [...extlist2, ...row.coalMacRList]
      this.coalMacRManualList.map((coal) => {
        obj3[coal.rangeName] = coal.rangeCount
      })
      if (this.rateList.length === 2) {
        this.rateList = [{ ...this.rateList[0] }, { ...obj3 }]
      }
      if (this.rateList.length === 3) {
        this.rateList = [{ ...this.rateList[0] }, { ...this.rateList[1] }, { ...obj3 }]
      }
    },
    // 自定义合计算法
    showDiff(param) {
      const { data } = param
      let row = data[0]
      let length = 0
      if (this.space.workspaceCoalRockList.length) {
        length = this.space.workspaceCoalRockList.length
      }
      // 获取煤种动态长度
      const sums = []
      if (this.activeRowList && row) {
        sums[length] = '-'
        sums[0] = '对比'
        if (row.inAd && this.activeRowList.inAd) {
          let index = Number(length) + 1
          sums[index] = (row.inAd - this.activeRowList.inAd).toFixed(2)
          // 根据煤种动态长度显示  (本来写循环显示但是好像死循环)
        }
        if (row.inVdaf && this.activeRowList.inVdaf) {
          let index = Number(length) + 2
          sums[index] = (row.inVdaf - this.activeRowList.inVdaf).toFixed(2)
        }
        if (row.inStd && this.activeRowList.inStd) {
          let index = Number(length) + 3
          sums[index] = (row.inStd - this.activeRowList.inStd).toFixed(2)
        }
        if (row.inG && this.activeRowList.inG) {
          let index = Number(length) + 4
          sums[index] = (row.inG - this.activeRowList.inG).toFixed(2)
        }
        if (row.inY && this.activeRowList.inY) {
          let index = Number(length) + 5
          sums[index] = (row.inY - this.activeRowList.inY).toFixed(2)
        }
        if (row.inX && this.activeRowList.inX) {
          let index = Number(length) + 6
          sums[index] = (row.inX - this.activeRowList.inX).toFixed(2)
        }
        if (row.inR0 && this.activeRowList.inR0) {
          let index = Number(length) + 7
          sums[index] = (row.inR0 - this.activeRowList.inR0).toFixed(2)
        }
        if (row.theoryMacS && this.activeRowList.theoryMacS) {
          let index = Number(length) + 8
          sums[index] = (row.theoryMacS - this.activeRowList.theoryMacS).toFixed(2)
        }
        if (row.inPrice && this.activeRowList.inPrice) {
          let index = Number(length) + 9
          sums[index] = (row.inPrice - this.activeRowList.inPrice).toFixed(2)
        }
        let endIndex = Number(length) + 10
        sums[endIndex] = '-'
      }
      return sums
    },
    onFcous() {
      this.space.workspaceCoalRockList.map((item) => {
        if (Number(item.quantity) === 0) {
          item.quantity = ''
        }
        return item
      })
    },
    loseFcous(index, row, column) {
      let number = 0
      this.space.workspaceCoalRockList.map((item) => {
        number = Number(item.quantity) + number
      })
      this.space.workspaceCoalRockList.map((item) => {
        if (item.quantity > 0) {
          item.percent2 = ((item.quantity * 100) / number).toFixed(2)
        } else {
          item.percent2 = 0
        }
        return item
      })
    },
    /*
     * 单选增加煤种
     * */
    async choose(coal) {
      this.dialogFormVisible = false
      this.resetChecked()
      const res = await Func.fetch(addCoalWashing, { spaceType: this.spaceType, coalIdList: [coal.id] })
      if (res) {
        this.isAddCoal = true
        if (res) {
          this.isAddCoal = true
          if (!res.data.length) return false
          this.getNewCoalRock()
        }
      }
    },
    /*
     * 多选增加煤种
     * */
    async addCoals(list) {
      const checkBoxGather = this.$refs.coal.checkBoxGather
      const resultGather = []
      for (let i in checkBoxGather) {
        if (checkBoxGather.hasOwnProperty(i) && checkBoxGather[i]) {
          resultGather.push(i)
        }
      }
      if (list.length === 0) {
        this.$message.error('至少选择一个')
        return
      }
      const res = await Func.fetch(addCoalWashing, { spaceType: this.spaceType, coalIdList: list })
      this.resetChecked()
      if (res) {
        this.isAddCoal = true
        if (!res.data.length) return false
        this.getNewCoalRock()
      }
    },
    /*
     * 重置多选状态并隐藏对话框
     * */
    resetChecked() {
      const checkBoxGather = this.$refs.coal.checkBoxGather
      for (let i in checkBoxGather) {
        if (checkBoxGather.hasOwnProperty(i)) {
          checkBoxGather[i] = false
        }
      }
      this.dialogFormVisible = false
    },
    /*
     * 显示选煤对话框
     * */
    add() {
      this.chooseVisible = true
      // this.$router.push('/coal?activeTitle=MWASH')
    },
    /*
     * 移除一行数据
     * */
    async remove(row) {
      await this.$confirm('移除煤种会清空当前方案数据，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      this.isLoading = true
      const res = await Func.fetch(deleteWashingById, { id: row.id, spaceType: 'YH' })
      this.isLoading = false
      if (res) {
        this.$message({
          type: 'success',
          message: '删除成功',
        })
        this.getNewCoalRock()
        // this.space = {...limit}
        this.impChartDataList = [{}, {}]
        this.rateList = []
      }
    },

    /*
     * 获取数据
     * */
    async getNewCoalRock() {
      this.isLoading = true
      const res = await Func.fetch(getNewCoalRock, { type: 'YH' })
      // console.log(res)
      // let extList = res.data.rangeList
      // let list = res.data.workspaceCoalRockList
      // if (list.length > 0) {
      //     this.active = 3
      // }
      // if (list.length > 0 && extList.length > 0) {
      //     this.active = 4
      // } else {
      //     this.active = 2
      // }
      if (res.data) {
        let newExtList = res.data.workspaceCoalWashingResultList
        let newlist = res.data.workspaceCoalRockList
        if (newlist.length > 0) {
          this.active = 3
        }
        if (newlist.length > 0 && newExtList.length > 0) {
          this.active = 4
        } else {
          this.active = 2
        }
        this.isLoading = false
        this.space = { ...res.data }
        this.limit = { ...this.space }
        // 动态显示煤种表头 数据处理
        this.space.workspaceCoalRockList.map((item, index) => {
          let id = item.id
          this.space.workspaceCoalWashingResultList.map((wash) => {
            wash[id] = wash.coalPercentList[index]
            return wash
          })
        })
        // 当页面查询移除或者添加煤种时  重新计算煤种配比
        let number = 0
        if (this.space.workspaceCoalRockList) {
          this.space.workspaceCoalRockList.map((item) => {
            number = Number(item.quantity) + number
          })
          this.space.workspaceCoalRockList.map((item) => {
            if (item.quantity) {
              item.percent2 = ((item.quantity * 100) / number).toFixed(2)
            } else {
              item.percent2 = 0
            }
            return item
          })
        }
        // 添加Echart数据
        this.impChartDataList = []
        let chartObj = {}
        chartObj.name = '人工'
        chartObj.color = '#FF726B'
        chartObj.xName = '区间'
        chartObj.type = 'bar'
        chartObj.yData = res.data.proportionManualList
        chartObj.xData = res.data.rangeManualList
        this.impChartDataList.push(chartObj)
        let chartObj1 = {}
        chartObj1.name = '智能'
        chartObj1.xName = '区间'
        chartObj1.type = 'bar'
        chartObj1.color = '#33CAD9'
        chartObj1.yData = res.data.proportionSpecifyList
        chartObj1.xData = res.data.rangeSpecifyList
        this.impChartDataList.push(chartObj1)
        // echart下面列表显示
        this.leftData = res.data.coalTypeProportionManualList
        this.rightData = res.data.coalTypeProportionSpecifyList
        // 反射率列表显示
        let obj = {}
        let obj2 = {}
        let obj3 = {}
        let extlist = [
          {
            rangeCount: '人工',
            rangeName: '区间',
            sort: null,
          },
        ]
        let extlist2 = [
          {
            rangeCount: '智能',
            rangeName: '区间',
            sort: null,
          },
        ]
        this.coalMacRManualList = [...extlist, ...res.data.coalMacRManualList]
        this.coalMacRSpecifyList = [...extlist2, ...res.data.coalMacRSpecifyList]
        if (res.data.coalMacRSpecifyList.length > 0) {
          this.rateList = []
          this.coalMacRSpecifyList.map((coal, index) => {
            obj3[coal.rangeName] = coal.rangeCount
            obj2[coal.rangeName] = coal.rangeName
            if (coal.rangeCount > 0) {
              this.rateIndex = index + 1
              return true
            }
          })
          this.rateList = [{ ...obj2 }, { ...obj3 }]
        }
        if (res.data.coalMacRManualList.length > 0) {
          this.rateList = []
          this.coalMacRManualList.map((coal, index) => {
            obj2[coal.rangeName] = coal.rangeName
            obj[coal.rangeName] = coal.rangeCount
            if (coal.rangeCount > 0) {
              this.newRateIndex = index + 1
            }
          })
          this.rateList = [{ ...obj2 }, { ...obj }]
        }
        if (res.data.coalMacRSpecifyList.length > 0 && res.data.coalMacRManualList.length > 0) {
          this.rateList = []
          this.coalMacRSpecifyList.map((coal) => {
            obj3[coal.rangeName] = coal.rangeCount
          })
          this.coalMacRManualList.map((coal) => {
            obj2[coal.rangeName] = coal.rangeName
            obj[coal.rangeName] = coal.rangeCount
          })
          this.rateList = [{ ...obj2 }, { ...obj }, { ...obj3 }]
        }
      }
    },
    /*
     * 计算操作
     * */
    calculate: async function (formName, type) {
      // 至少选择一条数据
      if (this.space.workspaceCoalRockList.length === 0) {
        this.$message.error('至少选择一条来源数据')
        return false
      }
      // 至少选择一条数据
      let message = ''
      let valid = true
      // // 列表字段校验
      this.space.workspaceCoalRockList.forEach((val) => {
        if (val.cleanAd < 0) {
          valid = false
          message = val.name + 'Ad不能为负值'
          return false
        }

        if (val.cleanVdaf < 0) {
          valid = false
          message = val.name + 'Vdaf不能为负值'
          return false
        }

        if (val.cleanStd < 0) {
          valid = false
          message = val.name + 'St,d不能为负值'
          return false
        }

        if (val.procG < 0) {
          valid = false
          message = val.name + 'G不能为负值'
          return false
        }

        if (val.procY < 0) {
          valid = false
          message = val.name + 'Y不能为负值'
          return false
        }

        if (val.procX < 0) {
          valid = false
          message = val.name + 'X不能为负值'
          return false
        }

        if (val.macR0 < 0) {
          valid = false
          message = val.name + 'R0不能为负值'
          return false
        }
      })
      if (!valid) {
        this.$message.error(message)
        return false
      }
      // console.log(message)
      // valid = await this.$refs[formName].validate()
      // if (!valid) {
      //   this.$message.error(message)
      //   return false
      // }
      //
      const data = { ...this.space, ...this.limit }
      if (type === 'OptTypeSpecify' || type === 'OptTypeManual') {
        this.loadingText = '正在努力计算中,请稍后...'
      }
      this.isLoading = true
      const _data = { ...data, ...this.limit, optimizeType: type }
      const res = await Func.fetch(coalWashingQualityOptimizeByType, _data)
      this.isLoading = false
      this.loadingText = ''
      if (res) {
        this.getNewCoalRock()
        this.active = 4
      }
    },
    /*
     * 打开修改煤种信息对话框
     * @param row
     * */
    edit(row) {
      const deatils = { ...row, id: row.coalId } // 传入煤种对id
      this.coalDetail = deatils
      this.addVisible = true
      // this.$router.push({
      //     name: 'myDetails',
      //     params: {row, isShow: true, title: row.name, current: 'CoalDetails'}
      // })
    },
    /*
     * 关闭修改煤种信息对话框并重置状态
     * */
    closeDialog() {
      this.entityForm = { ...entityForm }
      this.$refs.entityForm.$children[0].resetFields()
      this.dialogCoalEntityFormVisible = false
    },
    /*
     * 保存修改状态
     * */
    async entityFormSave() {
      const entityForm = this.$refs.entityForm.$children[0]
      if (await entityForm.validate()) {
        this.entityFormLoading = true
        const data = { ...entityForm.model }
        this.space.workspaceCoalRockList.splice(this.EntityFormIndex, 1, data)
        this.entityFormLoading = false
        this.closeDialog()
      }
    },
    /*
     * 显示保存方案对话框
     * */
    async save(index) {
      this.dialogProjectVisible = true
      this.projectForm.index = index
      this.$nextTick(() => {
        this.$refs['projectNameForm'].resetFields()
      })
    },
    /*
     * 关闭保存方案对话框并重置状态
     * */
    closeProjectNameForm() {
      this.$refs['projectNameForm'].resetFields()
      this.dialogProjectVisible = false
    },
    /*
     * 保存方案
     * */
    async saveProject() {
      const valid = await this.$refs['projectNameForm'].validate()
      if (!valid) {
        return false
      }
      let data = { ...this.space }
      if (this.isOptimization) {
        data = { ...data, ...this.limit }
      }
      data.workspaceCoalWashingResultList = data.workspaceCoalWashingResultList.map((val) => {
        delete val.coal
        return val
      })
      data.forecastItemDto = data.workspaceCoalWashingResultList[this.projectForm.index]
      data.projectName = this.projectForm.name
      data.projectType = this.projectForm.projectType
      data.spaceType = this.spaceType
      delete data.workspaceCoalWashingResultList
      const res = await Func.fetch(saveWashProjectApi, data)
      this.isLoading = false
      if (res) {
        this.$message({
          showClose: true,
          message: '保存成功',
          type: 'success',
        })
        this.closeProjectNameForm()
      }
    },
    /*
     * 清空列表
     * */
    async clearList() {
      this.isLoading = true
      const res = await Func.fetch(removeWashAllCoal, { spaceType: this.spaceType })
      this.isLoading = false
      if (res) {
        this.getNewCoalRock()
        this.active = 2
      }
    },
    /*
     * 配比列头渲染函数
     * */
    percentColRender(createElement, { column, $index }) {
      const { label, property } = column
      return createElement(
        'span',
        {
          style: {
            'line-height': '22px',
            'padding-right': '0',
            color: '#000',
          },
        },
        [
          label,
          label !== '指定' && property === 'percent2'
            ? `(${this.count.toFixed()})%`
            : label !== '人工' && property === 'percent1'
            ? `(${this.count1.toFixed()})%`
            : property === 'percent5'
            ? `(${this.count2.toFixed()})%`
            : `(${this.count3.toFixed()})%`,
          createElement('el-button', {
            attrs: {
              size: 'mini',
              icon: 'el-icon-delete',
            },
            style: {
              float: 'right',
            },
            on: {
              click: (_) => {
                let { workspaceCoalRockList } = this.space
                workspaceCoalRockList = workspaceCoalRockList.map((val) => {
                  val[property] = 0
                  return val
                })
                this.space.workspaceCoalRockList = [...workspaceCoalRockList]
              },
            },
          }),
        ]
      )
    },
    /*
     *  保存方案公司信息
     * */
    async loadingAll() {
      const res = await Func.fetch(getWashName, { projectType: 'LL', jtAd: 12, jtStd: 0.6 })
      // console.log(res.data)
      return res.data

      // return [
      //   { 'name': '执行-Ad-13-S-0.7', 'projectType': 'ZX' },
      //   { 'name': '试验-Ad-13-S-0.7', 'projectType': 'SY' },
      //   { 'name': '理论-Ad-13-S-0.7', 'projectType': 'LL' }
      // ]
    },
    querySearch(queryString, cb) {
      const restaurants = this.restaurants
      const results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (restaurants) => {
        return restaurants.name.indexOf(queryString) === 0
      }
    },
    handleSelect(item) {
      this.projectForm.name = item.name
    },
  },
  props: {
    isOptimization: {
      //  是否是optimization页面
      type: [Boolean],
      default: false,
    },
    spaceType: {
      //  类型字段
      type: [String],
      required: true,
    },
    cokeId: {
      type: [String],
      required: false,
    },
    formType: {
      type: [String],
      required: false,
    },
  },
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.saveSchemeDialog {
  ::v-deep .el-dialog__body {
    height: auto;
  }
}

.addCoal {
  ::v-deep .el-dialog__body {
    padding: 0 35px;
    height: 85vh;
  }
}

::v-deep .el-select .el-input .el-select__caret {
  color: #f8a20f;
}

.yellow-input ::v-deep .el-input__inner,
.el-input__inner:focus {
  border-radius: 4px;
  border: 1px solid #f8a20f;
  color: #f8a20f;
}

::v-deep .el-select .el-input.is-focus .el-input__inner,
::v-deep .el-select .el-input__inner:focus {
  border: 1px solid #f8a20f;
}

.card-wrap {
  position: absolute;
  margin-top: 15px;
  width: 1.5px;
  height: 80px;
  border-radius: 3px;
  background: #ebebeb;
  right: 31%;
  top: 50%;
  transform: translateY(-50%);
  margin-top: 10px;
}

.res-title {
  display: flex;
  justify-content: center;
  margin: 30px 0;
  font-size: 18px;
}

::v-deep .el-card {
  border: none;
}

::v-deep .el-card.is-always-shadow {
  box-shadow: none;
}

::v-deep .el-collapse {
  border: none;
}

::v-deep .el-collapse-item__wrap {
  border: none;
}

::v-deep .el-tag.el-tag--danger {
  margin-bottom: 5px;
}

::v-deep .el-table .cell .el-input__inner {
  font-size: 12px;
  height: 30px;
  line-height: 30px;
}

::v-deep .el-table .cell {
  font-size: 12px;
  font-family: unset;
  height: 30px;
  line-height: 30px;
}

::v-deep .el-table .cell .el-input__inner {
  font-size: 12px;
  font-family: unset;
  height: 30px;
  line-height: 30px;
}

.content {
  background: #f7f7f7;
  padding: 0 10px;
  height: 100%;
}

.app-layout {
  height: 100%;
}

.single-wrap {
  font-weight: 400 !important;
  text-align: right;
}

.card-title {
  font-size: 14px;
  height: 16px;
  line-height: 16px;
  // font-weight: bold;
  border-left: #33cad9 2px solid;
  padding-left: 4px;
}

.action {
  height: 28px;
  line-height: 28px;
}

.table-content {
  display: flex;
  width: 100%;
  position: relative;

  .table-title {
    width: 120px;
    font-size: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ebeef5;
    border-right: none;
  }

  .el-table {
    flex: 1;
  }

  .table-action {
    padding: 0 5px;
    display: flex;
    flex-direction: column;
    position: absolute;
    right: 0;
    bottom: 2px;

    button {
      margin-top: 5px;
    }
  }
}

.mb0 {
  .el-form-item--mini.el-form-item,
  .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }
}

.textCenter {
  text-align: center;
  vertical-align: top;
  font-size: 12px;
  margin-top: 8px;
}

.tc {
  text-align: center;
  margin-left: -6px;
}

.container {
  width: 100%;
  display: inline-block;

  p {
    font-size: 14px;
    color: #595ec9;
    margin: 0;
    padding-left: 5px;
    font-weight: bold;
    line-height: 10px;
    height: 20px;
  }

  .single {
    display: inline-block;
    height: 28px;
    line-height: 28px;

    > span {
      display: inline-block;
      margin: 0 5px;
      font-size: 12px;
      font-weight: 600;
      color: #606266;
    }

    > input {
      height: 28px;
      line-height: 28px;
      border: 1px solid rgb(220, 223, 230);
      border-radius: 4px;
      width: 60px;
      box-sizing: border-box;
      padding: 10px 10px;
    }
  }
}

.mr {
  margin-right: 5px !important;
}

.table-title-bg {
  background-color: #f5f7fa;
}

/*.el-autocomplete /deep/ input {*/
/*  width: 260px*/
/*}*/

/*/deep/ .el-table__body tr.current-row > td {*/
/*  background-color: #ffe1e1 !important;*/
/*}*/

.title-text {
  width: 80px;
  text-align: center;
  text-indent: 2em;
  // transform: scale(0.8);
}

.card {
  margin-bottom: 10px;

  // margin: px 0;
}

.tag {
  display: flex;
  justify-content: flex-end;
  margin: 5px 0;
}

.steps {
  display: flex;
  width: 116%;
  padding: 5px 30px;

  .step-out {
    position: relative;
    flex: 1;
    display: flex;
    // flex-direction: column;
    align-items: center;

    .step {
      /*flex: 1;*/
      width: 30px;
      height: 30px;
    }

    .line {
      // position: absolute;
      // width: calc(100% - 32px);
      /*height: 0;*/
      flex: 1;
      border: 2px solid #f8a20f;
      background-color: #f8a20f;
      border-radius: 2px;
      margin: 0 8px;
      // top: 16px;

      // left: calc(50% + 16px);
    }

    .gray-line {
      border-radius: 2px;
      margin: 0 8px;
      flex: 1;
      background-color: #ccc;
      border: 2px solid #ccc;
    }
  }
}

.gray-step {
  /*flex: 1;*/
  width: 30px;
  height: 30px;
  margin: 5px;
  text-align: center;
  line-height: 30px;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  border-radius: 50%;
  background: #ccc;
}

.checkBg {
  background: url('../../assets/my/checkBg.png');
  background-size: 35px 35px;
  height: 35px;
  width: 35px;
  text-align: center;
  line-height: 35px;
  font-size: 14px;
  // font-weight: bold;
  color: #fff;
  border-radius: 50%;
  text-indent: 0.05rem;
}

.text {
  line-height: 25px;
  font-size: 14px;
  margin-left: 5px;
  color: #f8a20f;
}

.under-line {
  color: #33cad9;
  text-decoration: underline;
  padding-bottom: 2px;
}

.black {
  color: #000;
}

.redColor {
  color: red;
}
</style>
