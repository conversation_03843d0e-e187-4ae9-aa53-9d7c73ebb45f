<template>
  <div id="mapDemo">
    <el-amap
      ref="map"
      vid="amapDemo"
      :amap-manager="amapManager"
      :events="mapEvents"
      :zoom="zoom"
      :center="centerPosition"
      class="amap-demo"
    >
      <el-amap-marker
        class="selectedMarker"
        v-for="(item, index) in citys"
        :key="index"
        :position="item.lnglat"
        topWhenClick="true"
        :extData="item"
        :events="markerEvents"
        :content="getMarkerContent(item, 85, 30)"
      >
      </el-amap-marker>
      <el-amap-polygon
        v-for="item in citys"
        :path="item.city"
        :key="item.mark"
        :draggable="false"
        :fillColor="item.fillColor"
        fillOpacity="0.5"
        strokeWeight="0"
        strokeColor="#7A84D9"
        strokeOpacity="0.5"
      ></el-amap-polygon>
    </el-amap>
  </div>
</template>

<style lang="scss" scoped>
.amap-demo {
  height: calc(100vh - 50px);
}
</style>
<script>
import { AMapManager } from 'vue-amap'

let amapManager = new AMapManager()
export default {
  props: {
    citys: {
      type: Array,
      required: true
    },
    centerPosition: {
      type: Array,
      required: true
    }
  },
  data() {
    let self = this
    return {
      plugin: {
        pName: 'Geolocation',
        events: {}
      },
      amapManager,
      zoom: 6,
      // centerPosition: [112.53, 37.87], // 用户当前位置经纬度
      // centerPosition: centerPosition, // 用户当前位置经纬度
      clickedMarker: null,
      markerEvents: {
        click(e) {
          const data = e.target.getExtData()
          self.$emit('cityClick', data)
        }
      },
      mapEvents: {
        init(o) {
          o.setMapStyle('amap://styles/macaron') // 自定义的高德地图的样式，我选的是马卡龙
        }
      }
    }
  },
  mounted() {},
  methods: {
    getMarkerContent(item, width, height) {
      let backgroundColor = 'rgba(0,0,0,.4)'
      let color = item.num > 0 ? '#FF726B' : '#33CAD9'
      let content = ''
      if (item.num > 0) {
        content = `<div style="
                                      display: flex;
                                      justify-content: space-between;
                                      padding: 0 5px;
                                      align-items: center;
                                      height: ${height - 6}px;
                                      /*width: ${width}px;*/
                                      border-radius: 20px;
                                      font-family: Arial-BoldMT;
                                      font-size: 16px;
                                      color: ${color};
                                      box-shadow: 2px 2px 4px 0 rgba(0,0,0,0.30);
                                      background-color: ${backgroundColor};">
                                         <div style="font-size: 10px; padding: 0 5px; width: 40px; color: #fff"> ${item.title} </div>
                                         <div style="font-size: 10px;background: #fff; padding: 3px; border-radius: 10px"> ${
                                           item.num
                                         } </div>
                           </div>`
      } else {
        content = `<div style="color:${color};"></div>`
      }
      return content
    }
  }
}
</script>
