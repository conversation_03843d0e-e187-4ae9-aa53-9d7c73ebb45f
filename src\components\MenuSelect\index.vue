<template>
  <el-select :value="value" :clearable="clearable" placeholder="请选择菜单" remote filterable :remote-method="search"
             :loading="loading" :disabled="disabled" @input="change($event)">
    <el-option v-for="item in list" :key="item.code" :label="item.name" :value="item.code">
    </el-option>
  </el-select>
</template>

<script>
import { findByKeyword } from '@/api/common'

export default {
  name: 'MenuSelect',
  data() {
    return {
      loading: false,
      list: []
    }
  },
  props: {
    value: {
      required: true
    },
    name: {
      type: String
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  mounted() {
    this.list.push({ code: 'root', name: '顶级菜单' })
  },
  watch: {
    'name'(val) {
      this.list = []
      if (val) {
        this.list.push({ code: this.value, name: val })
      }
      this.list.unshift({ code: 'root', name: '顶级菜单' })
    }
  },
  methods: {
    search(keyword) {
      findByKeyword(keyword).then(response => {
        this.list = response.data
        this.list.unshift({ code: 'root', name: '顶级菜单' })
      })
    },
    change(val) {
      this.$emit('input', val)
    }
  }
}
</script>
