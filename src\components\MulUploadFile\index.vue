<template>
    <div style="display: flex">
        <el-upload
                :class="{hide:hideUpload}"
                class="upload-demo"
                action=""
                :http-request="upload"
                :file-list="initList"
                :on-remove="removeFileHandler"
                :on-exceed="exceedHandler"
                multiple
                :limit="limit">
            <el-button size="small" type="primary">点击上传</el-button>
        </el-upload>
        <!--        <div v-if="FileName !== ''" class="fileName">-->
        <!--            <i class="el-icon-folder-opened"></i>-->
        <!--            <div @click="openUrl(value)">{{FileName}}</div>-->
        <!--            <i class="el-icon-circle-close i-show" @click="deleteFile"></i>-->
        <!--        </div>-->
    </div>
</template>

<script>
    // import {uploadFile} from '@/api/public'

    // const COS = window.COS
    // const {bucket, endpoint, region, sid, skey} = JSON.parse(process.env.VUE_APP_COS)
    export default {
        name: 'MulUploadFile',
        data() {
            return {
                fileData: '', // 文件上传数据（多文件合一）
                tableData: [],
                fileList: [], // 映射数组
                hideUpload: false,
                initList: [], // 初始显示列表
                FileName: ''
            }
        },
        props: {
            list: { // 初始列表
                type: [Array],
                default: () => ([]) // [{url:''}]
            },
            value: {
                default: ''
            },
            limit: {
                type: Number,
                default: 1
            },
            only: {
                type: [Boolean],
                default: false
            }
        },
        created() {
            // const FileName = this.value.replace(/[^\\\/]*[\\\/]+/g, '')
            // this.FileName = FileName
            // let list = []
            // if (this.value) {
            //     list = this.value.split(',')
            // }
            // list.forEach((v, i) => {
            //     this.list.push({
            //         unique: `${i}${this.guid()}`,
            //         name: v.replace(/[^\\\/]*[\\\/]+/g, ''),
            //         url: v
            //     })
            // })
            // const mapGather = this.list
            // this.cos = new window.COS({
            //     getAuthorization(options, callback) {
            //         // 用于判断当前请求是否合法,返回值是计算得到的鉴权凭证字符串
            //         const authorization = window.COS.getAuthorization({
            //             SecretId: sid,
            //             SecretKey: skey,
            //             Method: options.Method,
            //             Key: options.Key
            //         })
            //         callback(authorization)
            //     }
            // })
            // this.fileList = JSON.parse(JSON.stringify(mapGather))
            // this.remoteFileList = JSON.parse(JSON.stringify(mapGather))
            // this.initList = JSON.parse(JSON.stringify(mapGather))
            // console.log(this.fileList)
        },
        watch: {
            value(val) {
            }
        },
        methods: {
            // 上传触发前 对文件格式的校验只校验了excel文件
            // beforeAvatarUpload(file) {
            //     let FileExt = file.name.replace(/.+\./, '').toLowerCase()
            //     if (this.only) {
            //         let flag = ['xls', 'xlsx'].includes(FileExt)
            //         if (!flag) this.$message.error('只能上传excel文件!')
            //         return flag
            //     } else {
            //         let flag = ['xls', 'xlsx', 'png', 'jpeg', 'jpg'].includes(FileExt)
            //         if (!flag) this.$message.error('只能上传excel文件!')
            //         return flag
            //     }
            // },
            openUrl(name) {
                window.open(name)
            },
            // 删除文件
            // deleteFile() {
            //     this.$confirm('确定删除?', '提示', {
            //         confirmButtonText: '确定',
            //         cancelButtonText: '关闭',
            //         type: 'warning'
            //     }).then(() => {
            //         this.FileName = ''
            //         this.$message({
            //             type: 'success',
            //             message: '删除成功!'
            //         })
            //     }).catch(() => {
            //         this.$message({
            //             type: 'info',
            //             message: '已取消删除'
            //         })
            //     })
            // },
            // uploadFileOSS(fileName, file, callback) {
            //     fileName = 'tpt-file/' + fileName
            //     const client = new OSS({
            //         region: region,
            //         accessKeyId: sid,
            //         accessKeySecret: skey,
            //         bucket: bucket
            //     })
            //     client.put(fileName, file).then((res) => {
            //         if (res) {
            //             callback(null, {key: endpoint + '/' + res.name})
            //         }
            //     })
            // },
            // uploadFileCOS(fileName, file, callback) {
            //     const Key = 'yzq-upload/' + fileName
            //     const cos = new COS({
            //         getAuthorization(options, callback) {
            //             // 用于判断当前请求是否合法,返回值是计算得到的鉴权凭证字符串
            //             const authorization = window.COS.getAuthorization({
            //                 SecretId: sid,
            //                 SecretKey: skey,
            //                 Method: options.Method,
            //                 Key: options.Key
            //             })
            //             callback(authorization)
            //         }
            //     })
            //     cos.putObject(
            //         {
            //             Bucket: bucket,
            //             Region: region,
            //             Key,
            //             Body: file
            //         }, (err) => {
            //             if (!err) {
            //                 callback(null, {url: `${endpoint}/${Key}`})
            //             }
            //         })
            // },
            // async addFileHandler(currentFile) {
            //     console.log(currentFile)
            //     const file = JSON.stringify(currentFile.raw)
            //     console.log(file)
            //     if (this.limit === 1) {
            //         let res = await uploadFile({file})
            //         console.log(res)
            //         this.$emit('input', file)
            //     } else {
            //         this.$nextTick(async () => {
            //             let res = await uploadFile({file})
            //             console.log(res)
            //             // 注意是files还是fileList
            //             // this.fileList.push(currentFile)
            //             // let newFile = JSON.stringify(this.fileList)
            //             // this.$emit('input', newFile)
            //         })
            //     }
            // },
            // backList() {
            //     const backList = []
            //     this.fileList.forEach(v => {
            //         if (v.url) {
            //             if (v.url.startsWith('http')) {
            //                 backList.push({url: v.url, file: ''})
            //             }
            //         } else {
            //             const name1 = v.raw.name.split('.')[0]
            //             let typeSuffix = v.name.split('.')[1]
            //             const Key = `${name1}${this.guid()}.${typeSuffix}`
            //             const url = endpoint + '/yzq-upload/' + Key
            //             // let typeSuffix = v.raw.type.split('/')[1]
            //             // const Key = `${name1}${this.guid()}`
            //             // const url = endpoint + '/yzq-upload/' + Key + '.' + name1
            //             backList.push({url: url, file: v.raw, fileName: Key})
            //         }
            //     })
            //     this.$emit('input', backList)
            // },
            async upload(file) {
                this.fileData.append('files', file.file)
                this.initList.push(file)
            },
            // guid() {
            //     function s4() {
            //         return Math.floor((1 + Math.random()) * 0x10000)
            //             .toString(16)
            //             .substring(1)
            //     }
            //
            //     return s4() + s4() + '-' + s4() + '-' + s4() + '-' +
            //         s4() + '-' + s4() + s4() + s4()
            // },
            /**
             *删除文件之后处理
             */
            removeFileHandler({unique}) {
                const index = this.fileList.findIndex(v => v.unique === unique)
                const _index = this.remoteFileList.findIndex(v => v.unique === unique)
                this.fileList.splice(index, 1)
                this.hideUpload = this.fileList.length >= this.limit
                if (_index > -1) {
                    this.remoteFileList.splice(_index, 1)
                }
                let infoList = this.remoteFileList.map(item => {
                    return item.url
                })
                this.$emit('input', infoList.join(','))
            },
            exceedHandler() {
                this.$message({showClose: true, message: `选择文件数量不能超过${this.limit}个`, type: 'warning'})
            }
        }
    }
</script>

<style lang="scss" scoped>
    .upload-demo {
        display: flex;

        ::v-deep.el-upload-list {
            width: 300px !important;
        }

        .el-upload-list__item-name {
            overflow: hidden !important;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 200px !important;
        }
    }

    .fileName {
        color: #606266;
        cursor: pointer;
        margin: 0 20px;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 5px;
    }

    .i-show {
        display: none;
        margin-left: 5px;
    }

    .fileName:hover {
        background: #eee;
        padding: 0 5px;
        border-radius: 5px;
    }

    .fileName:hover .i-show {
        display: inline;
    }

</style>

