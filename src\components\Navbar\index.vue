<template>
  <div class="container" :class="{ sticky,border }">
    <div class="header-content ">
      <slot>
        <div class="header" @click="()=>$router.back()">
          <i class="el-icon-arrow-left" />
          <span :style="{'text-align': align }"> {{title}}</span>
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'navbar',
  data() {
    return {}
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    sticky: {
      type: Boolean,
      default: false
    },
    border: {
      type: Boolean,
      default: false
    },
    align: {
      type: String,
      default: 'left'
    }
  }
}
</script>

<style scoped lang='scss'>
.sticky {
  position: sticky;
  top: 0;
}
.border {
  border-bottom: 1px solid #eee;
}

.header-content {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 55px;
  background: #fff;
  padding: 0 10px 0;

  .header {
    width: 100%;
    display: flex;
    align-items: center;
    background: #fff;
    cursor: pointer;
    // font-size: 18px;
    i {
      font-size: 22px;
      cursor: pointer;
    }

    span {
      line-height: 18px;
      flex: 1;
      font-size: 18px;
    }
  }
}
</style>
