<template>
    <el-row class="container">
        <el-input :value="start" type="number" @input="change($event,0)"></el-input>
        -
        <el-input :value="end" type="number" @input="change($event,1)"></el-input>
    </el-row>
</template>

<script>
    export default {
        name: 'index',
        data() {
            return {
                result: []
            }
        },
        props: {
            start: {
                required: true
            },
            end: {
                required: true
            }
        },
        methods: {
            change(val, index) {
                this.result.splice(index, 1, val)
                this.$emit('update:start', this.result[0])
                this.$emit('update:end', this.result[1])
            }
        }
    }
</script>

<style scoped lang="scss">
    .container {
        display: inline-block;

        ::v-deep .el-input {
            width: 60px !important;
        }

        ::v-deep .el-input__inner {
            width: 60px;
        }
    }
</style>
