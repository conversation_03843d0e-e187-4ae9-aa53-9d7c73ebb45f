<template>
  <section class="panel" v-if="type==='filter'">
    <div class="panel-contract">
      <el-cascader ref="contract" v-model="currentContract" v-bind="contractConfig" @change="handleContractChange" />
    </div>
    <div class="panel-indicators">
      <div class="panel-title">{{title}}</div>
      <div class="panel-desc">{{desc}}</div>
    </div>
  </section>
  <section class="panelV" v-else-if="type==='location'">
    <div class="panel-title">{{title}}</div>
    <div class="panel-list">
      <div :class="[active===index?'panel-list-active':'', 'panel-list-item']" v-for="(item,index) in list" :key="index"
           v-text="item.label" @click="handleToLocation(index)" />
    </div>
  </section>
  <section class="panel" v-else>
    <slot />
  </section>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: 'filter',
    },
    title: {
      type: String,
      default: '定位查看',
    },
    currentContract: {
      type: Array,
      default() {
        return []
      },
    },
    contractConfig: {
      type: Object,
      default() {
        return {}
      },
    },
    desc: {
      type: String,
      default: 'Mt≤10% ，Adt≤10.5% ，St,d≤1.3% ，St,d≤1.3% ，Vdaf：24%-27% ，G≥80',
    },
    list: {
      type: Array,
      default() {
        return []
      },
    },
    active: {
      type: [String, Number],
      default: '',
    },
  },
  methods: {
    handleToLocation(index) {
      this.$emit('locationChange', index)
    },
    handleContractChange(value) {
      this.$emit('handleToLocation', value)
    },
  },
}
</script>

<style lang="scss" scoped>
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
    }
  }
  &-title {
    width: 60px;
    line-height: 31px;
    margin-right: 10px;
    font-size: 14px;
    color: #767676;
  }
  &-list {
    flex: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;

    &-item {
      cursor: pointer;
      display: flex;
      text-align: center;
      justify-content: center;
      margin-right: 15px;
      align-items: center;
      border: 1px solid #f1f1f2;
      // flex: 0 0 115px;
      display: flex;
      height: 31px;
      padding: 0 10px;

      // margin-bottom: 10px;

      border-radius: 4px;
      font-weight: 400;
      font-size: 9px;
      // opacity: 0.9;
      font-size: 14px;
      color: #868686;
    }
    &-item:hover {
      transition: opacity 0.5s;
      opacity: 1;
    }
    &-active {
      opacity: 1;
      color: #fff;
      background: #ff9639;
    }
  }
}

.panelV {
  position: relative;
  display: flex;
  padding: 10px 0;
  // align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
    }
  }
  &-title {
    width: 60px;
    margin-right: 10px;
    font-size: 14px;
    color: #767676;
  }
  &-list {
    flex: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;

    &-item {
      cursor: pointer;
      display: flex;
      text-align: center;
      justify-content: center;
      margin-right: 15px;
      align-items: center;
      border: 1px solid #f1f1f2;
      flex: 0 0 115px;
      height: 31px;
      margin-bottom: 10px;

      border-radius: 4px;
      font-weight: 400;
      font-size: 9px;
      // opacity: 0.9;
      font-size: 14px;
      color: #2d2d2d;
    }
    &-item:hover {
      transition: opacity 0.5s;
      opacity: 1;
    }
    &-active {
      opacity: 1;
      color: #fff;
      background: #ff9639;
    }
  }
}
</style>