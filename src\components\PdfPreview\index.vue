<template>
    <div ref="pdfDialog"
         class="pdfPrew"
    >
        <div class="printSetting">
            <el-form ref="settingForm" label-position="top" :rules="itemFormRules" label-width="80px"
                     :model="settingForm">
                <el-form-item label="打印方向" required>
                    <el-select v-model="settingForm.pageOrientation">
                        <el-option value="portrait" label="竖向"></el-option>
                        <el-option value="landscape" label="横向"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="纸型" required>
                    <el-select v-model="settingForm.pageSize">
                        <el-option value="A1" label="A1"></el-option>
                        <el-option value="A2" label="A2"></el-option>
                        <el-option value="A3" label="A3"></el-option>
                        <el-option value="A4" label="A4"></el-option>
                        <el-option value="A5" label="A5"></el-option>
                        <el-option value="custom" label="自定义"></el-option>
                    </el-select>
                </el-form-item>
                <template v-if="settingForm.pageSize==='custom'">
                    <el-form-item label="纸张宽度(mm)" prop="width">
                        <el-input clearable
                                  type="number" v-model.number="settingForm.width" placeholder="纸张宽度"></el-input>
                    </el-form-item>
                    <el-form-item label="纸张高度(mm)" prop="height">
                        <el-input clearable
                                  type="number" v-model.number="settingForm.height" placeholder="纸张高度"></el-input>
                    </el-form-item>
                </template>
                <el-form-item label="上边距(mm)" prop="top" :rules="[{required:true, message:'上边距不能为空'}]">
                    <el-input clearable
                              type="number" v-model.number="settingForm.top" placeholder="上边距"></el-input>
                </el-form-item>
                <el-form-item label="下边距(mm)" prop="bottom" :rules="[{required:true, message:'下边距不能为空'}]">
                    <el-input clearable
                              type="number" v-model.number="settingForm.bottom" placeholder="下边距"></el-input>
                </el-form-item>
                <el-form-item label="左边距(mm)" prop="left" :rules="[{required:true, message:'左边距不能为空'}]">
                    <el-input clearable
                              type="number" v-model.number="settingForm.left" placeholder="左边距"></el-input>
                </el-form-item>
                <el-form-item label="右边距(mm)" prop="right" :rules="[{required:true, message:'右边距不能为空'}]">
                    <el-input clearable
                              type="number" v-model.number="settingForm.right" placeholder="右边距"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit">确定</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="printView" v-loading.lock="loading">
            <embed width="100%" height="100%" :src="pdfBlob" type="application/pdf"/>
        </div>
    </div>
</template>

<script>
    import {mapGetters} from 'vuex'
    import trans from '@/api/font'
    import Func from '@/utils/func'

    let pdfMake = require('pdfmake/build/pdfmake')

    pdfMake.fonts = {
        'webfont': {
            normal: 'webfont',
            bold: 'webfont',
            italics: 'webfont',
            bolditalics: 'webfont'
        }
    }

    export default {
        data() {
            return {
                imgUrl: '',
                allList: [],
                reviewRegion: [],
                settingForm: {
                    pageOrientation: 'portrait',
                    pageSize: 'A4',
                    width: 210,
                    height: 297,
                    trueWidth: 0, // 实际打印纸的宽度
                    trueHeight: 0, // 实际打印纸的高度
                    top: 10,
                    bottom: 10,
                    left: 10,
                    right: 10
                },
                pdfBlob: '',
                loading: false,
                visible: false,
                itemFormRules: {
                    width: [
                        {
                            validator: (rule, value, callback) => {
                                if (!value) {
                                    return callback(new Error('纸张宽度不能为空'))
                                }
                                if (!Number.isInteger(value)) {
                                    callback(new Error('请输入数字值'))
                                } else {
                                    // console.log(this.itemForm.deliveryAmount)
                                    if (value < 80) {
                                        callback(new Error('最小80'))
                                    } else {
                                        callback()
                                    }
                                }
                            },
                            trigger: 'blur'
                        }
                    ],
                    height: [
                        {
                            validator: (rule, value, callback) => {
                                if (!value) {
                                    return callback(new Error('纸张高度不能为空'))
                                }
                                if (!Number.isInteger(value)) {
                                    callback(new Error('请输入数字值'))
                                } else {
                                    // console.log(this.itemForm.deliveryAmount)
                                    if (value < 80) {
                                        callback(new Error('最小80'))
                                    } else {
                                        callback()
                                    }
                                }
                            },
                            trigger: 'blur'
                        }
                    ]
                }
            }
        },
        props: {
            qrCode: {
                type: String,
                default: ''
            },
            type: {
                type: String,
                default: 'stock'
            },
            form: {
                type: Object,
                default: undefined
            },
            list: {
                required: true,
                default: []
            },
            params: {
                type: Object,
                default: null
            }
        },
        computed: {
            name() {
                let name
                switch (this.type) {
                    case 'stock':
                        name = '发货单'
                        break
                    case 'entry':
                        name = '出库单'
                        break
                    case 'task':
                        name = '盘点表'
                        break
                    case 'enter':
                        name = '入库单'
                        break
                    case 'in':
                        name = '预约入库单'
                        break
                }
                return name
            },
            ...mapGetters([
                'company'
            ])
        },
        created() {
            const defaultSetting = {
                pageOrientation: 'portrait',
                pageSize: 'A4',
                width: 210,
                height: 297,
                trueWidth: 0, // 实际打印纸的宽度
                trueHeight: 0, // 实际打印纸的高度
                top: 20,
                bottom: 10,
                left: 10,
                right: 10
            }
            const setting = localStorage.getItem(this.type + '_print_setting')
            if (setting) {
                this.settingForm = JSON.parse(setting)
            } else {
                localStorage.setItem(this.type + '_print_setting', JSON.stringify(defaultSetting))
                this.settingForm = defaultSetting
            }
        },
        watch: {},
        mounted() {
            this.$nextTick(() => {
                this.preview()
            })
        },
        methods: {
            async preview() {
                this.visible = true
                this.$emit('printRecord', null)
                if (this.params) {
                    this.loading = true
                    const params = Object.assign({}, this.params, {
                        pageSize: 10000,
                        orderDir: 'asc'
                    })
                    let res
                    switch (this.type) {
                        case 'stock':
                            params.orderBy = 'storageLocationCode'
                            // res = await Func.fetch(stockList, params)
                            break
                        case 'entry':
                            params.orderBy = 'itemCode'
                            // res = await Func.fetch(pageOrderItems, params)
                            break
                        case 'task':
                            params.orderBy = 'storageLocationCode'
                            params.taskId = this.params.id
                            // res = await Func.fetch(checkDetailList, params)
                            break
                        case 'in':
                            params.filter_EQL_inOrderId = this.params.filter_EQL_inOrderId
                            params.orderBy = 'itemCode'
                            // res = await Func.fetch(inOrderItemsList, params)
                            break
                    }
                    this.loading = false
                    if (res) {
                        if (this.type === 'in') {
                            this.allList = res.data.map(val => {
                                return Object.assign({
                                    type_text: val.type === 'ZC' ? '正常' : '残次'
                                }, val)
                            })
                        } else {
                            this.allList = res.data.records.map(val => {
                                return Object.assign({
                                    type_text: val.type === 'ZC' ? '正常' : '残次'
                                }, val)
                            })
                        }
                    }
                } else {
                    this.allList = this.list
                }
                this.getTrans(this.buildPdf())
            },
            async getTrans(pdfDate) {
                this.loading = true
                const res = await Func.fetch(trans, Func.uniqueString(JSON.stringify(pdfDate)) + ' ')
                this.loading = false
                if (res) {
                    pdfMake.vfs = res.data
                    this.createPdf(pdfDate)
                }
            },
            buildPdf() {
                // let formData
                let listData
                let newList = []
                // detailData = [
                //     [
                //         {
                //             text: `委托单号：${this.form.code}`,
                //             style: 'tableHeader1'
                //         },
                //         {
                //             text: `委托单位：${this.form.applayUserName}`,
                //             style: 'tableHeader1'
                //         },
                //         {
                //             text: `报告获取：${this.form.notifyWay}`,
                //             style: 'tableHeader1'
                //         },
                //         {
                //             text: `余样处理：${this.form.leftSampleDeal}`,
                //             style: 'tableHeader1'
                //         },
                //         {
                //             text: `检测依据：${this.form.checkBase}`,
                //             style: 'tableHeader1'
                //         }
                //     ]
                // ]
                // console.log(this.form)
                listData = this.form.assayItemNames.join(',')
                // listData.map(item => {
                //     let obj = {
                //         text: item,
                //         style: 'tableHeader'
                //     }
                //     newList.push(obj)
                // })
                newList = {
                    text: `委托化验项：${listData}`,
                    style: 'tableHeader'
                }
                // console.log('+========================')
                // console.log(newList)
                // console.log('+========================')
                // formData = {
                //     style: 'tableExample',
                //     table: {
                //         widths: [100, '*'],
                //         body: [
                //             [
                //                 {
                //                     text: '类型',
                //                     style: 'tableHeader'
                //                 },
                //                 {
                //                     text: '化验项目',
                //                     style: 'tableHeader'
                //                 }
                //             ]
                //         ]
                //     }
                // }
                return {
                    pageSize: this.settingForm.pageSize === 'custom' ? {
                        width: Number(parseFloat(this.settingForm.width / 25.4 * 72).toFixed(2)),
                        height: Number(parseFloat(this.settingForm.height / 25.4 * 72).toFixed(2))
                    } : this.settingForm.pageSize,
                    pageMargins: [this.settingForm.left, this.settingForm.top, this.settingForm.right, this.settingForm.bottom],
                    pageOrientation: this.settingForm.pageOrientation,
                    background: this.qrCode ? {image: this.qrCode, fit: [70, 70], style: 'qrcode'} : '',
                    content: [
                        {text: this.form.name, style: 'header'},
                        newList
                    ],
                    styles: {
                        headerInfo: {
                            fontSize: 10,
                            color: 'black',
                            alignment: 'center',
                            margin: [20, 0, 10, 0]
                        },
                        header: {
                            fontSize: 14,
                            bold: true,
                            color: 'black',
                            alignment: 'center'
                        },
                        tableExample: {
                            margin: [0, 5, 0, 15],
                            whiteSpace: 'normal'
                        },
                        tableHeader: {
                            bold: true,
                            fontSize: 10,
                            color: 'black',
                            margin: [0, 60, 0, 15],
                            alignment: 'left'
                        },
                        tableHeader2: {
                            bold: true,
                            fontSize: 16,
                            color: 'red',
                            alignment: 'center'
                        },
                        tableHeader1: {
                            fontSize: 12,
                            color: 'black',
                            margin: [5, 5, 5, 5],
                            alignment: 'left'
                        },
                        sign: {
                            fontSize: 13,
                            alignment: 'right',
                            color: 'black',
                            margin: [0, 0, 0, 10]
                        },
                        qrcode: {
                            alignment: 'center',
                            color: 'black',
                            margin: [10, 25, 0, 10]
                        }
                    },
                    defaultStyle: {
                        alignment: 'center',
                        font: 'webfont',
                        fontSize: 11,
                        color: 'blue'
                    },
                    tableTop: {
                        border: 'none'
                    }
                }
            },
            createPdf(pdfDate) {
                const pdfDocGenerator = pdfMake.createPdf(pdfDate)
                pdfDocGenerator.getBuffer(dataUrl => {
                    const blobFIle = new Blob([dataUrl], {type: 'application/pdf'})
                    this.pdfBlob = URL.createObjectURL(blobFIle)
                    setTimeout(() => {
                        this.loading = false
                    }, 1000)
                })
            },
            async onSubmit() {
                const valid = await this.$refs['settingForm'].validate()
                if (valid) {
                    localStorage.setItem(this.type + '_print_setting', JSON.stringify(this.settingForm))
                    this.createPdf(this.buildPdf())
                    this.$message({
                        showClose: true,
                        message: '保存打印配置成功',
                        type: 'success'
                    })
                }
            }
        }
    }
</script>

<style lang="scss" scoped>
    .pdfPrew {
        display: flex;
        height: 88vh;

        .printSetting {
            display: flex;
            width: 300px;
        }

        .printView {
            display: flex;
            width: 100%;
        }
    }

    .tableTop {
        border: none;
    }
</style>

