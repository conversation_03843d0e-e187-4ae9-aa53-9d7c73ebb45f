<template>
  <div>
    <div class="header">
      <span><img src="../../assets/logo2.png" style="width: 100%;height: 42px;"></span>
      <span style="position: absolute;right: 15px;top: 15px;" @click="close">
        <img src="../../assets/close.png" style="width: 24px;height: 24px;"></span>
    </div>
    <div style="margin-top: 30px;display: flex;justify-content: flex-end">
    </div>
    <div style="display: flex;justify-content: center;align-items: center;margin: 10px;position: relative">
      <div class="watermark">
        <div :style="`color:${color};transform: rotate(${rotate}deg);width:${wm_w}%;height:${wm_h}%`" class="wm"
             v-for="(i,index) in watermarkNum" :key="index">{{watermarkText}}
        </div>
      </div>
      <div id="PdfContent" class="pdfContent" v-html="content">
      </div>
    </div>
    <div style="display: flex;justify-content: center">
      <el-button v-if="saveReport" type="danger" style="margin-right: 40px; height: 40px;width: 120px;" icon="el-icon-share"
                 @click="sendEmail">发送邮件
      </el-button>
      <el-button v-if="saveReport" type="danger" style="height: 40px;width: 120px;" icon="el-icon-download" @click="save">保存报告
      </el-button>
    </div>
  </div>
</template>

<script>
import { reviewDownoadReport } from '@/api/coal'
import Func from '@/utils/func'
// 实体模型
export default {
  name: 'PdfView',
  data() {
    return {
      watermarkNum: 0, // 水印数量
      watermarkRow: 2, // 列数
      watermarkLine: 4, // 行数
      rotate: -20, // 文字旋转角度
      color: '#ccc', // 文字颜色
      watermarkText: '掌上煤焦', // 水印文字
      wm_w: 0, // 水印宽度百分比
      wm_h: 0 // 水印高度百分比
    }
  },
  props: {
    content: {
      required: true,
      type: String
    },
    entityForm: {
      required: true,
      type: Object
    },
    saveReport: {
      required: false,
      type: Boolean
    },
    exportPdf: {
      required: false,
      type: Boolean
    }
  },
  watch: {},
  mounted() {
    this.setWatermark()
  },
  methods: {
    // 创建水印
    setWatermark() {
      let that = this
      that.watermarkNum = that.watermarkRow * that.watermarkLine
      that.wm_w = 100 / that.watermarkRow
      that.wm_h = 100 / that.watermarkLine
    },
    async reviewDownLoad() {
      const loading = this.$loading({
        lock: true,
        text: '导出报告中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const res = await Func.fetch(reviewDownoadReport, { ...this.entityForm })
      if (res) {
        const blob = new Blob([res.data], { type: 'application/octet-binary' })
        // // //从response的headers中获取filename, 后端response.setHeader("Content-disposition", "attachment; filename=xxxx.docx") 设置的文件名;
        const patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
        const contentDisposition = decodeURI(res.headers['content-disposition'])
        const result = patt.exec(contentDisposition)
        const filename = result[0].split('=')[1]
        const downloadElement = document.createElement('a')
        const href = window.URL.createObjectURL(blob)
        downloadElement.style.display = 'none'
        downloadElement.href = href
        downloadElement.download = filename
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
        loading.close()
        this.reportVisible = false
      } else {
        loading.close()
      }
    },
    async sendEmail() {
      this.$emit('email')
    },
    close() {
      this.$emit('closeDialog', false)
    },
    async save() {
      this.$emit('save')
    }
  }
}
</script>
<style scoped lang="scss">
.btn {
  color: #909399;
  background: none;
  border: none;
  cursor: pointer;
  outline: none;
}

.dialog-content {
  margin: 5px 10px;
}

.m-b-10 {
  margin: 20px 0;
}

.saveBtn {
  display: flex;
  padding-top: 10px;
  justify-content: flex-end;
}

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.pdfbtn {
  width: 150px;
  height: 50px;
  font-size: 14px;
}

.pdfbtn1 {
  width: 150px;
  height: 40px;
  font-size: 14px;
  padding: 10px;
}

.btnContent {
  position: relative;
  display: flex;
  top: 0;
  justify-content: center;
}

.header {
  padding: 20px 64px 0 64px;
  width: 100%;
  position: absolute;
  background: #ffffff;
  top: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s;
}

.pdfContent {
  margin-top: 20px;
  box-shadow: 2px 2px 10px #fff, 4px 4px 15px #ccc, 5px 5px 20px #ccc;
  background: #ffffff;
}

.watermark {
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  pointer-events: none;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.wm {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
