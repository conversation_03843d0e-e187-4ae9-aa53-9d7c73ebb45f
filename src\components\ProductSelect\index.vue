<template>
  <el-select class="productSelect" v-model="val" filterable :placeholder="placeholder" @change="handleChange"
             :clearable="clearable">
    <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item.name" :disabled="disabled" />
  </el-select>
</template>
<script>
import { productList } from '@/api/public'
export default {
  name: 'productSelect',
  data() {
    return {
      val: '',
      list: []
    }
  },
  props: {
    value: {
      required: true
    },
    disabled: {
      type: [Boolean],
      default: false
    },
    filterType: {
      type: String
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择品名'
    }

  },
  async created() {
    this.list = await productList()
  },
  methods: {
    handleChange(name) {
      let searchName = this.list.find(item => item.name === name)
      this.val = name
      this.$emit('update:value', name || '')
      this.$emit('change', searchName || { name: '', id: '', code: '', coalCategory: '' })
    }
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss" scoped>
.productSelect {
  width: 100%;
}
.optionContent1 {
  color: #2d8cf0;
  font-size: 12px;
  text-align: end;
  line-height: 20px;
  border-bottom: 1px solid #eee;
}
</style>
