<template>
  <div class="SingleWash">
    <el-dialog :before-close="handleClose" :title="details.name || '' " :visible.sync="visible" class="add" top="3vh"
               width="80%">
      <indicators-card title="配煤指标">
        <section class="table">
          <div class="table_title">指标</div>
          <div class="table_row">
            <span>洗煤</span>
            <span>{{ qualityData.cInVdafL }}≤Vdaf≤{{ qualityData.cInVdafH }}</span>
            <span>Ad≤{{ qualityData.cJtAdH }}</span>
            <span>>St,d≤{{ qualityData.cJtStdH }}</span>
            <span>G≥{{ qualityData.cInGL }}</span>
            <span>Y≥{{ qualityData.cInYL }}</span>
            <span>标准差S≤{{ qualityData.cInSH }}</span>
            <span>步长(%):{{ qualityData.step }}</span>
          </div>
        </section>
      </indicators-card>
      <indicators-coal-proportion :dataConfig="coalProportionConfig"/>
      <indicators-card title="洗煤计算结果">
        <indicators-table :dataConfig="coalWashingResultConfig">
          <el-table-column width="80">
            <template slot-scope="scope">
              {{
                scope.$index === 0 ? '理论' : scope.$index === 1 ? '实际' : scope.$index === 2 ? '误差' : '允许误差'
              }}
            </template>
          </el-table-column>
          <el-table-column label="Ad(%)" prop="inAd">
            <template slot-scope="scope">
              <el-input v-if='scope.$index===0' v-model="qualityData.inAd" disabled></el-input>
              <el-input v-if='scope.$index===1' v-model="qualityData.factInAd" min="0" type="number"
                        @input="handleChangeInput('inAd','factInAd','diffInAd')"></el-input>
              <el-input v-if='scope.$index===2' v-model="qualityData.diffInAd" disabled></el-input>
            </template>
          </el-table-column>
          <el-table-column label="Vdaf(%)" prop="inVdaf">
            <template slot-scope="scope">
              <el-input v-if='scope.$index===0' v-model="qualityData.inVdaf" disabled></el-input>
              <el-input v-if='scope.$index===1' v-model="qualityData.factInVdaf"
                        min="0" type="number" @input="handleChangeInput('inVdaf','factInVdaf','diffInVdaf')"></el-input>
              <el-input v-if='scope.$index===2' v-model="qualityData.diffInVdaf" disabled></el-input>
            </template>
          </el-table-column>
          <el-table-column label="St,d(%)" prop="inStd">
            <template slot-scope="scope">
              <el-input v-if='scope.$index===0' v-model="qualityData.inStd" disabled></el-input>
              <el-input v-if='scope.$index===1' v-model="qualityData.factInStd"
                        min="0" type="number" @input="handleChangeInput('inStd','factInStd','diffInStd')"></el-input>
              <el-input v-if='scope.$index===2' v-model="qualityData.diffInStd" disabled></el-input>
            </template>
          </el-table-column>

          <el-table-column label="G" prop="inG">
            <template slot-scope="scope">
              <el-input v-if='scope.$index===0' v-model="qualityData.inG" disabled></el-input>
              <el-input v-if='scope.$index===1' v-model="qualityData.factInG"
                        min="0" type="number" @input="handleChangeInput('inG','factInG','diffInG')"></el-input>
              <el-input v-if='scope.$index===2' v-model="qualityData.diffInG" disabled></el-input>
            </template>
          </el-table-column>
          <el-table-column label="Y(mm)" prop="inY">
            <template slot-scope="scope">
              <el-input v-if='scope.$index===0' v-model="qualityData.inY" disabled></el-input>
              <el-input v-if='scope.$index===1' v-model="qualityData.factInY"
                        min="0" type="number" @input="handleChangeInput('inY','factInY','diffInY')"></el-input>
              <el-input v-if='scope.$index===2' v-model="qualityData.diffInY" disabled></el-input>
            </template>
          </el-table-column>
          <el-table-column label="X(mm)" prop="inX">
            <template slot-scope="scope">
              <el-input v-if='scope.$index===0' v-model="qualityData.inX" disabled></el-input>
              <el-input v-if='scope.$index===1' v-model="qualityData.factInX" min="0" type="number"
                        @input="handleChangeInput('inX','factInX','diffInX')"></el-input>
              <el-input v-if='scope.$index===2' v-model="qualityData.diffInX" disabled></el-input>
            </template>
          </el-table-column>
          <el-table-column label="R0(%)" prop="theoryMacR0">
            <template slot-scope="scope">
              <el-input v-if='scope.$index===0' v-model="qualityData.theoryMacR0" disabled></el-input>
              <el-input v-if='scope.$index===1' v-model="qualityData.actualMacR0" min="0" type="number"
                        @input="handleChangeInput('theoryMacR0','actualMacR0','difMacR0')"></el-input>
              <el-input v-if='scope.$index===2' v-model="qualityData.difMacR0" disabled></el-input>
            </template>
          </el-table-column>
          <el-table-column label="S0" prop="theoryMacS">
            <template slot-scope="scope">
              <el-input v-if='scope.$index===0' v-model="qualityData.theoryMacS" disabled></el-input>
              <el-input v-if='scope.$index===1' v-model="qualityData.actualMacS"
                        min="0" type="number"
                        @input="handleChangeInput('theoryMacS','actualMacS','difMacS')"></el-input>
              <el-input v-if='scope.$index===2' v-model="qualityData.difMacS" disabled></el-input>
            </template>
          </el-table-column>
          <el-table-column label="洗精煤成本" prop="inPrice">
            <template slot-scope="scope">
              <el-input v-if='scope.$index===0' v-model="qualityData.inPrice" disabled></el-input>
              <el-input v-if='scope.$index===1' v-model="qualityData.factInPrice" min="0" type="number"
                        @input="handleChangeInput('inPrice','factInPrice','diffInPrice')"></el-input>
              <el-input v-if='scope.$index===2' v-model="qualityData.diffInPrice" disabled></el-input>
            </template>
          </el-table-column>
        </indicators-table>
      </indicators-card>
      <indicators-card title="图形显示">
        <column-chart v-if="isShowColumnChart" :impChartData="impChartData"/>
        <el-table :data="qualityData.coalTypeProportionList" :show-header="false" border class="chart-table">
          <el-table-column prop="brownCoal">
          </el-table-column>
          <el-table-column prop="longFlame">
          </el-table-column>
          <el-table-column prop="gasCoal">
          </el-table-column>
          <el-table-column prop="thirdCokingCoal">
          </el-table-column>
          <el-table-column prop="fatCoal">
          </el-table-column>
          <el-table-column prop="cokingCoal">
          </el-table-column>
          <el-table-column prop="leanCoal">
          </el-table-column>
          <el-table-column prop="meagerLeanCoal">
          </el-table-column>
          <el-table-column prop="meagerCoal">
          </el-table-column>
          <el-table-column prop="smokelessCoal">
          </el-table-column>
        </el-table>
      </indicators-card>
      <indicators-card title="备注">
        <el-col :span="24">
          <el-input v-model="qualityData.remarks" :rows="6" placeholder="请填写备注信息~" type="textarea">
          </el-input>
        </el-col>
      </indicators-card>

      <div slot="footer" class="dialog-footer">
        <el-button class="item" style="background:#f8a20f;color:#fff;border:none;" type="save"
                   @click="redoOptimization">
          重新优化
        </el-button>
        <el-button :loading="saveOptimizationLoading" class="item" type="save" @click="saveOptimization">保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import columnChart from '@/components/Chart/columnChart'
import {remakeNewCoal} from '@/api/coalWashing'
import {IndicatorsCard, IndicatorsTable, IndicatorsCoalProportion} from '@/components/Indicators/index'
import {getDetail, sendEmail, saveProjectApi} from '@/api/mulWash'
import Func from '@/utils/func'
import {mapGetters} from 'vuex'

export default {
  name: 'manyeWash',
  data() {
    return {
      qualityData: {},
      saveOptimizationLoading: false,
      coalProportionConfig: {
        // 煤种及配比
        list: [],
        columns: [
          {isShow: true, label: '名称', prop: 'name'},
          {isShow: true, label: '煤种', prop: 'type'},
          {isShow: true, label: '配比(%)', prop: 'percent', slot: 'percent'},
          {isShow: true, label: 'Ad(%)', prop: 'cleanAd'},
          {isShow: true, label: 'Vdaf(%)', prop: 'cleanVdaf'},
          {isShow: true, label: 'St,d(%)', prop: 'cleanStd'},
          {isShow: true, label: 'G', prop: 'procG'},
          {isShow: true, label: 'Y(mm)', prop: 'procY'},
          {isShow: true, label: 'X(mm)', prop: 'procX'},
          {isShow: true, label: 'R0(%)', prop: 'macR0'},
          {isShow: true, label: '到厂价（元/吨', slot: 'arrivePrice', prop: 'arrivePrice'}
        ]
      },
      coalWashingResultConfig: {
        list: [],
        columns: []
      },
      isShowColumnChart: false,
      impChartData: {name: '', xData: [], yData: []},
      visible: false
    }
  },
  components: {
    IndicatorsCard,
    IndicatorsTable,
    IndicatorsCoalProportion,
    columnChart
  },
  computed: {
    ...mapGetters(['showRoutes'])
  },
  props: {
    details: {
      type: Object,
      default() {
        return {}
      }
    },
    washVisible: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    washVisible(v) {
      this.visible = v
      // console.log(v, '22222');
      if (v) {
        this.getInfo()
      }
    },
    details: {
      handler(value) {
      },
      deep: true,
      immediate: true
    }
  },
  created() {
  },
  methods: {
    async saveOptimization() {
      this.saveOptimizationLoading = true
      const res = await Func.fetch(saveProjectApi, {...this.qualityData})
      this.saveOptimizationLoading = false
      if (res) {
        this.getInfo()
        this.$message({type: 'success', message: '保存成功'})
        this.handleClose()
      }
    },

    handleClose() {
      this.$emit('washDetailClose')
    },
    handleSend() {
      this.$confirm('此操作将发送邮件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await sendEmail({id: this.details.id})
          if (res) {
            this.$message({
              type: 'success',
              message: '邮件信息已发送'
            })
            this.getList()
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消发送'
          })
        })
    },
    coalRockResultList() {
      const coalRockResultList = [{}, {}, {}]
      for (let i in this.qualityData) {
        for (let k in coalRockResultList) {
          coalRockResultList[k][i] = this.qualityData[i]
        }
      }
      return coalRockResultList
    },
    handleChangeInput(theory, fact, diff) {
      this.qualityData[diff] = Math.abs(this.qualityData[theory] - this.qualityData[fact]).toFixed(2)
    },
    async getInfo() {
      try {
        await this.$nextTick()
        const {data} = await Func.fetch(getDetail, {id: this.details.id})
        if (data) {
          this.qualityData = {...data}
          this.coalProportionConfig.list = data.coalWashingProjectCoalList || []
          this.coalWashingResultConfig.list = this.coalRockResultList()
          this.impChartData.xName = '区间'
          this.impChartData.yName = '占比'
          this.impChartData.xData = data.rangeList
          this.impChartData.yData = data.proportionList
          this.impChartData.type = 'bar'
          this.impChartData.color = '#ff726b'
          this.isShowColumnChart = true
        }
      } catch (error) {
      }
    },

    handleToNamePage(option) {
      const {parent, child, Pagename, params} = option

      console.log(option)

      const route = this.showRoutes.find((route) => route.name === parent)
      this.$store.dispatch('changeRoute', route)
      this.$store.dispatch('changeChildRoute', {parent: child, child: ''})
      this.$router.replace({name: Pagename, params})
    },
    // 重新优化
    async redoOptimization() {
      let params = {
        id: this.details.id,
        activeTitle: 'MWASH'
      }
      const res = await remakeNewCoal({coalWashingProjectId: this.details.id})
      if (res) {
        this.$emit('washDetailClose', params)
      }
      // this.handleToNamePage({
      //   parent: `coalblending`,
      //   child: `autoBlending`,
      //   Pagename: `autoBlending`,
      //   params: {
      //     id: this.details.id,
      //     activeTitle: 'MWASH'
      //   }
      // })
    }
  }
}
</script>

<style lang='scss' scoped>
.add {
  ::v-deep .el-dialog__body {
    padding: 0 35px;
    height: 80vh;
    overflow: auto;
  }
}

.chart-table {
  width: 80%;
  margin: 0 auto;
  margin-bottom: 10px;
  // display: flex;
  // justify-content: center;
}

//.btn_group {
//  border-radius: 5px;
//  display: flex;
//  background: #fff;
//  // color: #e9e9e9;
//  align-items: center;
//  box-sizing: border-box;
//  padding: 10px;
//  min-height: 65px;
//  margin: 10px 0 10px;
//
//  .item {
//    border-radius: 5px;
//    height: 35px;
//  }
//}
</style>
