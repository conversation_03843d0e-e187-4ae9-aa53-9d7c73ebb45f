<template>
  <el-cascader clearable :disabled="disabled" :options="option" :value="value" @input="change($event)" :props="prop">
  </el-cascader>
</template>

<script>
import { dataTree } from './regionData'
import { getRegionText } from '@/filters'
// 数据项
const option = dataTree.map(item => {
  let { name, id, children } = item
  let tweFieldCont = children
  let tweField = []
  for (let i = 0; i < tweFieldCont.length; i++) {
    let { name, id, children } = tweFieldCont[i]
    let threeFieldCont = children
    let threeField = []
    for (let j = 0; j < threeFieldCont.length; j++) {
      let { name, id } = threeFieldCont[j]
      threeField.push({ label: name, value: id })
    }
    children = [...threeField]
    tweField.push({ label: name, value: id, children })
  }
  return {
    label: name,
    value: id,
    children: tweField
  }
})
export default {
  name: 'RegionSelect',
  data() {
    return {
      option
    }
  },
  props: {
    value: {
      require: true,
      default: []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    province: {
      default: ''
    },
    city: {
      default: ''
    },
    area: {
      default: ''
    },
    text: {
      default: ''
    },
    prop: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    change(val) {
      const location = getRegionText(val)
      this.$emit('input', { location, val })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
