<template>
    <el-cascader
            ref="location"
            class="region-select"
            :disabled="disabled"
            :options="regionTree"
            :value="value"
            :props="propsInfo"
            filterable
            clearable
            @change="change($event)"
            placeholder="省市区"
    >
    </el-cascader>
</template>

<script>
    import Region from './regionData'

    const {regionTree} = Region
    export default {
        name: 'RegionSelect',
        data() {
            return {
                regionTree,
                propsInfo: {
                    label: 'name',
                    value: 'id'
                }
            }
        },
        props: {
            value: {
                require: true,
                default: function () {
                    return []
                }
            },
            props: Object,
            disabled: {
                type: Boolean,
                default: false
            },
            province: {
                default: ''
            },
            city: {
                default: ''
            },
            area: {
                default: ''
            },
            text: {
                default: ''
            }
        },
        mounted() {

        },
        watch: {
            value(val) {
            }
        },
        methods: {
            change(val) {
                // console.log(val)
                const location = this.$refs.location.getCheckedNodes()[0].pathLabels.join(' ')
                this.$emit('input', val, location)
                this.$emit('update:province', val[0])
                this.$emit('update:city', val[1])
                this.$emit('update:area', val[2])
            }
        }
    }
</script>
<style lang="scss" scoped>
    .region-select {
        width: 100%;

        ::v-deep .el-cascader__label {
            padding-left: 10px;
        }

        ::v-deep .el-input__inner {
            width: 100%;
        }
    }

    /*v::deep .region-select .el-input__inner {*/
    /*width: 100%;*/
    /*}*/
</style>
