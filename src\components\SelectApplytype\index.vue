<template>
  <el-autocomplete class="select_input" v-model="valuew" :fetch-suggestions="querySearch" :placeholder="placeholder"
                   :clearable="clearable" value-key="name" @input="handleInput">
    <i :class="[searchType?'el-icon-zoom-out':'el-icon-zoom-in', 'el-input__icon']" slot="prefix" @click="handleIconClick" />
    <slot />
  </el-autocomplete>
</template>
<script>
import { productList } from '@/api/public'
export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '',
      valuew: '',
      list: [],
      searchType: true, // true===LIKES false===EQS
    }
  },
  inject: ['filterOption'],
  props: {
    value: {
      required: true,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: '请选择结算类型',
    },
    changeFilter: {
      type: Boolean,
      default: true,
    },
  },
  async created() {
    // this.list = await productList()
    this.list = [
      { id: 'TRADE', name: '贸易往来结算' },
      { id: 'CARRIAGE', name: '运费结算' },
      { id: 'GOOD', name: '物资结算' },
      { id: 'PERSONAL', name: '个人结算' },
    ]
    if (!this.changeFilter) {
      this.searchType = false
    }
  },
  methods: {
    querySearch(queryString, cb) {
      let list = this.list
      let results = queryString ? list.filter(this.createFilter(queryString)) : list
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => restaurant.name.toString().includes(queryString.toString())
    },
    handleInput(val) {
      // console.log(val)
      let value = ''
      if (val == '贸易往来结算') {
        val = 'TRADE'
        value = '贸易往来结算'
      } else if (val == '运费结算') {
        val = 'CARRIAGE'
        value = '运费结算'
      } else if (val == '物资结算') {
        val = 'GOOD'
        value = '物资结算'
      } else if (val == '个人结算') {
        val = 'PERSONAL'
        value = '个人结算'
      }
      this.valuew = value
      this.$emit('update:value', val)
    },

    handleIconClick() {
      if (!this.changeFilter) return this.$notify({ title: '提示', message: '当前搜索只支持模糊匹配', type: 'info' })
      this.searchType = !this.searchType
      let message = '切换成'
      message += this.searchType ? '模糊匹配' : '精确匹配'
      this.$notify({ title: '提示', message, type: 'success' })
      // this.$parent.$parent.$parent.$emit('iconNameTrigger', this.searchType)
      const map = {
        true: 'filter_LIKES_applyType',
        false: 'filter_EQS_applyType',
      }
      // 同步filterOption
      this.filterOption.columns.forEach((col) => {
        if (col.prop === 'applyType') col.filter = map[this.searchType + '']
      })
      // console.log(this.filterOption)
    },
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true,
    },
  },
}
</script>
<style lang="scss" scoped>
.select_input {
  width: 100%;
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }
}
::v-deep .el-input__inner {
  padding-left: 24px !important;
}
::v-deep .el-input__prefix {
  cursor: pointer;
  width: 16px !important;
  line-height: 30px !important;
  height: 16px !important;
  font-size: 16px;
  left: 2px !important;
  right: 0 !important;
}
</style>
