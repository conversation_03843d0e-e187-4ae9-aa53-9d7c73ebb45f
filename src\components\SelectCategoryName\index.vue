<template>
  <el-autocomplete class="select_input" v-model="slectname" :fetch-suggestions="querySearch" :placeholder="placeholder"
                   :clearable="clearable" value-key="name" @input="handleInput">
    <i :class="[searchType?'el-icon-zoom-out':'el-icon-zoom-in', 'el-input__icon']" slot="prefix" @click="handleIconClick" />
    <slot />
  </el-autocomplete>
</template>
<script>
import { categoryNameList } from '@/api/public'
export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '',
      slectname: '',
      list: [],
      searchType: true, // true===LIKES false===EQS
    }
  },
  inject: ['filterOption'],
  props: {
    value: {
      required: true,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: '请选择物资分类',
    },
    changeFilter: {
      type: Boolean,
      default: true,
    },
  },
  async created() {
    this.list = await categoryNameList()
    if (!this.changeFilter) {
      this.searchType = false
    }
  },
  methods: {
    querySearch(queryString, cb) {
      let list = this.list
      let results = queryString ? list.filter(this.createFilter(queryString)) : list
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => restaurant.name.toString().includes(queryString.toString())
    },
    handleInput(val) {
      this.slectname = val
      this.$emit('update:value', val)
    },

    handleIconClick() {
      if (!this.changeFilter) return this.$notify({ title: '提示', message: '当前搜索只支持模糊匹配', type: 'info' })
      this.searchType = !this.searchType
      let message = '切换成'
      message += this.searchType ? '模糊匹配' : '精确匹配'
      this.$notify({ title: '提示', message, type: 'success' })
      // this.$parent.$parent.$parent.$emit('iconNameTrigger', this.searchType)
      const map = {
        true: 'filter_LIKES_name',
        false: 'filter_EQS_name',
      }
      // 同步filterOption
      this.filterOption.columns.forEach((col) => {
        if (col.prop === 'name') col.filter = map[this.searchType + '']
      })
      // console.log(this.filterOption)
    },
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true,
    },
  },
}
</script>
<style lang="scss" scoped>
.select_input {
  width: 100%;
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }
}
::v-deep .el-input__inner {
  padding-left: 24px !important;
}
::v-deep .el-input__prefix {
  cursor: pointer;
  width: 16px !important;
  line-height: 30px !important;
  height: 16px !important;
  font-size: 16px;
  left: 2px !important;
  right: 0 !important;
}
</style>
