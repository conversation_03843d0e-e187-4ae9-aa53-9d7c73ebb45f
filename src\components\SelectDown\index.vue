<template>
  <el-select class="select" v-model="active" v-bind="config" @change="change" @clear="clear" :disabled="disabled"
             filterable>
    <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item.name">
    </el-option>
  </el-select>
</template>
<script>
import {string} from 'jszip/lib/support'

export default {
  name: 'SelectDown',
  data() {
    return {
      active: ''
    }
  },
  props: {
    list: {
      type: Array,
      default() {
        return []
      },
      require: true
    },
    value: {
      required: true
    },
    code: {
      type: string,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default() {
        return {
          disabled: false,
          placeholder: '请选择',
          clearable: true
        }
      }
    }
  },
  created() {
  },

  methods: {
    change(item) {
      this.$emit('eventChange', item)
      this.$emit('update:value', item)
      const v = this.list.find((val) => val.name === item)
      this.$emit('update:code', v ? v.productCode : '')
      this.$emit('update:id', v ? v.productId : '')
      this.$emit('update:stock', v ? v.stock : 0)
    },
    clear() {
      this.$emit('eventClear')
    }
  },
  watch: {
    value: {
      handler(v) {
        this.active = v
      },
      immediate: true
    }
  }
}
</script>
<style>
.select {
  width: 100%;
}
</style>

