<template>
  <el-autocomplete class="select_input" v-model="val" :fetch-suggestions="querySearch" :placeholder="placeholder"
                   :clearable="clearable" value-key="name" @input="handleInput" @select="handlechangeInput">
    <i :class="[searchType?'el-icon-zoom-out':'el-icon-zoom-in', 'el-input__icon']" v-if="changeFilter==true" slot="prefix"
       @click="handleIconClick" />
    <slot />
  </el-autocomplete>
</template>
<script>
import { productList, getAllCompanyList, supplierList, customerList } from '@/api/public'
import { number } from 'echarts/lib/export'
export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '',
      list: [],
      searchType: true // true===LIKES false===EQS
    }
  },
  inject: ['filterOption'],
  props: {
    value: {
      required: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: <PERSON>olean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    changeFilter: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: ''
    }
  },
  async created() {
    console.log(this.type)
    // type==2 采购 type==1销售
    if (this.type == 2) {
      this.list = await supplierList()
    } else if (this.type == 1) {
      //收货单位
      this.list = await customerList()
      // this.list = await getAllCompanyList({ keyword: '' })
    }

    if (!this.changeFilter) {
      this.searchType = false
    }
  },
  methods: {
    querySearch(queryString, cb) {
      let list = this.list
      let results = queryString ? list.filter(this.createFilter(queryString)) : list
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => restaurant.name.toString().includes(queryString.toString())
    },
    handleInput(val) {
      this.$emit('update:value', val)
    },
    async handlechangeInput(val) {
      this.$nextTick(() => {
        this.$emit('update:value', val.name)
      })
    },
    handleIconClick() {
      if (!this.changeFilter) return this.$notify({ title: '提示', message: '当前搜索只支持模糊匹配', type: 'info' })
      this.searchType = !this.searchType
      let message = '切换成'
      message += this.searchType ? '模糊匹配' : '精确匹配'
      this.$notify({ title: '提示', message, type: 'success' })
      let map = {}
      map = {
        true: 'filter_LIKES_companyFullName',
        false: 'filter_EQS_companyFullName'
      }
      // 同步filterOption
      this.filterOption.columns.forEach((col) => {
        if (col.prop === 'companyFullName') col.filter = map[this.searchType + '']
      })
      // console.log(this.filterOption)
    }
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss" scoped>
.select_input {
  width: 100%;
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }
}
::v-deep .el-input__inner {
  padding-left: 24px !important;
}
::v-deep .el-input__prefix {
  cursor: pointer;
  width: 16px !important;
  line-height: 30px !important;
  height: 16px !important;
  font-size: 16px;
  left: 2px !important;
  right: 0 !important;
}
</style>
