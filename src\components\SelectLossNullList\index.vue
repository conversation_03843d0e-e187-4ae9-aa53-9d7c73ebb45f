<template>
  <div v-show="isShow">
    <el-autocomplete class="select_input" v-model="val" :fetch-suggestions="querySearch" :placeholder="placeholder"
                     :clearable="clearable" value-key="lossNull" @input="handleInput">
      <i :class="[searchType?'el-icon-zoom-out':'el-icon-zoom-in', 'el-input__icon']" slot="prefix" @click="handleIconClick" />
      <template v-slot="{ item }">
        <span>{{item.lossNull + ' ('+item.truckCount+')'}}</span>
      </template>
    </el-autocomplete>
  </div>

</template>
<script>
import { getfindLoseNull } from '@/api/public'
export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '',
      list: [],
      searchType: false, // true===LIKES false===EQS
    }
  },
  inject: ['filterOption'],
  props: {
    value: {
      required: true,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
    changeFilter: {
      type: Boolean,
      default: true,
    },
    serchCompanyFullName: {
      type: String,
      default: '',
    },
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value: {
      handler(v) {
        this.val = v.toString()
        // console.log(v)
      },
      immediate: true,
    },

    isShow: {
      handler(v) {
        if (v) {
          this.getfindLoseNullfn()
        } else {
          this.list = []
        }
      },
      immediate: true,
    },
  },
  async created() {
    // console.log(this.changeFilter)
    // this.list = await getfindLoseNull({ companyFullName: this.serchCompanyFullName })
    if (!this.changeFilter) {
      this.searchType = false
    }
  },
  methods: {
    async getfindLoseNullfn() {
      this.list = await getfindLoseNull({ companyFullName: this.serchCompanyFullName })
      // console.log(this.list)
    },
    querySearch(queryString, cb) {
      let list = this.list
      // console.log(list)
      // console.log(queryString)
      let results = queryString ? list.filter(this.createFilter(queryString)) : list
      // console.log(results)
      cb(results)
    },
    createFilter(queryString) {
      // console.log(queryString)
      return (restaurant) => restaurant.lossNull.toString().includes(queryString.toString())
    },
    handleInput(val) {
      // console.log(val)
      this.$emit('update:value', val)
    },
    handleIconClick() {
      if (!this.changeFilter) return this.$notify({ title: '提示', message: '当前搜索只支持模糊匹配', type: 'info' })
      this.searchType = !this.searchType
      let message = '切换成'
      message += this.searchType ? '模糊匹配' : '精确匹配'
      this.$notify({ title: '提示', message, type: 'success' })

      // console.log(this.searchType)

      // this.$parent.$parent.$parent.$emit('iconNameTrigger', this.searchType)
      let map = {}
      map = {
        true: 'filter_LIKES_lossNull',
        false: 'filter_EQS_lossNull',
      }
      // 同步filterOption
      this.filterOption.columns.forEach((col) => {
        if (col.prop === 'lossNull') col.filter = map[this.searchType + '']
      })
      // console.log(this.filterOption)
    },
  },
}
</script>
<style lang="scss" scoped>
.select_input {
  width: 100%;
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }
}
::v-deep .el-input__inner {
  padding-left: 24px !important;
}
::v-deep .el-input__prefix {
  cursor: pointer;
  width: 16px !important;
  line-height: 30px !important;
  height: 16px !important;
  font-size: 16px;
  left: 2px !important;
  right: 0 !important;
}
</style>
