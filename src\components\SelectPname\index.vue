<template>
  <el-autocomplete class="select_input" v-model="slectname" :fetch-suggestions="querySearch" :placeholder="placeholder"
                   :clearable="clearable" value-key="name" @input="handleInput">
    <i :class="[searchType?'el-icon-zoom-out':'el-icon-zoom-in', 'el-input__icon']" slot="prefix" @click="handleIconClick" />
    <slot />
  </el-autocomplete>
</template>
<script>
import { gettype, findUsedDeptByKey, findUsedManByKey } from '@/api/public'
// import MaterialinformationModel from '@/model/material/Materialinformation'
export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '',
      slectname: '',
      list: [],
      searchType: true // true===LIKES false===EQS
    }
  },
  inject: ['filterOption'],
  props: {
    value: {
      required: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择商品名称'
    },
    changeFilter: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: 'itemName'
    }
  },
  async created() {
    if (this.type == 'itemName') {
      this.list = await gettype({ ...this.listQuery, size: 999 })
    } else if (this.type == 'usedDept') {
      this.list = await findUsedDeptByKey()
    } else if (this.type == 'usedMan') {
      this.list = await findUsedManByKey()
    }

    if (!this.changeFilter) {
      this.searchType = false
    }
  },
  methods: {
    querySearch(queryString, cb) {
      let list = this.list
      let results = queryString ? list.filter(this.createFilter(queryString)) : list
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => restaurant.name.toString().includes(queryString.toString())
    },
    handleInput(val) {
      this.slectname = val
      this.$emit('update:value', val)
    },

    handleIconClick() {
      if (!this.changeFilter) return this.$notify({ title: '提示', message: '当前搜索只支持模糊匹配', type: 'info' })
      this.searchType = !this.searchType
      let message = '切换成'
      message += this.searchType ? '模糊匹配' : '精确匹配'
      this.$notify({ title: '提示', message, type: 'success' })
      // this.$parent.$parent.$parent.$emit('iconNameTrigger', this.searchType)
      const map = {}
      if (this.type == 'itemName') {
        map = {
          true: 'filter_LIKES_itemName',
          false: 'filter_EQS_itemName'
        }
      } else if (this.type == 'usedDept') {
        map = {
          true: 'filter_LIKES_usedDept',
          false: 'filter_EQS_usedDept'
        }
      } else if (this.type == 'usedMan') {
        map = {
          true: 'filter_LIKES_usedMan',
          false: 'filter_EQS_usedMan'
        }
      }

      // 同步filterOption
      this.filterOption.columns.forEach((col) => {
        if (col.prop === 'itemName') col.filter = map[this.searchType + '']
        if (col.prop === 'usedDept') col.filter = map[this.searchType + '']
        if (col.prop === 'usedMan') col.filter = map[this.searchType + '']
      })
      // console.log(this.filterOption)
    }
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss" scoped>
.select_input {
  width: 100%;
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }
}
::v-deep .el-input__inner {
  padding-left: 24px !important;
}
::v-deep .el-input__prefix {
  cursor: pointer;
  width: 16px !important;
  line-height: 30px !important;
  height: 16px !important;
  font-size: 16px;
  left: 2px !important;
  right: 0 !important;
}
</style>
