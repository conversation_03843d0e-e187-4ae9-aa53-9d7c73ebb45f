<template>
  <el-autocomplete class="select_input" v-model="val" :fetch-suggestions="querySearch" :placeholder="placeholder"
                   :clearable="clearable" value-key="name" @input="handleInput">
    <i v-show="iconisshow" :class="[searchType?'el-icon-zoom-out':'el-icon-zoom-in', 'el-input__icon']" slot="prefix"
       @click="handleIconClick" />

    <!-- <i v-show="!iconisshow" class="el-icon-arrow-down" style="padding-left:5px" slot="prefix" /> -->
    <slot />
  </el-autocomplete>
</template>
<script>
import { productList, coalproductList, washproductList } from '@/api/public'
import { array } from 'jszip/lib/support'
export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '',
      list: [],
      searchType: true, // true===LIKES false===EQS
    }
  },
  inject: ['filterOption'],
  props: {
    value: {
      required: true,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: '请选择品名',
    },
    changeFilter: {
      type: Boolean,
      default: true,
    },
    istype: {
      type: String,
      default: '',
    },
    iconisshow: {
      type: Boolean,
      default: true,
    },
  },
  async created() {
    // console.log(this.istype)
    if (this.istype == 'pei') {
      //生产管理--配煤生产日报 品名搜索
      this.list = await coalproductList()
    } else if (this.istype == 'xi') {
      //生产管理--洗煤生产日报 品名搜索
      this.list = await washproductList()
    } else if (this.istype == '') {
      this.list = await productList()
    }
    if (!this.changeFilter) {
      this.searchType = false
    }
  },
  methods: {
    async querySearch(queryString, cb) {
      // console.log('获取焦点')
      let that = this
      if (that.istype == 'pei') {
        //生产管理--配煤生产日报 品名搜索
        that.list = await coalproductList()
      } else if (that.istype == 'xi') {
        //生产管理--洗煤生产日报 品名搜索
        that.list = await washproductList()
      } else if (that.istype == '') {
        that.list = await productList()
      }

      let list = that.list

      let results = queryString ? list.filter(this.createFilter(queryString)) : list
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => restaurant.name.toString().includes(queryString.toString())
    },
    handleInput(val) {
      this.$emit('update:value', val)
    },

    handleIconClick() {
      if (!this.changeFilter) return this.$notify({ title: '提示', message: '当前搜索只支持模糊匹配', type: 'info' })
      this.searchType = !this.searchType
      let message = '切换成'
      message += this.searchType ? '模糊匹配' : '精确匹配'
      this.$notify({ title: '提示', message, type: 'success' })
      // this.$parent.$parent.$parent.$emit('iconNameTrigger', this.searchType)
      const map = {
        true: 'filter_LIKES_productName',
        false: 'filter_EQS_productName',
      }
      // 同步filterOption
      this.filterOption.columns.forEach((col) => {
        if (col.prop === 'name') col.filter = map[this.searchType + '']
        if (col.prop === 'productName') col.filter = map[this.searchType + '']
      })
      // console.log(this.filterOption)
    },
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true,
    },
  },
}
</script>
<style lang="scss" scoped>
.select_input {
  width: 100%;
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }
}
::v-deep .el-input__inner {
  padding-left: 24px !important;
}
::v-deep .el-input__prefix {
  cursor: pointer;
  width: 16px !important;
  line-height: 30px !important;
  height: 16px !important;
  font-size: 16px;
  left: 2px !important;
  right: 0 !important;
}
</style>
