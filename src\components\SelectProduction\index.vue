<template>
  <el-autocomplete class="select_input" v-model="val" :fetch-suggestions="querySearch" :placeholder="placeholder" clearable
                   @input="handleChange">
    <slot>
      <template slot="append">h/时</template>
    </slot>
  </el-autocomplete>
</template>
<script>

export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '',
      list: [
        { value: '350', _value: '350t/时' },
        { value: '400', _value: '400t/时' },
        { value: '420', _value: '420t/时' },
        { value: '450', _value: '450t/时' },
        { value: '500', _value: '500t/时' },
        { value: '550', _value: '550t/时' },
        { value: '600', _value: '600t/时' }
      ]
    }
  },
  props: {
    value: {
      required: true
    },
    type: {
      type: String,
      default: 'COAL_TYPE',
      required: false
    },
    placeholder: {
      type: String,
      default: '设定产量'
    }
  },
  created() { },
  methods: {
    querySearch(queryString, cb) {
      let list = this.list
      let results = queryString ? list.filter(this.createFilter(queryString)) : list
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => (restaurant.value.toString().indexOf(queryString.toString()) === 0)
    },
    handleChange(val) {
      this.$emit('update:value', val)
    }
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true
    }

  }
}
</script>
<style lang="scss" scoped>
.select_input {
  width: 100%;
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }
  ::v-deep .el-input__inner {
    // font-size: 14px;
  }
}
</style>
