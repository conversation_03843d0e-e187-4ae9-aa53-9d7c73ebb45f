<template>
  <el-autocomplete class="select_input" v-model="val" :fetch-suggestions="querySearch" :placeholder="placeholder"
                   @clear="blurForBug" :clearable="clearable" value-key="name" @input="handleInput" @select="handlechangeInput">
    <i :class="[searchType?'el-icon-zoom-out':'el-icon-zoom-in', 'el-input__icon']" slot="prefix" @click="handleIconClick" />
    <slot />
  </el-autocomplete>
</template>
<script>
import { productList, getAllCompanyList, supplierList, customerList, supplierListV } from '@/api/public'
export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '',
      list: [],
      searchType: true // true===LIKES false===EQS
    }
  },
  inject: ['filterOption'],
  props: {
    value: {
      required: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    changeFilter: {
      type: Boolean,
      default: true
    },
    isnodata: {
      type: Boolean,
      default: false
    },
    isNogetlist: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    selecttype: {
      type: String,
      default: ''
    }
  },
  async created() {
    let that = this
    if (!this.isNogetlist) {
      if (!this.isnodata) {
        that.list = await productList()
      } else if (that.isnodata) {
        that.list = await getAllCompanyList({ keyword: '' })
      }
    } else {
      if (this.type == 'senderName' || this.type == 'supplierName') {
        //发货单位
        this.list = await supplierList()
      }
      if (this.type == 'supplierNameV') {
        //发货单位
        this.list = await supplierListV()
      }

      if (this.type == 'receiverName' || this.type == 'customerName') {
        //收货单位
        this.list = await customerList()
      }
    }

    if (!this.changeFilter) {
      this.searchType = false
    }
  },
  methods: {
    querySearch(queryString, cb) {
      let list = this.list
      let results = queryString ? list.filter(this.createFilter(queryString)) : list
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => restaurant.name.toString().includes(queryString.toString())
    },
    handleInput(val) {
      this.$emit('update:value', val)
      if (val == '') {
        this.blurForBug()
      }
    },
    async handlechangeInput(val) {
      this.$emit('getlossNullListV', val)
      let map = {}
      if (this.isNogetlist) {
        if (this.type == 'senderName') {
          if (this.selecttype === 'aName') {
            map = {
              true: 'filter_LIKES_a.senderName',
              false: 'filter_EQS_a.senderName'
            }
          } else {
            map = {
              true: 'filter_LIKES_senderName',
              false: 'filter_EQS_senderName'
            }
          }
        }
        if (this.type == 'receiverName') {
          map = {
            true: 'filter_LIKES_receiverName',
            false: 'filter_EQS_receiverName'
          }
        }
        if (this.type == 'supplierName' || this.type == 'supplierNameV') {
          map = {
            true: 'filter_LIKES_supplierName',
            false: 'filter_EQS_supplierName'
          }
        }
        if (this.type == 'customerName') {
          map = {
            true: 'filter_LIKES_customerName',
            false: 'filter_EQS_customerName'
          }
        }
      } else {
        if (this.type == 'cargoType') {
          map = {
            true: 'filter_LIKES_cargoType',
            false: 'filter_EQS_cargoType'
          }
        }
      }
      this.filterOption.columns.forEach((col) => {
        if (col.prop === 'senderName') col.filter = map[this.searchType + '']
        if (col.prop === 'senderaName') col.filter = map[this.searchType + '']
        if (col.prop === 'receiverName') col.filter = map[this.searchType + '']
        if (col.prop === 'supplierName') col.filter = map[this.searchType + '']
        if (col.prop === 'customerName') col.filter = map[this.searchType + '']
        if (col.prop === 'cargoType') col.filter = map[this.searchType + '']
      })
      this.$nextTick(() => {
        this.$emit('update:value', val.name)
      })
    },
    blurForBug() {
      this.$emit('getlossNullListV', ' ')
      this.$emit('cargoType', '')
    },

    handleIconClick() {
      this.handleInput('')
      if (!this.changeFilter) return this.$notify({ title: '提示', message: '当前搜索只支持模糊匹配', type: 'info' })
      this.searchType = !this.searchType
      let message = '切换成'
      message += this.searchType ? '模糊匹配' : '精确匹配'
      this.$notify({ title: '提示', message, type: 'success' })
      let map = {}
      if (this.isNogetlist) {
        if (this.type == 'senderName') {
          if (this.selecttype === 'aName') {
            map = {
              true: 'filter_LIKES_a.senderName',
              false: 'filter_EQS_a.senderName'
            }
          } else {
            map = {
              true: 'filter_LIKES_senderName',
              false: 'filter_EQS_senderName'
            }
          }
        }
        if (this.type == 'receiverName') {
          map = {
            true: 'filter_LIKES_receiverName',
            false: 'filter_EQS_receiverName'
          }
        }
        if (this.type == 'supplierName') {
          map = {
            true: 'filter_LIKES_supplierName',
            false: 'filter_EQS_supplierName'
          }
        }
        if (this.type == 'customerName') {
          map = {
            true: 'filter_LIKES_customerName',
            false: 'filter_EQS_customerName'
          }
        }
      } else {
        if (this.type == 'cargoType') {
          map = {
            true: 'filter_LIKES_cargoType',
            false: 'filter_EQS_cargoType'
          }
        }
      }
      // 同步filterOption
      this.filterOption.columns.forEach((col) => {
        if (col.prop === 'senderName') col.filter = map[this.searchType + '']
        if (col.prop === 'receiverName') col.filter = map[this.searchType + '']
        if (col.prop === 'supplierName') col.filter = map[this.searchType + '']
        if (col.prop === 'customerName') col.filter = map[this.searchType + '']
        if (col.prop === 'cargoType') col.filter = map[this.searchType + '']
      })
      // console.log(this.filterOption)
    }
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss" scoped>
.select_input {
  width: 100%;
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }
}
::v-deep .el-input__inner {
  padding-left: 24px !important;
}
::v-deep .el-input__prefix {
  cursor: pointer;
  width: 16px !important;
  line-height: 30px !important;
  height: 16px !important;
  font-size: 16px;
  left: 2px !important;
  right: 0 !important;
}
</style>
