<template>
  <el-autocomplete class="select_input" v-model="val" :fetch-suggestions="querySearch" :placeholder="placeholder"
                   :clearable="clearable" value-key="name" @input="handleInput" @select="handlechangeInput">
    <i :class="[searchType?'el-icon-zoom-out':'el-icon-zoom-in', 'el-input__icon']" v-if="changeFilter==true" slot="prefix"
       @click="handleIconClick" />
    <slot />
  </el-autocomplete>
</template>
<script>
import { productList, getAllCompanyList, supplierList, customerList } from '@/api/public'
export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '',
      list: [],
      searchType: true // true===LIKES false===EQS
    }
  },
  inject: ['filterOption'],
  props: {
    value: {
      required: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    changeFilter: {
      type: Boolean,
      default: true
    },
    isnodata: {
      type: Boolean,
      default: false
    },
    isNogetlist: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    selecttype: {
      type: String,
      default: ''
    }
  },
  async created() {
    //发货单位
    this.list = await supplierList()

    // let that = this
    // if (!this.isNogetlist) {
    //   if (!this.isnodata) {
    //     that.list = await productList()
    //   } else if (that.isnodata) {
    //     that.list = await getAllCompanyList({ keyword: '' })
    //   }
    // } else {
    //   if (this.type == 'senderName') {
    //     //发货单位
    //     this.list = await supplierList()
    //   }
    //   if (this.type == 'receiverName' || this.type == 'customerName') {
    //     //收货单位
    //     this.list = await customerList()
    //   }
    // }

    if (!this.changeFilter) {
      this.searchType = false
    }
  },
  methods: {
    querySearch(queryString, cb) {
      let list = this.list
      let results = queryString ? list.filter(this.createFilter(queryString)) : list
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => restaurant.name.toString().includes(queryString.toString())
    },
    handleInput(val) {
      this.$emit('update:value', val)
    },
    async handlechangeInput(val) {
      this.$nextTick(() => {
        this.$emit('update:value', val.name)
      })
    },

    handleIconClick() {
      if (!this.changeFilter) return this.$notify({ title: '提示', message: '当前搜索只支持模糊匹配', type: 'info' })
      this.searchType = !this.searchType
      let message = '切换成'
      message += this.searchType ? '模糊匹配' : '精确匹配'
      this.$notify({ title: '提示', message, type: 'success' })

      let map = {}
      map = {
        true: 'filter_LIKES_supplierName',
        false: 'filter_EQS_supplierName'
      }
      // 同步filterOption
      this.filterOption.columns.forEach((col) => {
        if (col.prop === 'supplierName') col.filter = map[this.searchType + '']
      })
      // console.log(this.filterOption)
    }
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss" scoped>
.select_input {
  width: 100%;
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }
}
::v-deep .el-input__inner {
  padding-left: 24px !important;
}
::v-deep .el-input__prefix {
  cursor: pointer;
  width: 16px !important;
  line-height: 30px !important;
  height: 16px !important;
  font-size: 16px;
  left: 2px !important;
  right: 0 !important;
}
</style>
