<template>
  <el-autocomplete class="select_input" v-model="slectname" :fetch-suggestions="querySearch" :placeholder="placeholder"
                   :clearable="clearable" value-key="name" @input="handleInput">
    <slot />
  </el-autocomplete>
</template>
<script>
import { productList } from '@/api/public'
export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '',
      slectname: '',
      list: [
        { type: 'Y', name: '是' },
        { type: 'N', name: '否' },
      ],
      searchType: true, // true===LIKES false===EQS
    }
  },
  inject: ['filterOption'],
  props: {
    value: {
      required: true,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: '请选择过磅类型',
    },
    changeFilter: {
      type: Boolean,
      default: true,
    },
  },
  async created() {
    // this.list = await productList()
    if (!this.changeFilter) {
      this.searchType = false
    }
  },
  methods: {
    querySearch(queryString, cb) {
      let list = this.list
      let results = queryString ? list.filter(this.createFilter(queryString)) : list
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => restaurant.name.toString().includes(queryString.toString())
    },
    handleInput(val) {
      this.slectname = val
      if (val == '是') {
        val = 'Y'
      } else {
        val = 'N'
      }
      this.$emit('update:value', val)
    },
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true,
    },
  },
}
</script>
<style lang="scss" scoped>
.select_input {
  width: 100%;
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }
}
::v-deep .el-input__prefix {
  cursor: pointer;
  width: 16px !important;
  line-height: 30px !important;
  height: 16px !important;
  font-size: 16px;
  left: 2px !important;
  right: 0 !important;
}
</style>
