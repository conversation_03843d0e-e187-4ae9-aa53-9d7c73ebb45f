<template>
  <el-select ref="comSelect" class="common-select" v-model="val" :placeholder="placeholder" :disabled="disabled" v-on="$listeners"
             :loading="loading" :remote="remote" :clearable="clearable" :filterable="filterable" @change="handleChange"
             v-bind="$attrs">
    <template v-if="haveGroup">
      <el-option-group>
        <el-option v-for="item in list" :key="item[option.key]" :label="item[option.label]" :value="item[option.value]">
          <slot name="optionItem" :item="item"></slot>
        </el-option>
      </el-option-group>
    </template>
    <template v-else>
      <el-option v-for="item in list" :key="item[option.key]" :label="item[option.label]" :value="item[option.value]">
        <slot name="optionItem" :item="item"></slot>
      </el-option>
    </template>
  </el-select>
</template>
<script>
export default {
  name: 'ComSelect',
  inheritAttrs: false,
  data() {
    return {
      val: ''
    }
  },
  computed: {
    refSelect() {
      return this.$refs.comSelect && this.$refs.comSelect
    }
  },
  props: {
    value: {
      type: String,
      default: '',
      require: true
    },
    haveGroup: {
      type: Boolean,
      default: false
    },
    remote: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    filterable: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: `请选择`
    },
    list: {
      type: Array,
      default: () => [],
      require: true
    },
    option: {
      type: Object,
      default() {
        return {
          label: 'label',
          value: 'value',
          key: 'value'
        }
      }
    }
  },
  watch: {
    value(newValue) {
      this.val = newValue
    }
  },
  methods: {
    handleChange(value) {
      this.$emit('update:value', value)
      this.$emit('change', this.searchItem(value))
    },
    searchItem(value) {
      return value ? this.list.filter(item => item[this.option.value] === value)[0] : {}
    }

  }
}
</script>

<style  scoped lang='scss'>
</style>