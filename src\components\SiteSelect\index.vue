<template>
  <el-select class="select" :value="value" :clearable="clearable" placeholder="请选择" remote filterable :remote-method="search"
             :loading="loading" :disabled="disabled" @input="change($event)">
    <el-option v-for="item in list" :key="item.code" :label="item.name" :value="item.code">
    </el-option>
  </el-select>
</template>
<script>
import { findByKeyword, findSiteByKeyword } from '@/api/siteList'

export default {
  name: 'SiteSelect',
  data() {
    return {
      loading: false,
      list: []
    }
  },
  props: {
    value: {
      required: true
    },
    name: {
      type: String
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    isTopMenu: { // 含有顶级菜单
      type: Boolean,
      default: false
    },
    isOnlySite: { // 是否只查网点(true: 网点, false: 网点和分拨中心)
      type: <PERSON>olean,
      default: false
    }
  },
  mounted() {
    if (this.isTopMenu) {
      this.list.push({ code: 'root', name: '顶级菜单' })
    }
  },
  watch: {
    name: {
      handler(val) {
        this.list = []
        this.list.push({ code: this.value, name: val })
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    search(keyword) {
      if (this.isOnlySite) {
        findSiteByKeyword(keyword).then(response => {
          this.list = response.data
          if (this.isTopMenu) {
            this.list.unshift({ code: 0, name: '顶级菜单' })
          }
        })
      } else {
        findByKeyword(keyword).then(response => {
          this.list = response.data
          if (this.isTopMenu) {
            this.list.unshift({ code: 0, name: '顶级菜单' })
          }
        })
      }
    },
    change(val) {
      let name = ''
      let info = {
        siteName: '',
        siteCode: ''
      }
      this.list.filter(v => {
        if (v.code === val) {
          name = v.name
          info.siteCode = v.code
          info.siteName = v.name
        }
      })
      this.$emit('input', val)
      this.$emit('input:name', name)
      this.$emit('info', info)
    }
  }
}
</script>
<style>
.select {
  width: 100%;
}
</style>

