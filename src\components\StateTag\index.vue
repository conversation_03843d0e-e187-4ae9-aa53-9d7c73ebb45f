<template>
  <div v-if="value">
    <el-tag :disable-transitions="true" :class="{'is-dark':dark,'is-light': light}" style="margin-right: 2px"
            v-for="(item, index) in labelShow" :key="index" :type="tagType" disableTransitions :effect="effect">
      {{ item }}
    </el-tag>
  </div>
</template>
<script>
import { getStateTextFromDict } from '@/filters/index'

export default {
  name: 'StateTag',
  data() {
    return {
      dark: false,
      light: false,
      labelShow: [],
    }
  },
  props: {
    value: {
      type: String,
    },
    label: {
      type: String,
    },
    format: {
      type: String,
    },
    effect: {
      type: String,
    },
  },
  computed: {
    tagType() {
      let type
      switch (this.value) {
        case 'jj':
          type = 'danger'
          this.dark = true
          this.light = false
          break
        case 'cg':
          type = 'info'
          this.dark = true
          this.light = false
          break
        case 'hywc':
          type = 'success'
          this.dark = true
          this.light = false
          break
        case 'hyfb':
          type = 'success'
          this.dark = true
          this.light = false
          break
        case 'hyz':
          type = 'warning'
          this.dark = true
          this.light = false
          break
        default:
          type = 'primary'
          break
      }
      return type
    },
  },
  watch: {
    value(val) {
      let vArray = []
      this.labelShow = []
      if (val.indexOf(',') >= 0) {
        vArray = val.split(',')
      } else {
        vArray = [val]
      }
      if (this.format) {
        vArray.forEach((v) => {
          this.labelShow.push(getStateTextFromDict(v, this.format))
        })
      } else {
        this.labelShow = [this.label]
      }
    },
  },
  mounted() {
    let vArray = []
    if (this.value.indexOf(',') >= 0) {
      vArray = this.value.split(',')
    } else {
      vArray = [this.value]
    }
    if (this.format) {
      vArray.forEach((v) => {
        this.labelShow.push(getStateTextFromDict(v, this.format))
      })
      // this.labelShow = getStateTextFromDict(this.value, this.format)
    } else {
      this.labelShow = [this.label]
    }
  },
  methods: {},
  beforeDestroy() {
    this.labelShow = []
  },
}
</script>
