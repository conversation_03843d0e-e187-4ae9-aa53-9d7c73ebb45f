<template>
    <el-steps :active="activeStep">
        <el-step title="草稿" icon="el-icon-s-order"></el-step>
        <el-step title="待审批" icon="el-icon-s-check"></el-step>
        <el-step title="待收款" icon="el-icon-wallet"></el-step>
        <el-step title="待分配" icon="el-icon-s-custom"></el-step>
        <el-step title="待完成" icon="el-icon-edit-outline"></el-step>
        <el-step title="待发布" icon="el-icon-upload"></el-step>
        <el-step title="化验结果" icon="el-icon-s-claim"></el-step>
    </el-steps>
</template>

<script>
    export default {
        name: 'Step',
        props: {
            activeStep: {
                required: true
            }
        }
    }
</script>

<style scoped>

</style>
