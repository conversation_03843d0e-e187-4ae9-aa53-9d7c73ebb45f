<template>
  <div class="superTableContent">
    <!--        v-loading="loading"-->
    <div :class="[showWidth ? 'min-super-table':'super-table']">
      <el-card>
        <div class="filter-container" ref="filterContainer">
          <div style="width: 100%">
            <slot name="filter"></slot>
            <div style="display:flex; align-items: center">
              <template v-if="filters.length >0">
                <template v-for="(filter, index) in filters">
                  <div class="filter-item" :key="index">
                    <div class="lable">{{ filter.label }}:</div>
                    <slot v-if="filter.slot" :name="filter.slot" class="filter-item"></slot>
                    <component v-if="filter.component" v-model="listQuery[filter.prop]"
                               :ownerId="listQuery[filter.need]"
                               :is="filter.component" :isDefault="filter.isDefault"
                               :beginDate.sync="listQuery[filter.beginDate]"
                               :endDate.sync="listQuery[filter.endDate]" :start.sync="listQuery[filter.start]"
                               :end.sync="listQuery[filter.end]" :type="filter.type">
                    </component>
                    <el-input style="width:130px" v-else v-model="listQuery[filter.prop]" clearable
                              :placeholder="filter.label">
                    </el-input>
                  </div>
                </template>
                <div class="filter-item filter-button-wrap"
                     v-if="(config.tabs && config.tabs.length >0) || filters.length >0">
                  <el-button type="search" @click="getList" style="height: 30px;width: 60px;">搜索
                  </el-button>
                  <el-button type="reset" @click="resetSearch" style="height: 30px;width: 60px;">重置
                  </el-button>

                </div>
              </template>
            </div>
          </div>
          <div v-if="actions && actions.length >0" style="display: flex">
            <template v-if="actions && actions.length >0">
              <template v-if="actions.length >1">
                <template v-for="(action,index) in actions">
                  <el-button style="height: 30px;width: 60px;" :key="index" v-bind="action.config" @click="action.click"
                             :data-val="action.params"
                             v-if="action.permission ? permissionMap[action.permission] :true">
                    {{ action.label }}
                  </el-button>
                </template>
              </template>
              <template v-else>
                <template v-for="action in actions">
                  <el-button style="height: 30px;width: 60px;" v-bind="action.config" @click="action.click"
                             v-if="action.permission ? permissionMap[action.permission] :true">
                    {{ action.label }}
                  </el-button>
                </template>
              </template>
            </template>
          </div>
        </div>
      </el-card>
      <div class="superTable-container">
        <div class="superTable-content">
          <el-table ref="superTable" :data="list" :height="height" style="width: 100%" v-loading="loading"
                    element-loading-text="加载中..." :border="border" :cell-class-name="handleAddStyle"
                    @sort-change="handleSortChange" @row-contextmenu="handleContextmenu"
                    @selection-change="handleSelectionChange"
                    :multiselectable="multiselectable" :show-summary="showSummary" :summary-method="getSummaries"
                    v-on="$listeners" :empty-text="emptyText" :header-cell-style="{background:'#2F79E8', color:'#fff'}">
            <el-table-column type="selection" width="43" align="center" v-if="showSelection">
            </el-table-column>
            <template v-for="colConfig in config.columns">
              <template v-if="colConfig.isShow && !colConfig.head">
                <slot v-if="colConfig.slot" :name="colConfig.slot"></slot>
                <component v-if="colConfig.component" :is="colConfig.component" :col-config="colConfig">
                </component>
                <el-table-column v-else v-bind="colConfig" :show-overflow-tooltip="true" v-on="$listeners">
                </el-table-column>
              </template>
              <template v-if="colConfig.head">
                <el-table-column :label="colConfig.head" v-if="colConfig.group">
                  <template v-for="col in colConfig.group">
                    <slot v-if="col.slot" :name="col.slot"></slot>
                    <component v-if="col.component" :is="col.component" :col-config="col">
                    </component>
                    <el-table-column v-else v-bind="col" :show-overflow-tooltip="true" v-on="$listeners">
                    </el-table-column>
                  </template>
                </el-table-column>
                <el-table-column v-else :label="colConfig.head">
                  <slot v-if="colConfig.slot" :name="colConfig.slot"></slot>
                  <component v-if="colConfig.component" :is="colConfig.component" :col-config="colConfig">
                  </component>
                  <el-table-column v-else v-bind="colConfig" :show-overflow-tooltip="true" v-on="$listeners">
                  </el-table-column>
                </el-table-column>
              </template>
            </template>
          </el-table>
          <div class="pagination-container" style="display: flex;justify-content: flex-end;align-items: center">
            <el-pagination :current-page="listQuery.current" :layout="pagination.layout"
                           :page-sizes="pagination.pageSizes"
                           class="pagination-container" :next-text="pageNextText" :background="pageBackground"
                           :page-size="listQuery.size" :total="total" @size-change="handleSizeChange"
                           @current-change="handleCurrentChange">
            </el-pagination>
          </div>
        </div>
      </div>

      <context-menu v-if="currentRow && contextMenus && contextMenus.length > 0" :target.sync="contextMenuTarget">
        <ul class="el-dropdown-menu" slot="action">
          <li tabindex="-1" class="el-dropdown-menu__item" v-for="(row,index) in contextMenus"
              @click="row.click(currentRow)"
              :key="index">
            <i :class="row.icon" v-if="row.permission ? permissionMap[row.permission] :true"></i>{{ row.label }}
          </li>
          <slot name="contextMenu"></slot>
        </ul>
      </context-menu>
      <el-dialog ref="settingDialog" top="2%" width="456px" :title="dialogTitle" :visible.sync="dialogVisible"
                 :before-close="handleClose" :close-on-press-escape="true" :close-on-click-modal="true">
        <el-transfer filterable :filter-method="filterMethod" filter-placeholder="请输入名称"
                     :titles="['未选中', '已选中']"
                     v-model="selectedItems" :data="allItems">
        </el-transfer>
        <div slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="saveSetting">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {pagination} from '@/const'
import Func from '@/utils/func'
import {mapGetters} from 'vuex'
import {exportExcel} from '@/utils'

const Filters = require('@/filters')
const listQuery = {
  current: 1,
  size: pagination.pageSize
}
export default {
  name: 'SuperTable',
  components: {},
  data() {
    return {
      pagination,
      list: [],
      sums: [],
      tableHeight: 200,
      loading: false,
      total: 0,
      listQuery: Object.assign({}, listQuery),
      currentRow: undefined,
      contextMenuTarget: {
        x: 0,
        y: 0
      },
      dialogTitle: '设置',
      dialogVisible: false,
      settingCommand: '',
      selectedItems: [],
      allItems: [],
      config: {
        tabProp: '', // 表格tab头部绑定
        tabs: [],
        columns: [], // 表格显示列
        listQuery: undefined,
        actions: [],
        contextMenus: []
      }
    }
  },
  computed: {
    ...mapGetters(['permissionMap']),
    filters() {
      const filters = []
      this.config.columns.forEach((val) => (val.isFilter && 'filter' in val ? filters.push(val.filter) : ''))
      this.config.columns.forEach((val) => {
        if (val.group) {
          val.group.forEach((g) => (g.isFilter && 'filter' in g ? filters.push(g.filter) : ''))
        }
      })
      return filters.concat(this.customFilters)
    },
    formats() {
      const formats = []
      this.config.columns.forEach((val) => {
        if (val['format']) {
          formats.push(val)
        }
        if (val['group']) {
          val.group.forEach((g) => {
            if (g['format']) formats.push(g)
          })
        }
      })
      return formats
    },
    averageColumns() {
      const keys = {}
      this.config.columns.forEach((val) => {
        if (val['isAverage']) {
          keys[val.prop || val.slot] = true
        }
        if (val['group']) {
          val['group'].forEach((val) => {
            if (val['isAverage']) {
              keys[val.prop || val.slot] = true
            }
          })
        }
      })
      return keys
    },
    weightingAverageColumns() {
      // 加权值和
      const keys = {}
      this.config.columns.forEach((val) => {
        if (val['isWeightingAverage']) {
          keys[val.prop || val.slot] = true
        }
        if (val['group']) {
          val['group'].forEach((val) => {
            if (val['isWeightingAverage']) {
              keys[val.prop || val.slot] = true
            }
          })
        }
      })
      return keys
    },
    hadSelection() {
      return this.defaultConfig.columns.findIndex((val) => val['type'] === 'selection') > -1
    }
  },
  props: {
    border: true,
    showSelection: false,
    showWidth: {
      type: Boolean,
      default: false
    },
    name: {
      type: String,
      required: true
    },
    pageBackground: {
      type: Boolean,
      default: true
    },
    pageNextText: {
      type: String,
      default: '下一页'
    },
    addStyle: {
      // 是否添加样式
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    weightingValue: {
      // 加权值
      type: String,
      default: ''
    },
    showWeighting: {
      // 是否显示加权平均
      type: Boolean,
      default: false
    },
    showSum: {
      // 是否显示合计
      type: Boolean,
      default: false
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    defaultConfig: {
      type: Object,
      required: true,
      default() {
        return {
          tabProp: '', // 表格tab头部绑定
          tabs: [],
          columns: [], // 表格显示列
          listQuery: undefined,
          actions: []
        }
      }
    },
    customFilters: {
      type: Array,
      required: false,
      default() {
        return []
      }
    },
    actions: {
      default() {
        return []
      }
    },
    contextMenus: {
      default() {
        return []
      }
    },
    offsetTop: {
      // 表格离顶部的高度
      type: Number,
      default: 0
    },
    height: {
      type: Number,
      default: 0
    },
    minHeight: {
      // 最小高度
      type: Number,
      default: 360
    },
    listFunc: {
      // 查询的方法
      required: true
    },
    mountQuery: {
      // 是否在初始化完成后查询
      type: Boolean,
      default: false
    },
    showSummary: {
      // 是否显示合计
      type: Boolean,
      default: false
    },
    exportAble: {
      type: Boolean, // 是否可以导出
      default: false
    },
    showSetting: {
      type: Boolean, // 是否可以设置
      default: false
    },
    noOrder: {
      type: Boolean, // 自定义搜索 只取外部搜索条件
      default: false
    },
    query: {
      type: Object,
      default() {
        return {}
      }
    },
    size: {
      type: Number,
      default: 0
    },
    multiselectable: {
      type: Boolean,
      default: false
    },
    istype: {
      type: String,
      default: 'page'
    }
  },
  watch: {
    tableHeight(val) {
      if (val) {
        this.$refs['superTable'].doLayout()
      }
    },
    'config.columns'() {
      setTimeout(() => {
        this.doLayout()
      }, 500)
    },
    list(val) {
      this.$emit('isEmptyList', !val.length)
      if (val) {
        this.$emit('update:refData', val)
      }
    },
    query(val) {
      if (val) {
      }
    },
    backList(val) {
      if (val) {
        this.list = val
      }
    }
  },
  async mounted() {
    const _this = this
    this.config = this.defaultConfig
    if (this.size > 0) {
      this.listQuery.size = this.size
    }
    if (this.config.listQuery) {
      this.resetSearch()
    }
    if (this.config.tabProp) {
      this.$watch('listQuery.' + this.config.tabProp, (val) => {
        this.$emit('update:tabProp', val)
      })
    }
    if (this.mountQuery) {
      this.getList()
    }

    window.onresize = () => {
      _this.doLayout()
    }
    this.$on('refresh', this.getList)
    this.$on('setQuery', this.getList)
    this.$on('resetSearch', this.resetSearch)
  },
  methods: {
    /**
     * 重新布局表格
     */
    doLayout: function () {
      // if (this.height > 0) {
      //   this.tableHeight = this.height
      // } else {
      // const fH = this.$refs['filterContainer'] ? this.$refs['filterContainer'].clientHeight + 5 : 0
      // const aH = this.$refs['action'] ? this.$refs['action'].clientHeight : 0
      // let tH = document.documentElement.clientHeight - fH - aH - 106 - this.offsetTop
      // if (tH < this.minHeight) {
      //   tH = this.minHeight
      // }
      this.tableHeight = this.height
      // console.log('doLayout:' + this.tableHeight)
      // }
    },
    handleSizeChange(val) {
      //        alert(val)
      this.listQuery.size = val
      this.getList()
    },
    handleAddStyle({row, column, rowIndex, columnIndex}) {
      if (!this.addStyle) {
        return false
      }
    },
    handleCurrentChange(val) {
      this.listQuery.current = val
      this.getList()
    },
    handleSortChange({prop, order}) {
      this.listQuery.orderBy = prop ? prop.split('_')[0] : ''
      this.listQuery.orderDir = order === 'descending' ? 'desc' : 'asc'
      this.getList()
    },
    handleContextmenu(row, event) {
      event.preventDefault()
      event.stopPropagation()
      this.currentRow = row
      this.contextMenuTarget = {
        x: event.x,
        y: event.y
      }
    },
    async _getList(queryAll) {
      const _this = this
      _this.loading = true

      if (this.istype === 'page') {
        await this.$nextTick()
        const query = this.noOrder
          ? Object.assign(this.listQuery, this.query)
          : Object.assign(this.listQuery, {
            orderBy: 'createDate',
            orderDir: 'desc'
          })
        if (queryAll) {
          query.size = 10000
        }
        const res = await Func.fetch(this.listFunc, query)
        _this.loading = false
        let list = []
        let total = 0
        if (res) {
          list = res.data.records.map(function (val) {
            let ext
            let remarks
            try {
              ext = JSON.parse(val.ext)
              if (Array.isArray(ext)) {
                ext = {
                  demo: ext
                }
              }
            } catch (e) {
              ext = {}
            }
            try {
              remarks = JSON.parse(val.remarks)
              if (Array.isArray(remarks)) {
                remarks = {
                  list: remarks
                }
              }
            } catch (e) {
              remarks = {}
            }
            const formatObj = {}
            if (_this.formats.length > 0) {
              _this.formats.forEach((f) => {
                const k = f.prop.split('_')[0]
                formatObj[f.prop] = Filters[f.format](val[k])
              })
            }
            return Object.assign({}, val, ext, formatObj, remarks)
          })
        }
        total = res.data.total
        if (this.name === 'ovenTempReport') {
          let obj = {}
          let _list = []
          list.sort(function (a, b) {
            return a.ovenCode - b.ovenCode
          })
          list.forEach((item) => {
            if (obj[item.collectionIndex]) {
              obj[item.collectionIndex].push(item)
            } else {
              obj[item.collectionIndex] = []
              obj[item.collectionIndex].push(item)
            }
          })
          Object.keys(obj)
            .sort()
            .forEach((ind) => {
              _list = _list.concat(obj[ind])
            })
          list = _list
        }
        return {
          total: total,
          list: list
        }
      } else if (this.istype === 'list') {
        await this.$nextTick()
        const query = this.noOrder
          ? Object.assign(this.listQuery, this.query)
          : Object.assign(this.listQuery, {
            orderBy: 'createDate',
            orderDir: 'desc'
          })
        if (queryAll) {
          query.size = 10000
        }
        const res = await Func.fetch(this.listFunc, query)
        _this.loading = false
        let list = []
        let total = 0
        if (res) {
          list = res.data.map(function (val) {
            let ext
            let remarks
            try {
              ext = JSON.parse(val.ext)
              if (Array.isArray(ext)) {
                ext = {
                  demo: ext
                }
              }
            } catch (e) {
              ext = {}
            }
            try {
              remarks = JSON.parse(val.remarks)
              if (Array.isArray(remarks)) {
                remarks = {
                  list: remarks
                }
              }
            } catch (e) {
              remarks = {}
            }
            const formatObj = {}
            if (_this.formats.length > 0) {
              _this.formats.forEach((f) => {
                const k = f.prop.split('_')[0]
                formatObj[f.prop] = Filters[f.format](val[k])
              })
            }
            return Object.assign({}, val, ext, formatObj, remarks)
          })
        }
        total = res.data.total
        if (this.name === 'ovenTempReport') {
          let obj = {}
          let _list = []
          list.sort(function (a, b) {
            return a.ovenCode - b.ovenCode
          })
          list.forEach((item) => {
            if (obj[item.collectionIndex]) {
              obj[item.collectionIndex].push(item)
            } else {
              obj[item.collectionIndex] = []
              obj[item.collectionIndex].push(item)
            }
          })
          Object.keys(obj)
            .sort()
            .forEach((ind) => {
              _list = _list.concat(obj[ind])
            })
          list = _list
        }
        return {
          total: total,
          list: list
        }
      }
    },
    async getList(reset = false) {
      this.$emit('summary', this.listQuery)
      let flag = true
      let message = ''
      this.filters.every((val) => {
        if (val['required'] && !this.listQuery[val.prop]) {
          flag = false
          message = val['message'] || '请输入' + val.label
          return false // 在使用every时候 返回false 跳出循环
        }
      })
      if (!flag) {
        this.$message({
          showClose: true,
          message: message,
          type: 'error'
        })
        return
      }
      if (reset) {
        this.listQuery.current = 1
      }
      this.loading = true
      const res = await this._getList()
      this.loading = false
      this.total = res.total
      this.list = res.list
      this.$emit('update:list', this.list)
      this.$emit('update:total', this.total)
    },
    getSummaries(param) {
      const {columns, data} = param
      const average = []
      columns.forEach((column, index) => {
        if (index === 0) {
          average[index] = this.showWeighting ? '加权平均值' : this.showSum ? '合计' : '平均值'
          return
        }
        const values = data.map((item) => {
          return this.averageColumns[column.property] ? Number(item[column.property]) : '-'
        })
        if (!this.showWeighting && !this.showSum) {
          if (!values.every((value) => isNaN(value))) {
            average[index] =
              values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!isNaN(value)) {
                  return prev + curr
                } else {
                  return prev
                }
              }, 0) / values.length
            average[index] = parseFloat(average[index].toFixed(2))
          } else {
            average[index] = ''
          }
        } else if (this.showWeighting) {
          const weightingValues = data.map((item) => {
            return this.weightingAverageColumns[column.property] && item[column.property] != null
              ? Number(item[column.property])
              : '-'
          })

          const weighting = data.map((item) => {
            return item[this.weightingValue] ? Number(item[this.weightingValue]) : '_'
          })
          const weightingSum = weighting.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          let hasNull = false
          if (!weightingValues.every((value) => isNaN(value))) {
            average[index] =
              weightingValues.reduce((prev, curr, idx) => {
                if (isNaN(curr)) {
                  hasNull = true
                }
                const value = Number(curr)
                if (!isNaN(value)) {
                  return prev + curr * weighting[idx]
                } else {
                  return prev
                }
              }, 0) / weightingSum
            average[index] = parseFloat(average[index].toFixed(2))
          } else {
            average[index] = ''
          }
          if (hasNull) {
            average[index] = ''
          }
          if (this.weightingValue === column.property) {
            average[index] = parseFloat(weightingSum.toFixed(2))
          }
        } else {
          if (!values.every((value) => isNaN(value))) {
            average[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                const num1Digits = (prev.toString().split('.')[1] || '').length
                const num2Digits = (curr.toString().split('.')[1] || '').length
                const baseNum = Math.pow(10, Math.max(num1Digits, num2Digits))
                return (prev * baseNum + curr * baseNum) / baseNum
                // return parseFloat(prev.toPrecision(16)) + parseFloat(curr.toPrecision(16))
              } else {
                return parseFloat(prev.toPrecision(16))
              }
            }, 0)
          } else {
            average[index] = ''
          }
        }
      })
      this.sums = [...average]
      return average
    },
    handleSetting(command) {
      this.settingCommand = command
      this.allItems = []
      const selected = []
      switch (command) {
        case 'list':
          this.dialogTitle = '列表设置'
          this.allItems = this.config.columns.map((val) => {
            return {
              key: val['prop'],
              label: val['label'],
              disabled: val['notHidden']
            }
          })
          this.config.columns.forEach((val) => {
            if (val.isShow) {
              selected.push(val.prop)
            }
          })
          break
        case 'filter':
          this.dialogTitle = '筛选设置'
          this.config.columns.forEach((val) => {
            if ('filter' in val) {
              this.allItems.push({
                key: val['prop'],
                label: val.filter['label'],
                disabled: false
              })
            }
          })
          this.config.columns.forEach((val) => {
            if (val.isFilter) {
              selected.push(val.prop)
            }
          })
          break
        case 'sort':
          this.dialogTitle = '排序设置'
          break
      }
      this.selectedItems = selected
      this.dialogVisible = true
    },
    resetSearch() {
      this.listQuery = Object.assign({}, listQuery, this.config.listQuery)
      this.$emit('clean', this.listQuery)
      this.getList()
    },
    updateSearch(otherQuery) {
      this.listQuery = Object.assign({}, listQuery, otherQuery)
    },
    filterMethod(query, item) {
      return query ? item.label.indexOf(query) > -1 : item
    },
    saveSetting() {
      this.dialogVisible = false
      // const _this = this
      switch (this.settingCommand) {
        case 'list':
          this.config.columns = this.config.columns.map((val) => {
            val['isShow'] = this.selectedItems.includes(val.prop)
            return val
          })
          break
        case 'filter':
          this.config.columns = this.config.columns.map((val) => {
            val['isFilter'] = this.selectedItems.includes(val.prop)
            return val
          })
          break
        case 'sort':
          break
      }
      // setTemplate(this.name, this.config)
    },
    input(val) {
      this.$emit('getCustomerName', val)
    },
    /**
     * 关闭设置
     */
    handleClose() {
      this.dialogVisible = false
      setTimeout(() => {
        this.selectedItems = []
        this.allItems = []
      }, 200)
    },
    async handleDownload(type) {
      const batchNo = Filters.dateFormat(new Date(), 'yyyymmdd')
      const header = []
      const columns = []
      let sumRow = {}
      let i = 0
      this.config.columns.forEach((val) => {
        sumRow[val.prop] = this.sums[i++]
        if (!val.type && val.isShow) {
          if (!val.noExport) {
            const tHeader = val.isHeader ? val.isHeader : val.label
            if (tHeader !== undefined) {
              header.push(tHeader)
              columns.push(val.prop)
            }
            if (val.group) {
              val.group.forEach((g) => {
                columns.push(g.prop)
                header.push(g.label)
              })
            }
          }
        }
      })
      let list = [...this.list]
      if (type === 'all') {
        const res = await this._getList('all')
        list = res.list
      } else {
        // add sumary line
        if (this.showSummary) {
          list.push(sumRow)
        }
      }
      exportExcel({
        title: this.title + '-' + batchNo,
        header: header,
        columns: columns,
        list: list
      })
    },
    handleSelectionChange(row) {
      this.$emit('update:selectedRow', row)
    }
  }
}
</script>
<style lang="scss">
.filter-container {
  border-top: none;
  display: flex;
  align-items: center;
  background: #fff;
  margin-bottom: 0;
  justify-content: space-between;
  padding: 5px 0;

  .filter-item {
    display: flex;
    margin-bottom: 0;
    margin-right: 5px;

    .lable {
      min-width: 50px;
      display: flex;
      align-items: center;
      padding: 0 5px;
    }

    ::v-deep .el-input {
      width: 120px !important;

      .el-input__inner {
        height: 28px;
        line-height: 28px;
      }
    }
  }

  button {
    height: 28px;
    margin-left: 10px;
    // padding: 12px;
  }
}

.el-table .cell,
.el-table .cell .el-input__inner {
  font-size: 12px;
  font-weight: 400;
  height: 35px;
  line-height: 35px;
  padding: 0 5px;
}

.el-dialog__body {
  padding: 5px;

  .el-transfer__buttons {
    padding: 0 5px;
  }

  .el-transfer-panel__body {
    height: 400px;
  }

  .el-transfer-panel__list {
    height: 355px !important;
  }

  .el-checkbox__label {
    font-size: 12px;
  }

  .el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label {
    font-size: 14px;
  }
}

.super-table {
  min-width: 100%;
  position: relative;
  margin: 0 auto;
  border-top: 1px solid #e6ebf5;
}

.min-super-table {
  width: 860px;
  position: relative;
  margin: 0 auto;
  border-top: 1px solid #e6ebf5;
}

.reds {
  background: #ff9140;
}

.pagination-container.is-background .el-pager li {
  /*对页数的样式进行修改*/

  background: #fff;
  color: #646464;
  border: 1px solid #f1f1f2;
}

.el-pager li {
  font-weight: 400;
}

.pagination-container.is-background .btn-next {
  padding: 0 10px;
  background: #fff;
  color: #646464;
  border: 1px solid #f1f1f2;
}

.pagination-container.is-background .el-pager li:not(.disabled).active {
  /*当前选中页数的样式进行修改*/
  background-color: #2f79e8;
  color: #fff;
}

.el-pagination__jump {
  margin-left: 0px;
}
</style>
