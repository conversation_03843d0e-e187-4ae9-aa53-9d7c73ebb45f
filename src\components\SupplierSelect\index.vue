<template>
  <el-select class="supplierSelect" v-model="val" filterable :placeholder="placeholder" @change="handleChange"
             :clearable="clearable">
    <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item.name" :disabled="disabled" />
  </el-select>
</template>
<script>
import { supplierList } from '@/api/public'
export default {
  name: 'supplierSelect',
  data() {
    return {
      val: '',
      list: []
    }
  },
  props: {
    value: {
      required: true
    },
    disabled: {
      type: [Boolean],
      default: false
    },
    filterType: {
      type: String
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择品名'
    }
  },
  async created() {
    this.list = await supplierList()
  },
  methods: {
    handleChange(name) {
      let searchName = this.list.find(item => item.name === name)
      this.val = name
      this.$emit('update:value', name || '')
      this.$emit('change', searchName || { name: '', id: '' })
    }
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss" scoped>
.supplierSelect {
  width: 100%;
}
</style>
