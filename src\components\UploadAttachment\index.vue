<template>
  <el-upload class="uploadAttachment" :accept="accept" :file-list="fileList" :action="actionUrl" :before-upload="beforeUploadFile"
             multiple :data="uploadData" :headers="uploadHeaders" :on-exceed="exceedFile" :on-success="uploadSuccess"
             :list-type="listType" :show-file-list="isShowFile" :limit="limitNum">

    <template v-if="source==='ContractBuy'">
      <div class="upload" v-show="limitNum>size">
        <img alt="" src="@/assets/upload.png" class="upload-img">
      </div>
    </template>
    <template v-else>
      <div>
        <el-button type="upload" plain size="large">选择附件</el-button>
      </div>
    </template>
  </el-upload>
</template>

<script>
import Cache from '@/utils/cache'
import { attachmentDelete, applyPayDelete } from '@/api/attachment'

export default {
  name: 'uploadAttachment',
  data() {
    return {
      actionUrl: process.env.VUE_APP_CENTER,
      uploadHeaders: { Authorization: Cache.get('SYS_TOKEN') }
    }
  },
  props: {
    listType: {
      type: String,
      default: 'picture-card'
    },
    source: {
      type: String,
      default: ''
    },
    accept: {
      type: String,
      default: ''
    },
    fileList: {
      type: Array,
      default() {
        return []
      }
    },
    uploadData: {
      type: Object,
      default() {
        return {}
      }
    },
    isShowFile: {
      type: Boolean,
      default: false
    },
    url: {
      type: String,
      default: '/cwe/a/attachment/upload'
    },
    limitNum: {
      type: [String, Number],
      default: 3
    },
    size: {
      type: [String, Number],
      default: 0
    }
  },

  methods: {
    // 文件上传之前的钩子
    beforeUploadFile(file) {
      return true
      let FileExt = file.name.replace(/.+\./, '')
      if (this.source === 'ContractBuy') {
        if (['jpg', 'png', 'jpeg'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({ type: 'warning', message: '请上传后缀名为jpg、jpeg、png的图片！' })
          return false
        }
      } else if (this.source === 'coalSample') {
        if (['xls', 'xlsx', 'docx', 'jpg', 'png', 'jpeg', 'pdf'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({ type: 'warning', message: '请上传后缀名为xls、xlsx、docx、pdf、jpg、jpeg、png的文件！' })
          return false
        }
      }
    },
    // 文件超出个数时的钩子
    exceedFile(files, fileList) {
      this.$message.warning(`只能上传 ${this.size} 个文件`)
      // this.$message.warning(`只能选择 ${this.limitNum} 个文件，当前共选择了 ${this.size + fileList.length}个`)
    },
    // 文件上传成功的钩子
    uploadSuccess(response, file, fileList) {
      this.$emit('successNotify', response)
    },
    // 删除
    async attachmentDelete({ id, index }) {
      try {
        await this.$confirm('此操作将永久删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        const status = await attachmentDelete(id)
        if (status) {
          this.$message({ type: 'success', message: '删除成功!' })
        } else {
          this.$message({ type: 'error', message: '删除失败!' })
        }
        this.$emit('deleteNotify', { status, index })
      } catch (error) {
        this.$message({ type: 'info', message: '已取消删除' })
      }
    },

    async applyPayDelete({ id, index }) {
      try {
        await this.$confirm('此操作将永久删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        const status = await applyPayDelete(id)
        if (status) {
          this.$message({ type: 'success', message: '删除成功!' })
        } else {
          this.$message({ type: 'error', message: '删除失败!' })
        }
        this.$emit('deleteNotify', { status, index })
      } catch (error) {
        this.$message({ type: 'info', message: '已取消删除' })
      }
    }
  },
  watch: {
    url: {
      handler(value) {
        this.actionUrl += value
      },
      immediate: true
    },
    limitNum(v) {
    }
  }
}
</script>

<style scoped lang="scss">
.uploadAttachment {
  text-align: right;
}

.upload {
  background: #f4f4f4;
  border-radius: 8px;
  padding: 19px;
  // width: 80px;
  display: flex;
  align-content: center;
  justify-content: center;

  .upload-img {
    width: 40px;
    width: 40px;
  }
}

.el-button--upload:focus,
.el-button--upload:hover {
  background: #f4f4f4 !important;
  border-color: #fff !important;
  color: #867b7b !important;
}

.el-button--upload {
  transition: all 0.5s;
  background: #f4f4f4;
  border-color: #fff;
  color: #a5a28a;
  padding: 9px 15px;
  font-size: 12px;
}
</style>
