<template>
  <el-drawer :before-close="close" :visible.sync="visible" direction="rtl" title="附件列表">
    <el-tabs style="margin: 10px" active-name="煤岩报告列表">
      <el-tab-pane name="煤岩报告列表" label="煤岩报告列表">
        <template v-if="rockList.length">
          <div class="files-warp" style="cursor: pointer">
            <div v-for="(item,index) in rockList" :key="index" class="file-item" @click="handleFileClick(item)">
              <el-image v-if="isImage(item.uri)" :src="item.uri" fit="cover" class="file-thumbnail"
                        :preview-src-list="isImage(item.uri) ? [item.uri] : []">
                <div slot="error" class="image-error">
                  <i class="el-icon-picture"></i>
                </div>
              </el-image>
              <i v-else :class="getFileIcon(item.uri)"></i>
              <span class="file-name">{{ item.display }}</span>
            </div>
          </div>
        </template>
        <template v-else>
          <el-empty :image="emptyImgPath" :image-size="200" description="暂无图片" />
        </template>
      </el-tab-pane>
      <el-tab-pane name="CSR报告列表" label="CSR报告列表">
        <template v-if="csrList.length">
          <div class="files-warp">
            <div v-for="(item,index) in csrList" :key="index" class="file-item" @click="handleFileClick(item)">
              <el-image v-if="isImage(item.uri)" :src="item.uri" fit="cover" class="file-thumbnail"
                        :preview-src-list="isImage(item.uri) ? [item.uri] : []">
                <div slot="error" class="image-error">
                  <i class="el-icon-picture"></i>
                </div>
              </el-image>
              <i v-else :class="getFileIcon(item.uri)"></i>
              <span class="file-name">{{ item.display }}</span>
            </div>
          </div>
        </template>
        <template v-else>
          <el-empty :image="emptyImgPath" :image-size="200" description="暂无图片" />
        </template>
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>
<script>
export default {
  name: 'ViewAttachment',
  props: {
    requestMethod: {
      type: Function
    }
  },
  data() {
    return {
      emptyImgPath: require(`@/assets/empty_img.jpg`),
      visible: false,
      rockPreview: [],
      csrPreview: [],
      rockList: [],
      csrList: [],
      imagePreviewList: [],  //存储图片
    }
  },
  methods: {
    close() {
      this.list = []
      this.preview = []
      this.rockList = []
      this.csrList = []
      this.visible = false
    },
    async open(row) {
      const res = await this.requestMethod(row.id)
      if (res) {
        this.rockList = res.data.attachmentList
        this.csrList = res.data.attachmentCsrList
        this.rockPreview = this.rockList.map((item) => item.uri)
        this.csrPreview = this.csrList.map((item) => item.uri)
        // 预处理图片预览列表
        this.imagePreviewList = [
          ...this.rockList.filter(item => this.isImage(item.uri)),
          ...this.csrList.filter(item => this.isImage(item.uri))
        ].map(item => item.uri)
        this.visible = true
      }
    },
    getFileIcon(fileUrl) {
      const extension = fileUrl.split('.').pop().toLowerCase()
      switch (extension) {
        case 'pdf':
          return 'el-icon-document'
        case 'doc':
        case 'docx':
          return 'el-icon-document'
        case 'xls':
        case 'xlsx':
          return 'el-icon-document'
        case 'ppt':
        case 'pptx':
          return 'el-icon-document'
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
          return 'el-icon-picture'
        default:
          return 'el-icon-document'
      }
    },
    isImage(fileUrl) {
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif']
      const extension = fileUrl.split('.').pop().toLowerCase()
      return imageExtensions.includes(extension)
    },
    handleFileClick(file) {
      if (!this.isImage(file.uri)) {
        this.downloadFile(file)
      }
    },
    downloadFile(file) {
      const link = document.createElement('a')
      link.href = file.uri
      link.download = file.display || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>
<style scoped lang="scss">
:deep(.el-drawer__header) {
  margin-bottom: 10px;
}

// .images-warp {
//   > .img {
//     margin: 10px 0;
//     border: 1px solid #c0ccda;
//     border-radius: 4px;
//     overflow: hidden;

//     .img {
//       float: left;
//       width: 100px;
//       height: 100px;
//       padding: 5px;
//     }

//     .img-title {
//       float: left;
//       font-size: 14px;
//       margin: auto;
//       text-align: center;
//       line-height: 100px;
//     }
//   }
// }

.files-warp {
  .file-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: #f5f7fa;
    }

    .file-thumbnail {
      width: 40px;
      height: 40px;
      margin-right: 10px;
      border-radius: 4px;
      overflow: hidden;

      .image-error {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f7fa;

        i {
          font-size: 20px;
          color: #c0c4cc;
        }
      }
    }

    i {
      margin-right: 10px;
      font-size: 20px;
      color: #606266;
      min-width: 40px;
      text-align: center;
    }

    .file-name {
      font-size: 14px;
      color: #606266;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
