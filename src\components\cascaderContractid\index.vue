<template>
  <!-- <el-autocomplete class="inline-input" v-model="val" :fetch-suggestions="gainRemoteData" placeholder="" clearable
                   :disabled="disabled" @input="handleInputChange" @blur="handleChange">
  </el-autocomplete> -->

  <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
               :disabled="disabled" @input="handleInputChange" @change="handleChange" filterable clearable placeholder="请选择合同">
  </el-cascader>
</template>
<script>
import { getCustomerContractNotSettle } from '@/api/quality'
export default {
  name: 'CategorySelect',
  data() {
    return {
      val: '', // 映射值
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      list: []
    }
  },
  props: {
    value: {
      required: true
    },
    ext: {
      type: Object
    },
    disabled: {
      type: [<PERSON>ole<PERSON>],
      default: false
    }
  },
  async created() {
    const { data } = await getCustomerContractNotSettle()
    // 供应商名称返回id用来查询
    this.contractEntity.options = this.formatContract({
      data,
      key: { customerName: 'displayName', customerId: 'id', contractSellList: 'contractSellList' }
    })
  },
  methods: {
    /**
     * @用于格式化合约数据因为合约的子customerId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    handleInputChange(val) {
      console.log(val)
      this.$emit('update:value', val[1])
    },
    handleChange(v) {
      this.$emit('blur', v[1])
    }
  },
  watch: {
    value: {
      handler(v) {
        this.contractEntity.active = v
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss" scoped>
.inline-input {
  width: 100%;
}
</style>
