<template>
  <div>
    <!-- <el-dialog ref="exportExcel" title="Excel导入" :visible.sync="visible" :before-close="closeExportExcelDialog" width="1060px"> -->
    <div style="padding: 5px;" v-loading="uploading.state">
      <EditTable ref="editTable" :beforePaste="beforePaste" @inited="handleEditTableInit" :disabled="!visible"
                 :nestedHeaders="nestedHeaders" :columns="getColumns" v-model="editData"></EditTable>
      <template>
        <template v-if="isCreateStatus">
          <el-button type="add-row" @click="handleRowInsert">插 入 行</el-button>
          <el-divider direction="vertical"></el-divider>
          <el-button type="add-row" @click="handleReducer('clear')">清空上次内容</el-button>
        </template>
      </template>
    </div>
    <div style="float:right" v-if="isshowbtn">
      <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" @click="okbtn()">上传
      </el-button>
      <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" @click="closeExportExcelDialog()">取消
      </el-button>
    </div>
    <!-- </el-dialog> -->
  </div>
</template>

<script>
import { FetchData, deepClone } from '@/utils'
import { getDay } from '@/utils/dateUtils'
import Cache from '@/plugins/cache'
import productModel from '@/model/product/productList'
// import Model from '@/model/invoicing/weightHouselogtable'
// import Func from '../../utils/func'
const initState = () => {
  return {
    dataDate: getDay(),
  }
}
const initEditData = () => {
  return [
    ...Array(10)
      .fill(null)
      .map(() => ({})),
  ]
}

const getExcludeDirtyList = (target = []) => {
  const list = deepClone(target)
  list.forEach((v) => {
    for (const key in v) {
      if ([null, ''].includes(v[key])) {
        delete v[key]
      }
    }
  })
  // console.log(list)
  return list
}

export default {
  name: 'ExportExcel',
  data() {
    return {
      licenseKey: '4d522-5237a-55f42-6653a-d1494',
      settings: {
        data: [],
        className: 'htCenter htMiddle',
        width: 850,
        height: 550,
        stretchH: 'all',
        colHeaders: [],
        columns: [],
        rowHeaders: true,
        manualColumnResize: true,
        manualRowResize: true,
        currentRowClassName: 'currentRow',
        currentColClassName: 'currentCol',
        autoWrapRow: true,
        autoRowSize: { syncLimit: 300 },
        manualRowMove: true,
        manualColumnMove: false,
        contextMenu: {
          items: {
            row_above: { name: '向上插入一行' },
            row_below: { name: '向下插入一行' },
            remove_row: { name: '删除行' },
          },
        },
      },
      uploading: {
        state: false,
      },
      parserLoading: false,
      isExistsFile: false,
      visible: false,
      editData: initEditData(),
      form: {},
      defaultInfo: {},
    }
  },
  props: {
    dynamicMaxLength: {
      // 动态最大长度
      type: [Number],
      default: 20,
    },
    tableOption: {
      // 数据实体
      type: [Object],
      required: true,
    },
    traitSettings: {
      type: [Object],
      required: true,
      default: () => ({
        colHeaders: [],
        columns: [],
      }),
    },
    // traitSettings: {
    //   type: [Object],
    //   required: true,
    //   default: () => ({
    //     colHeaders: [],
    //     columns: [],
    //   }),
    // },
    // exportLabelMap: {
    //   type: [Object],
    //   required: true,
    // },
    exportExcelDialogVisible: {
      type: [Boolean],
      default: false,
      required: true,
    },
    optName: {
      type: String,
      require: true,
    },
    isshowbtn: {
      type: [Boolean],
      default: false,
    },
    type: {
      type: String,
    },
    nestedHeaders: {
      type: Array,
      default: () => [],
    },
    responsiblelist: {
      type: Array,
      default: () => [],
    },
    contractlist: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    getColumns() {
      return this.isCreateStatus
        ? this.traitSettings.columns
        : [
            {
              label: '数据日期',
              width: 120,
              data: 'dataDate',
              type: 'date',
              dateFormat: 'YYYY-MM-DD HH:mm:ss',
            },
            ...this.traitSettings.columns,
          ]
    },
    title() {
      return optMap[this.optName]
    },
    isViewStatus() {
      return this.optName === 'view'
    },
    isUpdateStatus() {
      return this.optName === 'update'
    },

    isCreateStatus() {
      return this.optName === 'create'
    },
    getUniqueFormKey() {
      // console.log(this.$route.fullPath + 'FormModal' + this.optName)
      return this.$route.fullPath + 'FormModal' + this.optName
    },
    listType() {
      return window.dict[this.type] || []
    },
  },
  created() {
    // console.log(this.optName)
  },
  watch: {
    tableOption: {
      async handler(val) {
        await this.$nextTick()
        if (this.isCreateStatus) {
          this.setInfo()
        } else {
          const { editData, ...rest } = val
          this.editData = editData
          this.form = { ...rest }
        }
        this.setDefaultInfo()
      },
      immediate: true,
    },
    exportExcelDialogVisible: {
      handler(val) {
        this.visible = val
      },
      immediate: true,
    },
  },

  methods: {
    // 获取品名接口
    // async getName() {
    //   try {
    //     let { data } = await productModel.page({ ...this.listQuery, size: 999 })
    //     this.nameEntity.options = data.records.map((item) => {
    //       return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
    //     })
    //   } catch (error) {}
    // },
    async okbtn() {
      const status = await this.$refs.editTable.validate()
      if (!status) return
      let importList = ''
      const data = this.getFormatEditData(this.editData)
      // 创建提交时如果走了草稿数据，需要清除id  这里需要把时间格式化一下
      if (this.isCreateStatus) {
        importList = {
          ...this.form,
          id: undefined,
          data: data.map((v) => ({ ...v, dataDate: this.form.dataDate, id: undefined })),
        }
      }
      const fList = importList.data.map((v) => {
        const item = {}
        this.traitSettings.columns.forEach((e, index) => {
          // console.log(e)
          if (!e.noExport) {
            // if (e.rules) {
            //   console.log(v[index])
            //   if (v[index] == undefined) {
            //     that.$message.error(`这是必填项目！不能为空`)
            //     return
            //   }
            // }
            item[e.prop] = v[index]
          }
        })
        return item
      })

      // fList.forEach((e, index) => {
      //   console.log(e)
      //   if (e.firstWeightDate) {
      //     if (e.firstWeightDate.indexOf(' ') == -1) {
      //       console.log(e.firstWeightDate)
      //       var timearr = e.firstWeightDate.replace('', ':').replace(/\:/g, '-').split('-')
      //       console.log(timearr)
      //       var nian = timearr[1] - 1
      //       var yue = timearr[2]
      //       var ri = timearr[3].substring(0, 2)
      //       var shi = timearr[3].slice(2)
      //       var fen = timearr[4]
      //       var miao = timearr[5]
      //       let date = nian + '-' + yue + '-' + ri
      //       let time = shi + ':' + fen + ':' + miao
      //       let datetime = date + ' ' + time
      //       e.firstWeightDate = datetime
      //     }
      //   }
      //   if (e.secondWeightDate) {
      //     if (e.secondWeightDate.indexOf(' ') == -1) {
      //       console.log(e.secondWeightDate)
      //       var timearr = e.secondWeightDate.replace('', ':').replace(/\:/g, '-').split('-')
      //       console.log(timearr)
      //       var nian = timearr[1] - 1
      //       var yue = timearr[2]
      //       var ri = timearr[3].substring(0, 2)
      //       var shi = timearr[3].slice(2)
      //       var fen = timearr[4]
      //       var miao = timearr[5]
      //       let date = nian + '-' + yue + '-' + ri
      //       let time = shi + ':' + fen + ':' + miao
      //       let datetime = date + ' ' + time
      //       e.secondWeightDate = datetime
      //     }
      //   }
      //   if (e.loadDate) {
      //     if (e.loadDate.indexOf(' ') == -1) {
      //       console.log(e.loadDate)
      //       var timearr = e.loadDate.replace('', ':').replace(/\:/g, '-').split('-')
      //       console.log(timearr)
      //       var nian = timearr[1] - 1
      //       var yue = timearr[2]
      //       var ri = timearr[3].substring(0, 2)
      //       var shi = timearr[3].slice(2)
      //       var fen = timearr[4]
      //       var miao = timearr[5]
      //       let date = nian + '-' + yue + '-' + ri
      //       let time = shi + ':' + fen + ':' + miao
      //       let datetime = date + ' ' + time
      //       e.loadDate = datetime
      //     }
      //   }
      // })

      // console.log(fList)
      this.$emit('setSubmittext', fList)

      // console.log(fList)
      // const text = JSON.stringify(fList)
      // console.log(text)
      // this.$emit('setSubmittext', text)

      // const res = await Model.saveList({ text })
      // if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      // if (res.data.length) {
      //   // scope.settings.data = res.data.map((v, i) => v)
      // } else {
      //   this.$message({ showClose: true, message: '导入成功', type: 'success' })
      //   this.$emit('update:exportExcelDialogVisible', false)
      //   this.$emit('okbtn')
      //   this.$emit('getList')
      //   this.setSubmitData()
      // }
    },
    setSubmitData() {
      // 只有在创建状态下才会缓存数据
      if (!this.isCreateStatus) return
      const { dataDate } = this.form
      Cache.session.setJSON(this.getUniqueFormKey, {
        form: {
          dataDate,
        },
        editData: initEditData(),
      })
    },
    /**
     * 过滤掉空数据，校验数据
     * @param target 提供的车牌列表
     * @param isSubmit 是否提交 主要用于控制是否校验车牌号 true时抛出Error
     */
    getFormatEditData(target = []) {
      const list = getExcludeDirtyList(target)
      const data = []

      // 获取需要校验的字段
      const validateList = this.traitSettings.columns
        .filter((col) => col.formRequire)
        .map((v) => {
          return {
            key: v.data,
            msg: v.title,
          }
        })

      list.forEach((item, index) => {
        // 过滤掉空数据 但在编辑的时候会保留id所以不能被删除只能修改当前这条数据
        if (Object.keys(item).length) {
          validateList.forEach((v) => {
            if (!item[v.key]) {
              this.$message.error(`表格第${index + 1}行${v.msg}不能为空`)
              throw new Error('数据不能为空')
            }
          })
          if (!item.dataDate && !this.isCreateStatus) {
            this.$message.error(`表格第${index + 1}行数据日期不能为空`)
            throw new Error('数据不能为空')
          }
          data.push(item)
        }
      })
      if (!data.length) {
        this.$message.error('表格至少添加一条数据。')
        throw new Error('数据不能为空')
      }
      // 格式化数据
      data.forEach((item, index) => {
        item.timeSlot = item.timeSlot ? item.timeSlot : ''
      })
      return data
    },

    setInfo() {
      const { form, editData } = this.getUnSubmitData()
      // const { form, editData } = {
      //   form: initState(),
      //   editData: initEditData(),
      //   isNoCache: true,
      // }
      this.form = { ...form }
      this.editData = editData
    },
    getUnSubmitData() {
      return (
        Cache.session.getJSON(this.getUniqueFormKey) || {
          form: initState(),
          editData: initEditData(),
          isNoCache: true,
        }
      )
    },
    beforePaste(data, coords) {
      const startCol = coords[0]?.startCol
      // 如果时车牌号的列,合并特殊处理
      if (startCol === 0) {
        const newData = []
        let i = null
        for (const [index, item] of Object.entries(data)) {
          const v = item.filter((val) => val !== null)
          if (v.length > 1) {
            i = newData.push(item)
          } else {
            const lastVal = newData[i - 1]
            if (lastVal === undefined) continue
            lastVal[0] = lastVal[0] + (item[0] ?? '') // 合并 item0为null时往后追加''
            item[0] = null
          }
        }
        // 删除逆向循环
        for (let i = data.length - 1; i >= 0; i--) {
          const status = data[i].every((val) => val === null)
          if (status) {
            data.splice(i, 1)
          }
        }
      }
      return data
    },
    async handleEditTableInit(instance) {
      //  const source = await FetchData(listAnalysisNumberplate, false, [])
      let source = []
      let source2 = []
      let source3 = []
      if (this.type == 'productName') {
        let { data } = await productModel.page({ size: 999 })
        source = data.records.map((item) => {
          return item.name
        })
      } else {
        if (this.listType) {
          let Array = []
          this.listType.forEach((item, index) => {
            Array.push(item.name)
          })
          source = Array
        } else {
          source = ['销售', '采购']
        }
      }
      if (this.responsiblelist.length > 0) {
        source2 = this.responsiblelist
      }
      if (this.contractlist.length > 0) {
        // console.log('销售合同列表')
        source3 = this.contractlist
      }

      const getSetting = () => {
        if (this.isCreateStatus) {
          return {
            contextMenu: {
              items: {
                row_above: { name: '向上插入一行' },
                row_below: { name: '向下插入一行' },
                remove_row: { name: '删除行' },
                clear_custom: {
                  name: '清空所有单元格数据',
                  callback() {
                    this.clear()
                  },
                },
              },
            },
            data: this.editData,
          }
        }
        return {
          height: '350',
          contextMenu: undefined,
          data: this.editData,
        }
      }
      const typeColIndex = this.getColumns.findIndex((v) => v.prop === 'type')
      const nameColIndex = this.getColumns.findIndex((v) => v.prop === 'productName')
      const responsibleUserNameIndex = this.getColumns.findIndex((v) => v.prop === 'responsibleUserName')
      const contractCodeIndex = this.getColumns.findIndex((v) => v.prop === 'contractCode')
      instance.updateSettings({
        ...getSetting(),
        async cells(row, col) {
          if (col == typeColIndex || col == nameColIndex) {
            this.source = source
          }
          if (col == responsibleUserNameIndex) {
            this.source = source2
          }
          if (col == contractCodeIndex) {
            this.source = source3
          }
        },
      })
    },
    handleReducer(type, payload = {}) {
      switch (type) {
        case 'clear':
          const { isNoCache } = this.getUnSubmitData()
          if (isNoCache) {
            this.$message({
              message: '当前没有草稿无需清理。',
              type: 'warning',
            })
            this.$message(`当前没有草稿无需清理。`)
            return
          }
          Cache.session.remove(this.getUniqueFormKey)
          this.setInfo()
          this.setDefaultInfo()
          this.$message({
            message: '清除成功。',
            type: 'warning',
          })
          this.$refs.editTable.getHotInstance().loadData(this.editData)
          break
      }
    },
    setDefaultInfo() {
      this.defaultInfo = {
        form: deepClone(this.form),
        editData: deepClone(this.editData),
      }
    },
    handleRowInsert() {
      try {
        this.$refs.editTable.addLastRow()
      } catch (error) {
        this.$message({
          message: '插入行失败',
          type: 'error',
        })
        console.error('error')
      }
    },

    /**
     *关闭Excel导入对话框
     */
    async closeExportExcelDialog() {
      this.closeDialog()
    },
    async closeDialog() {
      return !this.isCreateStatus ? false : await this.setUnSubmitData()
    },
    setUnSubmitData() {
      const validateFormDateIsChange = () => {
        const info = {
          form: this.form,
          editData: getExcludeDirtyList(this.editData),
        }
        const defaultInfo = {
          form: this.defaultInfo.form,
          editData: getExcludeDirtyList(this.defaultInfo.editData),
        }
        const infoStr = JSON.stringify(info)
        const defaultInfoStr = JSON.stringify(defaultInfo)

        if (infoStr !== defaultInfoStr) {
          // console.log('不同')
          return [false, infoStr]
        }
        // console.log('返回')
        return [true]
      }
      // this.$emit('update:exportExcelDialogVisible', false)

      return new Promise((resolve) => {
        const [isNoEditStatus, infoStr] = validateFormDateIsChange()
        // 当前表单数据没有修改 则直接返回
        if (isNoEditStatus) {
          this.$emit('update:exportExcelDialogVisible', false)
          resolve(false)
          return
        }
        this.$confirm('检测到未保存的内容，是否在离开页面前保存修改？', '确认信息', {
          distinguishCancelAndClose: true,
          confirmButtonText: '保存修改',
          cancelButtonText: '放弃修改',
        })
          .then(() => {
            Cache.session.set(this.getUniqueFormKey, infoStr)
            this.$message({ type: 'success', message: `已保存修改`, showClose: true })
            this.$emit('update:exportExcelDialogVisible', false)
            resolve(false)
          })
          .catch((action) => {
            const isCancel = action === 'cancel'
            this.$emit('update:exportExcelDialogVisible', false)
            if (isCancel) {
              Cache.session.remove(this.getUniqueFormKey)
            }
            this.$message({
              type: 'info',
              showClose: true,
              message: isCancel ? '放弃保存并离开页面' : '停留在当前页面',
            })
            resolve(!isCancel)
          })
      })
    },
  },
}
</script>
<style lang="scss" scoped>
</style>
