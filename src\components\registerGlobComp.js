import Vue from 'vue'

function _registerGlobComp(Vue) {
  const compList = []
  const components = require.context('@/components/Common', true, /index\.(js|vue)$/)
  components.keys().map((src) => {
    const res = src.match(/\w+/)
    const name = res[0]
    const shallowDir = res.input.match(/\//g).length <= 2
    // res 是数组为什么可以.取到值？ 原型上也没有方法
    if (!compList.includes(name) && shallowDir) {
      compList.push(name)
      const comp = components(src).default
      Vue.component(name, comp)
    } else {
    }
  })
}

function _reqisterComponents(Vue) {
  /**
   * 注册components下的组件
   * 名称为目录名称
   * 注意:components目录下只能有一个index.vue 或者 index.js 否则组件不能用
   */
  const components = require.context('@/components/', true, /index\.(js|vue)$/)
  components.keys().map((key) => {
    const name = key.match(/\w+/)[0]
    const component = process.env === 'production' ? components(key) : components(key).default
    Vue.component(name, component)
  })
}

export default function registerGlobComps(Vue) {
  _registerGlobComp(Vue)
  _reqisterComponents(Vue)
}
