export const pagination = {
    pageSizes: [50, 100, 200, 500],
    pageSize: 50,
    pageCount: 5,
    layout: 'total,pager,next,sizes,jumper,slot'
}

export const minPagination = {
    pageSizes: [50, 100, 300],
    pageSize: 50,
    pageCount: 5,
    layout: 'total,sizes, prev, pager, next'
}

export const fetchSetting = {
    // 传给后台的当前页字段
    pageField: 'current',
    // 传给后台的每页显示多少条的字段
    sizeField: 'size',
    // 接口返回表格数据的字段
    listField: 'records',
    // 接口返回表格总数的字段
    totalField: 'total',
    // 排序名称字段
    sortField: 'orderBy',
    // 排序顺序字段
    sortOrder: 'orderDir'
}

export const listQuery = {
    orderBy: 'createDate',
    orderDir: 'desc'
}

export const typeName = {
    YM: '原煤',
    BCPJM: '半成品精煤',
    CPJM: '成品精煤',
    ZM: '中煤',
    GS: '矸石'
}

export const COMPANY = () => {
    try {
        return window.dict.company_info[0].value
    } catch (error) {
        return ''
    }
}

/**
 * 库存管理
 */
export const stockFilterOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'name',
            filter: 'filter_LIKES_name',
            component: 'select-name',
            props: {placeholder: '请选择品名', changeFilter: true, clearable: true}
        }
    ]
}
// 库存日报
export const itemStockDayFilterOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'itemName',
            filter: 'filter_LIKES_itemName',
            component: 'select-itemName',
            props: {placeholder: '请选择物资名称'}
        },
        {
            prop: 'categoryName',
            filter: 'filter_LIKES_categoryName',
            component: 'select-categoryName',
            props: {placeholder: '请选择物资分类'}
        },
        {
            prop: 'date',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '请选择刷新日期',
                clearable: false
            }
        }
    ]
}
// 库存
export const inventoryFilterOption = {
    showMore: true,
    columns: [
        {
            prop: 'itemName',
            filter: 'filter_LIKES_itemName',
            component: 'select-itemName',
            props: {placeholder: '请选择物资名称'}
        },
        {
            prop: 'categoryName',
            filter: 'filter_LIKES_categoryName',
            component: 'select-categoryName',
            props: {placeholder: '请选择物资分类'}
        }
        // {
        //   prop: 'date',
        //   component: 'date-select',
        //   filter: 'filter_GES_date',
        //   props: {
        //     placeholder: '请选择入库日期',
        //     clearable: false,
        //   },
        // },
    ]
}
/**
 * 煤质量管理
 */
export const qualityFilterOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'name',
            filter: 'filter_LIKES_name',
            component: 'select-name',
            props: {placeholder: '请选择品名', changeFilter: true, clearable: true}
        }
    ]
}
//生产化验
export const qualProductionOption = {
    showMore: true,
    columns: [
        // {
        //   prop: 'date',
        //   component: 'date-select',
        //   filter: 'filter_EQS_date',
        //   props: {
        //     placeholder: '日期',
        //     clearable: false,
        //   },
        // },
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'productName',
            filter: 'filter_LIKES_productName',
            component: 'select-ProductName',
            props: {placeholder: '请选择品名', clearable: true, selecttype: 'productName'}
        },
        {
            prop: 'customerName',
            filter: 'filter_LIKES_customerName',
            props: {
                placeholder: '客户名称',
                clearable: true
            }
        }
    ]
}


// 附产品化验
export const AttachedProductOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'name',
            filter: 'filter_LIKES_name',
            props: {
                placeholder: '名称',
                clearable: true
            }
        },
        {
            prop: 'type',
            filter: 'filter_EQS_type',
            component: 'dict-select',
            props: {
                type: 'b_qual_attach_product_type',
                placeholder: '请选择类型',
                clearable: true
            }
        }

    ]
}

// 配煤生产化验、洗煤生产化验
export const coalblendingproduceOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'productName',
            filter: 'productName',
            component: 'select-ProductName',
            props: {placeholder: '请选择品名', clearable: true, selecttype: 'productName'}
        }
        // {
        //   prop: 'supplierName',
        //   filter: 'supplierName',
        //   component: 'select-ProductName',
        //   props: { placeholder: '请选择品名', clearable: true, selecttype: 'productName' },
        // },
    ]
}


// 过程化验
export const qualProcessOption = {
    showMore: true,
    columns: [
        // {
        //   prop: 'date',
        //   component: 'date-select',
        //   filter: 'filter_EQS_date',
        //   props: {
        //     placeholder: '日期',
        //     clearable: false,
        //   },
        // },
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'productName',
            filter: 'filter_LIKES_productName',
            component: 'select-ProductName',
            props: {placeholder: '请选择品名', clearable: true, selecttype: 'productName'}

        }
    ]
}

//
export const weightHouselogtableOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_weightDate',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_weightDate',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'companyFullName',
            filter: 'filter_LIKES_companyFullName',
            props: {
                placeholder: '企业名称',
                clearable: true
            }
        },
        {
            prop: 'plateNumber',
            filter: 'filter_LIKES_plateNumber',
            props: {
                placeholder: '车牌号',
                clearable: true
            }
        },
        {
            prop: 'type',
            filter: 'filter_LIKES_type',
            component: 'select-name',
            props: {placeholder: '请选择过磅类型', changeFilter: true, clearable: true}
        }
    ]
}

export const qualInOrOutFilterOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        // {
        //   prop: 'supplierName',
        //   filter: 'filter_LIKES_supplierName',
        //   props: {
        //     placeholder: '供应商',
        //     clearable: true,
        //   },
        // },
        // {
        //   prop: 'supplierName',
        //   filter: 'filter_LIKES_supplierName',
        //   component: 'select-Sender-Name',
        //   props: { placeholder: '请选择供应商', changeFilter: true, isNogetlist: true, clearable: true, type: 'supplierName' },
        // },


        // {
        //   prop: 'receiverName',
        //   filter: 'filter_LIKES_receiverName',
        //   props: {
        //     placeholder: '收货单位',
        //     clearable: true,
        //   },
        // },
        // {
        //   prop: 'name',
        //   filter: 'filter_LIKES_productName',
        //   component: 'select-ProductName',
        //   props: { placeholder: '请选择品名', changeFilter: true, clearable: true, selecttype: 'productName' },
        // },
        {
            prop: 'customerName',
            filter: 'filter_LIKES_customerName',
            component: 'select-Sender-Name',
            props: {
                placeholder: '请选择客户',
                changeFilter: true,
                isNogetlist: true,
                clearable: true,
                type: 'customerName'
            }
        }
    ]
}


export const qualBuyInFilterOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'name',
            filter: 'filter_LIKES_productName',
            component: 'select-ProductName',
            props: {placeholder: '请选择品名', changeFilter: true, clearable: true, selecttype: 'productName'}
        }
        // {
        //   prop: 'customerName',
        //   filter: 'filter_LIKES_customerName',
        //   component: 'select-Sender-Name',
        //   props: { placeholder: '请选择客户', changeFilter: true, isNogetlist: true, clearable: true, type: 'customerName' },
        // },
    ]
}


export const rawcoalqualBuyInFilterOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        // {
        //   prop: 'receiverName',
        //   filter: 'filter_LIKES_receiverName',
        //   props: {
        //     placeholder: '收货单位',
        //     clearable: true,
        //   },
        // },
        {
            prop: 'name',
            filter: 'filter_LIKES_productName',
            component: 'select-ProductName',
            props: {placeholder: '请选择品名', changeFilter: true, clearable: true, selecttype: 'productName'}
        }

    ]
}


export const qualSellOutOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        // {
        //   prop: 'customerName',
        //   filter: 'filter_LIKES_customerName',
        //   component: 'select-Sender-Name',
        //   props: { placeholder: '请选择客户', changeFilter: true, isNogetlist: true, clearable: true, type: 'customerName' },
        // },
        {
            prop: 'productName',
            filter: 'filter_LIKES_productName',
            component: 'select-ProductName',
            props: {placeholder: '请选择品名', changeFilter: true, clearable: true, selecttype: 'productName'}
        }
    ]
}

export const qualOuterOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        // {
        //   prop: 'name',
        //   filter: 'filter_LIKES_name',
        //   props: {
        //     placeholder: '请输入小样名称',
        //     clearable: true,
        //   },
        // },
        {
            prop: 'name',
            filter: 'filter_LIKES_name',
            component: 'select-name',
            props: {
                placeholder: '请输入小样名称',
                clearable: true,
                changeFilter: true
            }
        }
    ]
}


// coalSample
export const coalSampleOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_sampleDate',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_sampleDate',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'type',
            filter: 'filter_EQS_type',
            component: 'dict-select',
            props: {
                type: 'coal_type',
                placeholder: '请选择煤种',
                clearable: true
            }
        },

        // {
        //     prop: 'name',
        //     filter: 'filter_LIKES_name',
        //     component: 'select-name',
        //     props: {
        //         placeholder: '请选择品名',
        //         clearable: true
        //     }
        // }
        {
            prop: 'name',
            filter: 'filter_LIKES_name',
            props: {
                placeholder: '请输入小样名称',
                clearable: true
            }
        }

    ]
}

//  付款订单
export const applyPayOption = {
    showMore: true,
    columns: [
        {
            prop: 'applyDate',
            component: 'date-select',
            filter: 'filter_EQS_applyDate',
            props: {
                placeholder: '日期',
                clearable: false
            }
        },
        {
            prop: 'supplierName',
            filter: 'filter_LIKES_supplierName',
            props: {
                placeholder: '请输入发货单位',
                clearable: true
            }
        },
        {
            prop: 'name',
            filter: 'filter_LIKES_productName',
            component: 'select-name',
            props: {placeholder: '请选择品名', clearable: true, changeFilter: true, selecttype: 'productName'}
        }
    ]
}
//收款单
export const applyRecvOption = {
    showMore: true,
    columns: [
        {
            prop: 'applyDate',
            component: 'date-select',
            filter: 'filter_EQS_applyDate',
            props: {
                placeholder: '日期',
                clearable: false
            }
        },
        // {
        //   prop: 'customerName',
        //   filter: 'filter_LIKES_customerName',
        //   props: {
        //     placeholder: '请输入发货单位',
        //     clearable: true,
        //   },
        // },
        {
            prop: 'customerName',
            filter: 'filter_LIKES_customerName',
            component: 'select-Sender-Name',
            props: {
                placeholder: '请输入往来单位',
                changeFilter: true,
                isNogetlist: true,
                clearable: true,
                type: 'customerName'
            }
        },

        {
            prop: 'name',
            filter: 'filter_LIKES_productName',
            component: 'select-name',
            props: {placeholder: '请选择品名', clearable: true, changeFilter: true, selecttype: 'productName'}
        },
        {
            prop: 'applyType',
            filter: 'filter_EQS_applyType',
            component: 'dict-select',
            props: {
                type: 'apply_recv_type',
                placeholder: '请选择收款类型',
                clearable: true
            }
        }

    ]
}

//  付款订单
export const paymentorderOption = {
    showMore: true,
    columns: [
        {
            prop: 'applyDate',
            component: 'date-select',
            filter: 'filter_EQS_applyDate',
            props: {
                placeholder: '日期',
                clearable: false
            }
        },
        // {
        //   prop: 'supplierName',
        //   filter: 'filter_LIKES_supplierName',
        //   props: {
        //     placeholder: '请输入发货单位',
        //     clearable: true,
        //   },
        // },
        {
            prop: 'customerName',
            filter: 'filter_LIKES_customerName',
            component: 'select-Sender-Name',
            props: {
                placeholder: '请输入往来单位',
                changeFilter: true,
                isNogetlist: true,
                clearable: true,
                type: 'customerName'
            }
        },
        {
            prop: 'name',
            filter: 'filter_LIKES_productName',
            component: 'select-name',
            props: {placeholder: '请选择品名', clearable: true, changeFilter: true, selecttype: 'productName'}
        }
    ]
}

export const detailsdailyreportOption = {
    showMore: true,
    columns: [
        {
            prop: 'applyDate',
            component: 'date-select',
            filter: 'filter_EQS_applyDate',
            props: {
                placeholder: '日期',
                clearable: false
            }
        },
        // {
        //   prop: 'supplierName',
        //   filter: 'filter_LIKES_supplierName',
        //   props: {
        //     placeholder: '请输入发货单位',
        //     clearable: true,
        //   },
        // },
        {
            prop: 'supplierName',
            filter: 'filter_LIKES_supplierName',
            component: 'select-Sender-Name',
            props: {
                placeholder: '请输入发货单位',
                changeFilter: true,
                isNogetlist: true,
                clearable: true,
                type: 'supplierName'
            }
        },

        {
            prop: 'name',
            filter: 'filter_LIKES_productName',
            component: 'select-name',
            props: {placeholder: '请选择品名', clearable: true, changeFilter: true, selecttype: 'productName'}

        },


        {
            prop: 'payWay',
            filter: 'filter_EQS_payWay',
            component: 'dict-select',
            props: {
                type: 'payWay_type',
                placeholder: '请选择支付类型',
                clearable: true
            }
        },
        {
            prop: 'applyType',
            filter: 'filter_EQS_applyType',
            component: 'dict-select',
            props: {
                type: 'apply_pay_type',
                placeholder: '请选择付款类型',
                clearable: true
            }
        }
    ]
}

//  付款结算
export const settlePayOption = {
    showMore: true,
    columns: [
        {
            prop: 'applyDate',
            component: 'date-select',
            filter: 'filter_EQS_applyDate',
            props: {
                placeholder: '日期',
                clearable: false
            }
        },
        // {
        //   prop: 'supplierName',
        //   filter: 'filter_LIKES_supplierName',
        //   props: {
        //     placeholder: '请输入结算单位',
        //     clearable: true,
        //   },
        // },
        {
            prop: 'supplierName',
            filter: 'filter_LIKES_supplierName',
            component: 'select-Sender-Name',
            props: {
                placeholder: '请输入结算单位',
                changeFilter: true,
                isNogetlist: true,
                clearable: true,
                type: 'supplierName'
            }
        },

        {
            prop: 'applyType',
            filter: 'filter_LIKES_applyType',
            component: 'select-applytype',
            props: {placeholder: '请选择结算类型', clearable: true, changeFilter: true}
        },
        // {
        //   prop: 'applyType',
        //   filter: 'filter_EQS_applyType',
        //   component: 'dict-select',
        //   props: {
        //     type: 'apply_pay_type',
        //     placeholder: '请选择付款类型',
        //     clearable: true,
        //   },
        // },


        {
            prop: 'name',
            filter: 'filter_LIKES_productName',
            component: 'select-name',
            props: {placeholder: '请选择品名', clearable: true, changeFilter: true, selecttype: 'productName'}
        }
    ]
}
//  收款结算
export const settleRecvOption = {
    showMore: true,
    columns: [
        {
            prop: 'applyDate',
            component: 'date-select',
            filter: 'filter_EQS_applyDate',
            props: {
                placeholder: '日期',
                clearable: false
            }
        },
        // {
        //   prop: 'customerName',
        //   filter: 'filter_LIKES_customerName',
        //   props: {
        //     placeholder: '请输入结算单位',
        //     clearable: true,
        //   },
        // },
        {
            prop: 'customerName',
            filter: 'filter_LIKES_customerName',
            component: 'select-Sender-Name',
            props: {
                placeholder: '请输入结算单位',
                changeFilter: true,
                isNogetlist: true,
                clearable: true,
                type: 'customerName'
            }
        },
        {
            prop: 'name',
            filter: 'filter_LIKES_productName',
            component: 'select-name',
            props: {placeholder: '请选择品名', clearable: true, changeFilter: true, selecttype: 'productName'}
        }
    ]
}

// 通知磅房
export const weightHouseNoticeOption = {
    showMore: true,
    columns: [
        {
            prop: 'beginDate',
            component: 'date-select',
            filter: 'filter_EQS_beginDate',
            props: {
                placeholder: '日期',
                clearable: false
            }
        },
        // {
        //   prop: 'supplierName',
        //   filter: 'filter_LIKES_supplierName',
        //   props: {
        //     placeholder: '请输入发货单位',
        //     clearable: true,
        //   },
        // },
        {
            prop: 'name',
            filter: 'filter_LIKES_productName',
            component: 'select-name',
            props: {placeholder: '请选择品名', clearable: true, changeFilter: true, selecttype: 'productName'}
        }
    ]
}
//  供应商每日台账
export const sbSupplierDayOption = {
    showMore: true,
    columns: [
        // {
        //     prop: 'sbDate',
        //     component: 'date-select',
        //     filter: 'filter_EQS_sbDate',
        //     props: {
        //         placeholder: '日期',
        //         clearable: false
        //     }
        // },
        // {
        //   prop: 'supplierName',
        //   filter: 'filter_LIKES_supplierName',
        //   props: {
        //     placeholder: '请输入供应商',
        //     clearable: true
        //   }
        // },
        {
            prop: 'supplierName',
            filter: 'filter_LIKES_supplierName',
            component: 'select-Sender-Name',
            props: {
                placeholder: '请选择供应商',
                changeFilter: true,
                isNogetlist: true,
                clearable: true,
                type: 'supplierName'
            }
        },

        {
            prop: 'productName',
            filter: 'filter_LIKES_productName',
            props: {
                placeholder: '请输入品名',
                clearable: true
            }
        },
        {
            prop: 'firstParty',
            filter: 'filter_LIKES_firstParty',
            props: {
                placeholder: '请输入买方',
                clearable: true
            }
        }
        // {
        //   prop: 'name',
        //   filter: 'filter_LIKES_productName',
        //   component: 'select-name',
        //   props: { placeholder: '请选择品名', clearable: true, changeFilter: true, selecttype: 'productName'  },
        // },
    ]
}

//  客户每日台账
export const sbCustomerDayOption = {
    showMore: true,
    columns: [
        // {
        //     prop: 'sbDate',
        //     component: 'date-select',
        //     filter: 'filter_EQS_sbDate',
        //     props: {
        //         placeholder: '日期',
        //         clearable: false
        //     }
        // },
        // {
        //   prop: 'customerName',
        //   filter: 'filter_LIKES_customerName',
        //   props: {
        //     placeholder: '请输入客户',
        //     clearable: true,
        //   },
        // },
        {
            prop: 'customerName',
            filter: 'filter_LIKES_customerName',
            component: 'select-Sender-Name',
            props: {
                placeholder: '请选择客户',
                changeFilter: true,
                isNogetlist: true,
                clearable: true,
                type: 'customerName'
            }
        },
        {
            prop: 'linkman',
            filter: 'filter_LIKES_linkman',
            props: {
                placeholder: '请输入负责人',
                clearable: true
            }
        }
        // {
        //   prop: 'name',
        //   filter: 'filter_LIKES_productName',
        //   component: 'select-name',
        //   props: { placeholder: '请选择品名', clearable: true,selecttype: 'productName'  },
        // },
    ]
}

export const inOrderOption = {
    showMore: true,
    columns: [
        // {
        //     prop: 'commitDate',
        //     component: 'date-select',
        //     filter: 'filter_EQS_commitDate',
        //     props: {
        //         placeholder: '日期',
        //         clearable: false
        //     }
        // },

        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_commitDate',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_commitDate',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },

        // {
        //     prop: 'categoryName',
        //     filter: 'filter_LIKES_inOrderItemList_categoryName',
        //     props: {
        //         placeholder: '物资名称',
        //         clearable: false
        //     }
        // },

        // {
        //     prop: 'commitDate',
        //     component: 'date-select',
        //     filter: 'filter_LIKES_inOrderItemList',
        //     props: {
        //         placeholder: '物资名称',
        //         clearable: false
        //     }
        // },
        {
            prop: 'ext',
            filter: 'filter_LIKES_ext',
            props: {
                placeholder: '请输入物资名称',
                clearable: true
            }
        }

        // {
        //     prop: 'itemCode',
        //     filter: 'filter_LIKES_itemCode',
        //     props: {
        //         placeholder: '请输入物资编号',
        //         clearable: true
        //     }
        // },
        // {
        //     prop: 'categoryId',
        //     filter: 'filter_EQS_categoryId',
        //     component: 'dict-select',
        //     props: {
        //         type: 'coal_type',
        //         placeholder: '请选择物资状态',
        //         clearable: true
        //     }
        // }
    ]
}

export const MaterialClassificationOption = {
    showMore: true,
    columns: [
        {
            prop: 'name',
            filter: 'filter_LIKES_name',
            props: {
                placeholder: '请输入物资分类名称',
                clearable: true
            }
        }
    ]
}

export const MaterialinformationOption = {
    showMore: true,
    columns: [
        {
            prop: 'name',
            filter: 'filter_LIKES_name',
            props: {
                placeholder: '请输入物资名称',
                clearable: true
            }
        },
        {
            prop: 'itemCode',
            filter: 'filter_LIKES_code',
            props: {
                placeholder: '请输入物资编号',
                clearable: true
            }
        },
        {
            prop: 'categoryName',
            filter: 'filter_LIKES_categoryName',
            component: 'select-categoryName',
            props: {placeholder: '请选择物资分类'}
        }
    ]
}

// 客户管理
export const supplierOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'code',
            filter: 'filter_EQS_code',
            props: {
                placeholder: '请输入编码',
                clearable: true
            }
        },
        {
            prop: 'name',
            filter: 'filter_LIKES_name',
            props: {
                placeholder: '请输入品名',
                clearable: true
            }
        }
    ]
}

// contract
export const contractOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'code',
            filter: 'filter_EQS_code',
            props: {
                placeholder: '请输入编码',
                clearable: true
            }
        },
        {
            prop: 'customerName',
            filter: 'filter_LIKES_customerName',
            props: {
                placeholder: '请输入品名',
                clearable: true
            }
        }
    ]
}

// product
export const productListOption = {
    showMore: true,
    columns: [
        {
            prop: 'name',
            filter: 'filter_LIKES_name',
            props: {
                placeholder: '请输入品名',
                clearable: true
            }
        }
    ]
}


export const formindexOption = {
    showMore: true,
    columns: [
        {
            prop: 'applyDate',
            component: 'date-select',
            filter: 'filter_EQS_applyDate',
            props: {
                placeholder: '日期',
                clearable: false
            }
        }
    ]
}


export const formindexOptionbelongState = {
    showMore: true,
    columns: [
        {
            prop: 'belongState',
            filter: 'filter_EQS_belongState',
            component: 'dict-select',
            props: {
                type: 'b_flow_design_belong_state',
                placeholder: '请选择类型',
                clearable: true
            }
        }
    ]
}

export function getTablePagination() {
    return {
        pageSizes: [30, 50, 100, 200, 500, 1000, 2000],
        pageSize: 100,
        pageCount: 5,
        layout: 'total, sizes, prev, pager, next, jumper'
    }
}

export const DICT_PREFIX = 'dict|'
export const DATE_PREFIX = 'date|'
export const DICT_PROPS = {
    label: 'label',
    value: 'value',
    key: 'value'
}
export const getBaseUrl = function () {
    return isProd() ? window.serve?.VUE_APP_CENTER : process.env.VUE_APP_CENTER
}
export const InventoryDetailsOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_commitDate',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_commitDate',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        {
            prop: 'itemName',
            filter: 'filter_LIKES_itemName',
            component: 'SelectPname',
            props: {placeholder: '请选择商品名称', type: 'itemName'}
        },
        {
            prop: 'categoryName',
            filter: 'filter_LIKES_categoryName',
            component: 'select-categoryName',
            props: {placeholder: '请选择物资类型'}
        },
        {
            prop: 'supplierName',
            filter: 'filter_LIKES_supplierName',
            component: 'select-Sender-Name',
            props: {
                placeholder: '请输入送货单位',
                changeFilter: true,
                isNogetlist: true,
                clearable: true,
                type: 'supplierNameV'
            }
        }
        // {
        //   prop: 'supplierName',
        //   filter: 'filter_LIKES_supplierName',
        //   props: {
        //     placeholder: '请输入送货单位',
        //     clearable: true,
        //   },
        // },
    ]
}
export const OutboundDetailsOption = {
    showMore: true,
    columns: [
        {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_commitDate',
            props: {
                placeholder: '开始日期',
                clearable: false
            }
        },
        {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_commitDate',
            props: {
                placeholder: '结束日期',
                clearable: false
            }
        },
        // {
        //   prop: 'usedDept',
        //   filter: 'filter_LIKES_usedDept',
        //   props: {
        //     placeholder: '请输入领用部门',
        //     clearable: true,
        //   },
        // },

        {
            prop: 'usedDept',
            filter: 'filter_LIKES_usedDept',
            component: 'SelectPname',
            props: {placeholder: '请输入领用部门', type: 'usedDept'}
        },
        {
            prop: 'itemName',
            filter: 'filter_LIKES_itemName',
            component: 'SelectPname',
            props: {placeholder: '请选择商品名称', type: 'itemName'}
        },
        {
            prop: 'categoryName',
            filter: 'filter_LIKES_categoryName',
            component: 'select-categoryName',
            props: {placeholder: '请选择物资类型'}
        },
        {
            prop: 'usedMan',
            filter: 'filter_LIKES_usedMan',
            component: 'SelectPname',
            props: {placeholder: '请输入领用人', type: 'usedMan'}
        }
    ]
}


export const INDEX_NUM_FORMAT = {
    cleanMt: {pattern: '0.0'},
    cleanAd: {pattern: '0.00'},
    crc: {pattern: '0'},
    cleanVdaf: {pattern: '0.00'},
    cleanStd: {pattern: '0.00'},
    fineness0dot1: {pattern: '0.00'},
    fineness3: {pattern: '0.00'},
    fineness: {pattern: '0.00'},
    fineness2: {pattern: '0.00'},
    procG: {pattern: '0'},
    procY: {pattern: '0.0'},
    procX: {pattern: '0.0'},
    chloride: {pattern: '0.000'},
    weight: {pattern: '0.00'},
    default: {pattern: '0.00'},
    default0: {pattern: '0'},

    autoCoalCleanMt: {pattern: '0.00'},
    default3: {pattern: '0.000'},
    default2: {pattern: '0.00'},
    default1: {pattern: '0.0'}
}


export const getIndexNumPattern = (key) => {
    return INDEX_NUM_FORMAT[key].pattern || undefined
}

export const getIndexNumDecimalPlaces = (key) => {
    const target = INDEX_NUM_FORMAT[key].pattern || INDEX_NUM_FORMAT['default'].pattern
    return target.replace('.', '').length - 1
}

export const COAL_SOURCE_TYPE = {
  ZI_YOU: "zy",
  WAI_BU: "wb",
  ZSMJ: "zsmj",
};

export const MERGE_TYPE = {
  MERGE: "merge",
  SINGLE: "single",
};
