// set function parseTime,formatTime to filter
import Region from '@/components/RegionSelect/regionData'
import dateformat from 'dateformat'

export { parseTime, formatTime, formatGmt } from '@/utils'


export function dateFormat(time, format) {
    return dateformat(time, format)
}

function pluralize(time, label) {
    if (time === 1) {
        return time + label
    }
    return time + label + 's'
}
export function timeAgo(time) {
    const between = Date.now() / 1000 - Number(time)
    if (between < 3600) {
        return pluralize(~~(between / 60), ' minute')
    } else if (between < 86400) {
        return pluralize(~~(between / 3600), ' hour')
    } else {
        return pluralize(~~(between / 86400), ' day')
    }
}

/* 数字 格式化 */
export function numberFormatter(num, digits) {
    const si = [
        { value: 1E18, symbol: 'E' },
        { value: 1E15, symbol: 'P' },
        { value: 1E12, symbol: 'T' },
        { value: 1E9, symbol: 'G' },
        { value: 1E6, symbol: 'M' },
        { value: 1E3, symbol: 'k' }
    ]
    for (let i = 0; i < si.length; i++) {
        if (num >= si[i].value) {
            return (num / si[i].value + 0.1).toFixed(digits).replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
        }
    }
    return num.toString()
}

export function toThousandFilter(num) {
    return (+num || 0).toString().replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

/**
 * @param value
 * @param type
 */
export function getStateTextFromDict(value, type) {
    if (!type) {
        return ''
    }
    const gather = window.dict[type]
    if (gather) {
        const index = gather.findIndex(val => val.code === value)
        if (index !== -1) {
            return gather[index].name
        } else {
            return ''
        }
    } else {
        return ''
    }
}

export function formatDate(time) {
    if (time != null && time !== '') {
        var date = new Date(time)
        var year = date.getFullYear()
        let month = date.getMonth() + 1
        month = month > 9 ? month : '0' + month
        var day = date.getDate()
        return year + '-' + month + '-' + day
    } else {
        return ''
    }
}

export function formatDateAndTime(time) {
    if (time != null || time !== '') {
        var date = new Date(time)
        var year = date.getFullYear()
        var month = date.getMonth() + 1
        var day = date.getDate()
        var hour = date.getHours()
        var minute = date.getMinutes()
        var second = date.getSeconds()
        return year + '-' + month + '-' + day + '\n' + hour + ':' + minute + ':' + second
    } else {
        return ''
    }
}

/**
 * 根据省市区名称
 * @param  region(省市区编码数据)
 * @returns {string}
 */
export function getRegionText(region) {
    const gather = ['未知', '未知', '未知']
    Region.regionTree.forEach(item => {
        if (item.id === region[0]) {
            gather[0] = item.name
            for (let i = 0; i < item.children.length; i++) {
                if (item.children[i].id === region[1]) {
                    gather[1] = item.children[i].name
                    const middle = item.children[i].children
                    for (let j = 0; j < middle.length; j++) {
                        if (middle[j].id === region[2]) {
                            gather[2] = middle[j].name
                        }
                    }
                }
            }
        }
    })
    return `${gather[0]},${gather[1]},${gather[2]}`
}
