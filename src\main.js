import Vue from 'vue'
import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import Element from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

import '@/styles/index.scss' // global css
import App from './App'
import router from './router'
import store from './store'
import VueDND from 'awe-dnd'
import i18n from './lang' // Internationalization
import './icons' // icon
import './permission' // permission control
import './registerServiceWorker'
import * as filters from './filters' // global filters
import SCurd from './s-curd'
import 'handsontable/dist/handsontable.full.css'
import permission from '@/directive/permission/index.js' // 权限判断指令
// 碧水复制
import { getDicts } from '@/api/system/dict/data'
import { getConfigKey } from '@/api/infra/config'
import { parseTime, resetForm, handleTree } from '@/utils/ruoyi'
import { DICT_TYPE, getDictDataLabel, getDictDatas, getDictDatas2 } from '@/utils/dict'
// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.getDictDatas = getDictDatas
Vue.prototype.getDictDatas2 = getDictDatas2
Vue.prototype.getDictDataLabel = getDictDataLabel
Vue.prototype.DICT_TYPE = DICT_TYPE
Vue.prototype.handleTree = handleTree

// Form Generator 组件需要使用到 tinymce
import Tinymce from '@/components/tinymce/index.vue'

Vue.component('tinymce', Tinymce)
import '@/icons'
import request from '@/utils/request' // 实现 form generator 使用自己定义的 axios request 对象
Vue.prototype.$axios = request
import '@/styles/index.scss'
// end碧水复制


//挂载执行结果组件
import executeResult from '@/components/CusConfirm/index.vue'

// const Notification = Vue.extend(executeResult)
// //此处options就是你给该组件传递的data值
// executeResult.install = function (options) {
//   //使用$mount()给组件手动挂载参数
//   let instance = new Notification({
//     data: options
//   }).$mount()
//   document.body.appendChild(instance.$el) //将组件插入页面中
//   //注解：假如不想是全屏的，只是想将弹窗添加进入每个页面路由内，可以先获取到那个dom元素进行添加
//   //例如：let el = document.querySelectorAll('#execute')[0]
//   // el === undefined ? document.body.appendChild(instance.$el) : el.appendChild(instance.$el)注意判空
//   // console.log(instance.showRoutes)
//   Vue.nextTick(() => {
//     localStorage.setItem('isclear', false)
//     // 设置弹窗为显示状态
//     instance.openDialog(router, store)
//   })


// }
// Vue.prototype.$executeResult = executeResult.install


Vue.use(Element, {
  size: 'mini',
  i18n: (key, value) => i18n.t(key, value)
})
Vue.use(SCurd)
Vue.use(VueDND)
import registerGlobComps from '@/components/registerGlobComp'

Vue.use(Element, {
  size: 'mini',
  i18n: (key, value) => i18n.t(key, value)
})

registerGlobComps(Vue)
//
// /**
//  * 注册components下的组件
//  * 名称为目录名称
//  * 注意:components目录下只能有一个index.vue 或者 qualPile.js 否则组件不能用
//  */
// function reqisterComponents() {
//   const components = require.context('@/components/', true, /index\.(js|vue)$/)
//   components.keys().map(key => {
//     const name = key.match(/\w+/)[0]
//     const component = process.env === 'production' ? components(key) : components(key).default
//     Vue.component(name, component)
//   })
// }

// reqisterComponents()

Vue.directive('permission', permission)

// register global utility filters.
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

/**
 * 对Date的扩展，将 Date 转化为指定格式的String
 * 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
 * 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
 * @param fmt
 * @returns {*}
 * @constructor
 * @example
 * (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
 * (new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18
 *
 */
// eslint-disable-next-line no-extend-native
Date.prototype.Format = function (fmt) {
  const o = {
    'M+': this.getMonth() + 1, // 月份
    'd+': this.getDate(), // 日
    'h+': this.getHours(), // 小时
    'm+': this.getMinutes(), // 分
    's+': this.getSeconds(), // 秒
    'q+': Math.floor((this.getMonth() + 3) / 3), // 季度
    'S': this.getMilliseconds() // 毫秒
  }
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length))
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
  }
  return fmt
}

new Vue({
  router,
  store,
  i18n,
  // apolloProvider,
  render: h => h(App)
}).$mount('#app')
