import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import { typeName } from '@/const'

const name = '/cwe/a/contractParty'

class InvoicingModel extends Model {
    constructor() {
        const dataJson = {
            id: '',
            name: '', // 合同名称
            code: '', // 编码
            coalCategory: '', // 煤种
            type: '', // 类型
            source: '', // 来源
            cleanMt: '', // 全水
            cleanAd: '', // 灰分
            cleanStd: '', // 硫
            cleanVdaf2: '',
            procY2: '',
            cleanVdaf: '', // 挥发分
            crc: '', // 特征
            procG: '', // 粘结
            procY: '', // 胶质,
            procX: '', // x指标
            remarks: '', // 备注
            isPublic: 'N', // 是否开放接口
            isPublicBoolean: false,
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '名称',
                    prop: 'name',
                    slot: 'name',
                    isShow: true,
                },
                {
                    label: '类型',
                    prop: 'type',
                    slot: 'type',
                    isShow: true,
                },
                {
                    label: '简称',
                    prop: 'aliasName',
                    isShow: true,
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true,
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                },
            ],
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/saveContractParty`,
            method: 'post',
            data,
        })
    }

    async page(params = {}) {
        const res = await fetch({
            url: `${name}/page`,
            method: 'get',
            params: params,
        })
        // res.data.records.forEach(item => {
        //     item.isPublicBoolean = item.isPublic === 'Y'
        //     item.typeName = typeName[item.type]
        // })
        return res
    }

    async saveList(text) {
        try {
            return await fetch({
                url: `${name}/saveList`,
                method: 'post',
                data: text,
            })
        } catch (error) {
            console.log(error)
        }
    }
}

export default new InvoicingModel()
