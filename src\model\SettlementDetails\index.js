import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = '/cwe/a/stockLog'
class ChildModel extends Model {
    constructor() {
        const dataJson = {
            date: '', // 日期
            type: 'YM', // 类型:半成品精煤-BCPJM,成品精煤-CPJM,原煤-YM,中煤-ZM
            name: '', // 品名
            ad: '', // 灰分
            std: '', // 硫
            lastStock: '', // 上日库存
            todayIn: '', // 本日购入或生产
            todayOut: '', // 本日销售或使用
            stock: '', // 库存
            wasteRock: '', // 坑石矸
            remarks: '' // 备注信息
        }
        const form = {}
        const tableOption = {
            // mountQuery: false,
            showPage: true,
            showSelection: true,
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    sortable: true,
                    // width: 100,
                    isShow: true
                },
                {
                    label: '库存增加',
                    prop: 'todayIn',
                    // width: 100,
                    isShow: true
                },
                {
                    label: '库存减少',
                    prop: 'todayOut',
                    // width: 100,
                    isShow: true
                },
                {
                    label: '类型',
                    prop: 'optLog',
                    isShow: true
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true
                }
            ]
        }
        super(name, dataJson, form, tableOption)
    }
    async page(params = {}) {
        try {
            const res = await fetch({
                url: `${name}/page`,
                method: 'get',
                params: params
            })
            this.formatPage(res.data)
            return res
        } catch (error) {

        }
    }

    formatPage(data) {
        // 库存日志，就显示比如 原煤购进1000吨，库存增加1000吨，  精煤销售200吨，库存减少200吨  ，生产精煤1000吨，精煤库存增加1000吨
        const log = {
            WEIGHT_IN: '磅房购进 ',
            WEIGHT_OUT: '磅房销售 ',
            BLENDING_IN: '生产增加 ',
            BLENDING_OUT: '生产消耗 ',
            STOCK_CHECK: '盘库 ',
            WASH_IN: '生产增加',
            WASH_OUT: '生产消耗'
        }
        data.records.forEach(item => {
            item.optLog = `${log[item['refType']]}`
        })
    }
}

export default new ChildModel()
