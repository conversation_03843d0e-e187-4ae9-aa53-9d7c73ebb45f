import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class EntrustOrder extends Model {
    constructor () {
        const dataJson = {
            'name': '',
            'applayUserName': '',
            'location': '',
            'sampleDate': '',
            'taxCompanyName': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [],
            rules: {}
        }
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            offsetTop: -30,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '样品',
                    prop: 'name',
                    slot: 'name',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '样品编号',
                    prop: 'code',
                    minWidth: 120,
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '样品编号',
                        prop: 'filter_LIKES_code'
                    }
                },
                {
                    label: '品种',
                    prop: 'type',
                    minWidth: 120,
                    format: 's_assay_coal_type',
                    isShow: true
                },
                {
                    label: '客户名称',
                    prop: 'applayUserName',
                    minWidth: 120,
                    isFilter: true,
                    filter: {
                        label: '客户名称',
                        prop: 'filter_LIKES_applayUserName'
                    },
                    isShow: true
                },
                {
                    label: '客户电话',
                    prop: 'samplePersonTel',
                    minWidth: 120,
                    isFilter: true,
                    filter: {
                        label: '客户电话',
                        prop: 'filter_LIKES_samplePersonTel'
                    },
                    isShow: true
                },
                {
                    label: '收款金额',
                    prop: 'finalFee',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '送样时间',
                    prop: 'sampleDate',
                    isFilter: true,
                    minWidth: 120,
                    filter: {
                        label: '样品',
                        end: 'filter_LED_sampleDate',
                        start: 'filter_GED_sampleDate'
                    },
                    component: 'DateRanger',
                    isShow: true
                },
                {
                    label: '省市区',
                    prop: 'location',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '委托状态',
                    prop: 'assayStatus',
                    minWidth: 120,
                    format: 'b_assay_status_type',
                    isShow: true
                },
                {
                    label: '公司名称',
                    minWidth: 120,
                    prop: 'taxCompanyName',
                    isShow: true
                },
                {
                    label: '电话',
                    minWidth: 120,
                    prop: 'taxPhone',
                    isShow: true
                },
                {
                    label: '创建人',
                    prop: 'createBy',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '创建时间',
                    minWidth: 140,
                    prop: 'createDate',
                    isShow: true
                },
                {
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            actions: []
        }
        super('/cwe/a/assayApply', dataJson, form, tableOption)
    }

    acceptAssayApply (data) {
        return fetch({
            url: '/cwe/a/assayApply/acceptAssayApply',
            method: 'post',
            data: { ...data }
        })
    }

    rejectAssayApply (data) {
        return fetch({
            url: '/cwe/a/assayApply/rejectAssayApply',
            method: 'post',
            data: { ...data }
        })
    }

    createTask (data) {
        return fetch({
            url: '/cwe/a/assayApply/createTask',
            method: 'post',
            data: { ...data }
        })
    }

    collectMoney (data) {
        return fetch({
            url: '/cwe/a/assayApply/collectMoney',
            method: 'post',
            data: { ...data }
        })
    }

    assayApplyLog (searchForm) {
        return fetch({
            url: '/cwe/a/assayApplyLog/page',
            method: 'get',
            params: { ...searchForm }
        })
    }
}

export default new EntrustOrder()
