import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class SConfigModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': '',
            'code': '',
            'tel': '',
            'mobile': '',
            'customerClassify': '',
            'fax': '',
            'email': '',
            'provinceCode': '',
            'provinceName': '',
            'cityCode': '',
            'cityName': '',
            'areaCode': '',
            'areaName': '',
            'initialArrears': '',
            'attachment': '',
            'createBy': '',
            'createDate': '',
            'remarks': '',
            'state': ''
        }
        const form = {}
        const tableOption = {
            add: false,
            edit: true,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '客户名称',
                    prop: 'name',
                    slot: 'name',
                    isShow: true,
                    minWidth: 120,
                    isFilter: true,
                    filter: {
                        label: '客户名称',
                        prop: 'filter_LIKES_name'
                    }
                },
                {
                    label: '电话',
                    prop: 'mobile',
                    isShow: true,
                    minWidth: 120,
                    isFilter: true,
                    filter: {
                        label: '电话',
                        prop: 'filter_LIKES_mobile'
                    }
                },
                {
                    label: '省',
                    prop: 'provinceName',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '市',
                    prop: 'cityName',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '区',
                    prop: 'areaName',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '期初欠款',
                    prop: 'initialArrears',
                    isShow: true,
                    minWidth: 120
                },
                // isMonthlySettlement: 'Y'
                {
                    label: '是否月结',
                    prop: 'isMonthlySettlement',
                    isShow: true,
                    format: 's_yes_or_no',
                    minWidth: 100,
                    isFilter: true,
                    filter: {
                        label: '是否月结',
                        name: 'select',
                        prop: 'filter_EQS_isMonthlySettlement',
                        type: 's_yes_or_no'
                    },
                    component: 'DictSelect'
                },
                {
                    label: '备用电话',
                    prop: 'tel',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '传真',
                    prop: 'fax',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '邮箱',
                    prop: 'email',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '公司名称',
                    prop: 'taxCompanyName',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '税务识别号',
                    prop: 'taxNo',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '公司地址',
                    prop: 'taxAddress',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '公司电话',
                    prop: 'taxPhone',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '开户行',
                    prop: 'taxBankName',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '开户账号',
                    prop: 'taxAccount',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '状态',
                    prop: 'state',
                    format: 's_user_state',
                    isShow: true,
                    minWidth: 80,
                    isFilter: true,
                    filter: {
                        label: '状态',
                        prop: 'filter_EQS_state',
                        type: 's_user_state',
                        name: 'select'
                    },
                    component: 'DictSelect'
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                    minWidth: 120
                }
            ]
        }
        super('/cwe/a/customer', dataJson, form, tableOption)
    }

    getUser (keyword) {
        return fetch({
            url: '/cwe/a/user/findByKeyword',
            method: 'get',
            params: { ...keyword }
        })
    }

    getEquipment (keyword) {
        return fetch({
            url: '/cwe/a/equipment/findByKeyword',
            method: 'get',
            params: { ...keyword }
        })
    }

    getSite (keyword) {
        return fetch({
            url: '/cwe/a/site/findByKeyword',
            method: 'get',
            params: { ...keyword }
        })
    }

    getCustomer (keyword) {
        return fetch({
            url: '/cwe/a/customer/findByKeyword',
            method: 'get',
            params: { ...keyword }
        })
    }

    deleteId (id) {
        return fetch({
            url: this.name + '/delete',
            method: 'post',
            data: { id }
        })
    }

    save (data) {
        return fetch({
            url: this.name + '/saveCustomer',
            method: 'post',
            data: { ...data }
        })
    }

    enable (id) {
        return fetch({
            url: this.name + '/enable',
            method: 'post',
            data: { id }
        })
    }

    disable (id) {
        return fetch({
            url: this.name + '/disable',
            method: 'post',
            data: { id }
        })
    }
}

export default new SConfigModel()
