import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import fetchPdf from '@/utils/fetchPdf'

class EntrustOrder extends Model {
    constructor () {
        const dataJson = {
            'name': '',
            'state': '',
            'downloadUrl': '',
            'downloadTimes': '',
            'exportParam': '',
            'submitDate': '',
            'generateDate': '',
            'elapsedTime': '',
            'rowNumber': '',
            'errorMessage': '',
            'createBy': '',
            'createDate': '',
            'updateBy': '',
            'updateDate': '',
            'ext': '',
            'remarks': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [],
            rules: {}
        }
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '样品名称',
                    prop: 'name',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '样品名称',
                        prop: 'filter_LIKES_name'
                    }
                },
                {
                    label: '制样时间',
                    prop: 'makeSampleDate',
                    isShow: true,
                    minWidth: 110
                },
                {
                    label: '样品编号',
                    prop: 'code',
                    minWidth: 120,
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '样品编号',
                        prop: 'filter_LIKES_code'
                    }
                },
                {
                    label: '品种',
                    prop: 'type',
                    format: 's_assay_coal_type',
                    isShow: true
                },
                {
                    label: '送样日期',
                    prop: 'sampleDate',
                    isFilter: true,
                    minWidth: 120,
                    filter: {
                        label: '样品',
                        end: 'filter_LED_sampleDate',
                        start: 'filter_GED_sampleDate'
                    },
                    component: 'DateRanger',
                    isShow: true
                },
                {
                    label: '打印次数',
                    prop: 'printTimes',
                    isShow: true
                },
                {
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            actions: []
        }
        super('/cwe/a/assayApply', dataJson, form, tableOption)
    }

    makeSample (data) {
        return fetch({
            url: '/cwe/a/assayApply/makeSample',
            method: 'post',
            data: { ...data }
        })
    }

    getContractById (data) {
        return fetch({
            url: '/cwe/a/assayApply/getContractById',
            method: 'get',
            params: { ...data }
        })
    }

    printSample (data) {
        return fetchPdf({
            url: '/cwe/a/assayApply/printSample',
            method: 'post',
            data: { ...data }
        })
    }
}

export default new EntrustOrder()
