import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/qualAttachProduct`

class ChildModel extends Model {
    constructor() {
        const dataJson = {
            productName: '',
            attachmentList: []
        }
        const form = {}
        const tableOption = {
            showSelection: true,
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    sortable: true,
                    width: 100,
                    isShow: true,
                    align: 'center',
                    fixed: 'left'
                },
                {
                    label: '名称',
                    prop: 'name',
                    isShow: true,
                    align: 'center',
                },
                {
                    label: '类型',
                    prop: 'type',
                    align: 'center',
                    isShow: true,
                },
                {
                    label: '硫分St,d',
                    prop: 'std',
                    isShow: true,
                    align: 'center',
                },
                {
                    label: '灰分Ad',
                    prop: 'ad',
                    isShow: true,
                    align: 'center',
                },
                {
                    label: '高位发热量',
                    prop: 'qgr',
                    isShow: true,
                    align: 'center',
                },
                {
                    label: '低位发热量',
                    prop: 'qnet',
                    isShow: true,
                    align: 'center',
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                },
            ],
            table: {
                stripe: true,
                'element-loading-text': '加载中...',
                'highlight-current-row': true,
                cellStyle: { height: '44.5px' },
                headerCellStyle: { background: '#2f79e8', color: '#fff', 'font-weight': 400 }
            },
            sumIndex: 3,
            sumText: ' ',
            summaries: []
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: name + '/save',
            method: 'post',
            data
        })
    }
    page(searchForm) {
        searchForm.orderBy = 'date'
        return fetch({
            url: name + '/page',
            method: 'get',
            params: searchForm
        })
    }

    getUploadList(id) {
        return fetch({
            url: `${name}/getDetail`,
            method: 'get',
            params: { id }
        })
    }
    // 导入
    async importQualAttachProduct(text) {
        try {
            return await fetch({
                url: `${name}/importQualAttachProduct`,
                method: 'post',
                data: text
            })
        } catch (error) {
            console.log(error)
        }
    }
    savebatchChange(data) {
        return fetch({
            url: `${name}/batchRemove`,
            method: 'post',
            data
        })
    }
}

export default new ChildModel()
