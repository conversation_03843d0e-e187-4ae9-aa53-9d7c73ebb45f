import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import Decimal from 'decimal.js'

const name = `/cwe/a/coalRawSample`

class ChildModel extends Model {
  constructor() {
    const dataJson = {}
    const form = {}
    const tableOption = {
      showSelection: true,
      columns: [
        {
          label: '化验日期',
          prop: 'sampleDate',
          slot: 'sampleDate',
          sortable: true,
          width: 100,
          isShow: true,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '小样名称',
          prop: 'name',
          width: 100,
          isShow: true
        },
        {label: '含精', prop: 'haRaw', isShow: true, minWidth: 100},
        {label: '含中', prop: 'haMid', isShow: true, minWidth: 100},
        {label: '含矸', prop: 'haRock', isShow: true, minWidth: 100},
        {label: '灰分', prop: 'ad', isShow: true, minWidth: 100},
        {label: '硫', prop: 'std', isShow: true, minWidth: 100},
        {label: '挥发份', prop: 'vdaf', isShow: true, minWidth: 100},
        {label: '粘结指数', prop: 'g', isShow: true, minWidth: 100},
        {label: '焦渣', prop: 'crc', isShow: true, minWidth: 100},
        {label: '平均反射率', prop: 'macR0', isShow: true, minWidth: 100},
        {label: '标准偏差', prop: 'macS', isShow: true, minWidth: 100},
        {label: '全水', prop: 'mt', isShow: true, minWidth: 100},
        {label: '手动Y值', prop: 'y', isShow: true, minWidth: 100},
        {label: '自动Y值', prop: 'smY', isShow: true, minWidth: 100},
        {
          label: '查看附件',
          prop: 'attachment',
          slot: 'attachment',
          isShow: true
        },
        {
          noExport: true,
          label: '操作',
          prop: 'opt',
          slot: 'opt',
          isShow: true
        }
      ],
      table: {
        stripe: true,
        'element-loading-text': '加载中...',
        'highlight-current-row': true,
        headerCellStyle: {background: '#2f79e8', color: '#fff', 'font-weight': 400},
        border: true,
        summaryMethod: (param) => {
          const sums = []
          let {columns, data} = param
          columns.forEach((column, index) => {
            let sumOpt = this._tableOption.summaries.find(item => item => item.prop === column.property)
            if (sumOpt) {
              const values = data.map(item => item[column.property])
                ?.filter(item => Boolean(item))?.filter(item => !isNaN(Number(item)))
              if (values.length) {
                sums[index] = Decimal.sum(...values).div(values.length).toFixed(2)
              }
            }
            if (index === this._tableOption.sumIndex) {
              sums[index] = this._tableOption.sumText
            }
          })
          return sums
        }
      },
      sumIndex: 2,
      sumText: '平均',
      summaries: [
        {prop: 'cleanAd', suffix: '', avg: true, fixed: 2},
        {prop: 'cleanStd', suffix: '', avg: true, fixed: 2},
        {prop: 'cleanVdaf', suffix: '', avg: true, fixed: 2},
        {prop: 'procCrc', suffix: '', avg: true, fixed: 2},
        {prop: 'recovery', suffix: '', avg: true, fixed: 2},
        {prop: 'midCoal', suffix: '', avg: true, fixed: 2},
        {prop: 'procG', suffix: '', avg: true, fixed: 2},
        {prop: 'cleanMt', suffix: '', avg: true, fixed: 2},
        {prop: 'oneDotFour', suffix: '', avg: true, fixed: 2}
      ]
    }
    super(name, dataJson, form, tableOption)
  }

  save(data) {
    return fetch({
      url: '/cwe/a/coalRawSampleItem/saveObject',
      method: 'post',
      data
    })
  }

  saveCoalRawSampleItem(data) {
    return fetch({
      url: '/cwe/a/coalRawSampleItem/saveCoalRawSampleItem',
      method: 'post',
      data
    })
  }

  // async page(searchForm) {
  //   searchForm.orderBy = 'sampleDate'
  //   const res = await fetch({
  //     url: name + '/page',
  //     method: 'get',
  //     params: searchForm
  //   })
  //   res.data.records = this.formatData(res.data.records)
  //   // console.log(res, 'res')
  //   return res
  // }
  async page(searchForm) {
    return fetch({
      url: '/cwe/a/coalRawSampleItem/page',
      method: 'get',
      params: searchForm
    })
  }

  getUploadList(id) {
    return fetch({
      url: `/cwe/a/coalRawSampleItem/get`,
      method: 'get',
      params: {id}
    })
  }

  formatData(records) {
    const _records = []
    records.forEach(val => {
      if (!val.coalRawSampleItemList.length) {
        _records.push({...val})
      }
      val.coalRawSampleItemList.forEach((item, idx) => {
        if (idx === 0) {
          _records.push({...val, ...item, id: val.id})
        } else {
          _records.push({...item, id: val.id})
        }
      })
    })
    // console.log(_records)
    return _records
  }

  async importCoalRawSample(text) {
    try {
      return await fetch({
        url: `${name}/importCoalRawSample`,
        method: 'post',
        data: text
      })
    } catch (error) {
      console.log(error)
    }
  }

  savebatchChange(data) {
    return fetch({
      url: `${name}/batchRemove`,
      method: 'post',
      data
    })
  }

  getuserList(params = {}) {
    return fetch({
      url: `/cwe/a/user/list `,
      method: 'get',
      params
    })
  }
}

export default new ChildModel()
