import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/coalSample`

class ChildModel extends Model {
    constructor() {
        const dataJson = {
            name: '', // 名称
            type: '', // 品种
            province: '', // 省份
            city: '', // 市县
            area: '', // 区
            location: '', // ⽣产区域省市区拼接
            sampleDate: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 送样日期
            samplePerson: '', // 送样⼈
            samplePersonTel: '', // 联系电话
            mineName: '', // 矿名
            washeryName: '', // 洗煤⼚名称
            qualTestCond: '', // 实验条件-XJL⼩焦炉、TX铁箱
            samplePrice: '', // 价格
            rawMt: '', // 原煤全⽔
            rawAd: '', // 原煤全灰
            rawPointFive: '', // 原煤-0.5
            rawOnePointFour: '', // 原煤-1.4
            rawOnePointSix: '', // 原煤-1.6
            rawAdIn: '', // 原煤内灰,
            rawStd: '', // 原煤硫
            rawVdaf: '', // 原煤挥发分
            rawG: '', // 原煤粘结
            rawAdInPointFour: '', // 原煤内灰
            rawAdInPointSix: '', //  原煤内灰
            cleanVdaf: '', // 精煤挥发分
            cleanAd: '', // 靖煤全灰
            cleanStd: '', // 精煤硫份
            cleanMt: '', // ⽔份
            cleanP: '', // 磷
            less3mm: '', // ⼩于3mm
            greater3mm: '', // ⼤于3mm
            procG: '', // ⼊炉煤Ymm
            procY: '', // 原煤粘结
            procX: '', // ⼊炉煤Xmm
            procTs: '', //  ⼯艺ts
            procTr: '', // ⼯艺tr
            procTrs: '', // ⼯艺tr-s
            procTmax: '', // ⼯艺tmax
            procTf: '', // ⼯艺tf
            procCrc: '', // ⼯艺-特征
            macRmax: '', //  煤岩组分-反射率
            macS: '', // 煤岩组分⽅差
            macV: '', // 煤岩组-镜质组
            macI: '', // 煤岩组-丝质组
            macE: '', // 稳定组
            qualAd: '', //  焦炭Ad
            qualStd: '', // 焦炭Std
            qualVdaf: '', // 焦炭挥发分
            qualM40: '', // M40
            qualM10: '', // M10
            qualM25: '', // M25
            qualCsr: '', //  CSR
            qualCri: '', // CRI
            comSiO2: '', //  煤灰成分
            comAl2O3: '', // comAl2O3
            comFe2O3: '', // comFe2O3
            comCaO: '', // comCaO
            comMgO: '', //  comMgO
            comNa2O: '', // comNa2O
            comK2O: '', // comK2O
            comTiO2: '', // comTiO2
            comP2O5: '', // comP2O5
            comSO3: '', // comSO3
            attachmentRock: '', // 附件-煤岩
            attachmentNameRock: '', //  附件名称-煤岩
            attachmentProc: '', // 附件-⼯艺
            attachmentNameProc: '', //  附件名称-⼯艺
            procT1: '', // 软化温度
            procT2: '', //  开始膨胀温度
            procT3: '', // 固化温度
            procB: '', //  最大膨胀度b
            procA: '', // 最大收缩度a
            procV: '', //  活性物
            procVi: '', // 活惰比
            attachmentList: []
        }
        const form = {}
        const tableOption = {
            showSelection: true,
            columns: [
                {
                    label: '化验日期',
                    prop: 'sampleDate',
                    sortable: true,
                    width: 100,
                    isShow: true,
                    align: 'center',
                    fixed: 'left'
                },
                {
                    label: '小样名称',
                    prop: 'name',
                    slot: 'name',
                    width: 100,
                    isShow: true
                },
                {
                    label: '煤种',
                    prop: 'type',
                    align: 'center',
                    width: 100,
                    isShow: true
                },
                {
                    label: '负责人',
                    prop: 'responsibleUserName',
                    align: 'center',
                    width: 100,
                    isShow: true
                },
                {
                    label: '灰Ad%',
                    prop: 'rawAd',
                    isShow: true,
                    slot: 'rawAd'
                },
                {
                    label: '硫St,d',
                    prop: 'rawStd',
                    isShow: true,
                    slot: 'rawStd'
                },
                {
                    label: '挥发分Vdaf%',
                    prop: 'rawVdaf',
                    isShow: true,
                    slot: 'rawVdaf'
                },
                {
                    label: '特征Crc%',
                    prop: 'procCrc',
                    isShow: true,
                    slot: 'procCrc'
                },
                {
                    label: '粘结G',
                    prop: 'procG',
                    isShow: true,
                    slot: 'procG'
                },
                {
                    label: 'Ymm',
                    prop: 'procY',
                    isShow: true,
                    slot: 'procY'
                },
                {
                    label: 'Xmm',
                    prop: 'procX',
                    isShow: true,
                    slot: 'procX'
                },
                {
                    label: '全水Mt',
                    prop: 'rawMt',
                    isShow: true,
                    slot: 'rawMt'
                },

                {
                    label: '回收',
                    prop: 'recover',
                    isShow: true,
                    slot: 'recover'
                },
                {
                    label: '中煤',
                    prop: 'midCoal',
                    isShow: true,
                    slot: 'midCoal'
                },
                {
                    label: '矸石',
                    prop: 'wasteRock',
                    isShow: true,
                    slot: 'wasteRock'
                },

                {
                    label: '>3mm',
                    prop: 'less3mm',
                    isShow: true,
                    slot: 'less3mm'
                },
                {
                    label: '<3mm',
                    prop: 'greater3mm',
                    isShow: true,
                    slot: 'greater3mm'
                },
                // 焦炭
                {
                    label: 'Ad',
                    prop: 'qualAd',
                    isShow: true,
                    slot: 'qualAd'
                },
                {
                    label: 'St,d',
                    prop: 'qualStd',
                    isShow: true,
                    slot: 'qualStd'
                },
                {
                    label: 'Vdaf',
                    prop: 'qualVdaf',
                    isShow: true,
                    slot: 'qualVdaf'
                },
                {
                    label: 'M40',
                    prop: 'qualM40',
                    isShow: true,
                    slot: 'qualM40'
                },
                {
                    label: 'M25',
                    prop: 'qualM25',
                    isShow: true,
                    slot: 'qualM25'
                },
                {
                    label: 'M10',
                    prop: 'qualM10',
                    isShow: true,
                    slot: 'qualM10'
                },
                {
                    label: 'CRI',
                    prop: 'qualCri',
                    isShow: true,
                    slot: 'qualCri'
                },
                {
                    label: 'CSR',
                    prop: 'qualCsr',
                    isShow: true,
                    slot: 'qualCsr'
                },
                // 灰成份
                {
                    label: 'SiO2',
                    prop: 'comSiO2',
                    isShow: true,
                    slot: 'comSiO2'
                },
                {
                    label: 'Ai2O3',
                    prop: 'comAl2O3',
                    isShow: true,
                    slot: 'comAl2O3'
                },
                {
                    label: 'Fe2O3',
                    prop: 'comFe2O3',
                    isShow: true,
                    slot: 'comFe2O3'
                },
                {
                    label: 'CaO',
                    prop: 'comCaO',
                    isShow: true,
                    slot: 'comCaO'
                },
                {
                    label: 'MgO',
                    prop: 'comMgO',
                    isShow: true,
                    slot: 'comMgO'
                },
                {
                    label: 'Na2O',
                    prop: 'comNa2O',
                    isShow: true,
                    slot: 'comNa2O'
                },
                {
                    label: 'K2O',
                    prop: 'comK2O',
                    isShow: true,
                    slot: 'comK2O'
                },
                {
                    label: 'TiO2',
                    prop: 'comTiO2',
                    isShow: true,
                    slot: 'comTiO2'
                },
                {
                    label: 'P',
                    prop: 'comP2O5',
                    isShow: true,
                    slot: 'comP2O5'
                },
                {
                    label: 'SO3',
                    prop: 'comSO3',
                    isShow: true,
                    slot: 'comSO3'
                },
                // {
                //     label: 'Rran平均值',
                //     prop: 'macRran',
                //     width: 100,
                //     isShow: true,
                // },
                // 岩相
                {
                    label: 'Rmax平均值',
                    prop: 'macRmax',
                    isShow: true,
                    slot: 'macRmax'
                },
                // {
                //     label: 'Rmax平均值',
                //     prop: 'macRmax',
                //     isShow: true,
                //     slot: 'macRmax'
                // },
                {
                    label: '标准偏差S',
                    prop: 'macS',
                    isShow: true,
                    slot: 'macS'
                },
                {
                    label: '活性物',
                    prop: 'procV',
                    isShow: true,
                    slot: 'procV'
                },
                {
                    label: '活惰比',
                    prop: 'procVi',
                    isShow: true,
                    slot: 'procVi'
                },
                // 奥亚
                {
                    label: '软化温度',
                    prop: 'procT1',
                    isShow: true,
                    slot: 'procT1'
                },
                {
                    label: '开始膨胀温度',
                    prop: 'procT2',
                    isShow: true,
                    slot: 'procT2'
                },
                {
                    label: '固化温度',
                    prop: 'procT3',
                    isShow: true,
                    slot: 'procT3'
                },
                {
                    label: '最大膨胀度',
                    prop: 'procB',
                    isShow: true,
                    slot: 'procB'
                },
                {
                    label: '最大收缩度',
                    prop: 'procA',
                    isShow: true,
                    slot: 'procA'
                },
                // ----------------------------------------
                {
                    noExport: true,
                    label: '入炉煤细度',
                    slot: 'chargingTest',
                    isShow: true
                },
                {
                    noExport: true,
                    label: '入炉样测定',
                    slot: 'chargingFineness',
                    isShow: true
                },
                {
                    noExport: true,
                    label: '焦炭',
                    slot: 'coke',
                    isShow: true
                },
                {
                    noExport: true,
                    label: '灰成份',
                    slot: 'ashComposition',
                    isShow: true
                },
                {
                    noExport: true,
                    label: '岩相',
                    slot: 'lithofacies',
                    isShow: true
                },
                {
                    noExport: true,
                    label: '奥亚',
                    slot: 'aoya',
                    isShow: true
                },
                {
                    label: '附件列表',
                    prop: 'attachment',
                    slot: 'attachment',
                    isShow: true
                },

                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
                // ----------------------------------------

            ],
            table: {
                stripe: true,
                'element-loading-text': '加载中...',
                'highlight-current-row': true,
                cellStyle: { height: '44.5px' },
                headerCellStyle: { background: '#2f79e8', color: '#fff', 'font-weight': 400 }
            },
            sumIndex: 2,
            sumText: ' ',
            summaries: [
                { prop: 'rawAd', suffix: '', avg: true, fixed: 2 },
                { prop: 'rawStd', suffix: '', avg: true, fixed: 2 },
                { prop: 'rawVdaf', suffix: '', avg: true, fixed: 2 },
                { prop: 'procCrc', suffix: '', avg: true, fixed: 2 },
                { prop: 'procG', suffix: '', avg: true, fixed: 2 },
                { prop: 'procY', suffix: '', avg: true, fixed: 2 },
                { prop: 'procX', suffix: '', avg: true, fixed: 2 },
                { prop: 'rawMt', suffix: '', avg: true, fixed: 2 },
                { prop: 'less3mm', suffix: '', avg: true, fixed: 2 },
                { prop: 'greater3mm', suffix: '', avg: true, fixed: 2 },
                { prop: 'qualAd', suffix: '', avg: true, fixed: 2 },
                { prop: 'qualStd', suffix: '', avg: true, fixed: 2 },
                { prop: 'qualVdaf', suffix: '', avg: true, fixed: 2 },
                { prop: 'qualM40', suffix: '', avg: true, fixed: 2 },
                { prop: 'qualM25', suffix: '', avg: true, fixed: 2 },
                { prop: 'qualM10', suffix: '', avg: true, fixed: 2 },
                { prop: 'qualCri', suffix: '', avg: true, fixed: 2 },
                { prop: 'qualCsr', suffix: '', avg: true, fixed: 2 },
                { prop: 'comSiO2', suffix: '', avg: true, fixed: 2 },
                { prop: 'comAl2O3', suffix: '', avg: true, fixed: 2 },
                { prop: 'comFe2O3', suffix: '', avg: true, fixed: 2 },
                { prop: 'comCaO', suffix: '', avg: true, fixed: 2 },
                { prop: 'comMgO', suffix: '', avg: true, fixed: 2 },
                { prop: 'comNa2O', suffix: '', avg: true, fixed: 2 },
                { prop: 'comK2O', suffix: '', avg: true, fixed: 2 },
                { prop: 'comTiO2', suffix: '', avg: true, fixed: 2 },
                { prop: 'comP2O5', suffix: '', avg: true, fixed: 2 },
                { prop: 'comSO3', suffix: '', avg: true, fixed: 2 },
                { prop: 'macRmax', suffix: '', avg: true, fixed: 2 },
                { prop: 'macS', suffix: '', avg: true, fixed: 2 },
                { prop: 'procV', suffix: '', avg: true, fixed: 2 },
                { prop: 'procVi', suffix: '', avg: true, fixed: 2 },
                { prop: 'procT1', suffix: '', avg: true, fixed: 2 },
                { prop: 'procT2', suffix: '', avg: true, fixed: 2 },
                { prop: 'procT3', suffix: '', avg: true, fixed: 2 },
                { prop: 'procB', suffix: '', avg: true, fixed: 2 },
                { prop: 'procA', suffix: '', avg: true, fixed: 2 },
                { prop: 'chargingTest', suffix: '', avg: true, fixed: 2 },
                { prop: 'chargingFineness', suffix: '', avg: true, fixed: 2 },
                { prop: 'coke', suffix: '', avg: true, fixed: 2 },
                { prop: 'ashComposition', suffix: '', avg: true, fixed: 2 },
                { prop: 'lithofacies', suffix: '', avg: true, fixed: 2 },
                { prop: 'aoya', suffix: '', avg: true, fixed: 2 },
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: name + '/save',
            method: 'post',
            data
        })
    }
    page(searchForm) {
        searchForm.orderBy = 'sampleDate'
        return fetch({
            url: name + '/page',
            method: 'get',
            params: searchForm
        })
    }
    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }

    async importCoalSample(text) {
        try {
            return await fetch({
                url: `${name}/importCoalSample`,
                method: 'post',
                data: text
            })
        } catch (error) {
            console.log(error)
        }
    }
    savebatchChange(data) {
        return fetch({
            url: `${name}/batchRemove`,
            method: 'post',
            data
        })
    }

    getuserList(params = {}) {
        return fetch({
            url: `/cwe/a/user/list `,
            method: 'get',
            params
        })
    }

}

export default new ChildModel()
