import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import Decimal from 'decimal.js'

const name = `/cwe/a/qualProduction`

class ChildModel extends Model {
  constructor() {
    const dataJson = {
      productName: '',
      attachmentList: []
    }
    const form = {}
    const tableOption = {
      showSelection: true,
      columns: [
        {
          label: '日期',
          prop: 'date',
          align: 'center',
          sortable: true,
          width: 100,
          isShow: true,
          fixed: 'left'
        },
        {
          label: '品名',
          prop: 'productName',
          align: 'center',
          // slot: 'productName',
          isShow: true
        },
        {
          label: '灰分',
          prop: 'ad',
          align: 'center',
          isShow: true
        },
        {
          label: '硫',
          prop: 'std',
          align: 'center',
          isShow: true
        },
        {
          label: '挥发分',
          prop: 'vdaf',
          align: 'center',
          isShow: true
        },
        {
          label: '粘结指数',
          prop: 'g',
          align: 'center',
          isShow: true
        },
        {
          label: '焦渣',
          prop: 'crc',
          align: 'center',
          isShow: true
        },
        {
          label: '全水',
          prop: 'mt',
          align: 'center',
          isShow: true
        },
        {
          label: '平均反射率',
          prop: 'macR0',
          align: 'center',
          isShow: true
        },
        {
          label: '标准方差',
          prop: 'macS',
          align: 'center',
          isShow: true
        },
        {
          label: '自动Y值',
          prop: 'smY',
          align: 'center',
          isShow: true
        },
        {
          label: '手动Y值',
          prop: 'y',
          align: 'center',
          isShow: true
        },
        {
          label: '查看附件',
          prop: 'attachment',
          slot: 'attachment',
          isShow: true
        },
        {
          noExport: true,
          label: '操作',
          prop: 'opt',
          slot: 'opt',
          isShow: true
        }
      ],
      table: {
        stripe: true,
        'element-loading-text': '加载中...',
        'highlight-current-row': true,
        cellStyle: {height: '44.5px'},
        headerCellStyle: {background: '#2f79e8', color: '#fff', 'font-weight': 400},
        summaryMethod: (param) => {
          const sums = []
          let {columns, data} = param
          columns.forEach((column, index) => {
            let sumOpt = this._tableOption.summaries.find(item => item => item.prop === column.property)
            if (sumOpt) {
              const values = data.map(item => item[column.property])
                ?.filter(item => Boolean(item))?.filter(item => !isNaN(Number(item)))
              if (values.length) {
                sums[index] = Decimal.sum(...values).div(values.length).toFixed(2)
              }
            }
            if (index === this._tableOption.sumIndex) {
              sums[index] = this._tableOption.sumText
            }
          })
          return sums
        }
      },
      sumIndex: 1,
      sumText: '平均',
      summaries: [
        {prop: 'ad', suffix: '', avg: true, fixed: 2},
        {prop: 'std', suffix: '', avg: true, fixed: 2},
        {prop: 'vdaf', suffix: '', avg: true, fixed: 2},
        {prop: 'g', suffix: '', avg: true, fixed: 2},
        {prop: 'crc', suffix: '', avg: true, fixed: 2},
        {prop: 'mt', suffix: '', avg: true, fixed: 2},
        {prop: 'macR0', suffix: '', avg: true, fixed: 2},
        {prop: 'macS', suffix: '', avg: true, fixed: 2},
        {prop: 'smY', suffix: '', avg: true, fixed: 2},
        {prop: 'y', suffix: '', avg: true, fixed: 2}

        // {prop: 'mt', suffix: '', avg: true, fixed: 2},
        // {prop: 'mad', suffix: '', avg: true, fixed: 2},
        // {prop: 'aad', suffix: '', avg: true, fixed: 2},
        // {prop: 'ad', suffix: '', avg: true, fixed: 2},
        // {prop: 'stad', suffix: '', avg: true, fixed: 2},
        // {prop: 'std', suffix: '', avg: true, fixed: 2},
        // {prop: 'vad', suffix: '', avg: true, fixed: 2},
        // {prop: 'vdaf', suffix: '', avg: true, fixed: 2},
        // {prop: 'crc', suffix: '', avg: true, fixed: 2},
        // {prop: 'g', suffix: '', avg: true, fixed: 2},
        // {prop: 'recover', suffix: '', avg: true, fixed: 2},
        // {prop: 'midCoal', suffix: '', avg: true, fixed: 2},
        // {prop: 'wasteRock', suffix: '', avg: true, fixed: 2}
      ]
    }
    super(name, dataJson, form, tableOption)
  }

  deleteId(id) {
    return fetch({
      url: name + '/deleteById',
      method: 'post',
      data: {id}
    })
  }

  save(data) {
    return fetch({
      url: name + '/saveQualProduction',
      method: 'post',
      data
    })
  }

  page(searchForm) {
    searchForm.orderBy = 'date'
    return fetch({
      url: name + '/page',
      method: 'get',
      params: searchForm
    })
  }

  getUploadList(id) {
    return fetch({
      url: `${name}/getDetail`,
      method: 'get',
      params: {id}
    })
  }

  // 导入
  async importQualProduction(text) {
    try {
      return await fetch({
        url: `${name}/importQualProduction`,
        method: 'post',
        data: text
      })
    } catch (error) {
      console.log(error)
    }
  }

  // 批量删除
  savebatchChange(data) {
    return fetch({
      url: `${name}/batchRemove`,
      method: 'post',
      data
    })
  }
}

export default new ChildModel()
