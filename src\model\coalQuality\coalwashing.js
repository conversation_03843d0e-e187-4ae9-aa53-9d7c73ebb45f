import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import Decimal from 'decimal.js'

const name = `/cwe/a/qualWashCoal`

class ChildModel extends Model {
  constructor() {
    const dataJson = {
      // date: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 日期
      // time: '', // 采样时间
      // name: '', // 煤名称
      // contractId: '',
      // contractCode: '', // 合同编号
      // customerName: '', // 客户名称
      productName: '',
      // productCode: '',
      // productId: '',
      // mt: '', // 水分
      // mad: '', // 内水
      // aad: '', // 空气干燥基灰分
      // stad: '', // 全硫分
      // vad: '', // 挥发分,
      // crc: '', // 特征
      // g: '', // 粘结
      // ad: '', // 干燥基灰分Aad
      // std: '', // 硫分St.ad
      // vdaf: '', // 挥发分Vad
      // x: '',
      // y: ''
      attachmentList: []
    }
    const form = {}
    const tableOption = {
      showSelection: true,
      columns: [
        {
          label: '日期',
          prop: 'date',
          align: 'center',
          // sortable: true,
          width: 100,
          isShow: true,
          fixed: 'left'
        },
        // {
        //     label: '送样时间',
        //     prop: 'time',
        //     align: 'center',
        //     width: 100,
        //     isShow: true,
        //     fixed: 'left'
        // },
        {
          label: '品名',
          prop: 'productName',
          slot: 'productName',
          align: 'center',
          minWidth: 120,
          isShow: true
        },
        {noExport: true, label: '大堆精', slot: 'indicators', isShow: true},
        {label: '灰分', prop: 'blendingAd', align: 'center', isShow: false},
        {label: '硫', prop: 'blendingStd', align: 'center', isShow: false},
        {label: '挥发分', prop: 'blendingVdaf', align: 'center', isShow: false},
        {label: '粘结指数', prop: 'blendingG', align: 'center', isShow: false},
        {label: '焦渣', prop: 'blendingCrc', align: 'center', isShow: false},
        {label: '全水', prop: 'blendingMt', align: 'center', isShow: false},
        {label: '查看附件', prop: 'attachment', slot: 'attachment', isShow: true},
        {
          noExport: true,
          label: '操作',
          prop: 'opt',
          slot: 'opt',
          isShow: true
        }
      ],
      table: {
        stripe: true,
        'element-loading-text': '加载中...',
        'highlight-current-row': true,
        cellStyle: {height: '44.5px'},
        headerCellStyle: {background: '#2f79e8', color: '#fff', 'font-weight': 400},
        border: true,
        summaryMethod: (param) => {
          const sums = []
          let {columns, data} = param
          columns.forEach((column, index) => {
            let sumOpt = this._tableOption.summaries.find(item => item => item.prop === column.property)
            if (sumOpt) {
              const values = data.map(item => item[column.property])
                ?.filter(item => Boolean(item))?.filter(item => !isNaN(Number(item)))
              if (values.length) {
                sums[index] = Decimal.sum(...values).div(values.length).toFixed(2)
              }
            }
            if (index === this._tableOption.sumIndex) {
              sums[index] = this._tableOption.sumText
            }
          })
          return sums
        }
      },
      sumIndex: 2,
      sumText: '平均',
      summaries: [
        {prop: 'blendingAd', suffix: '', avg: true, fixed: 2},
        {prop: 'blendingStd', suffix: '', avg: true, fixed: 2},
        {prop: 'blendingVdaf', suffix: '', avg: true, fixed: 2},
        {prop: 'blendingG', suffix: '', avg: true, fixed: 2},
        {prop: 'blendingCrc', suffix: '', avg: true, fixed: 2},
        {prop: 'blendingMt', suffix: '', avg: true, fixed: 2}
      ]
    }
    super(name, dataJson, form, tableOption)
  }

  deleteId(id) {
    return fetch({
      url: name + '/deleteById',
      method: 'post',
      data: {id}
    })
  }

  save(data) {
    return fetch({
      url: name + '/saveQualWashCoal',
      method: 'post',
      data
    })
  }

  async page(searchForm) {
    return fetch({
      method: 'get',
      url: name + '/findPage',
      params: searchForm
    })
  }

  formatData(records) {
    const _records = []
    records.forEach(val => {
      if (!val.productItemList.length) {
        _records.push({...val})
      }
      val.productItemList.forEach((item, idx) => {
        if (idx === 0) {
          _records.push({...val, ...item, id: val.id})
        } else {
          _records.push({...item, id: val.id})
        }
      })
    })
    // console.log(_records)
    return _records
  }

  getUploadList(id) {
    return fetch({
      url: `${name}/getDetail`,
      method: 'get',
      params: {id}
    })
  }

  // 导入
  async importQualWashCoal(text) {
    try {
      return await fetch({
        url: `${name}/importQualWashCoal`,
        method: 'post',
        data: text
      })
    } catch (error) {
      console.log(error)
    }
  }

  savebatchChange(data) {
    return fetch({
      url: `${name}/batchRemove`,
      method: 'post',
      data
    })
  }
}

export default new ChildModel()
