import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/customerFeedback`

class ChildModel extends Model {
    constructor() {
        const dataJson = {
            date: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 日期
            name: '', // 名称
            contractId: '', // 合同ID
            contractCode: '', //  合同编号
            senderName: '', // 发货单位
            receiverName: '', // 收货单位
            coalCategory: '', // 煤种，精煤、原煤
            truckCount: '', // 车数
            sendWeight: 0, // 原发数
            receiveWeight: 0, // 实收数,
            mt: '', // 水分
            wayCost: 0, // 途耗
            crc: '', // 特征
            g: '', // 粘结
            ad: '', // 干燥基灰分
            std: '', // 硫分
            vdaf: '', // 挥发分
            x: '', // 收缩度
            y: '' // 胶质层厚度
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    align: 'center',
                    sortable: true,
                    width: 100,
                    isShow: true,
                    fixed: 'left'
                },
                {
                    label: '合同编号',
                    prop: 'contractCode',
                    align: 'center',
                    width: 150,
                    isShow: true,
                    fixed: 'left'
                },
                {
                    label: '发货单位',
                    prop: 'senderName',
                    width: 170,
                    align: 'center',
                    isShow: true
                },
                {
                    label: '收货单位',
                    prop: 'receiverName',
                    align: 'center',
                    width: 170,
                    isShow: true
                },
                {
                    label: '品名',
                    prop: 'name',
                    slot: 'name',
                    isShow: true
                },
                {
                    label: '煤种',
                    prop: 'coalCategory',
                    align: 'center',
                    width: 80,
                    isShow: true
                },
                {
                    label: '车数',
                    prop: 'truckCount',
                    align: 'center',
                    isShow: true
                },
                {
                    label: '原发数',
                    prop: 'sendWeight',
                    align: 'center',
                    isShow: true
                },
                {
                    label: '实收数',
                    prop: 'receiveWeight',
                    align: 'center',
                    isShow: true
                },
                {
                    label: '途耗',
                    prop: 'wayCost',
                    slot: 'wayCost',
                    isShow: true
                },
                {
                    noExport: true,
                    isShow: true,
                    label: '化验指标',
                    slot: 'indicators'
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            table: {
                stripe: true,
                'element-loading-text': '加载中...',
                'highlight-current-row': true,
                cellStyle: { height: '44.5px' },
                headerCellStyle: { background: '#2f79e8', color: '#fff', 'font-weight': 400 }
            },
            sumIndex: 2,
            sumText: '合计',
            summaries: [
                { prop: 'truckCount', suffix: '', fixed: 2 },
                { prop: 'sendWeight', suffix: '', fixed: 2 },
                { prop: 'receiveWeight', suffix: '', fixed: 2 },
                { prop: 'wayCost', suffix: '%', fixed: 2, custom: 'wayCost' },
                { prop: 'ad', suffix: '', fixed: 2, avg: true },
                { prop: 'std', suffix: '', fixed: 2, avg: true },
                { prop: 'vdaf', suffix: '', fixed: 2, avg: true },
                { prop: 'crc', suffix: '', fixed: 2, avg: true },
                { prop: 'g', suffix: '', fixed: 2, avg: true },
                { prop: 'y', suffix: '', fixed: 2, avg: true },
                { prop: 'x', suffix: '', fixed: 2, avg: true },
                { prop: 'mt', suffix: '', fixed: 2, avg: true }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: name + '/save',
            method: 'post',
            data
        })
    }
    page(searchForm) {
        return fetch({
            url: name + '/listByPage',
            method: 'get',
            params: searchForm
        })
    }
    async bookkeeping(id, date) {
        const { data: { records } } = await fetch({
            url: '/cwe/a/sellOut/page',
            method: 'get',
            params: {
                filter_EQS_contractId: id,
                filter_EQS_date: date
            }
        })
        if (records.length) {
            let { sendWeight = 0, receiveWeight = 0, wayCost = 0 } = records[0]
            return { sendWeight, receiveWeight, wayCost }
        }
        return { sendWeight: 0, receiveWeight: 0, wayCost: 0 }
    }
}

export default new ChildModel()
