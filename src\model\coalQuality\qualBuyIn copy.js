import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/qualBuyIn`
class ChildModel extends Model {
    constructor() {
        const dataJson = {
            date: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 日期
            name: '', // 煤名称
            senderName: '', // 发货单位
            receiverName: '', // 收货单位
            coalCategory: '', // 煤种
            truckCount: '', // 车数
            sendWeight: 0, // 原发吨数
            receiveWeight: 0, // 实收吨数
            wayCost: 0, // 途耗
            mt: '', // 水分,
            vad: '', // 挥发分
            crc: '', // 特征
            g: '', // 粘结
            ad: '', // 干燥基灰分
            std: '', // 硫分St.ad
            vdaf: '', // 挥发分Vad
            x: '', // 收缩度
            y: '', // 胶质层厚度
            contractCode: '',
            contractId: '',
            attachmentList: []
        }
        const form = {}
        const tableOption = {
            showSelection: true,
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    align: 'center',
                    sortable: true,
                    width: 100,
                    isShow: true,
                    fixed: 'left'
                },
                {
                    label: '采样时间',
                    prop: 'time',
                    align: 'center',
                    width: 170,
                    isShow: true
                },
                {
                    label: '车牌',
                    prop: 'plateNumber',
                    align: 'center',
                    width: 170,
                    isShow: true
                },
                {
                    label: '品名',
                    prop: 'productName',
                    slot: 'productName',
                    isShow: true
                },
                {
                    label: '合同编号',
                    prop: 'contractCode',
                    isShow: true,
                    width: 180,
                    align: "center"
                },
                {
                    label: '煤种',
                    prop: 'coalCategory',
                    align: 'center',
                    slot: 'coalCategory',
                    isShow: true
                },
                {
                    label: '原发吨数',
                    prop: 'sendWeight',
                    align: 'center',
                    slot: 'sendWeight',
                    isShow: true
                },
                {
                    label: '实收吨数',
                    prop: 'receiveWeight',
                    align: 'center',
                    slot: 'receiveWeight',
                    isShow: true
                },
                {
                    label: '灰Ad',
                    prop: 'ad',
                    slot: 'ad',
                    isShow: true
                },
                {
                    label: '硫St,d',
                    prop: 'std',
                    slot: 'std',
                    isShow: true
                },
                {
                    label: '含中',
                    prop: 'midCoal',
                    slot: 'midCoal',
                    isShow: true
                },

                {
                    label: '挥发Vdaf',
                    prop: 'vdaf',
                    slot: 'vdaf',
                    isShow: true
                },
                {
                    label: '回收',
                    prop: 'recovery',
                    slot: 'recovery',
                    isShow: true
                },
                {
                    label: '特征',
                    prop: 'crc',
                    slot: 'crc',
                    isShow: true
                },
                {
                    label: '粘结',
                    prop: 'g',
                    slot: 'g',
                    isShow: true
                },
                {
                    label: 'Y值',
                    prop: 'y',
                    slot: 'y',
                    isShow: true
                },
                {
                    label: 'X值',
                    prop: 'x',
                    slot: 'x',
                    isShow: true
                },
                {
                    label: '水分',
                    prop: 'mt',
                    slot: 'mt',
                    isShow: true
                },
                // noExport
                {
                    noExport: true,
                    isShow: true,
                    label: '化验指标',
                    slot: 'indicators'
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            table: {
                stripe: true,
                'element-loading-text': '加载中...',
                'highlight-current-row': true,
                cellStyle: { height: '44.5px' },
                headerCellStyle: { background: '#2f79e8', color: '#fff', 'font-weight': 400 }
            },
            sumIndex: 2,
            sumText: ' ',
            summaries: [
                { prop: 'truckCount', suffix: '', avg: false, fixed: 2 },
                { prop: 'sendWeight', suffix: '', avg: true, fixed: 2 },
                { prop: 'receiveWeight', suffix: '', avg: true, fixed: 2 },
                { prop: 'midCoal', suffix: '', avg: true, fixed: 2 },
                { prop: 'recovery', suffix: '', avg: true, fixed: 2 },
                { prop: 'gangue', suffix: '', avg: true, fixed: 2 },
                { prop: 'foamCoal', suffix: '', avg: true, fixed: 2 },

                { prop: 'ad', suffix: '', fixed: 2, avg: true },
                { prop: 'std', suffix: '', fixed: 2, avg: true },
                { prop: 'vdaf', suffix: '', fixed: 2, avg: true },
                { prop: 'crc', suffix: '', fixed: 2, avg: true },
                { prop: 'g', suffix: '', fixed: 2, avg: true },
                { prop: 'y', suffix: '', fixed: 2, avg: true },
                { prop: 'x', suffix: '', fixed: 2, avg: true },
                { prop: 'mt', suffix: '', fixed: 2, avg: true },

                { prop: 'midAd', suffix: '', fixed: 2, avg: true },
                { prop: 'midStd', suffix: '', fixed: 2, avg: true },
                { prop: 'midVdaf', suffix: '', fixed: 2, avg: true },
                { prop: 'midCrc', suffix: '', fixed: 2, avg: true },
                { prop: 'midG', suffix: '', fixed: 2, avg: true },
                { prop: 'midMt', suffix: '', fixed: 2, avg: true },
                { prop: 'midRecovery', suffix: '', fixed: 2, avg: true },

                { prop: 'foamAd', suffix: '', fixed: 2, avg: true },
                { prop: 'foamStd', suffix: '', fixed: 2, avg: true },
                { prop: 'foamVdaf', suffix: '', fixed: 2, avg: true },
                { prop: 'foamCrc', suffix: '', fixed: 2, avg: true },
                { prop: 'foamG', suffix: '', fixed: 2, avg: true },
                { prop: 'foamMt', suffix: '', fixed: 2, avg: true },
                { prop: 'foamRecovery', suffix: '', fixed: 2, avg: true },


            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: name + '/save',
            method: 'post',
            data
        })
    }
    page(searchForm) {
        searchForm.orderBy = 'date'
        return fetch({
            url: name + '/listByPage',
            method: 'get',
            params: searchForm
        })
    }

    getUploadList(id) {
        return fetch({
            url: `${name}/getDetail`,
            method: 'get',
            params: { id }
        })
    }
    async bookkeeping(id, date) {
        const { data: { records } } = await fetch({
            url: '/cwe/a/buyIn/page',
            method: 'get',
            params: {
                filter_EQS_contractId: id,
                filter_EQS_date: date
            }
        })

        if (records.length) {
            let { sendWeight = 0, receiveWeight = 0, wayCost = 0 } = records[0]
            return { sendWeight, receiveWeight, wayCost }
        }

        return { sendWeight: 0, receiveWeight: 0, wayCost: 0 }
    }

    async importQualBuyIn(text) {
        try {
            return await fetch({
                url: `${name}/importQualBuyIn`,
                method: 'post',
                data: text
            })
        } catch (error) {
            console.log(error)
        }
    }

    savebatchChange(data) {
        return fetch({
            url: `${name}/batchRemove`,
            method: 'post',
            data
        })
    }

}

export default new ChildModel()
