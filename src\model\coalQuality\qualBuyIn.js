import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import Decimal from 'decimal.js'

const name = `/cwe/a/qualBuyIn`

class ChildModel extends Model {
  constructor() {
    const dataJson = {
      // date: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 日期
      date: (new Date(new Date().getTime())).Format('yyyy-MM-dd'), // 日期
      name: '', // 煤名称
      senderName: '', // 发货单位
      receiverName: '', // 收货单位
      coalCategory: '', // 煤种
      truckCount: '', // 车数
      sendWeight: 0, // 原发吨数
      receiveWeight: 0, // 实收吨数
      wayCost: 0, // 途耗
      mt: '', // 水分,
      vad: '', // 挥发分
      crc: '', // 特征
      g: '', // 粘结
      ad: '', // 干燥基灰分
      std: '', // 硫分St.ad
      vdaf: '', // 挥发分Vad
      cri: '', // 收缩度
      y: '', // 胶质层厚度
      csr: '', // 胶质层厚度
      macR0: '',
      macS: '',
      contractCode: '',
      contractId: '',
      attachmentList: [],
      plateNumber: '',
      attachmentCsrList: []
    }
    const form = {}
    const tableOption = {
      showSelection: true,
      columns: [
        {
          label: '日期',
          prop: 'date',
          align: 'center',
          sortable: true,
          width: 100,
          isShow: true,
          fixed: 'left'
        },
        {
          label: '品名',
          prop: 'productName',
          slot: 'productName',
          isShow: true,
          fixed: 'left'
        },
        {
          label: '采样时间',
          prop: 'time',
          align: 'center',
          width: 170,
          isShow: false
        },
        {
          label: '灰分',
          align: 'center',
          prop: 'ad',
          isShow: true
        },
        {
          label: '硫',
          align: 'center',
          prop: 'std',
          isShow: true
        },
        {
          label: '挥发分',
          align: 'center',
          prop: 'vdaf',
          isShow: true
        },
        {
          label: '粘结指数',
          align: 'center',
          prop: 'g',
          isShow: true
        },
        {
          label: '焦渣',
          prop: 'crc',
          align: 'center',
          isShow: true
        },
        {
          label: '全水',
          prop: 'mt',
          align: 'center',
          isShow: true
        },
        {
          label: '反射率',
          prop: 'macR0',
          align: 'center',
          isShow: true
        },
        {
          label: '标准偏差',
          align: 'center',
          prop: 'macS',
          isShow: true
        },
        {
          label: '手动Y值',
          align: 'center',
          prop: 'y',
          isShow: true
        },
        {
          label: '自动Y值',
          align: 'center',
          prop: 'smY',
          isShow: true
        },
        {
          label: '查看附件',
          prop: 'attachment',
          slot: 'attachment',
          isShow: true
        },
        {
          noExport: true,
          label: '操作',
          prop: 'opt',
          slot: 'opt',
          isShow: true
        },

      ],
      table: {
        stripe: true,
        'element-loading-text': '加载中...',
        'highlight-current-row': true,
        cellStyle: { height: '44.5px' },
        headerCellStyle: { background: '#2f79e8', color: '#fff', 'font-weight': 400 },
        summaryMethod: (param) => {
          const sums = []
          let { columns, data } = param
          columns.forEach((column, index) => {
            let sumOpt = this._tableOption.summaries.find(item => item => item.prop === column.property)
            if (sumOpt) {
              const values = data.map(item => item[column.property])
                ?.filter(item => Boolean(item))?.filter(item => !isNaN(Number(item)))
              if (values.length) {
                sums[index] = Decimal.sum(...values).div(values.length).toFixed(2)
              }
            }
            if (index === this._tableOption.sumIndex) {
              sums[index] = this._tableOption.sumText
            }
          })
          return sums
        }
      },
      sumIndex: 2,
      sumText: '平均',
      summaries: [
        { prop: 'ad', suffix: '', fixed: '2', avg: false },
        { prop: 'std', suffix: '', fixed: '2', avg: false },
        { prop: 'vdaf', suffix: '', fixed: '2', avg: false },
        { prop: 'g', suffix: '', fixed: '2', avg: false },
        { prop: 'crc', suffix: '', fixed: '2', avg: false },
        { prop: 'mt', suffix: '', fixed: '2', avg: false },
        { prop: 'macS', suffix: '', fixed: '2', avg: false },
        { prop: 'y', suffix: '', fixed: '2', avg: false },
        { prop: 'macR0', suffix: '', fixed: '2', avg: false },
        { prop: 'smY', suffix: '', fixed: '2', avg: false }
      ]
    }
    super(name, dataJson, form, tableOption)
  }

  save(data) {
    return fetch({
      url: name + '/save',
      method: 'post',
      data
    })
  }

  page(searchForm) {
    searchForm.orderBy = 'date'
    return fetch({
      url: name + '/listByPage',
      method: 'get',
      params: searchForm
    })
  }

  getUploadList(id) {
    return fetch({
      url: `${name}/getDetail`,
      method: 'get',
      params: { id }
    })
  }
  async bookkeeping(id, date) {
    const { data: { records } } = await fetch({
      url: '/cwe/a/buyIn/page',
      method: 'get',
      params: {
        filter_EQS_contractId: id,
        filter_EQS_date: date
      }
    })

    if (records.length) {
      let { sendWeight = 0, receiveWeight = 0, wayCost = 0 } = records[0]
      return { sendWeight, receiveWeight, wayCost }
    }

    return { sendWeight: 0, receiveWeight: 0, wayCost: 0 }
  }

  async importQualBuyIn(text) {
    try {
      return await fetch({
        url: `${name}/importQualBuyIn`,
        method: 'post',
        data: text
      })
    } catch (error) {
      console.log(error)
    }
  }

  savebatchChange(data) {
    return fetch({
      url: `${name}/batchRemove`,
      method: 'post',
      data
    })
  }

  deleteId(id) {
    return fetch({
      url: `${name}/deleteById`,
      method: 'post',
      data: { id }
    })
  }

  getActualWeightSum(date, productId) {
    return fetch({
      url: `/cwe/a/weightHouseIn/getActualWeightSum`,
      method: 'get',
      params: {
        date: date,
        productId: productId
      }
    })
  }

}

export default new ChildModel()
