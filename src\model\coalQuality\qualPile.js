import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/qualPile`

class ChildModel extends Model {
    constructor() {
        const dataJson = {
            date: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 日期
            time: '', // 采样时间
            name: '', // 煤名称
            contractId: '',
            contractCode: '', // 合同编号
            contractName: '',
            customerName: '', // 客户名称
            productName: '',
            productCode: '',
            productId: '', // -------------没有
            mt: '', // 水分
            mad: '', // 内水
            aad: '', // 空气干燥基灰分
            stad: '', // 全硫分
            vad: '', // 挥发分,
            crc: '', // 特征
            g: '', // 粘结
            ad: '', // 干燥基灰分Aad
            std: '', // 硫分St.ad
            vdaf: '' // 挥发分Vad
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    align: 'center',
                    sortable: true,
                    width: 100,
                    isShow: true,
                    fixed: 'left'
                },
                // {
                //     label: '合同名称',
                //     prop: 'customerName',
                //     align: 'center',
                //     fixed: 'left',
                //     width: 120,
                //     isShow: true
                // },
                {
                    label: '合同编号',
                    prop: 'contractCode',
                    align: 'center',
                    fixed: 'left',
                    width: 120,
                    isShow: true
                },
                {
                    label: '采样时间',
                    prop: 'time',
                    align: 'center',
                    width: 90,
                    isShow: true
                },
                {
                    label: '品名',
                    prop: 'productName',
                    align: 'center',
                    slot: 'name',
                    width: 90,
                    isShow: true
                },
                // 水分%
                {
                    label: 'Mt',
                    prop: 'mt',
                    isShow: true,
                    slot: 'mt'
                },
                {
                    label: 'Mad',
                    prop: 'mad',
                    slot: 'mad',
                    isShow: true
                },
                // 灰分%
                {
                    label: 'Aad',
                    prop: 'aad',
                    slot: 'aad',
                    isShow: true
                },
                {
                    label: 'Ad',
                    prop: 'ad',
                    slot: 'ad',
                    isShow: true
                },
                // 硫分%
                {
                    label: 'St.ad',
                    prop: 'stad',
                    slot: 'stad',
                    isShow: true
                },
                {
                    label: 'St.d',
                    prop: 'std',
                    slot: 'std',
                    isShow: true
                },
                // 挥发份%
                {
                    label: 'Vad',
                    prop: 'vad',
                    slot: 'vad',
                    isShow: true
                },
                {
                    label: 'Vdaf',
                    prop: 'vdaf',
                    slot: 'vdaf',
                    isShow: true
                },
                // 特征
                {
                    label: 'CRC',
                    prop: 'crc',
                    slot: 'crc',
                    isShow: true
                },
                // 粘结指数
                {
                    label: 'G',
                    prop: 'g',
                    slot: 'g',
                    isShow: true
                },
                // noExport
                {
                    noExport: true,
                    isShow: true,
                    slot: 'moisture'
                },
                {
                    noExport: true,
                    isShow: true,
                    slot: 'ash'
                },
                {
                    noExport: true,
                    isShow: true,
                    slot: 'sulfur'
                },
                {
                    noExport: true,
                    isShow: true,
                    slot: 'volatilization'
                },
                {
                    noExport: true,
                    isShow: true,
                    slot: 'crcSlot'
                },
                {
                    noExport: true,
                    isShow: true,
                    slot: 'gSlot'
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            table: {
                stripe: true,
                'element-loading-text': '加载中...',
                'highlight-current-row': true,
                cellStyle: { height: '44.5px' },
                headerCellStyle: { background: '#2f79e8', color: '#fff', 'font-weight': 400 }
            },
            sumIndex: 2,
            sumText: '平均',
            summaries: [
                { prop: 'mt', suffix: '', fixed: 2 },
                { prop: 'mad', suffix: '', fixed: 2 },
                { prop: 'aad', suffix: '', fixed: 2 },
                { prop: 'ad', suffix: '', fixed: 2 },
                { prop: 'stad', suffix: '', fixed: 2 },
                { prop: 'std', suffix: '', fixed: 2 },
                { prop: 'vad', suffix: '', fixed: 2 },
                { prop: 'vdaf', suffix: '', fixed: 2 },
                { prop: 'crc', suffix: '', fixed: 2 },
                { prop: 'g', suffix: '', fixed: 2 }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: name + '/save',
            method: 'post',
            data
        })
    }
    page(searchForm) {
        return fetch({
            url: name + '/listByPage',
            method: 'get',
            params: searchForm
        })
    }
}

export default new ChildModel()
