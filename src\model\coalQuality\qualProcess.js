import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/qualProcess`

class ChildModel extends Model {
    constructor() {
        const dataJson = {
            productName: '',
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    align: 'center',
                    // sortable: true,
                    // width: 200,
                    isShow: true,
                    fixed: 'left'
                },
                {
                    label: '客户',
                    prop: 'customerName',
                    align: 'center',
                    width: 170,
                    isShow: true
                },
                {
                    label: '品名',
                    prop: 'productName',
                    align: 'center',
                    // fixed: 'left',
                    // width: 120,
                    isShow: true
                },
                {
                    label: '筛精',
                    prop: 'sifterAd',
                    align: 'center',
                    // width: 90,
                    isShow: true
                },
                {
                    label: '纯精',
                    align: 'center',
                    prop: 'pureAd',
                    isShow: true,
                },
                {
                    label: '精矿',
                    align: 'center',
                    prop: 'pureMineAd',
                    // slot: 'pureMineAd',
                    isShow: true
                },
                {
                    label: '尾矿',
                    align: 'center',
                    prop: 'tailMineAd',
                    isShow: true
                },
                // {
                //     label: '一矸4',
                //     align: 'center',
                //     prop: 'oneGanAd',
                //     slot: 'oneGanAd',
                //     isShow: true
                // },
                {
                    label: '一矸',
                    align: 'center',
                    prop: 'oneGanAdDesc',
                    // slot: 'oneGanAd',
                    isShow: true
                },
                {
                    label: '二矸',
                    align: 'center',
                    prop: 'twoGanAdDesc',
                    isShow: true
                },
                {
                    label: '三矸',
                    align: 'center',
                    prop: 'threeGanAdDesc',
                    isShow: true
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            table: {
                stripe: true,
                'element-loading-text': '加载中...',
                'highlight-current-row': true,
                cellStyle: { height: '44.5px' },
                headerCellStyle: { background: '#2f79e8', color: '#fff', 'font-weight': 400 }
            },
            // sumIndex: 2,
            // sumText: ' ',
            // summaries: [
            //     { prop: 'sifterAd', suffix: '', avg: true, fixed: 1 },
            //     { prop: 'pureAd', suffix: '', avg: true, fixed: 2 },
            //     { prop: 'pureMineAd', suffix: '', avg: true, fixed: 2 },
            //     { prop: 'tailMineAd', suffix: '', avg: true, fixed: 2 },
            //     { prop: 'oneGanAdDesc', suffix: '', avg: true, fixed: 2 },
            //     { prop: 'twoGanAdDesc', suffix: '', avg: true, fixed: 2 },
            //     { prop: 'threeGanAdDesc', suffix: '', avg: true, fixed: 2 },
            // ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: name + '/save',
            method: 'post',
            data
        })
    }
    page(searchForm) {
        return fetch({
            url: name + '/page',
            method: 'get',
            params: searchForm
        })
    }
}

export default new ChildModel()
