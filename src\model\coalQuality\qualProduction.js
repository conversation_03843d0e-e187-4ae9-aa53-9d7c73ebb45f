import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/qualProduction`

class ChildModel extends Model {
    constructor() {
        const dataJson = {
            // date: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 日期
            // time: '', // 采样时间
            // name: '', // 煤名称
            // contractId: '',
            // contractCode: '', // 合同编号
            // customerName: '', // 客户名称
            productName: '',
            // productCode: '',
            // productId: '',
            // mt: '', // 水分
            // mad: '', // 内水
            // aad: '', // 空气干燥基灰分
            // stad: '', // 全硫分
            // vad: '', // 挥发分,
            // crc: '', // 特征
            // g: '', // 粘结
            // ad: '', // 干燥基灰分Aad
            // std: '', // 硫分St.ad
            // vdaf: '', // 挥发分Vad
            // x: '',
            // y: ''
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    align: 'center',
                    // sortable: true,
                    width: 100,
                    isShow: true,
                    fixed: 'left'
                },
                {
                    label: '采样时间',
                    prop: 'time',
                    align: 'center',
                    width: 100,
                    isShow: true,
                    fixed: 'left'
                },
                {
                    label: '客户',
                    prop: 'customerName',
                    width: 170,
                    align: 'center',
                    isShow: true
                },
                {
                    label: '品名',
                    prop: 'productName',
                    align: 'center',
                    // fixed: 'left',
                    // width: 120,
                    isShow: true
                },
                {
                    label: '挥发分',
                    prop: 'vad',
                    align: 'center',
                    // width: 90,
                    isShow: true
                },
                {
                    label: '干燥基灰分',
                    align: 'center',
                    prop: 'ad',
                    isShow: true,
                },
                {
                    label: 'Vdaf',
                    align: 'center',
                    prop: 'vdaf',
                    slot: 'vdaf',
                    isShow: true
                },
                {
                    label: '硫分',
                    align: 'center',
                    prop: 'std',
                    isShow: true
                },
                {
                    label: '粘结',
                    align: 'center',
                    prop: 'g',
                    isShow: true
                },
                {
                    label: '特征',
                    align: 'center',
                    prop: 'crc',
                    isShow: true
                },
                {
                    label: '水分',
                    align: 'center',
                    prop: 'mt',
                    isShow: true
                },
                {
                    label: '回收',
                    align: 'center',
                    prop: 'recover',
                    isShow: true
                },
                {
                    label: '中煤',
                    align: 'center',
                    prop: 'midCoal',
                    isShow: true
                },
                {
                    label: '矸石',
                    align: 'center',
                    prop: 'wasteRock',
                    isShow: true
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            table: {
                stripe: true,
                'element-loading-text': '加载中...',
                'highlight-current-row': true,
                cellStyle: { height: '44.5px' },
                headerCellStyle: { background: '#2f79e8', color: '#fff', 'font-weight': 400 }
            },
            sumIndex: 3,
            sumText: ' ',
            summaries: [
                { prop: 'mt', suffix: '', avg: true, fixed: 2 },
                { prop: 'mad', suffix: '', avg: true, fixed: 2 },
                { prop: 'aad', suffix: '', avg: true, fixed: 2 },
                { prop: 'ad', suffix: '', avg: true, fixed: 2 },
                { prop: 'stad', suffix: '', avg: true, fixed: 2 },
                { prop: 'std', suffix: '', avg: true, fixed: 2 },
                { prop: 'vad', suffix: '', avg: true, fixed: 2 },
                { prop: 'vdaf', suffix: '', avg: true, fixed: 2 },
                { prop: 'crc', suffix: '', avg: true, fixed: 2 },
                { prop: 'g', suffix: '', avg: true, fixed: 2 },
                { prop: 'recover', suffix: '', avg: true, fixed: 2 },
                { prop: 'midCoal', suffix: '', avg: true, fixed: 2 },
                { prop: 'wasteRock', suffix: '', avg: true, fixed: 2 }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: name + '/save',
            method: 'post',
            data
        })
    }
    page(searchForm) {
        return fetch({
            url: name + '/page',
            method: 'get',
            params: searchForm
        })
    }


    // 导入
    async saveList(text) {
        // try {
        //     return await fetch({
        //         // url: `/cwe/a/weightHouseLog/importData`,
        //         url: `${name}/importInWeightHouseLog`,
        //         method: 'post',
        //         data: text
        //     })
        // } catch (error) {
        //     console.log(error)
        // }
    }
}

export default new ChildModel()
