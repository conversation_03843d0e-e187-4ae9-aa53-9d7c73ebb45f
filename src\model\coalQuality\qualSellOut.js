import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import Decimal from 'decimal.js'

const name = `/cwe/a/qualSellOut`

class ChildModel extends Model {
  constructor() {
    const dataJson = {
      date: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 日期
      name: '', // 煤名称
      senderName: '', // 发货单位
      receiverName: '', // 收货单位
      coalCategory: '', // 煤种
      truckCount: '', // 车数
      sendWeight: 0, // 原发吨数
      receiveWeight: 0, // 实收吨数
      wayCost: 0, // 途耗
      mt: '', // 水分,
      vad: '', // 挥发分
      crc: '', // 特征
      g: '', // 粘结
      ad: '', // 干燥基灰分
      std: '', // 硫分St.ad
      vdaf: '', // 挥发分Vad
      x: '', // 收缩度
      y: '', // 胶质层厚度
      contractCode: '',
      contractId: '',
      attachmentList: []
    }
    const form = {}
    const tableOption = {
      showSelection: true,
      columns: [
        {
          label: '日期',
          prop: 'date',
          align: 'center',
          sortable: true,
          width: 100,
          isShow: true,
          fixed: 'left'
        },
        {
          label: '品名',
          prop: 'productName',
          slot: 'productName',
          isShow: true
        },
        {
          label: '灰分',
          prop: 'ad',
          isShow: true
        },
        {
          label: '硫',
          prop: 'std',
          isShow: true
        },
        {
          label: '挥发分',
          prop: 'vdaf',
          isShow: true
        },
        {
          label: '粘结指数',
          prop: 'g',
          isShow: true
        },
        {
          label: '焦渣',
          prop: 'crc',
          isShow: true
        },
        {
          label: '全水',
          prop: 'mt',
          isShow: true
        },
        {
          label: '平均反射率',
          prop: 'macR0',
          isShow: true
        },
        {
          label: '标准方差',
          prop: 'macS',
          isShow: true
        },
        {
          label: '自动Y值',
          prop: 'smY',
          isShow: true
        },
        {
          label: '手动Y值',
          prop: 'y',
          isShow: true
        },
        {
          label: '查看附件',
          prop: 'attachment',
          slot: 'attachment',
          isShow: true
        },
        {
          noExport: true,
          label: '操作',
          prop: 'opt',
          slot: 'opt',
          isShow: true
        }
      ],
      table: {
        stripe: true,
        'element-loading-text': '加载中...',
        'highlight-current-row': true,
        cellStyle: {height: '44.5px'},
        headerCellStyle: {background: '#2f79e8', color: '#fff', 'font-weight': 400},
        summaryMethod: (param) => {
          const sums = []
          let {columns, data} = param
          columns.forEach((column, index) => {
            let sumOpt = this._tableOption.summaries.find(item => item => item.prop === column.property)
            if (sumOpt) {
              const values = data.map(item => item[column.property])
                ?.filter(item => Boolean(item))?.filter(item => !isNaN(Number(item)))
              if (values.length) {
                sums[index] = Decimal.sum(...values).div(values.length).toFixed(2)
              }
            }
            if (index === this._tableOption.sumIndex) {
              sums[index] = this._tableOption.sumText
            }
          })
          return sums
        }
      },
      sumIndex: 2,
      sumText: '平均',
      summaries: [
        {prop: 'truckCount', suffix: '', avg: false, fixed: 2},
        {prop: 'sendWeight', suffix: '', avg: true, fixed: 2},
        {prop: 'receiveWeight', suffix: '', avg: true, fixed: 2},
        {prop: 'ad', suffix: '', fixed: 2, avg: true},
        {prop: 'std', suffix: '', fixed: 2, avg: true},
        {prop: 'vdaf', suffix: '', fixed: 2, avg: true},
        {prop: 'crc', suffix: '', fixed: 2, avg: true},
        {prop: 'g', suffix: '', fixed: 2, avg: true},
        {prop: 'y', suffix: '', fixed: 2, avg: true},
        {prop: 'x', suffix: '', fixed: 2, avg: true},
        {prop: 'mt', suffix: '', fixed: 2, avg: true},
        {prop: 'cri', suffix: '', fixed: 2, avg: true},
        {prop: 'csr', suffix: '', fixed: 2, avg: true},
        {prop: 'macR0', suffix: '', fixed: 2, avg: true},
        {prop: 'macS', suffix: '', fixed: 2, avg: true}
      ]
    }
    super(name, dataJson, form, tableOption)
  }

  deleteId(id) {
    return fetch({
      url: name + '/deleteById',
      method: 'post',
      data: {id}
    })
  }

  // save(data) {
  //     return fetch({
  //         url: name + '/save',
  //         method: 'post',
  //         data
  //     })
  // }
  save(data) {
    return fetch({
      url: name + '/saveQualSellOut',
      method: 'post',
      data
    })
  }


  page(searchForm) {
    searchForm.orderBy = 'date'
    return fetch({
      // url: name + '/listByPage',
      url: name + '/page',
      method: 'get',
      params: searchForm
    })
  }

  async bookkeeping(id, date) {
    const {data: {records}} = await fetch({
      url: '/cwe/a/sellOut/page',
      method: 'get',
      params: {
        filter_LIKES_contractId: id,
        filter_EQS_date: date
      }
    })

    if (records.length) {
      let {sendWeight = 0, receiveWeight = 0, wayCost = 0} = records[0]
      return {sendWeight, receiveWeight, wayCost}
    }

    return {sendWeight: 0, receiveWeight: 0, wayCost: 0}
  }

  async importQualSellOut(text) {
    try {
      return await fetch({
        url: `${name}/importQualSellOut`,
        method: 'post',
        data: text
      })
    } catch (error) {
      console.log(error)
    }
  }

  getUploadList(id) {
    return fetch({
      url: `${name}/getDetail`,
      method: 'get',
      params: {id}
    })
  }

  savebatchChange(data) {
    return fetch({
      url: `${name}/batchRemove`,
      method: 'post',
      data
    })
  }

}

export default new ChildModel()
