import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import Decimal from 'decimal.js'

const name = `/cwe/a/coalRawQualBuyIn`

class ChildModel extends Model {
  constructor() {
    const dataJson = {
      // date: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 日期
      date: (new Date(new Date().getTime())).Format('yyyy-MM-dd'), // 日期
      name: '', // 煤名称
      senderName: '', // 发货单位
      receiverName: '', // 收货单位
      coalCategory: '', // 煤种
      truckCount: '', // 车数
      sendWeight: 0, // 原发吨数
      receiveWeight: 0, // 实收吨数
      wayCost: 0, // 途耗
      mt: '', // 水分,
      vad: '', // 挥发分
      crc: '', // 特征
      g: '', // 粘结
      ad: '', // 干燥基灰分
      std: '', // 硫分St.ad
      vdaf: '', // 挥发分Vad
      cri: '', // 收缩度
      y: '', // 胶质层厚度
      csr: '', // 胶质层厚度
      macR0: '',
      macS: '',
      contractCode: '',
      contractId: '',
      attachmentList: [],
      plateNumber: '',
      attachmentCsrList: []
    }
    const form = {}
    const tableOption = {
      showSelection: true,
      columns: [
        {
          label: '日期',
          prop: 'date',
          align: 'center',
          sortable: true,
          width: 100,
          isShow: true,
          fixed: 'left'
        },
        {
          label: '品名',
          prop: 'productName',
          slot: 'productName',
          isShow: true
        },
        {
          label: '含精',
          prop: 'haRaw',
          isShow: true
        },
        {
          label: '含中',
          prop: 'haMid',
          isShow: true
        },
        {
          label: '含矸',
          prop: 'haRock',
          isShow: true
        },
        {
          noExport: true,
          label: '1.4',
          slot: 'indicators',
          isShow: true
        },
        {
          noExport: true,
          label: '1.4-18',
          slot: 'mincol',
          isShow: true
        },
        {
          label: '灰分',
          prop: 'ad',
          isShow: false
        },
        {
          label: '硫分',
          prop: 'std',
          isShow: false
        },
        {
          label: '挥发分',
          prop: 'vdaf',
          isShow: false
        },
        {
          label: '粘结指数',
          prop: 'g',
          isShow: false
        },
        {
          label: '焦渣',
          prop: 'crc',
          isShow: false
        },
        {
          label: '标准偏差',
          prop: 'macS',
          isShow: false
        },
        {
          label: 'Y值',
          prop: 'y',
          isShow: false
        },
        {
          label: '灰分',
          prop: 'rawAd',
          isShow: false
        },
        {
          label: '硫分',
          prop: 'rawStd',
          isShow: false
        },
        {
          label: '挥发分',
          prop: 'rawVdaf',
          isShow: false
        },
        {
          label: '粘结指数',
          prop: 'rawG',
          isShow: false
        },
        {
          label: '焦渣',
          prop: 'rawCrc',
          isShow: false
        },
        {
          label: '标准偏差',
          prop: 'rawMacS',
          isShow: false
        },
        {
          label: 'Y值',
          prop: 'rawY',
          isShow: false
        },
        {
          label: '查看附件',
          prop: 'attachment',
          slot: 'attachment',
          isShow: true
        },
        {
          noExport: true,
          label: '操作',
          prop: 'opt',
          slot: 'opt',
          isShow: true
        }
      ],
      table: {
        stripe: true,
        'element-loading-text': '加载中...',
        'highlight-current-row': true,
        cellStyle: {height: '44.5px'},
        headerCellStyle: {background: '#2f79e8', color: '#fff', 'font-weight': 400},
        summaryMethod: (param) => {
          const sums = []
          let {columns, data} = param
          columns.forEach((column, index) => {
            let sumOpt = this._tableOption.summaries.find(item => item => item.prop === column.property)
            if (sumOpt) {
              const values = data.map(item => item[column.property])
                ?.filter(item => Boolean(item))?.filter(item => !isNaN(Number(item)))
              if (values.length) {
                sums[index] = Decimal.sum(...values).div(values.length).toFixed(2)
              }
            }
            if (index === this._tableOption.sumIndex) {
              sums[index] = this._tableOption.sumText
            }
          })
          return sums
        }
      },
      sumIndex: 2,
      sumText: '平均',
      summaries: [
        {prop: 'truckCount', suffix: '', avg: false, fixed: 2},
        {prop: 'sendWeight', suffix: '', avg: false, fixed: 2},
        {prop: 'receiveWeight', suffix: '', avg: false, fixed: 2},
        {prop: 'midCoal', suffix: '', avg: false, fixed: 2},
        {prop: 'recovery', suffix: '', avg: false, fixed: 2},
        {prop: 'gangue', suffix: '', avg: false, fixed: 2},
        {prop: 'foamCoal', suffix: '', avg: false, fixed: 2},

        {prop: 'ad', suffix: '', fixed: 2, avg: false},
        {prop: 'std', suffix: '', fixed: 2, avg: false},
        {prop: 'vdaf', suffix: '', fixed: 2, avg: false},
        {prop: 'crc', suffix: '', fixed: 2, avg: false},
        {prop: 'g', suffix: '', fixed: 2, avg: false},
        {prop: 'y', suffix: '', fixed: 2, avg: false},
        {prop: 'x', suffix: '', fixed: 2, avg: false},
        {prop: 'mt', suffix: '', fixed: 2, avg: false},

        {prop: 'midAd', suffix: '', fixed: 2, avg: false},
        {prop: 'midStd', suffix: '', fixed: 2, avg: false},
        {prop: 'midVdaf', suffix: '', fixed: 2, avg: false},
        {prop: 'midCrc', suffix: '', fixed: 2, avg: false},
        {prop: 'midG', suffix: '', fixed: 2, avg: false},
        {prop: 'midMt', suffix: '', fixed: 2, avg: false},
        {prop: 'midRecovery', suffix: '', fixed: 2, avg: false},

        {prop: 'foamAd', suffix: '', fixed: 2, avg: false},
        {prop: 'foamStd', suffix: '', fixed: 2, avg: false},
        {prop: 'foamVdaf', suffix: '', fixed: 2, avg: false},
        {prop: 'foamCrc', suffix: '', fixed: 2, avg: false},
        {prop: 'foamG', suffix: '', fixed: 2, avg: false},
        {prop: 'foamMt', suffix: '', fixed: 2, avg: false},
        {prop: 'foamRecovery', suffix: '', fixed: 2, avg: false}
      ]
    }
    super(name, dataJson, form, tableOption)
  }

  save(data) {
    return fetch({
      url: name + '/save',
      method: 'post',
      data
    })
  }

  page(searchForm) {
    searchForm.orderBy = 'date'
    return fetch({
      url: name + '/page',
      method: 'get',
      params: searchForm
    })
  }

  getUploadList(id) {
    return fetch({
      url: `${name}/getDetail`,
      method: 'get',
      params: {id}
    })
  }

  async bookkeeping(id, date) {
    const {data: {records}} = await fetch({
      url: '/cwe/a/buyIn/page',
      method: 'get',
      params: {
        filter_EQS_contractId: id,
        filter_EQS_date: date
      }
    })

    if (records.length) {
      let {sendWeight = 0, receiveWeight = 0, wayCost = 0} = records[0]
      return {sendWeight, receiveWeight, wayCost}
    }

    return {sendWeight: 0, receiveWeight: 0, wayCost: 0}
  }

  async importQualBuyIn(text) {
    try {
      return await fetch({
        url: `${name}/importQualBuyIn`,
        method: 'post',
        data: text
      })
    } catch (error) {
      console.log(error)
    }
  }

  savebatchChange(data) {
    return fetch({
      url: `${name}/batchRemove`,
      method: 'post',
      data
    })
  }

  deleteId(id) {
    return fetch({
      url: `${name}/deleteById`,
      method: 'post',
      data: {id}
    })
  }

  getActualWeightSum(date, productId) {
    return fetch({
      url: `/cwe/a/weightHouseIn/getActualWeightSum`,
      method: 'get',
      params: {
        date: date,
        productId: productId
      }
    })
  }

}

export default new ChildModel()
