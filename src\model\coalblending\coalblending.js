import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

const name = '/cwe/a/product'
const date = new Date()
const defaultDate = new Date(date.getFullYear(), date.getMonth(), date.getDay(), 0, 0, 0)
class _ extends Model {
    constructor() {
        const dataJson = {
            productionDate: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 生产日期
            productName: '', // 产品名称
            productCode: '', // 产品编码
            productId: '', // 产品ID'
            contractCode: '', // 合同编码
            contractId: '', // 合同ID
            contractName: '', // 合同名称
            startTime: defaultDate, // 开机时间
            stopTime: defaultDate, // 停机时间
            startTime1: defaultDate, // 开机时间
            stopTime1: defaultDate, // 停机时间
            startTime2: defaultDate, // 开机时间
            stopTime2: defaultDate, // 停机时间
            actualProductionTime: '0.00', // 实际生产时间/h
            fieldCommand: '', // 现场指挥
            makeupMan: '', // 填表人
            auditMan: '', // 审核人
            outputWeight: 0, // 产量
            remarks: ''
        }
        const form = {}
        const tableOption = {}
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `/cwe/a/blendingProduction/saveBlendingProduction`,
            method: 'post',
            data
        })
    }
    async page(params = {}) {
        const res = await fetch({ url: `/cwe/a/blendingProduction/findList`, method: 'get', params: params })
        res.data.forEach(item => {
            this.formatPage(item.blendingProductionVoList)
        })
        return res
    }
    async detail(id) {
        const res = await fetch({
            url: `/cwe/a/blendingProduction/getDetail`,
            method: 'get',
            params: { id }
        })
        this.formatDetail(res.data, ['_measureWeight', '_mt', '_innerCode', '_startAccumulated', '_stopAccumulated', '_setPercent1', '_setPercent2', '_setPercent3'])
        return res
    }

    deleteId(id) {
        return fetch({
            url: '/cwe/a/blendingProduction/deleteById',
            method: 'post',
            data: { id }
        })
    }

    getDateList(params = {}) {
        return fetch({
            url: `/cwe/a/blendingProduction/getDateList`,
            method: 'get',
            params: params
        })
    }

    formatPage(data) {
        if (!data.length) return data
        data.sort((a, b) => a.measuringScale - b.measuringScale)
        const sum = {
            measuringScale: '合计', // 计量称
            measurePercent: 0, // '计量百分比',
            measureWeight: 0, // 计量（吨）
            mt: '/', // 水分
            startAccumulated: 0, // 开机累计
            stopAccumulated: 0, // 停机累计
            setUnit1: 0, // 设定单位1
            setTime1: '', // 设定时间1
            setPercent1: 0, // 设定配比1
            setWeight1: 0, // 设定量1
            setUnit2: 0, // 设定单位2
            setTime2: '', // 设定时间2
            setPercent2: 0, // 设定配比2
            setWeight2: 0, // 设定量2
            setUnit3: 0, // 设定单位3
            setTime3: '', // 设定时间3
            setPercent3: 0, // 设定配比3
            setWeight3: 0 // 设定量3
        }
        data.forEach(item => {
            item.measuringScale = `${item.measuringScale.slice(1) * 1}#给料机`
            sum.measurePercent += item.measurePercent * 1
            sum.measureWeight += item.measureWeight * 1
            sum.startAccumulated += item.startAccumulated * 1
            sum.stopAccumulated += item.stopAccumulated * 1
            sum.setUnit1 += item.setUnit1 * 1
            sum.setPercent1 += item.setPercent1 * 1
            sum.setWeight1 += item.setWeight1 * 1
            sum.setUnit2 += item.setUnit2 * 1
            sum.setPercent2 += item.setPercent2 * 1
            sum.setWeight2 += item.setWeight2 * 1
            sum.setUnit3 += item.setUnit3 * 1
            sum.setPercent3 += item.setPercent3 * 1
            sum.setWeight3 += item.setWeight3 * 1
        })
        sum.measurePercent = parseInt(sum.measurePercent) + '%'
        sum.measureWeight = sum.measureWeight.toFixed(2)
        sum.startAccumulated = sum.startAccumulated.toFixed(2)
        sum.stopAccumulated = sum.stopAccumulated.toFixed(2)
        sum.setPercent1 = sum.setPercent1.toFixed(2) + '%'
        sum.setWeight1 = sum.setWeight1.toFixed(2)
        sum.setPercent2 = sum.setPercent2.toFixed(2) + '%'
        sum.setWeight2 = sum.setWeight2.toFixed(2)
        sum.setPercent3 = sum.setPercent3.toFixed(2) + '%'
        sum.setWeight3 = sum.setWeight3.toFixed(2)

        data.forEach(item => {
            item.measurePercent += '%'
            item.setPercent1 += item.setPercent1 && '%'
            item.setPercent2 += item.setPercent2 && '%'
            item.setPercent3 += item.setPercent3 && '%'
        })
        const lastInfo = { id: 'lastRow', measuringScale: '' }
        data.push(sum, lastInfo)
        return data
    }

    number(target, key, fixed = 2) {
        return target[key] ? target[key].toFixed(fixed) : target[key]
    }
    formatDetail(data, params = []) {
        data.blendingProductionItemList.forEach(item => {
            for (const key of params) {
                item['_id'] = Math.random().toFixed(6).slice(-6)
                item['_stock'] = item.stock || 0
                item[key] = true
            }
        })
    }
}

export default new _()
