import Model from '@/s-curd/src/model'

class ActualModel extends Model {
    constructor() {
        const dataJson = {
            'id': '',
            'name': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            // showIndex: true,
            showPage: true,
            // showSelection: true,
            mountQuery: true,
            columns: [
                // {
                //     label: '名称',
                //     prop: 'name',
                //     slot: 'name',
                //     isFilter: true,
                //     isShow: true,
                //     filter: {
                //         label: '名称',
                //         prop: 'filter_LIKES_name'
                //     },
                //     minWidth: 100
                // },
                {
                    label: '编码',
                    prop: 'code',
                    slot: 'code',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '值',
                    prop: 'value',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true,
                    minWidth: 100
                }
            ]
        }
        super('/cwe/a/config', dataJson, form, tableOption)
    }
}

export default new ActualModel()
