import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class DictModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': '',
            'type': '',
            'value': '',
            'remarks': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '名称',
                    prop: 'name',
                    slot: 'name',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '名称',
                        prop: 'filter_LIKES_name'
                    }
                },
                {
                    label: '固定资产编号',
                    prop: 'fixedAssetsCode',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '固定资产编号',
                        prop: 'filter_LIKES_fixedAssetsCode'
                    },
                    minWidth: 100
                },
                {
                    label: '型号',
                    prop: 'model',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '型号',
                        prop: 'filter_LIKES_model'
                    },
                    minWidth: 100
                },
                {
                    label: '出厂编号',
                    prop: 'factoryNumber',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '出厂编号',
                        prop: 'filter_LIKES_factoryNumber'
                    },
                    minWidth: 100
                },
                {
                    label: '量程',
                    prop: 'measuringRange',
                    minWidth: 100,
                    isShow: true
                },
                {
                    label: '分辨率',
                    minWidth: 100,
                    prop: 'resolution',
                    isShow: true
                },
                {
                    label: '准确度等级',
                    minWidth: 100,
                    prop: 'accuracyLevel',
                    isShow: true
                },
                {
                    label: '单位',
                    minWidth: 100,
                    prop: 'unit',
                    isShow: true
                },
                {
                    label: '价格',
                    minWidth: 100,
                    prop: 'price',
                    isShow: true
                },
                {
                    label: '生产厂家',
                    prop: 'manufacturer',
                    minWidth: 100,
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '生产厂家',
                        prop: 'filter_LIKES_manufacturer'
                    }
                },
                {
                    label: '生产日期',
                    prop: 'factoryDate',
                    slot: 'factoryDate',
                    minWidth: 100,
                    isShow: true
                },
                {
                    label: '购置日期',
                    minWidth: 100,
                    prop: 'purchaseDate',
                    slot: 'purchaseDate',
                    isShow: true
                },
                {
                    label: '量值博方式保养',
                    prop: 'valueMaintenance',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '状态',
                    minWidth: 100,
                    prop: 'status',
                    format: 'b_equipment_status',
                    isShow: true
                },
                {
                    label: '部门名称',
                    prop: 'useSiteName',
                    minWidth: 100,
                    isShow: true
                },
                {
                    label: '责任人名称',
                    prop: 'responsibilityUserName',
                    minWidth: 100,
                    isShow: true
                },
                {
                    label: '存放位置',
                    prop: 'storageLocation',
                    minWidth: 100,
                    isShow: true
                },
                {
                    label: '保养周期',
                    prop: 'maintenanceCycle',
                    minWidth: 100,
                    isShow: true
                },
                {
                    label: '保养周期单位',
                    prop: 'maintenanceCycleUnit',
                    format: 'b_equipment_cycle_unit',
                    minWidth: 100,
                    isShow: true
                },
                {
                    label: '最近保养日期',
                    prop: 'recentlyMaintenanceDate',
                    minWidth: 160,
                    isShow: true
                },
                {
                    label: '核查周期',
                    prop: 'verificationCycle',
                    minWidth: 100,
                    isShow: true
                },
                {
                    label: '核查周期单位',
                    prop: 'verificationCycleUnit',
                    format: 'b_equipment_cycle_unit',
                    minWidth: 100,
                    isShow: true
                },
                {
                    label: '最近核查日期',
                    prop: 'recentlyVerificationDate',
                    minWidth: 160,
                    isShow: true
                },
                {
                    label: '下次核查时间',
                    prop: 'nextVerificationDate',
                    minWidth: 160,
                    isShow: true
                },
                {
                    label: 'opt',
                    prop: 'opt',
                    slot: 'opt',
                    minWidth: 100,
                    isShow: true
                }
            ]
        }
        super('/cwe/a/equipment', dataJson, form, tableOption)
    }

    async saveEquipment (data) {
        return fetch({
            url: '/cwe/a/equipment/saveEquipment',
            method: 'post',
            data: { ...data }
        })
    }

    async deleteById (id) {
        return fetch({
            url: '/cwe/a/equipment/deleteById',
            method: 'post',
            data: { id }
        })
    }
}

export default new DictModel()
