import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class DictModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': '',
            'type': '',
            'value': '',
            'remarks': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '设备名称',
                    prop: 'equipmentName',
                    slot: 'equipmentName',
                    isFilter: true,
                    filter: {
                        label: '设备名称',
                        prop: 'filter_LIKES_equipmentName'
                    },
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '型号',
                    prop: 'model',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '设备编号',
                    prop: 'equipmentCode',
                    minWidth: 120,
                    isFilter: true,
                    filter: {
                        label: '设备编号',
                        prop: 'filter_LIKES_equipmentCode'
                    },
                    isShow: true
                },
                {
                    label: '保养结果',
                    prop: 'result',
                    isShow: true,
                    minWidth: 140
                },
                {
                    label: '保养证书编号',
                    minWidth: 120,
                    prop: 'certificateNumber',
                    isShow: true
                },
                {
                    label: '检定',
                    minWidth: 120,
                    prop: 'verification',
                    isShow: true
                },
                {
                    label: '保养单位',
                    minWidth: 120,
                    prop: 'maintenanceUnit',
                    isShow: true
                },
                {
                    label: '最近保养时间',
                    minWidth: 160,
                    prop: 'recentlyMaintenanceDate',
                    slot: 'recentlyMaintenanceDate',
                    isFilter: true,
                    filter: {
                        label: '最近保养时间',
                        start: 'filter_GED_recentlyMaintenanceDate',
                        end: 'filter_LED_recentlyMaintenanceDate'
                    },
                    component: 'DateRanger',
                    isShow: true
                },
                {
                    label: '保养周期',
                    prop: 'maintenanceCycle',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '保养周期单位',
                    format: 'b_equipment_cycle_unit',
                    prop: 'maintenanceCycleUnit',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '下次保养日期',
                    minWidth: 160,
                    prop: 'nextMaintenanceDate',
                    slot: 'nextMaintenanceDate',
                    isShow: true
                },
                {
                    label: 'opt',
                    prop: 'opt',
                    slot: 'opt',
                    minWidth: 120,
                    isShow: true
                }
            ]
        }
        super('/cwe/a/equipmentMaintenance', dataJson, form, tableOption)
    }

    async save (data) {
        return fetch({
            url: '/cwe/a/equipmentMaintenance/saveEquipmentMaintenance',
            method: 'post',
            data: { ...data }
        })
    }
}

export default new DictModel()
