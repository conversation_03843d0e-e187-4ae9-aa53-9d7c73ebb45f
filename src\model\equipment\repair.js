import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class DictModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': '',
            'type': '',
            'value': '',
            'remarks': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            offsetTop: 80,
            columns: [
                {
                    label: '维修单号',
                    prop: 'code',
                    slot: 'code',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '维修单号',
                        prop: 'filter_LIKES_code'
                    },
                    minWidth: 140
                },
                {
                    label: '申请人名称',
                    prop: 'applyUserName',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '申请人名称',
                        prop: 'filter_LIKES_applyUserName'
                    },
                    minWidth: 120
                },
                {
                    label: '申请部门名称',
                    prop: 'applySiteName',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '申请日期',
                    prop: 'applyDate',
                    slot: 'applyDate',
                    isShow: true,
                    minWidth: 140
                },
                {
                    label: '设备编号',
                    minWidth: 120,
                    prop: 'equipmentCode',
                    isFilter: true,
                    filter: {
                        label: '设备编号',
                        prop: 'filter_LIKES_equipmentCode'
                    },
                    isShow: true
                },
                {
                    label: '设备名称',
                    minWidth: 120,
                    prop: 'equipmentName',
                    isFilter: true,
                    filter: {
                        label: '设备名称',
                        prop: 'filter_LIKES_equipmentName'
                    },
                    isShow: true
                },
                {
                    label: '维修单位',
                    minWidth: 120,
                    prop: 'repairUnit',
                    isShow: true
                },
                {
                    label: '维修人员',
                    prop: 'repairUser',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '维修电话',
                    prop: 'repairPhone',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: 'opt',
                    prop: 'opt',
                    slot: 'opt',
                    minWidth: 120,
                    isShow: true
                }
            ]
        }
        super('/cwe/a/equipmentRepair', dataJson, form, tableOption)
    }

    async save (data) {
        return fetch({
            url: '/cwe/a/equipmentRepair/saveEquipmentRepair',
            method: 'post',
            data: { ...data }
        })
    }
}

export default new DictModel()
