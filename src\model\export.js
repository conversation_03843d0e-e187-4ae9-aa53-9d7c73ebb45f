import Model from '@/s-curd/src/model'

class AdPositionModel extends Model {
    constructor () {
        const dataJson = {
            'userId': '',
            'userName': '',
            'name': '',
            'state': '',
            'downloadUrl': '',
            'downloadTimes': '',
            'exportParam': '',
            'submitDate': '',
            'generateDate': '',
            'elapsedTime': '',
            'rowNumber': '',
            'errorMessage': '',
            'createBy': '',
            'createDate': '',
            'updateBy': '',
            'updateDate': '',
            'ext': '',
            'remarks': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [],
            rules: {}
        }
        const tableOption = {
            printAble: 'enterpriseStaff:printAble',
            showSetting: true,
            exportAble: 'enterpriseStaff:export',
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '名称',
                    prop: 'name',
                    isShow: true
                },
                {
                    label: '提交时间',
                    prop: 'submitDate',
                    isShow: true,
                    width: 135
                },
                {
                    label: '状态',
                    prop: 'state',
                    slot: 'state',
                    isShow: true
                },
                {
                    label: '生成时间',
                    prop: 'generateDate',
                    isShow: true,
                    width: 135
                },
                {
                    label: '耗时',
                    prop: 'elapsedTime',
                    isShow: true
                },
                {
                    label: '行数',
                    prop: 'rowNumber',
                    isShow: true
                },
                {
                    label: '错误',
                    prop: 'errorMessage',
                    isShow: true
                }
            ],
            actions: []
        }
        super('/cwe/a/export', dataJson, form, tableOption)
    }
}

export default new AdPositionModel()
