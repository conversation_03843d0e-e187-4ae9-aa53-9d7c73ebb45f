import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/applyPay`
class paymentorderModel extends Model {
    constructor() {
        const dataJson = {
            // attachmentList: [],
            applyDate: (new Date(new Date().getTime())).Format('yyyy-MM-dd'),//日期
            // contractId: '',//合同id
            // contractCode: '',//合同编号
            // contractName: '',//合同名称
            // productId: '',//产品id
            productName: '',//产品名称
            // productCode: '',//产品编码
            supplierId: '',// 供应商ID（乙方）
            supplierName: '',
            // coalType: '',//  类型-焦肥气瘦
            // coalCategory: '',//  煤的类型 原煤、精煤
            // price: '',//  煤炭价格
            // amount: '',//吨数
            // // amountLeft: '',//剩余吨数
            payWay: '',//付款方式
            // money: '',//付款金额
            // bank: '',//开户银行
            // cardNo: '',//卡号或账号
            // reviewMessage: '',//备注
            // reviewLogList: [],//日志

            // state: '',//状态
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'applyDate',
                    isShow: true,
                    fixed: 'left',
                    minWidth: 100,
                    align: "center"
                },
                {
                    label: '往来单位',
                    prop: 'supplierName',
                    isShow: true,
                    width: 170,
                    align: "center"
                },
                {
                    label: '合同编号',
                    prop: 'contractCode',
                    isShow: true,
                    width: 180,
                    align: "center"
                },
                {
                    label: '品名',
                    prop: 'productName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '状态',
                    prop: 'state',
                    slot: 'state',
                    isShow: true,
                },
                {
                    label: '煤炭价格',
                    prop: 'price',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '吨数',
                    prop: 'amount',
                    isShow: true,
                    minWidth: 50,
                    align: "center"
                },
                {
                    label: '支付方式',
                    prop: 'payWay',
                    slot: 'payWay',
                    isShow: true,
                },
                {
                    label: '付款类型',
                    prop: 'applyType',
                    slot: 'applyType',
                    isShow: true,
                },
                {
                    label: '收款人',
                    prop: 'payee',
                    isShow: true,
                    width: 170,
                    align: "center"
                },
                {
                    label: '付款金额',
                    prop: 'money',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '开户银行',
                    prop: 'bank',
                    isShow: true,
                    width: 120,
                    align: "center"
                },
                {
                    label: '卡号或账号',
                    prop: 'cardNo',
                    isShow: true,
                    width: 170,
                    align: "center"
                },
                {
                    label: '行号',
                    prop: 'bankNo',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '是否结算',
                    prop: 'isSettle',
                    slot: 'isSettle',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    slot: 'remarks',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '备注',
                    prop: 'purpose',
                    slot: 'purpose',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '附件列表',
                    prop: 'attachmentList',
                    slot: 'attachmentList',
                    isShow: true
                },
                {
                    label: '当前流程',
                    width: 150,
                    prop: 'curFlowName',
                    slot: 'curFlowName',
                    isShow: true,
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    isShow: true,
                    slot: 'opt'
                }
            ],
            summaries: [
                { prop: 'money', suffix: '', fixed: 2, avg: false }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    //作废付款单
    Cancel(data) {
        return fetch({
            url: `${name}/cancel`,
            method: 'post',
            data
        })
    }
    //获取采购甲方
    getContractParty(params = {}) {
        return fetch({
            url: '/cwe/a/contractParty/findBuyByKeyword',
            method: 'get',
            params: params
        })
    }
    async page(params = {}) {
        console.log('params')
        params.filter_EQS_applyType = 'REFUND'
        console.log(params)
        if (params.filter_INS_state == 'NEW,PASS_FINANCE') {
            params.filter_INS_state = ''
            const res = await fetch({
                url: `${name}/findWaitReviewPage`,
                method: 'get',
                params: params
            })
            return res
        } else {
            if (params.filter_INS_state == 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS') {
                params.filter_INS_state = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS,PAYED'
            }
            const res = await fetch({
                url: `${name}/page`,
                method: 'get',
                params: params
            })
            return res
        }
    }
    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }
    // 付款申请
    getApproved(data) {
        return fetch({
            url: `${name}/submit`,
            method: 'post',
            data
        })
    }
    //保存草稿
    SaveDraftfn(data) {
        return fetch({
            url: `${name}/saveDraft`,
            method: 'post',
            data
        })
    }
    // setsave保存
    SaveUpdatePaymentOptions(data) {
        return fetch({
            url: `${name}/updatePaymentOptions`,
            method: 'post',
            data
        })
    }



    //审核通过
    savepass(data) {
        return fetch({
            url: `${name}/pass `,
            method: 'post',
            data
        })
    }
    //拒绝
    savereject(data) {
        return fetch({
            url: `${name}/reject`,
            method: 'post',
            data
        })
    }
    //去付款、支付确认
    Collected(data) {
        return fetch({
            url: `${name}/payed`,
            method: 'post',
            data
        })
    }

    //获取待审核的数据条数
    getcountByState() {
        return fetch({
            url: `${name}/countByStateRefund`,
            method: 'get',
        })
    }

    //是否结算
    getupdateIsSettle(params) {
        return fetch({
            url: `${name}/updateIsSettle`,
            method: 'post',
            params
        })
    }

    findByApplyPayId(params) {
        return fetch({
            url: `/cwe/a/applyPayDetail/findByApplyPayId`,
            method: 'get',
            params
        })
    }

    payOnlySave(data) {
        return fetch({
            url: `/cwe/a/applyPay/payOnlySave`,
            method: 'post',
            data
        })
    }

    //获取审核流程
    getlclist(params = {}) {
        return fetch({
            url: `/cwe/a/flowDesign/findListByType`,
            method: 'get',
            params,
        })
    }

    // 获取当前付款单的流程
    findContractBuyFlow(id) {
        return fetch({
            url: `${name}/findApplyPayFlow`,
            method: 'get',
            params: { id }
        })
    }

    //获取销售合同卖方
    getSellContractParty(params = {}) {
        return fetch({
            url: '/cwe/a/contractParty/findSellByKeyword',
            method: 'get',
            params: params
        })
    }

}

export default new paymentorderModel()
