import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/applyPay`
class paymentorderModel extends Model {
    constructor() {
        const dataJson = {
            attachmentList: [],
            applyDate: '',//日期
            contractId: '',//合同id
            contractCode: '',//合同编号
            contractName: '',//合同名称
            productId: '',//产品id
            productName: '',//产品名称
            productCode: '',//产品编码
            supplierId: '',// 供应商ID（乙方）
            supplierName: '',
            coalType: '',//  类型-焦肥气瘦
            price: '',//  煤炭价格
            amount: '',//吨数
            payWay: '',//付款方式
            money: '',//付款金额
            bank: '',//开户银行
            cardNo: '',//卡号或账号
            reviewMessage: '',//备注
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'applyDate',
                    isShow: true,
                    // fixed: 'left',
                    minWidth: 100,
                    align: "center"
                },
                {
                    label: '付款类型',
                    prop: 'applyType',
                    slot: 'applyType',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '合同编号',
                //     prop: 'contractCode',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     label: '合同名称',
                //     prop: 'contractName',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    label: '品名',
                    prop: 'productName',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '产品编码',
                //     prop: 'productCode',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    label: '往来单位',
                    prop: 'supplierName',
                    isShow: true,
                    width: 170,
                    align: "center"
                },
                // {
                //     label: '煤种',
                //     prop: 'coalType',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     label: '煤的类型',
                //     prop: 'coalCategory',
                //     isShow: true,
                //     align: "center"
                // },

                {
                    label: '煤炭价格',
                    prop: 'price',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '吨数',
                    prop: 'amount',
                    isShow: true,
                    minWidth: 50,
                    align: "center"
                },
                {
                    label: '剩余吨数',
                    prop: 'amountLeft',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '付款方式',
                    prop: 'payWay',
                    slot: 'payWay',
                    isShow: true,
                    align: "center"
                },

                {
                    label: '收款人',
                    prop: 'payee',
                    isShow: true,
                    width: 170,
                    align: "center"
                },
                {
                    label: '付款金额',
                    prop: 'money',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '开户银行',
                    prop: 'bank',
                    isShow: true,
                    width: 120,
                    align: "center"
                },
                {
                    label: '卡号或账号',
                    prop: 'cardNo',
                    isShow: true,
                    width: 170,
                    align: "center"
                },
                {
                    label: '行号',
                    prop: 'bankNo',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '附件列表',
                    prop: 'attachmentList',
                    slot: 'attachmentList',
                    isShow: true
                },
                // {
                //     noExport: true,
                //     label: '操作',
                //     prop: 'opt',
                //     isShow: true,
                //     slot: 'opt'
                // }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }

    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }
    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }
    // 付款申请
    // getApproved(data) {
    //     return fetch({
    //         url: `${name}/submit`,
    //         method: 'post',
    //         data
    //     })
    // }
    //保存草稿
    // SaveDraftfn(data) {
    //     return fetch({
    //         url: `${name}/saveDraft`,
    //         method: 'post',
    //         data
    //     })
    // }
    //审核通过
    // savepass(data) {
    //     return fetch({
    //         url: `${name}/pass `,
    //         method: 'post',
    //         data
    //     })
    // }
    //拒绝
    // savereject(data) {
    //     return fetch({
    //         url: `${name}/reject`,
    //         method: 'post',
    //         data
    //     })
    // }
    //去付款、支付确认
    // Collected(data) {
    //     return fetch({
    //         url: `${name}/payed`,
    //         method: 'post',
    //         data
    //     })
    // }



}

export default new paymentorderModel()
