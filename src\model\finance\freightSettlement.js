import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import fetchPdf from '@/utils/fetchPdf'


import { typeName, COMPANY } from '@/const'
const name = `/cwe/a/settlePay`

class ContractBuyModel extends Model {
    constructor() {
        const dataJson = {
            applyDate: (new Date(new Date().getTime())).Format('yyyy-MM-dd'),
            productId: '',
            supplierId: '',
            supplierName: '',
            punishCleanStd: '',
            punishCleanAd: '',
            punishCleanVdaf: '',
            punishProcG: '',
            punishCleanMt: '',
            punishProcY: '',
            punishQualCsr: '',
            punishMacR0: '',
            punishRecovery: '',
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    width: 100,
                    prop: 'applyDate',
                    isShow: true,
                    fixed: 'left',
                    align: "center"
                },
                {
                    label: '发货单位',
                    prop: 'supplierName',
                    isShow: true,
                    width: 190,
                    align: "center",
                },
                {
                    label: '品名',
                    prop: 'productName',
                    slot: 'productName',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '原发车数',
                //     prop: 'truckCount',
                //     isShow: true,
                //     align: "center"
                // },

                {
                    label: '原发吨数',
                    prop: 'sendWeight',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '实收吨数',
                    prop: 'receiveWeight',
                    isShow: true,
                    align: "center"
                },

                // {
                //     label: '实收数',
                //     prop: 'receiveWeight',
                //     isShow: true,
                //     align: "center"
                // },
                // 应结金额 实结金额 结算单位
                {
                    label: '结算单位',
                    prop: 'supplierName',
                    isShow: true,
                    width: 190,
                    align: "center",
                },
                {
                    label: '应结金额',
                    prop: 'finalMoney',
                    // prop: 'settleMoney',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '实结金额',
                    prop: 'settleMoney',
                    // prop: 'finalMoney',
                    isShow: true,
                    align: "center"
                },

                // {
                //     label: '状态',
                //     prop: 'state',
                //     slot: 'state',
                //     isShow: true
                // },
                // {
                //     label: '结算类型',
                //     prop: 'applyType',
                //     slot: 'applyType',
                //     isShow: true
                // },

                // {
                //     label: '结算金额',
                //     prop: 'settleMoney',
                //     isShow: true,
                //     align: "center"
                // },

                // {
                //     label: '结算额',
                //     prop: 'finalMoney',
                //     isShow: true,
                //     align: "center"
                // },


                // {
                //     label: '结算状态',
                //     prop: 'isSettle',
                //     isShow: true,
                //     slot: 'isSettle',
                //     align: "center"
                // },

                // {
                //     label: '当前流程',
                //     width: 150,
                //     prop: 'curFlowName',
                //     slot: 'curFlowName',
                //     isShow: true,
                // },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                    width: 30
                }
            ],
            sumText: '合计',
            summaries: [
                { prop: 'settleMoney', suffix: '', fixed: 2, avg: false },
                { prop: 'sendWeight', suffix: '', fixed: 2, avg: false },
                { prop: 'receiveWeight', suffix: '', fixed: 2, avg: false }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    deleteId(id) {
        return fetch({
            url: `${name}/delete`,
            method: 'post',
            data: { id }
        })
    }
    //作废
    Cancel(data) {
        return fetch({
            url: `${name}/cancel`,
            method: 'post',
            data
        })
    }
    //获取采购甲方
    getContractParty(params = {}) {
        return fetch({
            url: '/cwe/a/contractParty/findBuyByKeyword',
            method: 'get',
            params: params
        })
    }
    // async page(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: params
    //     })
    //     res.data.records.forEach(item => {
    //         item.isPublicBoolean = item.isPublic === 'Y'
    //         item.typeName = typeName[item.coalCategory]
    //     })
    //     return res
    // }


    // async page(searchForm) {
    //     const res = await fetch({
    //         url: name + '/page',
    //         method: 'get',
    //         params: searchForm,
    //     })
    //     res.data.records = this.formatData(res.data.records)
    //     return res
    // }


    //获取采购发货单位
    getfindBuyAndSupplier(params = {}) {
        return fetch({
            url: '/cwe/a/contractParty/findBuyAndSupplierByKeyword',
            method: 'get',
            params: params
        })
    }
    async page(params = {}) {
        params.filter_EQS_applyType = 'CARRIAGE'
        if (params.filter_INS_state == 'NEW,PASS_FINANCE') {
            params.filter_INS_state = ''
            const res = await fetch({
                url: `${name}/findWaitReviewPage`,
                method: 'get',
                params: params
            })
            res.data.records = this.formatData(res.data.records)
            return res
        } else {
            const res = await fetch({
                url: `${name}/page`,
                method: 'get',
                params: params
            })
            res.data.records = this.formatData(res.data.records)
            return res
        }
    }



    formatData(records) {
        const _records = []
        records.forEach(val => {
            if (!val.settlePayItemList.length) {
                _records.push({ ...val })
            }
            val.settlePayItemList.forEach((item, idx) => {
                item.supplierName = val.supplierName
                if (idx === 0) {
                    _records.push({ ...val, ...item, id: val.id })
                } else {
                    _records.push({ ...item, id: val.id })
                }
            })
        })
        // console.log(_records)
        return _records
    }


    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }
    // // 付款申请
    // getApproved(data) {
    //     return fetch({
    //         url: `${name}/submit`,
    //         method: 'post',
    //         data
    //     })
    // }
    // 总经理点击审核的时候调
    // savepass(data) {
    //     return fetch({
    //         url: `${name}/pass `,
    //         method: 'post',
    //         data
    //     })
    // }
    // SaveDraftfn(data) {
    //     return fetch({
    //         url: `${name}/saveDraft`,
    //         method: 'post',
    //         data
    //     })
    // }
    // 财务审批的按钮接口
    getfinancePass(data) {
        return fetch({
            url: `${name}/financePass`,
            method: 'post',
            data
        })
    }
    savereject(data) {
        return fetch({
            url: `${name}/reject`,
            method: 'post',
            data
        })
    }
    // 打印
    getprint(id) {
        return fetchPdf({
            url: `${name}/print`,
            method: 'get',
            params: { id }
        })
    }
    getdownload(id) {
        return fetchPdf({
            url: `${name}/download`,
            method: 'get',
            params: { id }
        })
    }
    //获取待审核的数据条数
    getcountByState() {
        return fetch({
            url: `${name}/countByState`,
            method: 'get',
        })
    }

    // // 获取结算明细列表
    // async purchasedetailspage(params = {}, normal = false) {
    //     try {
    //         const res = await fetch({
    //             url: `/cwe/a/weightHouseIn/page`,
    //             method: 'get',
    //             params: params
    //         })
    //         if (!normal) this.formatRecordsV({ records: res.data.records, fields: ['mt', 'carriage', 'price', 'manCost', 'contractId', 'remarks'] })
    //         return res
    //     } catch (e) {
    //         // console.log(e)
    //     }
    // }

    // formatRecordsV({ records, fields }) {
    //     records.forEach(item => {
    //         item._all = false
    //         item.active = {}
    //         item.bak = {}
    //         for (const field of fields) {
    //             item.active[field] = false
    //             item.bak[field] = item[field]
    //         }
    //     })
    //     return records
    // }

    // //start 明细数据
    // //获取明细左侧列表接口
    // async findBySettlePay(searchForm) {
    //     const res = await fetch({
    //         url: '/cwe/a/weightHouseIn/findBySettlePay',
    //         method: 'get',
    //         params: searchForm,
    //     })
    //     return res
    // }

    // //获取明细右侧列表接口
    // async getWeightHouseInList(searchForm) {
    //     const res = await fetch({
    //         url: `${name}/getWeightHouseInList`,
    //         method: 'get',
    //         params: searchForm,
    //     })
    //     return res
    // }

    // //左边加到右边将加的数据的setlePayId,weightHouseInList传过去
    // async selectWeightHouseInList(data) {
    //     const res = await fetch({
    //         url: `${name}/selectWeightHouseInList`,
    //         method: 'post',
    //         data,
    //     })
    //     return res
    // }

    // //右边加到左边的数据的setlePayId,weightHouseInList传过去
    // async deSelectWeightHouseInList(data) {
    //     const res = await fetch({
    //         url: `${name}/deSelectWeightHouseInList`,
    //         method: 'post',
    //         data,
    //     })
    //     return res
    // }
    // //end 明细数据






    //start 运费明细列表
    //    左侧列表
    async FreightFindBySettlePay(searchForm) {
        const res = await fetch({
            url: `/cwe/a/weightHouseLog/findBySettlePay`,
            method: 'get',
            params: searchForm,
        })
        return res
    }
    // 运费结算右侧已选中的列表
    async getWeightHouseLogList(searchForm) {
        const res = await fetch({
            url: `/cwe/a/settlePay/getWeightHouseLogList`,
            method: 'get',
            params: searchForm,
        })
        return res
    }
    // 运费结算：选中保存
    async selectWeightHouseLogList(data) {
        const res = await fetch({
            url: `${name}/selectWeightHouseLogList`,
            method: 'post',
            data,
        })
        return res
    }
    // 运费结算：取消选中
    async deSelectWeightHouseLogList(data) {
        const res = await fetch({
            url: `${name}/deSelectWeightHouseLogList`,
            method: 'post',
            data,
        })
        return res
    }

    async findCarriageSettlePaySummary(searchForm) {
        const res = await fetch({
            url: `${name}/findCarriageSettlePaySummary`,
            method: 'get',
            params: searchForm,
        })
        return res
    }

    getContractParty(params = {}) {
        return fetch({
            url: '/cwe/a/contractParty/findBuyByKeyword',
            method: 'get',
            params: params
        })
    }


    //获取审核流程
    getlclist(params = {}) {
        return fetch({
            url: `/cwe/a/flowDesign/findListByType`,
            method: 'get',
            params,
        })
    }

    // 获取当前付款单的流程
    findContractBuyFlow(id) {
        return fetch({
            url: `${name}/findSettlePayFlow`,
            method: 'get',
            params: { id }
        })
    }
    // 获取化验指标
    getProductQualIndicator(params = {}) {
        return fetch({
            url: `/cwe/a/product/getProductQualIndicator`,
            method: 'get',
            params,
        })
    }
}
export default new ContractBuyModel()
