import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
// import { getToday } from '@/utils/index'
const name = `/cwe/a/weightHouseLog`
const dataJson = {
    freightPrice: '',
    // date: getToday(), // 日期
    // productId: '', // 品名Id
    // productCode: '', // 品名编码
    // name: '', // 品名
    // contractCode: '', // 合同编码
    // contractId: '', // 合同Id
    // supplierId: '', // 供应商ID -- 对应发货单位
    // supplierName: '', // 供应商名称 -- 对应发货单位
    // senderName: '', // 发货单位
    // receiverName: '', // 收货单位
    // coalType: '', // 煤种
    // truckCount: '', // 车数
    // sendWeight: '', // 原发数
    // receiveWeight: '', // 实收数
    // wayCost: '', // 途耗
    // truckCountAcc: 0, // 车数累计
    // sendWeightAcc: 0, // 原发数累计
    // receiveWeightAcc: 0, // 实收数累计
    // wayCostAcc: 0, // 途耗累计
    // stationMan: '', // 在岗人
    // transferMan: '', // 交班人
    // nextMan: '', // 接班人
    // remarks: '' // 备注信息

}
const form = {}
const tableOption = {
    // showIndex: true,
    showPage: true,
    showSelection: true,
    columns: [
        // fixed: 'left'
        // {
        //     label: '日期',
        //     prop: 'weightDate',
        //     isShow: true,
        //     sortable: true,
        //     width: 100,
        //     align: "center"
        // },
        {
            label: '日期',
            prop: 'date',
            isShow: true,
            sortable: true,
            width: 100,
            align: "center"
        },

        // {
        //     label: '过磅类型',
        //     prop: 'type',
        //     slot: 'type',
        //     isShow: true,
        // },
        // {
        //     label: '同步状态',
        //     prop: 'sync',
        //     slot: 'sync',
        //     isShow: true,
        //     align: "center"
        // },
        // {
        //     label: '企业名称',
        //     prop: 'companyFullName',
        //     isShow: true,
        //     slot: 'companyFullName',
        //     align: "center"
        // },

        {
            label: '供应商/发货单位',
            prop: 'supplierName',
            width: 180,
            isShow: true,
            align: "center"
        },

        {
            label: '品名',
            prop: 'cargoType',
            isShow: true,
            width: 80,
            align: "center"
        },


        {
            label: '车牌号',
            prop: 'plateNumber',
            isShow: true,
            align: "center"
        },
        // {
        //     label: '毛重',
        //     prop: 'grossWeight',
        //     slot: 'grossWeight',
        //     isShow: true,
        //     align: "center"
        // },
        // {
        //     label: '皮重',
        //     prop: 'truckWeight',
        //     slot: 'truckWeight',
        //     isShow: true,
        //     align: "center"
        // },
        {
            label: '毛重',
            prop: 'firstWeight',
            slot: 'firstWeight',
            width: 120,
            isShow: true,
            align: "center"
        },
        {
            label: '皮重',
            prop: 'secondWeight',
            slot: 'secondWeight',
            isShow: true,
            align: "center"
        },


        // {
        //     label: '第一次过磅值',
        //     prop: 'firstWeight',
        //     isShow: true,
        //     align: "center"
        // },
        // {
        //     label: '第二次过磅值',
        //     prop: 'secondWeight',
        //     isShow: true,
        //     align: "center"
        // },
        {
            label: '原发数',
            prop: 'originalTon',
            slot: 'originalTon',
            isShow: true,
            align: "center"
        },
        {
            label: '实收',
            prop: 'weight',
            slot: 'weight',
            isShow: true,
            align: "center"
        },
        {
            label: '扣水',
            prop: 'deductWater',
            isShow: true,
            align: "center"
        },
        {
            label: '超扣',
            prop: 'beyondNum',
            isShow: true,
            align: "center"
        },
        {
            label: '亏吨',
            prop: 'lossNull',
            sortable: true,
            isShow: true,
            align: "center"
        },

        {
            label: '运费吨数',
            prop: 'freightTon',
            slot: 'freightTon',
            isShow: true,
            align: "center"
        },

        {
            label: '亏吨基准',
            prop: 'lossNullBase',
            slot: 'lossNullBase',
            isShow: true,
            align: "center"
        },


        {
            label: '运价',
            prop: 'freightPrice',
            slot: 'freightPrice',
            isShow: true,
        },
        {
            label: '煤价',
            prop: 'price',
            isShow: true,
        },
        {
            label: '扣款',
            prop: 'deductMoney',
            isShow: true,
            slot: 'deductMoney',
            align: "center"
        },
        {
            label: '应结金额',
            prop: 'freighMoney',
            isShow: true,
            slot: 'freighMoney',
            align: "center"
        },

        // {
        //     label: '运费金额',
        //     prop: 'freighMoney',
        //     isShow: true,
        //     align: "center"
        // },
        // {
        //     label: '原发差量',
        //     prop: 'diffNum',
        //     isShow: true,
        //     align: "center"
        // },
        // {
        //     label: '扣重',
        //     prop: 'deductNum',
        //     isShow: true,
        //     align: "center"
        // },
        {
            label: '结算金额',
            prop: 'settleMoney',
            slot: 'settleMoney',
            isShow: true
        },
        {
            label: '是否有票',
            prop: 'isBill',
            isShow: true,
            align: "center",
            slot: 'isBill',
        },
        {
            label: '是否结算',
            prop: 'isSettle',
            isShow: true,
            align: "center",
            slot: 'isSettle',
        },
        {
            label: '备注',
            prop: 'remarks',
            slot: 'remarks',
            isShow: true,
            align: "center"
        },
        {
            label: '类型',
            prop: 'type',
            isShow: true,
            slot: 'type',
            align: "center"
        },

        {
            label: '一次计量时间',
            prop: 'firstWeightDate',
            slot: 'firstWeightDate',
            // defaultsort:true,
            isShow: true,
            sortable: true,
            width: 180,
            align: "center"
        },
        {
            label: '二次计量时间',
            prop: 'secondWeightDate',
            slot: 'secondWeightDate',
            // defaultsort:true,
            isShow: true,
            sortable: true,
            width: 180,
            align: "center"
        },
        {
            label: '磅单号',
            prop: 'autoBH',
            isShow: true,
            slot: 'autoBH',
            // width: 120,
            align: "center"
        },

        {
            label: '收货单位',
            prop: 'customerName',
            width: 180,
            isShow: true,
            align: "center"
        },
        {
            noExport: true,
            label: '操作',
            prop: 'opt',
            slot: 'opt',
            isShow: true
        }
    ],
    summaries: [
        { prop: 'plateNumber', suffix: '', fixed: 2 },
        { prop: 'grossWeight', suffix: '', fixed: 2 },
        { prop: 'truckWeight', suffix: '', fixed: 2 },
        { prop: 'originalTon', suffix: '', fixed: 2 },
        { prop: 'weight', suffix: '', fixed: 2 },
        { prop: 'lossNull', suffix: '', fixed: 2 },
        { prop: 'freighMoney', suffix: '', fixed: 2 },
        { prop: 'settleMoney', suffix: '', fixed: 2 },
    ]
}
class weightHouselogtableModel extends Model {
    constructor() {
        super(name, dataJson, form, tableOption)
    }
    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }



    // page(searchForm) {
    //     return fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: searchForm
    //     })
    // }

    async page(params = {}, normal = false) {
        params.filter_EQS_type = 2
        const res = await fetch({
            url: `${name}/page`,
            method: 'get',
            params: params
        })
        if (!normal) this.formatRecords({ records: res.data.records, fields: ['freightPrice', 'deductMoney', 'truckWeight', 'grossWeight', 'freighMoney', 'settleMoney', 'isBill', 'isSettle', 'remarks', 'originalTon', 'weight', 'lossNullBase', 'freightTon'] })
        return res
    }

    formatRecords({ records, fields }) {
        records.forEach(item => {
            item._all = false
            item.active = {}
            item.bak = {}
            for (const field of fields) {
                item.active[field] = false
                item.bak[field] = item[field]
            }
        })
        return records
    }

    getAccValues(params) {
        return fetch({
            url: `${name}/getAccValues`,
            method: 'get',
            params
        })
    }
    deleteId(id) {
        return fetch({
            url: this.name + '/deleteById',
            method: 'post',
            data: { id }
        })
    }
    refresh(data) {
        return fetch({
            url: '/cwe/a/weightHouseIn/refreshByDate',
            method: 'post',
            data
        })
    }
    refreshV(data) {
        return fetch({
            url: '/cwe/a/weightHouseOut/refreshByDate',
            method: 'post',
            data
        })
    }

    // //批量修改运费
    // savebatchChangeCarriage(data) {
    //     return fetch({
    //         url: `${name}/updateFreightPrice`,
    //         method: 'post',
    //         data
    //     })
    // }
    // //批量修改备注
    // savebatchChangeRemarks(data) {
    //     return fetch({
    //         url: `${name}/updateRemarks`,
    //         method: 'post',
    //         data
    //     })
    // }
    // //批量修改是否结算
    // savebatchChangeSettle(data) {
    //     return fetch({
    //         url: `${name}/updateIsSettle`,
    //         method: 'post',
    //         data
    //     })
    // }



    // updateBuyInFreightPrice(data) {
    //     return fetch({
    //         url: `${name}/updateBuyInFreightPrice`,
    //         method: 'post',
    //         data
    //     })
    // }


    //批量运费修改
    saveupdateiSFrPr(data) {
        return fetch({
            // url: `${name}/updateiSFrPr`,
            url: `${name}/updateBuyInFreightPrice`,
            method: 'post',
            data
        })
    }

    //批量修改亏吨基准
    saveupdateiSLossNullBase(data) {
        return fetch({
            // url: `${name}/updateiSFrPr`,
            url: `${name}/updateBuyInLossNullBase`,
            method: 'post',
            data
        })
    }


}

export default new weightHouselogtableModel()
