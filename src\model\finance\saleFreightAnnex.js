import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/sellOutDetailAttachment`
class paymentorderModel extends Model {
    constructor() {
        const dataJson = {
            name: '',//
            parentId: 0,
            parentIds: 0,
            attachmentList: []
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    isShow: true,
                    sortable: true,
                    width: 100,
                    align: "center"
                },
                {
                    label: '客户名称',
                    prop: 'customerName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '附件',
                    // attachmentList
                    prop: 'attachmentList',
                    slot: 'attachmentList',
                    isShow: true
                },

                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/saveSellOutDetailAttachment`,
            method: 'post',
            data
        })
    }

    page(searchForm) {
        searchForm.orderBy = 'date'
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }

    getUploadList(id) {
        return fetch({
            url: `${name}/getDetail`,
            method: 'get',
            params: { id }
        })
    }
}

export default new paymentorderModel()
