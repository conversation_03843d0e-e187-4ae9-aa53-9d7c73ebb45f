import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import { typeName, COMPANY } from '@/const'
const name = `/cwe/a/sbCustomerDay`
class ContractBuyModel extends Model {
    constructor() {
        const dataJson = {
            productId: '',
            productName: '',
            productCode: '',
            coalType: '', // 类型-焦肥气瘦
            coalCategory: '', // 煤的类型 原煤、精煤
            supplierId: '',
            amountLeft: 0,
            supplierName: '',
            price: '', // 煤价
            reviewMessage: '',
            sbDate: ''

        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '负责人',
                    prop: 'linkman',
                    width: 100,
                    fixed: 'left',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '合同',
                    width: 100,
                    prop: 'contractId',
                    slot: 'contractId',
                    isShow: true
                },
                {
                    label: '买方',
                    prop: 'customerName',
                    slot: 'customerId',
                    isShow: true,
                    align: "center"
                },

                {
                    label: '煤价',
                    prop: 'price',
                    isShow: true,
                    align: "center"
                },
                // 这个跟供应商里面的正好相反
                {
                    label: '原发吨数',
                    prop: 'amount',
                    isShow: true,
                    align: "center"
                },

                {
                    label: '实收吨数',
                    prop: 'receiveAmount',
                    isShow: true,
                    align: "center"
                },


                {
                    label: '收款',
                    prop: 'actualMoney',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '预结算金额',
                    prop: 'preSettleMoney',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '结算金额',
                    prop: 'settleMoney',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '余额',
                    prop: 'leftMoney',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '余吨',
                    prop: 'leftAmount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '日期',
                    width: 200,
                    prop: 'createDate',
                    isShow: true,
                    sortable: true,
                    align: "center"
                },
                {
                    label: '合同名称',
                    width: 100,
                    prop: 'contractName',
                    slot: 'contractName',
                    fixed: 'left',
                    isShow: true,
                },
                {
                    label: '是否有票',
                    prop: 'isBill',
                    isShow: true,
                    align: "center",
                    slot: 'isBill',
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    width: 160,
                    isShow: true,
                    align: "center"
                },
                // {
                //     noExport: true,
                //     label: '操作',
                //     prop: 'opt',
                //     slot: 'opt',
                //     isShow: true,
                //     width: 30
                // }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    // async page(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: params
    //     })
    //     res.data.records.forEach(item => {
    //         item.isPublicBoolean = item.isPublic === 'Y'
    //         item.typeName = typeName[item.coalCategory]
    //     })
    //     return res
    // }


    // async page(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/list`,
    //         method: 'get',
    //         params: params
    //     })
    //     res.data.forEach(item => {
    //         item.isPublicBoolean = item.isPublic === 'Y'
    //         item.typeName = typeName[item.coalCategory]
    //     })
    //     return res
    // }

    async page(params = {}) {
        const res = await fetch({
            url: `${name}/list`,
            method: 'get',
            params: params
        })
        this.formatRecords({ records: res.data, fields: ['remarks', 'carriageRemarks'] })
        return res
    }
    formatRecords({ records, fields }) {
        records.forEach(item => {
            item._all = false
            item.active = {}
            item.bak = {}
            for (const field of fields) {
                item.active[field] = false
                item.bak[field] = item[field]
            }
        })
        return records
    }

    //确认结清
    settlef(data) {
        return fetch({
            url: `${name}/settle`,
            method: 'post',
            data
        })
    }


    // async page(searchForm) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: searchForm,
    //     })
    //     res.data.records = this.formatData(res.data.records)
    //     console.log(res, 'res')
    //     return res
    // }
    formatData(records) {
        const _records = []
        records.forEach(val => {
            val.isPublicBoolean = val.isPublic === 'Y'
            val.typeName = typeName[val.coalCategory]
            _records.push({ ...val })
        })
        this.sortArr(_records, 'linkman')
        return _records
    }

    sortArr(arr, str) {
        // console.log(arr)
        let _arr = [],
            _t = [],
            // 临时的变量
            _tmp

        // 按照特定的参数将数组排序将具有相同值得排在一起
        arr = arr.sort(function (a, b) {
            let s = a[str],
                t = b[str]
            return s < t ? -1 : 1
        })
        if (arr.length) {
            _tmp = arr[0][str]
        }
        // console.log( arr );
        // 将相同类别的对象添加到统一个数组
        for (let i in arr) {
            if (arr[i][str] === _tmp) {
                // console.log(_tmp)
                _t.push(arr[i])
            } else {
                _tmp = arr[i][str]
                _arr.push(_t)
                _t = [arr[i]]
            }
        }
        // 将最后的内容推出新数组
        _arr.push(_t)
        // console.log(_arr)
        return _arr
    }








    async refresh(data) {
        return fetch({
            url: `${name}/refresh`,
            method: 'post',
            data
        })
    }

    // getUploadList(id) {
    //     return fetch({
    //         url: `${name}/get`,
    //         method: 'get',
    //         params: { id }
    //     })
    // }
    // 付款申请
    // getApproved(data) {
    //     return fetch({
    //         url: `${name}/submit`,
    //         method: 'post',
    //         data
    //     })
    // }
    // // 总经理点击审核的时候调
    // savepass(data) {
    //     return fetch({
    //         url: `${name}/pass `,
    //         method: 'post',
    //         data
    //     })
    // }
    // SaveDraftfn(data) {
    //     return fetch({
    //         url: `${name}/saveDraft`,
    //         method: 'post',
    //         data
    //     })
    // }
    // 财务审批的按钮接口
    // getfinancePass(data) {
    //     return fetch({
    //         url: `${name}/financePass`,
    //         method: 'post',
    //         data
    //     })
    // }
    // savereject(data) {
    //     return fetch({
    //         url: `${name}/reject`,
    //         method: 'post',
    //         data
    //     })
    // }

}
export default new ContractBuyModel()
