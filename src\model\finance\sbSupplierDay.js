import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import { typeName, COMPANY } from '@/const'
const name = `/cwe/a/sbSupplierDay`

class ContractBuyModel extends Model {
    constructor() {
        const dataJson = {
            sbDate: (new Date(new Date().getTime())).Format('yyyy-MM-dd'), // 日期
            productId: '',
            productName: '',
            productCode: '',
            coalType: '', // 类型-焦肥气瘦
            coalCategory: '', // 煤的类型 原煤、精煤
            supplierId: '',
            amountLeft: 0,
            supplierName: '',
            price: '', // 煤价
            reviewMessage: '',
        }
        const form = {}
        const tableOption = {
            columns: [
                // {
                //     label: '日期',
                //     width: 100,
                //     prop: 'sbDate',
                //     sortable: true,
                //     isShow: true,
                //     fixed: 'left',
                //     align: "center"
                // },
                {
                    label: '合同',
                    width: 100,
                    prop: 'contractId',
                    slot: 'contractId',
                    fixed: 'left',
                    isShow: true,
                },

                {
                    label: '卖方',
                    width: 100,
                    prop: 'supplierId',
                    slot: 'supplierId',
                    fixed: 'left',
                    isShow: true,
                },

                {
                    label: '负责人',
                    prop: 'linkman',
                    isShow: true,
                    align: "center"
                },

                {
                    label: '名称',
                    prop: 'productName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '单价',
                    prop: 'price',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '实收吨数',
                    prop: 'amount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '原发吨数',
                    prop: 'sendAmount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '付款',
                    prop: 'actualMoney',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '预结算金额',
                    prop: 'preSettleMoney',
                    isShow: true,
                    align: "center"
                },

                {
                    label: '结算金额',
                    prop: 'settleMoney',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '余额',
                    prop: 'leftMoney',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '余吨',
                    prop: 'leftAmount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '更新时间',
                    width: 80,
                    prop: 'sbDate',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '合同名称',
                    width: 100,
                    prop: 'contractName',
                    slot: 'contractName',
                    fixed: 'left',
                    isShow: true,
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    slot: 'remarks',
                    isShow: true,
                    align: "center"
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                    width: 30
                }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    // async page(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: params
    //     })
    //     res.data.records.forEach(item => {
    //         item.isPublicBoolean = item.isPublic === 'Y'
    //         item.typeName = typeName[item.coalCategory]
    //     })
    //     return res
    // }


    //确认结清
    settlef(data) {
        return fetch({
            url: `${name}/settle`,
            method: 'post',
            data
        })
    }

    // async page(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/list`,
    //         method: 'get',
    //         params: params
    //     })
    //     res.data.forEach(item => {
    //         item.isPublicBoolean = item.isPublic === 'Y'
    //         item.typeName = typeName[item.coalCategory]
    //     })
    //     return res
    // }



    async page(params = {}) {
        const res = await fetch({
            url: `${name}/list`,
            method: 'get',
            params: params
        })
        this.formatRecords({ records: res.data, fields: ['remarks', 'carriageRemarks'] })
        return res
    }
    formatRecords({ records, fields }) {
        records.forEach(item => {
            item._all = false
            item.active = {}
            item.bak = {}
            for (const field of fields) {
                item.active[field] = false
                item.bak[field] = item[field]
            }
        })
        return records
    }













    // async page(searchForm) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: searchForm,
    //     })
    //     res.data.records = this.formatData(res.data.records)
    //     console.log(res, 'res')
    //     return res
    // }
    formatData(records) {
        const _records = []
        records.forEach(val => {
            val.isPublicBoolean = val.isPublic === 'Y'
            val.typeName = typeName[val.coalCategory]
            _records.push({ ...val })
        })
        this.sortArr(_records, 'contractId')
        return _records
    }

    sortArr(arr, str) {
        // console.log(arr)
        let _arr = [],
            _t = [],
            // 临时的变量
            _tmp
        // 按照特定的参数将数组排序将具有相同值得排在一起
        arr = arr.sort(function (a, b) {
            let s = a[str],
                t = b[str]
            return s < t ? -1 : 1
        })
        if (arr.length) {
            _tmp = arr[0][str]
        }
        // console.log( arr );
        // 将相同类别的对象添加到统一个数组
        for (let i in arr) {
            if (arr[i][str] === _tmp) {
                // console.log(_tmp)
                _t.push(arr[i])
            } else {
                _tmp = arr[i][str]
                _arr.push(_t)
                _t = [arr[i]]
            }
        }
        // 将最后的内容推出新数组
        _arr.push(_t)
        // console.log(_arr)
        // let newarry = []
        // for (var i = 0; i < _arr.length; i++) {
        //     let dd = _arr[i]
        //     for (var j = 0; j < dd.length; j++) {
        //         newarry.push(dd[j])
        //     }
        // }
        // console.log(newarry)
        // this.sortArr2(newarry, 'contractId')
        return _arr
    }


    sortArr2(arr2, str) {
        // console.log(str)
        let _arr1 = [],
            _t = [],
            // 临时的变量
            _tmp
        arr2 = arr2.sort(function (a, b) {
            // console.log(a)
            // console.log(a[str])
            // console.log(b[str])
            let y = a[str],
                z = b[str]
            return y < z ? -1 : 1
        })

        if (arr2.length) {
            _tmp = arr2[0][str]
        }
        // console.log(arr2);
        // 将相同类别的对象添加到统一个数组
        for (let i in arr2) {
            if (arr2[i][str] === _tmp) {
                // console.log(_tmp)
                _t.push(arr2[i])
            } else {
                _tmp = arr2[i][str]
                _arr1.push(_t)
                _t = [arr2[i]]
            }
        }
        // 将最后的内容推出新数组
        _arr1.push(_t)
        // console.log(_arr1)
        return _arr1
    }













    async refresh(data) {
        return fetch({
            url: `${name}/refresh`,
            method: 'post',
            data
        })
    }
    // getUploadList(id) {
    //     return fetch({
    //         url: `${name}/get`,
    //         method: 'get',
    //         params: { id }
    //     })
    // }
    // // 付款申请
    // getApproved(data) {
    //     return fetch({
    //         url: `${name}/submit`,
    //         method: 'post',
    //         data
    //     })
    // }
    // // 总经理点击审核的时候调
    // savepass(data) {
    //     return fetch({
    //         url: `${name}/pass `,
    //         method: 'post',
    //         data
    //     })
    // }
    // SaveDraftfn(data) {
    //     return fetch({
    //         url: `${name}/saveDraft`,
    //         method: 'post',
    //         data
    //     })
    // }
    // // 财务审批的按钮接口
    // getfinancePass(data) {
    //     return fetch({
    //         url: `${name}/financePass`,
    //         method: 'post',
    //         data
    //     })
    // }
    // savereject(data) {
    //     return fetch({
    //         url: `${name}/reject`,
    //         method: 'post',
    //         data
    //     })
    // }
    // 余额结转
    settleBalance(data) {
        return fetch({
            url: `${name}/settleBalance`,
            method: 'post',
            data
        })
    }
}
export default new ContractBuyModel()
