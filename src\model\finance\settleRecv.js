import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import { typeName, COMPANY } from '@/const'
import fetchPdf from '@/utils/fetchPdf'
const name = `/cwe/a/settleRecv`

class ContractBuyModel extends Model {
    constructor() {
        const dataJson = {
            applyDate: (new Date(new Date().getTime())).Format('yyyy-MM-dd'),
            productId: '',
            productName: '',
            productCode: '',
            coalType: '', // 类型-焦肥气瘦
            coalCategory: '', // 煤的类型 原煤、精煤
            customerId: '',
            customerName: '',
            // price: '', // 煤价
            // reviewMessage: '',

        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    width: 100,
                    prop: 'applyDate',
                    isShow: true,
                    fixed: 'left',
                    align: "center"
                },
                {
                    label: '结算单位',
                    prop: 'customerName',
                    isShow: true,
                    width: 170,
                    align: "center"
                },
                {
                    label: '合同编码',
                    width: 150,
                    prop: 'contractCode',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '品名',
                    prop: 'productName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '状态',
                    prop: 'state',
                    slot: 'state',
                    isShow: true,
                },
                {
                    label: '结算吨数',
                    prop: 'settleWeight',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '结算金额',
                    prop: 'settleMoney',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '原发车数',
                    prop: 'truckCount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '实收数',
                    prop: 'receiveWeight',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '原发重量',
                    prop: 'sendWeight',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '结算单价',
                    prop: 'price',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '惩罚原因',
                //     prop: 'punishReason',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     label: '扣罚金',
                //     prop: 'punishMoney',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     label: '奖励金',
                //     prop: 'awardMoney',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     width: 120,
                //     label: '奖励原因',
                //     prop: 'awardReason',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     label: '奖金类型',
                //     prop: 'awardType',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     label: '惩罚类型',
                //     prop: 'punishType',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    label: '结算周期',
                    prop: 'beginDate',
                    isShow: true,
                    slot: 'beginDate',
                    align: "center"
                },
                {
                    label: '结算周期',
                    prop: 'endDate',
                    isShow: true,
                    slot: 'endDate',
                    align: "center"
                },
                // {
                //     label: '结算额',
                //     prop: 'finalMoney',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     label: '结算状态',
                //     prop: 'isSettle',
                //     isShow: true,
                //     slot: 'isSettle',
                //     align: "center"
                // },
                {
                    label: '备注',
                    prop: 'remarks',
                    width: 250,
                    isShow: true,
                    align: "center"
                },
                {
                    label: '当前流程',
                    width: 150,
                    prop: 'curFlowName',
                    slot: 'curFlowName',
                    isShow: true,
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                    width: 30
                }
            ],
            summaries: [
                { prop: 'settleMoney', suffix: '', fixed: 2, avg: false }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        data.applyType = 'TRADE'
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    // async page(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: params
    //     })
    //     res.data.records.forEach(item => {
    //         item.isPublicBoolean = item.isPublic === 'Y'
    //         item.typeName = typeName[item.coalCategory]
    //     })
    //     return res
    // }
    async page(params = {}) {
        params.filter_LIKES_applyType = 'TRADE'
        if (params.filter_INS_state == 'NEW,PASS_FINANCE') {
            params.filter_INS_state = ''
            const res = await fetch({
                url: `${name}/findWaitReviewPage`,
                method: 'get',
                params: params
            })
            res.data.records = res.data.records
            return res
        } else {
            const res = await fetch({
                url: `${name}/page`,
                method: 'get',
                params: params
            })
            res.data.records = res.data.records
            return res
        }
    }
    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }
    // 付款申请
    getApproved(data) {
        data.applyType = 'TRADE'
        return fetch({
            url: `${name}/submit`,
            method: 'post',
            data
        })
    }
    // 总经理点击审核的时候调
    savepass(data) {
        data.applyType = 'TRADE'
        return fetch({
            url: `${name}/pass `,
            method: 'post',
            data
        })
    }
    SaveDraftfn(data) {
        data.applyType = 'TRADE'
        return fetch({
            url: `${name}/saveDraft`,
            method: 'post',
            data
        })
    }
    // 财务审批的按钮接口
    // getfinancePass(data) {
    //     return fetch({
    //         url: `${name}/financePass`,
    //         method: 'post',
    //         data
    //     })
    // }
    savereject(data) {
        data.applyType = 'TRADE'
        return fetch({
            url: `${name}/reject`,
            method: 'post',
            data
        })
    }

    // 打印
    getprint(id) {
        return fetchPdf({
            url: `${name}/print`,
            method: 'get',
            params: { id }
        })
    }


    //获取待审核的数据条数
    getcountByState() {
        return fetch({
            url: `${name}/countByState`,
            method: 'get',
        })
    }

    // start结算明细
    // 获取结算明细列表
    async purchasedetailspage(params = {}, normal = false) {
        try {
            const res = await fetch({
                url: `/cwe/a/weightHouseOut/page`,
                method: 'get',
                params: params
            })
            if (!normal) this.formatRecordsV({ records: res.data.records, fields: ['mt', 'carriage', 'price', 'manCost', 'contractId', 'remarks'] })
            return res
        } catch (e) {
            // console.log(e)
        }
    }

    formatRecordsV({ records, fields }) {
        records.forEach(item => {
            item._all = false
            item.active = {}
            item.bak = {}
            for (const field of fields) {
                item.active[field] = false
                item.bak[field] = item[field]
            }
        })
        return records
    }
    //作废
    Cancel(data) {
        return fetch({
            url: `${name}/cancel`,
            method: 'post',
            data
        })
    }
    //获取销售合同卖方
    getSellContractParty(params = {}) {
        return fetch({
            url: '/cwe/a/contractParty/findSellByKeyword',
            method: 'get',
            params: params
        })
    }

    //start 明细数据
    //获取明细左侧列表接口
    async findBySettlePay(searchForm) {
        const res = await fetch({
            url: '/cwe/a/weightHouseOut/findBySettlePay',
            method: 'get',
            params: searchForm,
        })
        return res
    }

    //获取明细右侧列表接口
    async getWeightHouseOutList(searchForm) {
        const res = await fetch({
            url: `${name}/getWeightHouseOutList`,
            method: 'get',
            params: searchForm,
        })
        return res
    }

    //左边加到右边将加的数据的setlePayId,weightHouseInList传过去
    async selectWeightHouseOutList(data) {
        const res = await fetch({
            url: `${name}/selectWeightHouseOutList`,
            method: 'post',
            data,
        })
        return res
    }

    //右边加到左边的数据的setlePayId,weightHouseInList传过去
    async deSelectWeightHouseOutList(data) {
        const res = await fetch({
            url: `${name}/deSelectWeightHouseOutList`,
            method: 'post',
            data,
        })
        return res
    }
    //end 明细数据

    getApproved(data) {
        return fetch({
            url: `${name}/submit`,
            method: 'post',
            data
        })
    }
    //获取审核流程
    getlclist(params = {}) {
        return fetch({
            url: `/cwe/a/flowDesign/findListByType`,
            method: 'get',
            params,
        })
    }

    // 获取当前付款单的流程
    findContractBuyFlow(id) {
        return fetch({
            url: `${name}/findSettleRecvFlow`,
            method: 'get',
            params: { id }
        })
    }
    // 获取化验指标
    getProductQualIndicator(params = {}) {
        return fetch({
            url: `/cwe/a/product/getProductQualIndicator`,
            method: 'get',
            params,
        })
    }

}
export default new ContractBuyModel()
