import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/weightHouseNotice`
class paymentorderModel extends Model {
    constructor() {
        const dataJson = {
            // attachmentList: [],
            // beginDate: '',
            beginDate: (new Date(new Date().getTime())).Format('yyyy-MM-dd'), //日期
            contractId: '',//合同id
            // contractCode: '',//合同编号
            // contractName: '',//合同名称
            // productId: '',//产品id
            productName: '',//产品名称
            // productCode: '',//产品编码
            supplierId: '',// 供应商ID（卖方）
            supplierName: '',
            // coalType: '',//  类型-焦肥气瘦
            // coalCategory: '',//  煤的类型 原煤、精煤
            price: '',//  煤炭价格
            // amount: '',//吨数
            // amountLeft: '',//剩余吨数
            // payWay: '',//付款方式
            // money: '',//付款金额
            // bank: '',//开户银行
            // cardNo: '',//卡号或账号
            // reviewMessage: '',//备注

            // state: '',//状态
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'beginDate',
                    isShow: true,
                    fixed: 'left',
                    minWidth: 100,
                    align: "center"
                },
                {
                    label: '类型',
                    prop: 'type',
                    slot: 'type',
                    isShow: true,
                    fixed: 'left',
                    align: "center"
                },
                {
                    label: '合同',
                    prop: 'contractCode',
                    isShow: true,
                    minWidth: 150,
                    align: "center"
                },
                {
                    label: '供发开始日期',
                    prop: 'createDate',
                    isShow: true,
                    minWidth: 160,
                    align: "center"
                },


                {
                    label: '供货商|客户',
                    prop: 'otherName',
                    isShow: true,
                    minWidth: 200,
                    align: "center"
                },
                {
                    label: '产品名称',
                    prop: 'productName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '产品编码',
                    prop: 'productCode',
                    isShow: true,
                    width: 170,
                    align: "center"
                },
                {
                    label: '煤种',
                    prop: 'coalType',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '煤的类型',
                //     prop: 'coalCategory',
                //     isShow: true,
                //     fixed: 'left'
                // },
                {
                    label: '煤炭价格',
                    prop: 'price',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '吨数',
                    prop: 'amount',
                    isShow: true,
                    minWidth: 50
                },
                {
                    label: '灰分',
                    prop: 'cleanAd',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '硫',
                    prop: 'cleanStd',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '挥发分',
                    prop: 'cleanVdaf',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '指数',
                    prop: 'procG',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '回收',
                    prop: 'recover',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '付款金额',
                //     prop: 'money',
                //     isShow: true,
                //     fixed: 'left'
                // },
                // {
                //     label: '开户银行',
                //     prop: 'bank',
                //     isShow: true,
                //     fixed: 'left'
                // },
                // {
                //     label: '卡号或账号',
                //     prop: 'cardNo',
                //     isShow: true,
                //     fixed: 'left'
                // },
                {
                    label: '状态',
                    prop: 'state',
                    slot: 'state',
                    isShow: true,
                },
                {
                    label: '附件列表',
                    prop: 'attachmentList',
                    slot: 'attachmentList',
                    isShow: true
                },
                // {
                //     label: '操作人',
                //     prop: 'Operator',
                //     isShow: true,
                //     fixed: 'left'
                // },
                // {
                //     label: '主管',
                //     prop: 'executivedirector',
                //     isShow: true,
                //     fixed: 'left'
                // },
                // {
                //     label: '备注',
                //     prop: 'remarks',
                //     isShow: true,
                //     fixed: 'left'
                // },

                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    isShow: true,
                    slot: 'opt'
                }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }

    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }
    // async page(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: params
    //     })
    //     res.data.records.forEach(item => {
    //         item.isPublicBoolean = item.isPublic === 'Y'
    //         item.typeName = typeName[item.coalCategory]
    //     })
    //     return res
    // }

    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }
    // 付款申请
    getApproved(data) {
        return fetch({
            url: `${name}/submit`,
            method: 'post',
            data
        })
    }
    //保存草稿
    SaveDraftfn(data) {
        return fetch({
            url: `${name}/saveDraft`,
            method: 'post',
            data
        })
    }
    //审核通过
    savepass(data) {
        return fetch({
            url: `${name}/pass `,
            method: 'post',
            data
        })
    }
    //拒绝
    savereject(data) {
        return fetch({
            url: `${name}/reject`,
            method: 'post',
            data
        })
    }
    //去付款、支付确认
    Collected(data) {
        return fetch({
            url: `${name}/payed`,
            method: 'post',
            data
        })
    }
    //发送短信
    SendSMSfn(data) {
        return fetch({
            url: `${name}/sendMsm`,
            method: 'post',
            data
        })
    }
    //获取短信内容
    getSms(id) {
        return fetch({
            url: `${name}/getSms`,
            method: 'get',
            params: { id }
        })
    }
    //获取待审核的数据条数
    getcountByState() {
        return fetch({
            url: `${name}/countByState`,
            method: 'get',
        })
    }

}

export default new paymentorderModel()
