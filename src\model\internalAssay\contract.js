import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import fetchPdf from '@/utils/fetchPdf'

class EntrustOrder extends Model {
    constructor () {
        const dataJson = {
            'name': '',
            'applayUserName': '',
            'location': '',
            'sampleDate': '',
            'taxCompanyName': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [],
            rules: {}
        }
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            // offsetTop: 30,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '合同单号',
                    prop: 'code',
                    slot: 'code',
                    minWidth: 120,
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '合同单号',
                        prop: 'filter_LIKES_code'
                    }
                },
                {
                    label: '客户名称',
                    prop: 'applayUserName',
                    minWidth: 120,
                    isFilter: true,
                    filter: {
                        label: '客户名称',
                        prop: 'filter_LIKES_applayUserName'
                    },
                    isShow: true
                },
                {
                    label: '客户电话',
                    prop: 'samplePersonTel',
                    minWidth: 120,
                    isFilter: true,
                    filter: {
                        label: '客户电话',
                        prop: 'filter_LIKES_samplePersonTel'
                    },
                    isShow: true
                },
                {
                    label: '应收金额',
                    prop: 'receivableFee',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '实收金额',
                    prop: 'receivedFee',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '送样时间',
                    prop: 'sampleDate',
                    isFilter: true,
                    minWidth: 140,
                    filter: {
                        label: '样品',
                        end: 'filter_LED_sampleDate',
                        start: 'filter_GED_sampleDate'
                    },
                    component: 'DateRanger',
                    isShow: true
                },
                {
                    label: '省市区',
                    prop: 'location',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '委托状态',
                    prop: 'assayStatus',
                    minWidth: 120,
                    format: 'b_assay_status_type',
                    isShow: true
                },
                {
                    label: '公司名称',
                    minWidth: 120,
                    prop: 'taxCompanyName',
                    isShow: true
                },
                {
                    label: '电话',
                    minWidth: 120,
                    prop: 'taxPhone',
                    isShow: true
                },
                {
                    label: '创建人',
                    prop: 'createBy',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '创建时间',
                    minWidth: 140,
                    prop: 'createDate',
                    isShow: true
                },
                {
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            actions: []
        }
        super('/cwe/a/assayContract', dataJson, form, tableOption)
    }

    saveAssayContract (data) {
        return fetch({
            url: '/cwe/a/assayContract/saveAssayContractNB',
            method: 'post',
            data: { ...data }
        })
    }

    getItemApi (keyword) {
        return fetch({
            url: '/cwe/a/assayItem/list',
            method: 'get',
            params: { keyword }
        })
    }

    saveAssayApplyByContract (data) {
        return fetch({
            url: '/cwe/a/assayApply/saveAssayApplyByContractNB',
            method: 'post',
            data: { ...data }
        })
    }

    findByAssayContractId (data) {
        return fetch({
            url: '/cwe/a/assayApply/findByAssayContractId',
            method: 'get',
            params: { ...data }
        })
    }

    deleteAssayContract (id) {
        return fetch({
            url: '/cwe/a/assayContract/deleteAssayContractNB',
            method: 'post',
            data: { id }
        })
    }

    printAssayContract (data) {
        return fetchPdf({
            url: '/cwe/a/assayContract/printAssayContract',
            method: 'get',
            params: { ...data }
        })
    }
}

export default new EntrustOrder()
