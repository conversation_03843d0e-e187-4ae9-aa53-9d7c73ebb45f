import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import fetchPdf from '@/utils/fetchPdf'

class EntrustOrder extends Model {
    constructor () {
        const dataJson = {
            'name': '',
            'state': '',
            'downloadUrl': '',
            'downloadTimes': '',
            'exportParam': '',
            'submitDate': '',
            'generateDate': '',
            'elapsedTime': '',
            'rowNumber': '',
            'errorMessage': '',
            'createBy': '',
            'createDate': '',
            'updateBy': '',
            'updateDate': '',
            'ext': '',
            'remarks': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [],
            rules: {}
        }
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            offsetTop: -30,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '样品名称',
                    prop: 'name',
                    slot: 'name',
                    minWidth: 120,
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '样品名称',
                        prop: 'filter_LIKES_name'
                    }
                },
                {
                    label: '合同单号',
                    prop: 'assayContractCode',
                    minWidth: 120,
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '合同单号',
                        prop: 'filter_LIKES_assayContractCode'
                    }
                },
                {
                    label: '样品编号',
                    prop: 'code',
                    minWidth: 120,
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '样品编号',
                        prop: 'filter_LIKES_code'
                    }
                },
                {
                    label: '品种',
                    prop: 'type',
                    minWidth: 120,
                    format: 's_assay_coal_type',
                    isShow: true
                },
                {
                    label: '客户名称',
                    prop: 'applayUserName',
                    minWidth: 120,
                    isFilter: true,
                    filter: {
                        label: '客户名称',
                        prop: 'filter_LIKES_applayUserName'
                    },
                    isShow: true
                },
                {
                    label: '客户电话',
                    prop: 'samplePersonTel',
                    minWidth: 120,
                    isFilter: true,
                    filter: {
                        label: '客户电话',
                        prop: 'filter_LIKES_samplePersonTel'
                    },
                    isShow: true
                },
                {
                    label: '收样人',
                    prop: 'recvSamplePerson',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '送样人',
                    prop: 'samplePerson',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '送样时间',
                    prop: 'sampleDate',
                    isFilter: true,
                    minWidth: 150,
                    filter: {
                        label: '送样时间',
                        end: 'filter_LED_sampleDate',
                        start: 'filter_GED_sampleDate'
                    },
                    component: 'DateRanger',
                    isShow: true
                },
                {
                    label: '省市区',
                    prop: 'location',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '是否收样',
                    prop: 'isSample',
                    format: 's_yes_or_no',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '委托状态',
                    prop: 'assayStatus',
                    minWidth: 120,
                    format: 'b_assay_status_type',
                    isFilter: true,
                    filter: {
                        label: '样品',
                        prop: 'filter_EQS_assayStatus',
                        type: 'b_assay_status_type'
                    },
                    component: 'DictSelect',
                    isShow: true
                },
                {
                    label: '公司名称',
                    minWidth: 120,
                    prop: 'taxCompanyName',
                    isShow: true
                },
                {
                    label: '电话',
                    minWidth: 120,
                    prop: 'taxPhone',
                    isShow: true
                },
                {
                    label: '创建人',
                    prop: 'createBy',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '创建时间',
                    minWidth: 140,
                    prop: 'createDate',
                    isShow: true
                },
                {
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            actions: []
        }
        super('/cwe/a/assayApply', dataJson, form, tableOption)
    }

    getItemApi (keyword) {
        return fetch({
            url: '/cwe/a/assayItem/list',
            method: 'get',
            params: { keyword }
        })
    }

    saveAssayApplyApi (data) {
        return fetch({
            url: '/cwe/a/assayApply/saveAssayApply',
            method: 'post',
            data: { ...data }
        })
    }

    receiveSample (data) {
        return fetch({
            url: '/cwe/a/assayApply/receiveSample',
            method: 'post',
            data: { ...data }
        })
    }

    handleSaveAssayApplyDraft (data) {
        return fetch({
            url: '/cwe/a/assayApply/saveAssayApplyDraft',
            method: 'post',
            data: { ...data }
        })
    }

    printAssay (data) {
        return fetchPdf({
            url: '/cwe/a/assayApply/printAssay',
            method: 'get',
            params: { ...data }
        })
    }

    printAssayContract (data) {
        return fetchPdf({
            url: '/cwe/a/assayApply/printAssayContract',
            method: 'get',
            params: { ...data }
        })
    }

    deleteByID (id) {
        return fetch({
            url: '/cwe/a/assayApply/deleteById',
            method: 'post',
            data: { id }
        })
    }
}

export default new EntrustOrder()
