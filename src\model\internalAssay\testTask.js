import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class EntrustOrder extends Model {
    constructor () {
        const dataJson = {
            'name': '',
            'applayUserName': '',
            'location': '',
            'sampleDate': '',
            'taxCompanyName': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [],
            rules: {}
        }
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            offsetTop: -30,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '样品名称',
                    prop: 'name',
                    slot: 'name',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '样品名称',
                        prop: 'filter_LIKES_name'
                    }
                },
                {
                    label: '样品编号',
                    prop: 'code',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '样品编号',
                        prop: 'filter_LIKES_code'
                    }
                },
                {
                    label: '省市区',
                    prop: 'location',
                    isShow: true
                },
                {
                    label: '委托时间',
                    prop: 'sampleDate',
                    isShow: true
                },
                {
                    label: '公司名称',
                    prop: 'taxCompanyName',
                    isShow: true
                },
                // {
                //     label: '状态',
                //     prop: 'taxPhone',
                //     isShow: true
                // },
                {
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            actions: []
        }
        super('/cwe/a/assayApply', dataJson, form, tableOption)
    }

    getTaskList (data) {
        return fetch({
            url: '/cwe/a/assayApply/getTaskList',
            method: 'post',
            data: { ...data }
        })
    }

    saveIndexValues (data) {
        return fetch({
            url: '/cwe/a/assayApply/saveIndexValues',
            method: 'post',
            data: { ...data }
        })
    }
}

export default new EntrustOrder()
