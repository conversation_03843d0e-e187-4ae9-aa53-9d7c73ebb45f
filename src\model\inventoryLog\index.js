import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
class ChildModel extends Model {
    constructor() {
        const dataJson = {
            date: '', // 日期
            type: 'YM', // 类型:半成品精煤-BCPJM,成品精煤-CPJM,原煤-YM,中煤-ZM
            name: '', // 品名
            ad: '', // 灰分
            std: '', // 硫
            lastStock: '', // 上日库存
            todayIn: '', // 本日购入或生产
            todayOut: '', // 本日销售或使用
            stock: '', // 库存
            wasteRock: '', // 坑石矸
            remarks: '' // 备注信息
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    sortable: true,
                    isShow: true
                },
                {
                    label: '物资名称',
                    prop: 'itemName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '出库数量',
                    prop: 'deliveryAmount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '类别',
                    prop: 'categoryName',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '单位',
                //     prop: 'stockUnit',
                //     isShow: true,
                //     align: "center"
                // },
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    page(params = {}) {
        return fetch({
            url: `/wms/a/outOrderItem/page`,
            method: 'get',
            params: params
        })
    }
}

export default new ChildModel()
