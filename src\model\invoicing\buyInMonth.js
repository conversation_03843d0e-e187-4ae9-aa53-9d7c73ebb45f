import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = '/cwe/a/buyInMonth'
class InvoicingModel extends Model {
    constructor () {
        const dataJson = {
            date: '', // 日期
            name: '', // 名称
            contractId: '',
            coalType: '',
            contractCode: '',
            senderName: '', // 供货单位
            receiverName: '',
            amount: '', // 合同数量
            amountLeft: '', // 剩余数量
            price: '', // 煤价
            truckCount: '', // 车数
            sendWeight: '', // 原发数
            carriage: '', // 运费
            remarks: ''

        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '品名',
                    prop: 'name',
                    slot: 'name',

                    isShow: true
                },
                {
                    label: '发货单位',
                    prop: 'senderName',
                    width: 150,
                    sortable: true,
                    isShow: true
                },
                {
                    label: '煤种',
                    prop: 'coalType',
                    width: 120,
                    isShow: true
                },
                {
                    label: '车数',
                    prop: 'truckCount',
                    isShow: true
                },
                {
                    label: '原发数(吨)',
                    prop: 'sendWeight',
                    isShow: true
                },
                {
                    label: '实收数(吨)',
                    prop: 'receiveWeight',
                    isShow: true
                },
                {
                    label: '途耗(吨)',
                    prop: 'wayCost',
                    isShow: true
                },
                {
                    label: '煤价',
                    prop: 'price',
                    slot: 'price',
                    isShow: true
                },
                {
                    label: '运费',
                    prop: 'carriage',
                    slot: 'carriage',
                    isShow: true
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    slot: 'remarks',
                    isShow: true
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            summaries: [
                { prop: 'truckCount', suffix: '', fixed: 0 },
                { prop: 'sendWeight', suffix: '', fixed: 2 },
                { prop: 'receiveWeight', suffix: '', fixed: 2 },
                { prop: 'wayCost', suffix: '', fixed: 2 }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save (data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }

    refresh (data) {
        return fetch({
            url: `${name}/refresh`,
            method: 'post',
            data
        })
    }

    async page (params = {}, normal = false) {
        try {
            const res = await fetch({
                url: `${name}/page`,
                method: 'get',
                params: params
            })
            if (!normal) this.formatRecords({ records: res.data.records, fields: ['price', 'carriage', 'remarks'] })
            return res
        } catch (e) {
            // console.log(e)
        }
    }
    async saveList (text) {
        try {
            return await fetch({
                url: `${name}/saveList`,
                method: 'post',
                data: text
            })
        } catch (error) {
            console.log(error)
        }
    }
    formatRecords ({ records, fields }) {
        records.forEach(item => {
            item._all = false
            item.active = {}
            item.bak = {}
            for (const field of fields) {
                item.active[field] = false
                item.bak[field] = item[field]
            }
        })
        return records
    }
}

export default new InvoicingModel()
