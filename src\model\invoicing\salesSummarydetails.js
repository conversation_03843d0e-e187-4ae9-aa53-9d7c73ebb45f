import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/sellOutDetail`
class _Model extends Model {
    constructor() {
        const dataJson = {
            date: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 日期
            name: '', // 品名,
            contractCode: '',
            contractId: '',
            customerId: '',
            customerName: '',
            senderName: '', // 发货单位
            receiverName: '', // 收货单位
            coalType: '', // 煤种
            truckCount: '', // 车数
            sendWeight: '', // 原发数
            receiveWeight: '', // 实收数
            wayCost: '', // 途耗
            truckCountAcc: '', // 车数累计
            sendWeightAcc: '', // 原发数累计
            receiveWeightAcc: '', // 实收数累计
            wayCostAcc: '', // 途耗累计
            stationMan: '', // 在岗人
            transferMan: '', // 交班人
            nextMan: '', // 接班人
            remarks: '', // 备注信息
            productCode: '',
            productId: ''
        }
        const form = {}
        const tableOption = {
            showSelection: true,
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    sortable: true,
                    isShow: true,
                    width: 100,
                    fixed: 'left',
                    align: "center"
                },
                {
                    label: '品名',
                    prop: 'name',
                    slot: 'name',
                    isShow: true
                },
                {
                    label: '收货单位',
                    prop: 'receiverName',
                    isShow: true,
                    width: 100,
                    align: "center"
                },
                {
                    label: '车数',
                    prop: 'truckCount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '原发数',
                    prop: 'sendWeight',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '实收数',
                    prop: 'receiveWeight',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '亏吨',
                    prop: 'lossNull',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '结算数',
                //     prop: 'settlement',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    label: '合同名称',
                    prop: 'contractName',
                    slot: 'contractName',
                    isShow: true,
                    align: 'center',
                    width: 150
                },
                {
                    label: '合同编号',
                    prop: 'contractId',
                    slot: 'contractId',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '煤价',
                    prop: 'price',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '运费',
                    prop: 'carriage',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '买方',
                    prop: 'firstParty',
                    isShow: true,
                    width: 100,
                    align: "center"
                },
                {
                    label: '卖方',
                    prop: 'secondParty',
                    isShow: true,
                    width: 100,
                    align: "center"
                },
                {
                    label: '发货单位',
                    prop: 'senderName',
                    isShow: true,
                    width: 100,
                    align: "center"
                },
                {
                    label: '总数量',
                    prop: 'amount',
                    isShow: true,
                    align: "center"
                },

                // {
                //     label: '煤种',
                //     prop: 'coalType',
                //     isShow: true
                // },


                {
                    label: '结清',
                    prop: 'isSettle',
                    slot: 'isSettle',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '是否开票',
                    prop: 'isBill',
                    isShow: true,
                    slot: 'isBill',
                    align: "center"
                },

                {
                    label: '运费情况',
                    prop: 'carriageRemarks',
                    isShow: true,
                    slot: 'carriageRemarks',
                    align: "center"
                },

                // {
                //     label: '水分',
                //     prop: 'mt',
                //     isShow: true,
                //     width: 200,
                //     align: "center"
                // },
                {
                    label: '途耗',
                    prop: 'wayCost',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '车数累计',
                //     prop: 'truckCountAcc',
                //     isShow: true
                // },
                // {
                //     label: '原发累计(本月)',
                //     prop: 'sendWeightAcc',
                //     isShow: true,
                //     width: 120
                // },
                // {
                //     label: '实收累计(本月)',
                //     prop: 'receiveWeightAcc',
                //     isShow: true,
                //     width: 120
                // },
                // {
                //     label: '途耗累计',
                //     prop: 'wayCostAcc',
                //     slot: 'wayCostAcc',
                //     isShow: true
                // },
                // {
                //     label: '在岗人',
                //     prop: 'stationMan',
                //     isShow: true
                // },
                // {
                //     label: '交班人',
                //     prop: 'transferMan',
                //     isShow: true
                // },
                // {
                //     label: '接班人',
                //     prop: 'nextMan',
                //     isShow: true
                // },
                {
                    label: '备注',
                    prop: 'remarks',
                    slot: 'remarks',
                    isShow: true,
                    align: "center"
                },
                // {
                //     noExport: true,
                //     label: '操作',
                //     prop: 'opt',
                //     slot: 'opt',
                //     isShow: true
                // }
            ],
            summaries: [
                { prop: 'truckCount', suffix: '', fixed: 0, avg: false },
                { prop: 'sendWeight', suffix: '', fixed: 2, avg: false },
                { prop: 'receiveWeight', suffix: '', fixed: 2, avg: false },

                { prop: 'price', suffix: '', fixed: 2, avg: false },
                { prop: 'mt', suffix: '', fixed: 2, avg: false },
                { prop: 'carriage', suffix: '', fixed: 2, avg: false },
                { prop: 'lossNull', suffix: '', fixed: 2, avg: false },
                // { prop: 'wayCost', suffix: '', fixed: 2, avg: true },

                // { prop: 'wayCost', suffix: '%', fixed: 2 },
                // { prop: 'truckCountAcc', suffix: '', fixed: 0 },
                // { prop: 'sendWeightAcc', suffix: '', fixed: 2 },
                // { prop: 'receiveWeightAcc', suffix: '', fixed: 2 },
                // { prop: 'wayCostAcc', suffix: '%', fixed: 2 }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    // page(searchForm) {
    //     searchForm.orderBy = 'date'
    //     return fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: searchForm
    //     })
    // }

    async page(params = {}, normal = false) {
        try {
            const res = await fetch({
                url: `${name}/page`,
                method: 'get',
                params: params
            })
            if (!normal) this.formatRecords({ records: res.data.records, fields: ['remarks'] })
            return res
        } catch (e) {
            // console.log(e)
        }
    }

    formatRecords({ records, fields }) {
        records.forEach(item => {
            item._all = false
            item.active = {}
            item.bak = {}
            for (const field of fields) {
                item.active[field] = false
                item.bak[field] = item[field]
            }
        })
        return records
    }




    getAccValues(params) {
        return fetch({
            url: `${name}/getAccValues`,
            method: 'get',
            params
        })
    }

    deleteId(id) {
        return fetch({
            url: this.name + '/deleteById',
            method: 'post',
            data: { id }
        })
    }

    refresh(data) {
        return fetch({
            url: '/cwe/a/weightHouseOut/refreshByDate',
            method: 'post',
            data
        })
    }
    //是否开票
    setupdateIsBill(data) {
        return fetch({
            url: `${name}/updateIsBill`,
            method: 'post',
            data
        })
    }
    // 运输情况
    setupdateCarriageRemarks(data) {
        return fetch({
            url: `${name}/updateCarriageRemarks`,
            method: 'post',
            data
        })
    }

    //批量修改
    savebatchChangeBillCarriage(data) {
        return fetch({
            url: `${name}/updateBillCarriage`,
            method: 'post',
            data
        })
    }

    remarkssave(data) {
        return fetch({
            url: '/cwe/a/sellOutDetail/updateRemarks',
            method: 'post',
            data
        })
    }
}

export default new _Model()
