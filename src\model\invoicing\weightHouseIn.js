import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import { getToday } from '@/utils/index'
const name = `/cwe/a/weightHouseIn`
const dataJson = {
    date: getToday(), // 日期
    productId: '', // 品名Id
    productCode: '', // 品名编码
    name: '', // 品名
    contractCode: '', // 合同编码
    contractId: '', // 合同Id
    supplierId: '', // 供应商ID -- 对应发货单位
    supplierName: '', // 供应商名称 -- 对应发货单位
    senderName: '', // 发货单位
    receiverName: '', // 收货单位
    coalType: '', // 煤种
    truckCount: '', // 车数
    sendWeight: '', // 原发数
    receiveWeight: '', // 实收数
    wayCost: '', // 途耗
    truckCountAcc: 0, // 车数累计
    sendWeightAcc: 0, // 原发数累计
    receiveWeightAcc: 0, // 实收数累计
    wayCostAcc: 0, // 途耗累计
    stationMan: '', // 在岗人
    transferMan: '', // 交班人
    nextMan: '', // 接班人
    remarks: '' // 备注信息

}
const form = {}
const tableOption = {
    showIndex: true,
    showPage: true,
    showSelection: true,
    columns: [
        // fixed: 'left'
        {
            label: '日期',
            prop: 'date',
            isShow: true,
            sortable: true,
            width: 100,
        },
        {
            label: '品名',
            prop: 'name',
            slot: 'name',
            isShow: true
        },
        {
            label: '发货单位',
            prop: 'senderName',
            isShow: true,
            width: 120
        },
        {
            label: '收货单位',
            prop: 'receiverName',
            isShow: true,
            width: 120
        },
        {
            label: '煤种',
            prop: 'coalType',
            isShow: true
        },
        {
            label: '车数',
            prop: 'truckCount',
            isShow: true
        },
        {
            label: '原发数',
            prop: 'sendWeight',
            isShow: true
        },
        {
            label: '实收数',
            prop: 'receiveWeight',
            isShow: true
        },
        {
            label: '途耗',
            prop: 'wayCost',
            slot: 'wayCost',
            isShow: true
        },
        {
            label: '车数累计',
            prop: 'truckCountAcc',
            isShow: true
        },
        {
            label: '原发累计(本月)',
            prop: 'sendWeightAcc',
            isShow: true,
            width: 120
        },
        {
            label: '实收累计(本月)',
            prop: 'receiveWeightAcc',
            isShow: true,
            width: 120
        },
        {
            label: '途耗累计',
            prop: 'wayCostAcc',
            slot: 'wayCostAcc',
            isShow: true
        },

        {
            label: '在岗人',
            prop: 'stationMan',
            isShow: true
        },
        {
            label: '交班人',
            prop: 'transferMan',
            isShow: true
        },
        {
            label: '接班人',
            prop: 'nextMan',
            isShow: true
        },
        {
            label: '备注',
            prop: 'remarks',
            isShow: true
        },
        {
            noExport: true,
            label: '操作',
            prop: 'opt',
            slot: 'opt',
            isShow: true
        }
    ],
    summaries: [
        { prop: 'truckCount', suffix: '', fixed: 0 },
        { prop: 'sendWeight', suffix: '', fixed: 2 },
        { prop: 'receiveWeight', suffix: '', fixed: 2 },
        { prop: 'wayCost', suffix: '%', fixed: 2, custom: 'wayCost' }
    ]
}
class WeightHouseInModel extends Model {
    constructor() {
        super(name, dataJson, form, tableOption)
    }
    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }
    getAccValues(params) {
        return fetch({
            url: `${name}/getAccValues`,
            method: 'get',
            params
        })
    }
    deleteId(id) {
        return fetch({
            url: this.name + '/deleteById',
            method: 'post',
            data: { id }
        })
    }
}

export default new WeightHouseInModel()
