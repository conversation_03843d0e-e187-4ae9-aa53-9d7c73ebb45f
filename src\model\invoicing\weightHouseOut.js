import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/weightHouseOut`
class _Model extends Model {
    constructor() {
        const dataJson = {
            date: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 日期
            name: '', // 品名,
            contractCode: '',
            contractId: '',
            customerId: '',
            customerName: '',
            senderName: '', // 发货单位
            receiverName: '', // 收货单位
            coalType: '', // 煤种
            truckCount: '', // 车数
            sendWeight: '', // 原发数
            receiveWeight: '', // 实收数
            wayCost: '', // 途耗
            truckCountAcc: '', // 车数累计
            sendWeightAcc: '', // 原发数累计
            receiveWeightAcc: '', // 实收数累计
            wayCostAcc: '', // 途耗累计
            stationMan: '', // 在岗人
            transferMan: '', // 交班人
            nextMan: '', // 接班人
            remarks: '', // 备注信息
            productCode: '',
            productId: ''
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    sortable: true,
                    isShow: true,
                    width: 100,
                    fixed: 'left'
                },
                {
                    label: '品名',
                    prop: 'name',
                    slot: 'name',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '发货单位',
                    prop: 'senderName',
                    isShow: true,
                    width: 120,
                    align: "center"
                },
                {
                    label: '收货单位',
                    prop: 'receiverName',
                    isShow: true,
                    width: 120,
                    align: "center"
                },
                {
                    label: '煤种',
                    prop: 'coalType',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '车数',
                    prop: 'truckCount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '原发数',
                    prop: 'sendWeight',
                    isShow: true
                },
                {
                    label: '实收数',
                    prop: 'receiveWeight',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '途耗',
                    prop: 'wayCost',
                    slot: 'wayCost',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '车数累计',
                    prop: 'truckCountAcc',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '原发累计(本月)',
                    prop: 'sendWeightAcc',
                    isShow: true,
                    width: 120,
                    align: "center"
                },
                {
                    label: '实收累计(本月)',
                    prop: 'receiveWeightAcc',
                    isShow: true,
                    width: 120,
                    align: "center"
                },
                {
                    label: '途耗累计',
                    prop: 'wayCostAcc',
                    slot: 'wayCostAcc',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '在岗人',
                    prop: 'stationMan',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '交班人',
                    prop: 'transferMan',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '接班人',
                    prop: 'nextMan',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true,
                    align:"center"
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            summaries: [
                { prop: 'truckCount', suffix: '', fixed: 0 },
                { prop: 'sendWeight', suffix: '', fixed: 2 },
                { prop: 'receiveWeight', suffix: '', fixed: 2 }
                // { prop: 'wayCost', suffix: '%', fixed: 2 },
                // { prop: 'truckCountAcc', suffix: '', fixed: 0 },
                // { prop: 'sendWeightAcc', suffix: '', fixed: 2 },
                // { prop: 'receiveWeightAcc', suffix: '', fixed: 2 },
                // { prop: 'wayCostAcc', suffix: '%', fixed: 2 }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }
    getAccValues(params) {
        return fetch({
            url: `${name}/getAccValues`,
            method: 'get',
            params
        })
    }

    deleteId(id) {
        return fetch({
            url: this.name + '/deleteById',
            method: 'post',
            data: { id }
        })
    }
}

export default new _Model()
