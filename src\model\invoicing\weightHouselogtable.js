import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
// import { getToday } from '@/utils/index'
const name = `/cwe/a/weightHouseLog`
const dataJson = {
    // date: getToday(), // 日期
    // productId: '', // 品名Id
    // productCode: '', // 品名编码
    // name: '', // 品名
    // contractCode: '', // 合同编码
    // contractId: '', // 合同Id
    // supplierId: '', // 供应商ID -- 对应发货单位
    // supplierName: '', // 供应商名称 -- 对应发货单位
    // senderName: '', // 发货单位
    // receiverName: '', // 收货单位
    // coalType: '', // 煤种
    // truckCount: '', // 车数
    // sendWeight: '', // 原发数
    // receiveWeight: '', // 实收数
    // wayCost: '', // 途耗
    // truckCountAcc: 0, // 车数累计
    // sendWeightAcc: 0, // 原发数累计
    // receiveWeightAcc: 0, // 实收数累计
    // wayCostAcc: 0, // 途耗累计
    // stationMan: '', // 在岗人
    // transferMan: '', // 交班人
    // nextMan: '', // 接班人
    // remarks: '' // 备注信息

}
const form = {}
const tableOption = {
    showIndex: false,
    showPage: true,
    showSelection: false,
    columns: [
        {
            label: '日期',
            prop: 'date',
            isShow: true,
            sortable: true,
            width: 100,
            align: "center"
        },
        // {
        //     label: '过磅单号/派车单号',
        //     prop: 'autoBH',
        //     slot: 'autoBH',
        //     isShow: true,
        //     width: 80,
        //     align: "center"
        // },
        {
            label: '过磅类型',
            prop: 'type',
            slot: 'type',
            isShow: true,
            width: 80,
            align: "center"
        },
        {
            label: '发货单位',
            prop: 'supplierName',
            isShow: true,
            width: 100,
            align: "center"
        },
        {
            label: '收货单位',
            prop: 'customerName',
            width: 100,
            isShow: true,
            align: "center"
        },

        {
            label: '品名',
            prop: 'cargoType',
            isShow: true,
            align: "center"
        },
        {
            label: '实收净重/过磅净重',
            prop: 'weight',
            isShow: true,
            align: "center"
        },
        {
            label: '原发净重',
            prop: 'originalTon',
            isShow: true,
            align: "center"
        },
        {
            label: '车数',
            prop: 'truckCount',
            isShow: true,
            align: "center"
        },
        {
            label: '亏吨',
            prop: 'lossNull',
            isShow: true,
            align: "center"
        },

        // {
        //     label: '结算数',
        //     prop: 'settlement',
        //     isShow: true,
        //     align: "center"
        // },
        // {
        //     label: '物料规格',
        //     prop: 'materialSpecification',
        //     isShow: true,
        //     align: "center"
        // },
        {
            label: '车号',
            prop: 'plateNumber',
            isShow: true,
            align: "center"
        },
        {
            label: '卸车方式',
            prop: 'unloadMode',
            isShow: true,
            align: "center"
        },
        {
            label: '司机',
            prop: 'driver',
            isShow: true,
            align: "center"
        },
        {
            label: '一次计量时间',
            prop: 'firstWeightDate',
            slot: 'firstWeightDate',
            isShow: true,
            sortable: true,
            width: 180,
            align: "center"
        },
        {
            label: '二次计量时间',
            prop: 'secondWeightDate',
            slot: 'secondWeightDate',
            // defaultsort: true,
            isShow: true,
            sortable: true,
            width: 180,
            align: "center"
        },
        {
            label: '毛重',
            prop: 'firstWeight',
            width: 120,
            isShow: true,
            align: "center"
        },
        {
            label: '皮重',
            prop: 'secondWeight',
            isShow: true,
            align: "center"
        },

        {
            label: '订单号',
            prop: 'orderNo',
            isShow: true,
            align: "center"
        },
        {
            label: '合同号',
            prop: 'contractNo',
            isShow: true,
            align: "center"
        },
        {
            label: '承运商',
            prop: 'carrier',
            isShow: true,
            align: "center"
        },
        {
            label: '集装箱号',
            prop: 'containerNo',
            isShow: true,
            align: "center"
        },

        {
            label: '备注',
            prop: 'remarks',
            isShow: true,
            align: "center"
        },
        {
            noExport: true,
            label: '操作',
            prop: 'opt',
            slot: 'opt',
            isShow: true
        }
    ],
    // sumIndex: 5,
    sumText: '合计',
    summaries: [
        { prop: 'weight', suffix: '', fixed: 2, avg: false },
        { prop: 'receiveWeight', suffix: '', fixed: 2, avg: false },
        { prop: 'originalTon', suffix: '', fixed: 2, avg: false },
        { prop: 'truckCount', suffix: '', fixed: 0, avg: false },
        // { prop: 'plateNumber', suffix: '', fixed: 0, avg: false },
        { prop: 'firstWeight', suffix: '', fixed: 2, avg: false },
        { prop: 'secondWeight', suffix: '', fixed: 2, avg: false },
        { prop: 'lossNull', suffix: '', fixed: 2, avg: false },
    ]


}
class weightHouselogtableModel extends Model {
    constructor() {
        super(name, dataJson, form, tableOption)
    }
    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }
    getAccValues(params) {
        return fetch({
            url: `${name}/getAccValues`,
            method: 'get',
            params
        })
    }
    deleteId(id) {
        return fetch({
            url: this.name + '/deleteById',
            method: 'post',
            data: { id }
        })
    }
    refresh(data) {
        return fetch({
            url: '/cwe/a/weightHouseIn/refreshByDate',
            method: 'post',
            data
        })
    }
    refreshV(data) {
        return fetch({
            url: '/cwe/a/weightHouseOut/refreshByDate',
            method: 'post',
            data
        })
    }
    // 采购导入
    async saveList(text) {
        try {
            return await fetch({
                // url: `/cwe/a/weightHouseLog/importData`,
                url: `${name}/importInWeightHouseLog`,
                method: 'post',
                data: text
            })
        } catch (error) {
            console.log(error)
        }
    }

    // 销售导入
    async savesaleList(text) {
        try {
            return await fetch({
                // url: `/cwe/a/weightHouseLog/importData`,
                url: `${name}/importOutWeightHouseLog`,
                method: 'post',
                data: text
            })
        } catch (error) {
            console.log(error)
        }
    }

    // 采购日报导入
    async importInDailyWeightHouseLog(text) {
        try {
            return await fetch({
                // url: `/cwe/a/weightHouseLog/importData`,
                url: `${name}/importInDailyWeightHouseLog`,
                method: 'post',
                data: text
            })
        } catch (error) {
            console.log(error)
        }
    }
    // 销售日报导入
    async importOutDailyWeightHouseLog(text) {
        try {
            return await fetch({
                url: `${name}/importOutDailyWeightHouseLog`,
                method: 'post',
                data: text
            })
        } catch (error) {
            console.log(error)
        }
    }

    //  新增修改
    createTodo(data, optName = 'create') {
        const method = optName === 'update' ? 'put' : 'post'
        const optNameApiMaps = {
            create: `${name}/create`,
            update: `${name}/update`,
            todo: `${name}/create`
        }
        return fetch({
            url: `${optNameApiMaps[optName]}`,
            method,
            data
        })
    }
    async removeBatchTodo({ ids = [], useConfirm = false }) {
        const fetchFn = () =>
            fetch({
                url: `${name}/delete`,
                method: 'delete',
                params: { ids }
            })
        if (useConfirm) {
            try {
                // await super.modal.confirm('是否确认删除当前数据?')
                await this.$confirm('是否确认删除当前数据?', '系统提示', {
                    distinguishCancelAndClose: true,
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                })
                return fetchFn()
            } catch (error) {
                return Promise.reject(error)
            }
        } else {
            return fetchFn()
        }
    }
}

export default new weightHouselogtableModel()
