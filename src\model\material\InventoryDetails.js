import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/wms/a/inOrderItem`
class paymentorderModel extends Model {
    constructor() {
        const dataJson = {
            commitDate: '',//入库时间
            commitBy: '',//存放人
            inOrderItemList: [],
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    align: "center",
                    isShow: true,
                    align: "center"
                },
                {
                    label: '商品名称',
                    prop: 'itemName',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '单位',
                    prop: 'stockUnit',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '数量',
                    prop: 'leftAmount',
                    isShow: true,
                    align: "center",
                },

                {
                    label: '单价',
                    prop: 'price',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '金额',
                    prop: 'totalMoney',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '物资类型',
                    prop: 'categoryName',
                    // slot: 'categoryName',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '送货单位',
                    prop: 'supplierName',
                    isShow: true
                },

                // {
                //     noExport: true,
                //     label: '操作',
                //     prop: 'opt',
                //     isShow: true,
                //     slot: 'opt'
                // }
            ],
            summaries: [
                { prop: 'leftAmount', suffix: '', fixed: 2 },
                { prop: 'totalMoney', suffix: '', fixed: 2 },
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    // 获取保管人
    findDistinctKeepManList() {
        return fetch({
            url: '/wms/a/inOrder/findDistinctKeepManList',
            method: 'get'
        })
    }
    // 获取送货人
    findDistinctSendManList() {
        return fetch({
            url: '/wms/a/inOrder/findDistinctSendManList',
            method: 'get'
        })
    }

    // 获取负责人
    findDistinctChargeManList() {
        return fetch({
            url: '/wms/a/inOrder/findDistinctChargeManList',
            method: 'get'
        })
    }
    // 获取送货单位
    findDistinctSupplierNameList() {
        return fetch({
            url: '/wms/a/inOrder/findDistinctSupplierNameList',
            method: 'get'
        })
    }


    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }
    // async page(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: params
    //     })
    //     res.data.records.forEach(item => {
    //         item.isPublicBoolean = item.isPublic === 'Y'
    //         item.typeName = typeName[item.coalCategory]
    //     })
    //     return res
    // }

    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }
    // 付款申请
    getApproved(data) {
        return fetch({
            url: `${name}/submit`,
            method: 'post',
            data
        })
    }
    //保存草稿
    SaveDraftfn(data) {
        return fetch({
            url: `${name}/saveDraft`,
            method: 'post',
            data
        })
    }
    //审核通过
    savepass(data) {
        return fetch({
            url: `${name}/pass`,
            method: 'post',
            data
        })
    }
    //拒绝
    savereject(data) {
        return fetch({
            url: `${name}/reject`,
            method: 'post',
            data
        })
    }

    //获取待审核的数据条数
    getcountByState() {
        return fetch({
            url: `${name}/countByState`,
            method: 'get',
        })
    }
}

export default new paymentorderModel()
