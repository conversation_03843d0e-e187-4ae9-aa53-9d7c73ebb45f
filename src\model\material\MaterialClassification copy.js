import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/wms/a/category`
class MaterialClassificationModel extends Model {
    constructor(data) {
        const dataJson = {
            'id': '',
            'code': '',
            'siteCode': '',
            'siteName': '',
            'loginName': '',
            'password': '',
            'jianpin': '',
            'pinyin': '',
            'name': '',
            'email': '',
            'phone': '',
            'mobile': '',
            'address': '',
            'state': 'ZC',
            'lastLoginIp': '',
            'lastLoginDate': '',
            'avatar': '',
            'gender': '',
            'birthday': '',
            'height': '',
            'weight': '',
            'hiredate': '',
            'dimissionDate': '',
            'registerTime': '',
            'nativePlace': '',
            'homeContactName': '',
            'homeContactPhone': '',
            'createBy': '',
            'createDate': '',
            'updateBy': '',
            'updateDate': '',
            'ext': '',
            'remarks': '',
            'no': '',
            roleCodes: ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            edit: true,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                // {
                //     label: '分类',
                //     width: 150,
                //     isShow: false,
                //     filter: {
                //         label: '分类查询',
                //         prop: 'filter_LIKES_no_OR_name_OR_loginName_OR_phone_OR_mobile'
                //     },
                //     isFilter: true
                // },
                {
                    label: '类型名称',
                    prop: 'name',
                    slot: 'name',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '类型名称',
                        prop: 'filter_LIKES_name'
                    }
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true
                },
                {
                    label: '操作',
                    prop: 'opt',
                    isShow: true,
                    slot: 'opt',
                    width: 150
                }
            ],
            actions: []
        }
        // super('/cwe/a/user', dataJson, form, tableOption)
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            // url: `${name}/saveUser`,
            url: `${name}/save`,
            method: 'post',
            data: data
        })
    }
    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }

}

export default new MaterialClassificationModel()
