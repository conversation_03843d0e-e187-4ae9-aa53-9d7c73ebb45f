import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/wms/a/category`
class paymentorderModel extends Model {
    constructor() {
        const dataJson = {
            name: '',//
            parentId: 0,
            parentIds: 0,
        }
        const form = {}
        const tableOption = {
            columns: [
                // {
                //     label: '类型名称',
                //     prop: 'name',
                //     slot: 'name',
                //     isFilter: true,
                //     isShow: true,
                //     filter: {
                //         label: '类型名称',
                //         prop: 'filter_LIKES_name'
                //     }
                // },

                {
                    label: '类型名称',
                    prop: 'name',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '状态',
                //     prop: 'state',
                //     slot: 'state',
                //     isShow: true
                // },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    isShow: true,
                    slot: 'opt'
                }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }

    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }
    //获取物资类型
    async gettype(params = {}) {
        const res = await fetch({
            url: `${name}/page`,
            method: 'get',
            params: params,
        })
        return res
    }


    //保存草稿
    SaveDraftfn(data) {
        return fetch({
            url: `${name}/saveDraft`,
            method: 'post',
            data
        })
    }
    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }





}

export default new paymentorderModel()
