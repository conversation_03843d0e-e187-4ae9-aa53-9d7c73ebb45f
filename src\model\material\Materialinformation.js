import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/wms/a/item`
class paymentorderModel extends Model {
    constructor() {
        const dataJson = {
            // attachmentList: [],
            name: '',//物资名称
            code: '',//物资名称
            categoryId: '',//物资类型
            categoryName: '',//分类名称

            stockUnit: '',//单位
            price: '',//单价
            entryAmount: '',//期初库存
            totalMoney: '',//金额
            remarks: '',
            initStock: 0,
            // date: (new Date(new Date().getTime())).Format('yyyy-MM-dd'),//盘库日期
            // date: (new Date(new Date().getTime())).Format('yyyy-MM-dd'), // 盘库日期
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '物资名称',
                    prop: 'name',
                    isShow: true,
                    fixed: 'left',
                    minWidth: 100,
                    align: "center"
                },
                {
                    label: '物资编号',
                    prop: 'code',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '分类id',
                //     prop: 'categoryId',
                //     isShow: true,
                // },
                {
                    label: '分类名称',
                    prop: 'categoryName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '单位',
                    prop: 'stockUnit',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '规格',
                    prop: 'spec',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '购买单位',
                    prop: 'buyName',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '单价',
                //     prop: 'price',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     label: '是否含税',
                //     prop: 'hasFax',
                //     isShow: true,
                //     slot: 'hasFax',
                //     align: "center"
                // },
                // {
                //     label: '总价',
                //     prop: 'totalMoney',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     label: '库存',
                //     prop: 'stockAmount',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '初始库存',
                //     prop: 'initStock',
                //     isShow: true
                // },


                // {
                //     label: '盘库日期',
                //     prop: 'date',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    isShow: true,
                    slot: 'opt'
                }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }

    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }
    //获取物资类型
    async gettype(params = {}) {
        const res = await fetch({
            url: `${name}/page`,
            method: 'get',
            params: params,
        })
        return res
    }
    //获取物资类型
    async getpageAmountGt0(params = {}) {
        const res = await fetch({
            url: `${name}/pageAmountGt0`,
            method: 'get',
            params: params,
        })
        return res
    }
    //保存草稿
    SaveDraftfn(data) {
        return fetch({
            url: `${name}/saveDraft`,
            method: 'post',
            data
        })
    }
    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }
    setNumber(searchForm) {
        return fetch({
            url: '/wms/a/itemStockPk/saveItemStock',
            method: 'post',
            params: searchForm
        })
    }




    //获取购买单位历史记录
    // findDistinctName() {
    //     return fetch({
    //         url: '/wms/a/item/findDistinctName',
    //         method: 'get',
    //     })
    // }

    findDistinctBuyName() {
        return fetch({
            url: '/wms/a/item/findDistinctBuyName',
            method: 'get'
        })
    }

    // 查看库存日志
    // getitemStockLog(id) {
    //     return fetch({
    //         url: `${name}/itemStockLog`,
    //         method: 'get',
    //         params: { id }
    //     })
    // }




}

export default new paymentorderModel()
