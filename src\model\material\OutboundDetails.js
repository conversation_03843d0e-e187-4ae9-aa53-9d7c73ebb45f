import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/wms/a/outOrderItem`
class paymentorderModel extends Model {
    constructor() {
        const dataJson = {
            commitDate: '',//入库时间
            commitBy: '',//存放人
            outOrderItemList: [],
            reviewMessage: '',//备注
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    align: "center",
                    isShow: true,
                    align: "center"
                },
                {
                    label: '领用部门',
                    prop: 'usedDept',
                    isShow: true,
                },
                {
                    label: '商品名称',
                    prop: 'itemName',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '单位',
                    prop: 'stockUnit',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '数量',
                    prop: 'deliveryAmount',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '单价',
                    prop: 'price',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '金额',
                    prop: 'totalMoney',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '物资类型',
                    prop: 'categoryName',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '领用人',
                    prop: 'usedMan',
                    isShow: true
                },

                // {
                //     noExport: true,
                //     label: '操作',
                //     prop: 'opt',
                //     isShow: true,
                //     slot: 'opt'
                // }
            ],
            // sumText: '合计',
            summaries: [
                { prop: 'leftAmount', suffix: '', fixed: 2 },
                { prop: 'totalMoney', suffix: '', fixed: 2 },
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }

    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }

    // // 获取负责人
    // findDistinctChargeManList() {
    //     return fetch({
    //         url: `${name}/findDistinctChargeManList`,
    //         method: 'get'
    //     })
    // }
    // 获取保管人
    // findDistinctKeepManList() {
    //     return fetch({
    //         url: `${name}/findDistinctKeepManList`,
    //         method: 'get'
    //     })
    // }

    // 获取会计
    // findDistinctAccountManList() {
    //     return fetch({
    //         url: `${name}/findDistinctAccountManList`,
    //         method: 'get'
    //     })
    // }

    // 获取领用部门
    findDistinctUsedDeptList() {
        return fetch({
            url: `${name}/findDistinctUsedDeptList`,
            method: 'get'
        })
    }

    // 获取领用人
    findDistinctUsedManList() {
        return fetch({
            url: `${name}/findDistinctUsedManList`,
            method: 'get'
        })
    }

    // async page(params = {}, normal = false) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: params
    //     })
    //     // if (!normal) this.formatRecords(res.data.records)
    //     return res
    // }


    // formatRecords(records) {
    //     console.log(records)
    //     // records.forEach((item, index) => {
    //     //     item.children = item.outOrderItemList
    //     // })
    //     let newarry = []
    //     for (var i = 0; i < records.length; i++) {
    //         records[i].children = records[i].outOrderItemList
    //         // if (records[i].outOrderItemList.length > 1) {
    //         //     let firstElement = records[i].outOrderItemList.shift();
    //         //     records[i].children = firstElement
    //         // } else {
    //         //     records[i].children = records[i].outOrderItemList
    //         // }
    //         for (var k = 0; k < records[i].outOrderItemList.length; k++) {
    //             records[i].itemName = records[i].ext
    //             records[i].batchNo = records[i].outOrderItemList[0].batchNo
    //             records[i].categoryName = records[i].outOrderItemList[0].categoryName
    //             records[i].createDate = records[i].outOrderItemList[0].createDate
    //             records[i].stockUnit = records[i].outOrderItemList[0].stockUnit
    //             records[i].deliveryAmount = records[i].outOrderItemList[0].deliveryAmount
    //             records[i].price = records[i].outOrderItemList[0].price
    //             records[i].useDesc = records[i].outOrderItemList[0].useDesc
    //         }
    //         newarry.push(records[i])
    //     }
    //     console.log(newarry)
    //     return newarry
    // }









    // async page(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: params
    //     })
    //     res.data.records.forEach(item => {
    //         item.isPublicBoolean = item.isPublic === 'Y'
    //         item.typeName = typeName[item.coalCategory]
    //     })
    //     return res
    // }

    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }
    getBatchNoList(itemId) {
        return fetch({
            url: `/wms/a/inOrderItem/listByItemIdAmountGt0`,
            method: 'get',
            params: { itemId }
        })
    }
    // 付款申请
    getApproved(data) {
        return fetch({
            url: `${name}/submit`,
            method: 'post',
            data
        })
    }
    //保存草稿
    SaveDraftfn(data) {
        return fetch({
            url: `${name}/saveDraft`,
            method: 'post',
            data
        })
    }
    //审核通过
    savepass(data) {
        return fetch({
            url: `${name}/pass`,
            method: 'post',
            data
        })
    }
    //拒绝
    savereject(data) {
        return fetch({
            url: `${name}/reject`,
            method: 'post',
            data
        })
    }

    //获取待审核的数据条数
    getcountByState() {
        return fetch({
            url: `${name}/countByState`,
            method: 'get',
        })
    }
}

export default new paymentorderModel()
