import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
// const name = `/wms/a/category`
const name = `/wms/a/itemStockDay`
class paymentorderModel extends Model {
    constructor() {
        const dataJson = {
            name: '',//
            parentId: 0,
            parentIds: 0,
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    isShow: true,
                    sortable: true,
                    width: 100,
                    align: "center"
                },
                {
                    label: '物资名称',
                    prop: 'itemName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '类别',
                    prop: 'categoryName',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '分类id',
                //     prop: 'categoryId',
                //     slot: 'categoryId',
                //     isShow: true
                // },
                {
                    label: '单位',
                    prop: 'stockUnit',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '上日库存',
                    prop: 'lastStock',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '入库数量',
                    prop: 'todayIn',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '出库数量',
                    width: 120,
                    prop: 'todayOut',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '盘盈盘亏',
                    prop: 'stockCheck',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '库存',
                    prop: 'stockAmount',
                    isShow: true,
                    align: "center"
                },

                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }

    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }
    //获取物资类型
    async gettype(params = {}) {
        const res = await fetch({
            url: `${name}/page`,
            method: 'get',
            params: params,
        })
        return res
    }


    //保存草稿
    SaveDraftfn(data) {
        return fetch({
            url: `${name}/saveDraft`,
            method: 'post',
            data
        })
    }
    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }

    refresh(data) {
        return fetch({
            url: `${name}/refreshByNameNoZero`,
            method: 'post',
            data
        })
    }


    // async refresh(data) {
    //     return fetch({
    //         url: `${name}/refresh`,
    //         method: 'post',
    //         data
    //     })
    // }

}

export default new paymentorderModel()
