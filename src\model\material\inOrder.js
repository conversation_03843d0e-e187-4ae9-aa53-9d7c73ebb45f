import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/wms/a/inOrder`
class paymentorderModel extends Model {
    constructor() {
        const dataJson = {
            // itemCode: '',
            // itemName: '',
            // remarks: '',
            // innerCode: '',
            // stockAmount: '',
            // price: '',
            // entryAmount: "",
            // totalMoney: '',
            // useDesc: '',
            commitDate: '',//入库时间
            commitBy: '',//存放人
            // state: '',//状态
            // itemId: 0,
            inOrderItemList: [],
            // reviewMessage: '',//备注
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '物资列表',
                    prop: 'inOrderItemList',
                    isShow: true,
                    fixed: 'left',
                    slot: 'inOrderItemList',
                    align: "center"
                },
                {
                    label: '物资',
                    prop: 'ext',
                    slot: 'ext',
                    isShow: true,
                },
                {
                    label: '日期',
                    prop: 'commitDate',
                    align: "center",
                    isShow: true,
                    // fixed: 'left',
                    // minWidth: 100,
                    align: "center"
                },
                {
                    label: '送货单位',
                    prop: 'supplierName',
                    align: "center",
                    isShow: true,
                    align: "center"
                },
                {
                    label: '总数量',
                    prop: 'itemCount',
                    align: "center",
                    isShow: true,
                    align: "center"
                },
                {
                    label: '金额',
                    prop: 'totalMoney',
                    align: "center",
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '物资名称',
                //     prop: 'categoryName',
                //     slot: 'categoryName',
                //     isFilter: true,
                //     isShow: true,
                //     filter: {
                //         label: '物资名称',
                //         prop: 'filter_LIKES_categoryName'
                //     }
                // },
                {
                    label: '状态',
                    prop: 'state',
                    slot: 'state',
                    isShow: true
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true,
                    align: "center",
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    isShow: true,
                    slot: 'opt'
                }
            ],
            summaries: [
                { prop: 'itemCount', suffix: '', fixed: 2 },
                { prop: 'totalMoney', suffix: '', fixed: 2 },
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    // 获取保管人
    findDistinctKeepManList() {
        return fetch({
            url: '/wms/a/inOrder/findDistinctKeepManList',
            method: 'get'
        })
    }
    // 获取送货人
    findDistinctSendManList() {
        return fetch({
            url: '/wms/a/inOrder/findDistinctSendManList',
            method: 'get'
        })
    }

    // 获取负责人
    findDistinctChargeManList() {
        return fetch({
            url: '/wms/a/inOrder/findDistinctChargeManList',
            method: 'get'
        })
    }
    // 获取送货单位
    findDistinctSupplierNameList() {
        return fetch({
            url: '/wms/a/inOrder/findDistinctSupplierNameList',
            method: 'get'
        })
    }


    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }
    // async page(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: params
    //     })
    //     res.data.records.forEach(item => {
    //         item.isPublicBoolean = item.isPublic === 'Y'
    //         item.typeName = typeName[item.coalCategory]
    //     })
    //     return res
    // }

    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }
    // 付款申请
    getApproved(data) {
        return fetch({
            url: `${name}/submit`,
            method: 'post',
            data
        })
    }
    //保存草稿
    SaveDraftfn(data) {
        return fetch({
            url: `${name}/saveDraft`,
            method: 'post',
            data
        })
    }
    //审核通过
    savepass(data) {
        return fetch({
            url: `${name}/pass`,
            method: 'post',
            data
        })
    }
    //拒绝
    savereject(data) {
        return fetch({
            url: `${name}/reject`,
            method: 'post',
            data
        })
    }

    //获取待审核的数据条数
    getcountByState() {
        return fetch({
            url: `${name}/countByState`,
            method: 'get',
        })
    }
}

export default new paymentorderModel()
