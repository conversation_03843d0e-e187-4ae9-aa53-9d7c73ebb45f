import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
// const name = `/wms/a/category`
const name = `/wms/a/inOrderItem`
class paymentorderModel extends Model {
    constructor() {
        const dataJson = {
            name: '',
            parentId: 0,
            parentIds: 0,
        }
        const form = {}
        const tableOption = {
            columns: [

                {
                    label: '物资名称',
                    prop: 'itemName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '批次',
                    prop: 'batchNo',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '规格',
                    prop: 'spec',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '单位',
                    prop: 'stockUnit',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '数量',
                    prop: 'leftAmount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '价格',
                    prop: 'price',
                    isShow: true,
                    align: "center"
                },

                {
                    label: '金额',
                    prop: 'totalMoney',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '类别',
                    prop: 'categoryName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '入库时间',
                    prop: 'date',
                    isShow: true,
                    sortable: true,
                    width: 150,
                    align: "center"
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            // sumIndex: 2,
            // sumText: '合计',
            summaries: [
                { prop: 'totalMoney', suffix: '', fixed: 2 },
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    page(searchForm) {
        //数量大于0的数据
        searchForm.filter_GTI_leftAmount = 0
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }

    // outOrderItem(batchNo) {
    //     return fetch({
    //         url: `${name}/outOrderItem`,
    //         method: 'get',
    //         params: { batchNo }
    //     })
    // }



    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }

}

export default new paymentorderModel()
