import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/wms/a/outOrder`
class paymentorderModel extends Model {
    constructor() {
        const dataJson = {
            // itemCode: '',
            // itemName: '',
            // remarks: '',
            // innerCode: '',
            // stockAmount: '',
            // price: '',
            // entryAmount: "",
            // totalMoney: '',
            // useDesc: '',
            commitDate: '',//入库时间
            commitBy: '',//存放人
            // state: '',//状态
            // itemId: 0,
            outOrderItemList: [],
            reviewMessage: '',//备注
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '物资列表',
                    prop: 'outOrderItemList',
                    isShow: true,
                    fixed: 'left',
                    slot: 'outOrderItemList'
                },
                {
                    label: '提交时间',
                    prop: 'commitDate',
                    isShow: true,
                    // fixed: 'left',
                    minWidth: 100,
                    align: "center"
                },
                // {//
                //     label: '总件数',
                //     prop: 'itemCount',
                //     isShow: true,
                //     minWidth: 100,
                //     align: "center"
                // },
                // {
                //     label: '提交人',
                //     prop: 'commitBy',
                //     isShow: true,
                //     minWidth: 100,
                //     align: "center"
                // },
                {
                    label: '状态',
                    prop: 'state',
                    slot: 'state',
                    isShow: true
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true,
                    align: "center"
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    isShow: true,
                    slot: 'opt'
                }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }

    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }



    async page(params = {}, normal = false) {
        const res = await fetch({
            url: `${name}/page`,
            method: 'get',
            params: params
        })
        if (!normal) this.formatRecords(res.data.records)
        return res
    }


    formatRecords(records) {
        console.log(records)
        // records.forEach((item, index) => {
        //     item.children = item.outOrderItemList
        // })
        let newarry = []
        for (var i = 0; i < records.length; i++) {
            // if (records[i].outOrderItemList.length > 1) {
            //     let firstElement = records[i].outOrderItemList.shift();
            //     records[i].children = firstElement
            // } else {
            //     records[i].children = records[i].outOrderItemList
            // }
            for (var k = 0; k < records[i].outOrderItemList.length; k++) {
                records[i].itemName = records[i].ext
                records[i].batchNo = records[i].outOrderItemList[0].batchNo
                records[i].categoryName = records[i].outOrderItemList[0].categoryName
                records[i].createDate = records[i].outOrderItemList[0].createDate
                records[i].stockUnit = records[i].outOrderItemList[0].stockUnit
                records[i].deliveryAmount = records[i].outOrderItemList[0].deliveryAmount
                records[i].price = records[i].outOrderItemList[0].price
                records[i].useDesc = records[i].outOrderItemList[0].useDesc
            }
            newarry.push(records[i])
        }
        console.log(newarry)
        return newarry
    }









    // async page(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: params
    //     })
    //     res.data.records.forEach(item => {
    //         item.isPublicBoolean = item.isPublic === 'Y'
    //         item.typeName = typeName[item.coalCategory]
    //     })
    //     return res
    // }

    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }
    getBatchNoList(itemId) {
        return fetch({
            url: `/wms/a/inOrderItem/listByItemIdAmountGt0`,
            method: 'get',
            params: { itemId }
        })
    }
    // 付款申请
    getApproved(data) {
        return fetch({
            url: `${name}/submit`,
            method: 'post',
            data
        })
    }
    //保存草稿
    SaveDraftfn(data) {
        return fetch({
            url: `${name}/saveDraft`,
            method: 'post',
            data
        })
    }
    //审核通过
    savepass(data) {
        return fetch({
            url: `${name}/pass`,
            method: 'post',
            data
        })
    }
    //拒绝
    savereject(data) {
        return fetch({
            url: `${name}/reject`,
            method: 'post',
            data
        })
    }

    //获取待审核的数据条数
    getcountByState() {
        return fetch({
            url: `${name}/countByState`,
            method: 'get',
        })
    }
}

export default new paymentorderModel()
