import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = '/wms/a/itemStockLog'
class ChildModel extends Model {
    constructor() {
        const dataJson = {
            createDate: '', // 日期
            type: 'YM', // 类型:半成品精煤-BCPJM,成品精煤-CPJM,原煤-YM,中煤-ZM
            lastStock: '', // 上日库存
            todayIn: '', // 本日购入或生产
            todayOut: '', // 本日销售或使用
            stock: '', // 库存
            remarks: '', // 备注信息
            logType: ''//,CK 出库 RK 入库 PD 盘点
        }
        const form = {}
        const tableOption = {
            // mountQuery: false,
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    sortable: true,
                    isShow: true,
                    align: "center"
                },
                {
                    label: '商品名称',
                    prop: 'itemName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '批次号',
                    prop: 'batchNo',
                    width: 150,
                    isShow: true,
                    align: "center"
                },
                {
                    label: '商品分类',
                    prop: 'categoryName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '原库存',
                    prop: 'stockAmount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '库存变化',
                    prop: 'changeAmount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '库存',
                    prop: 'newAmount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '类型',
                    prop: 'logType',
                    slot: 'logType',
                    isShow: true
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true,
                    align: "center"
                }
            ]
        }
        super(name, dataJson, form, tableOption)
    }
    async page(params = {}) {
        try {
            const res = await fetch({
                url: `${name}/page`,
                method: 'get',
                params: params
            })
            this.formatPage(res.data)
            return res
        } catch (error) {

        }
    }

    formatPage(data) {
        // 库存日志，就显示比如 原煤购进1000吨，库存增加1000吨，  精煤销售200吨，库存减少200吨  ，生产精煤1000吨，精煤库存增加1000吨
        const log = {
            WEIGHT_IN: '磅房购进 ',
            WEIGHT_OUT: '磅房销售 ',
            BLENDING_IN: '生产增加 ',
            BLENDING_OUT: '生产消耗 ',
            STOCK_CHECK: '盘库 ',
            WASH_IN: '生产增加',
            WASH_OUT: '生产消耗'
        }
        data.records.forEach(item => {
            item.optLog = `${log[item['refType']]}`
        })
    }
}

export default new ChildModel()
