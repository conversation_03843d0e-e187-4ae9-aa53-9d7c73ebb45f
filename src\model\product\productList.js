import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import { typeName } from '@/const'

const name = '/cwe/a/product'

class InvoicingModel extends Model {
    constructor() {
        const dataJson = {
            id: '',
            name: '', // 合同名称
            code: '', // 编码
            coalCategory: '', // 煤种
            type: '', // 类型
            source: '', // 来源
            cleanMt: '', // 全水
            cleanAd: '', // 灰分
            cleanStd: '', // 硫
            cleanVdaf2: '',
            procY2: '',
            cleanVdaf: '', // 挥发分
            crc: '', // 特征
            procG: '', // 粘结
            procY: '', // 胶质,
            procX: '', // x指标
            remarks: '', // 备注
            isPublic: 'N', // 是否开放接口
            isPublicBoolean: false,
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '品名',
                    prop: 'name',
                    slot: 'name',
                    isShow: true,
                },
                // {
                //     label: '矿井名称',
                //     prop: 'mineName',
                //     align: 'center',
                //     width: 100,
                //     isShow: true,
                // },
                {
                    label: '煤种',
                    prop: 'coalCategory',
                    align: 'center',
                    isShow: true,
                },
                {
                    label: '类型',
                    prop: 'typeName',
                    align: 'center',
                    isShow: true,
                },
                // {
                //     label: '水分',
                //     prop: 'cleanMt',
                //     align: 'center',
                //     isShow: true,
                // },
                // {
                //     label: '单铲重量',
                //     prop: 'shovelWeight',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     label: '灰分Ad',
                //     prop: 'cleanAd',
                //     align: 'center',
                //     isShow: true,
                // },
                // {
                //     label: '硫St,d',
                //     prop: 'cleanStd',
                //     align: 'center',
                //     width: 120,
                //     isShow: true,
                // },
                // {
                //     label: '挥发分Vdaf',
                //     // prop: 'cleanVdaf',
                //     slot: 'cleanVdaf',
                //     // width: 100,
                //     isShow: true,
                //     align: "center"
                // },

                // {
                //     label: '特征',
                //     prop: 'crc',
                //     isShow: true,
                //     align: 'center',
                // },
                // {
                //     label: '粘结',
                //     prop: 'procG',
                //     align: 'center',
                //     isShow: true,

                // },
                // {
                //     label: 'Y',
                //     // prop: 'procY',
                //     slot: 'procY',
                //     isShow: true,
                // },

                // {
                //     label: 'X',
                //     prop: 'procX',
                //     align: 'center',
                //     isShow: true,
                // },
                // {
                //     label: '来源',
                //     prop: 'source',
                //     align: 'center',
                //     isShow: true,
                // },
                // {
                //     label: '是否开放接口',
                //     prop: 'isPublic',
                //     slot: 'isPublic',
                //     isShow: true,
                // },
                {
                    label: '备注',
                    prop: 'remarks',
                    align: 'center',
                    isShow: true,
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                },
            ],
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data,
        })
    }

    async page(params = {}) {
        const res = await fetch({
            url: `${name}/page`,
            method: 'get',
            params: params,
        })
        res.data.records.forEach(item => {
            item.isPublicBoolean = item.isPublic === 'Y'
            item.typeName = typeName[item.type]
        })
        return res
    }

    async saveList(text) {
        try {
            return await fetch({
                url: `${name}/saveList`,
                method: 'post',
                data: text,
            })
        } catch (error) {
            console.log(error)
        }
    }
}

export default new InvoicingModel()
