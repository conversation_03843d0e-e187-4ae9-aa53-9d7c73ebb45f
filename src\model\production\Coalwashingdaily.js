import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = '/cwe/a/washCoal'
class ChildModel extends Model {
    constructor() {
        const dataJson = {
            productionDate: (new Date(new Date().getTime())).Format('yyyy-MM-dd'), // 生产日期
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    prop: 'productionDate',
                    width: 100,
                    isShow: true,
                    align: 'center',
                    fixed: 'left'
                },
                // {
                //     label: '原煤id',
                //     prop: 'productIdRaw',
                //     slot: 'productIdRaw',
                //     isShow: true,
                //     align: 'center',
                // },
                {
                    label: '原煤品名',
                    prop: 'productNameRaw',
                    slot: 'productNameRaw',
                    isShow: true,
                    align: 'center',
                },
                {
                    label: '原煤矿井名',
                    prop: 'mineNameRaw',
                    slot: 'mineNameRaw',
                    isShow: true,
                    align: 'center',
                },
                {
                    label: '车数',
                    prop: 'truckCountRaw',
                    slot: 'truckCountRaw',
                    isShow: true,
                },
                {
                    label: '吨数',
                    prop: 'weightRaw',
                    slot: 'weightRaw',
                    isShow: true,
                },

                {
                    label: '皮带称吨数',
                    prop: 'beltWeightRaw',
                    slot: 'beltWeightRaw',
                    isShow: true,
                },
                {
                    label: '铲数',
                    prop: 'shovelCountRaw',
                    slot: 'shovelCountRaw',
                    isShow: true,
                },
                {
                    label: '铲车吨数',
                    prop: 'weightCalcRaw',
                    slot: 'weightCalcRaw',
                    isShow: true,
                },

                {
                    label: '精煤过磅车数',
                    prop: 'truckCount',
                    slot: 'truckCount',
                    isShow: true,
                },
                {
                    label: '精煤过磅吨数',
                    prop: 'weight',
                    slot: 'weight',
                    isShow: true,
                },
                {
                    label: '精煤皮带称吨数',
                    prop: 'beltWeight',
                    slot: 'beltWeight',
                    isShow: true,
                },
                {
                    label: '精煤铲车铲数',
                    prop: 'shovelCount',
                    slot: 'shovelCount',
                    isShow: true,
                },
                {
                    label: '精煤铲车吨数',
                    prop: 'weightCalc',
                    slot: 'weightCalc',
                    isShow: true,
                },

                // {
                //     label: '原煤车数',
                //     prop: 'truckCountRaw',
                //     isShow: true,
                // },
                // {
                //     label: '原煤铲数',
                //     prop: 'shovelCountRaw',
                //     isShow: true,
                // },
                // {
                //     label: '原煤吨数计算',
                //     prop: 'weightCalcRaw',
                //     isShow: true,
                // },
                // {
                //     label: '原煤吨数过磅',
                //     prop: 'weightRaw',
                //     isShow: true,
                // },
                {
                    label: '精煤id',
                    prop: 'productId',
                    slot: 'productId',
                    isShow: true,
                    align: 'center',
                },
                {
                    label: '精煤品名',
                    prop: 'productName',
                    slot: 'productName',
                    isShow: true,
                    align: 'center',
                },
                {
                    label: '精煤矿井名',
                    prop: 'mineName',
                    slot: 'mineName',
                    isShow: true,
                    align: 'center',
                },
                // {
                //     label: '精煤车数',
                //     prop: 'truckCount',
                //     isShow: true,
                // },
                // {
                //     label: '精煤铲数',
                //     prop: 'shovelCount',
                //     isShow: true,
                //     minWidth: 50
                // },
                // {
                //     label: '精煤吨数计算',
                //     prop: 'weightCalc',
                //     isShow: true,
                // },
                // {
                //     label: '精煤吨数过磅',
                //     prop: 'weight',
                //     isShow: true,
                // },

                {
                    noExport: true,
                    label: '原煤',
                    slot: 'chargingTest',
                    isShow: true
                },


                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    isShow: true,
                    slot: 'opt'
                }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/saveWashCoal`,
            method: 'post',
            data
        })
    }

    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }

    async detail(id) {
        const res = await fetch({
            url: `/cwe/a/washCoal/getDetail`,
            method: 'get',
            params: { id }
        })
        // this.formatDetail(res.data, ['_measureWeight', '_mt', '_innerCode', '_startAccumulated', '_stopAccumulated', '_setPercent1', '_setPercent2', '_setPercent3'])
        return res
    }

    deleteId(id) {
        return fetch({
            url: '/cwe/a/washCoal/deleteById',
            method: 'post',
            data: { id }
        })
    }

    getDateList(params = {}) {
        return fetch({
            url: `/cwe/a/washCoal/getDateList`,
            method: 'get',
            params: params
        })
    }

    formatPage(data) {
        if (!data.length) return data
        const sum = {}
        const lastInfo = { id: 'lastRow', }
        data.push(sum, lastInfo)
        return data
    }

    number(target, key, fixed = 2) {
        // console.log(target[key])
        return target[key] ? target[key].toFixed(fixed) : target[key]
    }
    // formatDetail(data, params = []) {
    //     data.mixCoalItemList.forEach(item => {
    //         for (const key of params) {
    //             item['_id'] = Math.random().toFixed(6).slice(-6)
    //             item['_stock'] = item.stock || 0
    //             item[key] = true
    //         }
    //     })
    // }
}
export default new ChildModel()
