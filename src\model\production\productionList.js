import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'


const name = '/cwe/a/mixCoal'

// const date = new Date()
// const defaultDate = new Date(date.getFullYear(), date.getMonth(), date.getDay(), 0, 0, 0)
class _ extends Model {
    constructor() {
        const dataJson = {
            productionDate: '', // 生产日期
            // productionDate: (new Date()).Format('yyyy-MM-dd'), // 生产日期
            // productName: '', // 产品名称
            // productCode: '', // 产品编码
            // productId: '', // 产品ID'
        }
        const form = {}
        const tableOption = {}
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `/cwe/a/mixCoal/saveMixCoal`,
            method: 'post',
            data
        })
    }

    // save(data) {
    //     return fetch({
    //         url: `${name}/saveMixCoal`,
    //         method: 'post',
    //         data
    //     })
    // }



    // page(searchForm) {
    //     return fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: searchForm
    //     })
    // }


    // async page(params = {}) {
    //     const res = await fetch({ url: `/cwe/a/mixCoal/findList`, method: 'get', params: params })
    //     // res.data.forEach(item => {
    //     //     this.formatPage(item.mixCoalVoList)
    //     // })
    //     // this.formatPage(res.data)
    //     return res
    // }

    async page(params = {}) {
        const res = await fetch({ url: `/cwe/a/mixCoal/findList2`, method: 'get', params: params })
        return res
    }



    formatPage(data) {
        if (!data.length) return data
        // const sum = {}
        const lastInfo = { id: 'lastRow', }
        // data.push(sum, lastInfo)
        data.push(lastInfo)
        return data
    }
    async detail(id) {
        const res = await fetch({
            url: `/cwe/a/mixCoal/getDetail`,
            method: 'get',
            params: { id }
        })
        // this.formatDetail(res.data, ['_measureWeight', '_mt', '_innerCode', '_startAccumulated', '_stopAccumulated', '_setPercent1', '_setPercent2', '_setPercent3'])
        return res
    }

    deleteId(id) {
        return fetch({
            url: '/cwe/a/mixCoal/deleteById',
            method: 'post',
            data: { id }
        })
    }

    getDateList(params = {}) {
        return fetch({
            // url: `/cwe/a/blendingProduction/getDateList`,
            url: `/cwe/a/mixCoal/getDateList`,
            method: 'get',
            params: params
        })
    }



    number(target, key, fixed = 2) {
        // console.log(target[key])
        return target[key] ? target[key].toFixed(fixed) : target[key]
    }
    // formatDetail(data, params = []) {
    //     data.mixCoalItemList.forEach(item => {
    //         for (const key of params) {
    //             item['_id'] = Math.random().toFixed(6).slice(-6)
    //             item['_stock'] = item.stock || 0
    //             item[key] = true
    //         }
    //     })
    // }
}

export default new _()
