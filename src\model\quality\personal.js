import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class EntrustOrder extends Model {
    constructor () {
        const dataJson = {
            'name': '',
            'applayUserName': '',
            'location': '',
            'sampleDate': '',
            'taxCompanyName': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [],
            rules: {}
        }
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '名称',
                    prop: 'name',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '名称',
                        prop: 'filter_LIKES_name'
                    }
                },
                {
                    label: '联系电话',
                    prop: 'phone',
                    isFilter: true,
                    filter: {
                        label: '联系电话',
                        prop: 'filter_LIKES_phone'
                    },
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '出生日期',
                    prop: 'birthDate',
                    slot: 'birthDate',
                    isShow: true,
                    minWidth: 140
                },
                {
                    label: '年龄',
                    prop: 'age',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '文化程度',
                    prop: 'education',
                    isFilter: true,
                    filter: {
                        label: '文化程度',
                        prop: 'filter_EQS_education',
                        type: 'b_personal_education'
                    },
                    component: 'DictSelect',
                    format: 'b_personal_education',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '性别',
                    prop: 'gender',
                    format: 's_gender',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '职称',
                    prop: 'duty',
                    isFilter: true,
                    filter: {
                        label: '职称',
                        prop: 'filter_LIKES_duty'
                    },
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '专业',
                    prop: 'major',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '邮编',
                    prop: 'postcode',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '工号',
                    prop: 'no',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '入职日期',
                    prop: 'entryDate',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '在职状态',
                    prop: 'onJobStatus',
                    format: 'b_personal_onJobStatus',
                    isFilter: true,
                    filter: {
                        label: '在职状态',
                        prop: 'filter_EQS_onJobStatus',
                        type: 'b_personal_onJobStatus'
                    },
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '部门名称',
                    prop: 'siteName',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '家庭地址',
                    prop: 'homeAddress',
                    isShow: true,
                    minWidth: 120
                },
                {
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            actions: []
        }
        super('/cwe/a/personal', dataJson, form, tableOption)
    }

    async save (searchForm) {
        return fetch({
            url: '/cwe/a/personal/savePersonal',
            method: 'post',
            data: { ...searchForm }
        })
    }

    async educationPage (searchForm) {
        return fetch({
            url: '/cwe/a/education/list',
            method: 'get',
            params: { ...searchForm }
        })
    }

    async workPage (searchForm) {
        return fetch({
            url: '/cwe/a/workExperience/list',
            method: 'get',
            params: { ...searchForm }
        })
    }

    async saveWork (searchForm) {
        return fetch({
            url: '/cwe/a/workExperience/save',
            method: 'post',
            data: { ...searchForm }
        })
    }

    async saveEducation (searchForm) {
        return fetch({
            url: '/cwe/a/education/save',
            method: 'post',
            data: { ...searchForm }
        })
    }
}

export default new EntrustOrder()
