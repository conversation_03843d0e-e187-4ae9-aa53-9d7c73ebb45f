import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class EntrustOrder extends Model {
    constructor () {
        const dataJson = {
            'name': '',
            'applayUserName': '',
            'location': '',
            'sampleDate': '',
            'taxCompanyName': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [],
            rules: {}
        }
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '培训方式',
                    prop: 'trainType',
                    slot: 'trainType',
                    isShow: true,
                    minWidth: 120,
                    isFilter: true,
                    filter: {
                        prop: 'filter_LIKES_trainType'
                    }
                },
                {
                    label: '培训类型',
                    prop: 'trainWay',
                    minWidth: 120,
                    isFilter: true,
                    format: 'b_project_train_trainWay',
                    filter: {
                        prop: 'filter_EQS_trainWay',
                        type: 'b_project_train_trainWay'
                    },
                    component: 'DictSelect',
                    isShow: true
                },
                {
                    label: '培训老师',
                    minWidth: 120,
                    prop: 'teacher',
                    isFilter: true,
                    filter: {
                        prop: 'filter_LIKES_teacher'
                    },
                    isShow: true
                },
                {
                    label: '计划培训时间',
                    prop: 'planTrainDate',
                    slot: 'planTrainDate',
                    minWidth: 120,
                    isFilter: true,
                    filter: {
                        start: 'filter_GED_planTrainDate',
                        end: 'filter_LED_planTrainDate'
                    },
                    component: 'DateRanger',
                    isShow: true
                },
                {
                    label: '培训预算',
                    prop: 'budget',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '主办单位',
                    prop: 'organizer',
                    minWidth: 120,
                    isShow: true
                },
                {
                    label: '培训主题',
                    minWidth: 120,
                    prop: 'theme',
                    isShow: true
                },
                {
                    label: '培训地点',
                    minWidth: 120,
                    prop: 'address',
                    isShow: true
                },
                {
                    label: '审核信息',
                    minWidth: 120,
                    prop: 'auditContent',
                    isShow: true
                },
                {
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            actions: []
        }
        super('/cwe/a/projectTrain', dataJson, form, tableOption)
    }

    async savePage (searchForm) {
        return fetch({
            url: '/cwe/a/projectTrain/saveProjectTrain',
            method: 'post',
            data: { ...searchForm }
        })
    }

    async findNotInCode (searchForm) {
        return fetch({
            url: '/cwe/a/trainPersonal/findNotInProjectTrainId',
            method: 'get',
            params: { ...searchForm }
        })
    }

    async findByCode (searchForm) {
        return fetch({
            url: '/cwe/a/trainPersonal/findByProjectTrainId',
            method: 'get',
            params: { ...searchForm }
        })
    }

    async addUser (searchForm) {
        return fetch({
            url: '/cwe/a/trainPersonal/addTrainPersonal',
            method: 'post',
            data: { ...searchForm }
        })
    }

    async delUser (searchForm) {
        return fetch({
            url: '/cwe/a/trainPersonal/delTrainPersonal',
            method: 'post',
            data: { ...searchForm }
        })
    }

    async reject (searchForm) {
        return fetch({
            url: '/cwe/a/projectTrain/auditReject',
            method: 'post',
            data: { ...searchForm }
        })
    }

    async accept (searchForm) {
        return fetch({
            url: '/cwe/a/projectTrain/auditPass',
            method: 'post',
            data: { ...searchForm }
        })
    }

    async deleteById (id) {
        return fetch({
            url: '/cwe/a/projectTrain/deleteById',
            method: 'post',
            data: { id }
        })
    }
}

export default new EntrustOrder()
