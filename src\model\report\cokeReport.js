import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class EntrustOrder extends Model {
    constructor () {
        const dataJson = {
            'name': '',
            'applayUserName': '',
            'location': '',
            'sampleDate': '',
            'taxCompanyName': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [],
            rules: {}
        }
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            offsetTop: -30,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '煤种',
                    prop: 'type',
                    isShow: true,
                    format: 's_assay_coal_type'
                },
                {
                    label: '数量',
                    prop: 'count',
                    isShow: true
                },
                {
                    label: '创建时间',
                    prop: 'creatDate',
                    isShow: false,
                    minWidth: '140',
                    isFilter: true,
                    filter: {
                        label: '时间范围',
                        start: 'beginDate',
                        end: 'endDate'
                    },
                    component: 'DateRanger'
                }
            ],
            actions: []
        }
        super('/cwe/a/assayApply', dataJson, form, tableOption)
    }

    async page (searchForm) {
        return fetch({
            url: '/cwe/a/assayApply/findAssayApplyTypeSummary',
            method: 'get',
            params: { ...searchForm }
        })
    }
}

export default new EntrustOrder()
