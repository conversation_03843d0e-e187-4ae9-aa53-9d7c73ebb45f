import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class EntrustOrder extends Model {
    constructor () {
        const dataJson = {
            'name': '',
            'applayUserName': '',
            'location': '',
            'sampleDate': '',
            'taxCompanyName': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [],
            rules: {}
        }
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            offsetTop: -30,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '样品名称',
                    prop: 'name',
                    isShow: true,
                    minWidth: '120',
                    isFilter: true,
                    filter: {
                        prop: 'name'
                    }
                },
                {
                    label: '客户名称',
                    prop: 'applayUserName',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        prop: 'applayUserName'
                    },
                    minWidth: '120'
                },
                {
                    label: '样品编号',
                    prop: 'code',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        prop: 'code'
                    },
                    minWidth: '120'
                },
                {
                    label: '煤种类型',
                    prop: 'type',
                    isShow: true,
                    minWidth: '120',
                    format: 's_assay_coal_type'
                },
                {
                    label: '预估费用',
                    prop: 'estimateFee',
                    isShow: true,
                    minWidth: '120'
                },
                {
                    label: '最终费用',
                    prop: 'finalFee',
                    isShow: true,
                    minWidth: '120'
                },
                {
                    label: '全水分(%)',
                    prop: 'rawMt',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '灰分(%)',
                    prop: 'rawAd',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '挥发分(%)',
                    prop: 'rawVdaf',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '固定碳(%)',
                    prop: 'rawFc',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '全硫(%)',
                    prop: 'rawStd',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '粘结性指数(%)',
                    prop: 'procGri',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '胶质层指数Y(mm)',
                    prop: 'procY',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '胶质层指数X(mm)',
                    prop: 'procX',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '煤岩组分-标准差S',
                    prop: 'macS',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '煤岩组-镜质组(%)',
                    prop: 'macV',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '煤岩组-丝质组(惰质组%)',
                    prop: 'macI',
                    isShow: true,
                    minWidth: '160'
                },
                {
                    label: '煤岩组-稳定组(壳质组%)',
                    prop: 'macE',
                    isShow: true,
                    minWidth: '160'
                },
                {
                    label: '软化温度(℃)',
                    prop: 'oyaT1',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '开始膨胀温度(℃)',
                    prop: 'oyaT2',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '固化温度(℃)',
                    prop: 'oyaT3',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '最大收缩度a(%)',
                    prop: 'oyaA',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '最大膨胀度b(%)',
                    prop: 'oyaB',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '氧化硅(%)',
                    prop: 'comSiO2',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '三氧化二铝(%)',
                    prop: 'comAl2O3',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '三氧化二铁(%)',
                    prop: 'comFe2O3',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '氧化钙(%)',
                    prop: 'comCaO',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '氧化镁(%)',
                    prop: 'comMgO',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '氧化钠(%)',
                    prop: 'comNa2O',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '氧化钾(%)',
                    prop: 'comK2O',
                    isShow: true,
                    minWidth: '140'
                }, {
                    label: '二氧化钛(%)',
                    prop: 'comTiO2',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '弹筒发热量',
                    prop: 'feverQb',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '灰熔融性软化温度(℃)',
                    prop: 'st',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '焦炭灰分Ad(%)',
                    prop: 'jrawAd',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '焦炭硫分St,d(%)',
                    prop: 'jrawStd',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '焦炭挥发分Vdaf(%)',
                    prop: 'jrawVdaf',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '抗碎强度(M40%)',
                    prop: 'qualM40',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '耐磨强度(M25%)',
                    prop: 'qualM25',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '耐磨强度(M10%)',
                    prop: 'qualM10',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '热反应性CRI(%)',
                    prop: 'qualCri',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '热强度CSR(%)',
                    prop: 'qualCsr',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '多级-1.4',
                    prop: 'rawGtOnePointFour',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '多级1.4-1.8',
                    prop: 'rawGtOnePointFourToOnePointEight',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '多级1.8',
                    prop: 'rawGtOnePointEight',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '1.4',
                    prop: 'rawOnePointFour',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '1.4-1.5',
                    prop: 'rawOnePointFourToOnePointFive',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '1.5-1.6',
                    prop: 'rawOnePointFiveToOnePointSix',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '1.6-1.8',
                    prop: 'rawOnePointSixToOnePointEight',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '1.8',
                    prop: 'rawOnePointEight',
                    isShow: true,
                    minWidth: '140'
                },
                {
                    label: '样品创建时间',
                    prop: 'assayApplyCreateDate',
                    isShow: true,
                    minWidth: '140',
                    isFilter: true,
                    filter: {
                        label: '样品创建时间',
                        start: 'beginDate',
                        end: 'endDate'
                    },
                    component: 'DateRanger'
                }
            ],
            actions: []
        }
        super('/cwe/a/assayMeta', dataJson, form, tableOption)
    }

    async page (searchForm) {
        return fetch({
            url: 'cwe/a/assayMeta/findAssayMeta',
            method: 'get',
            params: {
                ...searchForm
            }
        })
    }
}

export default new EntrustOrder()
