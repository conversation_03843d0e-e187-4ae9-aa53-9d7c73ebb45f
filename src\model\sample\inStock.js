import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class InStockModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': '',
            'type': '',
            'value': '',
            'remarks': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '样品名称',
                    prop: 'standardSampleName',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '样品名称',
                        prop: 'filter_LIKES_standardSampleName'
                    }
                },
                {
                    label: '收样人',
                    prop: 'refUserName',
                    isShow: true,
                    minWidth: 100,
                    isFilter: true,
                    filter: {
                        label: '收样人',
                        prop: 'filter_LIKES_refUserName'
                    }
                },
                {
                    label: '库存数量',
                    prop: 'amount',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '单位',
                    prop: 'unit',
                    minWidth: 100,
                    isShow: true
                },
                {
                    label: '批次号',
                    minWidth: 100,
                    prop: 'batchCode',
                    isShow: true
                },
                {
                    label: '操作人',
                    minWidth: 100,
                    prop: 'operatorName',
                    isShow: true
                },
                {
                    label: '入库时间',
                    prop: 'storageOutboundDate',
                    slot: 'storageOutboundDate',
                    isShow: true,
                    minWidth: 160
                },
                {
                    label: '生产日期',
                    minWidth: 160,
                    prop: 'productionDate',
                    slot: 'productionDate',
                    isShow: true
                },
                {
                    label: '操作时间',
                    minWidth: 160,
                    prop: 'createDate',
                    slot: 'createDate',
                    isShow: true
                },
                {
                    label: 'opt',
                    prop: 'opt',
                    slot: 'opt',
                    minWidth: 100,
                    isShow: true
                }
            ]
        }
        super('/cwe/a/standardSampleStockLog', dataJson, form, tableOption)
    }
    getInStock (data) {
        return fetch({
            url: '/cwe/a/standardSample/findByKeyword',
            method: 'get',
            params: { ...data }
        })
    }
    async save (data) {
        return fetch({
            url: '/cwe/a/standardSampleStockLog/storage',
            method: 'post',
            data: { ...data }
        })
    }
}

export default new InStockModel()
