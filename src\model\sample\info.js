import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class DictModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': '',
            'type': '',
            'value': '',
            'remarks': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '名称',
                    prop: 'name',
                    slot: 'name',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '名称',
                        prop: 'filter_LIKES_name'
                    }
                },
                {
                    label: '编号',
                    prop: 'code',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '编号',
                        prop: 'filter_LIKES_code'
                    },
                    minWidth: 100
                },
                {
                    label: '类型',
                    prop: 'type',
                    format: 's_assay_coal_type',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '类型',
                        type: 's_assay_coal_type',
                        prop: 'filter_EQS_type'
                    },
                    component: 'DictSelect',
                    minWidth: 100
                },
                {
                    label: '样品状态',
                    prop: 'sampleStatus',
                    isShow: true,
                    format: 's_assay_sample_type',
                    isFilter: true,
                    filter: {
                        label: '样品状态',
                        type: 's_assay_sample_type',
                        prop: 'filter_EQS_sampleStatus'
                    },
                    component: 'DictSelect',
                    minWidth: 100
                },
                {
                    label: '规格',
                    prop: 'specification',
                    minWidth: 100,
                    isShow: true
                },
                {
                    label: '保质期(天)',
                    minWidth: 100,
                    prop: 'shelfLife',
                    isShow: true
                },
                {
                    label: 'opt',
                    prop: 'opt',
                    slot: 'opt',
                    minWidth: 100,
                    isShow: true
                }
            ]
        }
        super('/cwe/a/standardSample', dataJson, form, tableOption)
    }

    async save (data) {
        return fetch({
            url: '/cwe/a/standardSample/saveStandardSample',
            method: 'post',
            data: { ...data }
        })
    }

    deleteById (id) {
        return fetch({
            url: '/cwe/a/standardSample/deleteById',
            method: 'post',
            data: { id }
        })
    }
}

export default new DictModel()
