import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class StockModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': '',
            'type': '',
            'value': '',
            'remarks': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '标样名称',
                    prop: 'standardSampleName',
                    isFilter: true,
                    isShow: true,
                    minWidth: 140,
                    filter: {
                        label: '样品名称',
                        prop: 'filter_LIKES_standardSampleName'
                    }
                },
                {
                    label: '数量',
                    prop: 'amount',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '领取人',
                    prop: 'refUserName',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '出库时间',
                    prop: 'storageOutboundDate',
                    slot: 'storageOutboundDate',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        start: 'filter_GED_storageOutboundDate',
                        end: 'filter_LED_storageOutboundDate'
                    },
                    component: 'DateRanger',
                    minWidth: 100
                },
                {
                    label: '标样编号',
                    prop: 'standardSampleCode',
                    isFilter: true,
                    isShow: true,
                    minWidth: 100,
                    filter: {
                        label: '标样编号',
                        prop: 'filter_LIKES_standardSampleCode'
                    }
                },
                {
                    label: '单位',
                    prop: 'unit',
                    minWidth: 100,
                    isShow: true
                },
                {
                    label: '批次号',
                    minWidth: 100,
                    prop: 'batchCode',
                    isShow: true
                },
                {
                    label: '操作人',
                    minWidth: 100,
                    prop: 'operatorName',
                    isShow: true
                },
                {
                    label: '操作时间',
                    minWidth: 100,
                    prop: 'createDate',
                    slot: 'createDate',
                    isShow: true
                },
                {
                    label: 'opt',
                    prop: 'opt',
                    slot: 'opt',
                    minWidth: 100,
                    isShow: true
                }
            ]
        }
        super('/cwe/a/standardSampleStockLog', dataJson, form, tableOption)
    }

    getOutStock (data) {
        return fetch({
            url: '/cwe/a/standardSampleStock/findByKeyword',
            method: 'get',
            params: { ...data }
        })
    }

    async save (data) {
        return fetch({
            url: '/cwe/a/standardSampleStockLog/outbound',
            method: 'post',
            data: { ...data }
        })
    }
}

export default new StockModel()
