import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class StockModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': '',
            'type': '',
            'value': '',
            'remarks': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            offsetTop: -30,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '名称',
                    prop: 'standardSampleName',
                    // slot: 'standardSampleName',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '名称',
                        prop: 'filter_LIKES_standardSampleName'
                    }
                },
                {
                    label: '库存数量',
                    prop: 'stockQuantity',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '批次号',
                    prop: 'batchCode',
                    isShow: true,
                    isFilter: true,
                    filter: {
                        label: '批次号',
                        prop: 'filter_LIKES_batchCode'
                    },
                    minWidth: 100
                },
                {
                    label: '生产日期',
                    prop: 'productionDate',
                    slot: 'productionDate',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '过期时间',
                    prop: 'expireDate',
                    slot: 'expireDate',
                    minWidth: 100,
                    isFilter: true,
                    filter: {
                        label: '过期时间',
                        start: 'filter_GED_expireDate',
                        end: 'filter_LED_expireDate'
                    },
                    component: 'DateRanger',
                    isShow: true
                },
                {
                    label: '单位',
                    minWidth: 100,
                    prop: 'unit',
                    isShow: true
                }
                // {
                //     label: 'opt',
                //     prop: 'opt',
                //     slot: 'opt',
                //     minWidth: 100,
                //     isShow: true
                // }
            ]
        }
        super('/cwe/a/standardSampleStock', dataJson, form, tableOption)
    }

    async save (data) {
        return fetch({
            url: '/cwe/a/standardSample/saveStandardSample',
            method: 'post',
            data: { ...data }
        })
    }
}

export default new StockModel()
