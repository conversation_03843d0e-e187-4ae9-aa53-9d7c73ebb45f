import ChildModel from './stock'
import fetch from '@/utils/request'

class ModelStock extends ChildModel {
    constructor() {
        const dataJson = {
            date: '', // 日期
            type: 'ZM', // 类型:半成品精煤-BCPJM,成品精煤-CPJM,原煤-YM,中煤-ZM
            name: '', // 品名
            ad: '', // 灰分
            qnet: '', // 发热量
            lastStock: '', // 上日库存
            todayIn: '', // 本日购入或生产
            todayOut: '', // 本日销售或使用
            stock: '', // 库存
            stockCheck: '',
            remarks: '' // 备注信息

        }
        const form = {}
        const tableOption = {
            sumIndex: 1,
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    isShow: true,
                    sortable: true,
                    // width: 100,
                    align: "center"
                },
                {
                    label: '品名',
                    prop: 'name',
                    slot: 'name',
                    isShow: true
                },
                // {
                //     label: '矿井名称',
                //     prop: 'mineName',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    label: '本日库存',
                    prop: 'stock',
                    isShow: true,
                    align: "center"
                },

                {
                    label: '本日购产',
                    prop: 'todayIn',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '本日销售',
                    prop: 'todayOut',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '盘库',
                    prop: 'stockCheck',
                    isShow: true
                },
                {
                    label: '上日库存',
                    prop: 'lastStock',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '上日库存价',
                //     prop: 'lastPrice',
                //     isShow: true,
                //     slot: 'lastPrice',
                // },
                // {
                //     label: '本日库存价',
                //     prop: 'price',
                //     isShow: true,
                //     slot: 'price',
                // },
                // {
                //     label: '水分',
                //     prop: 'mt',
                //     isShow: true,
                //     width: 100,
                //     align: "center"
                // },
                // {
                //     label: '备注信息',
                //     prop: 'remarks',
                //     width: 160,
                //     isShow: true,
                //     align: "center"
                // },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            sumText: '本页合计',
            summaries: [
                { prop: 'todayIn', suffix: '', fixed: 2, avg: false },
                { prop: 'stock', suffix: '', fixed: 2, avg: false },
                { prop: 'lastStock', suffix: '', fixed: 2, avg: false },
                { prop: 'stockCheck', suffix: '', fixed: 2, avg: false },
                { prop: 'todayOut', suffix: '', fixed: 2, avg: false },

            ]
        }
        super(dataJson, form, tableOption)
    }

    async page(params = {}) {
        const res = await fetch({
            url: `/cwe/a/stockDay/findPageOrderByStock`,
            method: 'get',
            params: params
        })
        this.formatRecords({ records: res.data.records, fields: ['ad', 'std', 'remarks'] })
        return res
    }
}

export default new ModelStock()
