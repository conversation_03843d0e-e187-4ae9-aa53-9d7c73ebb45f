import ChildModel from './stock'
import fetch from '@/utils/request'

const today = new Date().Format('yyyy-MM-dd')
const name = '/cwe/a/stock'
class ModelStock extends ChildModel {
    constructor() {
        const dataJson = {
            date: today, // 日期
            type: '', // 类型:半成品精煤-BCPJM,成品精煤-CPJM,原煤-YM,中煤-ZM
            name: '', // 品名
            ad: '', // 灰分
            std: '', // 硫
            lastStock: '', // 上日库存
            todayIn: '', // 本日购入或生产
            todayOut: '', // 本日销售或使用
            stock: '', // 库存
            wasteRock: '', // 坑石矸
            remarks: '', // 备注信息,
            editStock: 0
        }
        const form = {}
        const tableOption = {
            showSelection: true,
            selectAtBottom: true,
            sumIndex: 1,
            columns: [
                {
                    label: '品名',
                    prop: 'name',
                    slot: 'name',
                    isShow: true,
                },
                // {
                //     label: '矿井名称',
                //     prop: 'mineName',
                //     isShow: true,
                // },
                // {
                //     label: '类型',
                //     prop: 'typeName',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    label: '实时库存',
                    prop: 'stock',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '库存价',
                //     prop: 'price',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    label: '水分',
                    prop: 'mt',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '灰分',
                    prop: 'ad',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '硫',
                    prop: 'std',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '挥发份',
                    prop: 'vdaf',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '备注信息',
                //     prop: 'remarks',
                //     width: 160,
                //     isShow: true,
                //     align: "center"
                // },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }

            ],
            sumText: '本页合计',
            summaries: [
                { prop: 'stock', suffix: '', fixed: 2, avg: false }
            ]
        }
        super(dataJson, form, tableOption, name)
    }
    async page(params = {}) {
        const res = await fetch({
            url: `${name}/page`,
            method: 'get',
            params: params
        })
        const typeName = {
            YM: '原煤',
            BCPJM: '半成品精煤',
            CPJM: '成品精煤',
            ZM: '中煤',
            GS: '矸石'
        }
        res.data.records.forEach(item => {
            item.date = today
            item.typeName = typeName[item.type]
            if (item.mt) {
                item.mt = parseFloat(item.mt).toFixed(1)
            }
            if (item.stock) {
                item.stock = parseFloat(item.stock).toFixed(2)
            }
            if (item.ad) {
                item.ad = parseFloat(item.ad).toFixed(2)
            }
            if (item.std) {
                item.std = parseFloat(item.std).toFixed(2)
            }
            if (item.vdaf) {
                item.vdaf = parseFloat(item.vdaf).toFixed(2)
            }
            item.editStock = ''
            item._stock = 0 // 传入服务端的库存
            item.stockBak = item.stock * 1
        })
        return res
    }

    inventory(data) {
        return fetch({
            url: name + '/inventory',
            method: 'post',
            data
        })
    }
    batchInventory(data) {
        return fetch({
            url: name + '/batchInventory',
            method: 'post',
            data
        })
    }
    stockLog(data) {
        return fetch({
            url: name + '/stockLog',
            method: 'post',
            data
        })
    }
    setTop(data) {
        return fetch({
            url: name + '/setTop',
            method: 'post',
            data
        })
    }

    refreshByName(data) {
        return fetch({
            url: '/cwe/a/stockDay/refreshByName',
            method: 'post',
            data
        })
    }
    //刷新库存
    async refreshIndicators(data) {
        return fetch({
            url: `${name}/refreshIndicators`,
            method: 'post',
            data
        })
    }
    //刷新库存价  
    async refreshPrice(data) {
        return fetch({
            url: `${name}/refreshPrice`,
            method: 'post',
            data
        })
    }
}

export default new ModelStock()
