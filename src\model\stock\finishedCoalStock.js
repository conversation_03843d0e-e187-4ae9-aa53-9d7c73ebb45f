import ChildModel from './stock'

class ModelStock extends ChildModel {
    constructor() {
        const dataJson = {
            date: '', // 日期
            type: 'CPJM', // 类型:半成品精煤-BCPJM,成品精煤-CPJM,原煤-YM,中煤-ZM
            name: '', // 品名
            ad: '', // 灰分
            std: '', // 硫
            lastStock: '', // 上日库存
            todayIn: '', // 本日购入或生产
            todayOut: '', // 本日销售或使用
            stock: '', // 库存
            stockCheck: '',
            remarks: '' // 备注信息
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '日期',
                    sortable: true,
                    prop: 'date',
                    isShow: true,
                    align: "center"
                    // span: 8,
                    // width: 100
                },
                {
                    label: '品名',
                    prop: 'name',
                    slot: 'name',
                    isShow: true
                },
                // {
                //     label: '矿井名称',
                //     prop: 'mineName',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    label: '本日库存',
                    prop: 'stock',
                    align: "center",
                    isShow: true
                },

                {
                    label: '本日产出',
                    prop: 'todayIn',
                    align: "center",
                    isShow: true
                },
                {
                    label: '本日销售',
                    prop: 'todayOut',
                    align: "center",
                    isShow: true
                },
                {
                    label: '盘库',
                    prop: 'stockCheck',
                    align: "center",
                    isShow: true
                },
                {
                    label: '上日库存',
                    prop: 'lastStock',
                    align: "center",
                    isShow: true
                },
                // {
                //     label: '上日库存价',
                //     prop: 'lastPrice',
                //     isShow: true,
                //     slot: 'lastPrice',
                // },
                // {
                //     label: '本日库存价',
                //     prop: 'price',
                //     isShow: true,
                //     slot: 'price',
                // },
                // {
                //     label: '水分',
                //     prop: 'mt',
                //     isShow: true,
                //     width: 100,
                //     align: "center"
                // },
                // {
                //     label: '备注信息',
                //     prop: 'remarks',
                //     width: 160,
                //     isShow: true,
                //     span: 24
                // },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            sumIndex: 0,
            // yesterday: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'),
            sumText: '本页合计',
            summaries: [
                { prop: 'lastStock', suffix: '', fixed: 2, avg: false },
                { prop: 'todayIn', suffix: '', fixed: 2, avg: false },
                { prop: 'todayOut', suffix: '', fixed: 2, avg: false },
                { prop: 'stockCheck', suffix: '', fixed: 2, avg: false },
                { prop: 'stock', suffix: '', fixed: 2, avg: false }
            ]
        }
        super(dataJson, form, tableOption)
    }
}

export default new ModelStock()
