import ChildModel from './stock'
class ModelStock extends ChildModel {
    constructor() {
        const dataJson = {
            date: '', // 日期
            type: 'BCPJM', // 类型-成品、半成品等
            name: '', // 品名
            ad: '', // 灰分
            std: '', // 硫
            lastStock: '', // 上日库存
            todayIn: '', // 本日购产
            todayOut: '', // 本日配销
            stock: '', // 本日库存
            stockCheck: '',
            wasteRock: '', // 坑石矸
            remarks: '' // 备注信息
        }
        const form = {}
        const tableOption = {
            // yesterday: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'),
            sumIndex: 0,
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    sortable: true,
                    align: "center",
                    isShow: true,
                    // width: 100
                },
                {
                    label: '品名',
                    prop: 'name',
                    slot: 'name',
                    isShow: true
                },
                // {
                //     label: '矿井名称',
                //     prop: 'mineName',
                //     isShow: true
                // },
                {
                    label: '灰分Ad≤',
                    prop: 'ad',
                    slot: 'ad',
                    isShow: true
                },
                {
                    label: '硫分St,d≤',
                    prop: 'std',
                    slot: 'std',
                    isShow: true
                },

                {
                    label: '本日库存',
                    prop: 'stock',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '本日购产',
                    prop: 'todayIn',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '本日配销',
                    prop: 'todayOut',
                    isShow: true,
                    align: "center",
                },
                {
                    label: '盘库',
                    width: 120,
                    prop: 'stockCheck',
                    align: "center",
                    isShow: true
                },
                {
                    label: '上日库存',
                    prop: 'lastStock',
                    align: "center",
                    isShow: true
                },
                // {
                //     label: '上日库存价',
                //     prop: 'lastPrice',
                //     isShow: true,
                //     slot: 'lastPrice',
                // },
                // {
                //     label: '本日库存价',
                //     prop: 'price',
                //     isShow: true,
                //     slot: 'price',
                // },
                // {
                //     label: '水分',
                //     prop: 'mt',
                //     isShow: true,
                //     width: 100,
                //     align: "center"
                // },
                // {
                //     label: '备注信息',
                //     prop: 'remarks',
                //     slot: 'remarks',
                //     isShow: true
                // },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            sumText: '本页合计',
            summaries: [
                { prop: 'todayIn', suffix: '', fixed: 2, avg: false },
                { prop: 'lastStock', suffix: '', fixed: 2, avg: false },
                { prop: 'todayOut', suffix: '', fixed: 2, avg: false },
                { prop: 'stockCheck', suffix: '', fixed: 2, avg: false },
                { prop: 'stock', suffix: '', fixed: 2, avg: false }
            ]

        }
        super(dataJson, form, tableOption)
    }
}

export default new ModelStock()
