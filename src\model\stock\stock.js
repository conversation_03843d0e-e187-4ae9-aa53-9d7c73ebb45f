import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = '/cwe/a/stockDay'
class StockModel extends Model {
    constructor(dataJson, form, tableOption, name = '/cwe/a/stockDay') {
        super(name, dataJson, form, tableOption)
    }

    get tableOption() {
        return super.tableOption
    }
    save(data) {
        return fetch({
            url: name + '/save',
            method: 'post',
            data
        })
    }
    async page(params = {}) {
        const res = await fetch({
            // url: `${name}/page`,
            url: `${name}/findPageOrderByStock`,
            method: 'get',
            params: params
        })
        this.formatRecords({ records: res.data.records, fields: ['ad', 'std', 'remarks'] })
        return res
    }
    // 图表接口
    pages(params = {}) {
        return fetch({
            url: `/cwe/a/stockDay/page`,
            method: 'get',
            params: params
        })
    }
    getLastDayStock(data) {
        return fetch({
            url: name + '/getLastDayStock',
            method: 'get',
            params: { ...data }
        })
    }

    refresh(data) {
        return fetch({
            url: `${name}/refreshByNameNoZero`,
            method: 'post',
            data
        })
    }
    inventory(data) {
        return fetch({
            url: name + '/inventory',
            method: 'post',
            data
        })
    }
    batchInventory(data) {
        return fetch({
            url: name + '/batchInventory',
            method: 'post',
            data
        })
    }
    stockLog(data) {
        return fetch({
            url: name + '/stockLog',
            method: 'post',
            data
        })
    }

    formatRecords({ records, fields }) {
        records.forEach(item => {
            item._all = false
            item.active = {}
            item.bak = {}
            for (const field of fields) {
                item.active[field] = false
                item.bak[field] = item[field]
            }
        })
        return records
    }
}

export default StockModel
