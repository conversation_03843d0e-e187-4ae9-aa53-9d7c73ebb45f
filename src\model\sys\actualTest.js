import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class ActualModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '类别名',
                    prop: 'categoryName',
                    slot: 'categoryName',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '类别名',
                        prop: 'filter_LIKES_categoryName'
                    },
                    minWidth: 160
                },
                {
                    label: '名称',
                    prop: 'name',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '名称',
                        prop: 'filter_LIKES_name'
                    },
                    minWidth: 100
                },
                {
                    label: '类型',
                    prop: 'type',
                    format: 's_assay_type',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '类型',
                        prop: 'filter_LIKES_name',
                        type: 's_assay_type'
                    },
                    component: 'DictSelect',
                    minWidth: 60
                },
                {
                    label: '符号',
                    prop: 'mark',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '单位',
                    prop: 'unit',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '界面类型',
                    prop: 'uiType',
                    format: 's_input_type',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '是否必填',
                    prop: 'required',
                    isShow: true,
                    format: 's_yes_or_no',
                    minWidth: 100
                },
                {
                    label: '参考标准',
                    prop: 'reference',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                    minWidth: 100
                }
            ]
        }
        super('/cwe/a/indexMeta', dataJson, form, tableOption)
    }

    async page (searchForm) {
        // /cwe/a/assayCategory
        return super.page(searchForm)
    }

    async pageList (searchForm) {
        return fetch({
            url: '/cwe/a/indexMeta/list',
            method: 'get',
            params: { searchForm }
        })
    }
}

export default new ActualModel()
