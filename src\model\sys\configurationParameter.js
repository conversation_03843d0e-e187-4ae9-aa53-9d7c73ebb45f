import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

const name = '/cwe/a/config'

class InvoicingModel extends Model {
    constructor() {
        const dataJson = {
        }
        const form = {}
        const tableOption = {
            columns: [
            ],
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/saveEmailConfig`,
            method: 'post',
            data,
        })
    }

    async getEmailConfig(params = {}) {
        const res = await fetch({
            url: `${name}/getEmailConfig`,
            method: 'get',
            params: params,
        })
        return res
    }

    savetestSendEmail(data) {
        return fetch({
            url: `${name}/testSendEmail`,
            method: 'post',
            data,
        })
    }
    //获取模板接口
    getsmsTemplate(params = {}) {
        return fetch({
            url: '/cwe/a/smsTemplate/list',
            method: 'get',
            params,
        })
    }
    //保存模板接口
    smsTemplatesave(data) {
        return fetch({
            url: '/cwe/a/smsTemplate/save',
            method: 'post',
            data,
        })
    }

    //获取打印模板接口
    getprintTemplate(params = {}) {
        return fetch({
            url: '/cwe/a/printTemplate/list',
            method: 'get',
            params,
        })
    }

    // printupload(data) {
    //     return fetch({
    //         url: '/cwe/a/printTemplate/upload',
    //         method: 'post',
    //         data,
    //     })
    // }

    printuploadsave(data) {
        return fetch({
            url: '/cwe/a/printTemplate/updateContent',
            method: 'post',
            data,
        })
    }




}

export default new InvoicingModel()
