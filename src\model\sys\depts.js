import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class ActualModel extends Model {
    constructor() {
        const dataJson = {
            'id': '',
            'name': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '部门名称',
                    prop: 'label',
                    isShow: true,
                    minWidth: 100,
                    isFilter: true,
                    filter: {
                        label: '部门名称',
                        prop: 'filter_LIKES_label'
                    },
                },
                // {
                //     label: '创建时间',
                //     prop: 'reference',
                //     isShow: true,
                //     minWidth: 100
                // },
                {
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                    minWidth: 100
                }
            ]
        }
        super('/cwe/a/indexMeta', dataJson, form, tableOption)
    }

    async page(searchForm) {
        return fetch({
            url: '/cwe/a/dept/findAllTreeDepts',
            method: 'get',
            params: {searchForm}
        })
    }

    async pageList(searchForm) {
        return fetch({
            url: '/cwe/a/indexMeta/list',
            method: 'get',
            params: {searchForm}
        })
    }
}

export default new ActualModel()
