import Model from '@/s-curd/src/model'

class DictModel extends Model {
    constructor() {
        const dataJson = {
            'id': '',
            'name': '',
            'type': '',
            'value': '',
            'remarks': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [
                {
                    label: '名称',
                    prop: 'name',
                    span: 8
                },
                {
                    label: '类型',
                    prop: 'type',
                    span: 8
                },
                {
                    label: '数据值',
                    prop: 'value',
                    span: 8
                },
                {
                    label: '备注信息',
                    prop: 'remarks',
                    row: 5,
                    type: 'textarea',
                    resize: 'none',
                    span: 22
                }
            ],
            rules: {
                name: [
                    { required: true, message: '请输入名称' }
                ],
                type: [
                    { required: true, message: '请输入类型' }
                ]
            }
        }
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '名称',
                    prop: 'name',
                    slot: 'name',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '名称',
                        prop: 'filter_LIKES_name'
                    },
                    width: 300
                },
                {
                    label: '类型',
                    prop: 'type',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '类型',
                        prop: 'filter_LIKES_type'
                    },
                    width: 300
                },
                {
                    label: '数据值',
                    prop: 'value',
                    isShow: true
                },
                {
                    label: '备注信息',
                    prop: 'remarks',
                    isShow: true
                },
                {
                    label: '编辑',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                    fixed: 'right',
                    width: 100
                }
            ]
        }
        super('/cwe/a/dict', dataJson, form, tableOption)
    }
}

export default new DictModel()
