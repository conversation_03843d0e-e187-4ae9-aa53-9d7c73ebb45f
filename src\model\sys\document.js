import Model from '@/s-curd/src/model'

class Document extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': '',
            'type': '',
            'value': '',
            'remarks': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '名称',
                    prop: 'fileName',
                    slot: 'fileName',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '名称',
                        prop: 'filter_LIKES_fileName'
                    }
                },
                {
                    label: '文件后缀',
                    prop: 'fileSuffix',
                    isShow: true
                },
                {
                    label: '创建人',
                    prop: 'createBy',
                    isShow: true
                },
                {
                    label: '创建时间',
                    prop: 'createDate',
                    isShow: true
                },
                {
                    label: '修改',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                    fixed: 'right',
                    width: 100
                }
            ]
        }
        super('/cwe/a/document', dataJson, form, tableOption)
    }
}

export default new Document()
