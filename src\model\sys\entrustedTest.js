import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class TestModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            offsetTop: 70,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '类别名',
                    prop: 'name',
                    slot: 'name',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '类别名',
                        prop: 'filter_LIKES_name'
                    },
                    width: 300
                },
                {
                    label: '编号',
                    prop: 'code',
                    isShow: true,
                    width: 50
                },
                {
                    label: '别名',
                    prop: 'aliasName',
                    isShow: true,
                    width: 120
                },
                {
                    label: '关联化验项',
                    prop: 'relItemCode',
                    isShow: true,
                    width: 110
                },
                {
                    label: '价格',
                    prop: 'price',
                    isShow: true,
                    width: 80
                },
                {
                    label: '类型',
                    prop: 'type',
                    isFilter: true,
                    isShow: true,
                    format: 's_assay_type',
                    width: 50,
                    filter: {
                        label: '类型',
                        type: 's_assay_type',
                        prop: 'filter_LIKES_type'
                    },
                    component: 'DictSelect'
                },
                {
                    label: '基础化验指标',
                    prop: 'indexDesc',
                    isShow: true
                },
                {
                    label: '备注信息',
                    prop: 'remarks',
                    isShow: true,
                    width: 100
                },
                {
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ]
        }
        super('/cwe/a/assayItem', dataJson, form, tableOption)
    }

    async pageList (data) {
        return fetch({
            url: '/cwe/a/assayItem/list',
            method: 'get',
            params: {
                ...data
            }
        })
    }

    async saveWithMultiIndexMetas (data) {
        return fetch({
            url: '/cwe/a/assayItem/saveWithMultiIndexMetas',
            method: 'post',
            data: {
                ContentType: 'json',
                ...data
            }
        })
    }

    async getById (id) {
        return fetch({
            url: '/cwe/a/assayItem/getById',
            method: 'get',
            params: {
                ...id
            }
        })
    }

    async saveRelItemCode (data) {
        return fetch({
            url: '/cwe/a/assayItem/saveRelItemCode',
            method: 'post',
            data: {
                ...data
            }
        })
    }
}

export default new TestModel()
