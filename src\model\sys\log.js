import Model from '@/s-curd/src/model'

class Log extends Model {
    constructor () {
        const dataJson = {
        }
        const form = {}
        const tableOption = {
            // showSetting: true,
            add: false,
            edit: false,
            del: false,
            // showIndex: true,
            showPage: true,
            // showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '时间',
                    prop: 'createDate',
                    isShow: true
                },
                {
                    label: '登录名',
                    prop: 'loginName',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '姓名',
                    prop: 'userName',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '模块',
                    prop: 'module',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '操作',
                    prop: 'method',
                    isShow: true,
                    minWidth: 100
                }
            ]
        }
        super('/cwe/a/systemLog', dataJson, form, tableOption)
    }
}

export default new Log()
