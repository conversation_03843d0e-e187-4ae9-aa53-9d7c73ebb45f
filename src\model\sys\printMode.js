import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class ActualModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '名称',
                    prop: 'name',
                    slot: 'name',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '名称',
                        prop: 'filter_LIKES_name'
                    },
                    minWidth: 100
                },
                {
                    label: '应用ID',
                    prop: 'clientId',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '应用秘钥',
                    prop: 'clientSecret',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '终端号',
                    prop: 'machineCode',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '终端秘钥',
                    prop: 'machineCode',
                    isShow: true,
                    minWidth: 100
                },
                {
                    label: '创建时间',
                    prop: 'createDate',
                    isShow: true,
                    minWidth: 100
                }
            ]
        }
        super('/cwe/a/printer', dataJson, form, tableOption)
    }
    async pageList (searchForm) {
        return fetch({
            url: '/cwe/a/indexMeta/list',
            method: 'get',
            params: { searchForm }
        })
    }
}

export default new ActualModel()
