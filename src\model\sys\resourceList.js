import Model from '@/s-curd/src/model'

class ResourceModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'menuCode': '',
            'code': '',
            'name': '',
            'sort': '',
            'createBy': '',
            'createDate': '',
            'updateBy': '',
            'updateDate': '',
            'ext': '',
            'remarks': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [
                {
                    label: '编码',
                    prop: 'code',
                    span: 8
                },
                {
                    label: '名称',
                    prop: 'name',
                    span: 8
                },
                {
                    label: '排序',
                    prop: 'sort',
                    span: 8
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    span: 24,
                    type: 'textarea'
                }
            ],
            rules: {
                code: [{ required: true, message: '请输入编码' }],
                name: [{ required: true, message: '请输入名称' }],
                sort: [{ required: true, message: '请输入排序' }]
            }
        }
        const tableOption = {
            printAble: 'staffWorkList:printAble',
            showSetting: true,
            exportAble: 'staffWorkList:export',
            add: false,
            edit: true,
            del: false,
            // showIndex: true,
            showPage: true,
            // showSelection: true,
            mountQuery: false,
            columns: [
                {
                    label: '菜单编码',
                    prop: 'menuCode',
                    isShow: false
                },
                {
                    label: '编码',
                    prop: 'code',
                    isShow: true
                },
                {
                    label: '名称',
                    prop: 'name',
                    isShow: true
                    // filter: {
                    //     label: '名称',
                    //     prop: 'filter_EQS_name'
                    // }
                },
                {
                    label: '排序',
                    prop: 'sort',
                    isShow: true
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true
                }
            ],
            actions: []
        }
        super('/cwe/a/resource', dataJson, form, tableOption)
        this.filter_EQS_menuCode = ''
    }

    async page (searchForm) {
        let form = {
            ...searchForm,
            filter_EQS_menuCode: this.filter_EQS_menuCode
        }
        return super.page(form)
    }

    save (data) {
        data.menuCode = this.filter_EQS_menuCode
        return super.save(data)
    }

    getMenuCode (code) {
        this.filter_EQS_menuCode = code
    }
}

export default new ResourceModel()
