import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

const name = '/cwe/a/flowDesign'

class InvoicingModel extends Model {
    constructor() {
        const dataJson = {
            type: 'BUY'
        }
        const form = {

        }
        const tableOption = {
            columns: [
                {
                    label: '编码',
                    prop: 'code',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '名称',
                    prop: 'name',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '别名',
                    prop: 'aliasName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '排序',
                    prop: 'sort',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '所属状态',
                    prop: 'belongState',
                    slot: 'belongState',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '上一流程',
                    prop: 'previousFlowName',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '下一个流程',
                    prop: 'nextFlowName',
                    isShow: true,
                    align: "center"
                },

                {
                    label: '权限编码',
                    prop: 'resourceCode',
                    isShow: true,
                    align: "center"
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                    width: 30
                }
            ],
        }
        super(name, dataJson, form, tableOption)
    }
    async page(params = {}) {
        params.orderBy = 'sort'
        params.orderDir = 'asc'
        if (!params.filter_EQS_type) {
            params.filter_EQS_type = 'BUY'
        }
        const res = await fetch({
            url: `${name}/page`,
            method: 'get',
            params: params
        })
        return res
    }

    save(data) {
        return fetch({
            url: `${name}/saveFlowDesign`,
            method: 'post',
            data,
        })
    }

    // async getEmailConfig(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/getEmailConfig`,
    //         method: 'get',
    //         params: params,
    //     })
    //     console.log(res)
    //     return res
    // }


    //获取流程是下拉选择
    // getlclist(params = {}) {
    //     return fetch({
    //         url: `${name}/list`,
    //         // url: '/cwe/a/flowDesign/list',
    //         method: 'get',
    //         params,
    //     })
    // }

    getlclist(params = {}) {
        return fetch({
            url: `${name}/findListByType`,
            method: 'get',
            params,
        })
    }


    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }

    async getDetailList(id) {
        const res = await fetch({
            url: `${name}/getDetail`,
            method: 'get',
            params: { id }
        })
        return res
    }



    getroleList(params = {}) {
        return fetch({
            url: `/cwe/a/role/list`,
            method: 'get',
            params
        })
    }

    getuserList(params = {}) {
        return fetch({
            url: `/cwe/a/user/list `,
            method: 'get',
            params
        })
    }


    // 、权限保存
    savePermissions(data) {
        return fetch({
            url: `${name}/savePermissions`,
            method: 'post',
            data,
        })
    }
}

export default new InvoicingModel()
