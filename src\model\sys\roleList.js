import Model from '@/s-curd/src/model'

class RoleModel extends Model {
    constructor() {
        const dataJson = {
            'id': '',
            'name': '',
            'code': '',
            'createBy': '',
            'createDate': '',
            'updateBy': '',
            'updateDate': '',
            'ext': '',
            'remarks': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [
                {
                    label: '角色名称',
                    prop: 'name',
                    span: 8
                },
                {
                    label: '备注信息',
                    prop: 'remarks',
                    span: 24,
                    type: 'textarea'
                }
            ],
            rules: {}
        }
        const tableOption = {
            // printAble: 'roleList:printAble',
            printAble: false,
            showSetting: true,
            exportAble: 'roleList:export',
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '角色名称',
                    prop: 'name',
                    slot: 'name',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '角色名称',
                        prop: 'filter_EQS_name'
                    }
                },
                {
                    label: '角色编码',
                    prop: 'code',
                    isFilter: false,
                    isShow: true
                },
                {
                    label: '用户',
                    prop: 'userNames',
                    isFilter: false,
                    isShow: true
                },
                {
                    label: '备注信息',
                    prop: 'remarks',
                    isShow: true
                }
            ],
            actions: []
        }
        super('/cwe/a/role', dataJson, form, tableOption)
    }
}

export default new RoleModel()
