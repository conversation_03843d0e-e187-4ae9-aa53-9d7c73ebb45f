import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class StationModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': '',
            'code': '',
            'createBy': '',
            'createDate': '',
            'updateBy': '',
            'updateDate': '',
            'ext': '',
            'remarks': ''
        }
        const form = {
            config: {
                labelWidth: '82px',
                labelPosition: 'right'
            },
            columns: [
                {
                    label: '角色名称',
                    prop: 'name',
                    span: 8
                },
                {
                    label: '备注信息',
                    prop: 'remarks',
                    span: 24,
                    type: 'textarea'
                }
            ],
            rules: {}
        }
        const tableOption = {
            printAble: 'roleList:printAble',
            showSetting: true,
            exportAble: 'roleList:export',
            add: false,
            // exportAble: 'roleList:export',
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '岗位名称',
                    prop: 'name',
                    slot: 'name',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '岗位名称',
                        prop: 'filter_EQS_name'
                    }
                },
                {
                    label: '角色编码',
                    prop: 'code',
                    isFilter: false,
                    isShow: false
                },
                {
                    label: '备注信息',
                    prop: 'remarks',
                    isShow: true
                }
            ],
            actions: []
        }
        super('/cwe/a/station', dataJson, form, tableOption)
    }

    stationPage (params) {
        return fetch({
            url: '/cwe/a/userStation/page',
            method: 'get',
            params: { params }
        })
    }

    savePage (data) {
        return fetch({
            url: '/cwe/a/station/saveWithIndexMetas',
            method: 'post',
            data: {
                ContentType: 'json',
                ...data
            }
        })
    }

    getById (id) {
        return fetch({
            url: '/cwe/a/station/getById',
            method: 'get',
            params: { id }
        })
    }

    findByCode (params) {
        return fetch({
            url: '/cwe/a/user/findByStationCode',
            method: 'get',
            params: { ...params }
        })
    }

    findNotInCode (params) {
        return fetch({
            url: '/cwe/a/user/findNotInStationCode',
            method: 'get',
            params: { ...params }
        })
    }

    addUser ({ stationCode, userCodeList }) {
        return fetch({
            url: '/cwe/a/user/addStationUser',
            method: 'post',
            data: { stationCode, userCodeList }
        })
    }

    delUser ({ stationCode, userCodeList }) {
        return fetch({
            url: '/cwe/a/user/delStationUser',
            method: 'post',
            data: { stationCode, userCodeList }
        })
    }

    delById (id) {
        return fetch({
            url: '/cwe/a/station/deleteById',
            method: 'post',
            data: { id }
        })
    }
}

export default new StationModel()
