import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'

class TextItemModel extends Model {
    constructor () {
        const dataJson = {
            'id': '',
            'name': ''
        }
        const form = {}
        const tableOption = {
            showSetting: true,
            add: false,
            edit: false,
            del: false,
            showIndex: true,
            showPage: true,
            showSelection: true,
            mountQuery: true,
            columns: [
                {
                    label: '名称',
                    prop: 'name',
                    slot: 'name',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '名称',
                        prop: 'filter_LIKES_name'
                    },
                    width: 300
                },
                {
                    label: '类别',
                    prop: 'type',
                    format: 's_assay_type',
                    isFilter: true,
                    isShow: true,
                    filter: {
                        label: '类别',
                        prop: 'filter_LIKES_type',
                        type: 's_assay_type'
                    },
                    component: 'DictSelect',
                    width: 300
                },
                {
                    label: '备注信息',
                    prop: 'remarks',
                    isShow: true
                }
            ]
        }
        super('/cwe/a/assayCategory', dataJson, form, tableOption)
    }

    async page (searchForm) {
        // /cwe/a/assayCategory
        return super.page(searchForm)
    }

    async pageList (searchForm) {
        return fetch({
            url: '/cwe/a/assayCategory/list',
            method: 'get',
            params: {
                ...searchForm
            }
        })
    }
}

export default new TextItemModel()
