import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
import { typeName, COMPANY } from '@/const'
const name = `/cwe/a/contractSell`
class _ extends Model {
    constructor() {
        const dataJson = {
            code: '', // 合同编码
            name: '', // 合同名称
            productId: '',
            productName: '',
            productCode: '',
            coalType: '', // 类型-焦肥气瘦
            coalCategory: '', // 煤的类型 原煤、精煤
            firstParty: '', // 甲方-供货单位
            // secondParty: COMPANY(), //  卖方
            secondParty: '雅达讯', //  卖方
            amount: '', // 吨数
            cleanMt: '', // 全水
            cleanAd: '', // 灰分
            cleanStd: '', // 硫
            cleanVdaf: '', // 挥发分
            crc: '', // 特征
            procG: '', // 粘结
            procY: '', // 胶质
            procX: '', // X指标
            recovery: '', // 回收率 原煤时
            beginDate: (new Date(new Date().getTime())).Format('yyyy-MM-dd'), // 合同开始日期
            endDate: '', // 合同结束日期
            signDate: (new Date(new Date().getTime())).Format('yyyy-MM-dd'), // 合同签订日期
            customerId: '',
            amountLeft: 0,
            customerName: '',
            carriage: '', //  运费
            carriageContract: '', // 运输编号
            price: '',// 煤价
            state: '',//合同状态
            attachmentList: []
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    width: 100,
                    label: '合同签订日期',
                    prop: 'signDate',
                    isShow: true,
                    fixed: 'left',
                    align: "center"
                },
                {
                    label: '品名',
                    width: 100,
                    prop: 'productName',
                    isShow: true,
                    fixed: 'left',
                    align: "center"
                },
                {
                    label: '合同名称',
                    width: 100,
                    prop: 'name',
                    isShow: true,
                    slot: 'name',
                    fixed: 'left'
                },
                {
                    label: '买方',
                    prop: 'customerName',
                    isShow: true,
                    width: 170,
                    align: "center"
                },
                {
                    label: '卖方',
                    prop: 'secondParty',
                    isShow: true,
                    // width: 150,
                    align: "center"
                },
                {
                    label: '发货单位',
                    prop: 'senderName',
                    isShow: true,
                    width: 150,
                    align: "center"
                },
                {
                    label: '收货单位',
                    prop: 'receiverName',
                    isShow: true,
                    width: 150,
                    align: "center"
                },
                // {
                //     label: '供应组',
                //     prop: 'groupName',
                //     width: 100,
                //     isShow: true,
                //     align: "center"
                // },

                // {
                //     label: '拉运地点',
                //     prop: 'pullLocation',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    label: '单价',
                    prop: 'price',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '价格类型',
                //     prop: 'payWay',
                //     slot: 'payWay',
                //     isShow: true,
                // },
                // {
                //     label: '是否含税',
                //     prop: 'hasFax',
                //     isShow: true,
                //     slot: 'hasFax',
                //     align: "center"
                // },
                // {
                //     label: '开票运费',
                //     prop: 'carriageBill',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     label: '类型',
                //     prop: 'typeName',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    label: '订单运费',
                    prop: 'carriage',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '合同余量',
                //     prop: 'amountLeft',
                //     isShow: true,
                //     align: "center"
                // },
                {
                    label: '合同状态',
                    width: 100,
                    prop: 'state',
                    isShow: true,
                    slot: 'state',
                    align: "center"
                },
                {
                    label: '当前流程',
                    width: 150,
                    prop: 'curFlowName',
                    slot: 'curFlowName',
                    isShow: true,
                },
                {
                    label: '吨数',
                    prop: 'amount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '总价',
                    prop: 'totalMoney',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '全水',
                    prop: 'cleanMt',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '灰分',
                    prop: 'cleanAd',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '硫',
                    prop: 'cleanStd',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '挥发分',
                    prop: 'cleanVdaf',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '特征',
                    prop: 'crc',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '粘结',
                    prop: 'procG',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '胶质',
                    prop: 'procY',
                    isShow: true,
                    align: "center"
                },
                {
                    label: 'X指标',
                    prop: 'procX',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '回收率',
                    prop: 'recovery',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '热强度CSR',
                    prop: 'qualCsr',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '反射率R0',
                    prop: 'macR0',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '方差S',
                    prop: 'macS',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    isShow: true,
                    align: "center"
                },
                {
                    width: 120,
                    label: '合同开始日期',
                    prop: 'beginDate',
                    isShow: true,
                    align: "center"
                },
                {
                    width: 120,
                    label: '合同结束日期',
                    prop: 'endDate',
                    isShow: true,
                    align: "center"
                },

                {
                    label: '合同附件列表',
                    prop: 'attachment',
                    slot: 'attachment',
                    isShow: true
                },
                {
                    label: '是否开放接口',
                    prop: 'isPublic',
                    slot: 'isPublic',
                    isShow: true
                },
                {
                    label: '合同编码',
                    prop: 'code',
                    slot: 'code',
                    width: 80,
                    isShow: true,
                    align: "center"
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true,
                    width: 30
                }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }

    saveContractSellNew(data) {
        return fetch({
            url: `${name}/saveContractSellNew`,
            method: 'post',
            data
        })
    }


    // async page(params = {}) {
    //     const res = await fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: params
    //     })
    //     res.data.records.forEach(item => {
    //         item.isPublicBoolean = item.isPublic === 'Y'
    //         item.typeName = typeName[item.coalCategory]
    //     })
    //     return res
    // }


    async page(params = {}) {
        if (params.filter_INS_state == 'NEW,PASS_FINANCE') {
            params.filter_INS_state = ''
            const res = await fetch({
                url: `${name}/findWaitReviewPage`,
                method: 'get',
                params: params
            })
            res.data.records.forEach(item => {
                item.isPublicBoolean = item.isPublic === 'Y'
                item.typeName = typeName[item.coalCategory]
            })
            return res
        } else {
            const res = await fetch({
                url: `${name}/page`,
                method: 'get',
                params: params
            })
            res.data.records.forEach(item => {
                item.isPublicBoolean = item.isPublic === 'Y'
                item.typeName = typeName[item.coalCategory]
            })
            return res
        }
    }

    getUploadList(id) {
        return fetch({
            url: `${name}/get`,
            method: 'get',
            params: { id }
        })
    }



    // 获取当前合同的流程
    findContractSellFlow(id) {
        return fetch({
            url: `${name}/findContractSellFlow`,
            method: 'get',
            params: { id }
        })
    }

    //审核
    getfinancePass(data) {
        return fetch({
            url: `${name}/pass`,
            method: 'post',
            data
        })
    }
    //拒绝
    getApprovednoPas(data) {
        return fetch({
            url: `${name}/reject `,
            method: 'post',
            data
        })
    }



    //作废合同
    Cancel(data) {
        return fetch({
            url: `${name}/cancel`,
            method: 'post',
            data
        })
    }

    //获取销售合同发货方
    getContractParty(params = {}) {
        return fetch({
            url: '/cwe/a/contractParty/findSellByKeyword',
            method: 'get',
            params: params
        })
    }
    //调价保存
    saveAdjustPrice(data) {
        return fetch({
            url: '/cwe/a/contractSellPrice/saveContractSellPrice',
            method: 'post',
            data
        })
    }
    // 调价审核通过
    passPriceState(data) {
        return fetch({
            url: `${name}/passPriceState`,
            method: 'post',
            data
        })
    }
    // 调价审核拒绝
    rejectPriceState(data) {
        return fetch({
            url: `${name}/rejectPriceState`,
            method: 'post',
            data
        })
    }

    //调量保存
    saveAddAmount(data) {
        return fetch({
            url: '/cwe/a/contractSellAmount/addAmount',
            method: 'post',
            data
        })
    }


    // 运费调价审核通过
    passCarriageState(data) {
        return fetch({
            url: `${name}/passCarriageState`,
            method: 'post',
            data
        })
    }
    // 运费调价审核拒绝
    rejectCarriageState(data) {
        return fetch({
            url: `${name}/rejectCarriageState`,
            method: 'post',
            data
        })
    }
    //运费调价保存
    saveContractSellCarriage(data) {
        return fetch({
            url: '/cwe/a/contractSellCarriage/saveContractSellCarriage',
            method: 'post',
            data
        })
    }



    //获取审核流程
    getlclist(params = {}) {
        return fetch({
            url: `/cwe/a/flowDesign/findListByType`,
            method: 'get',
            params,
        })
    }


    // getfinancePass(data) {
    //     return fetch({
    //         url: `${name}/financePass `,
    //         method: 'post',
    //         data
    //     })
    // }

    //获取待审核的数据条数
    getcontractSellState() {
        return fetch({
            url: `${name}/countByState`,
            method: 'get',
        })
    }
    getProductListfn(params = {}) {
        return fetch({
            url: `${name}/getProductList`,
            method: 'get',
            params: params
        })
    }

    //作废合同
    Cancel(data) {
        return fetch({
            url: `${name}/cancel`,
            method: 'post',
            data
        })
    }
    // 获取采销售同名称
    getNameCode(customerName) {
        return fetch({
            url: `${name}/getNameCode`,
            method: 'get',
            params: { customerName }
        })
    }

    //获取销售合同卖方
    getSellContractParty(params = {}) {
        return fetch({
            url: '/cwe/a/contractParty/findSellByKeyword',
            method: 'get',
            params: params
        })
    }

    //获取销售合同收货单位列表
    getfindBuyAndSupplier(params = {}) {
        return fetch({
            url: '/cwe/a/contractParty/findSellAndCustomerByKeyword',
            method: 'get',
            params: params
        })
    }
}

export default new _()
