import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/customer`
class CustomerModel extends Model {
    constructor() {
        const dataJson = {
            code: '', // 供应商编码
            name: '', // 供应商名称
            province: '', // 省份
            city: '', // 市县
            area: '', // 区
            location: '', //  省市区拼接
            linkman: '', // 联系人
            phone: '', // 联系电话,
            address: ''
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '客户名称',
                    prop: 'name',
                    isShow: true,
                    fixed: 'left',
                    align: "center"
                },
                {
                    label: '客户编码',
                    prop: 'code',
                    isShow: true,
                    fixed: 'left',
                    align: "center"
                },

                {
                    label: '负责人',
                    prop: 'linkman',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '联系电话',
                    prop: 'phone',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '省市区',
                    prop: 'location',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '详细地址',
                    prop: 'address',
                    isShow: true,
                    align: "center"
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    isShow: true,
                    slot: 'opt'
                }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }


}

export default new CustomerModel()
