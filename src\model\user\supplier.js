import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/supplier`
class SupplierModel extends Model {
    constructor() {
        const dataJson = {
            code: '', // 供应商编码
            name: '', // 供应商名称
            province: '', // 省份
            address: '',
            city: '', // 市县
            area: '', // 区
            location: '', //  省市区拼接
            linkman: '', // 联系人
            phone: '', // 联系电话,
            channel: ''// 渠道
        }
        const form = {}
        const tableOption = {
            columns: [
                {
                    label: '供应商名称',
                    prop: 'name',
                    isShow: true,
                    fixed: 'left',
                    minWidth: 140,
                    align: "center"
                },
                {
                    label: '供应商简称',
                    prop: 'aliasName',
                    isShow: true,
                    minWidth: 140,
                    align: "center"
                },
                {
                    label: '供应商编码',
                    prop: 'code',
                    isShow: true,
                    fixed: 'left',
                    align: "center"
                },
                {
                    label: '渠道',
                    prop: 'channel',
                    slot: 'channel',
                    isShow: true
                },
                {
                    label: '负责人',
                    prop: 'linkman',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '联系电话',
                    prop: 'phone',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '开户行',
                    prop: 'bank',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '账号或卡号',
                    prop: 'cardNo',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '省市区',
                    prop: 'location',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '详细地址',
                    prop: 'address',
                    isShow: true,
                    align: "center"
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    isShow: true,
                    slot: 'opt'
                }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }

    page(searchForm) {
        return fetch({
            url: `${name}/page`,
            method: 'get',
            params: searchForm
        })
    }
}

export default new SupplierModel()
