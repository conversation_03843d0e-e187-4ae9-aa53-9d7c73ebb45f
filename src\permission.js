import router from './router'
import store from './store'
import {getToken} from '@/utils/auth'
import Cache from '@/utils/cache'

function hasPermission(perms, permissions) {
  // if (perms.indexOf('*') >= 0) return true
  if (!permissions) return true
  // return perms.some(perm => permissions.indexOf(perm) >= 0)
  return perms[permissions]
}

const whiteList = ['/login', '/auth-redirect']

router.beforeEach((to, from, next) => {
  if (getToken()) {
    const user = Cache.get('user') || {}
    store.dispatch('setUser', user)
    if (to.path === '/login') {
      next({path: '/'})
    } else {
      if (store.getters.perms === null) {
        // 判断当前用户是否已拉取完user_info信息
        // store.dispatch('setPerms', ['*'])
        // const perms = []
        // store.dispatch('GenerateRoutes', {perms}).then(() => { // 根据perms权限生成可访问的路由表
        //     router.addRoutes(store.getters.addRouters) // 动态添加可访问路由表
        //     next({...to, replace: true}) // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
        // })
        store.dispatch('GetUserInfo').then(data => { // 拉取user_info
          // const perms = res.resources
          if (new Date() > new Date(data.user.costDate)) {
            store.dispatch('LogOut').then(() => {
              next({path: '/login', replace: true})
            })
          } else {
            store.dispatch('GenerateRoutes').then(() => { // 根据perms权限生成可访问的路由表
              store.dispatch('Sync').then(() => { // 同步数据
                router.addRoutes(store.getters.addRouters) // 动态添加可访问路由表
                next({...to, replace: true}) // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
              }).catch((e) => {
                router.addRoutes(store.getters.addRouters) // 动态添加可访问路由表
                next({...to, replace: true}) // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
              })
            })
          }
        }).catch(() => {
          store.dispatch('FedLogOut').then(() => {
            next({path: '/'})
          })
        })
      } else {
        if (hasPermission(store.getters.perms, to.meta.perms)) {
          next()
        } else {
          next({path: '/401', replace: true, query: {noGoBack: true}})
        }
      }
    }
  } else {
    /* has no token */
    if (whiteList.indexOf(to.path) !== -1) { // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.path}`) // 否则全部重定向到登录页
    }
  }
})

// router.afterEach(() => {
//     NProgress.done() // finish progress bar
// })
