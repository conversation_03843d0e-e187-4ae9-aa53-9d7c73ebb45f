/* eslint-disable no-console */

import {register} from 'register-service-worker'
import {Message} from 'element-ui'

if (process.env.NODE_ENV === 'production') {
    register(`/sw.js`, {
        ready() {
            console.log(
                'App is being served from cache by a service worker.\n'
            )
            // setInterval(() => {
            //     registration.update()
            // }, 5 * 1000)
        },
        cached() {
            console.log('Content has been cached for offline use.')
        },
        updated() {
            console.log('New content is available; please refresh.')
            Message({
                message: '新版本更新成功,3s后自动刷新页面',
                type: 'success',
                duration: 5 * 1000
            })
            setTimeout(() => {
                location.reload()
            }, 3000)
        },
        offline() {
            console.log('No internet connection found. App is running in offline mode.')
            Message({
                message: '您已经断网,进入离线模式',
                type: 'success',
                duration: 5 * 1000
            })
        },
        error(error) {
            console.error('Error during service worker registration:', error)
        }
    })
}
