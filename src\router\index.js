import Vue from "vue";
import Router from "vue-router";

/* Layout */
import Layout from "@/views/layout/Layout";
import LayoutH from "@/views/layoutH/Layout";

// const LayoutH = Layout
Vue.use(Router);
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};
const iconPath = (path, directory = "sidebar-item") =>
  require(`@/assets/${directory}/${path}.png`);
export const constantRouterMap = [
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/errorPage/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/errorPage/401"),
    hidden: true,
  },
  {
    path: "/formEditor",
    component: () => import("@/views/bpm/form/formEditor"),
    hidden: true,
  },
  {
    path: "/",
    component: Layout,
    redirect: "dashboard",
    meta: {
      title: "首页",
      perms: true,
      icon: require("@/assets/sidebar/home.png"),
      iconSelected: require("@/assets/sidebar/home_selected.png"),
    },
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index"),
        name: "Dashboard",
        meta: { title: "dashboard", noCache: true },
      },
    ],
  },
];

export default new Router({
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap,
});
export const asyncRouterMap = [
  {
    path: "/base",
    component: Layout,
    redirect: "noredirect",
    alwaysShow: true,
    name: "base",
    meta: {
      perms: "base:view",
      title: "合同",
      icon: require("@/assets/sidebar/user.png"),
      iconSelected: require("@/assets/sidebar/user_selected.png"),
    },
    children: [
      // {
      //     path: 'console',
      //     component: () => import('@/views/base/console'),
      //     name: 'baseConsole',
      //     meta: {
      //         perms: 'baseConsole:view',
      //         icon: iconPath('console'),
      //         iconSelected: iconPath('console_selected'),
      //         title: '工作台',
      //         noCache: true,
      //     },
      // },
      {
        path: "product",
        component: () => import("@/views/base/productList"),
        name: "product",
        meta: {
          perms: "product:view",
          icon: iconPath("name"),
          iconSelected: iconPath("name_selected"),
          title: "品名",
          noCache: true,
        },
      },

      {
        path: "buy",
        component: () => import("@/views/base/buyLayout"),
        name: "buy",
        redirect: "noredirect",
        meta: {
          perms: "buy:view",
          icon: iconPath("customer"),
          iconSelected: iconPath("customer_selected"),
          title: "采购",
          noCache: true,
        },
        children: [
          {
            path: "supplier",
            component: () => import("@/views/base/supplier"),
            name: "supplier",
            meta: {
              perms: "supplier:view",
              icon: iconPath("supplier"),
              iconSelected: iconPath("supplier_selected"),
              title: "供应商",
              noCache: true,
            },
          },
          {
            path: "contractBuy",
            component: () => import("@/views/base/contractBuy"),
            name: "contractBuy",
            meta: {
              perms: "contractBuy:view",
              icon: iconPath("contract"),
              iconSelected: iconPath("contract_selected"),
              title: "采购合同",
              noCache: true,
            },
          },
        ],
      },
      {
        path: "sell",
        component: () => import("@/views/base/sellLayout"),
        redirect: "noredirect",
        name: "sell",
        meta: {
          perms: "sell:view",
          icon: iconPath("contract"),
          iconSelected: iconPath("contract_selected"),
          title: "销售",
          noCache: true,
        },
        children: [
          {
            path: "customer",
            component: () => import("@/views/base/customer"),
            name: "customer",
            meta: {
              perms: "customer:view",
              title: "客户",
              icon: iconPath("customer"),
              iconSelected: iconPath("customer_selected"),
              noCache: true,
            },
          },
          {
            path: "contractSell",
            component: () => import("@/views/base/contractSell"),
            name: "contractSell",
            meta: {
              perms: "contractSell:view",
              icon: iconPath("contract"),
              iconSelected: iconPath("contract_selected"),
              title: "销售合同",
              noCache: true,
            },
          },
        ],
      },
    ],
  },
  {
    path: "/invoicing",
    component: Layout,
    redirect: "noredirect",
    alwaysShow: true,
    name: "invoicing",
    meta: {
      perms: "invoicing:view",
      icon: require("@/assets/sidebar/invoicing.png"),
      iconSelected: require("@/assets/sidebar/invoicing_selected.png"),
      title: "进销",
    },
    children: [
      // {
      //     path: 'console',
      //     component: () => import('@/views/invoicing/console'),
      //     name: 'invoicingConsole',
      //     meta: {
      //         perms: 'invoicingConsole:view',
      //         icon: iconPath('console'),
      //         iconSelected: iconPath('console_selected'),
      //         title: '工作台',
      //         noCache: true,
      //     },
      // },
      {
        path: "purchase",
        component: () => import("@/views/invoicing/purchase"),
        name: "purchase",
        redirect: "noredirect",
        meta: {
          perms: "purchase:view",
          icon: iconPath("payment"),
          iconSelected: iconPath("payment_selected"),
          title: "购进",
          noCache: true,
        },
        children: [
          {
            path: "purchasedetails",
            component: () => import("@/views/invoicing/purchasedetails"),
            name: "purchasedetails",
            meta: {
              perms: "purchasedetails:view",
              icon: iconPath("supplier"),
              iconSelected: iconPath("supplier_selected"),
              title: "明细日报",
              noCache: true,
            },
          },
          {
            path: "purchaseSummarydetails",
            component: () => import("@/views/invoicing/purchaseSummarydetails"),
            name: "purchaseSummarydetails",
            meta: {
              perms: "purchaseSummarydetails:view",
              icon: iconPath("supplier"),
              iconSelected: iconPath("supplier_selected"),
              title: "汇总日报",
              noCache: true,
            },
          },
          {
            path: "purchaseDetailHistory",
            component: () => import("@/views/invoicing/purchaseDetailHistory"),
            name: "purchaseDetailHistory",
            meta: {
              perms: "purchaseDetailHistory:view",
              icon: iconPath("supplier"),
              iconSelected: iconPath("supplier_selected"),
              title: "历史明细",
              noCache: true,
            },
          },
          // {
          //     path: 'contractbalance',
          //     component: () => import('@/views/invoicing/contractbalance'),
          //     name: 'contractbalance',
          //     meta: {
          //         perms: 'contractbalance:view',
          //         icon: iconPath('supplier'),
          //         iconSelected: iconPath('supplier_selected'),
          //         title: '合同余量',
          //         noCache: true,
          //     },
          // },
        ],
      },

      {
        path: "salev",
        component: () => import("@/views/invoicing/salev"),
        name: "salev",
        redirect: "noredirect",
        meta: {
          perms: "salev:view",
          icon: iconPath("payment"),
          iconSelected: iconPath("payment_selected"),
          title: "销售",
          noCache: true,
        },
        children: [
          {
            path: "salesdetails",
            component: () => import("@/views/invoicing/salesdetails"),
            name: "salesdetails",
            meta: {
              perms: "salesdetails:view",
              icon: iconPath("supplier"),
              iconSelected: iconPath("supplier_selected"),
              title: "明细日报",
              noCache: true,
            },
          },
          {
            path: "salesSummarydetails",
            component: () => import("@/views/invoicing/salesSummarydetails"),
            name: "salesSummarydetails",
            meta: {
              perms: "salesSummarydetails:view",
              icon: iconPath("supplier"),
              iconSelected: iconPath("supplier_selected"),
              title: "汇总日报",
              noCache: true,
            },
          },
          {
            path: "salesdetailsDetailHistory",
            component: () =>
              import("@/views/invoicing/salesdetailsDetailHistory"),
            name: "salesdetailsDetailHistory",
            meta: {
              perms: "salesdetailsDetailHistory:view",
              icon: iconPath("supplier"),
              iconSelected: iconPath("supplier_selected"),
              title: "历史明细",
              noCache: true,
            },
          },
          // {
          //     path: 'salescontractbalance',
          //     component: () => import('@/views/invoicing/salescontractbalance'),
          //     name: 'salescontractbalance',
          //     meta: {
          //         perms: 'salescontractbalance:view',
          //         icon: iconPath('supplier'),
          //         iconSelected: iconPath('supplier_selected'),
          //         title: '合同余量',
          //         noCache: true,
          //     },
          // },
        ],
      },
      {
        path: "weightHouselogtable",
        component: () => import("@/views/invoicing/weightHouselogtable"),
        name: "weightHouselogtable",
        meta: {
          perms: "weightHouselogtable:view",
          icon: iconPath("weightHouseIn"),
          iconSelected: iconPath("weightHouseIn_selected"),
          title: "磅房日志",
          noCache: true,
        },
      },
      // log table

      // {
      //     path: 'weightHouseIn',
      //     component: () => import('@/views/invoicing/weightHouseIn'),
      //     name: 'weightHouseIn',
      //     meta: {
      //         perms: 'weightHouseIn:view',
      //         icon: iconPath('weightHouseIn'),
      //         iconSelected: iconPath('weightHouseIn_selected'),
      //         title: '磅房购进日报表',
      //         noCache: true,
      //     },
      // },
      // {
      //     path: 'weightHouseOut',
      //     component: () => import('@/views/invoicing/weightHouseOut'),
      //     name: 'weightHouseOut',
      //     meta: {
      //         perms: 'weightHouseOut:view',
      //         icon: iconPath('weightHouseOut'),
      //         iconSelected: iconPath('weightHouseOut_selected'),
      //         title: '磅房销售日报表',
      //         noCache: true,
      //     },
      // },
      // {
      //     path: 'buyIn',
      //     component: () => import('@/views/invoicing/buyIn'),
      //     name: 'buyIn',
      //     meta: {
      //         perms: 'buyIn:view',
      //         icon: iconPath('buyIn'),
      //         iconSelected: iconPath('buyIn_selected'),
      //         title: '台账购进日报表',
      //         noCache: true,
      //     },
      // },
      // {
      //     path: 'sellOut',
      //     component: () => import('@/views/invoicing/sellOut'),
      //     name: 'sellOut',
      //     meta: {
      //         perms: 'sellOut:view',
      //         icon: iconPath('sellOut'),
      //         iconSelected: iconPath('sellOut_selected'),
      //         title: '台账销售日报表',
      //         noCache: true,
      //     },
      // },
      // {
      //     path: 'buyInMonth',
      //     component: () => import('@/views/invoicing/buyInMonth'),
      //     name: 'buyInMonth',
      //     meta: {
      //         perms: 'buyInMonth:view',
      //         icon: iconPath('buyIn'),
      //         iconSelected: iconPath('buyIn_selected'),
      //         title: '购进月报表',
      //         noCache: true,
      //     },
      // },
      // {
      //     path: 'sellOutMonth',
      //     component: () => import('@/views/invoicing/sellOutMonth'),
      //     name: 'sellOutMonth',
      //     meta: {
      //         perms: 'sellOutMonth:view',
      //         icon: iconPath('sellOut'),
      //         iconSelected: iconPath('sellOut_selected'),
      //         title: '销售月报表',
      //         noCache: true,
      //     },
      // },
    ],
  },
  {
    path: "/production",
    component: Layout,
    redirect: "noredirect",
    alwaysShow: true,
    name: "production",
    meta: {
      perms: "production:view",
      title: "生产",
      icon: require("@/assets/sidebar/stock.png"),
      iconSelected: require("@/assets/sidebar/stock_selected.png"),
    },
    children: [
      // {
      //     path: 'console',
      //     component: () => import('@/views/production/console'),
      //     name: 'coalProductionConsole',
      //     meta: {
      //         perms: 'productionConsole:view',
      //         icon: iconPath('console'),
      //         iconSelected: iconPath('console_selected'),
      //         title: '工作台',
      //         noCache: true,
      //     },
      // },
      {
        path: "coalProductionList",
        component: () => import("@/views/production/coalProductionList"),
        name: "coalProductionList",
        meta: {
          perms: "coalProductionList:view",
          icon: iconPath("coalProductionList"),
          iconSelected: iconPath("coalProductionList_selected"),
          title: "配煤生产",
          noCache: true,
        },
      },
      // {//Coalwashingdaily
      //     path: 'Coalwashingdaily',
      //     component: () => import('@/views/production/Coalwashingdaily'),
      //     name: 'Coalwashingdaily',
      //     meta: {
      //         perms: 'Coalwashingdaily:view',
      //         icon: iconPath('coalProductionList'),
      //         iconSelected: iconPath('coalProductionList_selected'),
      //         title: '洗煤生产日报(单)',
      //         noCache: true,
      //     },
      // },
      {
        //washMixCoal
        path: "washMixCoal",
        component: () => import("@/views/production/washMixCoal"),
        name: "washMixCoal",
        meta: {
          perms: "washMixCoal:view",
          icon: iconPath("coalProductionList"),
          iconSelected: iconPath("coalProductionList_selected"),
          title: "洗煤生产",
          noCache: true,
        },
      },

      // {
      //     path: 'coalProductionListV',
      //     component: () => import('@/views/production/coalProductionListV'),
      //     name: 'coalProductionListV',
      //     meta: {
      //         perms: 'coalProductionListV:view',
      //         icon: iconPath('coalProductionList'),
      //         iconSelected: iconPath('coalProductionList_selected'),
      //         title: '配煤生产日报y',
      //         noCache: true,
      //     },
      // },

      // {
      //     path: 'productionMonth',
      //     component: () => import('@/views/production/productionMonth'),
      //     name: 'productionMonth',
      //     meta: {
      //         perms: 'coalProductionList:view',
      //         icon: iconPath('coalProductionList'),
      //         iconSelected: iconPath('coalProductionList_selected'),
      //         title: '配煤生产月报',
      //         noCache: true,
      //     },
      // },
    ],
  },
  {
    path: "/stock",
    component: Layout,
    redirect: "noredirect",
    alwaysShow: true,
    name: "stock",
    meta: {
      perms: "stock:view",
      title: "库存",
      icon: require("@/assets/sidebar/product.png"),
      iconSelected: require("@/assets/sidebar/product_selected.png"),
    },
    children: [
      // {
      //     path: 'console',
      //     component: () => import('@/views/stock/console'),
      //     name: 'stockConsole',
      //     meta: {
      //         perms: 'stockConsole:view',
      //         icon: iconPath('console'),
      //         iconSelected: iconPath('console_selected'),
      //         title: '工作台',
      //         noCache: true,
      //     },
      // },
      {
        path: "coalStock",
        component: () => import("@/views/stock/coalStock"),
        name: "coalStock",
        meta: {
          perms: "coalStock:view",
          icon: iconPath("finishedCoalStock"),
          iconSelected: iconPath("finishedCoalStock_selected"),
          title: "实时库存",
          noCache: true,
        },
      },
      {
        path: "rawCoalStock",
        component: () => import("@/views/stock/rawCoalStock"),
        name: "rawCoalStock",
        meta: {
          perms: "rawCoalStock:view",
          icon: iconPath("rawCoalStock"),
          iconSelected: iconPath("rawCoalStock_selected"),
          title: "原煤",
          noCache: true,
        },
      },
      {
        path: "semiFinishedCoalStock",
        component: () => import("@/views/stock/semiFinishedCoalStock"),
        name: "semiFinishedCoalStock",
        meta: {
          perms: "semiFinishedCoalStock:view",
          icon: iconPath("semiFinishedCoalStock"),
          iconSelected: iconPath("semiFinishedCoalStock_selected"),
          title: "半成品",
          noCache: true,
        },
      },
      {
        path: "finishedCoalStock",
        component: () => import("@/views/stock/finishedCoalStock"),
        name: "finishedCoalStock",
        meta: {
          perms: "finishedCoalStock:view",
          icon: iconPath("productList"),
          iconSelected: iconPath("productList_selected"),
          title: "成品",
          noCache: true,
        },
      },
      {
        path: "chinaCoalStock",
        component: () => import("@/views/stock/chinaCoalStock"),
        name: "chinaCoalStock",
        meta: {
          perms: "chinaCoalStock:view",
          icon: iconPath("chinaCoalStock"),
          iconSelected: iconPath("chinaCoalStock_selected"),
          title: "副产品",
          noCache: true,
        },
      },
    ],
  },
  {
    path: "/coalQuality",
    component: Layout,
    redirect: "noredirect",
    alwaysShow: true,
    name: "coalQuality",
    meta: {
      perms: "coalQuality:view",
      title: "煤质",
      icon: require("@/assets/sidebar/coalQuality.png"),
      iconSelected: require("@/assets/sidebar/coalQuality_selected.png"),
    },
    children: [
      {
        path: "qualBuyIn",
        component: () => import("@/views/quality/qualBuyIn"),
        name: "qualBuyIn",
        meta: {
          perms: "qualBuyIn:view",
          icon: iconPath("qualBuyIn"),
          iconSelected: iconPath("qualBuyIn_selected"),
          title: "精煤购进",
          noCache: true,
        },
      },
      {
        path: "rawcoalqualBuyIn",
        component: () => import("@/views/quality/rawcoalqualBuyIn"),
        name: "rawcoalqualBuyIn",
        meta: {
          perms: "rawcoalqualBuyIn:view",
          icon: iconPath("qualBuyIn"),
          iconSelected: iconPath("qualBuyIn_selected"),
          title: "原煤购进",
          noCache: true,
        },
      },
      {
        path: "qualSellOut",
        component: () => import("@/views/quality/qualSellOut"),
        name: "qualSellOut",
        meta: {
          perms: "qualSellOut:view",
          icon: iconPath("qualSellOut"),
          iconSelected: iconPath("qualSellOut_selected"),
          title: "站台化验",
          noCache: true,
        },
      },
      {
        path: "coalblendingproduce",
        component: () => import("@/views/quality/coalblendingproduce"),
        name: "coalblendingproduce",
        meta: {
          perms: "coalblendingproduce:view",
          icon: iconPath("qualPile"),
          iconSelected: iconPath("qualPile_selected"),
          title: "配煤化验",
          noCache: true,
        },
      },
      {
        path: "coalwashing",
        component: () => import("@/views/quality/coalwashing"),
        name: "coalwashing",
        meta: {
          perms: "coalwashing:view",
          icon: iconPath("qualBelt"),
          iconSelected: iconPath("qualBelt_selected"),
          title: "洗煤化验",
          noCache: true,
        },
      },
      // {
      //   path: 'coalSample',
      //   component: () => import('@/views/quality/coalSample'),
      //   name: 'coalSample',
      //   meta: {
      //     perms: 'coalSample:view',
      //     icon: iconPath('coalSample'),
      //     iconSelected: iconPath('coalSample_selected'),
      //     title: '小样精煤',
      //     noCache: true
      //   }
      // },
      {
        path: "coalRawSample",
        component: () => import("@/views/quality/coalRawSample"),
        name: "coalRawSample",
        meta: {
          perms: "coalRawSample:view",
          icon: iconPath("coalSample"),
          iconSelected: iconPath("coalSample_selected"),
          title: "小样化验",
          noCache: true,
        },
      },
      // {
      //   path: 'AttachedProduct',
      //   component: () => import('@/views/quality/AttachedProduct'),
      //   name: 'AttachedProduct',
      //   meta: {
      //     perms: 'AttachedProduct:view',
      //     icon: iconPath('customerFeedback'),
      //     iconSelected: iconPath('customerFeedback_selected'),
      //     title: '副产品',
      //     noCache: true
      //   }
      // }
    ],
  },
  {
    path: "/finance",
    component: Layout,
    redirect: "noredirect",
    alwaysShow: true,
    name: "finance",
    meta: {
      perms: "finance:view",
      title: "财务",
      icon: require("@/assets/sidebar/Finance.png"),
      iconSelected: require("@/assets/sidebar/Finance_selected.png"),
    },
    children: [
      // {
      //     path: 'console',
      //     component: () => import('@/views/finance/console'),
      //     name: 'financeConsole',
      //     meta: {
      //         perms: 'financeConsole:view',
      //         icon: iconPath('console'),
      //         iconSelected: iconPath('console_selected'),
      //         title: '工作台',
      //         noCache: true,
      //     },
      // },
      {
        path: "payment",
        component: () => import("@/views/finance/payment"),
        name: "payment",
        redirect: "noredirect",
        meta: {
          perms: "payment:view",
          icon: iconPath("payment"),
          iconSelected: iconPath("payment_selected"),
          title: "采购",
          noCache: true,
        },
        children: [
          {
            path: "applyPay",
            component: () => import("@/views/finance/applyPay"),
            name: "applyPay",
            meta: {
              perms: "applyPay:view",
              icon: iconPath("supplier"),
              iconSelected: iconPath("supplier_selected"),
              title: "付款单",
              noCache: true,
            },
          },
          {
            path: "SupplierRefund",
            component: () => import("@/views/finance/SupplierRefund"),
            name: "SupplierRefund",
            meta: {
              perms: "SupplierRefund:view",
              icon: iconPath("supplier"),
              iconSelected: iconPath("supplier_selected"),
              title: "收款单",
              noCache: true,
            },
          },
          {
            path: "settlePay",
            component: () => import("@/views/finance/settlePay"),
            name: "settlePay",
            meta: {
              perms: "settlePay:view",
              title: "付款结算",
              icon: iconPath("customer"),
              iconSelected: iconPath("customer_selected"),
              noCache: true,
            },
          },
          {
            path: "freightSettlement",
            component: () => import("@/views/finance/freightSettlement"),
            name: "freightSettlement",
            meta: {
              perms: "freightSettlement:view",
              title: "采购运费结算",
              icon: iconPath("customer"),
              iconSelected: iconPath("customer_selected"),
              noCache: true,
            },
          },
          {
            path: "detailsdailyreport",
            component: () => import("@/views/finance/detailsdailyreport"),
            name: "detailsdailyreport",
            meta: {
              perms: "detailsdailyreport:view",
              title: "付款明细日报",
              icon: iconPath("customer"),
              iconSelected: iconPath("customer_selected"),
              noCache: true,
            },
          },
          {
            path: "sbSupplierDay",
            component: () => import("@/views/finance/sbSupplierDay"),
            name: "sbSupplierDay",
            meta: {
              perms: "sbSupplierDay:view",
              title: "卖方每日台账",
              icon: iconPath("customer"),
              iconSelected: iconPath("customer_selected"),
              noCache: true,
            },
          },

          {
            path: "procureFreightDetails",
            component: () => import("@/views/finance/procureFreightDetails"),
            name: "procureFreightDetails",
            meta: {
              perms: "procureFreightDetails:view",
              title: "采购运费明细",
              icon: iconPath("customer"),
              iconSelected: iconPath("customer_selected"),
              noCache: true,
            },
          },
        ],
      },
      {
        path: "settlement",
        component: () => import("@/views/finance/settlement"),
        redirect: "noredirect",
        name: "settlement",
        meta: {
          perms: "settlement:view",
          icon: iconPath("settlement"),
          iconSelected: iconPath("settlement_selected"),
          title: "销售",
          noCache: true,
        },
        children: [
          {
            path: "applyRecv",
            component: () => import("@/views/finance/applyRecv"),
            name: "applyRecv",
            meta: {
              perms: "applyRecv:view",
              icon: iconPath("contract"),
              iconSelected: iconPath("contract_selected"),
              title: "收款单",
              noCache: true,
            },
          },
          {
            path: "CustomerRefund",
            component: () => import("@/views/finance/CustomerRefund"),
            name: "CustomerRefund",
            meta: {
              perms: "CustomerRefund:view",
              icon: iconPath("supplier"),
              iconSelected: iconPath("supplier_selected"),
              title: "付款单",
              noCache: true,
            },
          },
          {
            path: "settleRecv",
            component: () => import("@/views/finance/settleRecv"),
            name: "settleRecv",
            meta: {
              perms: "settleRecv:view",
              title: "收款结算",
              icon: iconPath("customer"),
              iconSelected: iconPath("customer_selected"),
              noCache: true,
            },
          },
          {
            path: "settleRecvFreight",
            component: () => import("@/views/finance/settleRecvFreight"),
            name: "settleRecvFreight",
            meta: {
              perms: "settleRecvFreight:view",
              title: "销售运费结算",
              icon: iconPath("customer"),
              iconSelected: iconPath("customer_selected"),
              noCache: true,
            },
          },
          {
            path: "collectiondetailsreport",
            component: () => import("@/views/finance/collectiondetailsreport"),
            name: "collectiondetailsreport",
            meta: {
              perms: "collectiondetailsreport:view",
              title: "收款明细日报",
              icon: iconPath("customer"),
              iconSelected: iconPath("customer_selected"),
              noCache: true,
            },
          },
          {
            path: "sbCustomerDay",
            component: () => import("@/views/finance/sbCustomerDay"),
            name: "sbCustomerDay",
            meta: {
              perms: "sbCustomerDay:view",
              title: "买方每日台账",
              icon: iconPath("customer"),
              iconSelected: iconPath("customer_selected"),
              noCache: true,
            },
          },
          {
            path: "saleFreightDetails",
            component: () => import("@/views/finance/saleFreightDetails"),
            name: "saleFreightDetails",
            meta: {
              perms: "saleFreightDetails:view",
              title: "销售运费明细",
              icon: iconPath("customer"),
              iconSelected: iconPath("customer_selected"),
              noCache: true,
            },
          },
          {
            path: "saleFreightAnnex",
            component: () => import("@/views/finance/saleFreightAnnex"),
            name: "saleFreightAnnex",
            meta: {
              perms: "saleFreightAnnex:view",
              title: "销售明细附件",
              icon: iconPath("customer"),
              iconSelected: iconPath("customer_selected"),
              noCache: true,
            },
          },
        ],
      },

      // {
      //   path: "reportform",
      //   component: () => import("@/views/finance/reportform"),
      //   redirect: "noredirect",
      //   name: "reportform",
      //   meta: {
      //     perms: "reportform:view",
      //     icon: iconPath("reportform"),
      //     iconSelected: iconPath("reportform_selected"),
      //     title: "其他",
      //     noCache: true,
      //   },
      //   children: [
      //     // {
      //     //     path: 'FreightDetails',
      //     //     component: () => import('@/views/finance/FreightDetails'),
      //     //     name: 'FreightDetails',
      //     //     meta: {
      //     //         perms: 'FreightDetails:view',
      //     //         title: '运费明细',
      //     //         icon: iconPath('customer'),
      //     //         iconSelected: iconPath('customer_selected'),
      //     //         noCache: true,
      //     //     },
      //     // },
      //     {
      //       path: "weightHouseNotice",
      //       component: () => import("@/views/finance/weightHouseNotice"),
      //       name: "weightHouseNotice",
      //       meta: {
      //         perms: "weightHouseNotice:view",
      //         icon: iconPath("poundroom"),
      //         iconSelected: iconPath("poundroom_selected"),
      //         title: "通知磅房",
      //         noCache: true,
      //       },
      //     },
      //   ],
      // },
    ],
  },
  {
    path: "/material",
    component: Layout,
    redirect: "noredirect",
    alwaysShow: true,
    name: "material",
    meta: {
      perms: "material:view",
      title: "物资",
      icon: require("@/assets/sidebar/materialV.png"),
      iconSelected: require("@/assets/sidebar/material_selectedV.png"),
    },
    children: [
      {
        path: "inventory",
        component: () => import("@/views/material/inventory"),
        name: "inventory",
        meta: {
          perms: "inventory:view",
          icon: iconPath("outOrderv"),
          iconSelected: iconPath("outOrderv_selected"),
          title: "库存",
          noCache: true,
        },
      },
      {
        path: "inOrder",
        component: () => import("@/views/material/inOrder"),
        name: "inOrder",
        meta: {
          perms: "inOrder:view",
          icon: iconPath("‌inorderv"),
          iconSelected: iconPath("inorderv_selected"),
          title: "入库",
          noCache: true,
        },
      },
      {
        path: "InventoryDetails",
        component: () => import("@/views/material/InventoryDetails"),
        name: "InventoryDetails",
        meta: {
          perms: "InventoryDetails:view",
          icon: iconPath("MaterialClassification"),
          iconSelected: iconPath("MaterialClassification_selected"),
          title: "入库明细",
          noCache: true,
        },
      },
      {
        path: "outOrder",
        component: () => import("@/views/material/outOrder"),
        name: "outOrder",
        meta: {
          perms: "outOrder:view",
          icon: iconPath("outOrderv"),
          iconSelected: iconPath("outOrderv_selected"),
          title: "出库",
          noCache: true,
        },
      },
      {
        path: "OutboundDetails",
        component: () => import("@/views/material/OutboundDetails"),
        name: "OutboundDetails",
        meta: {
          perms: "OutboundDetails:view",
          icon: iconPath("MaterialClassification"),
          iconSelected: iconPath("MaterialClassification_selected"),
          title: "出库明细",
          noCache: true,
        },
      },
      {
        path: "Materialinformation",
        component: () => import("@/views/material/Materialinformation"),
        name: "Materialinformation",
        meta: {
          perms: "Materialinformation:view",
          icon: iconPath("Materialinformation"),
          iconSelected: iconPath("Materialinformation_selected"),
          title: "物资信息",
          noCache: true,
        },
      },
      // {
      //     path: 'itemStockDay',
      //     component: () => import('@/views/material/itemStockDay'),
      //     name: 'itemStockDay',
      //     meta: {
      //         perms: 'itemStockDay:view',
      //         icon: iconPath('outOrderv'),
      //         iconSelected: iconPath('outOrderv_selected'),
      //         title: '库存日报',
      //         noCache: true,
      //     },
      // },
      {
        path: "MaterialClassification",
        component: () => import("@/views/material/MaterialClassification"),
        name: "MaterialClassification",
        meta: {
          perms: "MaterialClassification:view",
          icon: iconPath("MaterialClassification"),
          iconSelected: iconPath("MaterialClassification_selected"),
          title: "物资分类",
          noCache: true,
        },
      },
    ],
  },
  {
    path: "/coalBlendingV",
    name: "coalBlendingV",
    component: LayoutH,
    redirect: "noredirect",
    meta: {
      title: "配煤",
      perms: "coalblending:view",
      icon: require("@/assets/sidebar/production.png"),
      iconSelected: require("@/assets/sidebar/production_selected.png"),
    },
    children: [
      {
        path: "stock",
        name: "Stock",
        component: () => import("@/views/coalBlendingV/Stock"),
        meta: {
          title: "煤源数据库",
        },
      },
      {
        path: "autoCoalConfig",
        name: "AutoCoalConfig",
        component: () =>
          import("@/views/coalBlendingV/AutoCoalConfig/index.vue"),
        meta: {
          title: "目标值配置",
        },
      },
      {
        path: "autoCoal",
        name: "AutoCoal",
        component: () => import("@/views/coalBlendingV/AutoCoal"),
        meta: {
          title: "智能配煤",
        },
      },
      {
        path: "planManage",
        name: "PlanManage",
        component: () => import("@/views/coalBlendingV/PlanManage"),
        meta: {
          title: "方案管理",
        },
      },
      {
        path: "priceTrend",
        name: "PriceTrend",
        component: () => import("@/views/coalBlendingV/PriceTrend"),
        meta: {
          title: "价格趋势",
        },
      },
    ],
  },
  {
    path: "/price",
    component: LayoutH,
    redirect: "noredirect",
    name: "price",
    meta: {
      title: "性 价 比",
      perms: "price:view",
      icon: require("@/assets/sidebar/xjb.png"),
      iconSelected: require("@/assets/sidebar/xjb_selected.png"),
    },
    children: [
      {
        path: "marketAuction",
        name: "MarketAuction",
        meta: {
          title: "市场竞拍",
          perms: "marketAuction:view",
        },
        component: () => import("@/views/price/MarketAuction"),
      },
      {
        path: "coalSourceAllPrice",
        name: "CoalSourceAllPrice",
        meta: {
          title: "性价比排序(综合)",
          perms: "coalSourceAllPrice:view",
        },
        component: () => import("@/views/price/CoalSourceAllPrice"),
      },
      {
        path: "coalSourceAllPriceStandard",
        name: "CoalSourceAllPriceStandard",
        meta: {
          title: "性价比排序(外部)",
          perms: "coalSourceAllPriceStandard:view",
        },
        component: () => import("@/views/price/CoalSourceAllPriceStandard"),
      },
      {
        path: "coalSourceAllPriceInnner",
        name: "CoalSourceAllPriceInnner",
        meta: {
          title: "性价比排序(内部)",
          perms: "coalSourceAllPriceInnner:view",
        },
        component: () => import("@/views/price/CoalSourceAllPriceInnner"),
      },
      {
        path: "costPerformance", // cp: cost performance
        name: "CostPerformance",
        meta: {
          title: "性价比配置",
          perms: "costPerformance:view",
        },
        component: {
          render(c) {
            return c("router-view");
          },
        },
        children: [
          {
            path: "coalCategory",
            name: "CPCoalCategory",
            component: () =>
              import("@/views/base/CostPerformance/CoalCategory"),
            meta: {
              title: "煤种分类",
              perms: "costPerformanceCoalCategory:view",
            },
          },
          {
            path: "params",
            name: "CPParams",
            component: () => import("@/views/base/CostPerformance/Params"),
            meta: {
              title: "参数配置(综合)",
              perms: "costPerformanceParams:view",
            },
          },
          {
            path: "ParamsInternal",
            name: "CPParamsInternal",
            component: () =>
              import("@/views/base/CostPerformance/ParamsInternal"),
            meta: {
              title: "参数配置(内部)",
              perms: "costPerformanceParamsInternal:view",
            },
          },
          {
            path: "ParamsStandard",
            name: "CPParamsStandard",
            component: () =>
              import("@/views/base/CostPerformance/ParamsStandard"),
            meta: {
              title: "参数配置(外部)",
              perms: "costPerformanceParamsStandard:view",
            },
          },
        ],
      },
    ],
  },
  {
    path: "/messagereminder",
    component: Layout,
    redirect: "noredirect",
    alwaysShow: true,
    name: "messagereminder",
    meta: {
      perms: "messagereminder:view",
      title: "消息",
      icon: require("@/assets/sidebar/dd.png"),
      iconSelected: require("@/assets/sidebar/dd.png"),
    },
    children: [
      {
        path: "message",
        component: () => import("@/views/messagereminder/message"),
        name: "message",
        meta: {
          perms: "message:view",
          icon: iconPath("console"),
          iconSelected: iconPath("console_selected"),
          title: "消息提醒",
          noCache: true,
        },
      },
    ],
  },
  {
    path: "/sys",
    component: Layout,
    redirect: "noredirect",
    alwaysShow: true,
    name: "sys",
    meta: {
      title: "系统",
      icon: require("@/assets/sidebar/sys.png"),
      iconSelected: require("@/assets/sidebar/sys_selected.png"),
      perms: "sys:view",
    },
    children: [
      {
        path: "userList",
        component: () => import("@/views/sys/userList"),
        name: "userList",
        meta: {
          perms: "userList:view",
          title: "用户列表",
          noCache: true,
          icon: iconPath("userList"),
          iconSelected: iconPath("userList_selected"),
        },
      },
      {
        path: "factory",
        component: () => import("@/views/sys/factory/index.vue"),
        name: "factory",
        meta: {
          perms: "userList:view",
          title: "工厂管理",
          noCache: true,
          // affix: true,
          icon: iconPath("userList"),
          iconSelected: iconPath("userList_selected"),
        },
      },
      {
        path: "roleList",
        component: () => import("@/views/sys/roleList"),
        name: "roleList",
        meta: {
          perms: "roleList:view",
          title: "角色管理",
          noCache: true,
          icon: iconPath("roleList"),
          iconSelected: iconPath("roleList_selected"),
        },
      },
      {
        path: "depts",
        component: () => import("@/views/sys/depts/index.vue"),
        name: "depts",
        meta: {
          perms: "roleList:view",
          title: "部门管理",
          noCache: true,
          icon: iconPath("roleList"),
          iconSelected: iconPath("roleList_selected"),
        },
      },

      {
        path: "log",
        component: () => import("@/views/sys/log"),
        name: "log",
        meta: {
          icon: iconPath("log"),
          iconSelected: iconPath("log_selected"),
          perms: "log:view",
          title: "日志列表",
          noCache: true,
        },
      },
      {
        path: "dictList",
        component: () => import("@/views/sys/dictList"),
        name: "dictList",
        meta: {
          perms: "dictList:view",
          title: "字典管理",
          noCache: true,
          icon: iconPath("dictList"),
          iconSelected: iconPath("dictList_selected"),
        },
      },
      {
        path: "resourceList",
        component: () => import("@/views/sys/resourceList"),
        name: "resourceList",
        meta: {
          perms: "resource:view",
          title: "资源管理",
          noCache: true,
          icon: iconPath("resourceList"),
          iconSelected: iconPath("resourceList_selected"),
        },
      },
      {
        path: "configPage",
        component: () => import("@/views/sys/configPage"),
        name: "configPage",
        meta: {
          icon: iconPath("configPage"),
          iconSelected: iconPath("configPage_selected"),
          perms: "config:view",
          title: "系统配置",
          noCache: true,
        },
      },
      {
        path: "ContractApartyB",
        component: () => import("@/views/base/ContractApartyB"),
        name: "ContractApartyB",
        meta: {
          perms: "ContractApartyB:view",
          icon: iconPath("name"),
          iconSelected: iconPath("name_selected"),
          title: "子公司",
          noCache: true,
        },
      },
      {
        path: "SupplyGroup",
        component: () => import("@/views/base/SupplyGroup"),
        name: "SupplyGroup",
        meta: {
          perms: "SupplyGroup:view",
          icon: iconPath("name"),
          iconSelected: iconPath("name_selected"),
          title: "供应组",
          noCache: true,
        },
      },

      {
        path: "configurationParameter",
        component: () => import("@/views/sys/configurationParameter"),
        name: "configurationParameter",
        meta: {
          icon: iconPath("configPage"),
          iconSelected: iconPath("configPage_selected"),
          perms: "configParameter:view",
          title: "配置参数",
          noCache: true,
        },
      },

      {
        path: "reviewprocess",
        component: () => import("@/views/sys/reviewprocess"),
        name: "reviewprocess",
        meta: {
          icon: iconPath("configPage"),
          iconSelected: iconPath("configPage_selected"),
          perms: "reviewprocess:view",
          title: "审核流程",
          noCache: true,
        },
      },
    ],
  },
];
