<template>
  <div class="s-curd">
    <s-filter ref="s-filter" id="sFilter" :filters="filters" :name="name" :model="model" :store="store" :data-list="dataList"
              :select-list="selectList" :filterItem="filterItem" :option="option" :title="title" @search-change="searchChange"
              @buildQuery="buildQuery" @reset="resetOutFilter" @clearFilter="clearFilters" />
    <s-table ref="stable" :option="option" :tabs="tabs" :actions="actions" :changeactions="changeactions" :actionList="actionList"
             :beforeFetch="beforeFetch" :buttomlist="buttomlist" :model="model" :query="query" :store="store" :height="height"
             :editItem="editItem" :showSummary="showSummary" @editHeader="editHeader" @getSelectList="getSelectList"
             @getDataList="getDataList" @addClick="add" :otherHeight="otherHeight" :statetype="statetype" :searchDate="searchDate"
             :countList="countList" :defaultsort="defaultsort" :spanMethod="spanMethod">
      <!-- @summaryMethod="summaryMethod" -->
      <el-table-column v-if="option.showIndex" type="index" :width="45" align="center" :fixed="option.showSelection === 'fixed'">
      </el-table-column>
      <el-table-column v-if="option.showSelection" type="selection" width="45" align="center"
                       :fixed="option.showSelection === 'fixed'">
      </el-table-column>

      <template v-if="option.columns.length > 0">
        <template v-for="(colConfig, index) in option.columns">
          <template v-if="colConfig.isShow">
            <slot v-if="colConfig.slot" :name="colConfig.slot"></slot>
            <el-table-column v-if="colConfig.isEdit" :key="index" v-bind="colConfig" :show-overflow-tooltip="true"
                             v-on="$listeners" :class-name="colConfig.className"
                             :sortable="colConfig.sortable === undefined ? false : colConfig.sortable">
              <template slot-scope="scope" v-if="colConfig.isEdit">
                <div class="under-line" @click="editRow(scope.row)">{{ scope.row[colConfig.prop] }}</div>
              </template>
            </el-table-column>
            <el-table-column v-if="colConfig.format" :key="index" v-bind="colConfig" :show-overflow-tooltip="true"
                             v-on="$listeners" :class-name="colConfig.className"
                             :sortable="colConfig.sortable === undefined ? false : colConfig.sortable">
              <template slot-scope="scope">
                <state-tag v-if="scope.row[colConfig.prop]" :value="scope.row[colConfig.prop]" :format="colConfig.format">
                </state-tag>
              </template>
            </el-table-column>
            <el-table-column v-else :key="index" v-bind="colConfig" :show-overflow-tooltip="false" v-on="$listeners"
                             :class-name="colConfig.className"
                             :sortable="colConfig.sortable === undefined ? false : colConfig.sortable">
            </el-table-column>
          </template>
        </template>
      </template>
      <slot name="column"></slot>
      <span slot="form-btn" slot-scope="props">
        <slot name="form-btn" :form="props.form"></slot>
      </span>
    </s-table>
    <s-form :show="showForm" :model="model" :data="editItem" @save="saveForm" @close="closeForm">
      <template #formContent>
        <el-col :span="item.span || 8" v-for="(item, index) in model.form.columns" :key="'f_' + index">
          <el-form-item v-bind="item">
            <div v-if="item.formSlot">
              <slot :name="item.formSlot" :data="editItem" :config="item"></slot>
            </div>
            <!-- {{item.component}} -->
            <component v-else-if="item.component" :is="item.component" v-bind.sync="item" @info="setDataForm"
                       v-model="editItem[item.prop]" :item="editItem" />
            <el-input v-else v-model="editItem[item.prop]" v-bind="item" />
          </el-form-item>
        </el-col>
      </template>
    </s-form>
    <slot name="select"></slot>
  </div>
</template>

<script>
import SFilter from './filter'
import STable from './table'
import SForm from './form'
import Model from './model'
import Store from './store'
import { getItemByKey } from '@/utils/db'

let timer = null
export default {
  name: 'SCurd',
  components: { SFilter, SForm, STable },
  props: {
    spanMethod: {
      type: Function
    },
    name: {
      // 表格英文名称
      type: String,
      required: true
    },
    showSummary: {
      // 表格英文名称
      type: Boolean,
      default: false
    },
    title: {
      // 表格中文名称（导出时表格名称）
      type: String,
      default: ''
    },
    tabs: {
      // 页面是否嵌套tabs
      type: Boolean,
      default: false
    },
    actions: {
      type: Array,
      default() {
        return []
      }
    },
    changeactions: {
      type: Array,
      default() {
        return []
      }
    },
    actionList: {
      type: Array,
      default() {
        return []
      }
    },
    buttomlist: {
      type: Array,
      default() {
        return []
      }
    },
    listQuery: {
      type: Object,
      default: () => {
        return {}
      }
    },
    model: {
      type: Model,
      required: true
    },
    otherHeight: {
      type: [String, Number],
      default: ''
    },
    height: {
      type: [String, Number],
      default: ''
    },
    statetype: {
      type: [String, Number],
      default: ''
    },
    searchDate: {
      type: Array,
      default() {
        return []
      }
    },
    countList: {
      type: Array,
      default() {
        return []
      }
    },
    defaultsort: {
      type: Object,
      default() {
        return {}
      }
    },
    beforeFetch: {
      type: Function
    }
  },
  data() {
    return {
      visible: false,
      option: {
        columns: [] // 表格显示列,
      },
      query: {},
      editItem: undefined,
      showForm: false,
      dataList: [], // s-table返回的data列表数据被s-filter使用
      selectList: [],
      filters: [],
      filterItem: {}
    }
  },
  computed: {
    store: {
      get() {
        return new Store(this, {})
      },
      set() {
        return new Store(this, {})
      }
    }
  },
  watch: {
    listQuery: {
      handler(val) {
        if (val) this.searchChange()
      },
      deep: true
    }
  },
  async created() {
    await this.$nextTick()
    // console.log(this.actionList, 'actionList')
    const defaultTemplate = await getItemByKey('template', this.name)
    if (defaultTemplate) {
      this.option = JSON.parse(JSON.stringify(defaultTemplate))
    } else {
      // 深拷贝取出model中定义的option
      // this.option = JSON.parse(JSON.stringify(this.model.tableOption))
      this.option = this.model.tableOption
    }
    // 检索到需要过滤-显示的字段压入`this.filters`
    this.option.columns.forEach((col) => {
      if (col.filter && col.isFilter) this.filters.push(col)

      // console.log('测试')
      // console.log(this.filters)

      // if (col.filter && col.isFilter) {
      //     this.filters.push(col)
      //     if (col.filter.prop) {
      //         this.filterItem[col.filter.prop] = ''
      //     }
      //     if (col.filter.start) {
      //         this.filterItem[col.filter.start] = ''
      //     }
      //     if (col.filter.end) {
      //         this.filterItem[col.filter.end] = ''
      //     }
      // }
    })
    if (this.option.mountQuery) this.searchChange()

    this.$on('editRow', this.editRow)
    this.$on('delRow', this.delRow)
    this.$on('refresh', this.refreshList)
    this.$on('clearFilter', this.clearFilters)
  },
  destroyed() {
    this.store = null
    this.$off('editRow')
  },
  methods: {
    setFormEntity(dataItem) {
      // console.log(111)
      this.editItem = { ...this.editItem, ...dataItem }
    },

    setDataForm() {
      // console.log(123)
    },

    clearSelect() {
      this.$refs['stable'].clearSelect()
    },
    toLocation(index) {
      this.$refs['stable'].toLocation(index)
    },
    scrollLeft(scrollLeft) {
      this.$refs['stable'].scrollLeft(scrollLeft)
    },
    /**
     * 拿到s-table中`refresh`查询请求方法
     */
    refreshList() {
      this.$refs['stable'].$emit('refresh')
    },
    /**
     * 监听到s-filter提交按钮触发后 初始化filterItem并请求refresh查询所有
     */
    clearFilters() {
      this.filterItem = {}
      this.searchChange(this.filterItem)
      this.$refs['stable'].$emit('clearFilter')
    },
    // 调用refs stable里面清除clearFilter
    resetOutFilter() {
      this.$refs['stable'].$emit('clearFilter')
    },
    /**
     * s-filter输入时会触发同步更新 -> `this.query` -> 会关联 s-table `refresh`查询请求
     * @param searchObj
     */
    searchChange(searchObj) {
      // console.log(searchObj, 2222)
      this.query = searchObj ? Object.assign({}, this.listQuery, searchObj) : this.listQuery
      // 调用s-table中`getData`查询请求
      this.$nextTick(() => this.refreshList())
      // this.$refs['s-filter'].buildQuery(this.query)
      // 返回给页面 选择性使用
      this.$emit('search-change', searchObj)
    },
    editRow(item) {
      if (this.showForm) {
        clearTimeout(timer)
        this.closeForm()
        timer = setTimeout(() => {
          this.$nextTick(() => {
            this.editRow(item)
          })
        }, 100)
      } else {
        this.editItem = { ...item }
        this.$nextTick(() => {
          this.$refs['stable'].$emit('edit')
          this.showForm = true
        })
      }
    },
    delRow(item) {
      this.editItem = { ...item }
      this.$nextTick(() => {
        this.$refs['stable'].$emit('delRow')
      })
    },
    /**
     * 监听s-form关闭事件
     */
    closeForm() {
      this.showForm = false
      this.editItem = undefined
    },
    /**
     * 监听s-form保存事件
     */
    saveForm() {
      this.closeForm()
      this.$nextTick(() => this.refreshList())
    },
    /**
     * 监听s-table点击新增事件
     */
    add() {
      this.editItem = { ...this.model.model }
      this.showForm = true
    },
    /**
     * 监听s-table更新数据同步更新到 `this.dataList` 用于s-filter
     * @param list s-table返回的dataList数据
     */
    getDataList(list) {
      // console.log(this.option)
      this.dataList = list
    },
    /**
     * 监听s-table被选中的列表同步更新到`this.selectList` 用于s-filter
     * @param list 被选中的列表
     */
    getSelectList(list) {
      this.selectList = list
    },
    /**
     * 监听s-table点击table头部事件
     */
    editHeader(e) {
    },

    //指定列求和
    // summaryMethod(param) {
    //   const { columns, data } = param
    //   const sums = []
    //   columns.forEach((column, index) => {
    //     if (index === 0) {
    //       sums[index] = '合计'
    //       return
    //     }
    //     const values = data.map((item) => Number(item[column.property]))
    //     if (column.property === 'winningNum' || column.property === 'rewardAmount' || column.property === 'cardNum') {
    //       sums[index] = values.reduce((prev, curr) => {
    //         const value = Number(curr)
    //         if (!isNaN(value)) {
    //           return prev + curr
    //         } else {
    //           return prev
    //         }
    //       }, 0)
    //       sums[index]
    //     }
    //   })
    //   return sums
    // },

    buildQuery() {
      const res = this.$refs.stable.buildQuery()
      this.$refs['s-filter'].buildQuery(res)
    },
    clearFilter() {
      this.$nextTick(() => {
        this.$refs['stable'].$emit('clearFilter')
      })
    },
    filterHandler(value, row, column) {
      const property = column['property']
      return row[property] === value
    },
    changeCheckBox(e, item) {
      const type = 'filter_INS_' + item.prop
      if (Object.prototype.toString.call(this.filterItem[type]) === '[object Undefined]') {
        this.filterItem[type] = [e]
      } else {
        if (~this.filterItem[type].split(',').indexOf(e)) {
          this.filterItem[type] = this.filterItem[type]
            .split(',')
            .filter((v) => v !== e)
            .join(',')
        } else if (this.filterItem[type] === '') {
          this.filterItem[type] = e
        } else {
          if (~this.filterItem[type].indexOf(',')) {
            this.filterItem[type] = this.filterItem[type].split(',').concat([e]).join(',')
          } else {
            this.filterItem[type] = [this.filterItem[type]].concat([e]).join(',')
          }
        }
      }
      this.filterItem = JSON.parse(JSON.stringify(this.filterItem))
    },
    appendFilter(flag, data) {
      if (!flag) {
        if (data.filter && data.filter.prop) {
          delete this.filterItem[data.filter.prop]
        }
        if (data.filter && data.filter.start && data.filter.end) {
          delete this.filterItem[data.filter.start]
          delete this.filterItem[data.filter.end]
        }
        if (data.format) {
          delete this.filterItem['filter_INS_' + data.prop]
        }
      }
      Object.keys(this.filterItem).forEach((v) => {
        if (Object.prototype.toString.call(this.filterItem[v]) === '[object Array]') {
          if (this.filterItem[v] === []) {
            this.filterItem[v] = ''
          } else {
            this.filterItem[v] = this.filterItem[v].join(',')
          }
        }
      })
      this.searchChange(this.filterItem)
    }
  }
}
</script>
<style lang="scss" scoped>
.s-curd {
  position: relative;
  background: #fff;
  padding: 5px 15px 15px;
  //height: 100vh;
}
</style>
