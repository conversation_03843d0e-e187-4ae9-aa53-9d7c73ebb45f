<template>
  <div>
    <form ref="s-filter" class="s-filter" @submit.prevent="search" @reset.prevent="reset">
      <div class="top-filter">
        <div v-if="searchItem && !showMore" class="smart-search">
          <div class="prepend">
            <el-dropdown placement="bottom-start" @command="changeKey" trigger="click">
              <div class="el-dropdown-link">
                <i class="el-icon-arrow-down el-icon--right"></i>
              </div>
              <el-dropdown-menu slot="dropdown" style="width: 140px; max-height:500px;overflow: auto;">

                <el-dropdown-item v-for="item in smartSearchItems" :key="'s_' + item.prop" :command="item.prop">
                  {{ item.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span class="search-key">{{ searchItem.label }}:</span>
          </div>
          <div class="search-component">
            <component v-if="searchItem.component" :is="searchItem.component" :start.sync="searchForm[searchItem.filter.start]"
                       :end.sync="searchForm[searchItem.filter.end]" v-bind="searchItem.filter"
                       v-model="searchForm[searchItem.filter.prop]" clearable />
            <el-input v-else v-model="searchForm[searchItem.filter.prop]" v-bind="searchItem.filter" clearable
                      :placeholder="searchItem.label" />
          </div>
          <button type="submit" class="el-button el-button--primary search-btn el-icon-search" :disabled="loading"
                  v-loading="loading"></button>
          <!-- <button type="reset" class="el-button el-button--warning reset-btn el-icon-refresh" :disabled="loading"
                  v-loading="loading">
          </button> -->
        </div>
        <div class="filter-item tool-bar">
          <!-- <button class="more-search" @click.prevent="toogleMore" v-if="filters.length>0">切换搜索
          </button> -->
          <template v-if="option.exportAble === true || perms[option.exportAble]">
            <el-dropdown trigger="click" @command="handleDownload">
              <div class="el-dropdown-link">
                <el-button icon="el-icon-download" class="settingBtn">导出 </el-button>
              </div>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :disable="dataList.length === 0" command="current">
                  导出当前页
                </el-dropdown-item>
                <el-dropdown-item :disable="dataList.length === 0" command="all">
                  导出全部
                </el-dropdown-item>
                <el-dropdown-item :disable="dataList.length === 0" command="select">
                  导出选中
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template v-if="option.printAble">
            <el-button icon="el-icon-printer" @click="handlePrint" v-loading="isPrinting" class="settingBtn">打印
            </el-button>
          </template>
          <template v-if="option.showSetting">
            <el-dropdown @command="handleSetting" trigger="click">
              <div class="el-dropdown-link">
                <el-button icon="el-icon-setting" class="settingBtn">设置 </el-button>
              </div>
              <el-dropdown-menu slot="dropdown">
                <!--                                <el-dropdown-item command="filter">筛选设置</el-dropdown-item>-->
                <el-dropdown-item command="listSet">列表设置</el-dropdown-item>
                <el-dropdown-item command="print" v-if="option.printAble">打印设置</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </div>

      </div>

      <div class="more-filter" v-if="showMore">
        <el-row>
          <el-col :span="8" v-for="(item, index) in filters" :key="index">
            <el-col :span="5">
              <label>{{ item.label }}:</label>
            </el-col>
            <el-col :span="16">
              <slot v-if="item.filter.slot" :name="item.filter.slot" class="filter-item"></slot>
              <component v-if="item.component" :is="item.component" :start.sync="searchForm[item.filter.start]"
                         :end.sync="searchForm[item.filter.end]" v-bind.sync="item.filter" v-model="searchForm[item.filter.prop]"
                         clearable>
              </component>
              <el-input v-else v-bind="item.filter" v-model="searchForm[item.filter.prop]" clearable />
            </el-col>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-col :span="16" :offset="5">
              <button type="submit" class="el-button el-button--primary el-button--mini is-plain" :disabled="loading"
                      v-loading="loading">
                <i class="el-icon-search"></i>搜索
              </button>
              <button type="reset" class="el-button el-button--warning el-button--mini is-plain" :disabled="loading"
                      v-loading="loading">
                <i class="el-icon-refresh"></i>重置
              </button>
            </el-col>
          </el-col>
        </el-row>
      </div>
    </form>
    <el-dialog :append-to-body="true" v-if="printSetTraitDialog" :visible.sync="printSetTraitDialog"
               :close-on-press-escape="false" :close-on-click-modal="false" title="打印设置" key="printSetTraitDialog"
               class="columnSortTraitDialog" :before-close="printSortDialogCloseHandler">
      <div class="columnSortSingle">
        <div class="singleInterior" v-for="(single, index) of printSetList" :key="index">
          <div class="singleCont">
            {{ single.label }}
            <div class="fr">
              <el-switch v-model="single.isPrint" active-text="打印" inactive-text="不打印" :key="index"
                         @change="triggerTabShow = true"></el-switch>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="printSortDialogCloseHandler">取 消</el-button>
        <el-button type="primary" @click="printSortDialogConfirmHandler">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog :append-to-body="true" :visible.sync="columnSortDialogVisible" v-if="columnSortDialogVisible"
               :close-on-press-escape="false" :close-on-click-modal="false" title="列表设置" key="columnSortTraitDialog"
               class="columnSortTraitDialog" :before-close="columnSortDialogCloseHandler">
      <div class="columnSortSingle">
        <div class="singleInterior" v-for="(single, index) of draggedList" :key="index"
             v-dragging="{ item: single, list: draggedList, group: 'color' }">
          <div class="singleCont">
            {{ single.label }}
            <div class="fr">
              <el-switch v-model="single.isShow" active-text="显示" inactive-text="隐藏" :disabled="single.notHidden" :key="index"
                         @change="triggerTabShow = true">
              </el-switch>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="columnSortDialogCloseHandler">取 消</el-button>
        <el-button type="primary" @click="columnSortDialogConfirmHandler">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { exportExcel, parseTime } from '@/utils/index'
import { getItemByKey, setItemByKey } from '@/utils/db'
import Cache from '@/utils/cache'
import Func from '@/utils/func'
import Model from '../model'
import fetch from '@/utils/request'
import trans from '@/api/font'

const pdfMake = window.pdfMake
export default {
  name: 'SFilter',
  components: {},
  props: {
    filters: {
      // 表格字段
      type: Array,
      required: true,
      default() {
        return []
      }
    },
    filterItem: {
      // 表格字段
      type: Object,
      required: true,
      default() {
        return {}
      }
    },
    store: {
      type: Object,
      required: true,
      default() {
        return {}
      }
    },
    loading: {
      type: Boolean,
      default() {
        return false
      }
    },
    dataList: {
      type: Array,
      required: true,
      default() {
        return []
      }
    },
    selectList: {
      type: Array,
      required: true,
      default() {
        return []
      }
    },
    name: {
      type: String,
      required: true
    },
    option: {
      type: Object,
      required: true,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    model: {
      type: Model,
      required: true
    }
  },
  data() {
    return {
      searchKey: '',
      searchForm: {},
      extFilter: {},
      showMore: false,
      isPrinting: false,
      printSetTraitDialog: false,
      query: {},
      settingCommand: '',
      selectedItems: [],
      isRender: true, // 是否渲染
      draggedList: [], // 拖动列表
      cachePrintSortList: [], // 缓存打印列表
      printSetList: [], // 打印列表
      fixationList: [], // 固定的列头
      triggerDragged: false,
      allItems: [],
      draggedLaterList: [],
      printList: [],
      searchItem: undefined,
      columnSortDialogVisible: false
    }
  },
  watch: {
    filters: {
      handler(val) {
        if (this.searchKey) {
          const index = val.findIndex((val) => val.prop === this.searchKey)
          if (~index) {
            this.searchItem = val[index]
          }
        } else {
          this.searchItem = val[0]
        }
        val.forEach((v) => {
          if (v.filter.ext) {
            const keys = Object.keys(v.filter.ext)
            keys.forEach((k) => {
              this.searchForm[v.filter.ext[k]] = v.filter[k]
            })
          }
        })
      },
      deep: true
    },
    columnSortDialogVisible(val) {
      if (val) {
        this.$dragging.$on('dragged', ({ value }) => {
          this.triggerDragged = true
          this.draggedLaterList = value.list
        })
      } else {
        this.$dragging.$off('dragged')
      }
    },
    searchKey(newVal, oldVal) {
      if (oldVal) {
        const index = this.smartSearchItems.findIndex((v) => v.prop === oldVal)
        if (index > -1) {
          const item = this.smartSearchItems[index]
          if (item.filter && item.filter.prop) {
            this.searchForm[item.filter.prop] = ''
          }
        }
      }
    }
  },
  created() {
    const filters = {}
    const extFilter = {}
    this.filters.forEach((v) => {
      const obj = ''
      if (v.filter.prop) {
        if (typeof v.filter.prop === 'string') {
          filters[v.filter.prop] = obj
        }
        if (typeof v.filter.ext === 'object') {
          const keys = Object.keys(v.filter.ext)
          keys.forEach((k) => {
            filters[v.filter.ext[k]] = ''
            extFilter[k] = v.filter.ext[k]
          })
        }
      }
    })
    /**
     * 只有一个查询条件的默认显示单项搜索
     */
    if (this.filters.length <= 1) {
      this.showMore = false
    }
    /**
     * 首次初始化filters 成对应传入的filter.prop对象集合
     */
    this.searchForm = filters

    this.extFilter = extFilter

    /**
     * 配置下拉菜单只能搜索提示
     */
    if (this.smartSearchItems.length > 0) {
      this.searchKey = this.smartSearchItems[0].prop
    }
  },
  destroyed() {
    this.$dragging.$off('dragged')
  },
  async mounted() {
    this.store.commit('setFilterHeight', this.$refs['s-filter'].clientHeight)
    const printList = await getItemByKey('supPrinttemplate', this.name)
    if (printList) {
      this.printList = printList
    }
  },
  computed: {
    ...mapGetters(['perms']),
    smartSearchItems() {
      const filters = []
      this.filters.forEach((v) => {
        if (!v.filter.justMore) {
          filters.push(v)
        }
      })
      return filters
    }
    // searchItem() {
    //     const index = this.filters.findIndex(val => val.prop === this.searchKey)
    //     return index > -1 ? this.filters[index] : null
    // }
  },
  methods: {
    reset() {
      const searchObj = this.searchForm
      for (var key in searchObj) {
        searchObj[key] = ''
      }
      this.store.commit('setFilters', searchObj)
      this.$emit('search-change', searchObj)
    },
    search() {
      if (this.filterItem) {
        if (Object.keys(this.filterItem).length > 0) {
          this.$emit('clearFilter')
        }
      }
      const searchObj = Object.freeze({ ...this.searchForm })
      this.store.commit('setFilters', searchObj)
      this.$emit('reset', true)
      this.$emit('search-change', searchObj)
    },
    toogleMore() {
      this.showMore = !this.showMore
      this.$nextTick(() => {
        this.store.commit('setFilterHeight', this.$refs['s-filter'].clientHeight)
        this.store.curd.$refs['stable'].$emit('filterHeight', this.$refs['s-filter'].clientHeight)
      })
    },
    changeKey(key) {
      this.searchKey = key
      const index = this.filters.findIndex((val) => val.prop === this.searchKey)
      if (~index) {
        this.searchItem = this.filters[index]
      }
    },
    handleSetting(command) {
      this.settingCommand = command
      this.allItems = []
      const selected = []
      switch (command) {
        case 'print':
          this.printSortCollection()
          this.printSetTraitDialog = true
          break
        case 'listSet':
          this.columnSortCollection()
          this.columnSortDialogVisible = true
      }
      this.selectedItems = selected
    },

    buildQuery(query) {
      this.query = query
    },
    async handleDownload(type) {
      // if (type === 'reportDown') {
      //     this.dialogExportDownVisible = true
      //     return true
      // }
      if (type === 'all') {
        await this.$emit('buildQuery')
        await this.$confirm('当前数据量比较大, 是否继续导出?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const res = await fetch({
          url: '/cwe/a/export/exportAllToExcel',
          method: 'post',
          data: {
            ContentType: 'json',
            url: Func.getBaseUrl(this.model.name) + this.model.name + '/page',
            title: this.title,
            query: this.query,
            columns: this.option.columns
          }
        })
        if (res) {
          this.$message.success('导出任务创建成功,请在导出中心下载数据')
        }
      } else if (type === 'current') {
        const dictHashMap = Cache.get('dictHashMap') || {}
        const batchNo = parseTime(new Date(), '{y}-{m}-{d}')

        const header = []
        const columns = []
        const keys = []
        this.option.columns.forEach((val) => {
          if (!val.type && val.isShow) {
            // 没type 有isShow
            if (!val.noExport) {
              const tHeader = val.isHeader ? val.isHeader + val.label : val.label // 有设定表头就用表头没有则用label
              header.push(tHeader)
              columns.push(val.prop || val.slot)
            }
            if (val.slot) {
              keys.push(val.slot)
            }
            if (val.format) {
              keys.push(val.prop)
            }
          }
        })

        const list = JSON.parse(JSON.stringify(this.dataList))
        // console.log('list', list)
        // console.log('keys', keys)

        list.forEach((v) => {
          keys.forEach((key) => {
            if (dictHashMap[v[key]]) {
              v[key] = dictHashMap[v[key]]
            }
          })
        })

        exportExcel({
          title: this.title + '-' + batchNo,
          header: header,
          columns: columns,
          list: list
        })
        await fetch({
          url: '/cwe/a/exportLog/save',
          method: 'post',
          data: {
            exportPage: this.title
          }
        })
      } else {
        if (this.selectList.length === 0) {
          this.$message({ message: `请选择至少一条有效数据`, type: 'warning' })
          return false
        }
        const dictHashMap = Cache.get('dictHashMap') || {}
        const batchNo = parseTime(new Date(), '{y}-{m}-{d}')
        const header = []
        const columns = []
        const keys = []
        this.option.columns.forEach((val) => {
          if (!val.type && val.isShow) {
            if (!val.noExport) {
              const tHeader = val.isHeader ? val.isHeader + val.label : val.label
              header.push(tHeader)
              columns.push(val.prop || val.slot)
            }
            if (val.slot) {
              keys.push(val.slot)
            }
          }
        })
        const list = JSON.parse(JSON.stringify(this.selectList))
        list.forEach((v) => {
          keys.forEach((key) => {
            if (dictHashMap[v[key]]) {
              v[key] = dictHashMap[v[key]]
            }
          })
        })

        exportExcel({
          title: this.title + '-' + batchNo,
          header: header,
          columns: columns,
          list: list
        })
        await fetch({
          url: '/cwe/a/exportLog/save',
          method: 'post',
          data: {
            exportPage: this.title
          }
        })
      }
    },
    /**
     *获取列头数集
     */
    columnSortCollection() {
      const middle = JSON.parse(JSON.stringify(this.option.columns))

      middle.forEach((v) => {
        if (v.fixed) {
          this.fixationList.push(v)
        } else if (v.label) {
          this.draggedList.push(v)
        }
      })
    },
    /**
     * 列头排序对话框关闭处理
     */
    columnSortDialogCloseHandler() {
      this.columnSortDialogVisible = false
      this.draggedList = []
      this.fixationList = []
      this.draggedLaterList = []
      this.triggerDragged = false
      this.triggerTabShow = false
    },
    /**
     * 列头排序对话框提交处理
     */
    columnSortDialogConfirmHandler() {
      let middle
      if (!this.triggerDragged && !this.triggerTabShow) {
        this.columnSortDialogCloseHandler()
        return false
      }
      if (this.triggerDragged) {
        middle = [...this.fixationList, ...this.draggedLaterList]
      } else {
        middle = [...this.fixationList, ...this.draggedList]
      }
      this.isRender = false
      this.option.columns = middle
      setItemByKey('template', this.name, this.option)
      this.columnSortDialogCloseHandler()
      this.$nextTick(() => {
        this.isRender = true
      })
    },
    // 打印相关-start
    async handlePrint() {
      this.isPrinting = true
      this.print(this.buildPdfContent())
    },
    /*
     *获取打印数集
     */
    printSortCollection() {
      if (this.cachePrintSortList.length) {
        this.printSetList = JSON.parse(JSON.stringify(this.cachePrintSortList))
        return false
      }
      const middle = JSON.parse(JSON.stringify(this.option.columns))
      middle.forEach((val) => {
        if (val.type) {
          return false
        }
        this.printSetList.push({
          key: val['prop'] || val['slot'],
          label: val['label'],
          isSum: val['isSum'],
          isPrint: false
        })
      })
      if (this.printList.length) {
        const collection = []
        this.printList.forEach((v) => {
          this.printSetList.forEach((_v, _i) => {
            if (_v.key === v.key) {
              collection.push(_v)
              _v.isPrint = true
            }
          })
        })
        collection.forEach((v) => {
          this.printSetList.forEach((_v, _i, array) => {
            if (_v === v) {
              array.splice(_i, 1)
            }
          })
        })
        this.printSetList.unshift(...collection)
      }
    },
    buildPdfContent() {
      const widths = [14]
      const listData = []
      const head = ['序号']
      const keys = []
      const total = ['合计']
      const totalKeys = []
      const distKeys = []
      let index = 0
      if (this.printList.length > 0) {
        this.printList.forEach((val) => {
          index++
          widths.push('*')
          if (val.isSum) {
            totalKeys.push(val.key)
          }
          total.push(val.isSum ? 0 : '')
          keys.push(val.key)
          if (val.format) {
            distKeys.push({ type: val.format, index: index })
          }
          head.push({
            text: val.label,
            style: 'tableHeader1'
          })
        })
      } else {
        let index = 0
        this.option.columns.forEach((val) => {
          index++
          if (!val.type) {
            widths.push('*')
            if (val.isSum) {
              totalKeys.push(val['prop'] || val['slot'])
            }
            total.push(val.isSum ? 0 : '')
            keys.push(val['prop'] || val['slot'])
            head.push({
              text: val.label,
              style: 'tableHeader1'
            })
          }
          if (val.format) {
            distKeys.push({ type: val.format, index: index })
          }
          // if (val.filter) {
          //     if (val.filter.type) {
          //         distKeys.push({type: val.filter.type, index: index})
          //     }
          // }
        })
      }
      listData.push(head)
      this.dataList.forEach((val, i) => {
        const item = [i + 1]
        keys.forEach((v, k) => {
          if (totalKeys.includes(v)) {
            total[k + 1] += val[v]
          }
          item.push(val[v] || '')
        })
        distKeys.forEach((_v) => {
          item[_v.index] = Func.getDictText(item[_v.index], _v.type)
        })
        listData.push(item)
      })
      listData.push(total)
      const content = [
        { text: this.title, style: 'header' },
        {
          style: 'tableExample',
          table: {
            headerRows: 1,
            widths: widths,
            body: listData
          }
        }
      ]
      return Func.buildPdf(content, { pageSize: 'A4', showFoot: true })
    },
    async print(pdfData) {
      const res = await Func.fetch(trans, Func.uniqueString(JSON.stringify(pdfData)) + ' ')
      if (res) {
        pdfMake.vfs = res.data
        const pdfDocGenerator = pdfMake.createPdf(pdfData)
        pdfDocGenerator.print()
      }
      setTimeout(() => {
        this.isPrinting = false
      }, 500)
    },
    /**
     * 打印对话框关闭处理
     */
    printSortDialogCloseHandler() {
      this.printSetTraitDialog = false
      this.printSetList = []
      this.draggedLaterList = []
      this.triggerDragged = false
      this.triggerTabShow = false
    },
    /**
     * 打印对话框提交处理
     */
    printSortDialogConfirmHandler() {
      let middle
      if (!this.triggerDragged && !this.triggerTabShow) {
        this.printSortDialogCloseHandler()
        return false
      }
      if (this.triggerDragged) {
        middle = [...this.draggedLaterList]
      } else {
        middle = [...this.printSetList]
      }
      this.cachePrintSortList = [...middle]
      middle = middle.filter((v) => v.isPrint)
      this.printList = JSON.parse(JSON.stringify(middle))
      setItemByKey('supPrinttemplate', this.name, this.printList)
      this.printSortDialogCloseHandler()
    }
    // 打印相关-end
  }
}
</script>
<style lang="scss">
.s-filter {
  background: #ffffff;
  padding-bottom: 2px;
  font-size: 12px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
    margin-bottom: 5px;

    label {
      color: #333333;
      font-weight: normal;
      margin-right: 3px;
    }
  }

  .more-search {
    border: none;
    color: #409eff;
    font-weight: 600;
    display: inline-block;
    padding: 5px;
    margin-right: 5px;
    cursor: pointer;

    & :hover {
      text-decoration: underline;
    }

    & :focus {
      outline: none;
    }
  }

  .more-filter {
    .el-col {
      display: flex;
      align-items: center;
      margin-bottom: 2px;
      justify-content: flex-start;

      label {
        font-weight: normal;
        font-size: 12px;
        color: #666;
        margin-right: 5px;
        min-width: 80px;
        text-align: left;
      }
    }
  }

  .el-input__inner {
    color: #333333 !important;
    /*border-radius: 0;*/
  }

  .el-select {
    width: auto;
  }

  .smart-search {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    width: 500px;
    margin-bottom: 5px;
    border-radius: 3px;

    .prepend {
      .el-dropdown {
        cursor: pointer;
      }

      .el-icon-arrow-down {
        width: 26px;
        text-align: center;
      }

      .search-key {
        display: inline-block;
        height: 30px;
        line-height: 30px;
      }
    }

    .search-component {
      flex: 1;

      .el-input {
        width: 100%;
        height: 30px;
        line-height: 30px;

        .el-input__inner {
          border: none;
        }

        & .is-focus {
          .el-input__inner {
            border-color: #dcdfe6 !important;
          }
        }
      }
    }

    .search-btn {
      height: 30px;
      width: 50px;
      border-radius: 3px;
      border: none;
      border-left: 1px solid #ddd;
      background-color: #f7f7f7;
      color: #333;
      font-size: 14px;
      padding-top: 9px;
    }

    .reset-btn {
      height: 30px;
      width: 50px;
      border-radius: 3px;
      border: none;
      border-left: 1px solid #ddd;
      background-color: #f2ce99;
      color: #333;
      font-size: 14px;
      padding-top: 9px;
      margin-left: 0;
    }
  }

  .top-filter {
    display: flex;
  }

  .tool-bar {
    flex: 1;
    display: flex;
    justify-content: flex-end;
  }
}

.drop-btn {
  width: 65px;
  height: 32px;
  font-size: 16px;
  text-align: center;
  padding: 0;
}

.columnSortTraitDialog {
  .columnSortSingle {
    padding: 10px;
    max-height: 500px;
    overflow: auto;

    .singleInterior {
      border-width: 1px 1px 0 1px;
      border-style: solid;
      border-color: rgb(235, 238, 245);
      padding: 12px 0;

      .singleCont {
        width: 100%;
        box-sizing: border-box;
        padding: 0 10px;
        font-size: 14px;
      }
    }

    & > .singleInterior:nth-last-child(1) {
      border-width: 1px;
    }
  }
}

.settingBtn {
  height: 30px;
  box-sizing: border-box;
  margin-left: 5px;
}
</style>
