<template>
    <div class="s-edit" :class="{ open: show }">
        <div class="s-edit-right" @click="close">
            <i class="el-icon-arrow-right"></i>
        </div>
        <div class="s-title">{{ this.data && this.data.id ? '修改' : '新增' }}<a class="close" @click="close">关闭</a>
        </div>
        <div class="s-content">
            <el-form v-if="data" ref="form" :model="data" v-bind="model.form.config" :rules="model.form.rules">
                <el-row>
                    <slot name="formContent"></slot>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item>
                            <el-button type="primary" @click="submit" :loading="loading"
                                       v-if="!model.form.config.saveHidden">保存
                            </el-button>
                            <el-button @click="close">关闭</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </div>
</template>

<script>
import Model from '../model'

export default {
    name: 'SEdit',
    components: {},
    props: {
        model: {
            type: Model,
            required: true,
        },
        data: {
            type: Object,
        },
        show: {
            type: Boolean,
            default() {
                return false
            },
        },
    },
    created() {
    },
    data() {
        return {
            value: {},
            loading: false,
        }
    },
    watch: {
        data: {
            handler(val) {
                // console.log(val, 'val')
            },
            deep: true,
        },
    },
    computed: {},
    methods: {
        setData(info) {
            for (const key in info) {
                this.data[key] = info[key]
            }
        },
        close() {
            this.$emit('close')
        },
        async submit() {
            try {
                const validate = await this.$refs['form'].validate()
                if (validate) {
                    this.loading = true
                    const res = await this.model.save(this.data)
                    this.loading = false
                    if (res) {
                        this.$notify.success('保存成功')
                        this.$emit('save')
                    } else {
                        this.loading = false
                    }
                }
            } catch (e) {
                this.loading = false
            }
        },
    },
}
</script>
<style lang="scss">
.s-edit {
  position: fixed;
  background: #ffffff;
  top: 43px;
  width: 668px;
  right: -668px;
  bottom: 0;
  box-shadow: -10px 20px 40px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease-out;
  z-index: 1003;
  overflow-y: auto;

  .el-button {
    border-radius: 0;
  }

  &.open {
    right: 0;

    .s-edit-right {
      right: 768px;
    }
  }

  .s-edit-mask {
    position: fixed;
    background: transparent;
    width: 100%;
    left: 0;
    bottom: 0;
    top: 0;
    height: 100%;
    z-index: 1002;
    cursor: pointer;
  }

  .s-edit-right {
    width: 28px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 43px;
    cursor: pointer;
    font-size: 30px;
    right: -28px;
    transition: all 0.2s ease-out;
    background: #ffffff;
    opacity: 0.8;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }

  .s-content {
    padding: 40px 0;

    .el-col {
      padding: 0 5px;
    }

    .el-input__inner {
      border-radius: 0;
    }

    .el-form-item {
      margin-bottom: 18px;
    }

    .el-form-item__label {
      font-weight: normal;
      font-size: 12px;
      color: #666;
    }

    .el-form-item__error {
      position: absolute;
      bottom: 0;
    }
  }

  .s-title {
    position: absolute;
    top: 0;
    width: 100%;
    font-size: 14px;
    padding: 0 10px 0 18px;
    height: 35px;
    line-height: 35px;
    background: #f8f8f8;
    margin-bottom: 5px;

    &:before {
      content: ' ';
      display: block;
      position: absolute;
      width: 4px;
      height: 15px;
      left: 10px;
      top: 10px;
      background: #4074e1;
    }

    .close {
      position: absolute;
      right: 10px;
      cursor: pointer;
      font-size: 12px;
      display: inline-block;
    }
  }

  .s-toolbar {
    position: absolute;
    bottom: 0;
    height: 30px;
    width: 100%;
    padding: 0 10px;
  }
}
</style>
