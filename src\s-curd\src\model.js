/*
 * @Author: your name
 * @Date: 2021-11-19 09:32:12
 * @LastEditTime: 2021-12-08 16:27:53
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \coal_manager_admin\src\s-curd\src\model.js
 */
import fetch from '@/utils/request'

export default class Model {
  constructor(name, dataJson, form, tableOption) {
    this.name = name
    this._model = dataJson
    this._form = {
      config: {
        labelWidth: '75px',
        labelPosition: 'right',
      },
      columns: [],
      rules: {},
      ...form,
    }
    this._tableOption = {
      showSetting: false,
      add: false,
      edit: false,
      del: false,
      showIndex: false,
      showPage: true,
      showSelection: false,
      mountQuery: true,
      actions: [],
      actionList: [],
      buttomlist: [],
      table: {
        stripe: true,
        border: false,
        'element-loading-text': '加载中...',
        'highlight-current-row': true,
        'header-cell-style': { background: '#2f79e8', color: '#fff', 'font-weight': 400, height: '48px' },
        'cell-style': { height: '44.5px', 'border-bottom': 'none' },
      },
      ...tableOption,
    }
    Object.freeze(this._model)
    Object.freeze(this._form)
    Object.freeze(this._tableOption)
  }

  get model() {
    return this._model
  }

  get form() {
    return this._form
  }

  get tableOption() {
    return this._tableOption
  }

  async page(searchForm) {
    return fetch({
      url: this.name + '/page',
      method: 'get',
      params: searchForm,
    })
  }

  async get(id) {
    return fetch({
      url: this.name + '/get',
      method: 'get',
      params: {
        id,
      },
    })
  }

  delete(ids) {
    return fetch({
      url: typeof ids === 'object' ? this.name + '/batchDelete' : this.name + '/delete',
      method: 'post',
      data: typeof ids === 'object' ? { ids } : { id: ids },
    })
  }

  save(data) {
    return fetch({
      url: this.name + '/save',
      method: 'post',
      data,
    })
  }

  deleteId(id) {
    return fetch({
      url: this.name + '/delete',
      method: 'post',
      data: { id },
    })
  }
  getImportConfig(traits = { date: { type: 'date', dateFormat: 'YYYY-MM-DD' } }) {
    const exportHeader = []
    const exportHeaderMap = {}
    const traitSettings = { colHeaders: null, columns: [] }
    this.tableOption.columns.forEach(column => {
      if (!column.noExport) {
        const { label, prop } = column
        const trait = traits[prop] ? traits[prop] : { type: 'text' }
        traitSettings.columns.push({ data: prop, ...trait })
        exportHeader.push(label)
        exportHeaderMap[label] = prop
      }
    })
    traitSettings.colHeaders = [...exportHeader]
    return { exportHeader, exportHeaderMap, traitSettings }
  }

  filterOption(col) {
    const option = {
      showMore: true,
      columns: [
        {
          prop: 'startDate',
          component: 'date-select',
          filter: 'filter_GES_date',
          // defaultValue: '',
          props: {
            placeholder: '开始日期',
            clearable: false,
          },
        },
        {
          prop: 'endDate',
          component: 'date-select',
          filter: 'filter_LES_date',
          // defaultValue: '',
          props: {
            placeholder: '结束日期',
            clearable: false,
          },
        },
      ],
    }
    if (col) {
      if (col instanceof Array) {
        option.columns.push(...col)
      } else {
        option.columns.push(col)
      }
    }

    return option
  }
}
