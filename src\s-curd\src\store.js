/* eslint-disable no-useless-call */
import Vue from 'vue'

const Store = function (curd, initialState = {}) {
    if (!curd) {
        throw new Error('curd is required.')
    }
    this.curd = curd

    this.states = {
        filteredData: null,
        dataList: [],
        filters: {},
        filterHeight: 32,
        printAble: false
    }

    for (let prop in initialState) {
        if (initialState.hasOwnProperty(prop) && this.states.hasOwnProperty(prop)) {
            this.states[prop] = initialState[prop]
        }
    }
}

Store.prototype.mutations = {
    setData(states, data) {
        // const dataInstanceChanged = states._data !== data
        // states._data = data
        // Object.keys(states.filters).forEach((columnId) => {
        //     const values = states.filters[columnId]
        //     if (!values || values.length === 0) return
        //     const column = getColumnById(this.states, columnId)
        //     if (column && column.filterMethod) {
        //         data = data.filter((row) => {
        //             return values.some(value => column.filterMethod.call(null, value, row, column))
        //         })
        //     }
        // })
        //
        // states.filteredData = data
        // states.data = sortData((data || []), states)
        //
        // this.updateCurrentRow()
        //
        // const rowKey = states.rowKey
        //
        // if (!states.reserveSelection) {
        //     if (dataInstanceChanged) {
        //         this.clearSelection()
        //     } else {
        //         this.cleanSelection()
        //     }
        //     this.updateAllSelected()
        // } else {
        //     if (rowKey) {
        //         const selection = states.selection
        //         const selectedMap = getKeysMap(selection, rowKey)
        //
        //         states.data.forEach((row) => {
        //             const rowId = getRowIdentity(row, rowKey)
        //             const rowInfo = selectedMap[rowId]
        //             if (rowInfo) {
        //                 selection[rowInfo.index] = row
        //             }
        //         })
        //
        //         this.updateAllSelected()
        //     } else {
        //         console.warn('WARN: rowKey is required when reserve-selection is enabled.')
        //     }
        // }
        //
        // const defaultExpandAll = states.defaultExpandAll
        // if (defaultExpandAll) {
        //     this.states.expandRows = (states.data || []).slice(0)
        // } else if (rowKey) {
        //     // update expandRows to new rows according to rowKey
        //     const ids = getKeysMap(this.states.expandRows, rowKey)
        //     let expandRows = []
        //     for (const row of states.data) {
        //         const rowId = getRowIdentity(row, rowKey)
        //         if (ids[rowId]) {
        //             expandRows.push(row)
        //         }
        //     }
        //     this.states.expandRows = expandRows
        // } else {
        //     // clear the old rows
        //     this.states.expandRows = []
        // }

        Vue.nextTick(() => this.table.updateScrollY())
    },
    setFilters(states, data) {
        states.filters = data
    },
    setFilterHeight(states, height) {
        states.filterHeight = height
    }
}

const doFlattenColumns = (columns) => {
    const result = []
    columns.forEach((column) => {
        if (column.children) {
            result.push.apply(result, doFlattenColumns(column.children))
        } else {
            result.push(column)
        }
    })
    return result
}

Store.prototype.clearFilter = function () {
    // const states = this.states
    // const {tableHeader, fixedTableHeader, rightFixedTableHeader} = this.table.$refs
    // let panels = {}
    //
    // if (tableHeader) panels = merge(panels, tableHeader.filterPanels)
    // if (fixedTableHeader) panels = merge(panels, fixedTableHeader.filterPanels)
    // if (rightFixedTableHeader) panels = merge(panels, rightFixedTableHeader.filterPanels)
    //
    // const keys = Object.keys(panels)
    // if (!keys.length) return
    //
    // keys.forEach(key => {
    //     panels[key].filteredValue = []
    // })
    //
    // states.filters = {}
    //
    // this.commit('filterChange', {
    //     column: {},
    //     values: [],
    //     silent: true
    // })
}

Store.prototype.commit = function (name, ...args) {
    const mutations = this.mutations
    if (mutations[name]) {
        mutations[name].apply(this, [this.states].concat(args))
    } else {
        throw new Error(`Action not found: ${name}`)
    }
}

export default Store
