<template>
  <div>
    <div class="s-table">
      <div class="action" ref="action">
        <el-button class="filter-item borderRadio" type="primary"
                   v-if="option.add === true || perms[option.add]"
                   @click="add">
          新增
        </el-button>
        <template v-if="selectList.length > 0">
          <el-button class="filter-item borderRadio" type="danger"
                     v-if="option.del === true || perms[option.del]"
                     @click="del">删除
          </el-button>
        </template>

        <!-- <template v-if="buttomlist">
          <el-button class="filter-item" v-for="(action, index) in buttomlist" :key="index"
                     :class="action.type == 'payapply' ? 'yellow' : 'red'" v-bind="action.config"
                     @click="action.click(action.type)">
            {{ action.label }}
          </el-button>
        </template> -->

        <template>
          <div :class="[changeactions!='' && actions!=''?'class1':'' ]" style="display: flex;">
            <div v-if="changeactions">
              <div class="actionList" style="position: relative;">
                <el-button class="filter-item" :class="action.active == true ? 'activeClass' : ''"
                           type="danger"
                           v-for="(action, index) in changeactions" :key="index" v-bind="action.config"
                           style="position: relative;color:#fff;border:none"
                           @click="action.click(selectList, action.type)">
                  {{ action.label }}
                </el-button>
              </div>
            </div>
            <div v-if="actions">
              <el-button class="filter-item" v-for="(action, index) in actions" :key="index"
                         v-bind="action.config"
                         :class="[action.isborder==true?'filter-itembtn':'',action.ismargin==true?'itembtn':'']"
                         @click="action.click(selectList, action.type)">
                {{ action.label }}
              </el-button>
            </div>
          </div>
        </template>

        <template v-if="actionList">
          <!-- 数据状态 -->
          <div class="actionListV" style="position: relative;border-radius:0px;">
            <el-button class="filter-item" v-for="(action, index) in actionList" :key="index"
                       v-bind="action.config"
                       :class="action.active == true ? 'activestyle' : ''"
                       style="width:150px;position: relative;margin:5px 0px 10px 0;  border-left: 1px solid #DCDFE6;
                             border-top: 1px solid #DCDFE6;border-right: 0px solid #DCDFE6;height: 35px;border-bottom: 1px solid #DCDFE6;"
                       @click="action.click(action.type)">
              {{ action.label }}
              <div v-if="countList">
                <div class="numbertext">
                  <span v-for="(item, indexv) in countList" :key="indexv">

                    <span class="conttext" v-if="action.type == 'DRAFT,REJECT' && item.state == 'DRAFT'">{{
                        item.count
                      }}</span>
                    <span class="conttext" v-if="action.type == 'DRAFT,REJECT' && item.state == 'REJECT'">{{
                        item.count
                      }}</span>

                    <span class="conttext"
                          v-if="action.type == 'DRAFT,REJECT' && item.state == 'DRAFT,REJECT'">{{ item.count }}</span>

                    <!-- <span class="conttext" v-if="action.type == 'NEW' && item.state == 'PASS'">{{ item.count }}</span> -->
                    <span class="conttext" v-if="action.type == 'PASS' && item.state == 'PASS'">{{ item.count }}</span>

                    <span class="conttext" v-if="action.type == 'NEW' && item.state == 'NEW'">{{ item.count }}</span>

                    <!-- //付款结算NEW,PASS_FINANCE -->
                    <span class="conttext"
                          v-if="action.type == 'NEW,PASS_FINANCE' && item.state == 'ALLNEW'">{{ item.count }}</span>
                    <span class="conttext"
                          v-if="action.type == 'PASS_FINANCE' && item.state == 'PASS_FINANCE'">{{ item.count }}</span>

                    <span class="conttext" v-if="action.type == 'PAYED' && item.state == 'PAYED'">{{
                        item.count
                      }}</span>
                    <span class="conttext" v-if="action.type == 'CANCEL' && item.state == 'CANCEL'">{{
                        item.count
                      }}</span>
                  </span>
                </div>
              </div>
            </el-button>
          </div>
        </template>
      </div>

      <div v-if="selectList.length > 0" class="el-alert el-alert--success is-light">
        <i class="el-alert__icon el-icon-info"></i>
        <div class="el-alert__content">
          <div class="el-alert__title">
            已选择: <span class="count">{{ selectList.length }}</span>
            <el-button type="text" @click="clearSelect">清空</el-button>
          </div>
        </div>
      </div>
      <!-- <span>{{defaultsort}}</span> -->
      <!-- :default-sort="{ prop: defaultsort.prop ,order:defaultsort.order}" -->
      <el-table class="superTable" ref="superTable" :data="dataList" :show-summary="showSummary"
                v-loading="loading"
                :beforeFetch="beforeFetch" :row-class-name="tableRowClassName" v-bind="option.table"
                :summary-method="getSummaries" :row-style="rowStyle" :height="height || tableHeight"
                @sort-change="sortChange"
                @row-click="rowClick" @header-click="editHeader" @selection-change="selectionChange"
                :default-expand-all="false"
                :cell-style="cellStyle" :span-method="objectSpanMethod">
        <slot></slot>
      </el-table>
      <div v-if="option.selectAtBottom" class="select_warp">
        <el-button @click="handleSelectAll" class="select_warp_btn">{{
            select.all ? '取消全选' : '全选'
          }}
        </el-button>
        <el-button @click="handleSelectBatch" class="select_warp_btn">批量盘库</el-button>
      </div>

      <div class="pagination-container" ref="paginationContainer">
        <el-pagination v-if="option.showPage" :current-page="page.current" :page-size="page.size"
                       :total="page.total"
                       :pager-count="pagination.pageCount" :layout="pagination.layout"
                       :page-sizes="pagination.pageSizes"
                       next-text="下一页" @size-change="sizeChange" @current-change="currentChange">
          <el-button class="confirm">确认</el-button>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import {pagination} from '@/const'
import Model from '../model'
import Cache from '@/utils/cache'
// import { evaluate } from 'mathjs'
export default {
  name: 'STable',
  props: {
    spanMethod: {
      type: Function
    },
    actions: {
      type: Array,
      default() {
        return []
      }
    },
    changeactions: {
      type: Array,
      default() {
        return []
      }
    },
    actionList: {
      type: Array,
      default() {
        return []
      }
    },
    buttomlist: {
      type: Array,
      default() {
        return []
      }
    },
    tabs: {
      type: Boolean,
      default: false
    },
    showSummary: {
      type: Boolean,
      default: false
    },

    objectSpan: {
      type: Boolean,
      default: false
    },

    option: {
      // 表格默认属性
      type: Object,
      required: true,
      default() {
        return {
          showIndex: true,
          showPage: true,
          mountQuery: false,
          table: {},
          columns: []
        }
      }
    },
    store: {
      type: Object,
      default() {
        return {}
      }
    },
    model: {
      type: Model,
      required: true
    },
    editItem: {
      type: Object
    },
    query: {
      type: Object,
      default() {
        return {}
      }
    },
    otherHeight: {
      type: [String, Number],
      default: ''
    },
    height: {
      type: [String, Number],
      default: ''
    },
    statetype: {
      type: [String, Number],
      default: ''
    },
    searchDate: {
      type: Array,
      default() {
        return []
      }
    },
    countList: {
      type: Array,
      default() {
        return []
      }
    },
    defaultsort: {
      type: Object,
      default() {
        return {}
      }
    },
    beforeFetch: {
      type: Function
    }
  },
  data() {
    return {
      loading: false,
      dataList: [],
      sumsList: [],
      pagination,
      page: {
        current: 1,
        size: pagination.pageSize,
        total: 0
      },
      sfheight: 0,
      selectList: [],
      hasSelect: false,
      listQuery: {},
      activetype: '',
      tableHeight: undefined,
      editData: undefined,
      showForm: false,
      select: {
        all: false,
        batch: false
      },
      filtertype: {},
      prop: '',
      order: ''
    }
  },
  updated() {
    this.$nextTick(() => {
      this.$refs['superTable'].doLayout()
    })
  },
  computed: {
    ...mapGetters(['perms', 'dict']),
    formats() {
      const formats = []
      this.option.columns.forEach((val) => {
        if (val['format']) {
          formats.push(val)
        }
      })
      return formats
    }
  },
  created() {
    this.$on('refresh', this.refresh)
    this.$on('doLayout', this.doLayout)
    this.$on('filterHeight', this.filterHeight)
    this.$on('edit', this.edit)
    this.$on('delRow', this.delRow)
    this.$on('clearFilter', this.clearFilters)
    if (this.actionList.length > 0) {
      this.activetype = this.actionList[0].type
    }
  },
  destroyed() {
    this.dataList = {}
    this.$off('refresh')
    this.$off('clearFilter')
    this.$off('doLayout')
    this.$off('filterHeight')
    this.$off('edit')
    this.$off('delRow')
  },
  watch: {
    tableHeight(val) {
      if (val) {
      }
    },
    option(val) {
      if (val) {
        this.doLayout()
      }
    },
    locationList(v) {
    },
    defaultsort: {
      handler(newVal, oldVal) {
        let prop = newVal.prop
        let order = newVal.order
        if (!newVal.issearch) {
          this.sortChange({prop, order})
        }
      },
      deep: true // deep属性是深度监听formData对象里的属性的值是否改变。
    },
    actionList: {
      handler(newVal, oldVal) {
        newVal.forEach((element, indexv) => {
          if (element.active == true) {
            this.activetype = element.type
          }
        })
      },
      deep: true // deep属性是深度监听formData对象里的属性的值是否改变。
    }
  },
  mounted() {
    this.$parent.$emit('init')
    if (this.option.mountQuery) this.getData()
    this.doLayout()
    window.onresize = () => {
      this.doLayout()
    }
  },
  methods: {
    tableRowClassName({row, rowIndex}) {
      return ''
    },

    //设置单个单元格样式   行下标：rowIndex    列下标：columnIndex （磅房日志）
    cellStyle({row, column, rowIndex, columnIndex}) {
      if (row.sync === 'N' || row.sync === '') {
        if (column.label == '物料名称' && row.productFlag === '') {
          return 'background-color:#f0f9eb;color:#FF0000'
        }
        // if (column.label == '企业名称' && row.contractFlag === '') {
        //   return 'background-color:#f0f9eb;color:#FF0000'
        // }

        if (row.type == '1') {
          //销售
          if (column.label == '购货单位/客户名称' && row.contractFlag === '') {
            return 'background-color:#f0f9eb;color:#FF0000'
          }
        } else if (row.type == '2') {
          //采购
          if (column.label == '供应商/发货单位' && row.contractFlag === '') {
            return 'background-color:#f0f9eb;color:#FF0000'
          }
        }
        return 'background-color:#f0f9eb'
      }

      if (column.label == '物料名称' && row.productFlag === '') {
        return 'color:#FF0000'
      }
      if (row.type == '1') {
        //销售
        if (column.label == '购货单位/客户名称' && row.contractFlag === '') {
          return 'color:#FF0000'
        }
      } else if (row.type == '2') {
        //采购
        if (column.label == '供应商/发货单位' && row.contractFlag === '') {
          return 'color:#FF0000'
        }
        if (column.label == '亏吨' && row.lossNull > 0.2) {
          return 'color:#FF0000'
        }
      }

      // if (column.label == '企业名称' && row.contractFlag === '') {
      //   return 'color:#FF0000'
      // }
    },

    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      if (this.spanMethod) {
        return this.spanMethod({row, column, rowIndex, columnIndex}, this.dataList)
      }
      return {
        // [0,0] 表示这一行不显示， [2,1]表示行的合并数
        rowspan: 1,
        colspan: 1
      }
    },

    handleSelectAll() {
      this.select.all = !this.select.all
      this.$refs['superTable'].toggleAllSelection()
    },
    handleSelectBatch() {
      if (!this.selectList.length) {
        this.$notify.error('请选择一条记录')
        return false
      }
      this.$parent.$emit('selectBatch', this.selectList)
    },
    // 用户可能拖动headers所以需要动态获取当前的偏移值
    async toLocation(index) {
      await this.$nextTick()
      if (index === -1) {
        this.scrollLeft(0)
        return
      }
      const selectList = this.$refs.superTable.$children[0].$el.querySelector('tr').querySelectorAll('th.index')
      Array.from(selectList).some((ele, idx) => {
        if (idx === index) {
          this.scrollLeft(ele.offsetLeft - 200)
          return true
        }
      })
    },
    // 偏移值
    scrollLeft(scrollLeft) {
      this.$refs.superTable.bodyWrapper.scrollLeft = scrollLeft
    },
    // 自定义设置表格累加
    getSummaries(param) {
      if (this.option?.table?.summaryMethod) {
        return this.option?.table?.summaryMethod(param)
      }
      const sums = []
      const summaries = this.option.summaries || []
      let sumIndex = this.option.sumIndex || 0
      let sumText = this.option.sumText || '合计'
      let yesterday = this.option.yesterday
      let {columns, data} = param

      if (yesterday) {
        const {filter_GES_date, filter_LES_date} = this.query
        let GES_date_status = !!filter_GES_date
        let LES_date_status = !!filter_LES_date
        if (GES_date_status && LES_date_status) {
        } else if (GES_date_status && LES_date_status && filter_GES_date === filter_LES_date) {
          data = data.filter((item) => item.date === filter_GES_date)
        } else {
          data = data.filter((item) => item.date === yesterday)
        }
      }
      columns.forEach((column, index) => {
        if (index <= sumIndex) {
          sums[sumIndex] = sumText
        } else {
          const values = data.map((item) => Number(item[column.property]))
          let filters = data
            .map((item) => item[column.property])
            .filter((v) => {
              if (v === '' || v === null || v === undefined || v === 0 || v === 'null') return false
              return true
            })
          if (!values.every((value) => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            if (sumText === '加权平均') {
              const idx = summaries.findIndex((val) => {
                if (val.prop === column.property) {
                  if (!val.avg) {
                    // evaluate()
                    const actualWeight = data.map((item) => {
                      return item['actualWeight'] ? Number(item['actualWeight']) : 0
                    })
                    const weightingSum = actualWeight.reduce((prev, curr) => {
                      const value = Number(curr)
                      if (!isNaN(value)) {
                        return prev + curr
                      } else {
                        return prev
                      }
                    }, 0)
                    let hasNull = false
                    if (!values.every((value) => isNaN(value))) {
                      sums[index] =
                        values.reduce((prev, curr, idx) => {
                          if (isNaN(curr)) {
                            hasNull = true
                          }
                          const value = Number(curr)
                          if (!isNaN(value)) {
                            return prev + curr * actualWeight[idx]
                          } else {
                            return prev
                          }
                        }, 0) / weightingSum

                      // sums[index] = parseFloat(sums[index].toFixed(2)) || 0
                      const sum = sums[index].toFixed(val.fixed) + val.suffix
                      sums[index] = isNaN(sum) ? '' : sum
                    }
                  }
                  return true
                }
              })
              idx === -1 && (sums[index] = '')
            } else {
              // if (sumText === '平均') sums[index] = (sums[index] / filters.length).toFixed(2)
              // if (sumText === '平均') sums[index] = (sums[index] / values.length).toFixed(2)

              const idx = summaries.findIndex((val) => {
                if (val.prop === column.property) {
                  if (val.avg) {
                    sums[index] = filters.length ? sums[index] / filters.length : 0
                    sums[index] = sums[index].toFixed(val.fixed) + val.suffix
                  } else {
                    if (val.prop === 'plateNumber') {
                      const sum = data.length
                      sums[index] = isNaN(sum) ? '' : sum
                    } else {
                      const sum = sums[index].toFixed(val.fixed) + val.suffix
                      sums[index] = isNaN(sum) ? '' : sum
                    }
                  }
                  if (val.custom) {
                  }
                  return true
                }
                // else {
                //   //财务管理，运费明细里面。合计车牌号
                //   if (val.prop == 'plateNumber') {
                //     const sum = data.length
                //     sums[3] = isNaN(sum) ? '' : sum
                //   }
                // }
              })
              idx === -1 && (sums[index] = '')
            }
          } else {
            sums[index] = ''
          }
        }
      })
      return sums
    },
    // table行样式设置
    rowStyle({row, rowIndex}) {
      if (row.isWarning === 'Y') {
        return 'color:red'
      }
    },
    headerStyle() {
      return 'background: blue'
    },
    filterHeight(height) {
      if (height) {
        this.sfheight = height
        this.doLayout()
      }
    },
    // 请求初始化table表格数据
    refresh(type) {
      this.getData(true)
    },
    doLayout() {
      const clientHeight = document.documentElement.clientHeight
      let sFilterHeight = 50
      const otherHeight = this.otherHeight || 160
      if (this.sfheight > 0) {
        sFilterHeight = this.sfheight
      }
      const pcH = this.option.showPage ? 39 : 0
      const offsetTop = this.option.offsetTop ? this.option.offsetTop : 0
      const selectHeight = this.hasSelect ? 40 : 0
      this.tableHeight = clientHeight - sFilterHeight - otherHeight - pcH - selectHeight - offsetTop
    },
    // superTable事件清除所有过滤
    clearFilters() {
      this.$refs.superTable.clearFilter()
    },
    // superTable事件清除所有被选中
    clearSelect() {
      this.$refs.superTable.clearSelection()
    },
    getFormat(type, value) {
      if (!value) {
        return '未知'
      }
      const gather = this.dict[type]
      if (gather) {
        const index = gather.findIndex((val) => val.value === value)
        if (~index) {
          return gather[index].name
        } else {
          return '未知'
        }
      } else {
        return '未知'
      }
    },

    /**
     * 请求table数据
     * @param refresh:Boolean true则初始化查询
     */
    async getData(refresh) {
      if (this.loading) return
      this.loading = true
      try {
        const params = this.beforeFetch ? this.beforeFetch(this.buildQuery(refresh, 'beforeFetch')) : this.buildQuery(refresh)
        const res = await this.model.page(params)
        // const res = await this.model.page(this.buildQuery(refresh))
        this.page = {
          current: res.data.current,
          size: res.data.size,
          total: res.data.total
        }
        const records = res.data.records.map((val) => {
          let ext
          try {
            ext = JSON.parse(val.ext)
          } catch (e) {
            ext = {}
          }
          const formatObj = {}
          return Object.assign({}, val, ext, formatObj)
        })
        Object.freeze(records)
        if (this.dataList.length > 50) {
          setTimeout(() => {
            this.dataList = records
            this.loading = false
            // 通知更新
            this.$emit('getDataList', this.dataList)
          }, 100)
        } else {
          this.dataList = records
          this.loading = false
          // 通知更新
          this.$emit('getDataList', this.dataList)
        }
      } catch (e) {
        this.loading = false
      }
    },
    /**
     * 构建table查询条件页数条件
     * @param refresh:Boolean true则初始化查询
     * @returns {} 查询结构
     */
    buildQuery(refresh, type) {
      if (!type) {
        if (refresh) this.page.current = 1
      }
      if (this.statetype) {
        if (this.searchDate.length > 0) {
          return {
            size: this.page.size,
            current: this.page.current,
            ...this.query, // 调换了位置
            ...this.listQuery,
            filter_INS_state: this.statetype,
            filter_GES_commitDate: this.searchDate[0].start,
            filter_LES_commitDate: this.searchDate[0].end
          }
        } else {
          return {
            size: this.page.size,
            current: this.page.current,
            ...this.query, // 调换了位置
            ...this.listQuery,
            filter_INS_state: this.statetype
          }
        }
      } else {
        if (this.searchDate.length > 0) {
          return {
            size: this.page.size,
            current: this.page.current,
            ...this.query, // 调换了位置
            ...this.listQuery,
            filter_GES_commitDate: this.searchDate[0].start,
            filter_LES_commitDate: this.searchDate[0].end
          }
        } else {
          return {
            size: this.page.size,
            current: this.page.current,
            ...this.query, // 调换了位置
            ...this.listQuery
          }
        }
      }
    },
    /**
     * 合计方法
     * @param param
     * @returns {*|[]|[]}
     */
    summaryMethod(param) {
      // 如果自己写逻辑则调用summaryMethod方法
      if (typeof this.summaryMethod === 'function') {
        return this.summaryMethod(param)
      }
      const {columns, data} = param
      const sums = []
      if (columns.length > 0) {
        columns.forEach((column, index) => {
          let currItem = this.sumColumnList.find((item) => item.name === column.property)
          if (index === 0) {
            sums[index] = this.option.sumText || '合计'
          } else if (currItem) {
            switch (currItem.type) {
              case 'count':
                sums[index] = '计数:' + data.length
                break
              case 'avg':
                let avgValues = data.map((item) => Number(item[column.property]))
                let nowindex = 1
                sums[index] = avgValues.reduce((perv, curr) => {
                  let value = Number(curr)
                  if (!isNaN(value)) {
                    return (perv * (nowindex - 1) + curr) / nowindex++
                  } else {
                    return perv
                  }
                }, 0)
                sums[index] = '平均:' + sums[index].toFixed(2)
                break
              case 'sum':
                let values = data.map((item) => Number(item[column.property]))
                sums[index] = values.reduce((perv, curr) => {
                  let value = Number(curr)
                  if (!isNaN(value)) {
                    return perv + curr
                  } else {
                    return perv
                  }
                }, 0)
                sums[index] = '合计:' + sums[index].toFixed(2)
                break
            }
          } else {
            sums[index] = '-'
          }
        })
      }
      this.sumsList = sums
      return sums
    },
    /**
     * 表格排序改变时的钩子  请求数据重新渲染界面
     * @returns void
     */
    sortChange({prop, order}) {
      if (!prop && order) return false
      if (prop) {
        const orderBy = prop ? prop.split('_')[0] : ''
        const orderDir = order ? (order === 'descending' ? 'DESC' : 'ASC') : ''
        this.listQuery = {...this.listQuery, orderBy, orderDir}
        //  this.$parent.$emit
        // this.query = { orderBy, orderDir }
        this.getData()
        // 给页面暴露出钩子可以选择性使用
        this.$parent.$emit('sortChange', {orderBy, orderDir})
      }
    },
    /**
     * 表格行点击事件
     * @param row 当前点击行
     */
    rowClick(row) {
      // 给页面暴露出钩子可以选择性使用
      this.$parent.$emit('rowClick', row)
    },
    /**
     * 表格Header触发钩子
     * @param row
     */
    editHeader(row) {
      // 向父级发送钩子
      this.$emit('editHeader', row)
    },
    /**
     * el-table选中切换时触发钩子
     * @param list
     */
    selectionChange(list = []) {
      this.selectList = list
      if (list.length === this.dataList.length) {
        this.select.all = true
      } else {
        this.select.all = false
      }
      if (list.length > 0) {
        this.hasSelect = true
      } else {
        this.hasSelect = false
      }
      this.doLayout()
      this.$emit('getSelectList', list)
      this.$parent.$emit('selectItem', list)
    },
    add() {
      this.$emit('addClick')
    },
    edit() {
      if (!this.editItem) {
        this.$notify.error('请选择一条记录')
        return false
      }
      this.editData = this.editItem
    },
    // 单条删除
    async delRow() {
      try {
        await this.$confirm('确认删除?')
        const res = await this.model.delete(this.editItem.id)
        if (res) {
          this.$notify.success('删除成功')
          this.getData()
        }
      } catch (e) {
      }
    },
    // 批量删除
    async del() {
      if (this.selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      try {
        await this.$confirm('确认删除?')
        const ids = this.selectList.map((val) => val.id)
        const res = await this.model.delete(ids)
        if (res) {
          this.$notify.success('删除成功')
          this.getData()
        }
      } catch (e) {
      }
    },
    /**
     * 页码大小改变触发
     * @param size
     */
    sizeChange(size) {
      this.page.size = size
      this.getData()
      // 返回给页面的钩子
      this.$parent.$emit('sizeChange', size)
    },
    /**
     * 当前页变动时候触发
     * @param current 当前页
     */
    currentChange(current) {
      this.page.current = current
      this.getData()
      // 返回给页面的钩子
      this.$parent.$emit('currentChange', current)
    }
  }
}
</script>
<style>
.class1 {
  display: flex;
  /* justify-content: space-between; */
}

.class2 {
}

.el-table .warning-row {
  background: oldlace;
}

.el-table .success-row {
  background: #f0f9eb;
}
</style>

<style>
.filter-itembtn {
  border: 1px solid #ff9639 !important;
}

.itembtn {
  color: #398fff !important;
  border: 1px solid #398fff !important;
}

.itembtn:focus,
.itembtn:hover {
  background: #398fff !important;
  border-color: #fff !important;
  color: #fff !important;
}

.filter-itembtn:focus,
.filter-itembtn:hover {
  background: #ff9639 !important;
  border-color: #fff !important;
  color: #fff !important;
}

/* .actionListV .filter-item:first-child {
  border-top-left-radius: 4rpx !important;
  border-bottom-left-radius: 4rpx !important;
} */
.actionListV .filter-item {
  border-radius: 0 !important;
}

.actionListV .filter-item:last-child {
  border-right: 1px solid #dcdfe6 !important;
  /* border-top-right-radius: 4rpx !important;
  border-bottom-right-radius: 4rpx !important; */
}
</style>
<style scoped>
.numbertext {
  position: absolute;
  right: 10px;
  top: 5px;
  z-index: 99;
  /* color: #fff;
  background-color: #ff726b; */
  /* padding: 2px 5px;
  border-radius: 5px;
  font-size: 12px; */
}

.conttext {
  color: #fff;
  background-color: #ff726b;
  padding: 0 4px;
  border-radius: 5px;
  font-size: 12px;
}

.activestyle {
  background-color: #2f79e8;
  color: #fff;
}

.activeClass {
  background-color: orange;
  color: #fff;
}
</style>
<style lang="scss">
.yellow {
  background-color: #ff9639;
  color: #fff;
}

.red {
  background-color: #ff726b;
  color: #fff;
}

.red:hover {
  background-color: #ff726b;
  color: #fff;
}

.yellow:hover {
  background-color: #ff9639;
  color: #fff;
}

.el-table .el-table__cell {
  // padding: 12px 0;
  padding: 8px 0;
}

.select_warp {
  position: absolute;
  bottom: 3px;
  left: 0%;

  .select_warp_btn {
    margin-right: 5px;
    background: #2f79e8;
    color: #fff;
    padding: 8px;
    border-radius: 4px !important;
    border: none !important;
    min-width: 55px;
  }
}

.el-button--export:focus,
.el-button--export:hover {
  background: #fff !important;
  border-color: #ff9639 !important;
  color: #ff9639 !important;
}

.el-button--export {
  color: #fff;
  background-color: #ff9639;
  border-color: #ff9639;
  padding: 9px 13px;
}

// .el-button--export-all:focus,
// .el-button--export-all:hover {
//   background: #ff9639 !important;
//   border-color: #fff !important;
//   color: #fff !important;
// }

.el-button--export-all {
  min-width: 70px;
  color: #ff9639;
  background-color: #fff;
  border-color: #ff9639;
  padding: 9px 13px;
}

.el-table__fixed,
.el-table__fixed-left {
  box-shadow: none;
}

.el-table__fixed-right {
  right: 0 !important;
  box-shadow: 0 0 10px rgb(0 0 0 / 12%) !important;
  z-index: 99;
  // width: 105px !important;
}

.el-table__fixed-footer-wrapper tbody td {
  border: none !important;
  background: transparent;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background: rgb(245, 246, 251);
}

// .el-table__body tr.current-row > td {
//   // background: rgb(222, 223, 242) !important;
//   background: #f3f7fe !important;
// }

.el-table--border th.gutter:last-of-type {
  display: block !important;
  width: 17px !important;
}

// 合计行样式
.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  height: 50px;
  line-height: 50px;
  color: #2f79e8;
  font-size: 13px;
  background: rgb(245, 246, 251);
}

.el-table {
  border: 1px solid #eef0ff;
}

.el-table::before {
  background-color: transparent;
}

.s-table {
  transform: translateZ(0);
  backface-visibility: hidden;
  position: relative;

  .el-button {
    border-radius: 3px;
  }

  .el-alert {
    margin-bottom: 5px;
    padding: 2px 8px;

    .count {
      font-weight: bold;
    }

    .el-button {
      color: #67c23a;
      margin-left: 20px;
    }
  }

  .filter-container {
    border: 1px solid #e6ebf5;
    /*border-top: none;*/
    border-bottom: none;
    margin-bottom: 0;

    &:after {
      content: ' ';
      display: block;
      clear: both;
    }

    & > .el-button {
      margin-left: 2px;
    }

    /*border-right: 1px solid #e6ebf5;*/
    .el-select {
      width: auto;
    }

    .filter-item {
      &.isDate {
        .el-range-editor {
          width: 200px !important;
        }
      }
    }
  }

  .filter-item-set {
    width: auto !important;

    & > .el-dropdown {
      margin-left: 2px;
    }
  }

  .top-filter {
    &:after {
      content: ' ';
      display: block;
      clear: both;
    }
  }

  .more-filter {
    /*.filter-item.isDate {*/
    /*width: 347px*/
    /*}*/
    &.isFormat {
      .filter-item {
        label {
          display: inline-block;
          width: 68px;
          text-align: right;
        }

        &.isDate {
          width: 347px;

          .el-date-editor {
            width: 276px;
          }
        }

        &.isCod {
          width: 347px;
        }
      }

      .filter-item:last-child {
        width: auto;

        label {
          width: auto;
        }
      }

      .region-select {
        .el-input {
          width: 276px;
        }
      }
    }
  }

  .el-table .caret-wrapper {
    position: absolute;
    top: -5px;
  }

  .el-table__footer .el-table_1_column_1 {
    font-size: 12px;
    color: #606266;
    font-weight: normal;
  }

  .input-with-select {
    .el-input-group__append,
    .el-input-group__prepend {
      .el-input--suffix {
        width: 94px;
        padding-right: 22px;
      }
    }

    .search-component {
      .el-input {
        width: 150px;

        &.is-focus {
          .el-input__inner {
            border-color: #dcdfe6 !important;
          }
        }
      }
    }

    .el-input-group--append .el-input__inner {
      width: 240px;
    }

    .el-input-group__append,
    .el-input-group__prepend {
      padding: 0 15px 0 20px;
    }
  }

  .tags {
    margin-top: 5px;

    .el-tag {
      margin-right: 5px;
    }
  }

  .more-search {
    border: none;
    color: #409eff;
  }

  .pagination-container {
    display: flex;
    margin: 0;
    padding: 15px 0px 0px;
    justify-content: flex-end;
    // position: absolute;
    // right: 20px;
    // bottom: 20px;
    // z-index: 999;
    border: none;

    .el-pager li.active + li {
      border: 1px solid #f1f1f2;
    }

    .el-pager li {
      width: 40px;
      height: 35px;
      line-height: 35px;
      border-radius: 5px;
      border: 1px solid #f1f1f2;
      margin: 0 5px;
    }

    .el-pager li.active {
      background: #2f79e8;
      color: #fff;
    }

    .el-pagination .btn-next,
    .el-pagination .btn-prev {
      margin: 0px;
      border-radius: 5px;
      border: 1px solid #f1f1f2;
      height: 35px;
      line-height: 35px;

      span {
        line-height: 35px;
      }
    }

    .el-pagination button,
    .el-pagination span:not([class*='suffix']) {
      padding: 0 5px;
      // width: 16%;
      border-radius: 5px;
      height: 35px;
      line-height: 35px;
    }

    .el-pagination__editor.el-input .el-input__inner {
      height: 35px;
    }

    .el-pagination .el-select .el-input .el-input__inner {
      height: 35px;
    }

    .confirm {
      margin-left: 15px;
      height: 32px;
      line-height: 32px;
      background: #2f79e8;
      color: #fff;
      border-radius: 5px;
    }
  }

  .el-table--scrollable-x .el-table__body-wrapper {
    z-index: 1;
  }

  .action {
    flex: 1;

    button {
      margin: 5px 10px 10px 0;
      border: none;
    }

    &:after {
      content: ' ';
      display: block;
      clear: both;
    }
  }

  .setting {
    /*float: right;*/
    i {
      font-size: 16px;
    }

    margin-right: 5px;
  }
}

.borderRadio {
  border-radius: 5px;
}
</style>
