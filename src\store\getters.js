const getters = {
    sidebar: state => state.app.sidebar,
    language: state => state.app.language,
    size: state => state.app.size,
    device: state => state.app.device,
    visitedViews: state => state.tagsView.visitedViews,
    cachedViews: state => state.tagsView.cachedViews,
    currentView: state => state.tagsView.currentView,
    token: state => state.user.token,
    avatar: state => state.user.avatar,
    name: state => state.user.name,
    db: state => state.sync.db,
    introduction: state => state.user.introduction,
    status: state => state.user.status,
    roles: state => state.user.roles,
    user: state => state.user.user,
    perms: state => state.user.perms,
    setting: state => state.user.setting,
    dict: state => state.sync.dict,
    permission_routers: state => state.permission.routers,
    addRouters: state => state.permission.addRouters,
    showRoutes: state => state.sidebar.showRoutes,
    currentRoute: state => state.sidebar.currentRoute,
    parentActive: state => state.sidebar.parentActive,
    childActive: state => state.sidebar.childActive,
    childItemActive: state => state.sidebar.childItemActive,
    isShowChild: state => state.sidebar.isShowChild,
    isFold: state => state.sidebar.isFold,
    isclear: state => state.user.isclear,
    // 数据字典
    dict_datas: state => state.dict.dictDatas,
    permissionMap: state => state.user.permissionMap,
    activesidebar: state => state.app.activesidebar,
}
export default getters
