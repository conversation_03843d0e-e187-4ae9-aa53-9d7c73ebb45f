import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import permission from './modules/permission'
import tagsView from './modules/tagsView'
import user from './modules/user'
import sync from './modules/sync'
import sidebar from './modules/sidebar'
import getters from './getters'
import dict from './modules/dict'
Vue.use(Vuex)

const store = new Vuex.Store({
    modules: {
        app,
        permission,
        tagsView,
        sync,
        user,
        sidebar,
        dict
    },
    getters
})

export default store
