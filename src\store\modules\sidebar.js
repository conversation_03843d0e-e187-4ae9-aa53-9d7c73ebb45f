import Cache from '@/utils/cache'

const sidebar = {
    state: {
        currentRoute: Cache.get('currentRoute') ? Cache.get('currentRoute') : {},
        parentActive: Cache.get('parentActive') ? Cache.get('parentActive') : '/',
        childActive: Cache.get('childActive') ? Cache.get('childActive') : '',
        childItemActive: Cache.get('childItemActive') ? Cache.get('childItemActive') : '',
        showRoutes: Cache.get('childActive') ? Cache.get('childActive') : [],
        isShowChild: Cache.get('isShowChild') ? Cache.get('isShowChild') : 0,
        isFold: true,
        activesidebar: false
    },
    mutations: {
        FORMAT_ROUTES(state, routes) {
            state.showRoutes = routes
            Cache.set('showRoutes', routes)
        },
        CHANGE_CURRENT_ROUTE(state, route) {
            state.currentRoute = { ...route }
            Cache.set('currentRoute', { ...route })
        },
        CHANGE_PARENT_ACTIVE(state, path) {
            state.parentActive = path
            Cache.set('parentActive', path)
        },
        CHANGE_CHILD_ACTIVE(state, path) {
            state.childActive = path
            Cache.set('childActive', path)
        },
        CHANGE_CHILD_ITEM_ACTIVE(state, path) {
            state.childItemActive = path
            Cache.set('childItemActive', path)
        },
        CHANGE_IS_SHOW_CHILD(state, flag) {
            state.isShowChild = flag
            Cache.set('isShowChild', flag)
        },

        CHANGE_IS_SHOW_FOLD(state) {
            state.isFold = !state.isFold
        },

    },
    actions: {
        formatRoutes({ commit, getters }) {
            const routes = getters.permission_routers.filter((item) => !item.hidden)
            commit('FORMAT_ROUTES', routes)
        },
        changeRoute({ commit, getters }, route) {
            commit('CHANGE_PARENT_ACTIVE', route.path)
            commit('CHANGE_CURRENT_ROUTE', route)
            if (!route.children || route.children[0].path === 'dashboard') {
                commit('CHANGE_IS_SHOW_CHILD', 0)
            } else {
                commit('CHANGE_IS_SHOW_CHILD', 1)
            }
        },
        changeChildRoute({ commit }, { parent, child }) {
            commit('CHANGE_CHILD_ACTIVE', parent)
            commit('CHANGE_CHILD_ITEM_ACTIVE', child || '')
        }
        // // 三级子路由
        // changeChildItemRoute({ commit }, { parent, child }) {
        //     // commit('CHANGE_CHILD_ACTIVE', parent)
        //     // commit('CHANGE_CHILD_ITEM_ACTIVE', child)
        //     // console.log(parent, child)
        // }

    }
}

export default sidebar
