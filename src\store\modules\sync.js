import { getDb } from '@/utils/db'
import { syncDict } from '@/api/system/dict'
import Cache from '@/utils/cache'

let db
const sync = {
    state: {
        syncInfo: []
    },
    mutations: {
        SET_DICT(state, dict) {
            const d = {}
            const e = {}
            const hashMap = {}
            dict.forEach(val => {
                d[val.type] = val.value
                e[val.type] = val.value.map(i => {
                    return {
                        text: i.name,
                        value: i.value
                    }
                })
                val.value.forEach(v => {
                    if (!hashMap[v.value]) {
                        hashMap[v.value] = v.name
                    }
                })
            })
            dict = null
            Cache.set('dictHashMap', hashMap)
            Cache.set('dict', d)
            Cache.set('filterList', e)
            window.filterList = e
            window.dict = d
        },
        SET_SYNC_INFO(state, collection) {
            state.syncInfo = collection.slice()
        }
    },
    actions: {
        // 同步
        async Sync({ commit }) {
            const user = this.state.user.user
            db = getDb(process.env.VUE_APP_APP_CODE + '_' + user.agentCode + '_' + user.userCode)
            await this.dispatch('SyncDict')
        },
        async SyncDict({ commit }) {
            const response = await syncDict()
            // console.log(response, 'SyncDict')
            await sync.actions.getData('dict', response)
            const dict = await db.dict.toArray()
            commit('SET_DICT', dict)

            await this.dispatch('SyncInfoHandler')
        },
        getData(type, result) {
            return new Promise(resolve => {
                const data = result['data']
                const rows = data
                if (rows.length > 0) {
                    rows.map(function (item) {
                        const content = item
                        try {
                            if (content['value'] && (content['value'].startsWith('{') || content['value'].startsWith('['))) {
                                content['value'] = JSON.parse(content['value'])
                            }
                        } catch (e) {
                            // console.log(e)
                        }
                        if (type === 'dict') {
                            db.table(type).get(content['id']).then(function (obj) {
                                if (obj) {
                                    delete content['id']
                                    db.table(type).update(obj['id'], content)
                                } else {
                                    db.table(type).add(content)
                                }
                            })
                        }
                    })
                }
                db.seq.get(type).then(function (obj) {
                    if (obj) {
                        db.table(type).count(function (count) {
                            db.seq.update(type, {
                                'count': count
                            }).then(function () {
                                resolve()
                            })
                        })
                    } else {
                        let name = ''
                        switch (type) {
                            case 'dict':
                                name = '系统字典'
                                break
                        }
                        db.seq.put({
                            'type': type,
                            'name': name,
                            'count': rows.length
                        }).then(function () {
                            resolve()
                        })
                    }
                })
            })
        },
        async SyncInfoHandler({ commit }) {
            const collection = await db.seq.toArray()
            commit('SET_SYNC_INFO', collection)
        }
    }
}

export default sync
