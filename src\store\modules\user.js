import { getUserInfo, loginByUsername } from '@/api/login'
import { getToken, setToken, removeToken, removeCerandCompCer, setIscl } from '@/utils/auth'
import Cache from '@/utils/cache'
import router from '@/router'

const user = {
    state: {
        user: '',
        status: '',
        code: '',
        token: getToken(),
        name: '',
        avatar: '',
        introduction: '',
        roles: [],
        perms: null,
        setting: {
            articlePlatform: []
        },
        isclear: false,
        permissionMap: {},
    },

    mutations: {
        SET_CODE: (state, code) => {
            state.code = code
        },
        SET_TOKEN: (state, token) => {
            state.token = token
        },
        SET_USER: (state, user) => {
            state.user = user
            Cache.set('user', user)
        },
        SET_PERMISSIONS: (state, permissions) => {
            state.permissions = permissions
            const map = {}
            if (permissions) {
                permissions.map(val => {
                    if (!map.hasOwnProperty(val)) {
                        map[val] = true
                    }
                })
            }
            state.permissionMap = map
        },
        SET_INTRODUCTION: (state, introduction) => {
            state.introduction = introduction
        },
        SET_SETTING: (state, setting) => {
            state.setting = setting
        },
        SET_STATUS: (state, status) => {
            state.status = status
        },
        SET_NAME: (state, name) => {
            state.name = name
            Cache.set('userName', name)
        },
        SET_AVATAR: (state, avatar) => {
            state.avatar = avatar
        },
        SET_ROLES: (state, roles) => {
            state.roles = roles
        },
        SET_PERMS: (state, perms) => {
            if (perms) {
                const map = {}
                perms.map(val => {
                    if (!map.hasOwnProperty(val)) {
                        map[val] = true
                    }
                })
                state.perms = map
            } else {
                state.perms = perms
            }
        }
    },

    actions: {
        setName({ commit }, name) {
            commit('SET_NAME', name)
        },
        setUser({ commit }, user) {
            commit('SET_USER', user)
        },
        /**
         * 用户登录时调用,用于加载当前用户的基本信息
         * @param userInfo:{username:string,password:string}
         * @returns {Promise<void>}
         */
        LoginByUsername({ commit }, userInfo) {
            const username = userInfo.username.trim()
            return new Promise((resolve, reject) => {
                loginByUsername(username, userInfo.password).then(response => {
                    const data = response.data
                    commit('SET_TOKEN', data.token)
                    setToken(data.token)
                    setIscl(false)
          resolve(response)
                }).catch(error => {
                    reject(error)
                })
            })
        },

        // 获取用户信息
        GetUserInfo({ commit, state }) {
            return new Promise((resolve, reject) => {
                // const USER_INFO = Cache.get('USER_INFO')
                const now = new Date().getTime()
                getUserInfo(state.token).then(response => {
                    const data = response.data
                    Cache.set('USER_INFO', {
                        user: data.user,
                        lastUpdate: now
                    })
                    // setToken(response.data.user.token)
                    commit('SET_USER', data.user)
                    commit('SET_TOKEN', data.user.token)
                    setToken(data.user.token)
                    commit('SET_PERMS', data.resources)
                    commit('SET_PERMISSIONS', data.resources)
                    resolve(data)
                }).catch(error => {
                    reject(error)
                })
            })
        },
        // 登出
        LogOut({ commit, state }) {
            return new Promise((resolve, reject) => {
                commit('SET_TOKEN', '')
                commit('SET_ROLES', [])
                commit('SET_PERMS', null)
                Cache.clear()
                // location.reload()
        resolve()
            })
        },

        // 前端 登出
        FedLogOut({ commit }) {
            return new Promise(resolve => {
                commit('SET_TOKEN', '')
                removeToken()
                removeCerandCompCer()
                resolve()
            })
        }
    }
}

export default user
