@import './variables.scss';

@mixin colorBtn($color) {
  background: $color;
  &:hover {
    color: $color;

    &:before,
    &:after {
      background: $color;
    }
  }
}

.blue-btn {
  @include colorBtn($blue);
}

.light-blue-btn {
  @include colorBtn($light-blue);
}

.red-btn {
  @include colorBtn($red);
}

.pink-btn {
  @include colorBtn($pink);
}

.green-btn {
  @include colorBtn($green);
}

.tiffany-btn {
  @include colorBtn($tiffany);
}

.yellow-btn {
  @include colorBtn($yellow);
}

.pan-btn {
  font-size: 14px;
  color: #fff;
  padding: 14px 36px;
  border-radius: 8px;
  border: none;
  outline: none;
  transition: 600ms ease all;
  position: relative;
  display: inline-block;

  &:hover {
    background: #fff;

    &:before,
    &:after {
      width: 100%;
      transition: 600ms ease all;
    }
  }

  &:before,
  &:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 2px;
    width: 0;
    transition: 400ms ease all;
  }

  &::after {
    right: inherit;
    top: inherit;
    left: 0;
    bottom: 0;
  }
}

.custom-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  color: #fff;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  padding: 10px 15px;
  font-size: 14px;
  border-radius: 4px;
}

.el-button--save {
  color: #fff;
  border-color: #595ec9;
  background-color: #595ec9;
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 8px;

  &:focus,
  &:hover {
    color: #fff;
    border-color: #595ec9;
    background-color: #595ec9;
  }
}

.el-button--cancel {
  color: #cecdcd;
  border-color: #dedede;
  background-color: #f9f9f9;
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 8px;

  &:focus,
  &:hover {
    color: #cecdcd;
    border-color: #dedede;
    background-color: #f9f9f9;
  }
}

@mixin factoryButtonColor($color, $bgc) {
  background: $bgc;
  color: $color;
  border: 1px solid $color;
  &:focus,
  &:hover {
    background: $bgc;
    color: $color;
    border: 1px solid $color;
  }
}

.el-button--search {
  // @include factoryButtonColor(#595ec9, #fff);
  @include factoryButtonColor(#2f79e8, #fff);
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  padding: 5px 8px;
  font-size: 14px;
}

.el-button--reset {
  font-size: 14px;
  color: #adadad;
  border-color: #adadad;
  background-color: #fff;
  padding: 5px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;

  &:focus,
  &:hover {
    color: #adadad;
    border-color: #adadad;
    background-color: #fff;
  }
}

.el-button--reset_production {
  font-size: 12px;
  color: #fff;
  border-color: #f8a20f;
  background-color: #f8a20f;
  border-radius: 5px;
  padding: 6px;
  width: 64px;

  &:focus,
  &:hover {
    color: #fff;
    border-color: #f8a20f;
    background-color: #f8a20f;
  }
}

.el-button--delete {
  font-size: 12px;
  // color: #adadad;
  // border-color: #efefef;
  // background-color: #efefef;

  color: #fff;
  border-color: #2f79e8;
  background-color: #2f79e8;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  padding: 6px;
  width: 64px;

  &:focus,
  &:hover {
    color: #adadad;
    border-color: #efefef;
    background-color: #efefef;
  }
}

.el-button--sendEmail {
  font-size: 12px;
  color: #ffffff;
  border-color: #f8a20f;
  background-color: #f8a20f;
  border-radius: 5px;
  padding: 6px;
  width: 64px;

  &:focus,
  &:hover {
    color: #ffffff;
    border-color: #f8a20f;
    background-color: #f8a20f;
  }
}

.el-button--btnSave {
  font-size: 12px;
  color: #ffffff;
  border-color: #595ec9;
  background-color: #595ec9;
  border-radius: 5px;
  padding: 6px;
  width: 64px;

  &:focus,
  &:hover {
    color: #ffffff;
    border-color: #595ec9;
    background-color: #595ec9;
  }
}

.el-button--selected {
  font-size: 12px;
  color: #ffffff;
  border-color: #f8a20f;
  background-color: #f8a20f;
  border-radius: 3px;
  padding: 6px;
  width: 50px;

  &:focus,
  &:hover {
    color: #ffffff;
    border-color: #f8a20f;
    background-color: #f8a20f;
  }
}

.el-button--add {
  border-radius: 4px !important;
  font-size: 12px;
  color: #ffffff;
  // border-color: #595ec9;
  // background-color: #595ec9;
  border-color: #2f79e8;
  background-color: #2f79e8;
  border-radius: 3px;
  padding: 6px;
  //width: 50px;
  &:focus,
  &:hover {
    color: #ffffff;
    border-color: #2f79e8;
    background-color: #2f79e8;
  }
}


.el-button.el-button--modal-save {
  color: #ffff;
  border-color: #256DDF;
  background-color: #256DDF;
  font-size: 12px;
  padding: 9px 5px;
  border-radius: 3px;
  min-width: 70px;

  &:focus,
  &:hover {
    color: #ffff;
    border-color: #256DDF;
    background-color: #256DDF;
  }
}

.el-button.el-button--modal-cancel {
  color: #686868;
  border-color: #f1f1f1;
  background-color: #f1f1f1;
  font-size: 12px;
  padding: 9px 5px;
  border-radius: 3px;
  min-width: 70px;

  &:focus,
  &:hover {
    color: #686868;
    border-color: #f1f1f1;
    background-color: #f1f1f1;
  }
}
