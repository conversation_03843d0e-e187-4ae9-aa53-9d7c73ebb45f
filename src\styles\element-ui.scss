//覆盖一些element-ui样式
$parmary-color: #2f79e8; //rgb(50, 65, 87);

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-image-viewer__wrapper {
  z-index: 999999 !important;
}


.el-tabs--card > .el-tabs__header .el-tabs__item.is-active:before {
  content: '';
  position: absolute;
  top: -1px;
  height: 2px;
  background: $parmary-color;
  left: 0;
  width: 100%;
  z-index: 1;
}
.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;
    .el-tag {
      margin-right: 0px;
    }
  }
}

//暂时性解决dialog 问题 https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  border-radius: 5px;
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

//文章页textarea修改样式
.article-textarea {
  textarea {
    padding-right: 40px;
    resize: none;
    border: none;
    border-radius: 0px;
    border-bottom: 1px solid #bfcbd9;
  }
}

//element ui upload
.upload-container {
  .el-upload {
    width: 100%;
    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

//dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}
.el-upload__input {
  display: none;
}
//2019年11月26日 14:05:25 dialog header 修改

.el-dialog__header {
  background: $parmary-color;
  padding: 15px;
}

.el-dialog__title {
  font-size: 16px;
  color: #ffffff;
}
.el-dialog__headerbtn .el-dialog__close,
.el-dialog__headerbtn:focus .el-dialog__close,
.el-dialog__headerbtn:hover .el-dialog__close {
  color: #ffffff;
}
//form修改
.el-form-item__label {
  font-size: 12px;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
}

.el-pagination {
  padding: 0;
}
.el-pagination__jump {
  margin-left: 15px;
}

.el-form--label-top .el-form-item__label {
  padding: 0;
  font-weight: 400;
  color: #646464;
}

.el-button--add:focus,
.el-button--add:hover {
  background: #fff;
  border-color: #33cad9;
  color: #33cad9;
}

.el-button--add {
  color: #fff;
  background-color: #33cad9;
  border-color: #33cad9;
}

.btn-custom-save {
  border: 1px solid #2f79e8;
  background: #fff;
  color: #2f79e8;
  &:hover {
    border: 1px solid #2f79e8;
    background: #fff;
    color: #2f79e8;
  }
}

.btn-custom-cancel {
  background: #fff !important;
  border: 1px solid #d5d5d5 !important;
  color: #747474 !important;
  &:hover {
    background: #fff;
    border: 1px solid #d5d5d5;
    color: #747474;
  }
}

.background {
  span {
    background-color: #c6cdeb;
    border-radius: 50%;
    color: #eee;
    opacity: 0.9;
    text-align: center;
    &:hover {
      opacity: 1;
      color: #000;
    }
  }
}
