@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './iconfont.scss';

::-webkit-scrollbar {
  width: 10px;
  height: 9px;
  border-radius: 15px;
}

::-webkit-scrollbar-track-piece {
  background-color: transparent;
  border-radius: 15px;
}

::-webkit-scrollbar-thumb:vertical {
  height: 5px;
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 15px;
}

::-webkit-scrollbar-thumb:horizontal {
  width: 30px;
  background-color: rgba(222, 223, 240, 1);
  border-radius: 15px;
}

::-webkit-scrollbar-thumb:horizontal:hover {
  cursor: pointer;
  background-color: rgb(222, 223, 242, 0.5);
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0;
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  font-size: 12px;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

.img-list {
  display: flex;
  flex-direction: row;

  .img-item {
    width: 80px;
    height: 80px;
    margin-left: 20px;
  }
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

code {
  background: #eef1f6;
  padding: 15px 16px;
  margin-bottom: 20px;
  display: block;
  line-height: 36px;
  font-size: 15px;
  font-family: 'Source Sans Pro', 'Helvetica Neue', Arial, sans-serif;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

.warn-content {
  background: rgba(66, 185, 131, 0.1);
  border-radius: 2px;
  padding: 16px;
  padding: 1rem;
  line-height: 1.6rem;
  word-spacing: 0.05rem;

  a {
    color: #42b983;
    font-weight: 600;
  }
}

//main-container全局样式
.app-container {
  //padding: 10px 20px;
  margin: 0 10px 0 10px;
  //background: #fff;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 8px;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
                  90deg,
                  rgba(32, 182, 249, 1) 0%,
                  rgba(32, 182, 249, 1) 0%,
                  rgba(33, 120, 241, 1) 100%,
                  rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.left-flex {
  border: 1px solid #d0d0d0;
  flex: 1;
  padding-bottom: 5px;
  width: 502px;
}

.content-title {
  padding: 0 10px 0 18px;
  height: 35px;
  line-height: 35px;
  background: #f8f8f8;
  position: relative;
  margin: 0 0 5px 0;

  &:before {
    content: ' ';
    display: block;
    position: absolute;
    width: 4px;
    height: 15px;
    left: 10px;
    top: 10px;
    background: $parmary-color;
  }

  button {
    margin-top: 2px;
    margin-left: 5px !important;
  }

  .el-input {
    width: 120px;
  }
}

.right-form {
  flex: 1;
  border: 1px solid #d0d0d0;
  padding-bottom: 5px;
  overflow: auto;
}

.mid-tools {
  width: 48px;
  text-align: center;
  padding-top: 180px;

  button {
    display: block;
    margin: 5px auto;
  }
}

.form-edit-content {
  min-height: 486px;
  position: relative;
  padding: 0;
  //border-top: 1px dashed #e6e6e6;
  display: flex;

  .left-form {
    border: 1px solid #d0d0d0;
    float: left;
    width: 312px;
    padding-bottom: 5px;

    h3 {
      .el-button--mini {
        margin-top: 5px;
      }
    }

    .el-textarea__inner,
    .el-input {
      width: 180px;
    }
  }

  .left-flex {
    border: 1px solid #d0d0d0;
    flex: 1;
    padding-bottom: 5px;
    width: 476px;
  }

  .content {
    padding: 0 5px;
  }

  .right-form {
    flex: 1;
    border: 1px solid #d0d0d0;
    padding-bottom: 5px;
    overflow: auto;
  }

  .mid-tools {
    width: 48px;
    text-align: center;
    padding-top: 180px;

    button {
      display: block;
      margin: 5px auto;
    }
  }

  .count {
    margin-top: 10px;

    .el-input {
      width: 181px;
      margin-left: 10px;
    }
  }
}

.margin-left-10 {
  margin-left: 10px;
}

.el-input.is-disabled .el-input__inner {
  color: #2f79e8;
}

.el-button--primary {
  background-color: #2f79e8;
  border-color: #2f79e8;
}

.el-step__head.is-finish {
  border: none;
}

.el-step__title {
  font-size: 12px;
  line-height: 20px;
}

.el-step__title.is-finish {
  color: #2f79e8;
}

.el-step__description.is-finish {
  color: #2f79e8;
}

.el-step__icon-inner {
  color: #2f79e8;
}

.vue-treeselect__control {
  height: 26px !important;
  line-height: 26px;
  font-size: 13px !important;

  .vue-treeselect--single .vue-treeselect__input {
    height: 26px !important;
    line-height: 26px !important;
  }
}
