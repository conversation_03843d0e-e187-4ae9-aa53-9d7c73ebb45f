// 项目变量


// zjmj
$project-theme: #2f79e8;
$sidebar-select-color: #2F79E8;

// nav头部配置
$nav-item-min-width: 6.25rem;
$nav-height: 4.8rem;
$nav-background: $project-theme;
$nav-color: #B5BDDF;
$nav-color-active: #ffffff;

// sidebar配置
$sidebar-width: 14rem;
$sidebar-background: #fff;
$sidebar-background-active: #000B39;
$sidebar-color: #636363;
$sidebar-color-active: #ffffff;
$sidebar-width-collapse: 80px;


// scss变量可以导出使用到js中
:export {
  projectTheme: $project-theme;
  navHeight: $nav-height;
  navBackground: $nav-background;
  navColor: $nav-color;
  navColorActive: $nav-color-active;
  sidebarWidth: $sidebar-width;
  sidebarBackground: $sidebar-background;
  sidebarColor: $sidebar-color;
  sidebarColorActive: $sidebar-color-active;
}
