::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}
::v-deep .el-input__prefix {
  right: 5px;
  left: auto;
}
::v-deep .el-dialog {
  // width: 950px;
  width: 90%;
}
::v-deep .el-input--prefix .el-input__inner {
  padding-left: 10px;
}
::v-deep .el-table__fixed-right::before {
  width: 0;
}
::v-deep .el-table__footer-wrapper tbody td {
  border: none !important;
}
.dialog {
  width: 980px;
  height: 740px;
}
::v-deep .el-dialog__footer {
  padding: 0px 0px 0px;
}
::v-deep .dialog-footer-all {
  color: #adadad;
  background: #adadad;
  border: 1px solid #adadad;
}
::v-deep .el-input.is-disabled .el-input__inner {
  color: #606266;
}
.dialog-footer {
  margin-top: 40px;
  padding: 10px;
  display: flex;
  justify-content: flex-end;
  // box-shadow: -3px 0 3px #ccc;
  // box-shadow: 0 -3px 3px #ccc;
  // box-shadow: 3px 3px 3px 3px #ccc;
  border-top: solid 1px #f1f1f2;
  box-sizing: border-box;
  padding: 10px 44px 10px 44px;
  &-all {
    margin-right: auto;
    font-size: 14px;
    margin-left: 34px;
    flex: 0 0 58px;
    color: #ff9639;
    border: 1px solid #ff9639;
    &:hover {
      background: transparent;
    }
  }
  &-btns {
    width: 85px;
    height: 35px;
    // border: 1px solid #2f79e8;
    background: #fff;
    // color: #2f79e8;
    margin-left: 20px;
    font-size: 14px;
    &:last-of-type {
      border: 1px solid #d5d5d5;
      color: #747474;
    }
  }
}
.dialog-footer {
  .el-button--default {
    border: 1px solid #adadad;
    color: #adadad;
  }

  .el-button--primary {
    border: 1px solid #2f79e8;
    color: #2f79e8;
  }
  ::v-deep .el-input--mini .el-input__inner {
    height: 38.5px;
    line-height: 29.5px;
  }
}

.form-layout {
  padding: 0 20px;
}

.form-title {
  font-size: 16px;
  position: relative;
  // padding: 15px 18px;
  padding: 10px 18px;
  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    // bottom: 17px;
    bottom: 12px;
    background: #2f79e8;
    height: 16px;
  }
}
::v-deep .el-input--mini .el-input__inner {
  height: 29.5px;
  line-height: 29.5px;
}
.shenpi {
  .el-input--mini .el-input__inner {
    height: 38.5px;
    line-height: 29.5px;
  }
}
.opt-btn {
  cursor: pointer;
  margin: 0 3px;
  color: #fff;
  border: none;
}
::v-deep .el-form--label-top .el-form-item__label {
  padding: 0;
  font-weight: 400;
  font-size: 13px;
  color: #646464;
}
::v-deep .el-form-item__label {
  font-weight: 0 !important;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 13px;
}
.name {
  // color: blue;
  text-decoration: underline;
  cursor: pointer;
  transition: 0.5s all;
  &:hover,
  :focus {
    color: #3234d3;
  }
}

.excessive {
  width: 100%;
  color: red;
}
