.el-dropdown-menu--medium .el-dropdown-menu__item {
  line-height: 28px;
  padding: 0 30px;
  font-size: 12px;
}

#app {
  // 主体区域
  .main-container {
    min-height: 100%;
    transition: margin-left 0.28s;
    // margin-left: 10.5rem;
    margin-left: 10.5rem;
    position: relative;
    background: rgb(245, 245, 245);
  }

  // 侧边栏
  .sidebar-container {
    transition: width 0.28s;
    // width: 120px;
    height: 100%;
    position: fixed;
    //font-size: 0;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    //reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    //.is-active {
    //  background-color: #33CAD9;
    //}
    .user-info {
      text-align: center;
      width: 70px;
      height: 102px;
      display: flex;
      align-items: center;
      margin: 0 auto 25px;

      .logo {
        width: 100%;
        margin: 5px auto;
      }

      .user {
        padding-left: 20px;
        margin-bottom: 10px;
        font-size: 12px;
        color: white;
        display: flex;
        align-items: flex-start;
        flex-direction: column;

        .company {
          margin-bottom: 4px;
          font-weight: 600;
        }

        .el-dropdown {
          color: #8095a8;
        }
      }
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;

      .el-scrollbar__view {
        height: 100%;
      }
    }

    .el-scrollbar__bar.is-vertical {
      right: 0;
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    .el-submenu {
      position: relative;
    }

    .el-menu-item,
    .el-submenu__title {
      font-size: 12px;
      height: 52px;
      line-height: 52px;
    }

    .el-menu-item {
      display: flex;
      flex-flow: column;
      align-items: center;
      justify-content: center;
      color: rgb(255, 255, 255);
      font-size: 12px;
      height: 80px;
      line-height: 20px;
    }

    .el-menu-item.is-active {
      background-color: #33cad9 !important;

      .el-submenu__title {
        color: #ffffff !important;
      }

      i {
        font-size: 30px;
      }
    }

    .el-submenu.is-opened {
      .el-submenu__title {
        line-height: 25px;
        background-color: $subMenuBg !important;
      }

      &:before {
        content: ' ';
        display: block;
        position: absolute;
        width: 4px;
        height: 100%;
        left: 0;
        top: 0;
        background-color: #0092dc;
        z-index: 2;
      }
    }
  }

  .el-menu {
  }

  //.hideSidebar {
  //  .sidebar-container {
  //    width: 52px !important;
  //
  //    .user-info {
  //      .logo {
  //        height: 24px;
  //        margin: 10px auto;
  //      }
  //
  //      .user {
  //        display: none !important;
  //      }
  //    }
  //  }
  //
  //  .main-container {
  //    margin-left: 52px;
  //  }
  //
  //  .el-menu-item, .el-submenu__title {
  //
  //    text-align: center;
  //    font-size: 16px;
  //    padding: 0 10px !important;
  //
  //    .fa {
  //      margin: 0;
  //    }
  //  }
  //
  //  .submenu-title-noDropdown {
  //    padding-left: 10px !important;
  //    position: relative;
  //
  //    .el-tooltip {
  //      padding: 0 10px !important;
  //    }
  //  }
  //
  //  .el-submenu {
  //    overflow: hidden;
  //
  //    & > .el-submenu__title {
  //
  //      padding-left: 10px !important;
  //
  //      .el-submenu__icon-arrow {
  //        display: none;
  //      }
  //    }
  //  }
  //
  //  .el-menu--collapse {
  //    .el-submenu {
  //      & > .el-submenu__title {
  //        & > span {
  //          height: 0;
  //          width: 0;
  //          overflow: hidden;
  //          visibility: hidden;
  //          display: inline-block;
  //        }
  //      }
  //    }
  //  }
  //}

  .sidebar-container .nest-menu .el-submenu > .el-submenu__title,
  .sidebar-container .el-submenu .el-menu-item {
    min-width: 180px !important;
    background-color: $subMenuBg !important;

    &:hover {
      background-color: $menuHover !important;
      color: #ffffff !important;
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: 90px !important;
  }
  .el-menu--collapse {
    width: 45px !important;
    min-width: 45px !important;
  }
  ul {
    margin-block-start: 0em;
    margin-block-end: 0em;
    margin-inline-start: 0px;
    padding-inline-start: 0px;
  }
  //适配移动端
  .mobile {
    .main-container {
      margin-left: 0;
    }

    .sidebar-container {
      transition: transform 0.28s;
      width: 180px !important;
    }

    &.hideSidebar {
      .sidebar-container {
        transition-duration: 0.3s;
        transform: translate3d(-180px, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }
}
