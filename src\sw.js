self.__precacheManifest = [
    'css/font-awesome.min.css',
    'fonts/FontAwesome.otf',
    'fonts/fontawesome-webfont.eot',
    'fonts/fontawesome-webfont.svg',
    'fonts/fontawesome-webfont.ttf',
    'fonts/fontawesome-webfont.woff',
    'fonts/fontawesome-webfont.woff2',
    'js/cos-js-sdk-v5.min.js',
    // 'js/echarts.min.js',
    'js/xlsx.full.min.js',
    'js/pdfmake.min.js',
    'js/region.min.js'
    // {
    //   url: '/index.html'
    // }
].concat(self.__precacheManifest || []);

// workbox.setConfig({ debug: true })
workbox.skipWaiting();
workbox.clientsClaim();

workbox.precaching.suppressWarnings();
workbox.precaching.precacheAndRoute(self.__precacheManifest, {});


workbox.routing.registerRoute(
    /\.(?:js|css)$/,
    workbox.strategies.staleWhileRevalidate({
        cacheName: 'static-resources'
    }),
);

workbox.routing.registerRoute(
    /\.(?:png|gif|jpg|jpeg|svg)$/,
    workbox.strategies.cacheFirst({
        cacheName: 'images',
        plugins: [
            new workbox.expiration.Plugin({
                maxEntries: 60,
                maxAgeSeconds: 24 * 60 * 60
            }),
        ],
    }),
);

//缓存字体图标
workbox.routing.registerRoute(
    /.*(?:bootcss)\.com.*$/,
    workbox.strategies.cacheFirst({
        cacheName: 'font-awesome',
        plugins: [
            new workbox.expiration.Plugin({
                maxEntries: 60,
                maxAgeSeconds: 365 * 24 * 60 * 60, // 365 Days
            }),
            new workbox.cacheableResponse.Plugin({
                statuses: [0, 200],
            }),
        ],
    }),
);

const postMessagePlugin = {
    cacheDidUpdate: async ({cacheName, url, oldResponse, newResponse}) => {
        if (oldResponse && (oldResponse.headers.get('App-Ver') !== newResponse.headers.get('App-Ver'))) {
            const clients = await self.clients.matchAll();
            for (const client of clients) {
                client.postMessage({url, cacheName});
            }
        }
    },
};
//缓存网络请求 当断网的时候
// workbox.routing.registerRoute(
//   /.*\/a\/.*$/,
//   workbox.strategies.networkFirst({
//     networkTimeoutSeconds: 10,
//     cacheName: "api-cache",
//     plugins: [
//       new workbox.expiration.Plugin({
//         "maxEntries": 5,
//         "maxAgeSeconds": 60,
//         "purgeOnQuotaError": false
//       }),
//       new workbox.cacheableResponse.Plugin({
//         "statuses": [0, 200],
//         "headers": {"from-cache": "true"}
//       }),
//       new workbox.broadcastUpdate.Plugin("update-channel"),
//       postMessagePlugin
//     ]
//   }), 'GET');
