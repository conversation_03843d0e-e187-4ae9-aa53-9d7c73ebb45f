// import Cookies from 'js-cookie'

const TokenKey = 'SYS_TOKEN'

export function getToken() {
  return localStorage.getItem(TokenKey)
}

export function isLogined() {
  // const userInfo = JSON.parse(localStorage.getItem('userInfo')) || {}
  return getToken()
}

export function setToken(token) {
  return localStorage.setItem(TokenKey, token)
}

export function removeToken() {
  return localStorage.removeItem(TokenKey)
}

export function getAccessToken() {
  return getToken()
}

export function removeCerandCompCer() {
  return localStorage.removeItem('certificateMap') &
    localStorage.removeItem('certificateList') &
    localStorage.removeItem('companyCertificateMap') &
    localStorage.removeItem('companyCertificateList')
}

export function getIscl() {
  return localStorage.getItem('isclear')
}

export function setIscl(flag) {
  return localStorage.setItem('isclear', flag)
}
