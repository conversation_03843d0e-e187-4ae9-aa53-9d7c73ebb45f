import Decimal from 'decimal.js'
import {isDef, isNumber} from '@/utils/is'

class CalcUtils {
    /**
     * 加
     */
    static add(a, b) {
        if (!isDef(a) || !isDef(b)) {
            return undefined
        }
        return new Decimal(a).plus(b).toNumber()
    }

    /**
     * 减
     */
    static subtract(a, b) {
        if (!isDef(a) || !isDef(b)) {
            return undefined
        }
        return new Decimal(a).minus(b).toNumber()
    }

    /**
     * 乘
     */
    static multiply(a, b) {
        if (!isDef(a) || !isDef(b)) {
            return undefined
        }
        return new Decimal(a).times(b).toNumber()
    }

    /**
     * 除
     */
    static divide(a, b) {
        if (!isDef(a) || !isDef(b)) {
            return undefined
        }
        return new Decimal(a).div(b).toNumber()
    }

    /**
     * 计算合计
     */
    static calculateSum(values) {
        if (!Array.isArray(values) || values.length === 0) {
            return undefined
        }
        const sum = values.reduce((acc, cur) => {
            if (isNumber(cur)) return acc.plus(cur)
            return acc
        }, new Decimal(0))

        return sum.toNumber()
    }

    /**
     * 计算平均值 需要过滤掉非数字的值
     */
    static calculateAverageDecimal(values) {
        if (!Array.isArray(values) || values.length === 0) {
            return undefined
        }

        values = values.filter(isNumber)

        if (values.length === 0) {
            return undefined
        }

        const sum = new Decimal(CalcUtils.calculateSum(values))
        return sum.div(values.length).toNumber()
    }

    /**
     * 计算煤焦比
     * @desc 煤焦比 1/（1-0.01*Vdaf*(1-0.01*Ad)+0.025）
     */
    static calcCokeToCoalRatio(vdaf, ad) {
        if (!isNumber(vdaf) || !isNumber(ad)) {
            return undefined
        }
        // 将输入参数转换为 Decimal 对象
        vdaf = new Decimal(vdaf)
        ad = new Decimal(ad)
        // 执行精确的计算

        const numerator = new Decimal(1)
        const denominator = new Decimal(1).minus(vdaf.times(0.01).times(new Decimal(1).minus(ad.times(0.01)))).plus(0.025)

        // Calculate the ratio as a Decimal object
        const ratio = numerator.div(denominator)
        return ratio.toString()
    }

    /**
     * 计算焦炭灰分
     * @desc 焦炭灰分=煤灰分Ad*煤焦比
     */
    static calculateAshContent(ad, cokeToCoalRatio) {
        if (!isNumber(ad) || !isNumber(cokeToCoalRatio)) {
            return undefined
        }
        // 将输入参数转换为 Decimal 对象
        ad = new Decimal(ad)
        cokeToCoalRatio = new Decimal(cokeToCoalRatio)
        // 执行精确的计算
        const ashContent = ad.times(cokeToCoalRatio)
        return ashContent.toString() // 将结果转换为字符串，如果需要显示
    }

    /**
     * 如果输入含税煤价
     * @desc 1、如果输入含税煤价：不含税到厂煤价= 含税煤价/1.13+不含税运费+含税煤价/1.13×路耗
     */
    static calculateTaxPrice(price, freight, dissipation) {
        if (!isNumber(price) || !isNumber(freight) || !isNumber(dissipation)) {
            return undefined
        }
        // 将输入参数转换为 Decimal 对象
        price = new Decimal(price).div(1.13) //
        freight = new Decimal(freight)
        dissipation = new Decimal(dissipation).times(0.01)
        // 执行精确的计算
        const factoryPrice = price.plus(freight).plus(price.times(dissipation))
        return factoryPrice.toString() // 将结果转换为字符串，如果需要显示
    }

    /**
     * 如输入不含税煤价
     * @desc 不含税到厂价=不含税煤价+运费+不含税煤价×路
     */
    static calculateNoTaxPrice(price, freight, dissipation) {
        if (!isNumber(price) || !isNumber(freight) || !isNumber(dissipation)) {
            return undefined
        }
        // 将输入参数转换为 Decimal 对象
        price = new Decimal(price)
        freight = new Decimal(freight)
        dissipation = new Decimal(dissipation).times(0.01)
        // 执行精确的计算
        const factoryPrice = price.plus(freight).plus(price.times(dissipation))
        return factoryPrice.toString() // 将结果转换为字符串，如果需要显示
    }


    /**
     * 折水不含税进厂价
     * 不含税到厂煤价/(1-水分%）×（1-折水标准%）
     */

    static calculateRemoveWaterNoTaxPrice(mt, water, noTaxCoalPrice) {
        if (!isNumber(noTaxCoalPrice) || !isNumber(mt) || !isNumber(water)) {
            return undefined
        }
        mt = new Decimal(mt).times(0.01)
        water = new Decimal(water).times(0.01)
        // 将输入参数转换为 Decimal 对象
        noTaxCoalPrice = new Decimal(noTaxCoalPrice)
        // 执行精确的计算
        const factoryPrice = noTaxCoalPrice.div(new Decimal(1).minus(mt)).times(new Decimal(1).minus(water))
        return factoryPrice.toString() // 将结果转换为字符串，如果需要显示
    }


    /**
     * 不含税煤价
     * @desc 不含税煤价=含税煤价/1.13  ｜ 不保留小数舍5进1
     */
    static calculateCoalPriceWithoutTax(coalPriceWithTax, n = 1) {
        if (!isNumber(coalPriceWithTax)) {
            return undefined
        }
        // 将输入参数转换为 Decimal 对象
        coalPriceWithTax = new Decimal(coalPriceWithTax)
        // 执行精确的计算
        const coalPriceWithoutTax = coalPriceWithTax.div(1.13).times(n)

        return coalPriceWithoutTax.toNumber() // 将结果转换为字符串，如果需要显示
    }


    /**
     * 路耗
     * @desc 路耗=不含税煤价*1%
     */
    static calculateRoadLoss(coalPriceWithoutTax) {
        if (!isNumber(coalPriceWithoutTax)) {
            return undefined
        }
        // 将输入参数转换为 Decimal 对象
        coalPriceWithoutTax = new Decimal(coalPriceWithoutTax)
        // 执行精确的计算
        const roadLoss = coalPriceWithoutTax.times(0.01)
        // .toDecimalPlaces(2, Decimal.ROUND_HALF_UP)
        return roadLoss.toNumber() // 将结果转换为字符串，如果需要显示
    }

    /**
     * 不含税进厂价
     * @desc 不含税进厂价=不含税煤价+不含税运费+路耗
     */
    static calculateFactoryPriceWithoutTax(coalPriceWithoutTax, freightWithoutTax, roadLoss) {
        if (!isNumber(coalPriceWithoutTax) || !isNumber(freightWithoutTax) || !isNumber(roadLoss)) {
            return undefined
        }

        // 将输入参数转换为 Decimal 对象
        coalPriceWithoutTax = new Decimal(coalPriceWithoutTax)
        freightWithoutTax = new Decimal(freightWithoutTax)
        roadLoss = new Decimal(roadLoss)
        // 执行精确的计算
        const factoryPriceWithoutTax = coalPriceWithoutTax.plus(freightWithoutTax).plus(roadLoss)
        // .toDecimalPlaces(1, Decimal.ROUND_HALF_UP)
        return factoryPriceWithoutTax.toNumber() // 将结果转换为字符串，如果需要显示
    }

    /**
     * 进厂干基成本
     * @desc 进厂干基成本=不含税进厂价/（1-水分%）
     */
    static calculateDryBasisCost(factoryPrice, moisturePercentage) {
        if (!isNumber(factoryPrice) || !isNumber(moisturePercentage)) {
            return undefined
        }
        // 将输入参数转换为 Decimal 对象
        factoryPrice = new Decimal(factoryPrice)
        moisturePercentage = new Decimal(moisturePercentage)
        // 执行精确的计算
        const denominator = new Decimal(1).minus(moisturePercentage.div(100))
        const dryBasisCost = factoryPrice.div(denominator)
        // .toDecimalPlaces(1, Decimal.ROUND_HALF_UP)
        return dryBasisCost.toNumber() // 将结果转换为字符串，如果需要显示
    }

    /**
     *
     */
    static getShowValue(value, decimalPlaces = 0, rounding = 'ROUND_HALF_UP', suffix = '') {
        if ([undefined, null, ''].includes(value)) {
            return undefined
        }
        return new Decimal(Number(value))
            .toDecimalPlaces(decimalPlaces, Decimal[rounding])
            .toFixed(decimalPlaces)
            .toString() + suffix
    }

    // 焦炭方法

    /**
     * 计算成焦率 1-0.01*Vdaf*(1-0.01*Ad)+0.025
     */
    static calculateCoalRate(vdaf, ad) {
        if (!isNumber(vdaf) || !isNumber(ad)) {
            return undefined
        }
        // 将输入参数转换为 Decimal 对象
        vdaf = new Decimal(vdaf)
        ad = new Decimal(ad)
        // 执行精确的计算
        const term1 = new Decimal(1)
        const term2 = new Decimal(0.01).times(vdaf)
        const term3 = new Decimal(1).minus(new Decimal(0.01).times(ad))
        const term4 = term2.times(term3)
        const term5 = new Decimal(0.025)
        const result = term1.minus(term4).plus(term5)
        return result.toNumber()
    }

    /**
     * 计算焦炭煤焦比 1/成焦率
     */
    static calculateCokeCoalRatio(coalRate) {
        if (!isNumber(coalRate) || coalRate === 0) {
            return undefined
        }
        // 将输入参数转换为 Decimal 对象
        coalRate = new Decimal(coalRate)
        // 执行精确的计算
        const ratio = new Decimal(1).div(coalRate)
        return ratio.toNumber()
    }

    /**
     * 计算焦炭成本 入炉煤成本*煤焦比
     */

    static calculateCokeCost(coalCost, cokeCoalRatio) {
        if (!isNumber(coalCost) || !isNumber(cokeCoalRatio)) {
            return undefined
        }
        // 将输入参数转换为 Decimal 对象
        coalCost = new Decimal(coalCost)
        cokeCoalRatio = new Decimal(cokeCoalRatio)
        // 执行精确的计算
        const result = coalCost.times(cokeCoalRatio)
        return result.toNumber()
    }

    /**
     * 计算焦炭灰分 煤焦比*入炉煤灰分+0.2
     */
    static calculateCokeAd(cokeCoalRatio, ad) {
        if (!isNumber(cokeCoalRatio) || !isNumber(ad)) {
            return undefined
        }
        // 将输入参数转换为 Decimal 对象
        cokeCoalRatio = new Decimal(cokeCoalRatio)
        ad = new Decimal(ad)
        // 执行精确的计算
        const term1 = cokeCoalRatio.times(ad)
        const term2 = new Decimal(0.2)
        const result = term1.plus(term2)
        return result.toNumber()
    }

    /**
     * 计算焦炭全硫 全硫*硫转化率
     */
    static calculateCokeStd(std, stdRate) {
        if (!isNumber(std) || !isNumber(stdRate)) {
            return undefined
        }
        std = new Decimal(std)
        stdRate = new Decimal(stdRate)
        const result = std.times(stdRate)
        return result.toNumber()
    }

    /**
     * 计算焦炭固定碳 100-灰分-1.4
     */
    static calculateCokeFixedCarbon(ad) {
        if (!isNumber(ad)) {
            return undefined
        }
        const result = new Decimal(100).minus(ad).minus(1.4)
        return result.toNumber()
    }

    /**
     * 计算焦炭M40 M40=-0.5*Vdaf+0.175*G+0.15*Y+82.3
     */
    static calculateCokeM40(vdaf, g, y) {
        if (!isNumber(vdaf) || !isNumber(g) || !isNumber(y)) {
            return undefined
        }
        vdaf = new Decimal(vdaf)
        g = new Decimal(g)
        y = new Decimal(y)
        const term1 = new Decimal(-0.5).times(vdaf)
        const term2 = new Decimal(0.175).times(g)
        const term3 = new Decimal(0.15).times(y)
        const term4 = new Decimal(82.3)
        const result = term1.plus(term2).plus(term3).plus(term4)
        return result.toNumber()
    }

    /**
     * 计算焦炭M10 M10=0.05*Vdaf-0.0175*G-0.06*Y+7.365
     */
    static calculateCokeM10(vdaf, g, y) {
        if (!isNumber(vdaf) || !isNumber(g) || !isNumber(y)) {
            return undefined
        }
        vdaf = new Decimal(vdaf)
        g = new Decimal(g)
        y = new Decimal(y)
        const term1 = new Decimal(0.05).times(vdaf)
        const term2 = new Decimal(-0.0175).times(g)
        const term3 = new Decimal(-0.06).times(y)
        const term4 = new Decimal(7.365)
        const result = term1.plus(term2).plus(term3).plus(term4)
        return result.toNumber()
    }

    /**
     * 计算焦炭M25 M25=97.5-M10
     */

    static calculateCokeM25(m10) {
        if (!isNumber(m10)) {
            return undefined
        }
        m10 = new Decimal(m10)
        const result = new Decimal(97.5).minus(m10)
        return result.toNumber()
    }

    /**
     * 计算焦炭CRI 92 -CSR
     */
    static calculateCokeCRI(crs) {
        if (!isNumber(crs)) {
            return undefined
        }
        crs = new Decimal(crs)
        const result = new Decimal(92).minus(crs)
        return result.toNumber()
    }

    /**
     * 计算焦炭CSR ＣＳＲ＝-0.825×Vdaf+0.15×G+0.3×Y+75
     */
    static calculateCokeCSR(vdaf, g, y) {
        if (!isNumber(vdaf) || !isNumber(g) || !isNumber(y)) {
            return undefined
        }
        vdaf = new Decimal(vdaf)
        g = new Decimal(g)
        y = new Decimal(y)
        const term1 = new Decimal(-0.825).times(vdaf)
        const term2 = new Decimal(0.15).times(g)
        const term3 = new Decimal(0.3).times(y)
        const term4 = new Decimal(75)
        const result = term1.plus(term2).plus(term3).plus(term4)
        return result.toNumber()
    }

    /**
     * 计算平均粒径 =(C18*90+D18*70+E18*50+F18*32.5+G18*12.5)/100
     *
     */
    static calculateCokeAverageParticleSize(c18, d18, e18, f18, g18) {
        if (!isNumber(c18) || !isNumber(d18) || !isNumber(e18) || !isNumber(f18) || !isNumber(g18)) {
            return undefined
        }
        c18 = new Decimal(c18)
        d18 = new Decimal(d18)
        e18 = new Decimal(e18)
        f18 = new Decimal(f18)
        g18 = new Decimal(g18)
        const term1 = c18.times(90)
        const term2 = d18.times(70)
        const term3 = e18.times(50)
        const term4 = f18.times(32.5)
        const term5 = g18.times(12.5)
        const term6 = new Decimal(100)
        const result = term1.plus(term2).plus(term3).plus(term4).plus(term5).div(term6)
        return result.toNumber()
    }

    /**
     * 计算焦炭水分
     */
    static getCokeMt() {
        return 0.3
    }

    /**
     * 计算挥发分Vdaf
     */
    static getCokeVdaf() {
        return '<1.5'
    }
}

// console.log(CalcUtils.calculateCokeAverageParticleSize(3.8, 11.1, 43.3, 39.6, 2.2), '平均粒径')

export default CalcUtils
