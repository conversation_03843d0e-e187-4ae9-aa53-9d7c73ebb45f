import moment from 'moment'

const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss'
const DATE_TIME_RANGE = {
  0: 'YYYY-MM-DD',
  1: 'YYYY-MM-DD'
}

const DATE_TIME_RANGE_TYPE = {
  0: 'startOf',
  1: 'endOf'
}
const DATE_FORMAT = 'YYYY-MM-DD '

export function getDay(date = undefined) {
  return moment(date).format('YYYY-MM-DD')
}

export function getDayRange(date = undefined, type = 'YYYY-MM-DD') {
  return [moment(date).startOf('day').format(type), moment(date).endOf('day').format(type)]
}

export function formatToDateTime(date = undefined, format = DATE_TIME_FORMAT) {
  return moment(date).format(format)
}

export function formatToDateRange(dateRange = [new Date(), new Date()], typeOf) {
  return dateRange.map((date, idx) => {
    if (!typeOf) {
      return formatToDateTime(date, DATE_TIME_RANGE[idx])
    } else {
      return dateUtil(date)[DATE_TIME_RANGE_TYPE[idx]](typeOf).format(DATE_TIME_RANGE[idx])
    }
  })
}

export function formatToDateRangeSubtract({ dateRange = [new Date(), new Date()], num = 3, typeOf }) {
  return [moment().subtract(num, typeOf).startOf(typeOf).format(DATE_TIME_RANGE[0]), moment().endOf(typeOf).format(DATE_TIME_RANGE[1])]
}

export function formatToDate(date = undefined, format = DATE_FORMAT) {
  return moment(date).format(format)
}

export const dateUtil = moment

/**
 * 将毫秒，转换成时间字符串。例如说，xx 分钟
 *
 * @param ms 毫秒
 * @returns {string} 字符串
 */

/**
 * 将毫秒，转换成时间字符串。例如说，xx 分钟
 *
 * @param ms 毫秒
 * @returns {string} 字符串
 */
export function getDate(date =  new Date(), format = 'YYYY-MM-DD') {
  return date ? moment(date).format(format) : undefined
}

