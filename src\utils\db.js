import Dexie from 'dexie'

let db
const DRAWING_ITEMS = 'drawingItems'
const DRAWING_ITEMS_VERSION = '1.2'
const DRAWING_ITEMS_VERSION_KEY = 'DRAWING_ITEMS_VERSION'
const DRAWING_ID = 'idGlobal'
const TREE_NODE_ID = 'treeNodeId'
const FORM_CONF = 'formConf'
/**
 * 獲取数据库对象
 * @param dbName
 * @returns {Dexie}
 */
export function getDb(dbName) {
  const db_ = new Dexie(dbName)
  db_.version(1).stores({
    dict: '&id,type',
    printTemplate: '&id',
    config: '&id,code',
    id: '&id,code',
    seq: '&type',
    template: '&name',
    supPrinttemplate: '&name'
  })

  db_.open().catch(function (error) {
    alert('Uh oh : ' + error)
  })
  window.db = db_
  db = db_
  return db_
}

/**
 * 根据字典 获取值
 * @param name
 */
export async function getDict(name) {
  let values = []
  try {
    const item = await db.dict.where('type').equals(name).first()
    values = item['value']
  } catch (e) {
    console.warn(e)
  }
  return values
}

/**
 * 获取模板
 * @param table
 * @param name
 * @returns {Promise<string>}
 */
export async function getItemByKey(table, name) {
  let template = ''
  try {
    const item = await db.table(table).where('name').equals(name).first()
    if (item) {
      template = JSON.parse(item.content)
    }
  } catch (e) {
    console.warn(e)
  }
  return template
}

/**
 *
 * @param table
 * @param name
 * @param content
 */
export function setItemByKey(table, name, content) {
  try {
    content = JSON.stringify(content)
    db.table(table).put({
      name,
      content
    })
  } catch (e) {
    console.warn(e)
  }
}




// 以下是碧水复制
export function getDrawingList() {
  // 加入缓存版本的概念，保证缓存数据与程序匹配
  const version = localStorage.getItem(DRAWING_ITEMS_VERSION_KEY)
  if (version !== DRAWING_ITEMS_VERSION) {
    localStorage.setItem(DRAWING_ITEMS_VERSION_KEY, DRAWING_ITEMS_VERSION)
    saveDrawingList([])
    return null
  }

  const str = localStorage.getItem(DRAWING_ITEMS)
  if (str) return JSON.parse(str)
  return null
}
export function getFormConf() {
  const str = localStorage.getItem(FORM_CONF)
  if (str) return JSON.parse(str)
  return null
}

export function getIdGlobal() {
  const str = localStorage.getItem(DRAWING_ID)
  if (str) return parseInt(str, 10)
  return 100
}
export function getTreeNodeId() {
  const str = localStorage.getItem(TREE_NODE_ID)
  if (str) return parseInt(str, 10)
  return 100
}
export function saveDrawingList(list) {
  localStorage.setItem(DRAWING_ITEMS, JSON.stringify(list))
}
export function saveIdGlobal(id) {
  localStorage.setItem(DRAWING_ID, `${id}`)
}

export function saveTreeNodeId(id) {
  localStorage.setItem(TREE_NODE_ID, `${id}`)
}
export function saveFormConf(obj) {
  localStorage.setItem(FORM_CONF, JSON.stringify(obj))
}