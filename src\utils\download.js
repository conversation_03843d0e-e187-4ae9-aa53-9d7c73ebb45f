export default {
  // 下载 Excel 方法
  excel(data, fileName) {
    this.download0(data, fileName, 'application/vnd.ms-excel')
  },

  // 下载 Word 方法
  word(data, fileName) {
    this.download0(data, fileName, 'application/msword')
  },

  // 下载 Zip 方法
  zip(data, fileName) {
    this.download0(data, fileName, 'application/zip')
  },

  // 下载 Html 方法
  html(data, fileName) {
    this.download0(data, fileName, 'text/html')
  },

  // 下载 Markdown 方法
  markdown(data, fileName) {
    this.download0(data, fileName, 'text/markdown')
  },

  download0(data, fileName, mineType) {
    // 创建 blob
    let blob = new Blob([data], { type: mineType })
    // 创建 href 超链接，点击进行下载
    window.URL = window.URL || window.webkitURL
    let href = URL.createObjectURL(blob)
    let downA = document.createElement('a')
    downA.href = href
    downA.download = fileName
    downA.click()
    // 销毁超连接
    window.URL.revokeObjectURL(href)
  },

  // 传递src，下载
  downloadBySrc(href) {
    window.URL = window.URL || window.webkitURL
    let downA = document.createElement('a')
    downA.href = href
    downA.download = href
    downA.click()
    // 销毁超连接
    window.URL.revokeObjectURL(href)
  },
  downloadExcel(data) {
    let blob = new Blob([data])
    let downloadElement = document.createElement('a')
    let href = window.URL.createObjectURL(blob) // 创建下载的链接
    downloadElement.href = href
    downloadElement.download = '用户数据.xlsx' // 下载后文件名
    document.body.appendChild(downloadElement)
    downloadElement.click() // 点击下载
    document.body.removeChild(downloadElement) // 下载完成移除元素
    window.URL.revokeObjectURL(href) // 释放掉blob对象
  }
}
