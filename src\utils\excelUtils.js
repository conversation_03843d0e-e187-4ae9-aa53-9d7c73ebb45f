import {saveAs} from 'file-saver'
import {Workbook} from 'exceljs'

import {getDictDataLabel} from '@/utils/dict'
import {DATE_PREFIX, DICT_PREFIX} from '@/const'
import {dateUtil, formatToDateTime} from '@/utils/dateUtils'
import {isFunction, isString} from '@/utils/is'
import {removeTreeWithoutKey} from '@/utils/index'

const DEFAULT_ROW_HEIGHT = 30
const DEFAULT_COLUMN_WIDTH = 40
const DEFAULT_COLUMN_TEXT_MAX = 15
const NO_EXPORT_KEY = 'noExport'

export default Workbook

/**
 * @desc 导出excel
 * @param data 表格数据
 * @param columns 表头 tips:label不能重复不然合并有问题
 * @param sheetName  sheet名称
 * @param fileName  导出文件名称
 * @param addHeaderStyleFunc 表头样式Func
 * @returns {Workbook}
 */
export function createWorkbook(
  {
    data,
    columns,
    sheetName = '',
    fileName = '',
    addHeaderStyleFunc,
    multiHeader = 1
  }) {
  const workbook = new Workbook()
  const worksheet = workbook.addWorksheet(sheetName)
  worksheet.properties.defaultRowHeight = DEFAULT_ROW_HEIGHT
  const {headers, nameLists, headerKeys} = generateHeaders(columns, multiHeader)
  handleHeader({worksheet, headers, nameLists, addHeaderStyleFunc})
  addDataToWorksheet({worksheet, headerKeys, headers, data, columns})
  saveWorkbook(workbook, `${fileName}-${formatToDateTime(new Date())}.xlsx`)
  return workbook
}

/**
 * 表格转换excel导出数据
 */
export function transExportList(list, cols) {
  // 策略模式
  const policyModeMap = {
    [DICT_PREFIX]: (type, value) => {
      return getDictDataLabel(type, value)
    },
    [DATE_PREFIX]: (type, value) => {
      return dateUtil(value).format(type)
    }
  }
  return list?.map((item) => {
    cols.forEach((col) => {
      const {prop, format} = col
      // if (item[prop]) {
      if (isString(format)) {
        const [policyType, type] = format.split('|')
        item[prop] = policyModeMap[policyType + '|'](type, item[prop])
      }
      if (isFunction(format)) {
        item[prop] = format({
          value: item[prop],
          row: item,
          col,
          isExport: true
        })
        // }
      }
    })
    return item
  })
}

/**
 * 删除不需要导出的列
 */
export function removeNoExportCols(cols) {
  removeTreeWithoutKey(cols, NO_EXPORT_KEY)
}

export function saveWorkbook(workbook, fileName) {
  // 导出文件
  workbook.xlsx.writeBuffer().then((data) => {
    const blob = new Blob([data], {type: ''})
    saveAs(blob, fileName)
  })
}

/**
 * 生成excel需要字段的表头
 */
export function generateHeaders(sourceColumns, multiHeader) {
  const headerKeys = []

  function _deepCreateHeaders(columns, parentKey, parentKeyName) {
    return columns?.map((col) => {
      const obj = {
        parentKey,
        parentKeyName,
        header: col.label,
        key: col.prop
      }

      if (col.children && col.children.length) {
        obj.children = _deepCreateHeaders(col.children, col.prop, col.label)
      } else {
        headerKeys.push(col.prop)
      }
      return obj
    })
  }

  const headers = _deepCreateHeaders(sourceColumns)

  /**
   * 收集表头名称 先只支持三级表头
   */
  const collectNames = (headers, key = 'header') => {
    const names1 = []
    const names2 = []
    const names3 = []
    for (const header of headers) {
      if (header.children && header.children.length) {
        for (const child of header.children) {
          if (child.children && child.children.length) {
            for (const child1 of child.children) {
              names1.push(header[key])
              names2.push(child[key])
              names3.push(child1[key])
            }
          } else {
            names1.push(header[key])
            names2.push(child[key])
            names3.push(child[key])
          }
        }
      } else {
        names1.push(header[key])
        names2.push(header[key])
        names3.push(header[key])
      }
    }
    return [names1, names2, names3]
  }

  return {headers, headerKeys, nameLists: [...collectNames(headers)].slice(0, multiHeader)}
}

const getCellFontStyle = (col = {}) => {
  return {
    bold: col.bold || false,
    size: col.fontSize || 12,
    // 使用微软雅黑字体
    name: 'Microsoft YaHei UI'
  }
}

/**
 * 处理真实表头
 */
export function handleHeader({
                               worksheet,
                               headers,
                               nameLists,
                               addHeaderStyleFunc,
                               isMergeHeader = true
                             }) {
  function addHeaderStyle(row, attr, index) {
    const {fontSize, horizontal, bold, height} = attr || {}
    row.height = height || DEFAULT_ROW_HEIGHT

    row.eachCell((cell, colNumber) => {
      cell.fill = {
        // 填充
        type: 'pattern',
        pattern: 'solid',
        fgColor: {argb: 'FFFFFF'},
        bgColor: {argb: 'FFFFFF'}
      }
      cell.border = {
        top: {style: 'thin'},
        left: {style: 'thin'},
        bottom: {style: 'thin'},
        right: {style: 'thin'}
      }
      cell.font = {
        ...getCellFontStyle({
          fontSize,
          bold: false
        })
      }
      cell.alignment = {
        vertical: 'middle',
        wrapText: true,
        horizontal: horizontal || 'center'
      }
    })
  }

  const rowHeaderGroupList = nameLists.map((names, index) => {
    const rowHeader = worksheet.addRow(names)
    addHeaderStyleFunc ? addHeaderStyleFunc(rowHeader, {}, index) : addHeaderStyle(rowHeader, {}, index)
    return rowHeader
  })

  isMergeHeader && mergeColumnCell(headers, rowHeaderGroupList, nameLists, worksheet)
}

/**
 * 处理数据
 */
export function addDataToWorksheet({worksheet, headerKeys, headers, data, columns}) {
  const colTextMap = new Map()

  data.forEach((item) => {
    const rowData = headerKeys?.map((key, index) => {
      return item[key]
    })
    const row = worksheet.addRow(rowData)

    let rowTextMaxLen = DEFAULT_COLUMN_TEXT_MAX
    row.eachCell((cell, colNumber) => {
      const getLen = () => {
        // 可以扩展自定义的长度
        return cell.value ? cell.value.toString().length : 0
      }
      const len = getLen()

      if (len > rowTextMaxLen) {
        rowTextMaxLen = len
      }

      colTextMap.set(colNumber, {
        width: rowTextMaxLen //* 1.2
      })

      rowTextMaxLen = DEFAULT_COLUMN_TEXT_MAX
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: {argb: 'FFFFFF'},
        bgColor: {argb: 'FFFFFF'}
      }
      cell.border = {
        top: {style: 'thin'},
        left: {style: 'thin'},
        bottom: {style: 'thin'},
        right: {style: 'thin'}
      }
      cell.alignment = {
        vertical: 'middle',
        horizontal: 'center',
        wrapText: true,
        shrinkToFit: false
      }
      cell.font = {
        ...getCellFontStyle()
      }
    })
  })

  /**
   * cell 列宽
   */
  worksheet.columns = worksheet.columns.map((col, idx) => {
    const colNumber = idx + 1
    return {
      ...col,
      width: colTextMap.get(colNumber)?.width || DEFAULT_COLUMN_WIDTH
    }
  })
}

export function mergeColumnCell(headers, rowHeaderGroupList, nameLists, worksheet) {
  function mergeColumnCellNormal(rowHeader1, rowHeader2, nameRow1, nameRow2, cb) {
    // 当前 index 的指针
    let pointer = -1
    nameRow1.forEach((name, index) => {
      // 当 index 小于指针时，说明这一列已经被合并过了，不能再合并
      if (index <= pointer) return
      if (cb) {
        const isReturn = cb(name, index)
        if (isReturn) return
      }
      // 是否应该列合并
      const shouldVerticalMerge = name === nameRow2?.[index]
      // 是否应该行合并
      const shouldHorizontalMerge = index !== nameRow1.lastIndexOf(name)
      pointer = nameRow1.lastIndexOf(name)
      if (shouldVerticalMerge && shouldHorizontalMerge) {
        // 两个方向都合并
        worksheet.mergeCells(Number(rowHeader1.number), index + 1, Number(rowHeader2.number), nameRow1.lastIndexOf(name) + 1)
      } else if (shouldVerticalMerge && !shouldHorizontalMerge) {
        // 只在垂直方向上同一列的两行合并
        worksheet.mergeCells(Number(rowHeader1.number), index + 1, Number(rowHeader2.number), index + 1)
      } else if (!shouldVerticalMerge && shouldHorizontalMerge) {
        // 只有水平方向同一行的多列合并
        worksheet.mergeCells(Number(rowHeader1.number), index + 1, Number(rowHeader1.number), nameRow1.lastIndexOf(name) + 1)

        // const cell = rowHeader1.getCell(index + 1)
        // cell.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true }
      }
    })
  }

  function mergeColumnCellThree(headers, rowHeaderGroupList, nameLists, worksheet) {
    const [rowHeader1, rowHeader2, rowHeader3] = rowHeaderGroupList
    const [nameRow1, nameRow2, nameRow3] = nameLists
    const pointers = Array(nameRow1.length).fill(-1)
    let cols = []
    nameRow1.forEach((name, index) => {
      if (index <= pointers[0]) return
      const shouldVerticalMerge = name === nameRow2[index] && name === nameRow3[index]
      const shouldHorizontalMerge =
        index !== nameRow1.lastIndexOf(name) || index !== nameRow2.lastIndexOf(name) || index !== nameRow3.lastIndexOf(name)
      pointers[0] = nameRow1.lastIndexOf(name)
      pointers[1] = nameRow2.lastIndexOf(name)
      pointers[2] = nameRow3.lastIndexOf(name)
      if (shouldVerticalMerge && shouldHorizontalMerge) {
        cols.push(nameRow2[index])
        worksheet.mergeCells(Number(rowHeader1.number), index + 1, Number(rowHeader3.number), pointers[2] + 1)
      } else if (shouldVerticalMerge && !shouldHorizontalMerge) {
        cols.push(nameRow2[index])
        worksheet.mergeCells(Number(rowHeader1.number), index + 1, Number(rowHeader3.number), index + 1)
      } else if (!shouldVerticalMerge && shouldHorizontalMerge) {
        worksheet.mergeCells(Number(rowHeader1.number), index + 1, Number(rowHeader1.number), pointers[0] + 1)
      }
    })

    mergeColumnCellNormal(rowHeaderGroupList[1], rowHeaderGroupList[2], nameLists[1], nameLists[2], (name, index) => {
      return cols.includes(name)
    })
  }

  if (rowHeaderGroupList.length === 3) {
    mergeColumnCellThree(headers, rowHeaderGroupList, nameLists, worksheet)
  } else {
    mergeColumnCellNormal(rowHeaderGroupList[0], rowHeaderGroupList[1], nameLists[0], nameLists[1])
  }
}
