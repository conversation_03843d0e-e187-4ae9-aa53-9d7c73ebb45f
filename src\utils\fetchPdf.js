import axios from 'axios'
import qs from 'qs'
import {Message} from 'element-ui'
import store from '@/store'
import {getToken} from '@/utils/auth'
// import Func from '@/utils/func'
// import NProgress from 'nprogress' // Progress 进度条
// import 'nprogress/nprogress.css' // Progress 进度条样式
import md5 from 'md5'
import {isBlob} from 'ali-oss/lib/common/utils/isBlob'

/**
 * 根据key值排序对象
 * @param unordered
 */
function ksort(unordered) {
  if (unordered) {
    const ordered = {}
    Object.keys(unordered).sort().forEach(function (key) {
      ordered[key] = unordered[key]
    })
    return ordered
  }
  return ''
}

// 创建axios实例
const service = axios.create({
  // baseURL: process.env.VUE_APP_CENTER, // api的base_url
  timeout: 30000, // 请求超时时间
  responseType: 'blob'
})

// request拦截器
service.interceptors.request.use(config => {
  // NProgress.start()
  // Do something before request is sent
  // if (store.getters.token) {
  config.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'
  config.headers['Access-Control-Allow-Origin'] = '*'
  config.headers['Access-Control-Allow-Credentials'] = true
  config.headers['App-Key'] = process.env.VUE_APP_APP_KEY
  config.headers['App-Secret'] = process.env.VUE_APP_APP_SECRET
  config.headers['App-Ver'] = process.env.VUE_APP_APP_VER
  config.headers['timestamp'] = new Date().getTime()
  const token = getToken()
  if (token) {
    config.headers['Authorization'] = token
  }
  // 签名请求 防止重复提交
  // 规则 url+ ksort(params) => md5
  let sign = ''
  if (config.method === 'post' ||
      config.method === 'put' ||
      config.method === 'delete') {
    // if (config.method === 'post') {
    //   config.headers['Content-Type'] = 'application/json;charset=UTF-8'
    // }
    // 序列化
    if (config.data) {
      sign = md5(config.url + JSON.stringify(ksort(config.data)))
      // config.data = qs.stringify(config.data)
      config.data = qs.stringify(config.data, {arrayFormat: 'indices', allowDots: true})
      // config.data.appCode = process.env.VUE_APP_APP_CODE
      // if (store.state.user.user) {
      //   config.data.tenantCode = store.state.user.user['tenantCode']
      // }
    }
  } else {
    if (config.params) {
      sign = md5(config.url + JSON.stringify(ksort(config.params)))
    }
  }
  config.headers['App-Sign'] = sign !== '' ? sign : md5(config.url)
  config.baseURL = process.env.VUE_APP_CENTER
  if (config.method === 'get') {
    config.paramsSerializer = function (params) {
      return qs.stringify(params, {arrayFormat: 'comma'})
    }
  }
  return config
}, error => {
  Message({
    dangerouslyUseHTMLString: true,
    message: '<h2>异常信息:' + error.message + '</h2>',
    type: 'error',
    showClose: true,
    duration: 10000
  })
  Promise.reject(error)
})

service.interceptors.response.use(function (response) {
  // 对响应数据做点什么
  // NProgress.done()
  return response
}, function (error) {
  if (error.response) {
    if (error.response.status === 401 && location.href.indexOf('/login') === -1) {
      Message({
        message: '请重新登录',
        type: 'error',
        duration: 5 * 1000
      })
      store.dispatch('LogOut').then(() => {
        location.reload()// 为了重新实例化vue-router对象 避免bug
      })
    }
    if (error.response.status === 404) {
      Message({
        message: '记录不存在',
        type: 'error',
        duration: 3 * 1000
      })
      return false
    }
    if (error.response['data']) {
      if (isBlob(error.response['data'])) {
        let reader = new FileReader() // 创建读取文件对象
        reader.addEventListener('loadend', function () {
          let res = JSON.parse(reader.result) // 返回的数据
          // console.log(res, '返回结果数据')
          if (res.code === 400) {
            Message({
              dangerouslyUseHTMLString: true,
              message: '<h2>[' + res['code'] + ']:' + res['message'] + '</h2>',
              type: 'error',
              showClose: true,
              duration: 100000
            })
            return false
          }
        })
        reader.readAsText(error.response.data, 'utf-8')
      } else {
        Message({
          dangerouslyUseHTMLString: true,
          message: '<h2>[' + error.response['status'] + ']:' + error.response.data['message'] + '</h2>',
          // message: '<h2>[' + error.response['status'] + ']:' + error.response.data['size'] + '</h2>',
          type: 'error',
          showClose: true,
          duration: 100000
        })
        return false
      }
    }
  } else {
    Message({
      message: error['message'] === 'Network Error' ? '网络错误,请检测您的网络' : error['message'],
      type: 'error',
      duration: 5 * 1000,
      showClose: true
    })
    return false
  }
  return Promise.reject(error)
})

export default service

// import axios from 'axios'
// import {MessageBox, Notification} from 'element-ui'
// import store from '@/store'
// import {getToken} from '@/utils/auth'
// import md5 from 'md5'
//
// function buildSign(postData, secret) {
//     if (typeof postData === 'string') {
//         throw new Error('postData类型必须json对象')
//     }
//     const paramNames = Object.keys(postData)
//     paramNames.sort()
//
//     const paramNameValue = []
//
//     let i = 0
//     const len = paramNames.length
//     for (; i < len; i++) {
//         const paramName = paramNames[i]
//         paramNameValue.push(paramName)
//         paramNameValue.push(postData[paramName])
//     }
//     const source = secret + paramNameValue.join('') + secret
//     return md5(source).toUpperCase()
// }
//
// // create an axios instance
// const service = axios.create({
//     // baseURL: process.env.VUE_APP_BASE_API, // api 的 base_url
//     timeout: 15000 // request timeout
// })
// // request interceptor
// service.interceptors.request.use(config => {
//         config.headers['Content-Type'] = 'application/json;charset=UTF-8'
//         if (store.getters.token) {
//             config.headers['Authorization'] = 'Bearer ' + getToken()
//         }
//         config.data = {
//             name: config.data.name,
//             version: '',
//             app_key: 'test',
//             data: config.data.data ? encodeURIComponent(JSON.stringify(config.data.data)) : '',
//             format: 'json',
//             timestamp: (new Date()).Format('yyyy-MM-dd HH:mm:ss')
//         }
//         config.data.sign = buildSign(config.data, process.env.VUE_APP_SECRET)
//         config.baseURL = process.env.VUE_APP_BASE_API
//         return config
//     },
//     error => {
//         // Do something with request error
//         console.log(error) // for debug
//         return Promise.reject(error)
//     }
// )
//
// // response interceptor
// service.interceptors.response.use(
//     response => {
//         const res = response.data
//         if (res.code === '21') {
//             MessageBox.alert('系统未登录，请重新登录', '错误', {
//                 confirmButtonText: '确定',
//                 type: 'error'
//             }).then(() => {
//                 store.dispatch('FedLogOut').then(() => {
//                     location.reload()
//                 })
//             })
//             return Promise.reject('error')
//         }
//         if (res.code !== '0') {
//             Notification({
//                 message: res.msg || '系统错误' + ' 异常代码：' + res.code,
//                 type: 'error',
//                 duration: 5 * 1000
//             })
//             return Promise.reject(res)
//         } else {
//             return res.data
//         }
//     }, error => {
//         Notification({
//             message: error + '（请联系系统管理员）',
//             type: 'error',
//             duration: 5 * 1000
//         })
//         return Promise.reject(error)
//     })
//
// export default service
