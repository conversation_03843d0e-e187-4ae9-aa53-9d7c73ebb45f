/* eslint-disable no-cond-assign,eqeqeq */
// import JsBarcode from 'jsbarcode'
import store from '@/store'
import trans from '@/api/font'
import { Message } from 'element-ui'
import Cache from '@/utils/cache'
// import * as OSS from 'ali-oss'

const COS = window.COS

const pdfMake = window.pdfMake || {}
pdfMake.fonts = {
    'webfont': {
        normal: 'webfont',
        bold: 'webfont',
        italics: 'webfont',
        bolditalics: 'webfont'
    }
}

/**
 * 获取屏幕DPI
 * @returns {Array}
 */
function getDPI() {
    let arrDPI = []
    if (window.screen.deviceXDPI !== undefined) {
        arrDPI[0] = window.screen.deviceXDPI
        arrDPI[1] = window.screen.deviceYDPI
    } else {
        const tmpNode = document.createElement('DIV')
        tmpNode.style.cssText = 'width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden;'
        document.body.appendChild(tmpNode)
        arrDPI[0] = parseInt(tmpNode.offsetWidth)
        arrDPI[1] = parseInt(tmpNode.offsetHeight)
        tmpNode.parentNode.removeChild(tmpNode)
    }
    return arrDPI[0] || 96
}

window.dpi = getDPI()

const Func = {
    // 获取字典文字
    getDictText(value, type) {
        if (!value || !type) {
            return '未知'
        }
        const dict = Cache.get('dict') || {}
        if (dict[type]) {
            let text = '未知'
            dict[type].forEach(v => {
                if (v.value === value) {
                    text = v.name
                }
            })
            return text
        } else {
            return '未知'
        }
    },
    // 获取字典文字
    getDictTextByWords(value, type) {
        // console.log(value, type)
        if (!value || !type) {
            return '未知'
        }
        const dict = Cache.get('dict') || {}
        if (dict[type]) {
            let text = '未知'
            dict[type].forEach(v => {
                if (v.name.indexOf(value) >= 0) {
                    text = v.value
                }
            })
            return text
        } else {
            return '未知'
        }
    },
    // uploadFile(file, fileName, cb) {
    //     const {bucket, endpoint, region, sid, skey} = JSON.parse(process.env.VUE_APP_OSS)
    //     fileName = 'tpt-agent/' + fileName
    //     const client = new OSS({
    //         region: region,
    //         accessKeyId: sid,
    //         accessKeySecret: skey,
    //         bucket: bucket
    //     })
    //
    //     client.put(fileName, file).then((res) => {
    //         if (res) {
    //             if (cb) {
    //                 cb(null, {key: endpoint + '/tpt-agent/' + res.name})
    //             }
    //         }
    //     })
    // },
    uploadFileCOS(fileName, file, callback) {
        const Key = 'tp-upload/' + fileName
        const { bucket, endpoint, region, sid, skey } = {
            appid: '1302505442', // Bucket 所属的项目 ID
            bucket: 'tp-upload-1302505442', // 空间名称 Bucket
            region: 'ap-nanjing',
            endpoint: 'https://tp-upload-1302505442.cos.ap-nanjing.myqcloud.com',
            sid: 'AKIDFO8cqq3nTHq9C2gFaknaBWcZS4QgpSM0', // 项目的 SecretID
            skey: 'Z8xlQqXy8B0e2A3wYv4yg7KIuGLJ1Yry' // 项目的 Secret Key
        }
        const cos = new COS({
            getAuthorization(options, callback) {
                // 用于判断当前请求是否合法,返回值是计算得到的鉴权凭证字符串
                const authorization = window.COS.getAuthorization({
                    SecretId: sid,
                    SecretKey: skey,
                    Method: options.Method,
                    Key: options.Key
                })
                callback(authorization)
            }
        })
        cos.putObject(
            {
                Bucket: bucket,
                Region: region,
                Key,
                Body: file
            }, (err) => {
                if (!err) {
                    callback(null, { url: `${endpoint}/${Key}` })
                }
            })
    },
    deleteEntityById: function ({ context, impl, id, loading = 'entityFormLoading' }, cb) {
        context.$confirm('删除后不可恢复, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            context[loading] = true
            impl(id).then(() => {
                context[loading] = false
                context.$message({
                    showClose: true,
                    message: '删除成功',
                    type: 'success'
                })
                if (cb) {
                    cb()
                } else {
                    context.getList()
                }
            }, err => {
                console.log(err)
                context[loading] = false
            })
        }).catch(() => {
            context[loading] = false
            context.$message({
                type: 'info',
                message: '已取消删除'
            })
        })
    },
    getList: function ({ context, impl }) {
        context.listLoading = true
        impl(context.listQuery).then(response => {
            context.listLoading = false
            context.list = response.data.records
            context.total = response.data.total
        }, err => {
            context.listLoading = false
            console.log(err)
        })
    },
    async fetch(impl, query) {
        let res
        try {
            res = await impl(query)
        } catch (e) {
            console.log(e)
        }
        return res
    },
    excelDateToJSDate(serial) {
        var utc_days = Math.floor(serial - 25569)
        var utc_value = utc_days * 86400
        var date_info = new Date(utc_value * 1000)
        var fractional_day = serial - Math.floor(serial) + 0.0000001
        var total_seconds = Math.floor(86400 * fractional_day)
        var seconds = total_seconds % 60
        total_seconds -= seconds
        var hours = Math.floor(total_seconds / (60 * 60))
        var minutes = Math.floor(total_seconds / 60) % 60
        return new Date(date_info.getFullYear(), date_info.getMonth(), date_info.getDate(), hours, minutes, seconds)
    },
    /**
     * 获取时间选择器的快捷组合时间
     * @returns {*}
     */
    getPickerOptions() {
        return {
            disabledDate(time) {
                return time.getTime() > Date.now() || time.getTime() < Date.now() - 3600 * 1000 * 24 * 90
            },
            shortcuts: [
                {
                    text: '最近三日',
                    onClick(picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
                        picker.$emit('pick', [start, end])
                    }
                },
                {
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 29)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        picker.$emit('pick', [start, end])
                    }
                }]
        }
    },
    getBase64(content) {
        let rv = ''
        rv = window.atob(content)
        rv = escape(rv)
        rv = decodeURIComponent(rv)
        return rv
    },
    guid() {
        function s4() {
            return Math.floor((1 + Math.random()) * 0x10000)
                .toString(16)
                .substring(1)
        }

        return s4() + s4() + '-' + s4() + '-' + s4() + '-' +
            s4() + '-' + s4() + s4() + s4()
    },
    /**
     * 毫米转像素
     * @param value
     * @returns {number}
     */
    mmConvertPx(value) {
        return Number((value * window.dpi / 25.4).toFixed(2))
    },
    mmConvertM8Px(value) {
        return Number((value * 200 / 25.4).toFixed(2))
    },
    pxConvertLodopmm(value) {
        return Number((value * 25.4 / 188).toFixed(2))
    },
    getM8TrueWidthPx(value) {
        return Func.mmConvertM8Px(value - 7)
    },
    /**
     * 像素转毫米
     * @param value
     * @returns {number}
     */
    pxConvertMm(value) {
        return Number((value * 25.4 / window.dpi).toFixed(2))
    },
    /**
     * 字符串去重
     * @param str
     * @returns {string}
     */
    uniqueString(str) {
        let helper = {}
        let res = ''
        let i = 0
        let sub
        for (; sub = str.charAt(i); i++) {
            if (helper[sub]) {
                helper[sub] = ++helper[sub]
                continue
            }
            helper[sub] = 1
            res += sub
        }
        return res
    },
    getBaseUrl(url) {
        let baseURL
        baseURL = process.env.VUE_APP_CENTER
        return baseURL
    },
    getPrintSetting() {
        // 迁移老配置
        const pickUpPrintConfig = Cache.get('pickUpPrintConfig')
        if (pickUpPrintConfig) {
            Cache.set('printConfig', pickUpPrintConfig)
            Cache.remove('pickUpPrintConfig')
        }
        const set = Cache.get('printConfig') || store.state.sync.config['PRINT']
        if (!set) {
            Message.error('暂无打印配置,请清除缓存,重新刷新页面')
            return false
        }
        return set
    },
    getTransPrintConfig() {
        const set = store.state.sync.config['PRINT_TRANSPORT']
        if (!set) {
            Message.error('暂无打印配置,请先同步数据')
            return false
        }
        return set
    },
    /**
     * 根据类型获取打印模板
     * @param type
     */
    getPrintTemplate(type) {
        let code = ''
        const printSetting = Func.getPrintSetting()
        if (!printSetting) {
            return false
        }
        switch (type) {
            case 'WAYBILL':
                code = printSetting.PCWaybillTemplateCode
                break
            case 'LABEL':
                // code = printSetting.labelTemplateCode
                code = printSetting.PCLabelTemplateCode
                break
            case 'RECEIPT':
                // code = printSetting.receiptTemplateCode
                code = printSetting.PCReceiptTemplateCode
                break
            case 'PICKUP':
                // code = printSetting.pickupTemplateCode
                code = printSetting.PCPickupTemplateCode
                break
        }
        return code ? store.state.sync.printTemplateMap[code] : ''
    },
    /**
     * 打印
     * @param data 需要打印的数据
     * @param type 类型 WAYBILL LABEL RECEIPT PICKUP PC_WAYBILL PC_PICKUP
     * @param start
     * @param end
     * @returns {boolean}
     */
    print(data, type, start = 1, end = 1) {
        this.commandPrint(data, type, start, end)
        // 废弃使用pdf打印
        // if (end < start) {
        //     Message.error('开始位置不能大于结束位置')
        //     return false
        // }
        // console.log(type)
        // const template = Func.getPrintTemplate(type)
        // if (!template) {
        //     Message.error('未找到模板')
        //     return false
        // }
        // let setting
        // try {
        //     setting = JSON.parse(template.options)
        //     setting.isM8 = !type.startsWith('PC')
        //     const s = this.parseTpl(data, template.content)
        //     if (!s) {
        //         Message.error('解析模板出错')
        //         return false
        //     }
        //     let ss = []
        //     switch (type) {
        //         case 'PC_WAYBILL':
        //         case 'WAYBILL':
        //             const waybillCount = Func.getPrintSetting().waybillCount || 1
        //             for (let i = 0; i < waybillCount; i++) {
        //                 let content = this.buildPdfContent(JSON.parse(s), type)
        //                 if (i < waybillCount - 1) {
        //                     this.pageBreak(content)
        //                 }
        //                 ss = ss.concat(content)
        //             }
        //             break
        //         case 'PC_PICKUP':
        //         case 'PICKUP':
        //             ss = ss.push(this.buildPdfContent(JSON.parse(s), type))
        //             break
        //         case 'LABEL':
        //         case 'RECEIPT':
        //             let content = Func.buildLabel(s, type, start, end)
        //             content[content.length - 1].pageBreak = ''
        //             ss = ss.concat(content)
        //             break
        //     }
        //     // console.table(ss)
        //     this.getTrans(ss, setting)
        // } catch (e) {
        //     console.error(e)
        // }
    },
    /**
     * 批量打印
     * @param list
     * @param type
     * @returns {boolean}
     */
    batchPrint(list = [], type) {
        list.forEach(val => {
            let end = 1
            switch (type) {
                case 'LABEL':
                    end = +val.cargoNumber
                    break
                case 'RECEIPT':
                    end = +val.receiptNumber
                    break
            }
            //  let end = type === 'LABEL' ? +val.cargoNumber : +val.cargoNumber
            Func.commandPrint(val, type, 1, end)
        })
        // 废弃pdf打印
        // const template = Func.getPrintTemplate(type)
        // if (!template) {
        //     Message.error('未找到模板')
        //     return false
        // }
        // let setting
        // try {
        //     setting = JSON.parse(template.options)
        //     setting.isM8 = !type.startsWith('PC')
        //     let s = []
        //     list.forEach(v => {
        //         s.push(this.parseTpl(v, template.content))
        //     })
        //     if (!s.length) {
        //         Message.error('解析模板出错')
        //         return false
        //     }
        //     let result = []
        //     switch (type) {
        //         case 'PC_WAYBILL':
        //         case 'WAYBILL':
        //             s.forEach((v, i, array) => {
        //                 const waybillCount = Func.getPrintSetting().waybillCount || 1
        //                 for (let _i = 0; _i < waybillCount; _i++) {
        //                     if (_i === waybillCount - 1 && array.length - 1 === i) {
        //                         result.push(this.buildPdfContent(JSON.parse(v), type))
        //                         return true
        //                     }
        //                     result.push(this.pageBreak(this.buildPdfContent(JSON.parse(v), type)))
        //                 }
        //                 /*
        //                 // 最后一个不加换行
        //                 if (array.length - 1 === i) {
        //                   result.push(this.buildPdfContent(JSON.parse(v), type))
        //                   return true
        //                 }
        //                 result.push(this.pageBreak(this.buildPdfContent(JSON.parse(v), type))) */
        //             })
        //             break
        //         case 'PC_PICKUP':
        //         case 'PICKUP':
        //             s.forEach((v, i, array) => {
        //                 // 最后一个不加换行
        //                 if (array.length - 1 === i) {
        //                     result.push(this.buildPdfContent(JSON.parse(v), type))
        //                     return true
        //                 }
        //                 result.push(this.pageBreak(this.buildPdfContent(JSON.parse(v), type)))
        //             })
        //             break
        //         case 'LABEL':
        //             list.forEach((v, i) => {
        //                 //  let start = 1
        //                 //  let end = +v.cargoNumber
        //                 if (!(+end || +v.cargoNumber)) {
        //                     return false
        //                 }
        //                 result = result.concat(this.buildLabel(s[i], type, +start || 1, +end || +v.cargoNumber))
        //             })
        //             if (!result.length) {
        //                 Message.error('没有件数,无需打印')
        //                 return false
        //             }
        //             // 最后一个不加换行
        //             result[result.length - 1].pageBreak = ''
        //             break
        //         case 'RECEIPT':
        //             list.forEach((v, i) => {
        //                 if (!(+end || +v.receiptNumber)) {
        //                     return false
        //                 }
        //                 result = result.concat(this.buildLabel(s[i], type, +start || 1, +end || +v.receiptNumber))
        //             })
        //             if (!result.length) {
        //                 Message.error('没有回单,无需打印')
        //                 return false
        //             }
        //             // 最后一个不加换行
        //             result[result.length - 1].pageBreak = ''
        //             break
        //     }
        //     this.getTrans(result, setting)
        // } catch (e) {
        //     console.error(e)
        // }
    },
    /**
     * 生成标签或收据
     * @param data
     * @param type
     * @param start
     * @param end
     * @returns {Array}
     */
    buildLabel(data = '', type, start, end) {
        let result = []
        for (start; start <= end; start++) {
            const labelNo = '0000' + start
            let t = ''
            switch (type) {
                case 'LABEL':
                    t = data.replace(/{{{labelNo}}}/g, labelNo.substr(labelNo.length - 4))
                    break
                case 'RECEIPT':
                    t = data.replace(/{{{receiptNo}}}/g, labelNo.substr(labelNo.length - 2))
                    break
            }
            t = t.replace(/{{{index}}}/g, start)
            let content = this.buildPdfContent(JSON.parse(t), type)
            this.pageBreak(content)
            result = result.concat(content)
        }
        return result
    },
    /**
     * 指令打印
     * @param data
     * @param type
     * @param start
     * @param end
     */
    commandPrint: function (data, type, start = 1, end = 1) {
        if (end < start) {
            Message.error('开始位置不能大于结束位置')
            return false
        }
        const printSetting = Func.getPrintSetting()
        if (!printSetting) {
            Message.error('请先去打印配置页面配置相关参数')
            return false
        }
        const template = Func.getPrintTemplate(type)
        if (!template) {
            Message.error('未找到模板')
            return false
        }
        let setting
        const CLODOP = window.CLODOP
        if (!CLODOP) {
            Message.error('请先下载并安装打印驱动后刷新页面再操作')
            return false
        }
        try {
            setting = JSON.parse(template.options)
            const s = this.parseTpl(data, template.command)
            if (!s) {
                Message.error('解析模板出错')
                return false
            }
            // console.log(printSetting)
            let ss = 'SIZE ' + setting.width + ' mm,' + setting.height + ' mm\r\n' +
                'GAP 3 mm,0 mm\r\n' +
                'REFERENCE 0,0\r\n' +
                'OFFSET 0 mm\r\n' +
                'SPEED 1.5\r\n' +
                'DENSITY 7\r\n' +
                'DIRECTION 0,0\r\n'
            let printer = 0 //
            let printerLabel = ''
            let mode = '2' // 1:套打 2:指令打印
            switch (type) {
                case 'WAYBILL':
                    printer = printSetting.PCWaybillPrinter // 打印机索引
                    printerLabel = 'PCWaybillPrinter'
                    const waybillCount = printSetting.PCWaybillCount || 1 // 运单联数
                    const command = JSON.parse(s) // 解析指令
                    mode = printSetting.PCWaybillPrinterMode // 打印模式
                    // let sHead
                    // ss = ''
                    // if (mode === 'electronicPanel') {
                    //     // 先禁用默认头部设置
                    //     sHead = 'SIZE 80 mm,112 mm\r\n' +
                    //         (printSetting.isPCWaybillPrinterBlackLabel !== 'N' ? 'BLINE 5 mm,0 mm\r\n' : 'GAP 0,0\r\n') +
                    //         // 'GAP 0,0' + // 连续纸打印
                    //         // // 'BLINE 5 mm,0 mm\r\n' +    // 黑标打印
                    //         'REFERENCE 0,0\r\n' +
                    //         'SPEED 1.5\r\n' +
                    //         'DENSITY 7\r\n' +
                    //         'DIRECTION 0,0\r\n' +
                    //         'CLS\r\n'
                    // }
                    // for (let i = 1; i <= waybillCount; i++) {
                    //     if (mode === '1') { // 针式套打
                    //         let intOrient = printSetting.PCWaybillOrientation === 'landscape' ? 1 : 2 // 打印方向
                    //         CLODOP.SET_PRINT_PAGESIZE(intOrient, setting.width + 'mm', setting.height + 'mm', '') // 设置打印页大小
                    //         this.parseLodop(command, i)
                    //     } else if (mode === 'electronicPanel') { // 电子面板
                    //         ss += sHead + this.parseTSCWabybill(data, command, i)
                    //         // ss += this.parseTSC(command, i)
                    //         // CLODOP.SET_SHOW_MODE('BKIMG_IN_PREVIEW', 1)
                    //         // CLODOP.SET_PRINT_PAGESIZE(2, 0, 0, '') // 设置打印页大小
                    //         // this.parseLodop(command, i)
                    //         // return
                    //     }
                    // }
                    // 是电子面单
                    if (mode === '2') {
                        // function d(printSetting) {
                        //     let sHead
                        //     ss = ''
                        //     sHead = 'SIZE 80 mm,112 mm\r\n' +
                        //         (printSetting.isPCWaybillPrinterBlackLabel !== 'N' ? 'BLINE 5 mm,0 mm\r\n' : 'GAP 0,0\r\n') +
                        //         // 'GAP 0,0' + // 连续纸打印
                        //         // // 'BLINE 5 mm,0 mm\r\n' +    // 黑标打印
                        //         'REFERENCE 0,0\r\n' +
                        //         'SPEED 1.5\r\n' +
                        //         'DENSITY 7\r\n' +
                        //         'DIRECTION 0,0\r\n' +
                        //         'CLS\r\n'
                        //     for (let i = 1; i <= waybillCount; i++) {
                        //         ss += sHead + this.parseTSCWabybill(data)
                        //     }
                        //     CLODOP.PRINT_INIT('打印任务')
                        //     // 获取打印机,有名称就通过名称，没有就通过打印机索引
                        //     if (!CLODOP.SET_PRINTER_INDEX(printSetting[`${printerLabel}Name`])) {
                        //         CLODOP.SET_PRINTER_INDEX(printer)
                        //     }
                        //     CLODOP.SEND_PRINT_RAWDATA(ss) // 原始数据
                        // }
                        Func.electronicPrint(printSetting, data)
                        return false
                    }
                    // 便携运单
                    if (mode === '1') {
                        ss = ''
                        for (let i = 1; i <= waybillCount; i++) {
                            ss += this.parseCPCL(command, i)
                        }
                    }
                    break
                case 'PICKUP':
                    ss = this.parseCPCL(JSON.parse(s))
                    break
                case 'LABEL':
                    printer = printSetting.PCLabelPrinter
                    printerLabel = 'PCLabelPrinter'
                    ss = Func.buildCommandLabel(s, type, start, end)
                    break
                case 'RECEIPT':
                    printer = printSetting.PCReceiptPrinter
                    printerLabel = 'PCReceiptPrinter'
                    ss = Func.buildCommandLabel(s, type, start, end)
                    break
            }
            if (mode === '2') {
                // console.log(ss)
                CLODOP.PRINT_INIT('打印任务')
                // 获取打印机,有名称就通过名称，没有就通过打印机索引
                if (!CLODOP.SET_PRINTER_INDEX(printSetting[`${printerLabel}Name`])) {
                    CLODOP.SET_PRINTER_INDEX(printer)
                }
                CLODOP.SEND_PRINT_RAWDATA(ss) // 原始数据
            }
        } catch (e) {
            console.error(e)
        }
    },
    buildCommandLabel(data = '', type, start, end) {
        let result = ''
        for (start; start <= end; start++) {
            const labelNo = '0000' + start
            let t = ''
            switch (type) {
                case 'LABEL':
                    t = data.replace(/{{{labelNo}}}/g, labelNo.substr(labelNo.length - 4))
                    break
                case 'RECEIPT':
                    t = data.replace(/{{{receiptNo}}}/g, labelNo.substr(labelNo.length - 2))
                    break
            }
            t = t.replace(/{{{index}}}/g, start)
            result += this.parseTSC(JSON.parse(t), start)
        }
        return result
    },
    /**
     * 模板引擎替换
     * @param data json
     * @param template string
     * @returns {*}
     */
    parseTpl(data, template) {
        const keys = Object.keys(data)
        let template2 = JSON.stringify(template)
        keys.forEach(val => {
            template2 = template2.replace(new RegExp('{{' + val + '}}', 'g'), data[val])
        })
        template2 = template2.replace(/[\n\r\s]/g, '')
        // console.log(JSON.parse(template2))
        try {
            return JSON.parse(template2)
        } catch (e) {
            // console.log(e)
            return false
        }
    },
    /**
     * 解析CPCL指令
     * @param template
     * @param index 保留字 序号
     * @returns {string}
     */
    parseCPCL(template, index) {
        let s = ''
        s += template.page ? '! 0 ' + template.page.ho + ' ' + template.page.vo + ' ' + template.page.h + ' 1\r\n' : '! 0 200 200 388 1\r\n'

        s += 'PAGE-WIDTH 576\r\n'

        if (template.text && template.text.length > 0) {
            let hadScale = false
            s += 'SETMAG 1 1 \r\n'
            template.text.forEach(val => {
                if ((val.xs > 1 || val.ys > 1) && !hadScale) {
                    hadScale = true
                    s += 'SETMAG ' + val.xs + ' ' + val.ys + '\r\n'
                }
                if (val.c && val.c.length > 0) {
                    val.c.forEach(v => {
                        if (v.n === 'index') {
                            switch (v.o) {
                                case '=':
                                    // noinspection EqualityComparisonWithCoercionJS
                                    if (index == v.v) {
                                        s += 'T ' + val.f + ' ' + val.s + ' ' + val.x + ' ' + val.y + ' ' + v.d + '\r\n'
                                    }
                                    break
                                case '>':
                                    if (index > v.v) {
                                        s += 'T ' + val.f + ' ' + val.s + ' ' + val.x + ' ' + val.y + ' ' + v.d + '\r\n'
                                    }
                                    break
                                case '<':
                                    if (index < v.v) {
                                        s += 'T ' + val.f + ' ' + val.s + ' ' + val.x + ' ' + val.y + ' ' + v.d + '\r\n'
                                    }
                                    break
                            }
                        }
                    })
                } else {
                    s += 'T ' + val.f + ' ' + val.s + ' ' + val.x + ' ' + val.y + ' ' + val.d + '\r\n'
                }
            })
        }

        if (template.line && template.line.length > 0) {
            template.line.forEach(val => {
                s += 'LINE ' + val.x0 + ' ' + val.y0 + ' ' + val.x1 + ' ' + val.y1 + ' ' + val.w + '\r\n'
            })
        }

        if (template.barcode && template.barcode.length > 0) {
            template.barcode.forEach(val => {
                s += val.c + ' 128 ' + val.w + ' ' + val.r + ' ' + val.h + ' ' + val.x + ' ' + val.y + ' ' + val.d + '\r\n'
            })
        }

        if (template.QRCode && template.QRCode.length > 0) {
            template.QRCode.forEach(val => {
                s += val.c + ' QR ' + val.x + ' ' + val.y + ' M ' + val.m + ' U ' + val.u + '\r\n'
                s += 'MA,' + val.d + '\r\n'
                s += 'ENDQR' + '\r\n'
            })
        }
        s += 'FORM\r\n'
        s += 'PRINT 1\r\n'
        // console.log(s)
        return s
    },
    parseTSC(template, index = 1) {
        let offsetX = 40
        let s = 'CLS\r\n'
        if (template.text && template.text.length > 0) {
            template.text.forEach(val => {
                let x = val.x + offsetX
                if (val.c && val.c.length > 0) {
                    val.c.forEach(v => {
                        if (v.n === 'index') {
                            switch (v.o) {
                                case '=':
                                    // noinspection EqualityComparisonWithCoercionJS
                                    if (index == v.v) {
                                        s += 'TEXT ' + x + ',' + val.y + ',' + '"TSS24.BF2"' + ',0,' + val.xs + ',' + val.ys + ',"' + v.d + '"\r\n'
                                    }
                                    break
                                case '>':
                                    if (index > v.v) {
                                        s += 'TEXT ' + x + ',' + val.y + ',' + '"TSS24.BF2"' + ',0,' + val.xs + ',' + val.ys + ',"' + v.d + '"\r\n'
                                    }
                                    break
                                case '<':
                                    if (index < v.v) {
                                        s += 'TEXT ' + x + ',' + val.y + ',' + '"TSS24.BF2"' + ',0,' + val.xs + ',' + val.ys + ',"' + v.d + '"\r\n'
                                    }
                                    break
                            }
                        }
                    })
                } else {
                    s += 'TEXT ' + x + ',' + val.y + ',' + '"TSS24.BF2"' + ',0,' + val.xs + ',' + val.ys + ',"' + val.d + '"\r\n'
                }
            })
        }

        if (template.line && template.line.length > 0) {
            template.line.forEach(val => {
                let x = val.x0 + offsetX
                let h = 0
                if (val.x0 === val.x1) {
                    h = val.y1 - val.y0
                    s += 'BAR ' + x + ',' + val.y0 + ',' + val.w + ',' + h + '\r\n'
                } else {
                    h = val.x1 - val.x0
                    s += 'BAR ' + x + ',' + val.y0 + ',' + h + ',' + val.w + '\r\n'
                }
            })
        }

        if (template.barcode && template.barcode.length > 0) {
            template.barcode.forEach(val => {
                let r = 0
                let x = val.x + offsetX
                let y = val.y
                if (val.c === 'VB') {
                    r = 90
                    y = y - 240
                }
                s += 'BARCODE ' + x + ',' + y + ',"128",' + val.h + ',0,' + r + ',2,2,"' + val.d + '"\r\n'
            })
        }

        if (template.QRCode && template.QRCode.length > 0) {
            template.QRCode.forEach(val => {
                let x = val.x + offsetX
                s += 'QRCODE ' + x + ',' + val.y + ',H,4,A,0,"' + val.d + '"\r\n'
            })
        }
        s += 'PRINT 1\r\r\n'
        // console.log(s)
        return s
    },
    /**
     * 电子面单打印
     *
     * */
    electronicPrint(printSetting, data) {
        const CLODOP = window.CLODOP
        let ss = ''
        let sHead = 'SIZE 80 mm,112 mm\r\n' +
            (printSetting.isPCWaybillPrinterBlackLabel !== 'N' ? 'BLINE 5 mm,0 mm\r\n' : 'GAP 0,0\r\n') +
            // 'GAP 0,0' + // 连续纸打印
            // // 'BLINE 5 mm,0 mm\r\n' +    // 黑标打印
            'REFERENCE 0,0\r\n' +
            'SPEED 1.5\r\n' +
            'DENSITY 7\r\n' +
            'DIRECTION 0,0\r\n' +
            'CLS\r\n'
        let count = printSetting.PCWaybillCount || 1
        for (let i = 1; i <= count; i++) {
            ss += sHead + this.parseTSCWabybill(data)
        }
        CLODOP.PRINT_INIT('打印任务')
        // 获取打印机,有名称就通过名称，没有就通过打印机索引
        if (!CLODOP.SET_PRINTER_INDEX(printSetting[`PCWaybillPrinterName`])) {
            CLODOP.SET_PRINTER_INDEX(printSetting.PCWaybillPrinter)
        }
        CLODOP.SEND_PRINT_RAWDATA(ss) // 原始数据
    },
    /**
     * 解析TSC指令
     * @param data
     * @returns {string}
     */
    parseTSCWabybill(data) {
        let template = store.state.sync.config['PRINT_PC_WAYBILL']
        if (!template) {
            Message({
                message: '请去【配置】->【打印模板】页面配置电子面单模板',
                type: 'error',
                duration: 5 * 1000,
                showClose: true
            })
            return false
        }
        const keys = Object.keys(data)
        // let template2 = Cache.get('PRINT_PC_WAYBILL')
        // debugger
        // if (!template2) {
        //     const t2 = 'TEXT 600,12,"TSS24.BF2",90,2,2,"{{sendSiteBelong}}"\r\n' +
        //         'TEXT 590,320,"TSS24.BF2",90,2,2,"{{waybillNo}}"\r\n' +
        //         'TEXT 595,660,"TSS24.BF2",90,2,2,"{{deliveryTypeText}}"\r\n' +
        //         'TEXT 570,770,"TSS24.BF2",90,1,1,"{{ymd}}"\r\n' +
        //         'TEXT 595,770,"TSS24.BF2",90,1,1,"开单时间"\r\n' +
        //         'TEXT 535,12,"TSS24.BF2",90,2,2,"{{sendSiteName}}→{{receiveAreaName}}({{receiveSiteName}})"\r\n' +
        //         'TEXT 535,600,"TSS24.BF2",90,2,2,""\r\n' +
        //         'TEXT 488,12,"TSS24.BF2",90,1,1,"发货客户:"\r\n' +
        //         'TEXT 488,312,"TSS24.BF2",90,1,1,"发货电话:"\r\n' +
        //         'TEXT 488,710,"TSS24.BF2",90,1,1,"重:"\r\n' +
        //         'TEXT 488,124,"TSS24.BF2",90,2,2,"{{senderName}}"\r\n' +
        //         'TEXT 488,424,"TSS24.BF2",90,2,2,"{{senderPhone}}"\r\n' +
        //         'TEXT 488,750,"TSS24.BF2",90,2,2,"{{cargoWeight}}kg"\r\n' +
        //         'TEXT 438,12,"TSS24.BF2",90,1,1,"收货客户:"\r\n' +
        //         'TEXT 438,312,"TSS24.BF2",90,1,1,"收货电话:"\r\n' +
        //         'TEXT 438,710,"TSS24.BF2",90,1,1,"容:"\r\n' +
        //         'TEXT 438,124,"TSS24.BF2",90,2,2,"{{receiverName}}"\r\n' +
        //         'TEXT 438,424,"TSS24.BF2",90,2,2,"{{receiverPhone}}"\r\n' +
        //         'TEXT 438,750,"TSS24.BF2",90,2,2,"{{cargoVolume}}方"\r\n' +
        //         'TEXT 385,312,"TSS24.BF2",90,2,2,"{{cargoName}}{{cargoNumber}}件回单{{receiptNumber}}张"\r\n' +
        //         'TEXT 340,312,"TSS24.BF2",90,2,2,""\r\n' +
        //         'TEXT 385,12,"TSS24.BF2",90,1,1,"现付:{{sendSpotPayFee}}"\r\n' +
        //         'TEXT 385,162,"TSS24.BF2",90,1,1,"提付:{{arriveSpotPayFee}}"\r\n' +
        //         'TEXT 358,12,"TSS24.BF2",90,1,1,"发货月结:{{monthlyPayFee}}"\r\n' +
        //         'TEXT 331,12,"TSS24.BF2",90,1,1,"送货费:{{deliveryFee}}"\r\n' +
        //         'TEXT 304,12,"TSS24.BF2",90,1,1,"接货费:{{pickupFee}}"\r\n' +
        //         'TEXT 277,12,"TSS24.BF2",90,1,1,"保费:{{insuranceFee}}"\r\n' +
        //         'TEXT 250,12,"TSS24.BF2",90,1,1,"回单费:{{receiptFee}}"\r\n' +
        //         'TEXT 223,12,"TSS24.BF2",90,1,1,"回单付:{{receiptPayFee}}"\r\n' +
        //         'TEXT 223,162,"TSS24.BF2",90,1,1,"货款扣:{{codDeductPayFee}}"\r\n' +
        //         'TEXT 196,12,"TSS24.BF2",90,1,1,"垫付:{{buyOutFee}}"\r\n' +
        //         'TEXT 285,312,"TSS24.BF2",90,2,2,"代收货款:{{cod}}"\r\n' +
        //         'TEXT 235,312,"TSS24.BF2",90,1,1,"开户行:{{bankCode}}"\r\n' +
        //         'TEXT 235,520,"TSS24.BF2",90,1,1,"持卡人:{{bankOwner}}"\r\n' +
        //         'TEXT 208,312,"TSS24.BF2",90,1,1,"卡号:{{bankCardNo}}"\r\n' +
        //         'TEXT 181,312,"TSS24.BF2",90,1,1,"代款手续费:{{codChargesFee}}"\r\n' +
        //         'TEXT 157,12,"TSS24.BF2",90,2,2,"{{senderFeeInfo}}"\r\n' +
        //         'TEXT 112,12,"TSS24.BF2",90,2,2,"{{deliveryTypeText}}:{{receiverAddress}}"\r\n' +
        //         'TEXT 67,12,"TSS24.BF2",90,2,2,"备注:{{remarks}}"\r\n' +
        //         'TEXT 198,740,"TSS24.BF2",90,1,1,"扫一扫查货"\r\n' +
        //         'TEXT 157,710,"TSS24.BF2",90,1,1,"签字:"\r\n' +
        //         'BAR 535,10,2,880\r\n' +
        //         'BAR 490,10,2,880\r\n' +
        //         'BAR 387,10,2,880\r\n' +
        //         'BAR 297,310,2,395\r\n' +
        //         'BAR 158,10,2,880\r\n' +
        //         'BAR 20,10,2,880\r\n' +
        //         'BAR 20,10,515,2\r\n' +
        //         'BAR 158,310,229,2\r\n' +
        //         'BAR 20,705,470,2\r\n' +
        //         'BAR 20,889,515,2\r\n' +
        //         'QRCODE 200,710,H,4,A,0,"https://track.365tms.com/?s={{waybillNo}}"\r\n' +
        //         'PRINT 1\r\n' +
        //         'CLS\r\n'
        // console.log(t2)
        //     Cache.set('PRINT_PC_WAYBILL', template2)
        // }
        // console.log(JSON.stringify(template3))
        // console.log(template2.length)
        // console.log(template3.length)
        // console.log(template2 === template3)
        // let demo = `
        // SIZE 80 mm,112 mm\\r\\n
        // BLINE 5 mm,0 mm\\r\\n
        // REFERENCE 0,0\\r\\n
        // SPEED 1.5\\r\\n
        // DENSITY 7\\r\\n
        // DIRECTION 0,0\\r\\n
        // CLS\\r\\n
        // TEXT 600,12,"TSS24.BF2",90,2,2,"{{sendSiteBelong}}"\\r\\n
        // TEXT 590,320,"TSS24.BF2",90,2,2,"{{waybillNo}}"\\r\\n
        // TEXT 595,660,"TSS24.BF2",90,2,2,"{{deliveryTypeText}}"\\r\\n
        // TEXT 570,770,"TSS24.BF2",90,1,1,"{{ymd}}"\\r\\n
        // TEXT 595,770,"TSS24.BF2",90,1,1,"开单时间"\\r\\n
        // TEXT 535,12,"TSS24.BF2",90,2,2,"{{sendSiteName}}→{{receiveAreaName}}({{receiveSiteName}})"\\r\\n
        // TEXT 535,600,"TSS24.BF2",90,2,2,""\\r\\n
        // TEXT 488,12,"TSS24.BF2",90,1,1,"发货客户:"\\r\\n
        // TEXT 488,312,"TSS24.BF2",90,1,1,"发货电话:"\\r\\n
        // TEXT 488,710,"TSS24.BF2",90,1,1,"重:"\\r\\n
        // TEXT 488,124,"TSS24.BF2",90,2,2,"{{senderName}}"\\r\\n
        // TEXT 488,424,"TSS24.BF2",90,2,2,"{{senderPhone}}"\\r\\n
        // TEXT 488,750,"TSS24.BF2",90,2,2,"{{cargoWeight}}kg"\\r\\n
        // TEXT 438,12,"TSS24.BF2",90,1,1,"收货客户:"\\r\\n
        // TEXT 438,312,"TSS24.BF2",90,1,1,"收货电话:"\\r\\n
        // TEXT 438,710,"TSS24.BF2",90,1,1,"容:"\\r\\n
        // TEXT 438,124,"TSS24.BF2",90,2,2,"{{receiverName}}"\\r\\n
        // TEXT 438,424,"TSS24.BF2",90,2,2,"{{receiverPhone}}"\\r\\n
        // TEXT 438,750,"TSS24.BF2",90,2,2,"{{cargoVolume}}方"\\r\\n
        // TEXT 385,312,"TSS24.BF2",90,2,2,"{{cargoName}}{{cargoNumber}}件回单{{receiptNumber}}张"\\r\\n
        // TEXT 340,312,"TSS24.BF2",90,2,2,""\\r\\n
        // TEXT 385,12,"TSS24.BF2",90,1,1,"现付:{{sendSpotPayFee}}"\\r\\n
        // TEXT 385,162,"TSS24.BF2",90,1,1,"提付:{{arriveSpotPayFee}}"\\r\\n
        // TEXT 358,12,"TSS24.BF2",90,1,1,"发货月结:{{monthlyPayFee}}"\\r\\n
        // TEXT 331,12,"TSS24.BF2",90,1,1,"送货费:{{deliveryFee}}"\\r\\n
        // TEXT 304,12,"TSS24.BF2",90,1,1,"接货费:{{pickupFee}}"\\r\\n
        // TEXT 277,12,"TSS24.BF2",90,1,1,"保费:{{insuranceFee}}"\\r\\n
        // TEXT 250,12,"TSS24.BF2",90,1,1,"回单费:{{receiptFee}}"\\r\\n
        // TEXT 223,12,"TSS24.BF2",90,1,1,"回单付:{{receiptPayFee}}"\\r\\n
        // TEXT 223,162,"TSS24.BF2",90,1,1,"货款扣:{{codDeductPayFee}}"\\r\\n
        // TEXT 196,12,"TSS24.BF2",90,1,1,"垫付:{{buyOutFee}}"\\r\\n
        // TEXT 285,312,"TSS24.BF2",90,2,2,"代收货款:{{cod}}"\\r\\n
        // TEXT 235,312,"TSS24.BF2",90,1,1,"开户行:{{bankCode}}"\\r\\n
        // TEXT 235,520,"TSS24.BF2",90,1,1,"持卡人:{{bankOwner}}"\\r\\n
        // TEXT 208,312,"TSS24.BF2",90,1,1,"卡号:{{bankCardNo}}"\\r\\n
        // TEXT 181,312,"TSS24.BF2",90,1,1,"代款手续费:{{codChargesFee}}"\\r\\n
        // TEXT 157,12,"TSS24.BF2",90,2,2,"{{senderFeeInfo}}"\\r\\n
        // TEXT 112,12,"TSS24.BF2",90,2,2,"{{deliveryTypeText}}:{{receiverAddress}}"\\r\\n
        // 'TEXT 67,12,"TSS24.BF2",90,2,2,"备注:{{remarks}}"\\r\\n
        // TEXT 198,740,"TSS24.BF2",90,1,1,"扫一扫查货"\\r\\n
        // TEXT 157,710,"TSS24.BF2",90,1,1,"签字:"\\r\\n
        // BAR 535,10,2,880\\r\\n
        // BAR 490,10,2,880\\r\\n
        // BAR 387,10,2,880\\r\\n
        // BAR 297,310,2,395\\r\\n
        // BAR 158,10,2,880\\r\\n
        // BAR 20,10,2,880\\r\\n
        // BAR 20,10,515,2\\r\\n
        // BAR 158,310,229,2\\r\\n
        // BAR 20,705,470,2\\r\\n
        // BAR 20,889,515,2\\r\\n
        // BAR 158,310,229,2\\r\\n
        // BAR 20,705,470,2\\r\\n
        // BAR 20,889,515,2\\r\\n
        // QRCODE 200,710,H,4,A,0,"https://track.365tms.com/?s={{waybillNo}}"\\r\\n
        // PRINT 1\\r\\n
        // CLS\\r\\n
        // `

        keys.forEach(val => {
            template = template.replace(new RegExp('{{' + val + '}}', 'g'), data[val])
        })
        template = template.replace(/\n/g, '\r\n')
        return template
    },
    parseLodop(template, index = 1) {
        const CLODOP = window.CLODOP
        CLODOP.SET_PRINT_STYLE('FontSize', 8)
        CLODOP.SET_PRINT_STYLE('FontName', '微软雅黑')
        let cIndex = 0
        let styleArr = []
        if (template.text && template.text.length > 0) {
            template.text.forEach(val => {
                let x = Func.pxConvertLodopmm(val.x) + 'mm'
                let y = Func.pxConvertLodopmm(val.y) + 'mm'
                if (val.xs > 1) {
                    styleArr.push(cIndex)
                }
                cIndex++
                if (val.c && val.c.length > 0) {
                    val.c.forEach(v => {
                        if (v.n === 'index') {
                            switch (v.o) {
                                case '=':
                                    // noinspection EqualityComparisonWithCoercionJS
                                    if (index == v.v) {
                                        // s += 'TEXT ' + x + ',' + val.y + ',' + '"TSS24.BF2"' + ',0,' + val.xs + ',' + val.ys + ',"' + v.d + '"\r\n'
                                        CLODOP.ADD_PRINT_TEXT(y, x, 1000, '', v.d)
                                    }
                                    break
                                case '>':
                                    if (index > v.v) {
                                        CLODOP.ADD_PRINT_TEXT(y, x, 1000, '', v.d)
                                    }
                                    break
                                case '<':
                                    if (index < v.v) {
                                        CLODOP.ADD_PRINT_TEXT(y, x, 1000, '', v.d)
                                    }
                                    break
                            }
                        }
                    })
                } else {
                    CLODOP.ADD_PRINT_TEXT(y, x, 1000, '', val.d)
                }
            })
        }
        if (template.line && template.line.length > 0) {
            template.line.forEach(val => {
                let x0 = Func.pxConvertLodopmm(val.x0) + 'mm'
                let x1 = Func.pxConvertLodopmm(val.x1) + 'mm'
                let y0 = Func.pxConvertLodopmm(val.y0) + 'mm'
                let y1 = Func.pxConvertLodopmm(val.y1) + 'mm'
                CLODOP.ADD_PRINT_LINE(y0, x0, y1, x1, 0, val.w)
            })
        }
        //
        if (template.QRCode && template.QRCode.length > 0) {
            template.QRCode.forEach(val => {
                let x = Func.pxConvertLodopmm(val.x) + 'mm'
                let y = Func.pxConvertLodopmm(val.y) + 'mm'
                CLODOP.ADD_PRINT_BARCODE(y, x, 80, 80, 'QRCode', val.d)
            })
        }
        styleArr.forEach(v => {
            CLODOP.SET_PRINT_STYLEA(v, 'FontSize', 16)
        })
        // CLODOP.PREVIEW()
        CLODOP.PRINT()
    },
    /**
     * 生成pdf内容
     * @param items {Array}
     * @param type
     * @returns {Array}
     */
    buildPdfContent(items, type) {
        const content = []
        const canvas = []
        // const barcodeCanvas = document.getElementById('barcodeCanvas')
        // const rotataCanvas = document.getElementById('rotataCanvas')
        // const rotateCanvasCtx = rotataCanvas.getContext('2d')
        items.forEach(val => {
            let x = val.x
            let y = val.y
            switch (val.type) {
                case 'line':
                    let lineWidth = val['height'] || 1
                    if (val.orientation === 'landscape') {
                        canvas.push({ type: 'line', x1: x, y1: y, x2: x + val.width, y2: y, lineWidth })
                    } else {
                        canvas.push({ type: 'line', x1: x, y1: y, x2: x, y2: y + val.width, lineWidth })
                    }
                    break
                // 条形码(包下载 https://www.jianshu.com/p/ab71ab75d360)
                // case 'barcode':
                // JsBarcode(barcodeCanvas, val.value, {
                //     format: 'CODE128',
                //     height: 34,
                //     displayValue: false,
                //     margin: 0
                // })
                // let image = barcodeCanvas.toDataURL()
                // let height = barcodeCanvas.height
                // if (val.orientation === 'portrait') {
                //     const rotation = 90 * Math.PI / 180
                //     rotataCanvas.width = barcodeCanvas.height || 100
                //     rotataCanvas.height = barcodeCanvas.width || 100
                //     rotateCanvasCtx.translate(barcodeCanvas.height || 100, 0)
                //     rotateCanvasCtx.rotate(rotation)
                //     rotateCanvasCtx.drawImage(barcodeCanvas, 0, 0)
                //     image = rotataCanvas.toDataURL()
                //     height = rotataCanvas.height
                //     if (type.indexOf('PC') === -1) {
                //         y = y - height
                //     }
                //     // console.log('%c       ', 'font-size: 100px; background: url(' + image + ') no-repeat;')
                // }
                // // console.log(val)
                // content.push({
                //     image,
                //     relativePosition: {x, y},
                //     height
                // })
                // break
                case 'QRCode':
                    content.push({
                        qr: val.value || '',
                        relativePosition: {
                            x: val.x,
                            y: val.y
                        },
                        fit: 128
                    })
                    break
                case 'text':
                case 'customText':
                    let f = val['fontSize']
                    switch (f) {
                        case 'small':
                            f = 14
                            break
                        case 'normal':
                            f = 18
                            break
                        case 'big':
                            f = 44
                            break
                    }
                    content.push({
                        text: val.value,
                        relativePosition: {
                            x: val.x,
                            y: val.y
                        },
                        fontSize: f || 18
                    })
                    break
            }
        })
        if (canvas.length > 0) {
            content.push({ canvas })
        }
        return content
    },
    pageBreak(content = []) {
        const last = content[content.length - 1]
        if (last) {
            content[content.length - 1] = { ...last, pageBreak: 'after' }
        }
        return content
    },
    buildPdf(content, setting) {
        // const printSetting = Func.getPrintSetting()
        // if (!printSetting) {
        //     return false
        // }
        // let pageMargins = [printSetting.left * 1, printSetting.top * 1, printSetting.right * 1, printSetting.bottom * 1]
        let pageMargins = [0, 0, 0, 0]
        let width
        let height
        if (setting.isM8) {
            pageMargins = [0, 0, 0, 0]
            width = Func.getM8TrueWidthPx(setting.width)
            height = Func.mmConvertM8Px(setting.height)
        } else {
            width = Func.mmConvertPx(setting.width || 210)
            height = Func.mmConvertPx(setting.height || 150)
        }
        return {
            pageSize: setting.pageSize !== 'custom' ? setting.pageSize : {
                width,
                height
            },
            customText: '第页,共页0123456789',
            pageMargins,
            pageOrientation: 'landscape',
            footer: function (currentPage, pageCount) {
                return setting.showFoot ? {
                    text: '第' + currentPage.toString() + '页,共' + pageCount + '页',
                    alignment: 'right'
                } : ''
            },
            content,
            styles: {
                headerInfo: {
                    fontSize: 8,
                    color: 'black',
                    alignment: 'right',
                    margin: [0, 0, 5, 0]
                },
                header: {
                    fontSize: 16,
                    bold: true,
                    color: 'black',
                    alignment: 'center',
                    margin: [0, 0, 0, 5]
                },
                tableExample: {
                    margin: [0, 5, 0, 5],
                    whiteSpace: 'normal'
                },
                tableHeader: {
                    bold: true,
                    fontSize: 8,
                    color: 'black',
                    alignment: 'center'
                },
                tableHeader1: {
                    bold: true,
                    fontSize: 8,
                    color: 'black'
                },
                sign: {
                    fontSize: 8,
                    alignment: 'right',
                    color: 'black',
                    margin: [0, 0, 0, 0]
                }
            },
            defaultStyle: {
                font: 'webfont',
                fontSize: 7
            }
        }
    },
    async getTrans(pdfData, setting) {
        const res = await Func.fetch(trans, Func.uniqueString(JSON.stringify(pdfData)) + ' 第页,共页0123456789')
        if (res) {
            pdfMake.vfs = res.data
            this.createPdf(pdfData, setting)
        }
    },
    createPdf(content, setting) {
        const pdfCotent = this.buildPdf(content, setting)
        const pdfDocGenerator = window.pdfMake.createPdf(pdfCotent)
        pdfDocGenerator.print()
    },
    /**
     * 大车单打印
     * @param transport  发运车次信息
     * @param list {Array} 装车运单
     */
    prinTransite(transport, list = []) {
        // console.log(transport)
        if (list.length === 0) {
            Message.error('没有运单')
            return false
        }
        const listData = []
        const widths = [14]
        const head = ['序号']
        const keys = []
        const printSetting = Func.getTransPrintConfig()
        if (!printSetting) {
            return false
        }
        const total = ['合计']
        const totalKeys = []
        const transportPrintTemplate = printSetting['transportPrintTemplate']
        const w = 94 / transportPrintTemplate.length
        transportPrintTemplate.forEach((v) => {
            widths.push(v.key === 'waybillNo' ? 40 : w + '%')
            if (v.isSum) {
                totalKeys.push(v.key)
            }
            total.push(v.isSum ? 0 : '')
            keys.push(v.key)
            head.push({
                text: v.label,
                style: 'tableHeader1'
            })
        })
        listData.push(head)

        list.forEach((val, i) => {
            let item = [i + 1]
            keys.forEach((v, k) => {
                let value = val[v] === undefined ? '' : val[v]
                if (totalKeys.includes(v)) {
                    total[k + 1] += value
                }
                item.push(value)
            })
            listData.push(item)
        })
        listData.push(total)

        const content = []

        content.push({ text: '车辆运输单', style: 'header' })

        content.push({
            alignment: 'justify',
            style: 'sign',
            columns: [
                '线路编号:' + (transport.code || transport.transportCode),
                '线路:' + (transport.transportLineName || '*'),
                '发货网点:' + (transport.sendSiteName || '*'),
                '车牌:' + (transport.licensePlate || '*'),
                '司机:' + (transport.driverName || '*'),
                '联系电话:' + (transport.driverPhone || '*'),
                '发车时间:' + (transport.sendDate || '无')
            ]
        })
        content.push({
            style: 'tableExample',
            table: {
                headerRows: 1,
                widths: widths,
                body: listData
            }
        })

        content.push({
            alignment: 'left',
            style: 'sign',
            columns: [
                '预付:' + transport.prePayFee + '      到付:' + transport.arrivePayFee + '      回付:' + transport.backPayFee
            ]
        })
        content.push({
            alignment: 'right',
            style: 'sign',
            columns: [
                '制单人:______________        负责人:______________       驾驶员:______________'
            ]
        })
        this.getTrans(content, { pageSize: 'A4' })
    },
    async printTable(title, data, isPreview = false) {
        try {
            const LODOP = window.LODOP
            if (!LODOP) {
                Message({
                    message: '请去【配置】->【打印设置】页面下载安装打印驱动并配置完成后再来打印',
                    type: 'error',
                    duration: 5 * 1000,
                    showClose: true
                })
                return false
            }

            // data = $.parseJSON(data);
            data.title = title
            let template = store.state.sync.config['PRINT_TRANSPORT']
            if (!template) {
                Message({
                    message: '请去【配置】->【打印模板】页面配置大车单模板',
                    type: 'error',
                    duration: 5 * 1000,
                    showClose: true
                })
                return false
            }
            const result = window.laytpl(template).render(data)
            // console.log(result)
            const printerConfig = Func.getPrintSetting()
            // console.log(printerConfig)
            LODOP.PRINT_INIT(title)
            LODOP.SET_PRINT_MODE('POS_BASEON_PAPER', true)
            if (!LODOP.SET_PRINTER_INDEX(printerConfig.PCTransportPrinterName)) {
                LODOP.SET_PRINTER_INDEX(printerConfig.PCTransportPrinter)
            }
            LODOP.SET_PRINT_PAGESIZE(printerConfig.PCTransportOrientation === 'landscape' ? 2 : 1, 0, 0, '')// 打印方向
            LODOP.SET_SHOW_MODE('LANDSCAPE_DEFROTATED', 1)// 横向时的正向显示
            LODOP.ADD_PRINT_TABLE(printerConfig.PCTransportTop + 'mm', printerConfig.PCTransportLeft + 'mm', 'RightMargin:' +
                printerConfig.PCTransportRight + 'mm', 'BottomMargin:' + printerConfig.PCTransportBottom + 'mm', result)
            // LODOP.PREVIEW()
            if (!isPreview) {
                LODOP.PRINT()
                Message.success('打印命令发送成功')
            } else {
                LODOP.PREVIEW()
            }
        } catch (e) {
            Message.error('操作失败:打印内容渲染异常' + e.message)
        }
    }
}

// setTimeout(() => {
//     Func.printTable('大车单', {
//         transport: {},
//         loadShiftSheetList: [{}, {}]
//     }, true)
// }, 3000)

export default Func
