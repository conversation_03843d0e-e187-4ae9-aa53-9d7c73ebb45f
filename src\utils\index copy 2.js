import XLSX from 'xlsx'

export function parseTime(time, cFormat) {
    if (arguments.length === 0) {
        return null
    }
    const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
    let date
    if (typeof time === 'object') {
        date = time
    } else {
        if (('' + time).length === 10) time = parseInt(time) * 1000
        date = new Date(time)
    }
    const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
    }
    return format.replace(/{([ymdhisa])+}/g, (result, key) => {
        let value = formatObj[key]
        // Note: getDay() returns 0 on Sunday
        if (key === 'a') {
            return ['日', '一', '二', '三', '四', '五', '六'][value]
        }
        if (result.length > 0 && value < 10) {
            value = '0' + value
        }
        return value || 0
    })
}

export function formatGmt(time) {
    const d = new Date(time)
    return (
        d.getMonth() +
        1 +
        '月' +
        d.getDate() +
        '日' +
        d.getHours() +
        '时' +
        d.getMinutes() +
        '分'
    )
}

export function formatTimeSecond(time) {
    const d = new Date(time)
    return (
        d.getHours() +
        '时' +
        d.getMinutes() +
        '分' +
        d.getSeconds() +
        '秒'
    )
}

export function getYearMonth() {
    const date = new Date()
    const year = date.getFullYear() // 当前年：四位数字
    let month = date.getMonth() + 1 // 当前月：0-11
    month = month < 10 ? ('0' + month) : month
    return year + '-' + month
}

export function lastMonth() {
    var date = new Date()
    var year = date.getFullYear() // 当前年：四位数字
    var month = date.getMonth() // 当前月：0-11

    if (month === 0) { // 如果是0，则说明是1月份，上一个月就是去年的12月
        year -= 1
        month = 12
    }

    month = month < 10 ? ('0' + month) : month // 月份格式化：月份小于10则追加个0

    let lastYearMonth = year + '-' + month

    return lastYearMonth
}

export function getToday(time) {
    return (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd')
}

// export function day(day) {
//     if (day) {
//         return (new Date(new Date(day).getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd')
//     }
//     return (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd')
// }

export function formatTime(time, option) {
    const d = new Date(time)
    const now = Date.now()

    const diff = (now - d) / 1000

    if (diff < 30) {
        return '刚刚'
    } else if (diff < 3600) {
        // less 1 hour
        return Math.ceil(diff / 60) + '分钟前'
    } else if (diff < 3600 * 24) {
        return Math.ceil(diff / 3600) + '小时前'
    } else if (diff < 3600 * 24 * 2) {
        return '1天前'
    }
    if (option) {
        return parseTime(time, option)
    } else {
        return (
            d.getMonth() +
            1 +
            '月' +
            d.getDate() +
            '日' +
            d.getHours() +
            '时' +
            d.getMinutes() +
            '分'
        )
    }
}

// 格式化时间
export function getQueryObject(url) {
    url = url == null ? window.location.href : url
    const search = url.substring(url.lastIndexOf('?') + 1)
    const obj = {}
    const reg = /([^?&=]+)=([^?&=]*)/g
    search.replace(reg, (rs, $1, $2) => {
        const name = decodeURIComponent($1)
        let val = decodeURIComponent($2)
        val = String(val)
        obj[name] = val
        return rs
    })
    return obj
}

export function cleanArray(actual) {
    const newArray = []
    for (let i = 0; i < actual.length; i++) {
        if (actual[i]) {
            newArray.push(actual[i])
        }
    }
    return newArray
}

export function param(json) {
    if (!json) return ''
    return cleanArray(
        Object.keys(json).map(key => {
            if (json[key] === undefined) return ''
            return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
        })
    ).join('&')
}

export function param2Obj(url) {
    const search = url.split('?')[1]
    if (!search) {
        return {}
    }
    return JSON.parse(
        '{"' +
        decodeURIComponent(search)
            .replace(/"/g, '\\"')
            .replace(/&/g, '","')
            .replace(/=/g, '":"') +
        '"}'
    )
}

export function html2Text(val) {
    const div = document.createElement('div')
    div.innerHTML = val
    return div.textContent || div.innerText
}

export function objectMerge(target, source) {
    /* Merges two  objects,
       giving the last one precedence */

    if (typeof target !== 'object') {
        target = {}
    }
    if (Array.isArray(source)) {
        return source.slice()
    }
    Object.keys(source).forEach(property => {
        const sourceProperty = source[property]
        if (typeof sourceProperty === 'object') {
            target[property] = objectMerge(target[property], sourceProperty)
        } else {
            target[property] = sourceProperty
        }
    })
    return target
}

export function toggleClass(element, className) {
    if (!element || !className) {
        return
    }
    let classString = element.className
    const nameIndex = classString.indexOf(className)
    if (nameIndex === -1) {
        classString += '' + className
    } else {
        classString =
            classString.substr(0, nameIndex) +
            classString.substr(nameIndex + className.length)
    }
    element.className = classString
}

export const pickerOptions = [
    {
        text: '今天',
        onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().toDateString())
            end.setTime(start.getTime())
            picker.$emit('pick', [start, end])
        }
    },
    // {
    //     text: '最近一周',
    //     onClick(picker) {
    //         const end = new Date(new Date().toDateString())
    //         const start = new Date()
    //         start.setTime(end.getTime() - 3600 * 1000)
    //         picker.$emit('pick', [start, end])
    //     }
    // },
    {
        text: '最近一周',
        onClick(picker) {
            const end = new Date(new Date().toDateString())
            const start = new Date()
            start.setTime(end.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
        }
    },
    {
        text: '最近一个月',
        onClick(picker) {
            const end = new Date(new Date().toDateString())
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
        }
    },
    {
        text: '最近三个月',
        onClick(picker) {
            const end = new Date(new Date().toDateString())
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
        }
    }
]

export function getTime(type) {
    if (type === 'start') {
        return new Date().getTime() - 3600 * 1000 * 24 * 90
    } else {
        return new Date(new Date().toDateString())
    }
}

export function debounce(func, wait, immediate) {
    let timeout, args, context, timestamp, result

    const later = function () {
        // 据上一次触发时间间隔
        const last = +new Date() - timestamp

        // 上次被包装函数被调用时间间隔last小于设定时间间隔wait
        if (last < wait && last > 0) {
            timeout = setTimeout(later, wait - last)
        } else {
            timeout = null
            // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
            if (!immediate) {
                result = func.apply(context, args)
                if (!timeout) context = args = null
            }
        }
    }

    return function (...args) {
        context = this
        timestamp = +new Date()
        const callNow = immediate && !timeout
        // 如果延时不存在，重新设定延时
        if (!timeout) timeout = setTimeout(later, wait)
        if (callNow) {
            result = func.apply(context, args)
            context = args = null
        }

        return result
    }
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 */
// export function deepClone(source) {
//     if (!source && typeof source !== 'object') {
//         throw new Error('error arguments')
//     }
//     const targetObj = source.constructor === Array ? [] : {}
//     Object.keys(source).forEach(keys => {
//         if (source[keys] && typeof source[keys] === 'object') {
//             targetObj[keys] = deepClone(source[keys])
//         } else {
//             targetObj[keys] = source[keys]
//         }
//     })
//     return targetObj
// }

// 深拷贝对象
// add by 平台源码 https://github.com/JakHuang/form-generator/blob/dev/src/utils/index.js#L107
export function deepClone(obj) {
    const _toString = Object.prototype.toString

    // null, undefined, non-object, function
    if (!obj || typeof obj !== 'object') {
        return obj
    }

    // DOM Node
    if (obj.nodeType && 'cloneNode' in obj) {
        return obj.cloneNode(true)
    }

    // Date
    if (_toString.call(obj) === '[object Date]') {
        return new Date(obj.getTime())
    }

    // RegExp
    if (_toString.call(obj) === '[object RegExp]') {
        const flags = []
        if (obj.global) {
            flags.push('g')
        }
        if (obj.multiline) {
            flags.push('m')
        }
        if (obj.ignoreCase) {
            flags.push('i')
        }

        return new RegExp(obj.source, flags.join(''))
    }

    const result = Array.isArray(obj) ? [] : obj.constructor ? new obj.constructor() : {}

    for (const key in obj) {
        result[key] = deepClone(obj[key])
    }

    return result
}




export function uniqueArr(arr) {
    return Array.from(new Set(arr))
}

export function isExternal(path) {
    return /^(https?:|mailto:|tel:)/.test(path)
}

// 递归清除冗余的数据
export function clearTreeEmptyChildren(data) {
    if (data === null || data === undefined) {
        return data
    }
    for (let i = 0; i < data.length; i++) {
        if (data[i].children.length === 0) {
            data[i].children = undefined
        } else {
            clearTreeEmptyChildren(data[i].children)
        }
    }
    return data
}

export function getUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
        return (c === 'x' ? (Math.random() * 16 | 0) : ('r&0x3' | '0x8')).toString(16)
    })
}

function formatJson(filterVal, jsonData) {
    return jsonData.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
            return parseTime(v[j])
        } else {
            return v[j]
        }
    }))
}

export function exportExcel({ title, header, columns, list }) {
    require.ensure([], () => {
        const { export_json_to_excel } = require('@/vendor/Export2Excel')
        const tHeader = header
        let data = []
        if (columns) {
            const filterVal = columns
            data = formatJson(filterVal, list)
        }
        export_json_to_excel(tHeader, data, title)
    })
}

export function importExcel(f) {
    // const XLSX = window.XLSX
    return new Promise((resolve, reject) => {
        try {
            const reader = new FileReader()
            reader.onload = function (e) {
                const data = e.target.result
                const workbook = XLSX.read(data, { type: 'binary' })
                if (workbook.SSF) XLSX.SSF.load_table(workbook.SSF)
                workbook.SheetNames.forEach(function (sheetName) {
                    const roa = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName]) //  {header: 1}
                    // if (roa.length > 0) {
                    //   console.log(roa)
                    // }
                    resolve(roa)
                })
            }
            reader.readAsBinaryString(f.raw)
        } catch (e) {
            reject(e)
        }
    })
}

export async function FetchData(imp, params, defaultValue) {
    try {
        const { data } = await imp(params)
        return data
    } catch (e) {
        console.log(e, 'error')
        return defaultValue
    }
}

export function createMemoryField({ fields = [], target = {}, init = {} }) {
    const fieldsForm = {}
    for (const [k, v] of Object.entries(target)) {
        if (fields instanceof Array) {
            if (fields.includes(k)) {
                fieldsForm[k] = init[k] || ''
            }
        } else {
            if (Object.keys(fields).includes(k)) {
                fieldsForm[k] = v
            }
        }
    }
    return fieldsForm
}

/**
     * 时间转为秒
     * @param time 时间(00:00:00)
     * @returns {string} 时间戳（单位：秒）
     */
export function timeToSec(time) {
    let s = ''
    let hour = time.split(':')[0]
    let min = time.split(':')[1]
    // let sec = time.split(':')[2]
    s = Number(hour * 3600) + Number(min * 60)
    return s
};
