import Func from '@/utils/func'
import { listQuery } from '@/const'
import Model from '@/s-curd/src/model'
import { exportExcel, parseTime, getToday } from '@/utils/index'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      model: Model,
      entityForm: { ...Model.model },
      title: this.$route.meta && this.$route.meta.title,
      listQuery: { ...listQuery },
      entityFormLoading: false,
      dialogFormVisible: false,
      // , noticePr: '通知磅房'
      textMap: {
        update: '编辑',
        create: '新建',
        watch: '查看',
        review: '审核',
        payapply: '付款申请',
        applyRecv: '收款登记',
        settlePay: '付款结算',
        settleRecv: '收款结算',
        Inventory: '盘库',
        Send: '发送短信',
        Refresh: '刷新',
        PaymentConfirmation: '出纳付款及确认',
        lookPAYED: '付款记录查看',
        change: '修改',
        priceAdjustment: '调价',
        AdjustPriceReview: '调价审核',
        addAmount: '调量',
        freightReview: '运费调价审核',
        freightAdjustment: '运费调价',
        pushPermission: '推送权限',
        CustomerRefund: '客户退款',
        SupplierRefund: '供应商退款',
        freightSettlement: '运费结算'
      },
      dialogStatus: 'create',
      field: { int: `value=value.replace(${/[^\d]/g},'')`, decimal: `value=value.replace(${/[^\d.]/g},'')` },
      actions: [],
      actionList: [],
      buttomlist: [],
      dialogVisible: false,
      activetype: 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS',
      filtersSeach: {},
      changeType: '',
      countList: []
    }
  },
  provide() {
    return {
      filterOption: this.filterOption
    }
  },
  computed: {
    ...mapGetters(
      ['perms']
    ),
    getToday() {
      return getToday()
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (vm.$route.params.isOpenAdd) {
        vm.dialogStatus = 'create'
        vm.dialogFormVisible = true
      }
      if (vm.$route.params.date) {
        const [filter_GES_date, filter_LES_date] = vm.$route.params.date
        vm.filterOption.columns[0].defaultValue = filter_GES_date
        vm.filterOption.columns[1].defaultValue = filter_LES_date
        vm.handleFilter({ filter_GES_date, filter_LES_date })
      }
    })
  },
  methods: {
    async Refreshfn() {
      this.changeeFormLoading = true
      try {
        const { data } = await this.model.refresh()
        this.$message({ showClose: true, message: '刷新成功', type: 'success' })
        this.changeeFormLoading = false
        this.$refs[this.curd].searchChange({ ...this.listQuery })
      } catch (error) {
      }
      this.changeeFormLoading = false
      this.getList()
    },

    async handleRefresh(row) {
      let { date, name, productName, itemName } = row
      name = name || productName || itemName
      try {
        const res = await this.model.refresh({ date, name })
        if (res) {
          this.$message({ showClose: true, message: '刷新成功', type: 'success' })
          this.getList()
        }
      } catch (error) {

      }
    },

    handleWatchForm(item) {
      this.entityForm = { ...item }
      this.dialogStatus = 'watch'
      this.dialogFormVisible = true
    },
    createMemoryField({ fields = [], target = {}, init = {} }) {
      const fieldsForm = {}
      for (const [k, v] of Object.entries(target)) {
        if (fields instanceof Array) {
          if (fields.includes(k)) {
            fieldsForm[k] = init[k] || ''
          }
        } else {
          if (Object.keys(fields).includes(k)) {
            fieldsForm[k] = v
          }
        }
      }
      return fieldsForm
    },
    // 保存时触发字段保留基础信息
    saveMemoryField() {
      this.memoryEntity.fields = this.createMemoryField({ fields: this.memoryEntity.fields, target: this.entityForm })
      this.memoryEntity.triggered = true
    },
    permsAction(curd) {
      let imp = `${curd}:import`
      let xpt = `${curd}:export`
      let newimp = `${curd}:newimport`

      let addimport = `${curd}:addimport`

      let actions = []

      if (this.perms[addimport]) {
        actions.push({
          config: {
            type: 'export-all'
          },
          show: true,
          label: '新增Excel导入',
          click: this.addimportExcel,
          type: 'procureimport',
          ismargin: true
        })
      }

      if (this.perms[imp]) {
        actions.push(
          // {
          //   config: {
          //     type: 'export-all'
          //   },
          //   show: true,
          //   label: '批量编辑',
          //   click: this.batchChangeexcele,
          //   type: 'editexcel',
          //   isborder: true
          // },
          // {
          //     config: {
          //         type: 'export-all'
          //     },
          //     show: true,
          //     label: '批量删除',
          //     click: this.batchDeleteexcele,
          //     type: 'deleteexcel',
          //     isborder: true
          // }
          // {
          //     config: {
          //         type: 'export-all'
          //     },
          //     show: true,
          //     label: '下载导入模板',
          //     click: this.downloadTemplateHandler,
          //     type: 'importTemplate',
          //     isborder: true
          // }
        )
      }

      if (this.perms[xpt]) {
        actions.push({
          config: { type: 'export-all' },
          show: true,
          label: '导出当前页',
          click: this.export,
          type: 'current',
          isborder: true
        }, {
          config: { type: 'export-all' },
          show: true,
          label: '导出所有',
          click: this.export,
          type: 'all',
          isborder: true
        })
      }


      // if (this.perms[newimp]) {
      //     actions.push({
      //         config: {
      //             type: 'export-all'
      //         },
      //         show: true,
      //         label: '新增Excel导入',
      //         click: this.newimportExcel,
      //         type: 'procureimport',
      //         isborder: true
      //     })
      // }


      this.actions = (actions)
    },
    permschange(curd) {
      let BatchModification = `${curd}:BatchModification`//批量改
      let batchChangePrice = `${curd}:batchChangePrice`//批量改煤价
      let batchChangeCarriage = `${curd}:batchChangeCarriage`//批量改运费
      let batchChangeContract = `${curd}:batchChangeContract`//批量改合同
      let batchChangeMt = `${curd}:batchChangeMt`//批量修改水分
      let batchChangeRemarks = `${curd}:batchChangeRemarks`//批量备注
      let batchChangeSettle = `${curd}:batchChangeSettle`//批量修改是否结算
      let BatchDelete = `${curd}:BatchDelete`//批量删除
      let batchLossNullBase = `${curd}:batchLossNullBase`//批量修改亏吨基准

      let changeactions = []

      // if (this.perms[BatchDelete]) {
      //     changeactions.push({
      //         active: false,
      //         show: true,
      //         label: '批量删除',
      //         click: this.BatchDelete,
      //         type: 'BatchModification'
      //     })
      // }

      if (this.perms[BatchModification]) {
        changeactions.push({
          active: false,
          show: true,
          label: '批量修改',
          // label: '批量修改运费',
          click: this.batchChangePrice,
          type: 'BatchModification'
        })
      }
      if (this.perms[batchChangePrice]) {
        changeactions.push({
          active: false,
          show: true,
          label: '批量修改煤价',
          click: this.batchChangePrice,
          type: 'batchChangePrice'
        })
      }
      if (this.perms[batchChangeCarriage]) {
        changeactions.push({
          active: false,
          show: true,
          label: '批量修改运费',
          click: this.batchChangePrice,
          type: 'batchChangeCarriage'
        })
      }
      if (this.perms[batchChangeContract]) {
        changeactions.push({
          active: false,
          show: true,
          label: '批量修改合同',
          click: this.batchChangePrice,
          type: 'batchChangeContract'
        })
      }
      if (this.perms[batchChangeMt]) {//
        changeactions.push({
          active: false,
          show: true,
          label: '批量修改水分',
          click: this.batchChangePrice,
          type: 'batchChangeMt'
        })
      }
      if (this.perms[batchChangeRemarks]) {//
        changeactions.push({
          active: false,
          show: true,
          label: '批量修改备注',
          click: this.batchChangePrice,
          type: 'batchChangeRemarks'
        })
      }
      if (this.perms[batchChangeSettle]) {//
        changeactions.push({
          active: false,
          show: true,
          label: '批量修改是否结算',
          click: this.batchChangePrice,
          type: 'batchChangeSettle'
        })
      }
      if (this.perms[batchLossNullBase]) {//
        changeactions.push({
          active: false,
          show: true,
          label: '批量修改亏吨基准',
          click: this.batchChangePrice,
          type: 'batchLossNullBase'
        })
      }


      this.changeactions = (changeactions)
    },


    permsActionbtn(curd) {
      let all = `${curd}:all`//全部
      let waitforreviewed = `${curd}:waitforreviewed`//待审核
      let applayReviewed = `${curd}:applayReviewed`//付款单=>代待付款
      let applayManagerwaitforreviewed = `${curd}:applayManagerwaitforreviewed`//付款单=>总经理待审核
      let applayFinancewaitforreviewed = `${curd}:applayFinancewaitforreviewed`//付款单=>财务待审核
      // let applayReviewed = `${curd}:waitforreviewed`//付款单=>已审核
      let Reviewed = `${curd}:Reviewed`//已审核
      let tsReviewed = `${curd}:tsReviewed`//已审核(PASS_FINANCE)
      let Paid = `${curd}:Paid`  //已付款
      let drafts = `${curd}:drafts`  //草稿箱
      let Noticedetails = `${curd}:Noticedetails`  //通知磅房 全部
      // let Applicationdetails = `${curd}:Applicationdetails`  //申请明细
      // let Settlementdetails = `${curd}:Settlementdetails`  //结算明细
      let managerwaitforreviewed = `${curd}:managerwaitforreviewed`  //待审核(总经理)
      let Financewaitforreviewed = `${curd}:Financewaitforreviewed`  //待审核(财务)
      // let Contractdetails = `${curd}:Contractdetails`  //合同明细
      let WarehousingWaitforreviewed = `${curd}:WarehousingWaitforreviewed`  //入库待审核
      let WarehousingReviewed = `${curd}:WarehousingReviewed`  //入库已审核
      let ExwarehouseWaitforreviewed = `${curd}:ExwarehouseWaitforreviewed`  //出库待审核
      let ExwarehouseReviewed = `${curd}:ExwarehouseReviewed`  //出库已审核
      let settlePayFinancewaitforreviewed = `${curd}:settlePayFinancewaitforreviewed`  //结算 付款/收款结算 ==》待审核(财务)
      let settlePayManagerwaitforreviewed = `${curd}:settlePayManagerwaitforreviewed`  //结算 付款/收款结算 ==》待审核(总经理)
      let settlePayReviewed = `${curd}:settlePayReviewed`  //结算 付款/收款结算 ==》已审核
      let tovoid = `${curd}:istovoid`//已作废
      let Audit = `${curd}:Audit`//合同待审核
      let actionList = []
      if (this.perms[all]) {
        actionList.push({
          active: false,
          label: '全部',
          click: this.waitforReviewed,
          type: 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
        })
      }
      if (this.perms[Noticedetails]) {
        actionList.push({
          active: true,
          label: '全部',
          click: this.waitforReviewed,
          type: ''
        })
      }
      if (this.perms[drafts]) {
        actionList.push({
          active: true,
          label: '草稿箱',
          click: this.waitforReviewed,
          type: 'DRAFT,REJECT'
        })
      }

      // if (this.perms[drafts]) {
      //     actionList.push({
      //         active: true,
      //         label: '草稿箱',
      //         click: this.waitforReviewed,
      //         type: 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
      //     })
      // }

      if (this.perms[Audit]) {
        actionList.push({
          active: false,
          label: '待审核',
          click: this.waitforReviewed,
          //不能改这个状态 ，影响很严重
          type: 'NEW,PASS_FINANCE'
        })
      }


      // if (this.perms[settlePayManagerwaitforreviewed]) {
      //     actionList.push({
      //         active: false,
      //         label: '待审核(总经理)',
      //         click: this.waitforReviewed,
      //         type: 'NEW'
      //     })
      // }
      // if (this.perms[settlePayFinancewaitforreviewed]) {
      //     actionList.push({
      //         active: false,
      //         label: '待审核(财务)22',
      //         click: this.waitforReviewed,
      //         type: 'PASS'
      //     })
      // }
      if (this.perms[settlePayReviewed]) {
        actionList.push({
          active: false,
          label: '已审核',
          click: this.waitforReviewed,
          type: 'PASS'
        })
      }
      // if (this.perms[Financewaitforreviewed]) {
      //     actionList.push({
      //         active: false,
      //         label: '待审核(财务)221',
      //         click: this.waitforReviewed,
      //         type: 'NEW'
      //     })
      // }
      // if (this.perms[managerwaitforreviewed]) {
      //     actionList.push({
      //         active: false,
      //         label: '待审核(总经理)21',
      //         click: this.waitforReviewed,
      //         type: 'PASS_FINANCE'
      //     })
      // }
      // if (this.perms[applayManagerwaitforreviewed]) {
      //     actionList.push({
      //         active: false,
      //         label: '待审核(总经理)',
      //         click: this.waitforReviewed,
      //         type: 'NEW'
      //     })
      // }
      // if (this.perms[applayFinancewaitforreviewed]) {
      //     actionList.push({
      //         active: false,
      //         label: '待审核(财务)',
      //         click: this.waitforReviewed,
      //         type: 'PASS'
      //     })
      // }
      if (this.perms[waitforreviewed]) {
        actionList.push({
          active: false,
          label: '待审核',
          click: this.waitforReviewed,
          type: 'NEW'
        })
      }
      if (this.perms[applayReviewed]) {
        actionList.push({
          active: false,
          label: '待付款',
          click: this.waitforReviewed,
          type: 'PASS'
        })
      }


      if (this.perms[Reviewed]) {
        actionList.push({
          active: false,
          label: '已审核',
          click: this.waitforReviewed,
          type: 'PASS'
        })
      }


      // if (this.perms[Applicationdetails]) {
      //     actionList.push({
      //         active: false,
      //         label: '申请明细',
      //         click: this.waitforReviewed,
      //         type: 'DRAFT,REJECT'
      //     })
      // }
      // if (this.perms[Settlementdetails]) {
      //     actionList.push({
      //         active: false,
      //         label: '结算明细',
      //         click: this.waitforReviewed,
      //         type: 'DRAFT,REJECT'
      //     })
      // }

      if (this.perms[Paid]) {
        actionList.push({
          active: false,
          label: '已付款',
          click: this.waitforReviewed,
          type: 'PAYED'
        })
      }

      if (this.perms[tsReviewed]) {
        actionList.push({
          active: false,
          label: '已审核',
          click: this.waitforReviewed,
          type: 'PASS_FINANCE'
        })
      }

      if (this.perms[WarehousingWaitforreviewed]) {
        actionList.push({
          active: false,
          label: '入库待审核',
          click: this.waitforReviewed,
          type: 'NEW'
        })
      }
      if (this.perms[WarehousingReviewed]) {
        actionList.push({
          active: false,
          label: '入库已审核',
          click: this.waitforReviewed,
          type: 'PASS'
        })
      }


      if (this.perms[ExwarehouseWaitforreviewed]) {
        actionList.push({
          active: false,
          label: '出库待审核',
          click: this.waitforReviewed,
          type: 'NEW'
        })
      }
      if (this.perms[ExwarehouseReviewed]) {
        actionList.push({
          active: false,
          label: '出库已审核',
          click: this.waitforReviewed,
          type: 'PASS'
        })
      }

      if (this.perms[tovoid]) {
        actionList.push({
          active: false,
          label: '已作废',
          click: this.waitforReviewed,
          type: 'CANCEL'
        })
      }

      // if (this.perms[Contractdetails]) {
      //     actionList.push({
      //         active: false,
      //         label: '合同明细',
      //         click: this.waitforReviewed,
      //         type: 'DRAFT,REJECT'
      //     })
      // }
      this.actionList = (actionList)
    },
    setbuttomlistV(curd) {
      let buttomlist = []
      let noticePr = `${curd}:noticePr`//通知磅房按钮
      let applyPay = `${curd}:applyPay`//付款申请
      let CustomerRefund = `${curd}:CustomerRefund`//客户退款
      let applyRecv = `${curd}:applyRecv`//收款登记
      let SupplierRefund = `${curd}:SupplierRefund`//供应商退款
      let settlePay = `${curd}:settlePay`//付款结算
      let settleRecv = `${curd}:settleRecv`//收款结算
      let settleRecvFreight = `${curd}:settleRecvFreight`//销售运费结算

      let freightSettlement = `${curd}:freightSettlement`//销售运费结算

      if (this.perms[noticePr]) {
        buttomlist.push({
          config: {
            type: '2'
          },
          show: true,
          active: false,
          label: '通知磅房',
          click: this.noticePoundroom,
          type: 'noticePr'
        })
      }
      if (this.perms[applyPay]) {
        buttomlist.push(
          {
            config: {
              type: '1'
            },
            show: true,
            label: '付款申请',
            click: this.PaymentApplication,
            type: 'payapply'
          })
      }
      if (this.perms[CustomerRefund]) {
        buttomlist.push(
          {
            config: {
              type: '1'
            },
            show: true,
            label: '客户退款',
            click: this.CustomerRefundApply,
            type: 'payapply'
          })
      }

      if (this.perms[applyRecv]) {
        buttomlist.push(
          {
            config: {
              type: '1'
            },
            show: true,
            label: '收款登记',
            click: this.CollectionApply,
            type: 'applyRecv'
          })
      }

      if (this.perms[SupplierRefund]) {
        buttomlist.push(
          {
            config: {
              type: '1'
            },
            show: true,
            label: '供应商退款',
            click: this.SupplierRefundApply,
            type: 'SupplierRefund'
          })
      }


      if (this.perms[freightSettlement]) {
        buttomlist.push({
          config: {
            type: '2'
          },
          show: true,
          active: false,
          label: '运费结算',
          click: this.purchasesettlePayFN,
          type: 'freightSettlement'
        })
      }


      if (this.perms[settlePay]) {
        buttomlist.push({
          config: {
            type: '2'
          },
          show: true,
          active: false,
          label: '付款结算',
          click: this.settlePayFN,
          type: 'settlePay'
        })
      }
      if (this.perms[settleRecv]) {
        buttomlist.push({
          config: {
            type: '2'
          },
          show: true,
          active: false,
          label: '收款结算',
          click: this.settleRecvFN,
          type: 'settleRecv'
        })
      }
      if (this.perms[settleRecvFreight]) {
        buttomlist.push({
          config: {
            type: '2'
          },
          show: true,
          active: false,
          label: '销售运费结算',
          click: this.settleRecvFreightFN,
          type: 'settleRecvFreight'
        })
      }


      this.buttomlist = (buttomlist)
    },
    getList() {
      this.$refs[this.curd].$emit('refresh')
    },

    handleFilter(filters) {
      this.filtersSeach = filters
      this.$refs[this.curd].searchChange({ ...this.listQuery, ...filters })
    },
    handleFilterReset() {
      this.$refs[this.curd].searchChange()
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },

    handleImpiort() {
      this.dialogStatus = 'create'
      this.dialogFormVisibleV = true
    },


    handleUpdate(item) {
      this.entityForm = { ...item }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
    },

    GotoAudit(item) {//财务管理 审核
      this.entityForm = { ...item }
      this.dialogStatus = 'review'
      this.dialogFormVisible = true
    },
    handleClose() {
      this.resetVariant()
    },
    async handleDel(row) {
      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await this.model.deleteId(row.id)
        if (res) {
          this.$message({ type: 'success', message: '删除成功!' })
          this.getList()
          this.getnumber()
        }
      }).catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },
    // async tobeCollected(row) {
    //     this.$confirm('是否确认支付', '提示', {
    //         confirmButtonText: '确定',
    //         cancelButtonText: '取消',
    //         type: 'warning'
    //     }).then(async () => {
    //         const res = await this.model.Collected({ id: row.id, reviewMessage: row.reviewMessage })
    //         if (res) {
    //             this.$message({ type: 'success', message: '支付成功!' })
    //             this.getList()
    //         }
    //     }).catch(() => this.$message({ type: 'info', message: '支付取消' }))
    // },
    async SendSMSbtn(id, phone, SMScontent) {
      // this.$confirm('是否确认发送短信', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      // }).then(async () => {
      const res = await this.model.SendSMSfn({ id: id, phnoe: phone, SMScontent: SMScontent })
      if (res) {
        this.$message({ type: 'success', message: '发送成功!' })
        this.getList()
      }
      // }).catch(() => this.$message({ type: 'info', message: '发送取消' }))
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.entityForm = { ...this.model.model }
    },
    async saveEntityForm(entityForm, type) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        this.entityFormLoading = true
        if (type !== undefined) {//物资信息盘库
          if (!this.entityForm.newAmount) {
            this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
            this.entityFormLoading = false
            return
          }
          this.entityForm.itemId = this.entityForm.id
          this.entityForm.id = ''
          const res = await Func.fetch(this.model.setNumber, { ...this.entityForm })
          this.entityFormLoading = false
          if (res) {
            this.getList()
            this.baseVariant && this.baseVariant()
            this.saveMemoryField && this.saveMemoryField()
            this.resetVariant()
            this.dialogFormVisible = false
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else {
          const res = await Func.fetch(this.model.save, { ...this.entityForm })
          this.entityFormLoading = false
          if (res) {
            this.getList()
            this.baseVariant && this.baseVariant()
            this.saveMemoryField && this.saveMemoryField()
            this.resetVariant()
            this.dialogFormVisible = false
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        }

      }
    },



    // setcontract(row) {
    //   if (this.contractEntity.options.length > 0) {
    //     let Things = this.contractEntity.options;
    //     let name = '';
    //     for (var i = 0; i < Things.length; i++) {
    //       if (row.supplierId === Things[i].supplierId) {
    //         for (var g = 0; g < Things[i].contractBuyList.length; g++) {
    //           if (Things[i].contractBuyList[g].id === row.contractId) {
    //             name = Things[i].contractBuyList[g].displayName + '/' + Things[i].supplierName;
    //           }
    //         }
    //       }
    //     }
    //     console.log('name', name)
    //     return name;
    //   }
    // },

    async export(unknown, type) {
      const dictHashMap = {
        1: '销售',  // 数字1映射为"销售"
        2: '采购'   // 数字2映射为"采购"
      }
      const batchNo = parseTime(new Date(), '{y}-{m}-{d}')
      const header = [] // 头部
      const columns = [] // prop属性
      const keys = []
      this.model.tableOption.columns.forEach(val => {
        if (!val.type && val.isShow) {
          if (!val.noExport) {
            const tHeader = val.isHeader ? val.isHeader + val.label : val.label
            header.push(tHeader)
            columns.push(val.prop || val.slot)
          }
          if (val.slot) {
            keys.push(val.slot)
          }
          if (val.format) {
            keys.push(val.prop)
          }
        }
      })
      let list
      if (type === `all`) {
        try {
          await this.$confirm('当前数据量比较大, 是否继续导出?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
        } catch (e) {
          return false
        }
        const res = await this.model.page({ ...this.listQuery, size: 10000 }, true)
        if (res.data.records.length) {
          list = res.data.records
        } else {
          return this.$message.success('当前没有数据可以导出(⊙⊙！)')
        }
      } else {
        list = JSON.parse(JSON.stringify(this.$refs[this.curd].dataList))
        if (!list.length) return this.$message.success('当前没有数据可以导出(⊙⊙！)')
      }

      list.forEach(v => {
        // 处理type字段转换
        if (v.type && dictHashMap[v.type]) {
          v.type = dictHashMap[v.type]
        }





        keys.forEach(key => {
          if (dictHashMap[v[key]]) {
            v[key] = dictHashMap[v[key]]
          }
        })

        // 处理 contractId 字段，替换为 setcontract 显示的内容
        if (v.contractId) {
          v.contractId = this.setcontract(v);
        }

      })





      // list.forEach(v => {
      //   keys.forEach(key => {
      //     if (dictHashMap[v[key]]) {
      //       v[key] = dictHashMap[v[key]]
      //     }
      //   })
      // })
      try {
        let title = this.title + '-' + batchNo
        title += type === 'all' ? '-全部' : ''
        exportExcel({ title, header: header, columns: columns, list: list })
        this.$message.success(`导出任务创建成功 (´・ω・｀)`)
      } catch (error) {
        this.$message.success('导出任务创建失败(o´▽`o)ﾉ')
      }
    },
    downloadTemplateHandler() {
      const header = []
      this.model.tableOption.columns.forEach(val => {
        if (!val.type && !val.noExport) {
          header.push(val.label)
        }
      })
      exportExcel({ title: this.title, header })
    },
    // 合同审核通过
    async Approved(id, reviewMessage, hasFax, price, cleanMt, cleanAd, cleanStd, procX, cleanVdaf, crc, procG, procY, recovery, qualCsr, macR0, macS, remarks) {
      const res = await this.model.getApproved({
        id: id,
        reviewMessage: reviewMessage,
        hasFax: hasFax,
        price: price,
        cleanMt: cleanMt,
        cleanAd: cleanAd,
        cleanStd: cleanStd,
        procX: procX,
        cleanVdaf: cleanVdaf,
        crc: crc,
        procG: procG,
        procY: procY,
        recovery: recovery,
        qualCsr: qualCsr,
        macR0: macR0,
        macS: macS,
        remarks: remarks
      })
      if (res) {
        this.$message({ type: 'success', message: '审核成功!' })
        this.getList()
        this.getnumber()
        this.resetVariant()
        this.dialogFormVisible = false
      }
    },
    // 合同审核通过
    async FinancePass(id, reviewMessage, hasFax, price, cleanMt, cleanAd, cleanStd, procX, cleanVdaf, crc, procG, procY, recovery, qualCsr, macR0, macS, remarks, curFlowCode) {
      const res = await this.model.getfinancePass({
        id: id,
        reviewMessage: reviewMessage,
        hasFax: hasFax,
        price: price,
        cleanMt: cleanMt,
        cleanAd: cleanAd,
        cleanStd: cleanStd,
        procX: procX,
        cleanVdaf: cleanVdaf,
        crc: crc,
        procG: procG,
        procY: procY,
        recovery: recovery,
        qualCsr: qualCsr,
        macR0: macR0,
        macS: macS,
        remarks: remarks,
        curFlowCode: curFlowCode
      })
      if (res) {
        this.$message({ type: 'success', message: '审核成功!' })
        this.getList()
        this.getnumber()
        this.resetVariant()
        this.dialogFormVisible = false
      }
    },
    // 审核不通过
    async ApprovednoPas(id, reviewMessage, hasFax, price, cleanMt, cleanAd, cleanStd, procX, cleanVdaf, crc, procG, procY, recovery, qualCsr, macR0, macS, remarks, curFlowCode) {
      const res = await this.model.getApprovednoPas({
        id: id,
        reviewMessage: reviewMessage,
        hasFax: hasFax,
        price: price,
        cleanMt: cleanMt,
        cleanAd: cleanAd,
        cleanStd: cleanStd,
        procX: procX,
        cleanVdaf: cleanVdaf,
        crc: crc,
        procG: procG,
        procY: procY,
        recovery: recovery,
        qualCsr: qualCsr,
        macR0: macR0,
        macS: macS,
        remarks: remarks,
        curFlowCode: curFlowCode
      })
      if (res) {
        this.$message({ type: 'success', message: '审核不通过成功!' })
        this.getList()
        this.resetVariant()
        this.getnumber()
        this.dialogFormVisible = false
      }
    },
    async getnumber() {
      const res = await this.model.getcountByState()
      this.countList = res.data
    },
    // 财务管理   //财务报表付款申请
    async SubmitReview(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        const res = await this.model.getApproved(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '提交成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.getnumber()
          this.resetVariant()
          this.dialogFormVisible = false
        }
      }
    },
    //保存草稿
    async SaveDraft(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        const res = await this.model.SaveDraftfn(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '保存草稿成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.getnumber()
          this.dialogFormVisible = false
        }
      }
    },
    //待审核
    waitforReviewed(type) {
      this.activetype = type
      this.getnumber()
    },

    PaymentApplication() {
      this.isshow = false
      this.dialogStatus = 'payapply'
      this.entityForm.applyType = 'TRADE'
      this.ispayWay = ''
      this.dialogFormVisible = true
    },
    //通知磅房
    async noticePoundroom(row) {
      this.dialogFormVisible = true
      this.entityForm.type = 'SELL'
      this.isshow = false
      this.dialogStatus = 'create'
      // this.dialogVisible = true
    },
    //付款款结算
    settlePayFN() {
      this.isshow = false
      // this.dialogStatus = 'settlePay'
      this.dialogStatus = 'create'
      this.isallShow = false
      this.isFreightallShow = false
      this.entityForm.applyType = 'TRADE'
      this.dialogFormVisible = true
    },
    //   采购运费结算
    purchasesettlePayFN() {
      this.isshow = false
      this.dialogStatus = 'create'
      this.isallShow = false
      this.isFreightallShow = false
      this.entityForm.applyType = 'CARRIAGE'
      this.dialogFormVisible = true
    },


    //销售运费结算
    settleRecvFreightFN() {
      this.getFreightLeftData()
      this.isshow = false
      this.isallShow = false
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },
    //收款结算
    settleRecvFN() {
      this.isshow = false
      this.isallShow = false
      // this.dialogStatus = 'settleRecv'
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },
    //收款单 收款申请
    CollectionApply() {
      this.isshow = false
      this.dialogStatus = 'applyRecv'
      this.dialogFormVisible = true
      this.entityForm.applyType = 'TRADE'
    },
    SupplierRefundApply() {
      this.isshow = false
      // this.dialogStatus = 'applyRecv'
      this.dialogFormVisible = true
      this.entityForm.applyType = 'REFUND'
    },
    CustomerRefundApply() {
      this.isshow = false
      this.dialogStatus = 'payapply'
      this.ispayWay = ''
      this.entityForm.applyType = 'REFUND'
      this.dialogFormVisible = true
    }

  }
}
