import store from '@/store'

/**
 * @param {Array} value
 * @returns {Boolean}
 * @example see @/views/permission/directive.vue
 */
export default function checkPermission(value) {
    const perms = store.getters && store.getters.perms
    if (value && value instanceof Array && value.length > 0) {
        if (value.includes('*')) {
            return true
        }
        const permissionFlag = value
        let hasPermission = false
        hasPermission = permissionFlag.every((item) => {
            return !!perms[item]
        })
        return hasPermission
    } else {
        console.error(`need perms! Like v-permission="['GET /aaa','POST /bbb']"`)
        return false
    }
}
