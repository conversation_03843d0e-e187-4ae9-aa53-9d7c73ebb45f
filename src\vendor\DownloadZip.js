import JSZip from 'jszip'
import fileSaver from 'file-saver'
import axios from 'axios'

const getFile = url => {
    return new Promise((resolve, reject) => {
        axios({
            method: 'get',
            url,
            responseType: 'arraybuffer'
        }).then(data => {
            resolve(data.data)
        }).catch(error => {
            reject(error.toString())
        })
    })
}

export function downloadZip(list, title) {
    const zip = new JSZip();
    const cache = {};
    const promises = [];
    list.forEach(item => {
        // item为对象，其中url代表下载地址
        if (item.url) {
            if (item.url.indexOf('img.tpt365.cn') > 0) {
                item.url = JSON.parse(JSON.stringify(item.url.replace('img.tpt365.cn', 'tp-pic.oss-cn-shanghai.aliyuncs.com')))
            }
            const promise = getFile(item.url).then(data => {
                let nameArr = item.url.split('/')
                var file_name = nameArr[nameArr.length - 1] // 获取文件名
                zip.file(file_name, data, { binary: true })
                cache[file_name] = data
            })
            promises.push(promise)
        }
    })
    Promise.all(promises).then(() => {
        zip.generateAsync({ type: "blob" }).then(content => { // 生成二进制流
            fileSaver.saveAs(content, title) // 利用file-saver保存文件
            // console.log('下载成功', content, title)
        })
    })
}
