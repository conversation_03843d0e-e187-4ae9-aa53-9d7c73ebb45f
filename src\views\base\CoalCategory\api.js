import request from '@/utils/request'
import { name } from './model'

export function page(query) {
  return request({
    url: `${name}/page`,
    method: 'get',
    params: query
  })
}

export function list(query) {
  return request({
    url: `${name}/list`,
    method: 'get',
    params: query
  })
}

export function get(id) {
  return request({
    url: `${name}/get`,
    method: 'get',
    params: { id }
  })
}

/**
 * 全部煤源
 * @returns {*}
 */
export async function tree() {
  const result = await request({
    // url: `${name}/tree`,
    url: `/cwe/a/coalDatabaseAll/allList`,
    method: 'get',
    params: {}
  })
  result.data = result.data.map((item) => {
    return {
      ...item,
      label: item.name,
      id: item.id + '',
      value: item.id
    }
  })

  return result
}
/**
 * 外部煤源
 * @returns {*}
 */
export async function treeOutside() {
  const result = await request({
    url: `/cwe/a/coal/coalList`,
    method: 'get',
    params: {}
  })
  result.data = result.data.map((item) => {
    return {
      ...item,
      label: item.name,
      id: item.id + '',
      value: item.id
    }
  })
  return result
}
/**
 * 内部煤源
 * @returns {*}
 */
export async function treeInside() {
  const result = await request({
    url: `/cwe/a/stock/stockList`,
    method: 'get',
    params: {}
  })
  result.data = result.data.map((item) => {
    return {
      ...item,
      label: item.name,
      id: item.id + '',
      value: item.id
    }
  })
  return result
}

/**
 * 内部煤源
 * @returns {*}
 */
export function buytreeInside() {
  return request({
    url: `${name}/buy-inside-tree`,
    method: 'get',
    params: {}
  })
}

/**
 * 价格曲线中获取的全部品名
 * @returns {*}
 */
export function listcoalName() {
  return request({
    url: `/a/coalSourceInside/list-coalName`,
    method: 'get',
    params: {}
  })
}
