import BaseModel from '@/components/Common/SnProTable/model'

export const name = `/cwe/a/coalCategory`

const createFormConfig = () => {
  return {
    filters: [
      {
        label: '煤种名称',
        prop: 'filter_LIKES_name'
      },
      {
        hide: true,
        prop: 'orderBy',
        defaultValue: 'createDate'
      },
      {
        hide: true,
        prop: 'orderDir',
        defaultValue: 'desc'
      }
    ]
  }
}

const createTableConfig = () => {
  return {
    showOpt: true, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: false, // 显示选择列

    columns: [
      {
        prop: 'name',
        label: '煤种名称'
      }
    ]
  }
}

class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }

  get(id) {
    return super.request({
      url: `${name}/get`,
      method: 'get',
      params: {id}
    })
  }
}

export default new Model()
