import request from '@/utils/request'
import { name } from './model'

export function page(query) {
  return request({
    url: `${name}/page`,
    method: 'get',
    params: query
  })
}

export function list(query) {
  return request({
    url: '/cwe/a/coalDatabaseAll/coalNames',
    method: 'get',
    params: query
  })
}

export function get(id) {
  return request({
    url: `${name}/get`,
    method: 'get',
    params: { id }
  })
}
