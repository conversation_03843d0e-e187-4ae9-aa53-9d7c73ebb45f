<template>
  <div class="app-container">
    <SnProTable :ref="pageRef" :actions="actions" :model="model">
      <template #factory="{ col }">
        <el-table-column v-bind="col">
          <template #default="{ row }"></template>
        </el-table-column>
      </template>

      <template #opt="{ row }">
        <el-button v-if="checkPermission([permissions.save])" type="text" @click="handleUpdate(row)">编辑 </el-button>

        <el-dropdown @command="onOptCommand($event, row)">
          <el-button type="text">
            <el-divider direction="vertical"></el-divider>
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-if="checkPermission([permissions.remove])" command="remove" icon="el-icon-delete">删除 </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </SnProTable>

    <FormModal
      v-if="addInfo.visitable"
      v-model="addInfo.visitable"
      :model="model"
      :optName="addInfo.optName"
      :record="addInfo.record"
      useApi
      @ok="getList"
    />
  </div>
</template>

<script>
import model from './model'
import FormModal from './components/FormModal.vue'
import checkPermission from '@/utils/permission'
import TipModal from '@/utils/modal'
import { createPermissionMap } from '@/utils'

export default {
  name: 'CoalName',
  components: {
    FormModal
  },
  data() {
    return {
      model,
      pageRef: 'page',
      permissions: createPermissionMap('coalName'),
      addInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      }
    }
  },
  computed: {
    actions() {
      return [
        {
          type: 'add',
          text: '新增',
          hasPermission: [this.permissions.save],
          onClick: async (item) => {
            this.setModal('addInfo')
          }
        }
      ]
    }
  },
  created() {
    console.log(this.$route)
  },
  methods: {
    async onDelFlagChange(row, val, info) {
      try {
        await this.model.save({ ...row, delFlag: val })
        TipModal.msgSuccess(`操作成功`)
        this.getList()
      } catch (e) {
        TipModal.msgError('操作失败')
      }
    },
    onOptCommand(command, row) {
      if (command === 'remove') {
        this.handleRemove(row)
      }
    },

    checkPermission,

    // 新增时record置为空
    setModal(target, optName = 'create', record = {}) {
      this[target].visitable = true
      this[target].optName = optName
      if (optName) this[target].record = { ...record }
    },

    async handleRemove({ id }) {
      try {
        await this.model.remove({ id, useConfirm: true })
        TipModal.msgSuccess(`删除成功`)
        this.getList()
      } catch (error) {}
    },
    async handleUpdate(row) {
      try {
        // const {data} = await this.model.get(row.id)
        // this.setModal('addInfo', 'update', data)
        this.setModal('addInfo', 'update', row)
      } catch (error) {
        console.log(error, 'error')
      }
    },
    async handleView(row) {
      try {
        const { data } = await this.model.get(row.id)
        this.setModal('addInfo', 'view', data)
      } catch (error) {
        console.log(error, 'error')
      }
    },
    getList() {
      this.$refs[this.pageRef].getList()
    }
  }
}
</script>

<style lang="scss"></style>
