import BaseModel from '@/components/Common/SnProTable/model'

export const name = `/a/coalName`

const createFormConfig = () => {
  return {
    filters: [
      {
        label: '品名',
        prop: 'filter_LIKES_name',
        labelWidth: 40
      },
      {
        label: '煤种名称',
        prop: 'filter_LIKES_categoryName'
      },
      {
        hide: true,
        prop: 'orderBy',
        defaultValue: 'createDate'
      },
      {
        hide: true,
        prop: 'orderDir',
        defaultValue: 'desc'
      }
    ]
  }
}

const createTableConfig = () => {
  return {
    showOpt: true, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: false, // 显示选择列
    columns: [
      {
        prop: 'name',
        label: '品名'
      },
      {
        prop: 'shortname',
        label: '简称'
      },
      {
        prop: 'categoryName',
        label: '煤种名称'
      },
      {
        prop: 'code',
        label: '编码'
      }
    ]
  }
}

class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }
  page(query) {
    query.filter_EQS_source = 'inside'
    return super.request({
      url: `${name}/page`,
      method: 'get',
      params: query
    })
  }

  get(id) {
    return super.request({
      url: `${name}/get`,
      method: 'get',
      params: { id }
    })
  }
}

export default new Model()
