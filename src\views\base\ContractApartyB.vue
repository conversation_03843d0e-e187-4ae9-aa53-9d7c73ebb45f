<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" @import="handleImpiort"
                  :showImport="perms[`${curd}:addimport`]||false" />
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="130">
      <el-table-column label="名称" slot="name" prop="name" width="400">
        <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{ scope.row.name }}</span></template>
      </el-table-column>

      <el-table-column label="类型" slot="type" prop="type">
        <template slot-scope="scope" v-if="scope.row.type">
          <el-tag :type="(scope.row.type=='SELL' ? 'danger':(scope.row.type == 'BUY' ? 'warning' : (scope.row.type == 'ALL' ? 'success' : '')))"
                  size="mini">
            {{scope.row.type == 'SELL' ? '销售' : (scope.row.type == 'BUY' ? '采购' : (scope.row.type == 'ALL' ? '采购销售' :'')) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" max-width="100" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" v-if="perms[`${curd}:update`] || false" color="#FF9639"
                  @click="handleUpdate(scope.row)">编辑</el-tag>
          <!-- <el-tag class="opt-btn" color="#FF726B" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`] || false">删除
          </el-tag> -->
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" :rules="rules"
               :disabled="dialogStatus === 'watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="名称" prop="name">
                    <el-input v-model="entityForm.name" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="类型" prop="type">
                    <dict-select v-model="entityForm.type" type="b_contract_party_type" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="简称" prop="aliasName">
                    <el-input v-model="entityForm.aliasName" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">其他信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus !== 'watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`] || false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/ContractApartyB/ContractApartyB'
import { createMemoryField } from '@/utils/index'
export default {
  name: 'ContractApartyB',
  mixins: [Mixins],
  data() {
    return {
      curd: 'ContractApartyB',
      model: Model,
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'name',
            filter: 'filter_LIKES_name',
            component: 'select-name',
            props: { placeholder: '请选择名称', clearable: true }
          }
          //   {
          //     prop: 'type',
          //     filter: 'filter_EQS_type',
          //     props: {
          //       placeholder: '请输入编码',
          //       clearable: true,
          //     },
          //   },
        ]
      },
      entityForm: { ...Model.model, filter_EQS_type: '', type: '' },
      rules: {
        name: { required: true, message: '请输入名称', trigger: 'blur' },
        // code: { required: true, message: '请输入编码', trigger: 'blur' },
        type: { required: true, message: '请选择类型', trigger: 'blur' }
        // coalCategory: { required: true, message: '请选择煤种类型', trigger: 'change' },
      },
      actions: [],
      sourceOptions: {
        show: true,
        list: [{ label: '自产' }, { label: '采购煤' }]
      },
      memoryEntity: { fields: {}, triggered: false }
    }
  },
  created() {
    this.memoryEntity.fields = createMemoryField({ fields: ['name', 'coalCategory', 'source'], target: this.entityForm })
  },
  watch: {
    // 'entityForm.isPublicBoolean': {
    //   handler(v) {
    //     this.entityForm.isPublic = v ? 'Y' : 'N'
    //   },
    // },
    // 'entityForm.source'(v) {
    //   if (v === '自产') {
    //     this.sourceOptions.show = true
    //   } else {
    //     this.sourceOptions.show = false
    //     this.hiddenQuality()
    //   }
    // },
  },
  methods: {
    // baseVariant() {
    //   if (!this.memoryEntity.triggered) {
    //     this.entityForm = { ...this.model.model }
    //   } else {
    //     this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
    //   }
    // },
    // hiddenQuality() {
    //   const { id, name, coalCategory, source, code, type, isPublicBoolean } = this.entityForm
    //   this.entityForm = { ...Model.model, id, name, coalCategory, source, code, type, isPublicBoolean }
    // },
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// ::v-deep .el-table .cell {
//   display: flex;
//   justify-content: center;
// }
::v-deep .el-dialog__body {
  height: 65vh;
}
.tags {
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    margin: 0 3px;
  }
  .tag {
    width: 30px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    // margin: 0 auto;
    // padding: 10px;
    // line-height: 0px;
  }
}
</style>
<style scoped>
/* .opt-btn {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
}
.opt-btnV {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
} */
</style>