<template>
  <div class="app-container">
    <SnProTable
      :ref="pageRef"
      :actions="actions"
      :afterFetch="afterFetch"
      :beforeFetch="beforeFetch"
      :model="model"
      :showSearchForm="false"
    >
      <template v-for="item in getColSlots" #[item.prop]="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span v-show="!row.isEdit">{{ row[col.prop] }} </span>
            <el-input v-show="row.isEdit" v-model="row[`${col.prop}${editKey}`]"
                      :placeholder="`请输入${item.label}`"></el-input>
          </template>
        </el-table-column>
      </template>

      <!-- 操作-->
      <template #opt="{ row,...rest }">
        <template v-if="checkPermission([permissions.save])">
          <el-button v-if="!row.isEdit" type="text" @click="handleRow(row, 'edit', rest)">
            编辑
          </el-button>
          <template v-else>
            <el-button :loading="row.loading" type="text" @click="handleRow(row, 'save', rest)">
              保存
            </el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button type="text" @click="handleRow(row, 'cancel', rest)">
              取消
            </el-button>
          </template>
        </template>
      </template>
    </SnProTable>
  </div>
</template>

<script>
import model from './model'
import checkPermission from '@/utils/permission'
import TipModal from '@/utils/modal'
import {createPermissionMap} from '@/utils'
import SnDateSelect from '@/components/Common/SnDateSelect/index.vue'
import {getDate} from '@/utils/dateUtils'
import {isString} from '@/utils/is'
import SnProTable from '@/components/Common/SnProTable/index.vue'

export default {
  name: 'CPCoalCategory',
  components: {
    SnProTable,
    SnDateSelect
  },
  data() {
    return {
      model,
      pageRef: 'page',
      permissions: createPermissionMap('costPerformanceCoalCategory'),
      addInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      },
      editKey: '_edit',
      editPropList: [
        {
          label: '灰分Ad%',
          prop: 'cleanAd'
        },
        {
          label: '硫分St,d%',
          prop: 'cleanStd'
        },
        {
          label: '粘结G',
          prop: 'procG'
        },
        {
          label: '挥发分Vdaf%',
          prop: 'cleanVdaf'
        },
        {
          label: 'Y/mm',
          prop: 'procY'
        }
      ]
    }
  },

  computed: {
    getColSlots() {
      const exclude = []
      return this.editPropList.filter((item) => !exclude.includes(item.prop))
    },
    actions() {
      return []
    }
  },

  watch: {},
  methods: {
    getDate,
    afterFetch(data) {
      // eslint-disable-next-line no-unused-expressions
      data?.forEach((item) => {
        item.loading = false
        item.isEdit = false
        this.editPropList.forEach((value) => {
          item[`${value.prop}${this.editKey}`] = item[value.prop]
        })
      })
      return {
        list: data
      }
    },

    beforeFetch(params) {
      return {
        ...params
      }
    },

    checkPermission,
    // 新增时record置为空
    setModal(target, optName = 'create', record = {}) {
      this[target].visitable = true
      this[target].optName = optName
      if (optName) this[target].record = {...record}
    },

    async handleRow(row, type, ...rest) {
      if (type === 'edit') {
        row.isEdit = true
      }
      if (type === 'save') {
        row.loading = true
        const rowIndex = rest[0]['$index'] + 1
        try {
          const resultRow = {...row}
          // 处理数据
          for (const [k, v] of Object.entries(resultRow)) {
            if (v === null || v === '/') resultRow[k] = undefined
          }
          this.editPropList.forEach((value) => {
            resultRow[value.prop] = resultRow[`${value.prop}${this.editKey}`]
            if (isString(resultRow[value.prop])) {
              // 去除空格，中文逗号，中文括号
              resultRow[value.prop] = resultRow[value.prop]
                .trim()
                .replaceAll('。', '.')
                .replaceAll('，', ',')
                .replaceAll('（', '(')
                .replaceAll('）', ')')
                .replaceAll('【', '[')
                .replaceAll('】', ']')
            }
          })
          await this.model.save({...resultRow})
          TipModal.msgSuccess(`第${rowIndex}行编辑成功。`)

          // 刷新界面
          this.getList()
        } catch (e) {
        }
        row.isEdit = false
        row.loading = false
      }

      if (type === 'cancel') {
        this.editPropList.forEach((value) => {
          row[`${value.prop}${this.editKey}`] = row[value.prop]
        })
        row.isEdit = false
      }
    },

    getList(clear = false) {
      if (clear) {
        this.$refs[this.pageRef].clearSelect(true)
      } else {
        this.$refs[this.pageRef].getList()
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
