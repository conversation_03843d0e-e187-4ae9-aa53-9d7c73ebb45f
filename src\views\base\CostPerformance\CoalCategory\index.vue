<template>
  <div class="app-container">
    <SnProTable
      :ref="pageRef"
      :actions="actions"
      :afterFetch="afterFetch"
      :beforeFetch="beforeFetch"
      :model="model"
      :showSearchForm="false"
    >
      <template v-for="item in getColSlots" #[item.prop]="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span v-show="!row.isEdit">{{ row[col.prop] }} </span>
            <el-input v-show="row.isEdit" v-model="row[`${col.prop}${editKey}`]"
                      :placeholder="`请输入${item.label}`"></el-input>
          </template>
        </el-table-column>
      </template>

      <!-- 操作-->
      <template #opt="{ row,...rest }">
        <template v-if="checkPermission([permissions.save])">
          <el-button v-if="!row.isEdit" type="text" @click="handleRow(row, 'edit', rest)">
            编辑
          </el-button>
          <template v-else>
            <el-button :loading="row.loading" type="text" @click="handleRow(row, 'save', rest)">
              保存
            </el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button type="text" @click="handleRow(row, 'cancel', rest)">
              取消
            </el-button>
          </template>
        </template>
        <template v-if="!row.isEdit && row.id">
          <el-divider direction="vertical"></el-divider>
          <el-button type="text" style="color: #f56c6c" @click="handleRow(row, 'delete', rest)">
            删除
          </el-button>
        </template>
      </template>
    </SnProTable>
  </div>
</template>

<script>
import model from './model'
import checkPermission from '@/utils/permission'
import TipModal from '@/utils/modal'
import {createPermissionMap} from '@/utils'
import SnDateSelect from '@/components/Common/SnDateSelect/index.vue'
import {getDate} from '@/utils/dateUtils'
import {isString} from '@/utils/is'
import SnProTable from '@/components/Common/SnProTable/index.vue'

export default {
  name: 'CPCoalCategory',
  components: {
    SnProTable,
    SnDateSelect
  },
  data() {
    return {
      model,
      pageRef: 'page',
      permissions: createPermissionMap('costPerformanceCoalCategory'),
      addInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      },
      editKey: '_edit',
      editPropList: [
        {
          label: '煤种',
          prop: 'coalCategoryName'
        },
        {
          label: '灰分Ad%',
          prop: 'cleanAd'
        },
        {
          label: '硫分St,d%',
          prop: 'cleanStd'
        },
        {
          label: '粘结G',
          prop: 'procG'
        },
        {
          label: '挥发分Vdaf%',
          prop: 'cleanVdaf'
        },
        {
          label: 'Y/mm',
          prop: 'procY'
        }
      ]
    }
  },

  computed: {
    getColSlots() {
      const exclude = []
      return this.editPropList.filter((item) => !exclude.includes(item.prop))
    },
    actions() {
      return []
    }
  },

  watch: {},
  methods: {
    getDate,
    afterFetch(data) {
      console.log('afterFetch 接收到的数据:', data)
      console.log('数据排序检查 - 前3条数据的创建时间:')
      if (data && data.length > 0) {
        data.slice(0, 3).forEach((item, index) => {
          console.log(`第${index + 1}条:`, {
            coalCategoryName: item.coalCategoryName,
            createDate: item.createDate,
            createTime: item.createTime,
            id: item.id
          })
        })
      }

      // eslint-disable-next-line no-unused-expressions
      data?.forEach((item) => {
        item.loading = false
        item.isEdit = false
        this.editPropList.forEach((value) => {
          item[`${value.prop}${this.editKey}`] = item[value.prop]
        })
      })

      const resultData = data || []

      // 对数据进行排序（按创建时间倒序）
      resultData.sort((a, b) => {
        // 尝试多个可能的时间字段
        const timeA = a.createDate || a.createTime || a.updateDate || a.updateTime || ''
        const timeB = b.createDate || b.createTime || b.updateDate || b.updateTime || ''

        // 如果有时间字段，按时间倒序排列
        if (timeA && timeB) {
          return new Date(timeB) - new Date(timeA)
        }

        // 如果有ID字段，按ID倒序排列（通常ID越大越新）
        if (a.id && b.id) {
          return b.id - a.id
        }

        return 0
      })

      console.log('排序后的数据（前3条）:')
      resultData.slice(0, 3).forEach((item, index) => {
        console.log(`排序后第${index + 1}条:`, {
          coalCategoryName: item.coalCategoryName,
          createDate: item.createDate,
          createTime: item.createTime,
          id: item.id
        })
      })

      // 始终在顶部添加空白行
      const blankRow = {
        id: null,
        coalCategoryName: '',
        cleanAd: '',
        cleanStd: '',
        procG: '',
        cleanVdaf: '',
        procY: '',
        loading: false,
        isEdit: false,
        isBlankRow: true // 标记为空白行
      }

      // 为空白行初始化编辑字段
      this.editPropList.forEach((value) => {
        blankRow[`${value.prop}${this.editKey}`] = blankRow[value.prop]
      })

      // 将空白行添加到数据开头
      resultData.unshift(blankRow)

      console.log('afterFetch 返回的数据:', resultData)

      return {
        list: resultData
      }
    },

    beforeFetch(params) {
      console.log('beforeFetch 发送的参数:', params)
      return {
        ...params
      }
    },

    checkPermission,
    // 新增时record置为空
    setModal(target, optName = 'create', record = {}) {
      this[target].visitable = true
      this[target].optName = optName
      if (optName) this[target].record = {...record}
    },

    async handleRow(row, type, ...rest) {
      if (type === 'edit') {
        row.isEdit = true
      }
      if (type === 'save') {
        row.loading = true
        const rowIndex = rest[0]['$index'] + 1
        try {
          const resultRow = {...row}
          // 处理数据
          for (const [k, v] of Object.entries(resultRow)) {
            if (v === null || v === '/') resultRow[k] = undefined
          }
          this.editPropList.forEach((value) => {
            resultRow[value.prop] = resultRow[`${value.prop}${this.editKey}`]
            if (isString(resultRow[value.prop])) {
              // 去除空格，中文逗号，中文括号
              resultRow[value.prop] = resultRow[value.prop]
                .trim()
                .replaceAll('。', '.')
                .replaceAll('，', ',')
                .replaceAll('（', '(')
                .replaceAll('）', ')')
                .replaceAll('【', '[')
                .replaceAll('】', ']')
            }
          })

          console.log('保存数据:', resultRow)
          const saveResult = await this.model.save({...resultRow})
          console.log('保存结果:', saveResult)

          const actionText = resultRow.id ? '编辑' : '新增'
          TipModal.msgSuccess(`第${rowIndex}行${actionText}成功。`)

          // 如果是空白行保存，在空白行下面插入新数据
          if (row.isBlankRow) {
            console.log('空白行保存，插入新数据到前面')

            // 创建新的数据行（基于保存结果或当前数据）
            const newDataRow = {
              ...(saveResult && saveResult.data ? saveResult.data : resultRow),
              loading: false,
              isEdit: false
            }

            // 为新数据行初始化编辑字段
            this.editPropList.forEach((value) => {
              newDataRow[`${value.prop}${this.editKey}`] = newDataRow[value.prop]
            })

            // 获取当前表格数据
            const currentData = this.$refs[this.pageRef].$refs.table.data || []

            // 在空白行（索引0）后面插入新数据
            currentData.splice(1, 0, newDataRow)

            // 清空空白行的数据
            this.editPropList.forEach((value) => {
              row[value.prop] = ''
              row[`${value.prop}${this.editKey}`] = ''
            })

            // 更新表格数据
            this.$refs[this.pageRef].$refs.table.data = currentData
          } else {
            // 对于编辑操作，刷新整个列表
            console.log('编辑操作，刷新列表数据')
            this.getList()
          }
        } catch (e) {
          console.error('保存失败:', e)
          TipModal.msgError('保存失败，请重试')
        }
        row.isEdit = false
        row.loading = false
      }

      if (type === 'cancel') {
        this.editPropList.forEach((value) => {
          row[`${value.prop}${this.editKey}`] = row[value.prop]
        })
        row.isEdit = false
      }

      if (type === 'delete') {
        this.$confirm('确定要删除这条记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            await this.model.remove({id: row.id})
            TipModal.msgSuccess('删除成功')
            this.getList()
          } catch (e) {
            console.error('删除失败:', e)
            TipModal.msgError('删除失败，请重试')
          }
        }).catch(() => {
          // 用户取消删除
        })
      }
    },

    getList(clear = false) {
      if (clear) {
        this.$refs[this.pageRef].clearSelect(true)
      } else {
        this.$refs[this.pageRef].getList()
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
