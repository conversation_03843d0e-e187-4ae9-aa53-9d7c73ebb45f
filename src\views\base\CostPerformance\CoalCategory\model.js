import BaseModel, { TableConfig, FormConfig } from '@/components/Common/SnProTable/model'

export const name = `/cwe/a/coalPerformanceCategoryConfig`

/**
 * 表单配置
 * @returns {FormConfig}
 */
const createFormConfig = () => {
  return {
    filters: []
  }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
  return {
    showOpt: true, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: false, // 显示选择列
    mountedQuery: true, // 自动查询,
    useApiList: true,
    columns: [
      {
        label: '煤种',
        prop: 'coalCategoryName',
        minWidth: 150,
        showOverflowTooltip: true,
        fixed: true,
        slot: 'coalCategoryName'
      },

      {
        label: '灰分Ad%',
        prop: 'cleanAd',
        slot: 'cleanAd'
      },
      {
        label: '硫分St,d%',
        prop: 'cleanStd',
        slot: 'cleanStd'
      },
      {
        label: '挥发分Vdaf%',
        prop: 'cleanVdaf',
        slot: 'cleanVdaf'
      },
      {
        label: '粘结G',
        prop: 'procG',
        slot: 'procG'
      },
      {
        label: 'Y/mm',
        prop: 'procY',
        slot: 'procY'
      }
    ]
  }
}

class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }

  list(query) {
    return super.request({
      url: `${name}/list`,
      method: 'get',
      params: query
    })
  }
}

export default new Model()
