import request from '@/utils/request'

const name = '/cwe/a/coalPerformanceBaseConfig'

export function page(query) {
  return request({
    url: `${name}/page`,
    method: 'get',
    params: query
  })
}

export function listByCoalCategory(query) {
  return request({
    url: `${name}/list-by-coal-category`,
    method: 'get',
    params: { ...query, coalSource: 'zy' }
  })
}

export function list(query) {
  return request({
    url: `${name}/list`,
    method: 'get',
    params: query
  })
}

export function save(data) {
  return request({
    url: `${name}/save`,
    method: 'post',
    data: { ...data, coalSource: 'zy' }
  })
}

export function get(id) {
  return request({
    url: `${name}/get`,
    method: 'get',
    params: { id }
  })
}

export function coalNameIndex(coalCode) {
  return request({
    url: `/a/coalNameIndex`,
    method: 'get',
    params: { coalCode }
  })
}
