<template>
  <div class="app-container">
    <panel-bar type="panel" title="数据看板">
      <el-date-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate" placeholder="选择日期"
                      :clearable="false" />
    </panel-bar>
    <card height="inherit" style="height:43vh">
      <card-item title="客户销量TOP5">
        <div class="item-echarts">
          <echarts width="inherit" height="inherit" v-if="isCustomer" :option="customer" />
        </div>
      </card-item>
      <card-item title="供应商供货TOP5">
        <div class="item-echarts">
          <echarts width="inherit" height="inherit" v-if="isSupplier" :option="supplier" />
        </div>
      </card-item>
    </card>
    <card height="inherit" style="height: 40.5rem">
      <card-item title="数据录入">
        <section class="reports">
          <div class="reports-item">
            <div class="reports-item-section">
              <div class="reports-item-img" style="background:#F8A20F;" @click="handleToPage('productList')">
                <img src="@/assets/console/name.png" />
              </div>
              <div class="reports-item-desc" @click="handleToPage('productList')">
                <span>品名录入</span>
                <span>PRODUCT NAME</span>
              </div>
            </div>

          </div>

        </section>
        <section class="reports">
          <div class="reports-item">
            <div class="reports-item-section">
              <div class="reports-item-img" style="background:#33CAD9;" @click="handleToPage('buy','supplier')">
                <img src="@/assets/console/supplier.png" />
              </div>
              <div class="reports-item-desc" @click="handleToPage('buy','supplier')">
                <span>供应商录入</span>
                <span>SUPPPLIER ENTRY</span>
              </div>
            </div>

            <div class="reports-item-section">
              <div class="reports-item-img" style="background:#33CAD9;" @click="handleToPage('buy','contractBuy')">
                <img src="@/assets/console/buy.png" />
              </div>
              <div class="reports-item-desc" @click="handleToPage('buy','contractBuy')">
                <span>采购合同录入</span>
                <span>CUSTOMER ENTRY</span>
              </div>
            </div>
          </div>
          <div class="reports-item">
            <div class="reports-item-section">
              <div class="reports-item-img" style="background:#FF726B;" @click="handleToPage('sell','customer')">
                <img src="@/assets/console/user.png" />
              </div>
              <div class="reports-item-desc" @click="handleToPage('sell','customer')">
                <span>客户录入</span>
                <span>PURCASE CONTRACT</span>
              </div>
            </div>
            <div class="reports-item-section">
              <div class="reports-item-img" style="background:#FF726B;" @click="handleToPage('sell','contractSell')">
                <img src="@/assets/console/sell.png" />
              </div>
              <div class="reports-item-desc" @click="handleToPage('sell','contractSell')">
                <span>销售合同录入</span>
                <span>SALES CONTRACT</span>
              </div>
            </div>
          </div>
        </section>
      </card-item>
    </card>
  </div>
</template>

<script>
import { supplierBuySum, customerSellSum } from '@/api/console'
import { PanelBar, Card, CardItem } from '@/components/Console'
const option = {
  animation: false,
  color: ['#2f79e8'],
  tooltip: {
    trigger: 'axis',
    point: [-30, -30],
    backgroundColor: '#EDEEFF',
    padding: [5, 20, 5, 20],
    textStyle: {
      color: '#9f9ea5',
    },
  },
  grid: {
    top: '50',
    left: '50px',
    right: '20px',
    bottom: '50px',
  },
  legend: {
    right: 20,
    itemGap: 20,
    padding: [10, 0, 10, 10],
    align: 'left',
    data: ['吨'],
  },
  xAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      // show:false, // 是否显示坐标轴轴线
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF', // 坐标轴线的颜色修改--文字也同步修改
        type: 'dashed',
      },
    },
    axisTick: {
      // 坐标轴刻度相关设置
      // show:true, // 是否显示坐标轴刻度
      alignWithLabel: true, // 可以保证刻度线和标签对齐
    },
    axisLabel: {
      // 坐标轴刻度标签的相关设置。
      color: '#9f9ea5', // 刻度标签文字的颜色
      interval: 0,
      // formatter: function (value) {
      //   var ret = ''// 拼接加\n返回的类目项
      //   var maxLength = 4// 每项显示文字个数
      //   var valLength = value.length// X轴类目项的文字个数
      //   var rowN = Math.ceil(valLength / maxLength) // 类目项需要换行的行数
      //   if (rowN > 1) {
      //     for (var i = 0; i < rowN; i++) {
      //       var temp = ''// 每次截取的字符串
      //       var start = i * maxLength// 开始截取的位置
      //       var end = start + maxLength// 结束截取的位置
      //       // 这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
      //       temp = value.substring(start, end) + '\n'
      //       ret += temp // 凭借最终的字符串
      //     }
      //     return ret
      //   } else {
      //     return value
      //   }
      // }
    },
    data: ['鑫飞能源', '焦煤集团', '汇锦', '棋盘井', '双柳'],
  },
  yAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF',
        type: 'solid',
      },
    },
    axisLabel: {
      // 坐标轴刻度标签的相关设置
      color: '#9f9ea5',
    },
    splitLine: {
      // 坐标轴在 grid 区域中的分隔线。
      show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示。
      lineStyle: {
        color: '#E3F0FF', // 分隔线颜色，可以设置成单个颜色
      },
    },
  },
  series: [
    {
      name: '吨',
      type: 'bar',
      barWidth: 16,
      itemStyle: {
        normal: {
          barBorderRadius: [30, 30, 0, 0], // 配置样式
          color: () => '#2f79e8',
        },
      },
      data: [5, 20, 36, 10, 10, 20],
    },
  ],
}
// Object.freeze(option)
export default {
  name: 'baseConsole',
  components: { PanelBar, Card, CardItem },
  data() {
    return {
      loading: false,
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      customer: {},
      supplier: {},
    }
  },
  created() {
    this.getData(this.currentDate)
  },
  computed: {
    isCustomer() {
      if (Object.keys(this.customer).length) return true
      return false
    },
    isSupplier() {
      if (Object.keys(this.supplier).length) return true
      return false
    },
  },
  methods: {
    handleToPage(par, child = '') {
      if (child) {
        this.$store.dispatch('changeChildRoute', { parent: par, child })
        this.$router.push({ name: child, params: { isOpenAdd: true } })
      } else {
        this.$store.dispatch('changeChildRoute', { parent: par, child: '' })
        this.$router.push({ name: par, params: { isOpenAdd: true } })
      }
    },
    async getData(date) {
      try {
        const res = await Promise.all([customerSellSum(date), supplierBuySum(date)])
        res[0].data.sort((a, b) => b.totalWeight - a.totalWeight)
        res[1].data.sort((a, b) => b.totalWeight - a.totalWeight)

        this.customer = this.formatOption(res[0].data)
        this.supplier = this.formatOption(res[1].data)
      } catch (error) {}
    },
    spliceName(val, max = 6) {
      let valLength = val.length
      let ret = ''
      let rowN = Math.ceil(valLength / max) // 类目项需要换行的行数
      if (rowN > 1) {
        for (let i = 0; i < rowN; i++) {
          let temp = '' // 每次截取的字符串
          let start = i * max // 开始截取的位置
          let end = start + max // 结束截取的位置
          // 这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
          temp = val.substring(start, end) + '\n'
          ret += temp // 凭借最终的字符串
        }
        return ret
      } else {
        return val
      }
    },
    formatOption(data) {
      const _options = JSON.parse(JSON.stringify(option))
      let nameList = []
      let totalList = []
      data.forEach((item) => {
        totalList.push(item.totalWeight)
        let name = this.spliceName(item.customerName || item.supplierName)
        nameList.push(name)
      })
      if (data[0].customerName) {
        _options.color = ['#33CAD9']
      }
      _options.xAxis.data = nameList
      _options.series[0].data = totalList
      return _options
    },
  },
  watch: {
    /**
     * 改变日期重新请求数据
     */
    currentDate(v) {
      this.getData(v)
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .card-item {
  padding: 5px;
}
::v-deep .item-echarts {
  width: 98.5%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  height: inherit;
}
.reports {
  width: inherit;
  height: inherit;
  display: flex;
  &-item {
    width: inherit;
    height: inherit;
    display: flex;
    flex-flow: column;
    justify-content: space-evenly;
    position: relative;

    &-section {
      display: flex;
      justify-content: center;
      opacity: 0.9;
      transition: all 0.5s;

      .reports-item-img {
        position: relative;
        width: 6rem;
        height: 6rem;
        border-radius: 50%;
        margin-right: 2rem;
        cursor: pointer;

        img {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 50%;
          height: 50%;
        }
      }

      .reports-item-desc {
        display: flex;
        justify-content: center;
        flex-flow: column nowrap;
        cursor: pointer;
        width: 200px;

        span:first-of-type {
          font-size: 1.5rem;
          color: #424242;
          margin-bottom: 10px;
        }

        span:last-of-type {
          font-size: 1rem;
          color: #a8a8a8;
        }
      }
    }

    &-section:hover {
      opacity: 1;
    }
  }
  &:first-of-type {
    flex: 0 0 30%;
  }
  &-item:first-of-type:after {
    content: '';
    position: absolute;
    opacity: 1;
    right: 0;
    height: 50px;
    width: 2px;
    background: #efefef;
  }
}
</style>
