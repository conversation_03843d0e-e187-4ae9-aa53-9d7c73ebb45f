<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="采购合同" :countList="countList"
            :statetype="activetype" :actionList="actionList">
      <el-table-column label="合同名称" slot="name" width="200" prop="name" align="center">
        <!-- <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{scope.row.name}}</span></template> -->
        <template slot-scope="scope"><span class="name"
                @click="handleUpdate(scope.row,'watch')">{{scope.row.name}}</span></template>
      </el-table-column>

      <el-table-column label="合同编码" slot="code" width="200" prop="code" align="center">
        <template slot-scope="scope">
          <el-tooltip popper-class="popper" :content="scope.row.code" placement="top" effect="light">
            <span>{{scope.row.code}}</span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="合同状态" slot="state" align="center" width="130">
        <template slot-scope="scope">
          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :(scope.row.state == 'PASS_FINANCE' ? 'success' :  (scope.row.state == 'CANCEL' ? 'danger' : 'info'))))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '财务待审核' : (scope.row.state == 'PASS' ? '已审核' : (scope.row.state == 'REJECT' ? '拒绝':(scope.row.state == 'PASS_FINANCE' ? '总经理待审核' :  (scope.row.state == 'CANCEL' ? '作废' : ''))))) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="价格类型" slot="payWay" align="center">
        <template slot-scope="scope" v-if="scope.row.payWay">
          <el-tag :type="(scope.row.payWay=='1' ? 'danger':(scope.row.payWay == '2' ? 'warning' : (scope.row.payWay == '3' ? 'success' : (scope.row.payWay == '4' ? 'danger' :(scope.row.payWay == '5' ? 'success' :'')))))"
                  size="mini">
            {{ scope.row.payWay == '1' ? '微信' : (scope.row.payWay == '2' ? '现金' : (scope.row.payWay == '3' ? '转账' : (scope.row.payWay == '4' ? '承兑':(scope.row.payWay == '5' ? '现汇':'')))) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="是否含税" slot="hasFax" align="center">
        <template slot-scope="scope" v-if="scope.row.hasFax">
          <el-tag :type="(scope.row.hasFax=='Y' ? 'success':(scope.row.hasFax == 'N' ? 'warning' :''))" size="mini">
            {{ scope.row.hasFax == 'Y' ? '是' : (scope.row.hasFax == 'N' ? '否' : '')}}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="是否开放接口" slot="isPublic" prop="isPublic" width="100" v-if="perms[`${curd}:interface`]||false"
                       align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isPublicBoolean === true ? 'success' : 'warning'">
            {{scope.row.isPublicBoolean === true ?'是':'否'}}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 合同状态 -->
      <!-- 通用审核状态 NEW-待审核、PASS-通过、REJECT-拒绝 -->
      <!-- <el-table-column label="合同状态" slot="state" prop="state" width="100">
        <template slot-scope="scope">
          <div @click="handleUpdate(scope.row,'review')" style="cursor: pointer;color:blue">
            <span v-if="scope.row.state=='NEW'">待审核</span>
            <span v-if="scope.row.state=='PASS'">通过</span>
            <span v-if="scope.row.state=='REJECT'">拒绝</span>
          </div>
        </template>
      </el-table-column> -->

      <el-table-column label="合同附件" slot="attachment" prop="attachment" width="100" align="center">
        <template slot-scope="scope">
          <div class="attachment" style="margin: 0 auto;">
            <img class="attachment-img" src="@/assets/attachment.png" @click="handlePreviewAttachment(scope.row)">
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" slot="opt" max-width="150" width="150" fixed="right" align="center">

        <!-- <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row,'edit')">编辑</el-tag>
          <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除</el-tag>
        </template> -->

        <template slot-scope="scope">
          <div style="display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <!-- <div v-if="perms[`${curd}:review`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state =='NEW' || scope.row.state=='PASS_FINANCE'" color="#FF726B"
                      @click="handleUpdate(scope.row,'review')">
                去审核
              </el-tag>
            </div> -->
            <div v-if="perms[`${curd}:ismanagerreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state =='PASS_FINANCE'" color="#FF726B"
                      @click="handleUpdate(scope.row,'review')">
                去审核(总经理)
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:isFinancereview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state=='NEW'" color="#FF726B" @click="handleUpdate(scope.row,'review')">
                去审核(财务)
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:update`] || false">
              <div v-if="perms[`${curd}:ismanagerreview`] || perms[`${curd}:isFinancereview`] ">
                <el-tag class="opt-btn" v-if="scope.row.state==='PASS' " color="#33CAD9"
                        @click="handleUpdate(scope.row,'change')">
                  修改
                </el-tag>
              </div>
              <el-tag class="opt-btn" color="#FF9639" v-if="scope.row.state == 'DRAFT'|| scope.row.state == 'REJECT'"
                      @click="handleUpdate(scope.row,'update')">编辑
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" color="#FF726B"
                      v-if="scope.row.state == 'NEW'|| scope.row.state == 'PASS_FINANCE' || scope.row.state == 'PASS' || scope.row.state == 'CANCEL'"
                      @click="handleUpdate(scope.row,'watch')">查看
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:istovoid`] || false">
              <el-tag class="opt-btn" color="#fb910e" v-if="scope.row.state == 'PASS'" @click="handleCancel(scope.row)">作废
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`]||false">
              <el-tag class="opt-btn" effect="plain" style="cursor: pointer;" color="#2f79e8"
                      v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>

      </el-table-column>
    </s-curd>

    <el-dialog width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose1" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible" :disabled="dialogStatus==='watch'">

        <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="reviewLogList">
              <el-steps :active="reviewLogList.length">
                <el-step v-for="(item,index) in reviewLogList" :key="index"
                         :title="item.reviewResult + ' ( 审批意见：'+item.reviewMessage+' )'"
                         :description="item.createDate+' '+item.reviewUserName">
                </el-step>
              </el-steps>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col>
            <div class="form-title">基本类型</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                 :disabled="dialogStatus==='review'?true:false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="类型" prop="type">
                    <dict-select v-model="entityForm.coalCategory" type="stock_type"
                                 :disabled="dialogStatus==='review'?true:isdisabled" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="煤种" prop="coalType">
                    <category-select :value.sync="entityForm.coalType" :disabled="dialogStatus==='review'?true:isdisabled" />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">合同编号</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="买方" prop="customerName">
                    <el-select v-model="supplierEntity.value" placeholder="请选择买方" clearable filterable style="width:100%">
                      <el-option v-for="item in supplierEntity.options" :key="item.id" :label="item.name" :value="item.id"
                                 :disabled="dialogStatus==='review'?true:false" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="卖方" prop="secondParty">
                    <!-- secondPartyEntity.value -->
                    <el-select v-model="secondPartyEntity.value" placeholder="请选择卖方" clearable filterable style="width:100%"
                               @change="changeSecondParty">
                      <el-option v-for="item in secondPartyEntity.options" :key="item.id" :label="item.name" :value="item.name"
                                 :disabled="dialogStatus==='review'?true:false" />
                    </el-select>
                  </el-form-item>
                </el-col>

              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="合同名称" prop="name" label-width="140px">
                    <el-input v-model="entityForm.name" clearable :disabled="dialogStatus==='review'?true:false"></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同编码" prop="code" label-width="140px">
                    <el-input v-model="entityForm.code" clearable :disabled="dialogStatus==='review'?true:false"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="开始日期" prop="beginDate">
                    <date-select v-model="entityForm.beginDate" clearable :disabled="dialogStatus==='review'?true:false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="结束日期" prop="endDate">
                    <date-select v-model="entityForm.endDate" clearable :disabled="dialogStatus==='review'?true:false" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="合同签订日期" prop="signDate">
                    <date-select v-model="entityForm.signDate" clearable :disabled="dialogStatus==='review'?true:false" />
                  </el-form-item>
                </el-col>
                <!-- <el-col>
                  <el-form-item label="煤价" prop="price">
                    <el-input v-model="entityForm.price" autocomplete="off" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')">
                      <template slot="append">
                        <span style="margin-right:10px">是否含税:</span>
                        <el-radio-group v-model="hasFax" size="small" @change="changeTheme">
                          <el-radio :label="'Y'">是</el-radio>
                          <el-radio :label="'N'">否</el-radio>
                        </el-radio-group>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col> -->
                <el-col>
                  <el-form-item label="价格类型" prop="payWay" :rules="[{ required: true, message: '请选择一项' }]">
                    <dict-select v-model="entityForm.payWay" type="price_type" :disabled="dialogStatus==='review'?true:false" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="煤价" prop="price">
                    <el-input v-model="entityForm.price" autocomplete="off" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')">
                      <template slot="append">
                        <span style="margin-right:10px">是否含税:</span>
                        <el-radio-group v-model="hasFax" size="small" @change="changeTheme">
                          <el-radio :label="'Y'">是</el-radio>
                          <el-radio :label="'N'">否</el-radio>
                        </el-radio-group>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="吨数" prop="amount" label-width="140px">
                    <el-input v-model="entityForm.amount" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable :disabled="dialogStatus==='review'?true:false">
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="总价" prop="totalMoney">
                    <el-input v-model="entityForm.totalMoney" clearable :disabled="dialogStatus==='review'?true:false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="运输编号" prop="carriageContract">
                    <el-input v-model="entityForm.carriageContract" clearable :disabled="dialogStatus==='review'?true:false" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="实际运费" prop="carriage">
                    <el-input v-model="entityForm.carriage" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable :disabled="dialogStatus==='review'?true:false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="开票运费" prop="carriageBill">
                    <el-input v-model="entityForm.carriageBill" :disabled="dialogStatus==='review'?true:false">
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="合同余量" prop="amountLeft">
                    <el-input v-model="entityForm.amountLeft" disabled>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">合同指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="全水" prop="cleanMt" label-width="140px">
                    <el-input v-model="entityForm.cleanMt" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="灰分" prop="cleanAd">
                    <el-input v-model="entityForm.cleanAd" autocomplete="off" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')">
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="硫" prop="cleanStd">
                    <el-input v-model="entityForm.cleanStd" clearable oninput="value=value.replace(/[^0-9.]/g,'')">
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="X指标" prop="procX">
                    <el-input v-model="entityForm.procX" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                      <template slot="append">mm</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="挥发分" prop="cleanVdaf">
                    <el-input v-model="entityForm.cleanVdaf" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="特征" prop="crc">
                    <el-input v-model="entityForm.crc" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="粘结" prop="procG">
                    <el-input v-model="entityForm.procG" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="胶质层" prop="procY">
                    <el-input v-model="entityForm.procY" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                      <template slot="append">mm</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="回收率" prop="recovery">
                    <el-input v-model="entityForm.recovery" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="热强度CSR" prop="qualCsr">
                    <el-input v-model="entityForm.qualCsr" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="反射率R0" prop="macR0">
                    <el-input v-model="entityForm.macR0" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="准差S" prop="macS">
                    <el-input v-model="entityForm.macS" oninput="value=value.replace(/[^0-9.]/g,'')" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" placeholder="请输入备注" />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- <el-row type="flex" :gutter="50" v-if="isshow==true">
                <el-col>
                  <el-form-item label="审批意见" prop="reviewMessage">
                    <el-input type="textarea" v-model="entityForm.reviewMessage" placeholder="请输入审批意见" />
                  </el-form-item>
                </el-col>
              </el-row> -->

              <!-- <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
                <el-col>
                  <el-form-item label="步骤" prop="reviewLogList">
                    <el-steps :active="reviewLogList.length">
                      <el-step v-for="(item,index) in reviewLogList" :key="index" :title="item.reviewResult"
                               :description="item.createDate+' '+item.reviewUserName">
                      </el-step>
                    </el-steps>
                  </el-form-item>
                </el-col>
              </el-row> -->

            </div>
          </el-col>

          <el-col>
            <div class="form-title">附件信息</div>
            <div class="form-layout">
              <el-row>
                <el-col>
                  <el-form-item>
                    <el-collapse v-model=" collapse">
                      <el-collapse-item title="合同附件上传" name="1">
                        <div class="upload">
                          <div class="upload-list">
                            <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                              <el-image class="img" :src="item.uri" fit="fill" :preview-src-list="srcList" />
                              <img src="@/assets/close.png" class="icon" @click="handleRemoveUpload(index)">
                            </div>
                            <upload-attachment ref="upload" class="upload-attachment" :uploadData="uploadData"
                                               source="ContractBuy" :size="size" :limitNum="limitNum" accept=".jpg,.jpeg,.png"
                                               :isShowFile="false" @successNotify="handleUploadSuccess"
                                               @deleteNotify="handleUploadDelete" listType="picture" />
                          </div>
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">其他信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50" v-if="perms[`${curd}:interface`]||false">
                <el-col>
                  <el-form-item label="是否开放接口" prop="isPublicBoolean">
                    <el-switch v-model="entityForm.isPublicBoolean"></el-switch>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <!-- <div slot="footer" class="dialog-footer" v-if="state=='NEW' || state=='REJECT'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" v-if="perms[`${curd}:review`]||false"
                   @click="Approved(contractId)">
          审核通过
        </el-button>
        <el-button @click="ApprovednoPas(contractId)" class="dialog-footer-btns" style="width:100px"
                   v-if="perms[`${curd}:review`]||false">审核不通过
        </el-button>
      </div>

      <div slot="footer" class="dialog-footer" v-else>
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div> -->

      <div slot="footer" class="dialog-footer" v-if="isshow==false">
        <div v-if="dialogStatus!=='watch'">
          <el-button @click="handleClose1" class="dialog-footer-btns">取消</el-button>
          <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                     v-if="perms[`${curd}:update`]||false">保存
          </el-button>
        </div>
      </div>

      <div slot="footer" class="dialog-footer" v-else style="width:100%">
        <div style="width:calc(100% - 300px)" class="shenpi">
          <el-input style="width:100%;" v-if="Auditstatus=='PASS_FINANCE' || Auditstatus=='NEW'" type="text"
                    v-model="entityForm.reviewMessage" placeholder="请输入审批意见" />
        </div>

        <!-- <el-button v-if="perms[`${curd}:review`]||false" type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                   @click="Approved(contractId)">
          审核通过
        </el-button> -->
        <div style="display: flex;flex-direction: row;width:300px">
          <div v-if="Auditstatus=='PASS_FINANCE'">
            <el-button v-if="perms[`${curd}:review`]||false" type="primary" class="dialog-footer-btns"
                       :loading="entityFormLoading" @click="Approved(contractId,entityForm.reviewMessage,
                       entityForm.hasFax,entityForm.price,entityForm.cleanMt,entityForm.cleanAd,
                       entityForm.cleanStd,entityForm.procX,entityForm.cleanVdaf,entityForm.crc,entityForm.procG,entityForm.procY,
                       entityForm.recovery,entityForm.qualCsr,entityForm.macR0,entityForm.macS,entityForm.remarks)"
                       style="width:150px">
              审核通过(总经理)
            </el-button>
          </div>

          <div v-if="Auditstatus=='NEW'">
            <el-button v-if="perms[`${curd}:review`]||false" type="primary" class="dialog-footer-btns" style="width:150px"
                       :loading="entityFormLoading" @click="FinancePass(contractId,entityForm.reviewMessage,
                       entityForm.hasFax,entityForm.price,entityForm.cleanMt,entityForm.cleanAd,
                       entityForm.cleanStd,entityForm.procX,entityForm.cleanVdaf,entityForm.crc,entityForm.procG,entityForm.procY,
                       entityForm.recovery,entityForm.qualCsr,entityForm.macR0,entityForm.macS,entityForm.remarks)">
              审核通过(财务)
            </el-button>
          </div>
          <div v-if="Auditstatus=='PASS_FINANCE' || Auditstatus=='NEW'">
            <el-button @click="ApprovednoPas(contractId,entityForm.reviewMessage,
                       entityForm.hasFax,entityForm.price,entityForm.cleanMt,entityForm.cleanAd,
                       entityForm.cleanStd,entityForm.procX,entityForm.cleanVdaf,entityForm.crc,entityForm.procG,entityForm.procY,
                       entityForm.recovery,entityForm.qualCsr,entityForm.macR0,entityForm.macS,entityForm.remarks)"
                       class="dialog-footer-btns" style="width:100px" v-if="perms[`${curd}:review`]||false">拒绝
            </el-button>
          </div>
        </div>

      </div>
    </el-dialog>

    <el-drawer title="合同附件列表" :visible.sync="drawer.visible" direction="rtl" :before-close="handleCloseDrawer">
      <template v-if="drawer.list.length">
        <div class="images-warp">
          <div class="img" v-for="(item,index) in drawer.list" :key="index">
            <el-image class="img" :title="item.display" :src="item.uri" fit="fill" :preview-src-list="drawer.preview" />
            <span class="img-title">{{item.display}}</span>
          </div>
        </div>
      </template>
      <template v-else>
        <el-empty :image="emptyImgPath" :image-size="200" description="暂无图片" />
      </template>
    </el-drawer>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import CustomerModel from '@/model/user/customer'
import Model from '@/model/user/contractSell'
import productModel from '@/model/product/productList'
import { createMemoryField } from '@/utils/index'
import pinyin from 'js-pinyin'
export default {
  name: 'contractSell',
  mixins: [Mixins],
  data() {
    return {
      curd: 'contractSell',
      model: Model,
      filterOption: {
        showMore: true,
        columns: [
          { prop: 'code', filter: 'filter_EQS_code', props: { placeholder: '请输入合同编号', clearable: true } },
          { prop: 'name', filter: 'filter_LIKES_name', props: { placeholder: '请输入合同名称', clearable: true } },
          {
            prop: 'productName',
            filter: 'filter_LIKES_productName',
            component: 'select-ProductName',
            props: { placeholder: '请选择品名', clearable: true, selecttype: 'productName' },
          },
        ],
      },
      entityForm: { ...Model.model, filter_EQS_type: '' },
      rules: {
        // name: { required: true, message: '请输入合同名称', trigger: 'blur' },
        code: { required: true, message: '请输入合同编码', trigger: 'blur' },
        customerName: { required: true, message: '请输入买方', trigger: 'change' },
        secondParty: { required: true, message: '请输入卖方', trigger: 'blur' },
        // amount: { required: true, message: '请输入吨数', trigger: 'blur' },
        hasFax: { required: true, message: '请输入选择是否含税', trigger: 'change' },
      },
      supplierEntity: { value: '', options: [] },
      secondPartyEntity: { value: '', options: [] },

      nameEntity: { options: [], active: '' },
      memoryEntity: { fields: {}, triggered: false },
      // upload
      fileList: [],
      limitNum: 50,
      uploadList: [], // 用于展示
      uploadData: { refId: '', refType: 'ContractSell' },
      drawer: {
        visible: false,
        list: [],
        preview: [],
      },
      collapse: '1',
      emptyImgPath: require(`@/assets/empty_img.jpg`),

      contractId: '',
      // state: '',
      isshow: false,
      Auditstatus: '',
      reviewLogList: [],
      Date: new Date(new Date().getTime()).Format('yyyyMMdd'),
      isdisabled: false,
      countList: [],
      hasFax: 'Y',
    }
  },
  computed: {
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    },
  },
  watch: {
    'entityForm.isPublicBoolean': {
      handler(v) {
        this.entityForm.isPublic = v ? 'Y' : 'N'
      },
    },
    // 'entityForm.customerName'(name) {
    // this.getContractList()
    // console.log('999999999999')
    // console.log(id)
    // console.log(this.supplierEntity.options)
    // if (!id) return
    // const item = this.supplierEntity.options.find((item) => item.id === id)
    // if (!item) return
    // let obj = item.name
    // var index = obj.lastIndexOf('有限公司')
    // console.log(index)
    // if (index == -1) {
    //   obj = obj
    // } else {
    //   obj = obj.substring(0, index)
    // }

    // this.entityForm.name = obj
    // this.entityForm.customerId = id
    // this.entityForm.customerName = name
    // this.entityForm.firstParty = item.name
    // this.entityForm.code = obj + '/ ' + this.Date
    // this.zhuanyi(obj)
    // },
    'entityForm.productName'(name) {
      if (name) {
        const item = this.nameEntity.options.find((item) => item.name === name)
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.entityForm.coalType = item.coalCategory || ''
        this.entityForm.coalCategory = item.type
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
        this.entityForm.coalCategory = ''
      }
    },
    'supplierEntity.value'(id) {
      if (!id) return
      const item = this.supplierEntity.options.find((item) => item.id === id)
      if (!item) return
      // let obj = item.name
      // var index = obj.lastIndexOf('有限公司')
      // if (index == -1) {
      //   obj = obj
      // } else {
      //   obj = obj.substring(0, index)
      // }
      // let randomnumber = Math.floor(Math.random() * 100 + 1)
      // this.entityForm.name = obj + randomnumber
      if (this.dialogStatus !== 'watch') {
        this.getNameCodefn(item.name)
      }
      this.entityForm.customerId = id
      this.entityForm.customerName = item.name
      this.entityForm.firstParty = item.name
      // this.zhuanyi(obj)
    },
    'entityForm.firstParty'(name) {
      // console.log(name)
      if (!name) {
        this.entityForm.firstParty = ''
      }
      const item = this.secondPartyEntity.options.find((item) => item.name === name)
      // console.log(item)
      if (!item) return
      this.entityForm.secondParty = item.name
    },
    activetype: function (newV, oldV) {
      this.getlist(newV)
      this.getnumber()
    },
  },

  async created() {
    this.memoryEntity.fields = createMemoryField({
      // 'supplierName', 'supplierId',
      fields: ['id', 'coalType', 'coalCategory', 'productName', 'productCode', 'productId'],
      target: this.entityForm,
    })
    this.getName()
    this.getContractList()
    this.getnumber()
    // this.permsActionLsit(this.curd)
    this.permsActionbtn(this.curd)
    this.entityForm.secondParty = ''
    if (this.$route.params.type) {
      this.Auditstatus = this.$route.params.type
      this.activetype = this.$route.params.type
      this.contractId = this.$route.params.id
      this.handleUpdate(this.$route.params.id, 'review')
    } else {
      this.activetype = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
      this.actionList.forEach((val, index) => {
        if (val.type == this.activetype) {
          val.active = true
        } else {
          val.active = false
        }
      })
    }

    this.entityForm.hasFax = 'Y'
    this.isdisabled = false
    //获取卖方列表
    this.getSellContractPartyfn()
  },
  methods: {
    async getSellContractPartyfn() {
      const res = await this.model.getSellContractParty()
      if (res.data.length) this.secondPartyEntity.options = res.data
    },
    async getNameCodefn(customerName) {
      const res = await this.model.getNameCode(customerName)
      this.entityForm.name = res.data.name
      this.entityForm.code = res.data.code
    },
    // zhuanyi(name) {
    //   //将汉字转义成汉字的大写首字母
    //   let char = '',
    //     randomnumber = ''
    //   pinyin.setOptions({ checkPolyphone: false, charCase: 0 })
    //   char = pinyin.getCamelChars(name)
    //   randomnumber = Math.floor(Math.random() * 100 + 1)
    //   this.entityForm.code = char + this.Date + randomnumber
    //   // char = XM
    // },
    handleCreate() {
      // console.log('新增')
      this.hasFax = 'Y'
      this.entityForm.hasFax = 'Y'
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.secondPartyEntity.value = ''
    },
    changeSecondParty(val) {
      this.entityForm.firstParty = val
      this.secondPartyEntity.value = val
    },
    async handleCancel(row) {
      this.$confirm('确定要作废吗，作废后不可恢复哦！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const res = await this.model.Cancel({
            id: row.id,
            reviewMessage: row.reviewMessage,
            hasFax: row.hasFax,
            price: row.price,
            cleanMt: row.cleanMt,
            cleanAd: row.cleanAd,
            cleanStd: row.cleanStd,
            procX: row.procX,
            cleanVdaf: row.cleanVdaf,
            crc: row.crc,
            procG: row.procG,
            procY: row.procY,
            recovery: row.recovery,
            qualCsr: row.qualCsr,
            macR0: row.macR0,
            macS: row.macS,
            remarks: row.remarks,
          })
          if (res) {
            this.$message({ type: 'success', message: '操作成功!' })
            this.getList()
            this.getnumber()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '操作取消' }))
    },
    changeTheme(val) {
      this.hasFax = val
      this.entityForm.hasFax = val
    },
    async getnumber() {
      const res = await this.model.getcountByState()
      this.countList = res.data
    },

    //点击状态获取数据
    async getlist(newV) {
      this.activetype = newV
      // let indx = ''
      // let obj = {}
      this.actionList.forEach((val, index) => {
        if (val.type == newV) {
          // indx = index
          // obj = val
          val.active = true
          // this.$set(this.actionList, index, val)
          // this.replyTo(index)
        } else {
          val.active = false
        }
      })
      this.filtersSeach.filter_INS_state = newV
      this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    },
    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      this.drawer.list = res.data.attachmentList
      this.drawer.preview = res.data.attachmentList.map((item) => item.uri)
      this.drawer.visible = true
    },

    async handleRemoveUpload(index) {
      const { id } = this.uploadList[index]
      this.$refs.upload.attachmentDelete({ id, index })
    },

    handleUploadSuccess(res) {
      this.uploadList = [...this.uploadList, res]
      // 上传完毕后更新展示的列表和把新增的附件id塞入到
      this.entityForm.attachmentList.push({ id: res.id })
    },
    handleUploadDelete({ status, index }) {
      this.uploadList.splice(index, 1)
    },

    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.isdisabled = false
      // console.log(this.dialogStatus)
      if (this.dialogStatus == 'create') {
        this.secondPartyEntity.value = ''
      }
      this.isshow = false
      this.uploadList = [] // 清空下载列表
      this.fileList = []
      this.reviewLogList = []
      this.supplierEntity.value = ''
      this.hasFax = 'Y'
      this.entityForm.hasFax = 'Y'
      this.nameEntity.active = ''
      this.entityForm = { ...this.model.model }
      this.entityForm.secondParty = ''
      // if (!this.memoryEntity.triggered) {
      //   this.nameEntity.active = ''
      //   this.entityForm = { ...this.model.model }
      // } else {
      //   this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      // }
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) {}
    },
    handleNameEntity(val) {
      this.entityForm.productName = val
      this.isdisabled = true
    },

    async getContractList() {
      let { data } = await await CustomerModel.page({ size: 999 })
      if (data.records.length) this.supplierEntity.options = data.records
    },
    handleWatchForm(item) {
      this.supplierEntity.value = item.customerId
      this.entityForm = { ...item }
      this.isshow = false
      this.dialogStatus = 'watch'
      this.dialogFormVisible = true
    },
    async handleUpdate(item, type) {
      if (typeof item == 'object') {
        this.entityForm = { ...item }
        this.contractId = item.id
        this.Auditstatus = item.state
        if (type == 'review') {
          this.dialogStatus = 'review'
          this.isshow = true
        } else if (type == 'update') {
          this.dialogStatus = 'update'
          this.isshow = false
        } else if (type == 'watch') {
          this.isshow = false
          this.dialogStatus = 'watch'
        } else if (type == 'change') {
          this.isshow = false
          this.dialogStatus = 'change'
        }
        this.supplierEntity.value = this.entityForm.customerId
        this.secondPartyEntity.value = this.entityForm.secondParty
        this.dialogFormVisible = true
        // 上传所需要的配置
        this.uploadData.refId = item.id
        const { data } = await this.model.getUploadList(item.id)
        this.entityForm = { ...data }
        // console.log(data)
        this.hasFax = this.entityForm.hasFax
        this.reviewLogList = data.reviewLogList
        this.uploadList = data.attachmentList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((item, index) => {
            newarry.push(item.reviewMessage)
          })
          // this.entityForm.reviewMessage = newarry.toString()
          this.entityForm.reviewMessage = ''
        }
      } else if (typeof item == 'string') {
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        }
        this.supplierEntity.value = this.entityForm.customerId
        this.secondPartyEntity.value = this.entityForm.secondParty
        this.dialogFormVisible = true
        // 上传所需要的配置
        this.uploadData.refId = item
        const { data } = await this.model.getUploadList(item)
        this.entityForm = { ...data }
        this.reviewLogList = data.reviewLogList
        this.uploadList = data.attachmentList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((itemv, index) => {
            newarry.push(itemv.reviewMessage)
          })
          this.entityForm.reviewMessage = newarry.toString()
        }
      }
    },
    handleClose1() {
      this.resetVariant()
    },
  },
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
::v-deep .el-input.is-disabled .el-input__inner {
  color: #606266;
}
.images-warp {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-start;
  align-content: flex-start;
  flex-flow: wrap;
  .img {
    width: 33.33%;
    display: flex;
    justify-content: flex-start;
    flex-flow: column wrap;
    margin-bottom: 10px;
    opacity: 0.95;
    transition: 1s all;
    &:hover {
      opacity: 1;
    }
    &-title {
      text-align: center;
      font-size: 14px;
      padding: 2px 0;
    }
    .img {
      margin: 0 auto;
      // border: 1px solid #ff9639;
      cursor: pointer;
      border-radius: 4px;
      width: 100px;
      height: 100px;
      img {
        width: 100%;
        margin: 0 auto;
      }
    }
  }
}
::v-deep .el-drawer__header > :first-child {
  font-size: 14px;
}
.attachment {
  width: 50px;
  height: 50px;
  cursor: pointer;
  border: 1px solid #ccc;
  border-radius: 3px;
  box-shadow: 0 2px 4px #adabab1f, 0 0 6px #aaa6a60a;
  &-img {
    width: 100%;
  }
}

::v-deep .el-collapse-item__arrow {
  display: none;
}

.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
<style scoped>
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
/* .dialog-footer >>> .dialog-footer-btns {
  border: 1px solid #2f79e8;
  color: #2f79e8;
} */
/* .opt-btn {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
}
.opt-btnV {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
} */
</style>