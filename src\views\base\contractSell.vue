<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="采购合同" :countList="countList"
            :statetype="activetype" :actionList="actionList" otherHeight="175">
      <el-table-column label="合同名称" slot="name" width="200" prop="name" align="center">
        <template slot-scope="scope"><span class="name"
                @click="handleUpdate(scope.row,'watch')">{{scope.row.name}}</span></template>
      </el-table-column>
      <el-table-column label="合同编码" slot="code" width="200" prop="code" align="center">
        <template slot-scope="scope">
          <el-tooltip popper-class="popper" :content="scope.row.code" placement="top" effect="light">
            <span>{{scope.row.code}}</span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="合同状态" slot="state" align="center" width="130">
        <template slot-scope="scope">
          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :(scope.row.state == 'PASS_FINANCE' ? 'success' :  (scope.row.state == 'CANCEL' ? 'danger' : 'info'))))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '财务待审核' : (scope.row.state == 'PASS' ? '已审核' : (scope.row.state == 'REJECT' ? '拒绝':(scope.row.state == 'PASS_FINANCE' ? '总经理待审核' :  (scope.row.state == 'CANCEL' ? '作废' : ''))))) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="价格类型" slot="payWay" align="center">
        <template slot-scope="scope" v-if="scope.row.payWay">
          <el-tag :type="(scope.row.payWay=='1' ? 'danger':(scope.row.payWay == '2' ? 'warning' : (scope.row.payWay == '3' ? 'success' : (scope.row.payWay == '4' ? 'danger' :(scope.row.payWay == '5' ? 'success' :'')))))"
                  size="mini">
            {{ scope.row.payWay == '1' ? '微信' : (scope.row.payWay == '2' ? '现金' : (scope.row.payWay == '3' ? '转账' : (scope.row.payWay == '4' ? '承兑':(scope.row.payWay == '5' ? '现汇':'')))) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="是否含税" slot="hasFax" align="center">
        <template slot-scope="scope" v-if="scope.row.hasFax">
          <el-tag :type="(scope.row.hasFax=='Y' ? 'success':(scope.row.hasFax == 'N' ? 'warning' :''))" size="mini">
            {{ scope.row.hasFax == 'Y' ? '是' : (scope.row.hasFax == 'N' ? '否' : '')}}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="是否开放接口" slot="isPublic" prop="isPublic" width="100" v-if="perms[`${curd}:interface`]||false"
                       align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isPublicBoolean === true ? 'success' : 'warning'">
            {{scope.row.isPublicBoolean === true ?'是':'否'}}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="合同附件" slot="attachment" prop="attachment" width="100" align="center">
        <template slot-scope="scope">
          <div class="attachment" style="margin: 0 auto;">
            <img class="attachment-img" src="@/assets/attachment.png" @click="handlePreviewAttachment(scope.row)">
          </div>
        </template>
      </el-table-column>

      <el-table-column label="当前流程" slot="curFlowName" align="center" fixed="right">
        <template slot-scope="scope">
          <el-popover placement="right" width="700" trigger="click">
            <div v-for="(item,index) in steplistNEW" :key="index">
              <div>
                <div>{{item.label}}: <span style="color:red">{{item.curFlowName}}</span></div>
              </div>
              <el-row type="flex" :gutter="50" style="margin:20px 0">
                <el-col style="margin-bottom: -13px">
                  <div class="bzboxV">
                    <div class="bzminboxV" v-for="(value,indexc) in item.flowDesignList" :key="indexc">
                      <div class="lin" :class="value.active==true?'bordercolorBLUE':''"></div>
                      <div class="context" :class="value.active==true?'colorBLUE':''">
                        <i class="el-icon-edit icon" v-if="value.code=='DRAFT' "></i>
                        <i class="el-icon-coordinate icon" v-if="value.code=='FINANCE' ||  value.code=='FINANCE_LEADER' ||  value.code=='MANAGER' ||  value.code=='BOSS' || value.code=='PRICE_MANAGER'
                       || value.code=='PRICE_FACTORY_MANAGER' || value.code=='PRICE_BOSS' || value.code=='PRICE_BOSS' || value.code=='CARRIAGE_FACTORY_MANAGER' 
                       || value.code=='CARRIAGE_MANAGER' || value.code=='CARRIAGE_BOSS'"></i>
                        <i class="el-icon-link icon" v-if="value.code=='PASS'"></i>
                        <i class="el-icon-help icon"
                           v-if="value.code=='REJECT' || value.code=='CARRIAGE_REJECT' || value.code=='PRICE_REJECT' "></i>
                        <div>{{value.aliasName}}</div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <el-button slot="reference" style="padding: 3px 6px;" @click="lookbtn(scope.row)">流程</el-button>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" max-width="100" width="170" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;flex-wrap:wrap">
            <div v-if="perms[`${curd}:ismanagerreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode =='MANAGER'" color="#FF726B"
                      @click="handleUpdate(scope.row,'review','CONTRACT_SELL')">
                <!-- 去审核(总经理) -->
                {{scope.row.curFlowName}}
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:isFinancereview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode=='FINANCE'" color="#FF726B"
                      @click="handleUpdate(scope.row,'review','CONTRACT_SELL')">
                <!-- 去审核(财务) -->
                {{scope.row.curFlowName}}
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:isFinanceLeaderreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode=='FINANCE_LEADER'" color="#FF726B"
                      @click="handleUpdate(scope.row,'review','CONTRACT_SELL')">
                <!-- 去审核(财务负责人) -->
                {{scope.row.curFlowName}}
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:isBossreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode=='BOSS'" color="#FF726B"
                      @click="handleUpdate(scope.row,'review','CONTRACT_SELL')">
                <!-- 去审核(董事长) -->
                {{scope.row.curFlowName}}
              </el-tag>
            </div>

            <div v-if="activetype == 'NEW,PASS_FINANCE' || activetype == 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'">
              <div v-if="scope.row.state == 'PASS'">
                <div v-if="scope.row.priceState=='NEW'">
                  <div v-if="scope.row.curPriceFlowCode=='PRICE_FACTORY_MANAGER'">
                    <el-tag v-if="perms[`${curd}:priceFactoryManagerWaitforReviewed`]||false" class="opt-btn" color="#FF726B"
                            @click="handleUpdate(scope.row,'AdjustPriceReview','CONTRACT_SELL_PRICE')">
                      <!-- 调价审核(厂长) -->
                      {{scope.row.curPriceFlowName}}
                    </el-tag>
                  </div>
                </div>
                <div v-if="scope.row.priceState=='NEW'">
                  <div v-if="scope.row.curPriceFlowCode=='PRICE_MANAGER'">
                    <el-tag v-if="perms[`${curd}:priceManagerWaitforReviewed`]||false" class="opt-btn" color="#FF726B"
                            @click="handleUpdate(scope.row,'AdjustPriceReview','CONTRACT_SELL_PRICE')">
                      <!-- 调价审核(总经理) -->
                      {{scope.row.curPriceFlowName}}
                    </el-tag>
                  </div>
                </div>
                <div v-if="scope.row.priceState=='NEW'">
                  <div v-if="scope.row.curPriceFlowCode=='PRICE_BOSS'">
                    <el-tag v-if="perms[`${curd}:priceBossWaitforReviewed`]||false" class="opt-btn" color="#FF726B"
                            @click="handleUpdate(scope.row,'AdjustPriceReview','CONTRACT_SELL_PRICE')">
                      <!-- 调价审核(董事长) -->
                      {{scope.row.curPriceFlowName}}
                    </el-tag>
                  </div>
                </div>
                <div v-if="scope.row.carriageState=='NEW'">
                  <div v-if="scope.row.curCarriageFlowCode=='CARRIAGE_FACTORY_MANAGER'">
                    <el-tag v-if="perms[`${curd}:carriageFactoryManagerWaitforReviewed`]||false" class="opt-btn" color="#FF726B"
                            @click="handleUpdate(scope.row,'freightReview','CONTRACT_SELL_CARRIAGE')">
                      <!-- 运费审核(厂长) -->
                      {{scope.row.curCarriageFlowName}}
                    </el-tag>
                  </div>
                </div>
                <div v-if="scope.row.carriageState=='NEW'">
                  <div v-if="scope.row.curCarriageFlowCode=='CARRIAGE_MANAGER'">
                    <el-tag v-if="perms[`${curd}:carriageManagerWaitforReviewed`]||false" class="opt-btn" color="#FF726B"
                            @click="handleUpdate(scope.row,'freightReview','CONTRACT_SELL_CARRIAGE')">
                      <!-- 运费审核(总经理) -->
                      {{scope.row.curCarriageFlowName}}
                    </el-tag>
                  </div>
                </div>
                <div v-if="scope.row.carriageState=='NEW'">
                  <div v-if="scope.row.curCarriageFlowCode=='CARRIAGE_BOSS'">
                    <el-tag v-if="perms[`${curd}:carriageBossWaitforReviewed`]||false" class="opt-btn" color="#FF726B"
                            @click="handleUpdate(scope.row,'freightReview','CONTRACT_SELL_CARRIAGE')">
                      <!-- 运费审核(董事长) -->
                      {{scope.row.curCarriageFlowName}}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>

            <template
                      v-if="(scope.row.state == 'PASS' && activetype == 'PASS') || (scope.row.state == 'PASS' && activetype == 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS')">
              <div v-if="scope.row.priceState=='PASS'">
                <el-tag v-if="perms[`${curd}:changePriceApply`]||false" class="opt-btn" color="#33CAD9"
                        @click="priceAdjustmentfn(scope.row,'priceAdjustment')">
                  调价
                </el-tag>
              </div>
              <div v-if="scope.row.carriageState=='PASS'">
                <el-tag class="opt-btn" v-if="perms[`${curd}:carriageApply`]||false" color="#ff9639"
                        @click="priceAdjustmentfn(scope.row,'freightAdjustment')">
                  调运费
                </el-tag>
              </div>
              <div class="lookCurFlowNamecss">
                <el-tag class="opt-btn" v-if="perms[`${curd}:ModulationApply`]||false" color="#fdf6ec"
                        @click="priceAdjustmentfn(scope.row,'addAmount')">
                  调量
                </el-tag>
              </div>
            </template>

            <div v-if="perms[`${curd}:update`] || false">
              <div class="lookCurFlowNamecss"
                   v-if="perms[`${curd}:ismanagerreview`] || perms[`${curd}:isFinancereview`]  || perms[`${curd}:isFinanceLeaderreview`]  || perms[`${curd}:isBossreview`]">
                <div v-if="activetype=='PASS' || activetype=='DRAFT,REJECT,NEW,PASS_FINANCE,PASS'">
                  <el-tag class="opt-btn" v-if="scope.row.state=='PASS' " color="#fdf6ec"
                          @click="handleUpdate(scope.row,'change')">
                    修改
                  </el-tag>
                </div>
              </div>
              <el-tag style="margin: 0 3px;" v-if="scope.row.state =='DRAFT'|| scope.row.state == 'REJECT'" type='warning'
                      effect="plain" @click="handleUpdate(scope.row,'update')">
                编辑
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:update`] || false" class="lookCurFlowNamecss">
              <el-tag class="opt-btn" color="#fdf6ec"
                      v-if="scope.row.state == 'NEW'|| scope.row.state == 'PASS_FINANCE' || scope.row.state == 'PASS' || scope.row.state == 'CANCEL'"
                      @click="handleUpdate(scope.row,'watch')">查看
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:istovoid`] || false">
              <el-tag class="opt-btn" color="#ff726b" v-if="scope.row.state == 'PASS'" @click="handleCancel(scope.row)">作废
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`]||false">
              <el-tag effect="plain" style="cursor: pointer;margin: 0 3px;"
                      v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>

          </div>
        </template>
      </el-table-column>

    </s-curd>

    <el-dialog top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose1" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               :disabled="dialogStatus==='watch' ||  dialogStatus==='AdjustPriceReview' || dialogStatus==='freightReview'"
               label-position="top" v-if="dialogFormVisible">
        <!-- <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="reviewLogList">
              <el-steps :active="reviewLogList.length">
                <el-step v-for="(item,index) in reviewLogList" :key="index"
                         :title="item.reviewResult + ' ( 审批意见：'+item.reviewMessage+' )'"
                         :description="item.createDate+' '+item.reviewUserName">
                </el-step>
              </el-steps>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row type="flex" :gutter="50" v-if="steplist.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="steplist">
              <div class="bzbox">
                <div v-for="(item,index) in steplist" :key="index" class="bzminbox">
                  <div class="lin" :class="item.active==true?'bordercolorBLUE':''"></div>
                  <div class="context" :class="item.active==true?'colorBLUE':''">
                    <i class="el-icon-edit icon" v-if="item.code=='DRAFT'"></i>
                    <i class="el-icon-coordinate icon" v-if="item.code=='FINANCE' ||  item.code=='FINANCE_LEADER' ||  item.code=='MANAGER' ||  item.code=='BOSS' || item.code=='PRICE_MANAGER'
                       || item.code=='PRICE_FACTORY_MANAGER' || item.code=='PRICE_BOSS' || item.code=='PRICE_BOSS' || item.code=='CARRIAGE_FACTORY_MANAGER' 
                       || item.code=='CARRIAGE_MANAGER' || item.code=='CARRIAGE_BOSS'"></i>
                    <i class="el-icon-link icon" v-if="item.code=='PASS'"></i>
                    <i class="el-icon-help icon"
                       v-if="item.code=='REJECT' || item.code=='CARRIAGE_REJECT' || item.code=='PRICE_REJECT' "></i>
                    <div>{{item.aliasName}}</div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col>
            <div class="form-title">基本类型</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                 :disabled="dialogStatus==='review'?true:false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="类型" prop="type">
                    <dict-select v-model="entityForm.coalCategory" type="stock_type"
                                 :disabled="dialogStatus==='review'?true:isdisabled" />
                  </el-form-item>
                </el-col>
                <!-- <el-col>
                  <el-form-item label="煤种" prop="coalType">
                    <category-select :value.sync="entityForm.coalType" :disabled="dialogStatus==='review'?true:isdisabled" />
                  </el-form-item>
                </el-col> -->
                <el-col>
                  <el-form-item label="买方" prop="customerName">
                    <el-select :disabled="dialogStatus==='review'|| dialogStatus === 'watch' ?true:false"
                               v-model="customerNameEntity.value" placeholder="请选择买方" clearable filterable style="width:100%">
                      <el-option v-for="item in customerNameEntity.options" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="卖方" prop="secondParty">
                    <el-select v-model="secondPartyEntity.value" placeholder="请选择卖方" clearable filterable style="width:100%"
                               @change="changeSecondParty"
                               :disabled="dialogStatus==='review'|| dialogStatus === 'watch' ?true:false">
                      <el-option v-for="item in secondPartyEntity.options" :key="item.id" :label="item.name" :value="item.name" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="发货单位" prop="senderName" label-width="140px">
                    <el-select v-model="entityForm.senderName" placeholder="请选择收货单位" clearable filterable style="width:100%"
                               :disabled="dialogStatus==='review'|| dialogStatus === 'watch' ?true:false">
                      <el-option v-for="item in senderNameEntity.options" :key="item.name" :label="item.name"
                                 :value="item.name" />
                    </el-select>
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="收货单位" prop="receiverName" label-width="140px">
                    <el-select v-model="entityForm.receiverName" placeholder="请选择收货单位" clearable filterable style="width:100%"
                               @change="changereceiverName"
                               :disabled="dialogStatus==='review'|| dialogStatus === 'watch' ?true:false">
                      <el-option v-for="item in receiverNameEntity.options" :key="item.name+item.type" :label="item.name"
                                 :value="item.name">
                        <span style="float: left">{{ item.name }}</span>
                        <el-tag style="float: right;" class="opt-btn" color="#69baff" v-if="item.type=='子公司'">
                          {{ item.type }}
                        </el-tag>
                        <el-tag style="float: right;" class="opt-btn" color="#f20813" v-if="item.type=='供应商'">
                          {{ item.type }}
                        </el-tag>
                        <el-tag style="float: right;" class="opt-btn" color="#f20813" v-if="item.type=='客户'">
                          {{ item.type }}
                        </el-tag>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同名称" prop="name" label-width="140px">
                    <el-input v-model="entityForm.name" clearable :disabled="dialogStatus==='review'?true:false"></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同编码" prop="code" label-width="140px">
                    <el-input v-model="entityForm.code" clearable :disabled="dialogStatus==='review'?true:false"></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="开始日期" prop="beginDate">
                    <date-select v-model="entityForm.beginDate" clearable :disabled="dialogStatus==='review'?true:false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="结束日期" prop="endDate">
                    <date-select v-model="entityForm.endDate" clearable :disabled="dialogStatus==='review'?true:false" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="合同签订日期" prop="signDate">
                    <date-select v-model="entityForm.signDate" clearable :disabled="dialogStatus==='review'?true:false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <!-- <el-form-item label="供应组负责人" prop="headSupplyGroup">
                    <el-input v-model="entityForm.headSupplyGroup" :disabled="dialogStatus==='review'?true:false">
                    </el-input>
                  </el-form-item> -->
                  <!-- <el-form-item label="供应组" prop="groupName">
                    <el-autocomplete v-model="entityForm.groupName" clearable ref="autocomplete" value-key="label"
                                     @clear="blurForBug" style="width:100%" :fetch-suggestions="querySearch" placeholder="请输入用供应组"
                                     @input="handleInput" @select="handlechangeInput"></el-autocomplete>
                  </el-form-item> -->
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">合同信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <!-- :rules="[{ required: true, message: '请选择一项' }]" -->
                  <el-form-item label="价格类型" prop="payWay">
                    <dict-select v-model="entityForm.payWay" type="price_type"
                                 :disabled="dialogStatus==='review'|| dialogStatus =='change'?true:false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="单价" prop="price">
                    <el-input v-model="entityForm.price" class="tsinput" autocomplete="off" clearable
                              @input="handleBlur(entityForm)" oninput="value=value.replace(/[^0-9.]/g,'')"
                              :disabled="dialogStatus==='review'|| dialogStatus === 'watch' || dialogStatus =='change' ?true:false">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="是否含税" prop="price">
                    <el-radio-group class="tsradio" v-model="hasFax" size="small" @change="changeTheme"
                                    :disabled="dialogStatus==='review'|| dialogStatus === 'watch' || dialogStatus =='change' ?true:false">
                      <el-radio :label="'Y'">是</el-radio>
                      <el-radio :label="'N'">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="吨数" prop="amount" label-width="140px">
                    <!-- <el-input v-model="entityForm.amount" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable :disabled="dialogStatus==='review'?true:false">
                      <template slot="append">吨</template>
                    </el-input> -->
                    <el-input v-model="entityForm.amount" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              @input="handleBlur(entityForm)" clearable
                              :disabled="dialogStatus==='review'|| dialogStatus === 'watch' || dialogStatus =='change' ?true:false">
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="总价" prop="totalMoney">
                    <el-input v-model="entityForm.totalMoney" clearable
                              :disabled="dialogStatus==='review' || dialogStatus =='change'?true:false" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="订单运费" prop="carriage">
                    <el-input v-model="entityForm.carriage" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable
                              :disabled="dialogStatus==='review'|| dialogStatus === 'watch' || dialogStatus =='change' ?true:false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <!-- <el-form-item label="拉运地点" prop="pullLocation">
                    <el-input v-model="entityForm.pullLocation"
                              :disabled="dialogStatus==='review'|| dialogStatus === 'watch' || dialogStatus =='change' ?true:false">
                    </el-input>
                  </el-form-item> -->
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col style="margin-bottom: 20px;">
            <div class="form-titlV form-warp">
              <span style="position: relative;"
                    :class="dialogStatus=='create' ||  dialogStatus=='update' ?'topbox':''">调价信息</span>
              <el-button type="export-all" @click="handleAdd"
                         v-if="dialogStatus=='create' || dialogStatus=='update'">新增调价</el-button>
            </div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-table :data="listV" style="width: 100%" stripe :header-cell-style="headClass">
                    <el-table-column label="价格" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.price" clearable
                                  :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'" />
                      </template>
                    </el-table-column>
                    <el-table-column label="是否含税" align="center">
                      <template slot-scope="scope">
                        <dict-select v-model="scope.row.hasFax" type="sync_type" :special="true"
                                     :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'" />
                      </template>
                    </el-table-column>
                    <el-table-column label="价格类型" align="center">
                      <template slot-scope="scope">
                        <dict-select v-model="scope.row.payWay" type="price_type"
                                     :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'" />
                      </template>
                    </el-table-column>
                    <el-table-column label="价格启用时间" align="center" width="200">
                      <template slot-scope="scope">

                        <el-date-picker style="width: 100%" v-model="scope.row.beginDate" value-format="yyyy-MM-dd HH:mm:ss"
                                        type="datetime" format="yyyy-MM-dd HH:mm:ss"
                                        :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'">
                        </el-date-picker>
                      </template>
                    </el-table-column>
                    <el-table-column label="价格截止时间" align="center" width="200">
                      <template slot-scope="scope">
                        <el-date-picker style="width: 100%" v-model="scope.row.endDate" value-format="yyyy-MM-dd HH:mm:ss"
                                        type="datetime" format="yyyy-MM-dd HH:mm:ss"
                                        :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'">
                        </el-date-picker>
                      </template>
                    </el-table-column>
                    <el-table-column label="状态" align="center">
                      <template slot-scope="scope">
                        <el-tag class="opt-btn" v-if="scope.row.state=='NEW'" color="#FF9639">
                          待审核
                        </el-tag>
                        <el-tag class="opt-btn" v-if="scope.row.state=='PASS'" color="#3caa0f">
                          已审核
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="备注" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.remarks" clearable
                                  :disabled="dialogStatus==='review' || dialogStatus === 'change' || dialogStatus==='watch'" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center"
                                     v-if="dialogStatus ==='create' || dialogStatus==='update'">
                      <template slot-scope="scope">
                        <el-button type="primary" @click="handleRemove(scope.row)" v-if="dialogStatus!= 'change'">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col style="margin-bottom: 20px;"
                  v-if="dialogStatus==='review'|| dialogStatus === 'watch' || dialogStatus =='change'">
            <div class="form-titlV form-warp">
              <span style="position: relative;" :class="dialogStatus=='create'?'topbox':''">调量信息</span>
            </div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-table :data="Modulationlist" style="width: 100%" stripe :header-cell-style="headClass">
                    <el-table-column label="原合同量（吨）" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.originalAmount" clearable
                                  :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'" />
                      </template>
                    </el-table-column>

                    <el-table-column label="追加量（吨）" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.addAmount" clearable
                                  :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'" />
                      </template>
                    </el-table-column>

                    <el-table-column label="追加后合同量（吨）" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.totalAmount" clearable
                                  :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'" />
                      </template>
                    </el-table-column>
                    <el-table-column label="追加时间" align="center">
                      <template slot-scope="scope">
                        <el-date-picker style="width: 100%" v-model="scope.row.addTime" value-format="yyyy-MM-dd HH:mm:ss"
                                        type="datetime" format="yyyy-MM-dd HH:mm:ss"
                                        :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'">
                        </el-date-picker>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col style="margin-bottom: 20px;">
            <div class="form-titlV form-warp">
              <span style="position: relative;"
                    :class="dialogStatus=='create' ||  dialogStatus=='update' ?'topbox':''">运费调价</span>
              <el-button type="export-all" @click="handleAddFreightAdjustment"
                         v-if="dialogStatus=='create' || dialogStatus=='update'">新增运费调价</el-button>
            </div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-table :data="FreightAdjustmentlist" stripe :header-cell-style="headClass">
                    <el-table-column label="运费" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.carriage" clearable
                                  :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'" />
                      </template>
                    </el-table-column>

                    <el-table-column label="运费启用时间" align="center">
                      <template slot-scope="scope">
                        <el-date-picker style="width: 100%" v-model="scope.row.beginDate" value-format="yyyy-MM-dd HH:mm:ss"
                                        type="datetime" format="yyyy-MM-dd HH:mm:ss"
                                        :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'">
                        </el-date-picker>
                      </template>
                    </el-table-column>
                    <el-table-column label="运费截止时间" align="center">
                      <template slot-scope="scope">
                        <el-date-picker style="width: 100%" v-model="scope.row.endDate" value-format="yyyy-MM-dd HH:mm:ss"
                                        type="datetime" format="yyyy-MM-dd HH:mm:ss"
                                        :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'">
                        </el-date-picker>
                      </template>
                    </el-table-column>
                    <el-table-column label="状态" align="center">
                      <template slot-scope="scope">
                        <el-tag class="opt-btn" v-if="scope.row.state=='NEW'" color="#FF9639">
                          待审核
                        </el-tag>
                        <el-tag class="opt-btn" v-if="scope.row.state=='PASS'" color="#3caa0f">
                          已审核
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="备注" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.remarks" clearable
                                  :disabled="dialogStatus==='review' || dialogStatus === 'change' || dialogStatus==='watch'" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center"
                                     v-if="dialogStatus ==='create' || dialogStatus==='update'">
                      <template slot-scope="scope">
                        <el-button type="primary" @click="handleRemoveFreightAdjustment(scope.row)"
                                   v-if="dialogStatus!= 'change'">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">合同指标</div>
            <div class="form-layout">
              <div class="tablebox">
                <div class="headbox">
                  <div class="headitem" v-for="(item,index) in headlist" :key="index"> {{item}}</div>
                </div>
                <div class="contenbox">
                  <div class="contenitem">
                    <el-input v-model="entityForm.cleanMt" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable>
                    </el-input>
                  </div>
                  <div class="contenitem">
                    <el-input v-model="entityForm.cleanAd" autocomplete="off" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')">
                    </el-input>
                  </div>
                  <div class="contenitem">
                    <el-input v-model="entityForm.cleanStd" clearable oninput="value=value.replace(/[^0-9.]/g,'')">
                    </el-input>
                  </div>
                  <div class="contenitem">
                    <el-input v-model="entityForm.cleanVdaf" clearable>
                    </el-input>
                  </div>
                  <div class="contenitem">
                    <el-input v-model="entityForm.crc" clearable></el-input>
                  </div>
                  <div class="contenitem">
                    <el-input v-model="entityForm.procG" clearable>
                    </el-input>
                  </div>
                  <div class="contenitem">
                    <el-input v-model="entityForm.procY" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                    </el-input>
                  </div>
                  <div class="contenitem">
                    <el-input v-model="entityForm.procX" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                    </el-input>
                  </div>
                  <div class="contenitem">
                    <el-input v-model="entityForm.recovery" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                    </el-input>
                  </div>
                  <div class="contenitem">
                    <el-input v-model="entityForm.qualCsr" clearable>
                    </el-input>
                  </div>
                  <div class="contenitem">
                    <el-input v-model="entityForm.macR0" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                    </el-input>
                  </div>
                  <div class="contenitem">
                    <el-input v-model="entityForm.macS" oninput="value=value.replace(/[^0-9.]/g,'')" clearable></el-input>
                  </div>
                </div>
              </div>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input class="font-col" type="textarea" v-model="entityForm.remarks" placeholder="请输入备注" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">附件信息</div>
            <div class="form-layout">
              <el-row>
                <el-col>
                  <el-form-item>
                    <el-collapse v-model=" collapse">
                      <el-collapse-item title="合同附件上传" name="1">
                        <div class="upload">
                          <div class="upload-list">
                            <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                              <!-- <el-image class="img" :src="item.uri" fit="fill" :preview-src-list="srcList" /> -->
                              <!-- <img src="@/assets/close.png" class="icon" @click="handleRemoveUpload(index)"> -->
                              <i class="el-icon-close icon" @click="handleRemoveUpload(index)"></i>
                              <el-link class="up-load-warp-link" :underline=" false" :href="item.uri">{{item.display}}</el-link>
                            </div>
                            <upload-attachment ref="upload" class="upload-attachment" :uploadData="uploadData"
                                               source="ContractBuy" :size="size" :limitNum="limitNum" :isShowFile="false"
                                               @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                               listType="text" />
                          </div>
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">其他信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50" v-if="perms[`${curd}:interface`]||false">
                <el-col>
                  <el-form-item label="是否开放接口" prop="isPublicBoolean">
                    <el-switch v-model="entityForm.isPublicBoolean"></el-switch>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer" v-if="isshow==false">
        <div v-if="dialogStatus!=='watch'">
          <el-button @click="handleClose1" class="dialog-footer-btns">取消</el-button>
          <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                     v-if="perms[`${curd}:update`]||false">保存
          </el-button>
        </div>
      </div>

      <div slot="footer" class="dialog-footer" v-else style="width:100%">
        <!-- 调价审核 -->
        <div style="width:calc(100% - 300px)" class="shenpi">
          <el-input v-if="entityForm.state=='NEW' " style="width:100%;" type="text" v-model="entityForm.reviewMessage"
                    placeholder="请输入审批意见" />
        </div>

        <div style="width:300px;display: flex;" v-if="entityForm.priceState=='NEW' && dialogStatus=='AdjustPriceReview'">
          <div v-if="perms[`${curd}:priceFactoryManagerWaitforReviewed`]||false">
            <el-button v-if="entityForm.curPriceFlowCode=='PRICE_FACTORY_MANAGER'" type="primary" class="dialog-footer-btns"
                       style="width:170px" :loading="entityFormLoading"
                       @click="passPriceStatefn(contractId,entityForm.curPriceFlowCode)">
              <!-- 调价审核通过(厂长) -->
              {{entityForm.curPriceFlowName}}
            </el-button>
          </div>
          <div v-if="perms[`${curd}:priceManagerWaitforReviewed`]||false">
            <el-button v-if="entityForm.curPriceFlowCode=='PRICE_MANAGER'" type="primary" class="dialog-footer-btns"
                       style="width:170px" :loading="entityFormLoading"
                       @click="passPriceStatefn(contractId,entityForm.curPriceFlowCode)">
              <!-- 调价审核通过(总经理) -->
              {{entityForm.curPriceFlowName}}
            </el-button>
          </div>
          <div v-if="perms[`${curd}:priceBossWaitforReviewed`]||false">
            <el-button v-if="entityForm.curPriceFlowCode=='PRICE_BOSS'" type="primary" class="dialog-footer-btns"
                       style="width:170px" :loading="entityFormLoading"
                       @click="passPriceStatefn(contractId,entityForm.curPriceFlowCode)">
              <!-- 调价审核通过(董事长) -->
              {{entityForm.curPriceFlowName}}
            </el-button>
          </div>
          <div>
            <el-button class="dialog-footer-btns" style="width:100px"
                       @click="rejectPriceStatefn(contractId,entityForm.curPriceFlowCode)">拒绝
            </el-button>
          </div>
        </div>

        <div style="width:300px;display: flex;" v-if="entityForm.carriageState=='NEW' && dialogStatus=='freightReview'">
          <!-- 运费调价审核 -->
          <div v-if="perms[`${curd}:carriageFactoryManagerWaitforReviewed`]||false">
            <el-button v-if="entityForm.curCarriageFlowCode=='CARRIAGE_FACTORY_MANAGER'" type="primary" class="dialog-footer-btns"
                       style="width:180px" :loading="entityFormLoading"
                       @click="passFreightStatefn(contractId,entityForm.curCarriageFlowCode)">
              <!-- 运费调价审核通过(厂长) -->
              {{entityForm.curCarriageFlowName}}
            </el-button>
          </div>

          <div v-if="perms[`${curd}:carriageManagerWaitforReviewed`]||false">
            <el-button v-if="entityForm.curCarriageFlowCode=='CARRIAGE_MANAGER'" type="primary" class="dialog-footer-btns"
                       style="width:180px" :loading="entityFormLoading"
                       @click="passFreightStatefn(contractId,entityForm.curCarriageFlowCode)">
              <!-- 运费调价审核通过(总经理) -->
              {{entityForm.curCarriageFlowName}}
            </el-button>
          </div>
          <div v-if="perms[`${curd}:carriageBossWaitforReviewed`]||false">
            <el-button v-if="entityForm.curCarriageFlowCode=='CARRIAGE_BOSS'" type="primary" class="dialog-footer-btns"
                       style="width:180px" :loading="entityFormLoading"
                       @click="passFreightStatefn(contractId,entityForm.curCarriageFlowCode)">
              <!-- 运费调价审核通过(董事长) -->
              {{entityForm.curCarriageFlowName}}
            </el-button>
          </div>
          <div>
            <el-button class="dialog-footer-btns" style="width:100px"
                       @click="rejectFreightStatefn(contractId,entityForm.curCarriageFlowCode)">拒绝
            </el-button>
          </div>
        </div>

        <div style="width:300px;display: flex;" v-if="Auditstatus">
          <div v-if="Auditstatus=='MANAGER'">
            <el-button v-if="perms[`${curd}:ismanagerreview`]||false" type="primary" class="dialog-footer-btns"
                       :loading="entityFormLoading"
                       @click="FinancePass(contractId,entityForm.reviewMessage,
                       entityForm.hasFax,entityForm.price,entityForm.cleanMt,entityForm.cleanAd,
                       entityForm.cleanStd,entityForm.procX,entityForm.cleanVdaf,entityForm.crc,entityForm.procG,entityForm.procY,
                       entityForm.recovery,entityForm.qualCsr,entityForm.macR0,entityForm.macS,entityForm.remarks,entityForm.curFlowCode)"
                       style="width:170px">
              <!-- 审核通过(总经理) -->
              {{entityForm.curFlowName}}
            </el-button>
          </div>
          <div v-if="Auditstatus=='FINANCE'">
            <el-button v-if="perms[`${curd}:isFinancereview`]||false" type="primary" class="dialog-footer-btns"
                       style="width:170px" :loading="entityFormLoading"
                       @click="FinancePass(contractId,entityForm.reviewMessage,
                       entityForm.hasFax,entityForm.price,entityForm.cleanMt,entityForm.cleanAd,
                       entityForm.cleanStd,entityForm.procX,entityForm.cleanVdaf,entityForm.crc,entityForm.procG,entityForm.procY,
                       entityForm.recovery,entityForm.qualCsr,entityForm.macR0,entityForm.macS,entityForm.remarks,entityForm.curFlowCode)">
              <!-- 审核通过(财务人员) -->
              {{entityForm.curFlowName}}
            </el-button>
          </div>
          <div v-if="Auditstatus=='FINANCE_LEADER'">
            <el-button v-if="perms[`${curd}:isFinanceLeaderreview`]||false" type="primary" class="dialog-footer-btns"
                       style="width:170px" :loading="entityFormLoading"
                       @click="FinancePass(contractId,entityForm.reviewMessage,
                       entityForm.hasFax,entityForm.price,entityForm.cleanMt,entityForm.cleanAd,
                       entityForm.cleanStd,entityForm.procX,entityForm.cleanVdaf,entityForm.crc,entityForm.procG,entityForm.procY,
                       entityForm.recovery,entityForm.qualCsr,entityForm.macR0,entityForm.macS,entityForm.remarks,entityForm.curFlowCode)">
              <!-- 审核通过(财务负责人) -->
              {{entityForm.curFlowName}}
            </el-button>
          </div>
          <div v-if="Auditstatus=='BOSS' || Auditstatus=='isBossreview'">
            <el-button v-if="perms[`${curd}:isBossreview`]||false" type="primary" class="dialog-footer-btns" style="width:170px"
                       :loading="entityFormLoading"
                       @click="FinancePass(contractId,entityForm.reviewMessage,
                       entityForm.hasFax,entityForm.price,entityForm.cleanMt,entityForm.cleanAd,
                       entityForm.cleanStd,entityForm.procX,entityForm.cleanVdaf,entityForm.crc,entityForm.procG,entityForm.procY,
                       entityForm.recovery,entityForm.qualCsr,entityForm.macR0,entityForm.macS,entityForm.remarks,entityForm.curFlowCode)">
              <!-- 审核通过(董事长) -->
              {{entityForm.curFlowName}}
            </el-button>
          </div>
          <div
               v-if="Auditstatus=='MANAGER' || Auditstatus=='FINANCE' || Auditstatus=='FINANCE_LEADER'  || Auditstatus=='BOSS' || Auditstatus=='isBossreview'">
            <el-button @click="ApprovednoPas(contractId,entityForm.reviewMessage,
                       entityForm.hasFax,entityForm.price,entityForm.cleanMt,entityForm.cleanAd,
                       entityForm.cleanStd,entityForm.procX,entityForm.cleanVdaf,entityForm.crc,entityForm.procG,entityForm.procY,
                       entityForm.recovery,entityForm.qualCsr,entityForm.macR0,entityForm.macS,entityForm.remarks,entityForm.curFlowCode)"
                       class="dialog-footer-btns" style="width:100px">拒绝
            </el-button>
          </div>
        </div>

      </div>

    </el-dialog>

    <!-- 调价模态框/调量模态框/运费调价模态框 600px-->
    <el-dialog :width="dialogStatus=='addAmount'?'600px':''" class="tsdialog" top="5vh" ref="priceadjustmentdialogStatus"
               :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="priceadjustmentdialogFormVisible"
               :before-close="handleClose1" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="priceadjustmentForm" :model="priceadjustmentForm" label-width="90px"
               :rules="priceadjustmentrules" label-position="top" v-if="priceadjustmentdialogFormVisible">
        <el-row v-if="dialogStatus==='priceAdjustment'">
          <el-col style="margin-bottom: 20px;">
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="价格" prop="price">
                    <el-input v-model="priceadjustmentForm.price" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="是否含税" prop="hasFax" label-width="140px">
                    <dict-select v-model="priceadjustmentForm.hasFax" type="sync_type" :special="true" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="价格类型" prop="payWay">
                    <dict-select v-model="priceadjustmentForm.payWay" type="price_type" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="价格启用时间" prop="beginDate">
                    <el-date-picker style="width: 100%" v-model="priceadjustmentForm.beginDate" value-format="yyyy-MM-dd HH:mm:ss"
                                    type="datetime" format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="价格截止时间" prop="endDate">
                    <el-date-picker style="width: 100%" v-model="priceadjustmentForm.endDate" value-format="yyyy-MM-dd HH:mm:ss"
                                    type="datetime" format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input v-model="priceadjustmentForm.remarks" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <!-- <el-form-item label="状态" prop="state">
                    <span>{{priceadjustmentForm.state}}</span>
                  </el-form-item> -->
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="dialogStatus==='addAmount'">
          <el-col style="margin-bottom: 20px;">
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="追加量（吨）" prop="addAmount">
                    <el-input v-model="priceadjustmentForm.addAmount" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="dialogStatus==='freightAdjustment'">
          <el-col style="margin-bottom: 20px;">
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="运费" prop="carriage">
                    <el-input v-model="priceadjustmentForm.carriage" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="运费启用时间" prop="beginDate">
                    <el-date-picker style="width: 100%" v-model="priceadjustmentForm.beginDate" value-format="yyyy-MM-dd HH:mm:ss"
                                    type="datetime" format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="运费截止时间" prop="endDate">
                    <el-date-picker style="width: 100%" v-model="priceadjustmentForm.endDate" value-format="yyyy-MM-dd HH:mm:ss"
                                    type="datetime" format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input v-model="priceadjustmentForm.remarks" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer" v-if="isshow==false">
        <div v-if="dialogStatus!=='watch'">
          <!-- 调价、调量、运费调价的保存 -->
          <el-button @click="handleClose1" class="dialog-footer-btns">取消</el-button>
          <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="savepriceadjustmentForm('priceadjustmentForm')" v-if="perms[`${curd}:update`]||false">保存
          </el-button>
        </div>
      </div>
    </el-dialog>

    <el-drawer title="合同附件列表" :visible.sync="drawer.visible" direction="rtl" :before-close="handleCloseDrawer">
      <template v-if="drawer.list.length">
        <div class="images-warp">
          <div class="img" v-for="(item,index) in drawer.list" :key="index">
            <!-- 图片预览 -->
            <div v-if="item.isImage" class="image-preview">
              <el-image class="preview-image" :src="item.uri" fit="contain" :preview-src-list="drawer.preview" />
              <div class="file-name">{{ item.display }}</div>
            </div>

            <!-- 文档展示 -->
            <div v-else-if="item.isDocument" class="document-preview">
              <i class="el-icon-document" style="font-size: 48px; color: #409EFF;"></i>
              <el-link :href="item.uri" :download="item.display" target="_blank" :underline="false" class="file-info">
                <div class="file-name">{{ item.display }}</div>
                <div class="file-type">{{ item.fileType.toUpperCase() }} 文档</div>
              </el-link>
            </div>

            <!-- Excel展示 -->
            <div v-else-if="item.isExcel" class="excel-preview">
              <i class="el-icon-s-data" style="font-size: 48px; color: #67C23A;"></i>
              <el-link :href="item.uri" :download="item.display" target="_blank" :underline="false" class="file-info">
                <div class="file-name">{{ item.display }}</div>
                <div class="file-type">Excel 表格</div>
                <!-- <div class="file-meta">已处理</div> -->
              </el-link>
            </div>

            <!-- 其他文件类型 -->
            <div v-else class="other-file">
              <i class="el-icon-files" style="font-size: 48px;"></i>
              <el-link :href="item.uri" :download="item.display" target="_blank" :underline="false" class="file-info">
                <div class="file-name">{{ item.display }}</div>
                <div class="file-type">{{ item.fileType.toUpperCase() }} 文件</div>
              </el-link>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <el-empty :image="emptyImgPath" :image-size="200" description="暂无图片" />
      </template>
    </el-drawer>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import CustomerModel from '@/model/user/customer'
import Model from '@/model/user/contractSell'
import productModel from '@/model/product/productList'
import { createMemoryField } from '@/utils/index'
import SupplyGroup from '@/model/SupplyGroup/SupplyGroup'
import pinyin from 'js-pinyin'
const form = {}
const Modulationform = {}
const FreightAdjustmentform = {}
export default {
  name: 'contractSell',
  mixins: [Mixins],
  data() {
    return {
      curd: 'contractSell',
      model: Model,
      steplist: [],
      steplistNEW: [],
      filterOption: {
        showMore: true,
        columns: [
          { prop: 'code', filter: 'filter_EQS_code', props: { placeholder: '请输入合同编号', clearable: true } },
          { prop: 'name', filter: 'filter_LIKES_name', props: { placeholder: '请输入合同名称', clearable: true } },
          {
            prop: 'productName',
            filter: 'filter_LIKES_productName',
            component: 'select-ProductName',
            props: { placeholder: '请选择品名', clearable: true, selecttype: 'productName' }
          }
        ]
      },
      entityForm: { ...Model.model, filter_EQS_type: '' },
      rules: {
        code: { required: true, message: '请输入合同编码', trigger: 'blur' },
        customerName: { required: true, message: '请输入买方', trigger: 'change' },
        secondParty: { required: true, message: '请输入卖方', trigger: 'blur' },
        hasFax: { required: true, message: '请输入选择是否含税', trigger: 'change' }
      },
      customerNameEntity: { value: '', options: [] },
      secondPartyEntity: { value: '', options: [] },

      nameEntity: { options: [], active: '' },
      memoryEntity: { fields: {}, triggered: false },
      fileList: [],
      limitNum: 50,
      uploadList: [], // 用于展示
      uploadData: { refId: '', refType: 'ContractSell' },
      drawer: {
        visible: false,
        list: [],
        preview: []
      },
      collapse: '1',
      emptyImgPath: require(`@/assets/empty_img.jpg`),
      contractId: '',
      isshow: false,
      Auditstatus: '',
      reviewLogList: [],
      Date: new Date(new Date().getTime()).Format('yyyyMMdd'),
      isdisabled: false,
      countList: [],
      hasFax: 'Y',

      headlist: [
        '全水(Mt)',
        '灰分(Ad)',
        '硫(St,d)',
        '挥发分(Vdaf)',
        '特征(CRC)',
        '粘结(G)',
        '胶质层(Y)',
        'X指标',
        '回收率',
        '热强度(CSR)',
        '岩相(R0)',
        '标准差S'
      ],
      priceadjustmentForm: {},
      priceadjustmentrules: {},
      priceadjustmentdialogFormVisible: false,
      listV: [{ ...form }],
      Modulationlist: [{ ...Modulationform }],
      FreightAdjustmentlist: [{ ...FreightAdjustmentform }],
      senderNameEntity: { value: '', options: [] },
      receiverNameEntity: { value: '', options: [] },

      headSupplyGroupEntity: { options: [], active: [] }
    }
  },
  computed: {
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    }
  },
  watch: {
    'entityForm.isPublicBoolean': {
      handler(v) {
        this.entityForm.isPublic = v ? 'Y' : 'N'
      }
    },
    'entityForm.productName'(name) {
      if (name) {
        const item = this.nameEntity.options.find((item) => item.name === name)
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.entityForm.coalType = item.coalCategory || ''
        this.entityForm.coalCategory = item.type
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
        this.entityForm.coalCategory = ''
      }
    },
    'customerNameEntity.value'(id) {
      if (!id) return
      const item = this.customerNameEntity.options.find((item) => item.id === id)
      if (!item) return
      if (this.dialogStatus !== 'watch') {
        this.getNameCodefn(item.name)
      }
      this.entityForm.customerId = id
      this.entityForm.customerName = item.name
      this.entityForm.firstParty = item.name
    },
    'entityForm.receiverName'(name) {
      if (!name) {
        this.entityForm.receiverName = ''
      }
      const item = this.receiverNameEntity.options.find((item) => item.name === name)
      if (!item) return
      //  + '--' + item.type
      this.entityForm.receiverName = item.name
    },
    activetype: function (newV, oldV) {
      if (newV) {
        this.getlist(newV)
      } else {
        this.getlist('DRAFT,REJECT,NEW,PASS_FINANCE,PASS')
      }
      this.getnumber()
    }
  },

  async created() {
    this.memoryEntity.fields = createMemoryField({
      fields: ['id', 'coalType', 'coalCategory', 'productName', 'productCode', 'productId'],
      target: this.entityForm
    })
    this.getName()
    this.getContractList()
    this.getnumber()
    // this.permsActionLsit(this.curd)
    this.permsActionbtn(this.curd)
    this.entityForm.secondParty = ''
    // if (this.$route.params.type) {
    //   this.Auditstatus = this.$route.params.type
    //   this.activetype = this.$route.params.type
    //   this.contractId = this.$route.params.id
    //   this.handleUpdate(this.$route.params.id, 'review')
    // } else {
    //   this.activetype = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
    //   this.actionList.forEach((val, index) => {
    //     if (val.type == this.activetype) {
    //       val.active = true
    //     } else {
    //       val.active = false
    //     }
    //   })
    // }

    if (this.$route.params.type) {
      this.activetype = this.$route.params.type
      this.contractId = this.$route.params.id
      this.Auditstatus = this.$route.params.Auditstatus
      if (this.$route.params.Auditstatus == 'AdjustPriceReview' || this.$route.params.Auditstatus == 'freightReview') {
        this.Auditstatus = ''
        this.handleUpdate(this.$route.params.id, this.$route.params.Auditstatus)
      } else {
        this.handleUpdate(this.$route.params.id, 'review')
      }
      // this.handleUpdate(this.$route.params.id, 'review')
    } else {
      this.activetype = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
      this.actionList.forEach((val, index) => {
        if (val.type == this.activetype) {
          val.active = true
        } else {
          val.active = false
        }
      })
    }

    this.entityForm.hasFax = 'Y'
    this.isdisabled = false
    //获取卖方列表
    this.getSellContractPartyfn()

    //获取收货单位列表
    this.getsenderListfn()
    //获取发货单位列表
    this.getContractPartyfn()
    //获取供应组列表
    this.getSupplyGroup()
  },
  methods: {
    async handlechangeInput(val) {
      console.log(val)
      this.entityForm.groupName = val.label
      this.entityForm.groupId = val.value
      this.$emit('update:value', val.label)
    },
    handleInput(val) {
      this.$emit('update:value', val)
      console.log(val)
      if (val) {
        this.entityForm.groupName = val
        this.entityForm.groupId = ''
      }
    },
    blurForBug() {
      this.entityForm.groupId = ''
    },
    querySearch(queryString, cb) {
      let list = this.headSupplyGroupEntity.options
      console.log(list)
      let results = queryString ? list.filter(this.createFilter(queryString)) : list
      cb(results)
    },
    createFilter(queryString) {
      console.log(queryString)
      return (restaurant) => restaurant.label.toString().includes(queryString.toString())
    },

    handleSupplyGroup({ value, label }) {
      console.log(value)
      console.log(label)
      this.entityForm = { ...this.entityForm, groupName: label, groupId: value }
    },

    async getSupplyGroup() {
      try {
        let options = []
        const { data } = await SupplyGroup.page({ size: 1000 })
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        console.log(options)
        this.headSupplyGroupEntity.options = options
      } catch (e) { }
    },

    // 调价审核通过
    async passPriceStatefn(id, curPriceFlowCode) {
      const res = await this.model.passPriceState({ id: id, curPriceFlowCode: curPriceFlowCode })
      if (res) {
        this.$message({ type: 'success', message: '调价审核成功!' })
        this.getList()
        this.getnumber()
        this.resetVariant()
        this.dialogFormVisible = false
      }
    },
    // 调价审核拒绝
    async rejectPriceStatefn(id, curPriceFlowCode) {
      const res = await this.model.rejectPriceState({ id: id, curPriceFlowCode: curPriceFlowCode })
      if (res) {
        this.$message({ type: 'success', message: '拒绝调价审核成功!' })
        this.getList()
        this.getnumber()
        this.resetVariant()
        this.dialogFormVisible = false
      }
    },
    // 运费调价审核通过
    async passFreightStatefn(id, curCarriageFlowCode) {
      const res = await this.model.passCarriageState({ id: id, curCarriageFlowCode: curCarriageFlowCode })
      if (res) {
        this.$message({ type: 'success', message: '运费调价审核成功!' })
        this.getList()
        this.getnumber()
        this.resetVariant()
        this.dialogFormVisible = false
      }
    },
    //  运费调价审核拒绝
    async rejectFreightStatefn(id, curCarriageFlowCode) {
      const res = await this.model.rejectCarriageState({ id: id, curCarriageFlowCode: curCarriageFlowCode })
      if (res) {
        this.$message({ type: 'success', message: '拒绝运费调价审核成功!' })
        this.getList()
        this.getnumber()
        this.resetVariant()
        this.dialogFormVisible = false
      }
    },

    handleBlur(entityForm) {
      if (this.dialogStatus == 'create') {
        if (entityForm.amount != '' && entityForm.price != '') {
          let totalMoney = parseInt(entityForm.amount * entityForm.price)
          if (isNaN(totalMoney) == false) {
            this.entityForm = { ...this.entityForm, totalMoney: totalMoney }
          }
        }
      } else if (this.dialogStatus == 'update') {
        if (entityForm.amount != '' && entityForm.price != '') {
          let totalMoney = parseInt(entityForm.amount * entityForm.price)
          if (isNaN(totalMoney) == false) {
            this.entityForm = { ...this.entityForm, totalMoney: totalMoney }
          }
        }
      }
    },
    async getsenderListfn() {
      //获取收货单位列表
      const res = await this.model.getfindBuyAndSupplier()
      if (res.data.length) this.receiverNameEntity.options = res.data
    },
    async getContractPartyfn() {
      //获取发货单位列表
      const res = await this.model.getContractParty()
      if (res.data.length) this.senderNameEntity.options = res.data
    },

    async getSellContractPartyfn() {
      const res = await this.model.getSellContractParty()
      if (res.data.length) this.secondPartyEntity.options = res.data
    },

    async saveEntityForm(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        const list = []
        const Freightlist = []
        if (this.listV.length) {
          this.listV.forEach((item) => {
            let obj = {}
            obj.price = item.price
            obj.payWay = item.payWay
            obj.beginDate = item.beginDate
            obj.endDate = item.endDate
            obj.remarks = item.remarks
            obj.hasFax = item.hasFax
            obj.state = item.state
            if (item.id !== undefined) {
              obj.ext = item.id
              obj.id = item.id
            } else {
              obj.ext = item.ext
            }
            list.push({ ...obj })
          })
          // if (!this.checkParams(list)) return false
        }
        if (this.FreightAdjustmentlist.length) {
          this.FreightAdjustmentlist.forEach((item) => {
            let obj = {}
            obj.carriage = item.carriage
            obj.beginDate = item.beginDate
            obj.endDate = item.endDate
            obj.state = item.state
            obj.remarks = item.remarks
            if (item.id !== undefined) {
              obj.ext = item.id
              obj.id = item.id
            } else {
              obj.ext = item.ext
            }
            Freightlist.push({ ...obj })
          })
          // if (!this.checkParamsV(Freightlist)) return false
        }

        this.entityFormLoading = true
        if (this.dialogStatus === 'change') {
          //已审核 修改
          const res = await Model.save({
            ...this.entityForm,
            contractSellPriceList: list,
            contractSellCarriageList: Freightlist
          })
          this.entityFormLoading = false
          if (res) {
            this.resetVariant()
            this.getList()
            this.getnumber()
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else {
          //新增。草稿箱编辑
          const res = await Model.saveContractSellNew({
            ...this.entityForm,
            contractSellPriceList: list,
            contractSellCarriageList: Freightlist
          })
          this.entityFormLoading = false
          if (res) {
            this.resetVariant()
            this.getList()
            this.getnumber()
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        }
      }
    },
    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },
    handleAddModulation() {
      const form = { ...this.Modulationform }
      form.ext = Math.random().toFixed(6).slice(-6)
      this.Modulationlist.push(form)
    },
    handleRemoveModulation({ ext }) {
      const index = this.Modulationlist.findIndex((item) => item.ext === ext)
      if (index === -1) return false
      this.Modulationlist.splice(index, 1)
    },
    handleAdd() {
      const form = { ...this.form }
      form.ext = Math.random().toFixed(6).slice(-6)
      this.listV.push(form)
    },
    handleRemove({ ext }) {
      const index = this.listV.findIndex((item) => item.ext === ext)
      if (index === -1) return false
      this.listV.splice(index, 1)
    },
    handleAddFreightAdjustment() {
      const form = { ...this.form }
      form.ext = Math.random().toFixed(6).slice(-6)
      this.FreightAdjustmentlist.push(form)
    },
    handleRemoveFreightAdjustment({ ext }) {
      const index = this.FreightAdjustmentlist.findIndex((item) => item.ext === ext)
      if (index === -1) return false
      this.FreightAdjustmentlist.splice(index, 1)
    },

    //调价申请
    async priceAdjustmentfn(item, type) {
      this.priceadjustmentForm = {}
      if (type == 'priceAdjustment') {
        //调价
        this.isshow = false
        this.dialogStatus = 'priceAdjustment'
      }
      if (type == 'addAmount') {
        //调量
        this.isshow = false
        this.dialogStatus = 'addAmount'
      }
      if (type == 'freightAdjustment') {
        //运费调价
        this.isshow = false
        this.dialogStatus = 'freightAdjustment'
      }
      this.priceadjustmentdialogFormVisible = true
      this.priceadjustmentForm = { ...this.priceadjustmentForm, contractSellId: item.id }
    },
    //调价保存
    async savepriceadjustmentForm(priceadjustmentForm) {
      let valid
      try {
        valid = await this.$refs[priceadjustmentForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        this.entityFormLoading = true
        if (this.dialogStatus == 'addAmount') {
          //调量保存
          //新增。草稿箱编辑
          const res = await Model.saveAddAmount({ ...this.priceadjustmentForm })
          this.entityFormLoading = false
          if (res) {
            this.resetVariant()
            this.getList()
            this.getnumber()
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else if (this.dialogStatus == 'priceAdjustment') {
          //调价保存
          //新增。草稿箱编辑
          const res = await Model.saveAdjustPrice({ ...this.priceadjustmentForm })
          this.entityFormLoading = false
          if (res) {
            this.resetVariant()
            this.getList()
            this.getnumber()
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else if (this.dialogStatus == 'freightAdjustment') {
          //运费调价保存
          //新增。草稿箱编辑
          const res = await Model.saveContractSellCarriage({ ...this.priceadjustmentForm })
          this.entityFormLoading = false
          if (res) {
            this.resetVariant()
            this.getList()
            this.getnumber()
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        }
      }
    },

    async getNameCodefn(customerName) {
      const res = await this.model.getNameCode(customerName)
      this.entityForm = { ...this.entityForm, name: res.data.name, code: res.data.code }
    },
    handleCreate() {
      this.hasFax = 'Y'
      this.entityForm.hasFax = 'Y'
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.secondPartyEntity.value = ''
      this.listV = []
      this.FreightAdjustmentlist = []
      this.Modulationlist = []
    },
    changeSecondParty(val) {
      this.entityForm.secondParty = val
      this.secondPartyEntity.value = val
    },
    changereceiverName(val) {
      this.entityForm.receiverName = val
    },
    async handleCancel(row) {
      this.$confirm('确定要作废吗，作废后不可恢复哦！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await this.model.Cancel({
            id: row.id,
            reviewMessage: row.reviewMessage,
            hasFax: row.hasFax,
            price: row.price,
            cleanMt: row.cleanMt,
            cleanAd: row.cleanAd,
            cleanStd: row.cleanStd,
            procX: row.procX,
            cleanVdaf: row.cleanVdaf,
            crc: row.crc,
            procG: row.procG,
            procY: row.procY,
            recovery: row.recovery,
            qualCsr: row.qualCsr,
            macR0: row.macR0,
            macS: row.macS,
            remarks: row.remarks
          })
          if (res) {
            this.$message({ type: 'success', message: '操作成功!' })
            this.getList()
            this.getnumber()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '操作取消' }))
    },
    changeTheme(val) {
      this.hasFax = val
      this.entityForm.hasFax = val
    },
    async getnumber() {
      const res = await this.model.getcontractSellState()
      this.countList = res.data
    },

    //点击状态获取数据
    async getlist(newV) {
      this.activetype = newV
      this.actionList.forEach((val, index) => {
        if (val.type == newV) {
          val.active = true
          // this.$set(this.actionList, index, val)
          // this.replyTo(index)
        } else {
          val.active = false
        }
      })
      this.filtersSeach.filter_INS_state = newV
      this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    },
    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      res.data.attachmentList.forEach((item, index) => {
        const fileType = item.uri.split('.').pop().toLowerCase()
        item.fileType = fileType
        // 添加文件类型判断
        item.isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileType)
        item.isDocument = ['doc', 'docx', 'pdf'].includes(fileType)
        item.isExcel = ['xls', 'xlsx'].includes(fileType)
      })
      this.drawer.list = res.data.attachmentList
      this.drawer.preview = res.data.attachmentList
        .filter(item => item.isImage)
        .map(item => item.uri)
      this.drawer.visible = true
    },

    async handleRemoveUpload(index) {
      const { id } = this.uploadList[index]
      this.$refs.upload.attachmentDelete({ id, index })
    },

    handleUploadSuccess(res) {
      this.uploadList = [...this.uploadList, res]
      // 上传完毕后更新展示的列表和把新增的附件id塞入到
      this.entityForm.attachmentList.push({ id: res.id })
    },
    handleUploadDelete({ status, index }) {
      this.uploadList.splice(index, 1)
    },

    // 保存取消时会触发初始化数据
    // resetVariant() {
    //   this.entityFormLoading = false
    //   this.dialogFormVisible = false
    //   this.isdisabled = false
    //   this.steplist = []
    //   if (this.dialogStatus == 'create') {
    //     this.secondPartyEntity.value = ''
    //   }
    //   this.isshow = false
    //   this.uploadList = [] // 清空下载列表
    //   this.fileList = []
    //   this.reviewLogList = []
    //   this.supplierEntity.value = ''
    //   this.hasFax = 'Y'
    //   this.entityForm.hasFax = 'Y'
    //   this.nameEntity.active = ''
    //   this.entityForm = { ...this.model.model }
    //   this.entityForm.secondParty = ''
    // },
    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.priceadjustmentdialogFormVisible = false
      this.isdisabled = false
      this.uploadList = [] // 清空下载列表
      this.fileList = []
      this.reviewLogList = []
      this.steplist = []
      this.secondPartyEntity.value = ''
      // this.state = ''
      this.isshow = false
      this.customerNameEntity.value = ''
      this.hasFax = 'Y'
      this.entityForm.hasFax = 'Y'
      this.receiverNameEntity.value = ''
      this.listV = []
      this.Modulationlist = []
      this.FreightAdjustmentlist = []
      this.edittjprice = {}
      // this.nameEntity.active = ''
      // this.entityForm = {
      //   ...this.entityForm,
      //   coalCategory: '',
      //   coalType: '',
      //   productId: '',
      //   productName: '',
      //   productCode: '',
      //   code: '',
      // }

      this.nameEntity.active = ''
      this.entityForm = { ...this.model.model }
      this.handleAdd()
      // this.handleAddModulation()
      this.handleAddFreightAdjustment()
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      // if (!this.memoryEntity.triggered) {
      //   this.nameEntity.active = ''
      //   this.entityForm = { ...this.model.model }
      // } else {
      //   this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      // }
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) { }
    },
    handleNameEntity(val) {
      this.entityForm.productName = val
      this.isdisabled = true
    },

    async getContractList() {
      let { data } = await await CustomerModel.page({ size: 999 })
      if (data.records.length) this.customerNameEntity.options = data.records
    },
    handleWatchForm(item) {
      this.customerNameEntity.value = item.customerId
      this.entityForm = { ...item }
      this.isshow = false
      this.dialogStatus = 'watch'
      this.dialogFormVisible = true
    },
    // async handleUpdate(item, type) {
    //   if (typeof item == 'object') {
    //     this.entityForm = { ...item }
    //     this.contractId = item.id
    //     this.Auditstatus = item.state
    //     if (type == 'review') {
    //       this.dialogStatus = 'review'
    //       this.isshow = true
    //     } else if (type == 'update') {
    //       this.dialogStatus = 'update'
    //       this.isshow = false
    //     } else if (type == 'watch') {
    //       this.isshow = false
    //       this.dialogStatus = 'watch'
    //     } else if (type == 'change') {
    //       this.isshow = false
    //       this.dialogStatus = 'change'
    //     }
    //     this.supplierEntity.value = this.entityForm.customerId
    //     this.secondPartyEntity.value = this.entityForm.secondParty
    //     this.dialogFormVisible = true
    //     // 上传所需要的配置
    //     this.uploadData.refId = item.id
    //     const { data } = await this.model.getUploadList(item.id)
    //     this.entityForm = { ...data }
    //     // console.log(data)
    //     this.hasFax = this.entityForm.hasFax
    //     this.reviewLogList = data.reviewLogList
    //     this.uploadList = data.attachmentList
    //     let newarry = []
    //     if (data.reviewLogList.length > 0) {
    //       data.reviewLogList.forEach((item, index) => {
    //         newarry.push(item.reviewMessage)
    //       })
    //       // this.entityForm.reviewMessage = newarry.toString()
    //       this.entityForm.reviewMessage = ''
    //     }
    //   } else if (typeof item == 'string') {
    //     if (type == 'review') {
    //       this.isshow = true
    //       this.dialogStatus = 'review'
    //     }
    //     this.supplierEntity.value = this.entityForm.customerId
    //     this.secondPartyEntity.value = this.entityForm.secondParty
    //     this.dialogFormVisible = true
    //     // 上传所需要的配置
    //     this.uploadData.refId = item
    //     const { data } = await this.model.getUploadList(item)
    //     this.entityForm = { ...data }
    //     this.reviewLogList = data.reviewLogList
    //     this.uploadList = data.attachmentList
    //     let newarry = []
    //     if (data.reviewLogList.length > 0) {
    //       data.reviewLogList.forEach((itemv, index) => {
    //         newarry.push(itemv.reviewMessage)
    //       })
    //       this.entityForm.reviewMessage = newarry.toString()
    //     }
    //   }
    // },
    async lookbtn(item) {
      const { data } = await this.model.findContractSellFlow(item.id)
      data.forEach((element, index) => {
        element.flowDesignList.forEach((value, indevc) => {
          value.active = false
          if (element.curFlowCode == value.code) {
            var citrus = element.flowDesignList
            citrus.forEach((value) => {
              value.active = true
            })
          }
        })
      })
      this.steplistNEW = data
    },
    async handleUpdate(item, type, gettype) {
      console.log(item)
      console.log(type)
      if (typeof item == 'object') {
        this.entityForm = { ...item }
        this.contractId = item.id
        // this.Auditstatus = item.state
        this.Auditstatus = item.curFlowCode
        if (type == 'review') {
          // this.isshow = copyToResource
          this.isshow = true
          this.dialogStatus = 'review'
        } else if (type == 'update') {
          this.isshow = false
          this.dialogStatus = 'update'
        } else if (type == 'watch') {
          this.isshow = false
          this.dialogStatus = 'watch'
        } else if (type == 'change') {
          this.isshow = false
          this.dialogStatus = 'change'
        } else if (type == 'AdjustPriceReview') {
          this.isshow = true
          this.dialogStatus = 'AdjustPriceReview'
        } else if (type == 'freightReview') {
          this.isshow = true
          this.dialogStatus = 'freightReview'
        }

        this.dialogFormVisible = true
        // 上传所需要的配置
        this.uploadData.refId = item.id

        const { data } = await this.model.getUploadList(item.id)
        // 调价信息
        let list = []
        data.contractSellPriceList.forEach((item) => {
          let obj = {}
          obj.price = item.price
          obj.payWay = item.payWay
          obj.beginDate = item.beginDate
          obj.endDate = item.endDate
          obj.remarks = item.remarks
          obj.hasFax = item.hasFax
          obj.id = item.id
          obj.ext = item.id
          obj.state = item.state
          list.push({ ...obj })
        })
        this.listV = list
        //  调量信息
        let Amountlist = []
        data.contractSellAmountList.forEach((item) => {
          let obj = {}
          obj.addAmount = item.addAmount
          obj.id = item.id
          obj.ext = item.id
          obj.originalAmount = item.originalAmount
          obj.totalAmount = item.totalAmount
          obj.addTime = item.addTime
          Amountlist.push({ ...obj })
        })
        this.Modulationlist = Amountlist
        // 运费调价
        let Freightlist = []
        data.contractSellCarriageList.forEach((item) => {
          let obj = {}
          obj.carriage = item.carriage
          obj.beginDate = item.beginDate
          obj.endDate = item.endDate
          obj.remarks = item.remarks
          obj.id = item.id
          obj.ext = item.id
          obj.state = item.state
          Freightlist.push({ ...obj })
        })
        this.FreightAdjustmentlist = Freightlist

        this.entityForm = { ...data }
        this.customerNameEntity.value = this.entityForm.customerId
        this.secondPartyEntity.value = this.entityForm.secondParty
        if (gettype) {
          const { data } = await Model.getlclist({ type: gettype })
          if (gettype == 'CONTRACT_SELL') {
            this.entityForm.stepcode = this.entityForm.curFlowCode
            this.entityForm.stepName = this.entityForm.curFlowName
          } else if (gettype == 'CONTRACT_SELL_PRICE') {
            this.Auditstatus = ''
            //调价审核
            this.entityForm.stepcode = this.entityForm.curPriceFlowCode
            this.entityForm.stepName = this.entityForm.curPriceFlowName
          } else if (gettype == 'CONTRACT_SELL_CARRIAGE') {
            this.Auditstatus = ''
            //运费调价审核
            this.entityForm.stepcode = this.entityForm.curCarriageFlowCode
            this.entityForm.stepName = this.entityForm.curCarriageFlowName
          }
          data.forEach((element, index) => {
            element.active = false
            if (element.code === this.entityForm.stepcode) {
              // var citrus = data.slice(0, index)
              var citrus = data
              // console.log(citrus)
              citrus.forEach((value) => {
                value.active = true
              })
            }
          })
          this.steplist = data
        }

        this.receiverNameEntity.value = this.entityForm.receiverName
        this.hasFax = this.entityForm.hasFax
        this.reviewLogList = data.reviewLogList
        this.uploadList = data.attachmentList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((item, index) => {
            newarry.push(item.reviewMessage)
          })
          // this.entityForm.reviewMessage = newarry.toString()
          this.entityForm.reviewMessage = ''
        }
      } else if (typeof item == 'string') {
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        } else if (type == 'AdjustPriceReview') {
          this.isshow = true
          this.dialogStatus = 'AdjustPriceReview'
        } else if (type == 'freightReview') {
          this.isshow = true
          this.dialogStatus = 'freightReview'
        }

        this.dialogFormVisible = true
        // 上传所需要的配置
        this.uploadData.refId = item
        const { data } = await this.model.getUploadList(item)
        // 调价信息
        let list = []
        data.contractSellPriceList.forEach((item) => {
          let obj = {}
          obj.price = item.price
          obj.payWay = item.payWay
          obj.beginDate = item.beginDate
          obj.endDate = item.endDate
          obj.remarks = item.remarks
          obj.hasFax = item.hasFax
          obj.id = item.id
          obj.ext = item.id
          obj.state = item.state
          list.push({ ...obj })
        })
        this.listV = list
        //  调量信息
        let Amountlist = []
        data.contractSellAmountList.forEach((item) => {
          let obj = {}
          obj.addAmount = item.addAmount
          obj.id = item.id
          obj.ext = item.id
          obj.originalAmount = item.originalAmount
          obj.totalAmount = item.totalAmount
          obj.addTime = item.addTime
          Amountlist.push({ ...obj })
        })
        this.Modulationlist = Amountlist
        // 运费调价
        let Freightlist = []
        data.contractSellCarriageList.forEach((item) => {
          let obj = {}
          obj.carriage = item.carriage
          obj.beginDate = item.beginDate
          obj.endDate = item.endDate
          obj.remarks = item.remarks
          obj.id = item.id
          obj.ext = item.id
          obj.state = item.state
          Freightlist.push({ ...obj })
        })
        this.FreightAdjustmentlist = Freightlist

        this.entityForm = { ...data }
        this.customerNameEntity.value = this.entityForm.customerId
        this.secondPartyEntity.value = this.entityForm.secondParty

        this.receiverNameEntity.value = this.entityForm.receiverName
        this.reviewLogList = data.reviewLogList
        this.uploadList = data.attachmentList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((item, index) => {
            newarry.push(item.reviewMessage)
          })
          this.entityForm.reviewMessage = newarry.toString()
        }
      }
    },

    handleClose1() {
      this.resetVariant()
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
::v-deep .el-input.is-disabled .el-input__inner {
  color: #606266;
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.form-titlV {
  font-size: 16px;
  position: relative;
  padding: 7px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    // top: 10px;
    bottom: 11px;
    background: #2f79e8;
    height: 16px;
  }
}

.lookCurFlowNamecss {
  .opt-btn {
    color: #e6a23c !important;
    border-color: #faecd8 !important;
    border: solid 1px #faecd8 !important;
  }
}
.colorBLUE {
  color: #2f79e8 !important;
}
.bordercolorBLUE {
  border: solid 1px #2f79e8 !important;
}
.bzminbox:first-child {
  width: 50% !important;
  .lin {
    width: 0px !important;
    display: none;
  }
  .context {
    width: 100% !important;
  }
}
.bzbox {
  width: 100%;
  display: flex;
  font-size: 12px;
  .bzminbox {
    width: 100%;
    display: flex;
    .icon {
      font-size: 30px;
    }
    .context {
      width: 50%;
      color: #ccc;
      text-align: center;
    }
    .lin {
      width: 50%;
      height: 1px;
      border: solid 1px #606266;
      margin-top: 15px;
    }
  }
}

.bzminboxV:first-child {
  width: 50% !important;
  .lin {
    width: 0px !important;
    display: none;
  }
  .context {
    width: 100% !important;
  }
}
.bzboxV {
  width: 100%;
  display: flex;
  font-size: 12px;
  .bzminboxV {
    width: 100%;
    display: flex;
    .icon {
      font-size: 30px;
    }
    .context {
      width: 50%;
      color: #ccc;
      text-align: center;
    }
    .lin {
      width: 50%;
      height: 1px;
      border: solid 1px #606266;
      margin-top: 15px;
    }
  }
}
.topbox {
  top: 10px;
}
.tablebox {
  width: 100%;
  .headbox {
    width: 100%;
    display: flex;
    align-items: center;
    background: rgb(47, 121, 232);
    // border: solid 1px #f4f4f4;
    font-size: 12px;
    color: #fff;
    .headitem {
      box-sizing: border-box;
      text-align: center;
      padding: 10px 0;
      // border-right: solid 1px #ccc;
      width: calc(100% / 12);
    }
  }
  .contenbox {
    width: 100%;
    display: flex;
    align-items: center;
    // padding: 10px;
    border: solid 1px #f4f4f4;
    font-size: 12px;
    box-sizing: border-box;
    .contenitem {
      width: calc(100% / 12);
      padding: 7px 5px;
      // border: solid 1px #f4f4f4;

      // ::v-deep .el-input__inner {
      //   border: 1px solid #fff;
      // }
      ::v-deep .el-input--suffix .el-input__inner {
        padding: 0 5px;
      }
    }
  }

  ul {
    width: 100%;
    list-style: none;
    display: flex;
    align-items: center;
    li {
      width: calc(100% / 12);
    }
  }
}

.images-warp {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-start;
  align-content: flex-start;
  flex-flow: wrap;
  .img {
    width: 33.33%;
    display: flex;
    justify-content: flex-start;
    flex-flow: column wrap;
    margin-bottom: 10px;
    opacity: 0.95;
    transition: 1s all;
    &:hover {
      opacity: 1;
    }
    &-title {
      text-align: center;
      font-size: 14px;
      padding: 2px 0;
    }
    .img {
      margin: 0 auto;
      // border: 1px solid #ff9639;
      cursor: pointer;
      border-radius: 4px;
      width: 100px;
      height: 100px;
      img {
        width: 100%;
        margin: 0 auto;
      }
    }
  }
}
::v-deep .el-drawer__header > :first-child {
  font-size: 14px;
}
.attachment {
  width: 50px;
  height: 50px;
  cursor: pointer;
  border: 1px solid #ccc;
  border-radius: 3px;
  box-shadow: 0 2px 4px #adabab1f, 0 0 6px #aaa6a60a;
  &-img {
    width: 100%;
  }
}

::v-deep .el-collapse-item__arrow {
  display: none;
}

.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}
.font-col {
  ::v-deep .el-textarea__inner {
    color: #000;
  }
}
</style>
<style scoped>
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}

.file-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #409eff;
  overflow: hidden;
}

.file-name {
  margin-top: 5px;
  font-size: 12px;
  text-align: center;
  word-break: break-all;
}

.attachment-container {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.attachment-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.attachment-item :hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.image-preview {
  width: 100%;
}

.image-preview .preview-image {
  width: 100%;
  height: 150px;
}
.image-preview .file-name {
  margin-top: 10px;
  font-size: 12px;
  color: #606266;
  word-break: break-all;
}
.document-preview,
.excel-preview,
.other-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}
.document-preview,
.excel-preview,
.other-file .file-info {
  margin-top: 10px;
  width: 100%;
}

.document-preview,
.excel-preview,
.other-file .file-info .file-name {
  font-size: 13px;
  color: #303133;
  word-break: break-all;
}

.document-preview,
.excel-preview,
.other-file .file-info .file-type {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.document-preview,
.excel-preview,
.other-file .file-info .file-meta {
  font-size: 11px;
  color: #67c23a;
  margin-top: 5px;
  font-style: italic;
}

.excel-preview .file-meta {
  background-color: #f0f9eb;
  padding: 2px 5px;
  border-radius: 3px;
}

/* .dialog-footer >>> .dialog-footer-btns {
  border: 1px solid #2f79e8;
  color: #2f79e8;
} */
/* .opt-btn {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
}
.opt-btnV {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
} */
</style>