<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" otherHeight="125" title="客户">
      <el-table-column label="操作" slot="opt" max-width="100" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)" v-if="perms[`${curd}:update`]||false">编辑
          </el-tag>
          <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除</el-tag>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog width="880px" top="25vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :rules="entityFormRules" :status-icon="true" ref="entityForm" :model="entityForm"
               label-width="90px" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="客户名称" prop="name">
                    <el-input v-model="entityForm.name" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="客户编码" prop="code">
                    <el-input v-model="entityForm.code" disabled></el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="负责人" prop="linkman" label-width="140px">
                    <el-input v-model="entityForm.linkman" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="联系电话" prop="phone" label-width="140px">
                    <el-input v-model="entityForm.phone" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="省市区" label-width="140px">
                    <region-select :province.sync="entityForm.province" :city.sync="entityForm.city" :area.sync="entityForm.area"
                                   :value="addressValue" @input="(_,location)=>{entityForm.location=location}" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="详细地址" prop="address" label-width="140px">
                    <el-input v-model="entityForm.address" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
            <!-- <div class="form-title">银行信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="账号或卡号" prop="cardNo">
                    <el-input v-model="entityForm.cardNo" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="开户行" prop="bank" label-width="140px">
                    <el-input v-model="entityForm.bank" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="行号" prop="bankNo" label-width="140px">
                    <el-input v-model="entityForm.bankNo" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div> -->
          </el-col>

        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/user/customer'
import { validatePhone } from '@/utils/validate'
export default {
  name: 'customer',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'customer',
      model: Model,
      addressValue: [],
      entityForm: { ...Model.model },
      filterOption: {
        showMore: true,
        columns: [
          { prop: 'code', filter: 'filter_EQS_code', props: { placeholder: '请输入客户编码', clearable: true } },
          { prop: 'name', filter: 'filter_LIKES_name', props: { placeholder: '请输入客户名称', clearable: true } }
        ]
      },
      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        name: [{ required: true, message: '不能为空', trigger: 'blur' }],
        phone: [{ validator: validateIsPhone, trigger: 'blur' }]
      }
    }
  },
  watch: {
    'entityForm.location'() {
      const { province, city, area } = this.entityForm
      this.addressValue = [province, city, area]
    }
  },
  methods: {
    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
::v-deep .el-dialog__body {
  height: 30vh;
}
</style>
<style scoped>
/* .opt-btn {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
}
.opt-btnV {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
} */
</style>
