<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                  :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" />
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">
      <el-table-column label="品名" slot="name" prop="name" width="150" align="center">
        <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{ scope.row.name }}</span></template>
      </el-table-column>
      <el-table-column label="挥发分Vdaf" slot="cleanVdaf" min-width="100" align="center">
        <template slot-scope="scope">
          <div class="tags">
            <el-tag class="tag" effect="plain" type="info">
              {{ scope.row.cleanVdaf }}
            </el-tag>
            <span>-</span>
            <!-- type="warning" -->
            <el-tag class="tag" effect="plain" type="info">
              {{ scope.row.cleanVdaf2 }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Y" slot="procY" min-width="100" align="center">
        <template slot-scope="scope">
          <div class="tags">
            <el-tag class="tag" effect="plain" type="info">
              {{ scope.row.procY }}
            </el-tag>
            <span>-</span>
            <el-tag class="tag" effect="plain" type="info">
              {{ scope.row.procY2 }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否开放接口" slot="isPublic" prop="isPublic" width="100" v-if="perms[`${curd}:interface`] || false">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isPublicBoolean === true ? 'success' : 'warning'">
            {{ scope.row.isPublicBoolean === true ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" slot="opt" max-width="100" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)"
                  v-if="perms[`${curd}:update`]||false">编辑</el-tag>
          <el-tag class="opt-btn" color="#FF726B" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`] || false">删除
          </el-tag>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               :disabled="dialogStatus === 'watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本类型</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="name">
                    <el-input v-model="entityForm.name" clearable :disabled="dialogStatus==='update'" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤种" prop="coalCategory">
                    <category-select :value.sync="entityForm.coalCategory" type="teshu" />
                  </el-form-item>
                  <!-- <el-form-item label="煤种" prop="type" :rules="[{ required: true, message: '请选择一项' }]">
                    <dict-select style="width:100%" v-model="entityForm.coalCategory" type="coal_type"></dict-select>
                  </el-form-item> -->
                </el-col>
                <el-col>
                  <el-form-item label="类型" prop="type">
                    <dict-select v-model="entityForm.type" type="stock_type" />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
              <!-- <el-row type="flex" :gutter="50">
                    <el-col>
                  <el-form-item label="编码" prop="code">
                    <el-input v-model="entityForm.code" clearable placeholder="编码" />
                  </el-form-item>
                </el-col>
                      <el-col>
                  <el-form-item label="矿井名称" prop="mineName">
                    <el-input v-model="entityForm.mineName" clearable placeholder="矿井名称" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="来源" prop="source">
                    <el-select v-model="entityForm.source" clearable placeholder="请选择来源">
                      <el-option v-for="item in sourceOptions.list" :key="item.label" :label="item.label" :value="item.label" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤种" prop="coalCategory">
                    <category-select :value.sync="entityForm.coalCategory" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="单铲重量" prop="shovelWeight">
                    <el-input v-model="entityForm.shovelWeight" clearable placeholder="单铲重量" autocomplete="off"
                              oninput="value=value.replace(/[^0-9.]/g,'')">
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col />
              </el-row> -->
            </div>
          </el-col>
          <!-- <el-col v-show="sourceOptions.show || ''">
            <div class="form-title">质量标准</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="水分" prop="cleanMt" label-width="140px">
                    <el-input v-model="entityForm.cleanMt" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="灰分Ad" prop="cleanAd">
                    <el-input v-model="entityForm.cleanAd" autocomplete="off" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')">
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="硫St,d" prop="cleanStd">
                    <el-input v-model="entityForm.cleanStd" clearable oninput="value=value.replace(/[^0-9.]/g,'')">
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="挥发分Vdaf">
                    <el-row :gutter="10">
                      <el-col :span="11">
                        <el-form-item prop="cleanVdaf">
                          <el-input v-model="entityForm.cleanVdaf" clearable />
                        </el-form-item>
                      </el-col>
                      <el-col :span="2">
                        <el-form-item>
                          -
                        </el-form-item>
                      </el-col>
                      <el-col :span="11">
                        <el-form-item prop="cleanVdaf2">
                          <el-input v-model="entityForm.cleanVdaf2" clearable />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="特征" prop="crc">
                    <el-input v-model="entityForm.crc" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="粘结" prop="procG">
                    <el-input v-model="entityForm.procG" clearable> </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="X值" prop="procX">
                    <el-input v-model="entityForm.procX" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                      <template slot="append">mm</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Y值">
                    <el-row :gutter="10">
                      <el-col :span="11">
                        <el-form-item prop="procY">
                          <el-input v-model="entityForm.procY" clearable />
                        </el-form-item>
                      </el-col>
                      <el-col :span="2">
                        <el-form-item>
                          -
                        </el-form-item>
                      </el-col>
                      <el-col :span="11">
                        <el-form-item prop="procY2">
                          <el-input v-model="entityForm.procY2" clearable />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col />
              </el-row>
            </div>
          </el-col> -->
          <el-col>
            <div class="form-title">其他信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50" v-if="perms[`${curd}:interface`] || false">
                <el-col>
                  <el-form-item label="是否开放接口" prop="isPublicBoolean">
                    <el-switch v-model="entityForm.isPublicBoolean"></el-switch>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus !== 'watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`] || false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/product/productList'
import { createMemoryField } from '@/utils/index'
export default {
  name: 'product',
  mixins: [Mixins],
  data() {
    return {
      curd: 'product',
      model: Model,
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'name',
            filter: 'filter_LIKES_name',
            component: 'select-name',
            props: { placeholder: '请选择品名', clearable: true }
          },
          {
            prop: 'type',
            component: 'dict-select',
            filter: 'filter_EQS_type',
            props: {
              type: 'stock_type',
              placeholder: '请选择类型',
              clearable: false
            }
          }
        ]
      },
      entityForm: { ...Model.model, filter_EQS_type: '', code: '', type: '' },
      rules: {
        name: { required: true, message: '请输入品名', trigger: 'blur' },
        // code: { required: true, message: '请输入编码', trigger: 'blur' },
        type: { required: true, message: '请选择类型', trigger: 'blur' }
        // coalCategory: { required: true, message: '请选择煤种类型', trigger: 'change' },
      },
      actions: [],
      sourceOptions: {
        show: true,
        list: [{ label: '自产' }, { label: '采购煤' }]
      },
      memoryEntity: { fields: {}, triggered: false }
    }
  },
  created() {
    this.memoryEntity.fields = createMemoryField({ fields: ['name', 'coalCategory', 'source'], target: this.entityForm })
  },
  watch: {
    'entityForm.isPublicBoolean': {
      handler(v) {
        this.entityForm.isPublic = v ? 'Y' : 'N'
      }
    },
    'entityForm.source'(v) {
      if (v === '自产') {
        this.sourceOptions.show = true
      } else if (v === '采购煤') {
        this.sourceOptions.show = false
        this.hiddenQuality()
      } else if (v === '') {
        // console.log('00000')
        this.sourceOptions.show = true
      }
    }
  },
  methods: {
    baseVariant() {
      if (!this.memoryEntity.triggered) {
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    hiddenQuality() {
      const { id, name, coalCategory, source, code, type, isPublicBoolean } = this.entityForm
      this.entityForm = { ...Model.model, id, name, coalCategory, source, code, type, isPublicBoolean }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// ::v-deep .el-table .cell {
//   display: flex;
//   justify-content: center;
// }
::v-deep .el-dialog__body {
  height: 65vh;
}
.tags {
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    margin: 0 3px;
  }
  .tag {
    width: 30px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    // margin: 0 auto;
    // padding: 10px;
    // line-height: 0px;
  }
}
</style>
<style scoped>
/* .opt-btn {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
}
.opt-btnV {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
} */
</style>