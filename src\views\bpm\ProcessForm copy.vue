
<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :statetype="activetype" :buttomlist="buttomlist"
            :showSummary='true' :countList="countList" :actionList="actionList" title="供应商" otherHeight="200">
      <el-table-column label="操作" slot="opt" max-width="150" width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <div v-if="scope.row.state =='NEW' || scope.row.state =='PASS'|| scope.row.state =='PAYED'">
              <el-tag class="opt-btn" color="#FF726B" @click="handleUpdate(scope.row,'watch')">
                查看
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag class="opt-btn" style="cursor: pointer;" color="#2f79e8"
                      v-if="scope.row.state == 'PAYED' || scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'"
                      @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="reviewLogList">
              <el-steps :active="reviewLogList.length">
                <el-step v-for="(item,index) in reviewLogList" :key="index"
                         :title="item.reviewResult + ' ( 审批意见：'+item.reviewMessage+' )'"
                         :description="item.createDate+' '+item.reviewUserName">
                </el-step>
              </el-steps>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col style="margin-top:30px;padding:0 15px">
            <el-tabs v-model="entityForm.applyType" @tab-click="changetab">
              <el-tab-pane label="贸易往来付款" name="TRADE"></el-tab-pane>
              <el-tab-pane label="其他付款" name="OTHER"></el-tab-pane>
              <el-tab-pane label="客户退款" name="REFUND"></el-tab-pane>
            </el-tabs>
          </el-col>
          <template v-if="entityForm.applyType=='TRADE'">
            <el-col>
              <div class="form-title">基础信息</div>
              <div class="form-layout">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="日期" prop="applyDate">
                      <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="合同" prop="contractId">
                      <!-- :disabled="dialogStatus==='update'" -->
                      <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                   filterable clearable placeholder="请选择合同" style="width:100%" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="供应商(收款单位)" prop="supplierId">
                      <el-select v-model="supplierEntity.value" placeholder="请选择" clearable filterable style="width:100%"
                                 @change="handleSupplier" disabled>
                        <el-option v-for="item in supplierEntity.options" :key="item.id" :label="item.name" :value="item.id" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="品名" prop="productName">
                      <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                   disabled />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <div class="form-title">付款信息</div>
              <div class="form-layout">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="支付方式" prop="payWay" :rules="[{ required: true, message: '请选择一项' }]">
                      <dict-select v-model="entityForm.payWay" type="payWay_type" @change="setpayway" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="收款人" prop="payee">
                      <el-input v-model="entityForm.payee" @input="changepayee" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50" v-if="entityForm.payWay!=='1'">
                  <el-col v-if="entityForm.payWay!='1'">
                    <el-form-item label="开户银行" prop="bank" label-width="140px">
                      <el-input v-model="entityForm.bank" @input="changebank" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="entityForm.payWay!='1'">
                    <el-form-item label="账号或卡号" prop="cardNo">
                      <el-input v-model="entityForm.cardNo" @input="changecardNo" clearable
                                oninput="value=value.replace(/[^0-9.]/g,'')" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="单价" prop="price">
                      <el-input v-model="entityForm.price" @input="handleBlur(entityForm)" clearable
                                oninput="value=value.replace(/[^0-9.]/g,'')" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="行号" prop="bankNo" v-if="entityForm.payWay!=='1'">
                      <el-input v-model="entityForm.bankNo" @input="changebankNo" clearable
                                oninput="value=value.replace(/[^0-9.]/g,'')" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="数量" prop="amount">
                      <el-input v-model="entityForm.amount" @input="handleBlur(entityForm)" :oninput="field.decimal" clearable>
                        <template slot="append">吨</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="付款金额" prop="money">
                      <el-input v-model="entityForm.money" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="备注" prop="remarks">
                      <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <!-- <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
                  <el-col>
                    <el-form-item label="步骤" prop="reviewLogList">
                      <el-steps :active="reviewLogList.length">
                        <el-step v-for="(item,index) in reviewLogList" :key="index"
                                 :title="item.reviewResult + ' ( 审批意见：'+item.reviewMessage+' )'"
                                 :description="item.createDate+' '+item.reviewUserName">
                        </el-step>
                      </el-steps>
                    </el-form-item>
                  </el-col>
                </el-row> -->
              </div>
            </el-col>
            <el-col>
              <div class="form-layout">
                <el-row>
                  <el-col>
                    <el-form-item label="附件上传">
                      <div class="upload">
                        <div class="upload-list">
                          <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                            <el-image class="img" :src="item.uri" fit="fill" :preview-src-list="srcList" />
                            <img src="@/assets/close.png" class="icon" @click="handleRemoveUpload(index)">
                          </div>
                          <!-- /cwe/a/applyPay/upload -->
                          <upload-attachment ref="upload" class="upload-attachment" style="text-align: left;"
                                             url="/cwe/a/attachment/upload" :uploadData="uploadData" source="ContractBuy"
                                             :size="size" :limitNum="limitNum" accept=".jpg,.jpeg,.png" :isShowFile="false"
                                             @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                             listType="picture" />
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </template>

          <!-- 其他支付 -->
          <template v-if="entityForm.applyType=='OTHER'">
            <el-col>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="applyDate">
                    <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="付款金额" prop="money">
                    <el-input v-model="entityForm.money" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="支付方式" prop="payWay" :rules="[{ required: true, message: '请选择一项' }]">
                    <dict-select v-model="entityForm.payWay" type="payWay_type" @change="setpayway" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="收款人" prop="payee">
                    <el-input v-model="entityForm.payee" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50" v-if="entityForm.payWay!=='1'">
                <el-col v-if="entityForm.payWay!='1'">
                  <el-form-item label="开户银行" prop="bank" label-width="140px">
                    <el-input v-model="entityForm.bank" @input="changebank" clearable />
                  </el-form-item>
                </el-col>
                <el-col v-if="entityForm.payWay!='1'">
                  <el-form-item label="账号或卡号" prop="cardNo">
                    <el-input v-model="entityForm.cardNo" @input="changecardNo" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50" v-if="entityForm.payWay!='1'">
                <el-col>
                  <el-form-item label="行号" prop="bankNo">
                    <el-input v-model="entityForm.bankNo" @input="changebankNo" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')" />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="purpose" label-width="140px">
                    <el-input v-model="entityForm.purpose" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </template>

          <!-- 客户退款 -->
          <template v-if="entityForm.applyType=='REFUND'">
            <el-col>
              <div class="form-title">基础信息</div>
              <div class="form-layout">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="日期" prop="applyDate">
                      <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="销售合同" prop="contractId">
                      <!-- :disabled="dialogStatus==='update'" -->
                      <el-cascader v-model="contractEntityV.active" :options="contractEntityV.options"
                                   :props="contractEntityV.props" filterable clearable placeholder="请选择合同" style="width:100%" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <!-- <el-col>
                    <el-form-item label="客户" prop="supplierId">
                      <el-select v-model="supplierEntity.value" placeholder="请选择客户" clearable filterable style="width:100%"
                                 @change="handleSupplier">
                        <el-option v-for="item in supplierEntity.options" :key="item.id" :label="item.name" :value="item.id" />
                      </el-select>
                    </el-form-item>
                  </el-col> -->

                  <el-col>
                    <el-form-item label="客户" prop="customerName">
                      <el-select v-model="customerEntity.active" placeholder="请选择" clearable @change="handleCustomer"
                                 style="width:100%" disabled>
                        <el-option v-for="item in customerEntity.options" :key="item.value" :label="item.label" :value="item">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="品名" prop="productName">
                      <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                   disabled />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <div class="form-title">付款信息</div>
              <div class="form-layout">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="支付方式" prop="payWay" :rules="[{ required: true, message: '请选择一项' }]">
                      <dict-select v-model="entityForm.payWay" type="payWay_type" @change="setpayway" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="收款人" prop="payee">
                      <el-input v-model="entityForm.payee" @input="changepayee" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <!-- ispayWay!=='1' -->
                <el-row type="flex" :gutter="50" v-if="entityForm.payWay!=='1'">
                  <el-col v-if="entityForm.payWay!='1'">
                    <el-form-item label="开户银行" prop="bank" label-width="140px">
                      <el-input v-model="entityForm.bank" @input="changebank" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="entityForm.payWay!='1'">
                    <el-form-item label="账号或卡号" prop="cardNo">
                      <el-input v-model="entityForm.cardNo" @input="changecardNo" clearable
                                oninput="value=value.replace(/[^0-9.]/g,'')" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="单价" prop="price">
                      <el-input v-model="entityForm.price" @input="handleBlur(entityForm)" clearable
                                oninput="value=value.replace(/[^0-9.]/g,'')" />
                    </el-form-item>
                  </el-col>
                  <el-col>

                    <!-- || ispayWay!=='1' -->
                    <el-form-item label="行号" prop="bankNo" v-if="entityForm.payWay!=='1' ">
                      <el-input v-model="entityForm.bankNo" @input="changebankNo" clearable
                                oninput="value=value.replace(/[^0-9.]/g,'')" />
                    </el-form-item>
                  </el-col>

                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="数量" prop="amount">
                      <el-input v-model="entityForm.amount" @input="handleBlur(entityForm)" :oninput="field.decimal" clearable>
                        <template slot="append">吨</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="付款金额" prop="money">
                      <el-input v-model="entityForm.money" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="备注" prop="remarks">
                      <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-col>
            <el-col>
              <div class="form-layout">
                <el-row>
                  <el-col>
                    <el-form-item label="附件上传">
                      <div class="upload">
                        <div class="upload-list">
                          <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                            <el-image class="img" :src="item.uri" fit="fill" :preview-src-list="srcList" />
                            <img src="@/assets/close.png" class="icon" @click="handleRemoveUpload(index)">
                          </div>
                          <upload-attachment ref="upload" class="upload-attachment" style="text-align: left;"
                                             url="/cwe/a/attachment/upload" :uploadData="uploadData" source="ContractBuy"
                                             :size="size" :limitNum="limitNum" accept=".jpg,.jpeg,.png" :isShowFile="false"
                                             @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                             listType="picture" />
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </template>
        </el-row>
      </el-form>

      <!-- <div slot="footer" class="dialog-footer" v-if="isshow==false">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="SubmitReview('entityForm')">
          提交审核
        </el-button>
        <el-button @click="SaveDraft('entityForm')" class="dialog-footer-btns" style="width:100px">保存草稿
        </el-button>
      </div>

      <div slot="footer" class="dialog-footer" v-else>
        <el-button v-if="perms[`${curd}:review`]||false" @click="rejectfn(entityForm.id,entityForm.reviewMessage)"
                   class="dialog-footer-btns" style="width:100px">拒绝
        </el-button>
        <el-button v-if="perms[`${curd}:review`]||false" type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                   @click="passfn(entityForm.id,entityForm.reviewMessage)">
          审核通过
        </el-button>
      </div> -->

      <div slot="footer" class="dialog-footer" v-if="dialogStatus != 'watch'">
        <!-- v-if="perms[`${curd}:review`]||false"-->
        <div v-if="dialogStatus != 'review'">
          <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="SubmitReview('entityForm')">
            提交审核
          </el-button>

          <el-button @click="SaveDraft('entityForm')" class="dialog-footer-btns">
            保存草稿
          </el-button>
        </div>

        <div v-else style="width:100%">
          <div v-if="perms[`${curd}:ismanagerreview`] || perms[`${curd}:isFinancereview`]"
               style="display: flex;flex-direction: row;">
            <div style="width:calc(100% - 250px)" class="tsinput">
              <el-input style="width:100%; height: 29.5px;" type="text" v-model="entityForm.reviewMessage"
                        placeholder="请输入审批意见" />
            </div>
            <div style="width:250px">
              <el-button v-if="perms[`${curd}:update`]||false" type="primary" class="dialog-footer-btns"
                         :loading="entityFormLoading" @click="passfn(entityForm.id,entityForm.reviewMessage,entityForm.state)">
                审核通过
              </el-button>
              <el-button v-if="perms[`${curd}:update`]||false" @click="rejectfn(entityForm.id,entityForm.reviewMessage)"
                         class="dialog-footer-btns" style="width:100px">拒绝
              </el-button>
            </div>

          </div>
          <!-- applyPay:waitforreviewed -->
        </div>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/bpm/ProcessForm'
import { validatePhone } from '@/utils/validate'
import { detailsdailyreportOption } from '@/const'
import { contractList } from '@/api/quality'
import productModel from '@/model/product/productList'
import { chooseContract, getCustomerContract } from '@/api/quality'
import { createMemoryField } from '@/utils/index'
import CustomerModel from '@/model/user/customer'
export default {
  name: 'ProcessForm',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'ProcessForm',
      model: Model,
      addressValue: '',
      entityFormV: {},
      dialogFormVisibleV: false,
      entityForm: { ...Model.model },
      noticeForm: {},
      nameEntity: { options: [], active: '' },
      supplierEntity: { value: '', options: [] },
      uploadList: [], // 用于展示
      uploadData: { refId: '', refType: 'ContractSell' },
      limitNum: 50,
      username: 'ss',
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      filterOption: { ...detailsdailyreportOption },
      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        phone: [{ validator: validateIsPhone, trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      rules: {
        applyDate: { required: true, message: '请输入日期', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' },
        supplierId: { required: true, message: '请输入收款单位', trigger: 'blur' },
        productName: { required: true, message: '请选择输入品名', trigger: 'blur' },
        payWay: { required: true, message: '请选择支付方式', trigger: 'blur' },
        bank: { required: true, message: '请输入开户银行', trigger: 'blur' },
        cardNo: { required: true, message: '请输入账号或卡号', trigger: 'blur' },
        payee: { required: true, message: '请输入收款人', trigger: 'blur' },
        money: { required: true, message: '请输入付款金额', trigger: 'blur' }
      },
      // 采购合同
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      // 销售合同
      contractEntityV: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      customerEntity: { options: [], active: [] },
      isshow: false,
      drawer: {
        visible: false,
        list: [],
        preview: [],
        dateList: []
      },
      emptyImgPath: require(`@/assets/empty.jpg`),
      reviewLogList: [],
      countList: [],

      uploadListV: [],
      uploadDataV: { refId: '', refType: 'ContractSell' },
      ispayWay: '',
      isBilllist: [
        { label: '是', type: 'Y' },
        { label: '否', type: 'N' }
      ]
    }
  },
  watch: {
    'entityForm.isPublicBoolean': {
      handler(v) {
        this.entityForm.isPublic = v ? 'Y' : 'N'
      }
    },
    'entityForm.productName'(name) {
      if (name) {
        const item = this.nameEntity.options.find((item) => item.name === name)
        if (item) {
          this.nameEntity.active = item.name
          this.entityForm.productCode = item.code
          this.entityForm.productId = item.id
          this.entityForm.coalType = item.coalCategory || ''
          this.entityForm.coalCategory = item.type
        }
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
        this.entityForm.coalCategory = ''
      }
    },

    'supplierEntity.value'(id) {
      console.log(id)
      if (!id) return
      const item = this.supplierEntity.options.find((item) => item.id === id)
      console.log(item)
      if (item) {
        this.supplierEntity.active = item
        this.entityForm.payee = item.name
      } else {
        this.supplierEntity.active = []
      }
    },

    'entityForm.contractId'(id) {
      console.log(id)
      console.log(this.entityForm.applyType)
      if (!id) return
      if (this.entityForm.applyType == 'TRADE') {
        if (this.dialogStatus === 'update' || this.dialogStatus === 'review' || this.dialogStatus === 'watch') {
          const value = this.filterContractItem(id, 'contractEntity')
          this.contractEntity.active = value
        }
      }
      if (this.entityForm.applyType == 'REFUND') {
        if (this.dialogStatus === 'update' || this.dialogStatus === 'review' || this.dialogStatus === 'watch') {
          const value = this.filterContractItemV(id, 'contractEntityV')
          this.contractEntityV.active = value
        }
      }
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.supplierId
      this.entityForm.contractName = form.secondParty
      this.supplierEntity.value = value[0]
      const item = this.contractEntity.options.find((v) => v.supplierId === value[0])
      if (!item) return
      const itemvv = item.contractBuyList.find((v) => v.id === value[1])
      console.log(itemvv)
      if (!itemvv) return
      if (itemvv.bank) {
        this.entityForm.bank = itemvv.bank
      }
      if (itemvv.bankNo) {
        this.entityForm.bankNo = itemvv.bankNo
      }
      if (itemvv.cardNo) {
        this.entityForm.cardNo = itemvv.cardNo
      }
      this.entityForm.productName = itemvv.productName
      this.entityForm.supplierId = itemvv.supplierId
      this.entityForm.supplierName = itemvv.secondParty
    },
    'contractEntityV.active'(value) {
      console.log(value)
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItemV(value, 'contractEntityV')
      console.log(form)
      this.entityForm.contractCode = form.customerName
      this.entityForm.contractId = form.customerId
      this.entityForm.customerName = form.firstParty
      this.entityForm.supplierId = form.customerId
      this.entityForm.customerId = form.customerId
      this.entityForm.contractName = form.firstParty
      this.entityForm.supplierName = form.firstParty
      this.entityForm.productName = form.productName
      this.nameEntity.active = form.productName
      this.customerEntity.active = form.firstParty
      this.supplierEntity.value = form.firstParty
      this.entityForm.payee = form.firstParty
      this.entityForm = {
        ...this.entityForm,
        productName: form.productName,
        supplierName: form.firstParty,
        customerName: form.firstParty
      }
    },

    activetype: function (newV, oldV) {
      this.getlist(newV)
      this.getnumber()
    },
    'entityForm.applyType'(type) {
      this.entityForm.applyType = type
    }
  },
  created() {
    // this.permsActionLsit(this.curd)
    this.permsActionbtn(this.curd)
    this.setbuttomlistV(this.curd)
    this.getName()
    //获取供应商列表
    this.getContractList()
    //获取采购合同列表
    this.getContract()
    //获取销售合同列表
    this.getContractV()
    //获取客户列表
    this.getCustomer()
    this.memoryEntity.fields = createMemoryField({
      fields: ['contractId', 'contractCode', 'supplierId', 'supplierName'],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    if (this.$route.params.id) {
      this.handleUpdate(this.$route.params.id, 'review')
    }
    this.getnumber()
    if (this.$route.params.type) {
      this.activetype = this.$route.params.type
      if (this.$route.params.type == 'PASS_FINANCE') {
        this.dialogFormVisible = false
        let row = {
          id: this.$route.params.id
        }
      }
    } else {
      this.activetype = ''
    }
  },
  computed: {
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    }
  },

  methods: {
    async selectChangeisSettle(row) {
      try {
        await this.model.getupdateIsSettle({ id: row.id, isSettle: row.isSettle })
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
        this.getList()
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },
    setpayway(val) {
      this.ispayWay = val
      if (val === '1') {
        this.$forceUpdate()
      }
    },
    handleCloseV() {
      this.dialogFormVisibleV = false
    },

    async confirmationSave() {
      const res = await this.model.Collected({
        id: this.entityFormV.id,
        reviewMessage: this.entityFormV.reviewMessage,
        attachmentList: this.entityFormV.attachmentList
      })
      if (res) {
        this.handleCloseV()
        this.$message({ type: 'success', message: '支付成功!' })
        this.getList()
      }
    },
    handleBlur(entityForm) {
      if (entityForm.amount != '' && entityForm.price != '') {
        this.money = parseInt(entityForm.amount * entityForm.price)
        let moneyv = parseInt(entityForm.amount * entityForm.price)
        if (isNaN(moneyv) == false) {
          this.entityForm.money = moneyv
        }
      }
    },
    changetab(tab, event) {
      this.ispayWay = ''
      this.$nextTick(() => {
        // 请求成功之后，在这里执行
        this.entityForm = { ...Model.model }
        this.entityForm.applyType = tab.name
        this.supplierEntity.value = ''
        this.nameEntity.active = ''
        this.customerEntity.active = ''
        this.contractEntity.active = ''
        this.contractEntityV.active = ''
        this.$forceUpdate()
      })
    },

    async getnumber() {
      const res = await this.model.getcountByState()

      this.countList = res.data
    },
    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      this.uploadList = [] // 清空下载列表
      this.reviewLogList = []
      this.contractEntity.active = []
      this.supplierEntity.value = ''
      this.isshow = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    changebankNo(e) {
      this.$set(this.entityForm, 'bankNo', e)
      this.$forceUpdate()
    },
    changepayee(e) {
      this.$set(this.entityForm, 'payee', e)
      this.$forceUpdate()
    },
    changecardNo(e) {
      // this.entityForm.cardNo = e
      this.$set(this.entityForm, 'cardNo', e)
      this.$forceUpdate()
    },
    changebank(e) {
      this.$set(this.entityForm, 'bank', e)
      this.$forceUpdate()
    },
    //供应商列表
    async getContractList() {
      const { data } = await contractList()
      if (data.length) this.supplierEntity.options = data
    },
    //获取客户列表
    async getCustomer() {
      try {
        let options = []
        const { data } = await CustomerModel.page({ size: 1000 })
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        this.customerEntity.options = options
      } catch (e) {}
    },

    handleCustomer({ value, label }) {
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label, customerName: label, customerId: value }
    },
    // 合同改变同步
    handleSupplier({ value, label }) {
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label }
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) {}
    },
    // 获取采购合同选择接口
    async getContract() {
      try {
        const { data } = await chooseContract()
        this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'displayName', supplierId: 'id' } })
      } catch (error) {}
    },
    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    // 获取销售合同选择接口
    async getContractV() {
      const { data } = await getCustomerContract()
      console.log(data)
      // 供应商名称返回id用来查询
      this.contractEntityV.options = this.formatContractV({ data, key: { customerName: 'code', customerId: 'id' } })
      console.log(this.contractEntityV.options)
    },

    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContractV({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },

    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        console.log('走string')
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },

    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     * @target {contract|contractEntityForm}<String>
     */
    filterContractItemV(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },

    async handleRemoveUpload(index) {
      const { id } = this.uploadList[index]
      this.$refs.upload.applyPayDelete({ id, index })
    },

    async SubmitReview(entityForm) {
      this.entityForm = { ...this.entityForm, payWay: this.ispayWay }
      console.log(this.ispayWay)
      console.log(this.entityForm.payWay)
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.entityForm.payWay === '1') {
          //支付方式是微信
          this.entityForm.bank = ' '
          this.entityForm.cardNo = ' '
          this.entityForm.bankNo = ' '
          this.$forceUpdate()
        }
        const res = await this.model.getApproved(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '提交成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.resetVariant()
          this.getnumber()
          this.dialogFormVisible = false
        }
      }
    },

    //保存草稿
    async SaveDraft(entityForm) {
      console.log(this.entityForm)
      this.entityForm = { ...this.entityForm, payWay: this.ispayWay }
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        // this.ispayWay
        if (this.entityForm.payWay === '1') {
          //支付方式是微信
          this.entityForm.bank = ' '
          this.entityForm.cardNo = ' '
          this.entityForm.bankNo = ' '
          this.$forceUpdate()
        }
        const res = await this.model.SaveDraftfn(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '保存草稿成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.getnumber()
          this.dialogFormVisible = false
        }
      }
    },

    getList() {
      this.$refs[this.curd].$emit('refresh')
    },
    handleUploadSuccess(res) {
      this.uploadList = [...this.uploadList, res]
      this.entityForm.attachmentList = this.uploadList
    },
    handleUploadSuccessV(res) {
      this.uploadListV = [...this.uploadListV, res]
      this.entityFormV.attachmentList = this.uploadListV
    },
    handleUploadDeleteV({ status, index }) {
      this.uploadListV.splice(index, 1)
    },
    async handleRemoveUploadV(index) {
      const { id } = this.uploadListV[index]
      this.$refs.upload.applyPayDelete({ id, index })
    },

    handleUploadDelete({ status, index }) {
      this.uploadList.splice(index, 1)
    },

    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    async handleUpdate(item, type) {
      if (typeof item == 'object') {
        console.log('7777')
        this.entityForm = { ...item }
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        } else if (type == 'update') {
          this.isshow = true
          this.dialogStatus = 'update'
        } else if (type == 'watch') {
          this.isshow = false
          this.dialogStatus = 'watch'
        }
        this.dialogFormVisible = true
        // 上传所需要的配置
        this.uploadData.refId = item.id
        const { data } = await this.model.getUploadList(item.id)
        this.entityForm = { ...data }

        console.log(this.entityForm.payWay)
        this.entityForm.payWay == this.ispayWay
        this.reviewLogList = data.reviewLogList
        this.uploadList = data.attachmentList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((item, index) => {
            newarry.push(item.reviewMessage)
          })
          this.entityForm.reviewMessage = ''
        }
      } else if (typeof item == 'string') {
        console.log('888')
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        }
        this.dialogFormVisible = true
        // 上传所需要的配置
        this.uploadData.refId = item
        const { data } = await this.model.getUploadList(item)
        this.entityForm = { ...data }
        this.supplierEntity.value = this.entityForm.supplierId
        this.reviewLogList = data.reviewLogList
        this.uploadList = data.attachmentList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((itemv, index) => {
            newarry.push(itemv.reviewMessage)
          })
          this.entityForm.reviewMessage = newarry.toString()
        }
      }
    },
    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      if (res.data.attachmentList) {
        this.drawer.list = res.data.attachmentList
        this.drawer.preview = res.data.attachmentList.map((item) => item.uri)
        this.drawer.visible = true
      }
    },
    //点击状态获取数据
    async getlist(newV) {
      this.activetype = newV
      this.actionList.forEach((val, index) => {
        if (val.type == newV) {
          val.active = true
        } else {
          val.active = false
        }
      })
      this.filtersSeach.filter_INS_state = newV
      this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    },

    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },
    async passfn(id, reviewMessage, state) {
      if (state == 'NEW') {
        const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage })
      } else if (state == 'PASS') {
        const res = await this.model.getfinancePass({ id: id, reviewMessage: reviewMessage })
      }
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
      this.resetVariant()
      this.getlist(this.activetype)
      this.getnumber()
    },

    async rejectfn(id, reviewMessage) {
      const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage })
      this.getnumber()
      this.$message({ type: 'success', message: '拒绝成功!' })
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.contentbox {
  width: 500px;
  margin: 100px auto 0 auto;
  font-size: 12px;
  .line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tsline {
    float: right;
    margin-top: 40px;
  }
  .inputbox {
    width: 100px;
    margin-bottom: 10px;
    border-bottom: solid 1px #ccc;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
<style scoped>
.tsinput >>> input {
  height: 37px !important;
}
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
</style>
