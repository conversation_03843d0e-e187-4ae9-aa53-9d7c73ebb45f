<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="表单名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入表单名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
          @import="handleImpiort"    :showImport="perms[`${curd}:addimport`]||false"    :showAdd="perms[`${curd}:save`]||false" :showRefresh="perms[`${curd}:Refresh`]||false" />

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :statetype="activetype" :buttomlist="buttomlist"
            :showSummary='true' :countList="countList" :actionList="actionList" title="供应商" otherHeight="200">
      <el-table-column label="备注" slot="remarks" prop="remarks" width="100">
        <template slot-scope="scope">
          <div v-if="scope.row.remarks">
            {{scope.row.remarks}}
          </div>
          <div v-if="scope.row.purpose">
            {{scope.row.purpose}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="附件" slot="attachmentList" prop="attachmentList" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handlePreviewAttachment(scope.row)">查看附件</el-button>
        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" max-width="150" width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <div v-if="perms[`${curd}:ismanagerreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state =='NEW'" color="#FF726B" @click="handleUpdate(scope.row,'review')">
                去审核(总经理)
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:isFinancereview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state=='PASS'" color="#33CAD9" @click="handleUpdate(scope.row,'review')">
                去审核(财务)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:pay`]||false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'PASS_FINANCE' " color="#FF9639" @click="tobeCollected(scope.row)">
                付款</el-tag>
            </div>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT' " color="#FF726B"
                      @click="handleUpdate(scope.row,'update')">
                编辑
              </el-tag>
            </div>
            <div v-if="scope.row.state =='NEW' || scope.row.state =='PASS'|| scope.row.state =='PAYED'">
              <el-tag class="opt-btn" color="#FF726B" @click="handleUpdate(scope.row,'watch')">
                查看
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag class="opt-btn" style="cursor: pointer;" color="#2f79e8"
                      v-if="scope.row.state == 'PAYED' || scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'"
                      @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
    </s-curd> -->

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['bpm:form:create']">新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="表单名" align="center" prop="name" />
      <el-table-column label="开启状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleDetail(scope.row)"
                     v-hasPermi="['bpm:form:query']">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['bpm:form:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['bpm:form:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList" />

    <!--表单配置详情-->
    <el-dialog title="表单详情1" :visible.sync="detailOpen" width="50%" append-to-body>
      <div class="test-form">
        <parser :key="new Date().getTime()" :form-conf="detailForm" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deleteForm, getForm, getFormPage } from '@/api/bpm/form'
import Parser from '@/components/parser/Parser'
import { decodeFields } from '@/utils/formGenerator'
import { formindexOption } from '@/const'
import Model from '@/model/bpm/formindex'
export default {
  name: 'Form',
  components: {
    Parser,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工作流的列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
      },
      // 表单详情
      detailOpen: false,
      detailForm: {
        fields: [],
      },

      curd: 'applyPay',
      model: Model,
      filterOption: { ...formindexOption },
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getFormPage(this.queryParams).then((response) => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      getForm(row.id).then((response) => {
        // 设置值
        const data = response.data
        this.detailForm = {
          ...JSON.parse(data.conf),
          fields: decodeFields(data.fields),
        }
        // 弹窗打开
        this.detailOpen = true
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.$router.push({
      //   path: '/bpm/form/formEditor',
      // })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$router.push({
        path: '/bpm/manager/form/edit',
        query: {
          formId: row.id,
        },
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$confirm('是否确认删除工作表单的编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteForm(id)
        })
        .then(() => {
          this.getList()
          this.$message({
            type: 'success',
            message: '删除成功',
          })
        })
        .catch(() => {})
    },
  },
}
</script>
