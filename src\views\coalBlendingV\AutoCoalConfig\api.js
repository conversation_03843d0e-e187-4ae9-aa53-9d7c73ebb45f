import request from '@/utils/request'
import {getDate} from "@/utils/dateUtils";

const name = '/cwe/a/coalWashingTargetValueConfig'

export function page(query) {
    return request({
        url: `${name}/page`,
        method: 'get',
        params: query
    })
}

export async function list(query) {
    const resp = await request({
        url: `${name}/pageConfig`,
        method: 'get',
        params: query
    })
    // 日期倒叙
    resp.data.sort((a, b) => new Date(b.date) * 1 - new Date(a.date) * 1)
    resp.data.forEach(v => {
        v.date = getDate(v.date, 'YYYY-MM-DD')
    })
    return resp
}

export function save(data) {
    return request({
        url: `${name}/saveConfig`,
        method: 'post',
        data: {...data,}
    })
}

export function remove(params) {
    return request({
        url: `${name}/delete`,
        method: 'post',
        params
    })
}


export function get(id) {
    return request({
        url: `${name}/get`,
        method: 'get',
        params: {id}
    })
}

