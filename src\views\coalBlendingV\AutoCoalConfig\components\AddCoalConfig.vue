<template>
  <div class="container">
    <SnModal v-model="_value" :cancel="cancel" :fullscreen.sync="fullscreen" :ok="ok" :showFooter="!isViewStatus"
             :title="title"
             class="modal" v-bind="$attrs" width="800px">
      <el-form ref="form" :disabled="isViewStatus" :model="form" :rules="rules" labelPosition="top"
               labelWidth="100px"
               style="height: 100%">


        <SnProFormItem label="日期" prop="date" required span="24">
          <template #default="props">
            <SnDateSelect v-model="form[props.bindField]"
                          :placeholder="`请输入`" valueFormat="yyyy-MM-dd">
            </SnDateSelect>
          </template>
        </SnProFormItem>

        <SnProFormItem label="目标名称" prop="name" required span="24">
          <template #default="props">
            <el-input
              v-model="form[props.bindField]"
              clearable
              style="width: 100%"
              v-bind="{ ...props }"
            ></el-input>
          </template>
        </SnProFormItem>

        <SnFormCard span="24" title="指标信息">
          <div style="display: flex;margin-top: -10px;position: relative;">
            <div class="container">
              <div>
                <p class="single " style="width: 70px"></p>
                <div class="single">
                  <span class="title-text">灰分Ad</span>
                </div>
                <div class="single">
                  <span class="title-text">硫St,d</span>
                </div>
                <div class="single">
                  <span class="title-text">Vdaf</span>
                </div>
                <div class="single">
                  <span class="title-text">粘结G</span>
                </div>
                <div class="single">
                  <span class="title-text">胶质Y</span>
                </div>
                <div class="single">
                  <span class="title-text">CSR</span>
                </div>
              </div>
              <div style="margin:5px 0">
                <p class="single single-wrap" style="width: 70px">上限:</p>
                <div class="single"><input v-model="form.adMax" type="number"/></div>
                <div class="single"><input v-model="form.stdMax" type="number"/></div>
                <div class="single"><input v-model="form.vdafMax" type="number"/></div>
              </div>
              <div style="margin:5px 0">
                <p class="single single-wrap" style="width: 70px">质量目标:</p>
                <div class="single"><input v-model="form.adTarget" type="number"/></div>
                <div class="single"><input v-model="form.stdTarget" type="number"/></div>
                <div class="single"><input v-model="form.vdafTarget" type="number"/></div>
                <div class="single"><input v-model="form.gTarget" type="number"/></div>
                <div class="single"><input v-model="form.yTarget" type="number"/></div>
                <div class="single"><input v-model="form.csrTarget" type="number"/></div>
              </div>
              <div style="height: 28px">
                <p class="single single-wrap" style="width: 70px">下限:</p>
                <div class="single"><input v-model="form.adMin" type="number"/></div>
                <div class="single"><input v-model="form.stdMin" type="number"/></div>
                <div class="single"><input v-model="form.vdafMin" type="number"/></div>
                <div class="single"><input v-model="form.gMin" type="number"/></div>
                <div class="single"><input v-model="form.yMin" type="number"/></div>
                <div class="single"><input v-model="form.csrMin" type="number"/></div>
              </div>
            </div>

          </div>
        </SnFormCard>


      </el-form>
    </SnModal>
  </div>
</template>

<script>
import TipModal from '@/utils/modal'
import {deepClone, FetchData} from '@/utils'
import {getDate} from '@/utils/dateUtils'
import {save} from '../api'
import SnDateSelect from '@/components/Common/SnDateSelect/index.vue'

export default {
  inheritAttrs: false,
  components: {SnDateSelect},
  data() {
    return {
      form: {},
      rules: {},
      fullscreen: false

    }
  },
  computed: {
    _value: {
      set(val) {
        this.$emit('input', val)
      },
      get() {
        return this.value
      }
    },
    title() {
      return '指标保存'
    },
    isViewStatus() {
      return this.optName === 'view'
    },
    isUpdateStatus() {
      return this.optName === 'update'
    },
    isCreateStatus() {
      return this.optName === 'create'
    }
  },
  props: {
    value: {
      type: Boolean,
      require: true
    },
    record: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    record: {
      async handler(value) {
        await this.$nextTick()
        this.form = this.getFormData(value)
      },
      immediate: true
    }
  },
  async created() {
  },
  methods: {
    getFormData(form) {
      const {...rest} = form
      return {
        ...rest,
        date: getDate()
      }
    },

    getSubmitForm() {
      const submitForm = deepClone(this.form)
      return {
        ...submitForm
      }
    },

    async cancel() {
      this.form = {}
      this.$refs['form'].resetFields()
      return false
    },

    async ok() {
      await this.$refs['form'].validate()
      const resp = await save(this.getSubmitForm())
      if (resp) {
        TipModal.msgSuccess(`保存成功`)
        this.$emit('ok')
        return this.cancel()
      }

    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  display: inline-block;

  p {
    font-size: 14px;
    color: #595ec9;
    margin: 0;
    padding-left: 5px;
    font-weight: bold;
    line-height: 10px;
    height: 20px;
  }

  .single {
    display: inline-block;
    height: 28px;
    line-height: 28px;
    width: 100px;
    text-align: center;
    > span {
      display: inline-block;
      margin: 0 5px;
      font-size: 12px;
      font-weight: 600;
      color: #606266;
    }

    > input {
      height: 28px;
      line-height: 28px;
      border: 1px solid rgb(220, 223, 230);
      border-radius: 4px;
      width: 65px;
      box-sizing: border-box;
      padding: 10px 10px;
    }
  }
}


::v-deep {
  .el-dialog__body {
    padding: 25px 25px;
    height: 500px;
    overflow: auto;
  }

}

</style>
