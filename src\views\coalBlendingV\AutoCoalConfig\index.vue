<template>
  <div class="app-container">
    <div class="common--page">
      <div class="common--page-content">
        <SnFormCard titlePrefixIcon="">
          <template #title>
            <i class="el-icon-s-tools"></i>
            <span style="margin:0 20px 0 7px">目标值配置</span>
            <el-button :loading="loading" type="primary" @click.stop="handleUpdate">更新</el-button>
          </template>

          <el-table :data="listBase" :show-header="false" :span-method="spanMethodBaseList" v-bind="getTableConfig">
            <el-table-column v-for="col in tableColsBase" :key="col.prop" :label="col.label" :prop="col.prop" align="center">
              <template #default="{row,...rest}">
                <template v-if="getComponentKey({row, ...rest}) ==='div'">
                  <div>{{ row[col.prop] }}</div>
                </template>
                <template v-if="getComponentKey({row, ...rest}) ==='input'">
                  <div v-if="rest.$index == 2 && ['y','g'].includes(col.prop) "></div>
                  <el-input v-else v-model="row[col.prop]" :placeholder="`请输入`" style="text-align: center">
                  </el-input>
                </template>
                <template v-if="getComponentKey({row, ...rest}) ==='date'">
                  <SnDateSelect v-model="row[col.prop]" :placeholder="`请输入`" style="text-align: center" valueFormat="yyyy-MM-dd">
                  </SnDateSelect>
                </template>
              </template>
            </el-table-column>
          </el-table>

        </SnFormCard>

        <SnFormCard style="margin-top: 3rem" titlePrefixIcon="">
          <template #title>
            <i class="el-icon-s-tools"></i>
            <span style="margin:0 20px 0 7px">配置数据</span>
          </template>

          <el-table :data="list" :show-header="false" :span-method="spanMethod" height="45vh" v-bind="getTableConfig">
            <el-table-column v-for="col in tableColsBase" :key="col.prop" :label="col.label" :prop="col.prop"
                             align="center"></el-table-column>
            <el-table-column label="操作" prop="opt" width="120">
              <template #default="{row}">
                <div style="display: flex;justify-content: center">
                  <span v-if="row.isFirst">操作</span>
                  <el-button v-else type="text" @click="handleDelete(row)">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </SnFormCard>
      </div>
    </div>
  </div>
</template>

<script>
import * as ApiParams from './api'
import { getDate, getDay } from '@/utils/dateUtils'
import SnSimpleSelect from '@/components/Common/SnSimpleSelect/index.vue'
import SnFormCard from '@/components/Common/SnFormCard/index.vue'
import { isDef } from '@/utils/is'
import SnDateSelect from '@/components/Common/SnDateSelect/index.vue'
import { get, set } from 'lodash'
import { getUuid } from '@/utils'
import TipModal from '@/utils/modal'
import Func from '@/utils/func'
import { refreshIndicators } from '@/api/coal'

export default {
  y: 'CPParams',
  components: { SnDateSelect, SnFormCard, SnSimpleSelect },
  data() {
    return {

      coalCategoryId: '',
      coalCategoryList: [],
      coalCategoryProps: {
        label: 'label',
        value: 'id'
      },
      editKey: '_edit',
      tableColsBase: [
        { label: '名称', prop: 'name' },
        { label: '灰分Ad', prop: 'ad' },
        { label: '硫St.d', prop: 'std' },
        { label: '挥发份Vdaf', prop: 'vdaf' },
        { label: '粘结G', prop: 'g' },
        { label: '胶质Y', prop: 'y' },
        { label: 'CSR', prop: 'csr' }
      ],
      listBase: [],
      list: [],
      loading: false,
      keys: ['name', 'date', 'ad', 'std', 'vdaf', 'g', 'y', 'csr']

    }
  },

  mounted() {
    this.listBase = this.getBaseTemp()
    this.getList()
  },
  directives: {
    focus: {
      inserted: (el) => {
        setTimeout(() => {
          el.children[0].focus()
        }, 10)
      }
    }
  },
  watch: {},
  computed: {
    getTableConfig() {
      return {
        border: true,
        headerCellStyle: {
          height: '50px',
          background: '#F4F8FF',
          color: '#2C2C2C',
          padding: '0',
          fontSize: '14px',
          fontWeight: '400'
        },
        rowStyle: {
          minHeight: '40px',
          height: '45px',
          fontSize: '14px',
          background: '#fff'
        }
      }
    }
  },
  methods: {
    async handleDelete(row) {
      this.$confirm('是否删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await Func.fetch(ApiParams.remove, { id: row.id })
          if (res) {
            this.$message({ type: 'success', message: '删除成功' })
            this.getList()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '取消删除' }))
    },
    getBaseTemp(other = {}) {
      return [
        {
          name: '目标名称:',
          ad: '',
          vdaf: '日期',
          g: getDate(new Date(), 'YYYY-MM-DD'),
          isFirst: true,
          ...other
        },
        {
          name: '/',
          ad: '灰分Ad',
          std: '硫St.d',
          vdaf: '挥发份Vdaf',
          g: '粘结G',
          y: '胶质Y',
          csr: 'CSR',
          isSecond: true,
          ...other
        },
        { name: '上限', ad: '', std: '', vdaf: '', g: '', y: '', ...other },
        { name: '质量目标', ad: '', std: '', vdaf: '', g: '', y: '', ...other },
        { name: '下限', ad: '', std: '', vdaf: '', g: '', y: '', ...other }
      ]
    },
    getComponentKey(scope) {
      const index = scope.$index
      const key = scope.column.property
      switch (index) {
        case 0:
          const compMap = { ad: 'input', g: 'date', default: 'div' }
          return compMap[key] || compMap.default
        case 1:
          return 'div'
        case 2:
        case 3:
        case 4:
          return key !== 'name' ? 'input' : 'div'
        default:
          return 'div'
      }
    },


    getBaseValueByKeyAll() {
      return ['name', 'date', 'ad', 'std', 'vdaf', 'g', 'y', 'csr'].reduce((result, key) => {
        return {
          ...result,
          ...this.getBaseValueByKey(key)
        }
      }, {})
    },

    getBaseValueByKey(key) {
      const list = this.listBase
      const joinKey = suffix => `${key}${suffix}`
      switch (key) {
        case 'name':
          return { name: list[0].ad }
        case 'date':
          return { date: list[0].g }
        case 'ad':
        case 'std':
        case 'vdaf':
        case 'g':
        case 'y':
        case 'csr':
          return {
            [joinKey('Max')]: list[2][key],
            [joinKey('Target')]: list[3][key],
            [joinKey('Min')]: list[4][key]
          }
      }
    },

    setBaseValueByKey(target) {
      const list = this.getBaseTemp({ id: target.id })
      const keys = new Map([
        ['name', '[0].ad'],
        ['date', '[0].g'],
        ['adMax', '[2].ad'],
        ['stdMax', '[2].std'],
        ['vdafMax', '[2].vdaf'],
        ['gMax', '[2].g'],
        ['yMax', '[2].y'],
        ['csrMax', '[2].y'],

        ['adTarget', '[3].ad'],
        ['stdTarget', '[3].std'],
        ['vdafTarget', '[3].vdaf'],
        ['gTarget', '[3].g'],
        ['yTarget', '[3].y'],
        ['csrTarget', '[3].csr'],

        ['adMin', '[4].ad'],
        ['stdMin', '[4].std'],
        ['vdafMin', '[4].vdaf'],
        ['gMin', '[4].g'],
        ['yMin', '[4].y'],
        ['csrMin', '[4].csr']
      ])

      for (const [k, path] of keys) {
        set(list, path, get(target, k))
      }
      return list
    },


    spanMethodBaseList({ row, column, rowIndex, columnIndex }) {
      if (row.isFirst) {
        const colSpanMap = { ad: 2, std: 0, g: 2, y: 0 }
        let colspan = isDef(colSpanMap[column.property]) ? colSpanMap[column.property] : 1
        return { rowspan: 1, colspan }
      }
    },


    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.isFirst) {
        const colSpanMap = { ad: 2, std: 0, g: 2, y: 0 }
        const colspan = isDef(colSpanMap[column.property]) ? colSpanMap[column.property] : 1
        return { rowspan: 1, colspan }
      } else if (column.property === 'opt') {
        if (row.isSecond) {
          return { rowspan: 4, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 1 }
        }
      }
    },

    async getList() {
      try {
        const { data: sourceList } = await ApiParams.list({
          orderBy: 'date',
          orderDir: 'desc'
        })

        // 配置数据列表
        const result = []
        sourceList.forEach((item, index) => {
          result.push(...this.setBaseValueByKey(item))
        })

        if (result.length) {
          // this.listBase = result.slice(0, 5).map(v => {
          //     return {...v}
          // })
        }

        this.list = result
      } catch (e) {
        console.log(e, 'e')
      }
    },


    async handleUpdate() {
      if (this.loading) return
      this.loading = true
      try {
        const params = this.getBaseValueByKeyAll()
        if (!params.name) {
          return this.$message.error('请输入名称')
        }
        if (!params.date) {
          return this.$message.error('请选择日期')
        }
        const resp = await ApiParams.save(params)
        if (resp) {
          this.$message.success('更新成功')
          this.getList()
          this.listBase = this.getBaseTemp()
        }
      } catch (e) {
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-input__inner {
    border: none;
  }
}

.common--page-content {
  padding: 1.25rem 1rem;

  .table {
  }
}
</style>
