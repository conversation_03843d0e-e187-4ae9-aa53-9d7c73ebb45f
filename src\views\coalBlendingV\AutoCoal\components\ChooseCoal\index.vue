<template>
  <div class="container">
    <SnModal
      v-model="_value"
      submitText="确认"
      :cancel="cancel"
      :fullscreen.sync="fullscreen"
      :ok="ok"
      :title="title"
      class="modal"
      v-bind="$attrs"
      width="70%"
    >
      <SnTabs v-model="tabActive" :tabs="tabs" @change="handleTabChange">
        <AllSideStock
          ref="table"
          :modelConfig="{
            tableConfig: {
              showSelection: true,
            },
          }"
          isModalShow
          :coalSource="currentTab.coalSource"
        />
      </SnTabs>
    </SnModal>
  </div>
</template>

<script>
import { deepClone } from "@/utils";
import SnTabs from "@/components/Common/SnTabs/index.vue";
import AllSideStock from "@/views/coalBlendingV/Stock/components/AllSideStock";
import { COAL_SOURCE_TYPE } from "@/const";
import TipModal from "@/utils/modal";
import { workspaceCoalWashingAddCoals } from "@/api/coalWashing";

export default {
  inheritAttrs: false,
  components: {
    SnTabs,
    AllSideStock,
    // InSideStock: AllSideStock,
    // OutSideStock: AllSideStock,
    // OtherSideStock: AllSideStock,
  },
  data() {
    return {
      form: {},
      rules: {},
      fullscreen: true,
      tabActive: "InSideStock",
      tabs: [
        // {
        //   label: "外部煤源",
        //   value: "OutSideStock",
        //   coalSource: COAL_SOURCE_TYPE.WAI_BU,
        // },
        {
          label: "我的煤源",
          value: "InSideStock",
          coalSource: COAL_SOURCE_TYPE.ZI_YOU,
        },
        {
          label: "掌上煤焦煤源",
          value: "OtherSideStock",
          coalSource: COAL_SOURCE_TYPE.ZSMJ,
        },
        // {
        //   label: "全部",
        //   value: "AllSideStock",
        //   coalSource: "",
        // },
      ],
    };
  },
  computed: {
    currentTab() {
      return this.tabs.find((item) => item.value === this.tabActive) || {};
    },
    _value: {
      set(val) {
        this.$emit("input", val);
      },
      get() {
        return this.value;
      },
    },
    title() {
      return "添加煤源";
    },
  },
  props: {
    value: {
      type: Boolean,
      require: true,
    },
    record: {
      type: Object,
      default() {
        return {};
      },
    },
    model: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  watch: {
    record: {
      async handler(value) {
        await this.$nextTick();
      },
      immediate: true,
    },
  },
  created() {
    this.tabActive = "InSideStock";
  },
  methods: {
    handleTabChange(value) {
      // 根据选项卡类型配置表格列
      if (value.value === "InSideStock") {
        // 我的煤源选项卡
        this.$refs.table.setColumns([
          {
            label: "来源",
            prop: "coalSource",
            width: 90,
            hide: value.coalSource !== "",
            format: "dict|b_coal_source_type",
          },
          {
            label: "含税煤价",
            prop: "factoryPrice",
            width: 100,
            visible: true
          },
          {
            label: "运费",
            prop: "activePriceTransportPriceNoTax",
            width: 100,
            visible: true
          }
        ]);
      } else {
        // 掌上煤焦煤源选项卡
        this.$refs.table.setColumns([
          {
            label: "来源",
            prop: "coalSource",
            width: 90,
            hide: value.coalSource !== "",
            format: "dict|b_coal_source_type",
          },
          {
            label: "含税煤价",
            prop: "factoryPrice", 
            width: 100,
            visible: true
          },
          {
            label: "运费",
            prop: "activePriceTransportPriceNoTax",
            width: 100,
            visible: true
          }
        ]);
      }
      
      this.$refs.table.getList();
    },
    getFormData(form) {
      const { ...rest } = form;
      return {
        ...rest,
      };
    },

    getSubmitForm(rows = []) {
      return {
        data: rows.map((v) => {
          return {
            id: v.id,
            spaceType: "YH",
            coalSource: v.coalSource,
          };
        }),
      };
    },

    async cancel() {
      return false;
    },

    async ok() {
      const rows = this.$refs.table.getSelectRows();
      if (!rows.length) {
        return TipModal.msgWarning("至少选择一条煤源数据");
      }
      const resp = await workspaceCoalWashingAddCoals(this.getSubmitForm(rows));
      if (resp) {
        TipModal.msgSuccess(`${this.title}成功`);
        this.$emit("ok");
        return this.cancel();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0px;
}
</style>
