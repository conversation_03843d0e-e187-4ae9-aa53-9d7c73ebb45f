<template>
  <div class="app-container">
    <SnProTable :ref="pageRef" :actions="actions" :afterFetch="afterFetch" :beforeFetch="beforeFetch" :model="model"
                @row-class-name="rowClassName">
      <template #createDate="{ col }">
        <el-table-column v-bind="col">
          <template #default="{ row }">
            <span>{{ row.createDate ? row.createDate.substring(0, 10) : '' }}</span>
          </template>
        </el-table-column>
      </template>
      <template #name="{ col }">
        <el-table-column min-width="120" v-bind="col">
          <template #default="{ row }">
            <span class="under-line" @click="handleView(row)">{{ row.name }}</span>
          </template>
        </el-table-column>
      </template>
      <template #isTrace="{ col }">
        <el-table-column v-bind="col">
          <template #default="{ row }">
            <el-switch v-model="row.isTrace" active-value="Y" inactive-value="N"
                       @change="traceChange($event, row)"></el-switch>
          </template>
        </el-table-column>
      </template>
      <template #opt="{ row }">
        <el-button type="text" @click="exportExcel(row)">导出excel</el-button>
        <el-button type="text" @click="trendDialogOpen(row)">趋势图</el-button>
        <el-button type="text" @click="handleRemove(row)">删除</el-button>
      </template>

    </SnProTable>
    <ManyWash :details="details" :washVisible.sync="dialogVisible" @washDetailClose="handleWashClose"/>
    <el-dialog :before-close="trendDialogClose" :visible.sync="trendDialogVisible" title="趋势图">
      <div class="block">
        <div class="header">
          <div class="title">{{ trendDialogQurey.name }}</div>
          <el-date-picker v-model="trendDaterange" :picker-options="pickerOptions" end-placeholder="结束日期"
                          start-placeholder="开始日期" style="width: 220px" type="daterange" value-format="yyyy-MM-dd"
                          @change="handleChange"></el-date-picker>
          <div class="details" @click="handleClick">查看详情<i class="el-icon-arrow-right"></i></div>
        </div>
        <div ref="chart" class="chart"></div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="detailsDialog.visible" class="details" title="每日成本" width="1000px">
      <el-table :border="true" :data="data" :stripe="true" height="500px">
        <el-table-column align="center" fixed label="日期" min-width="120" prop="date"/>
        <el-table-column align="center" label="名称" min-width="120" prop="name"/>
        <el-table-column align="center" label="入炉煤含税价" min-width="140" prop="inPrice"/>
        <!-- <el-table-column align="center" label="入炉煤不含税价" min-width="140" prop="inPrice"/> -->
        <el-table-column align="center" label="日涨跌" min-width="140" prop="rise"/>
      </el-table>
      <div class="pagination-container">name="switch"
        <el-pagination :current-page="query.current" :page-size="query.size" :page-sizes="[50, 100, 200, 500]"
                       :pager-count="5" :total="total" layout="total,pager,next,sizes,jumper,slot" next-text="下一页"
                       style="text-align: right;margin-bottom: 10px;padding-right:10px " @size-change="sizeChange"
                       @current-change="currentChange">
          <el-button class="confirm">确认</el-button>
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import model from './model'
import trendModel from '../PriceTrend/model'
import {formatToDateTime, getDate} from '@/utils/dateUtils'
import SnProTable from '@/components/Common/SnProTable/index.vue'
import TipModal from '@/utils/modal'
import ManyWash from '@/views/coalblending/details/manyWash.vue'
import {defineComponent} from 'vue'
import dayjs from 'dayjs'
import echarts from 'echarts'
import {mode} from 'mathjs'
import Download from '@/utils/download'

export default defineComponent({
  name: 'PlanManage',
  components: {ManyWash, SnProTable},
  data() {
    return {
      model,
      pageRef: 'page',
      permissions: {
        save: '*',
        remove: '*'
      },
      addInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      },
      dialogVisible: false,
      details: {},
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      trendDialogVisible: false,
      trendDaterange: [],
      trendDialogQurey: {
        current: 1,
        size: 50,
        name: '',
        beginDate: '',
        endDate: ''
      },
      trendChart: null,

      detailsDialog: {
        visible: false
      },
      query: {
        current: 1,
        size: 50,
        name: '',
        beginDate: '',
        endDate: ''
      },
      total: '',
      list: [],
      data: []
    }
  },
  computed: {
    actions() {
      return []
    }

  },
  destroyed() {
    window.removeEventListener('resize', this.resize)
  },
  methods: {
    async exportExcel(row) {
      try {
        const result = await this.model.coalWashingExport({
          id: row.id,
          useConfirm: true
        })
        Download.excel(result.data, `${this.$route.meta.title}-${formatToDateTime(new Date())}.xlsx`)
      } catch (e) {
        console.log(e)
      }
    },
    async traceChange(e, row) {
      const res = await model.saveIsTrace({isTrace: e, id: row.id})
      if (res) {
        this.getList()
      }
    },
    initChart(records) {
      this.chart.setOption({
        xAxis: {
          type: 'category',
          data: records.map(item => item.date.substring(0, 10)).reverse(),
          axisTick: {
            show: false
          },
          axisLine: {
            show: true,
            width: 5,
            symbol: ['none', 'arrow'],
            symbolOffset: 30,
            lineStyle: {
              color: '#F5F5FF',// 更改坐标轴颜色
              type: 'solid',
              shadowOffsetX: 30,
              shadowColor: '#F5F5FF'
            }
          },
          axisLabel: {
            color: '#9A9A9A'
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          scale: true,
          splitLine: {
            lineStyle: {
              color: '#F5F5FF'
            }
          },
          axisLine: {
            show: true,
            width: 5,
            symbol: ['none', 'arrow'],
            symbolOffset: 30,
            lineStyle: {
              color: '#F5F5FF', // 更改坐标轴颜色
              type: 'solid',
              shadowOffsetY: -30,
              shadowColor: '#F5F5FF'
            }
          },
          axisLabel: {
            showMaxLabel: false,
            color: '#9A9A9A'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let projectCoal = ''
            if (params[0].data.projectCoal) {
              let list = JSON.parse(params[0].data.projectCoal)
              projectCoal = list.map(item => {
                const _color = item.rise < 0 ? '#33cad9' : item.rise > 0 ? '#FF9D7F' : '#ffffff'
                return `<div>${item.name}：${item.oldPrice}(<span style="color: ${_color}">${item.rise}</span>)</div>`
              }).join('')
            }
            const color = params[0].data.rise < 0 ? '#33cad9' : params[0].data.rise > 0 ? '#FF9D7F' : '#ffffff'
            return `<div>日期：${params[0].axisValue}</div>`
              + `<div>价格：${params[0].data.value}</div>`
              + `<div >日涨跌：<span style="color: ${color};">${params[0].data.rise || 0}</span></div>`
              + `<div style="font-size: 12px">${projectCoal}</div>`
          }
        },
        legend: {
          type: 'plain',
          data: ['价格'],
          bottom: 0,
          selected: {
            '价格': true
          }
        },
        grid: {
          left: '3%',
          right: '40',
          top: '30',
          bottom: '30',
          containLabel: true
        },
        series: [
          {
            data: records.map(item => ({...item, value: item.inPrice})).reverse(),
            type: 'line',
            name: '价格',
            label: {
              show: true,
              color: '#595EC9'
            },
            formatter: function (item) {
              return item.data.inPrice
            },
            lineStyle: {
              color: '#595EC9'
            },
            itemStyle: {
              color: '#595EC9'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#595EC9'
                },
                {
                  offset: 1,
                  color: 'white'
                }
              ])
            }
          }
        ]
      })
    },
    async trendDialogOpen(row) {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      this.trendDaterange = [dayjs(start).format('YYYY-MM-DD'), dayjs(end).format('YYYY-MM-DD')]
      this.trendDialogQurey.name = row.name
      this.trendDialogQurey.beginDate = this.trendDaterange[0]
      this.trendDialogQurey.endDate = this.trendDaterange[1]
      const res = await trendModel.pageByCoalCategory(this.trendDialogQurey)
      if (res) {
        this.trendDialogVisible = true
        await this.$nextTick()
        if (!this.chart) {
          this.chart = echarts.init(this.$refs.chart)
          window.addEventListener('resize', this.resize)
        }
        this.initChart(res.data.records)
        this.trendDialogData = res.data.records
      }
    },
    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    trendDialogClose() {
      this.trendDialogVisible = false
    },
    async handleChange(e) {
      this.trendDialogQurey.beginDate = this.trendDaterange[0] || ''
      this.trendDialogQurey.endDate = this.trendDaterange[1] || ''
      const res = await trendModel.pageByCoalCategory(this.trendDialogQurey)
      if (res) {
        this.initChart(res.data.records)
      }
    },
    async handleClick() {
      this.query.name = this.trendDialogQurey.name
      this.query.beginDate = this.trendDialogQurey.beginDate
      this.query.endDate = this.trendDialogQurey.endDate
      const res = await trendModel.pageByCoalCategory(this.query)
      if (res) {
        this.detailsDialog.visible = true
        this.total = res.data.total
        this.data = res.data.records
      }
    },
    async sizeChange(e) {
      this.query.size = e
      const res = await trendModel.pageByCoalCategory(this.query)
      if (res) {
        this.detailsDialog.visible = true
        this.total = res.data.total
        this.data = res.data.records
      }
    },
    async currentChange(e) {
      this.query.current = e
      const res = await trendModel.pageByCoalCategory(this.query)
      if (res) {
        this.detailsDialog.visible = true
        this.total = res.data.total
        this.data = res.data.records
      }
    },

    handleWashClose() {
      this.dialogVisible = false
      this.getList()
    },

    async handleView(row) {
      this.dialogVisible = true
      this.details = {...row}
    },

    afterFetch(data, listKey) {
      return {...data}
    },

    getDate,
    rowClassName({row, rowIndex, columnIndex, column}) {
      if (rowIndex === 0 && row.base === 1) {
        return 'mark-base-row'
      }
    },
    beforeFetch(params) {
      return {
        ...params
      }
    },

    getList() {
      this.$refs[this.pageRef].getList()
    },

    // 新增时record置为空
    setModal(target, optName = 'create', record = {}) {
      this[target].visitable = true
      this[target].optName = optName
      if (optName) this[target].record = {...record}
    },

    async handleRemove({id}) {
      try {
        const resp = await this.model.remove({id, useConfirm: true})
        if (resp) {
          TipModal.msgSuccess(`删除成功`)
        }
        this.getList()
      } catch (error) {
      }
    },
    async handleUpdate(row) {
      try {
        this.setModal('addInfo', 'update', row)
      } catch (error) {
        console.log(error, 'error')
      }
    }

  }
})
</script>

<style lang="scss" scoped>
.under-line {
  cursor: pointer;
  color: #33cad9;
  text-decoration: underline;
}


::v-deep {

  .el-table__footer-wrapper tbody td.el-table__cell,
  .el-table__fixed-footer-wrapper tbody td.el-table__cell {
    background-color: #fff8f8;
  }

  // 选中 .el-table__footer-wrapper 第一个.cell
  .el-table__footer-wrapper .cell:first-child {
    //height: 35px;
  }

  .el-table .el-table__footer-wrapper .cell,
  .el-table .el-table__fixed-footer-wrapper .cell {
    padding: 0;
    height: 35px;

    .sampleName_cell {
      height: 100%;
      display: flex;
      justify-content: center;
      text-align: center;
      align-content: center;
      flex-direction: row;
      align-items: center;
    }

    .cell {
      flex: 1;
      display: flex;
      flex-direction: column;
      line-height: normal;
      justify-content: space-between;

      div {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: #ebeef5 1px solid;
        box-sizing: content-box;

        &:last-of-type {
          border-bottom: none;
        }
      }
    }
  }

  .el-table__footer-wrapper tbody td,
  .el-table__fixed-footer-wrapper tbody td {
    padding: 0;
  }

  .is-active .el-submenu__title {
    color: #2878ff;
  }
}

.tag-left-right {
  font-size: 10px;
  position: absolute;
  right: -35px;
  text-align: center;
  top: -8px;
  width: 76px;
  line-height: 42px;
  height: 28px;
  z-index: 999;
  color: #fff;
  background: #ff9639;
  transform: rotate(50deg);
}

::v-deep {
  .el-table .cell {
    min-height: 33px;
    line-height: 33px;
    padding-right: 0;
    padding-left: 0;
  }
}

.block {
  background-color: #fff;
  overflow: hidden;
  padding: 10px;
  width: 100%;
  height: 500px;

  .header {
    display: flex;
    align-items: center;

    .title {
      font-size: 16px;
      color: #0F0F0F;
      margin-right: auto;
      line-height: 35px;
      white-space: nowrap;
    }

    .details {
      cursor: pointer;
      margin-left: 10px;
      font-size: 14px;
      white-space: nowrap;
      color: #6E6E6E;
      line-height: 35px;
    }
  }

  .chart {
    padding-top: 10px;
    width: 100%;
    height: calc(100% - 30px);
  }
}

::v-deep .details {

  .el-table th.el-table__cell {
    background-color: #24c1ab;
    color: #e9e9e9;
  }

  .el-table .cell {
    min-height: 25px;
    height: 25px;
    line-height: 25px;
  }

  .el-table__row {
    height: 25px;
  }

  .el-table th.el-table__cell > .cell {
    color: white;
  }

  .el-table tr .el-table__cell {
    padding: 0;
    height: 25px;
    line-height: 25px;
    color: #292929;
  }
}
</style>
