// eslint-disable-next-line

import BaseModel, {TableConfig, FormConfig} from '@/components/Common/SnProTable/model'
import {dateUtil, formatToDateTime} from '@/utils/dateUtils'
import {MERGE_TYPE} from '@/const'
import request from '@/utils/request'

export const name = `/cwe/a/coalWashingProjectItem`

const createFormConfig = () => {
  return {
    // fieldMapToTime: [['_dateRange', ['beginDate', 'endDate'], 'YYYY-MM-DD']],
    filters: [
      {
        label: '品名',
        prop: 'filter_LIKES_name'
      },
      {
        hide: true,
        prop: 'orderBy',
        defaultValue: 'createDate'
      },
      {
        hide: true,
        prop: 'orderDir',
        defaultValue: 'desc'
      }
    ]
  }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
  return {
    mountedQuery: true,
    showOpt: true, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: false, // 显示选择列
    useApiList: false,
    columns: [
      {
        slot: 'name',
        label: '名称',
        isShow: true,
        prop: 'name',
        isFilter: true,
        filter: {
          prop: 'filter_LIKES_name',
          label: '名称'
        }
      },
      {
        label: 'Ad',
        prop: 'inAd',
        isShow: true,
        minWidth: 60
      },
      {
        label: 'St,d',
        prop: 'inStd',
        isShow: true,
        minWidth: 60
      },
      {
        label: 'G',
        prop: 'inG',
        isShow: true,
        minWidth: 60
      },
      // {
      //   label: '入炉煤不含税价(水8)',
      //   prop: 'inPrice',
      //   isShow: true,
      //   minWidth: 100
      // },
      {
        label: '入炉煤含税价',
        prop: 'inPrice',
        isShow: true,
        minWidth: 100
      },
      {
        label: '反射率',
        prop: 'theoryMacR0',
        isShow: true,
        minWidth: 60
      },
      {
        label: '标准差',
        prop: 'theoryMacS',
        isShow: true,
        minWidth: 60
      },

      {
        label: 'CSR',
        prop: 'gyCsr',
        isShow: true,
        minWidth: 60
      },
      {
        label: '焦炭价格',
        isShow: true,
        isFilter: true,
        prop: 'jtPrice',
        minWidth: 100,
        filter: {
          label: '焦炭成本',
          start: 'filter_GEM_jtPrice',
          end: 'filter_LEM_jtPrice',
          component: 'NumberRanger'
        }
      }

    ]
  }
}

export class Model extends BaseModel {
  coalWashingProject

  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }

  pageByCoalCategory(query) {
    return request({
      url: `${name}/pageByCoalCategory`,
      method: 'get',
      params: query
    })
  }

  pageProject(query) {
    return request({
      url: `/cwe/a/coalWashingProject/pageCoalProject`,
      method: 'get',
      params: query
    })
  }
}

export default new Model()
