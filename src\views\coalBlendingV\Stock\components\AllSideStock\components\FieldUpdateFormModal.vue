<template>
    <div class="container">
        <SnModal v-model="_value" :cancel="cancel" :fullscreen.sync="fullscreen" :ok="ok" :showFooter="!isViewStatus"
                 :title="title"
                 class="modal" v-bind="$attrs" width="700px">
            <el-form ref="form" :disabled="isViewStatus" :model="form" :rules="rules" labelPosition="top"
                     labelWidth="100px"
                     style="height: 100%">
                <SnFormCard title="基本信息">
                    <VhTable ref="excelTable" :list="form.data" class="table-content" @init="setTableSetting">
                        <VHColumn data="coalName" title="品名" type="autocomplete">
                            <template #default="{ value }">
                                <span class="t-ellipsis">{{ value }}</span>
                            </template>
                        </VHColumn>

                        <VHColumn formRequire data="activePriceTransportPriceNoTax" title="不含税运费" type="numeric">
                            <template #default="{ value }">
                                <span>{{ getShowValue(value, 2, 'ROUND_HALF_UP') }}</span>
                            </template>
                        </VHColumn>
                        <VHColumn formRequire data="activePriceTransportCostP1" title="路耗%" type="numeric">
                            <template #default="{ value }">
                                <span>{{ getShowValue(value, 2, 'ROUND_HALF_UP') }}</span>
                            </template>
                        </VHColumn>

                        <VHColumn formRequire data="reduceMtStandard" title="折水标准"
                                  type="numeric">
                            <template #default="{ value }">
                                <span>{{ getShowValue(value, 0, 'ROUND_HALF_UP') }}</span>
                            </template>
                        </VHColumn>

                    </VhTable>
                </SnFormCard>
            </el-form>
        </SnModal>
    </div>
</template>

<script>
import TipModal from '@/utils/modal'
import {deepClone, FetchData} from '@/utils'
import {VhTable, VHColumn, VHDate, VHSelect} from '@/components/ExcelTable'
import {INDEX_NUM_FORMAT} from '@/const'
import {getDate} from '@/utils/dateUtils'
import CalcUtils from '@/utils/calcUtils'
import {getRangeListApi} from "@/api/coal";
import {getDictData, getDictDatas} from '@/utils/dict'

export default {
    inheritAttrs: false,
    components: {
        VHDate,
        VHSelect,
        VhTable,
        VHColumn
    },
    data() {
        return {
            form: {},
            rules: {},
            fullscreen: false,
        }
    },
    computed: {
        getOptMap() {
            return {
                update: '修改折水/路耗',
                view: '查看'
            }
        },
        _value: {
            set(val) {
                this.$emit('input', val)
            },
            get() {
                return this.value
            }
        },
        title() {
            return this.getOptMap[this.optName]
        },
        isViewStatus() {
            return this.optName === 'view'
        },
        isUpdateStatus() {
            return this.optName === 'update'
        },
        isCreateStatus() {
            return this.optName === 'create'
        },
    },
    props: {
        useApi: {
            type: Boolean,
            default: false
        },
        getApi: {
            type: Function
        },
        optName: {
            type: String,
            require: true
        },
        value: {
            type: Boolean,
            require: true
        },
        record: {
            type: Object,
            default() {
                return {}
            }
        },
        model: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    watch: {
        record: {
            async handler(value) {
                await this.$nextTick()
                this.form = this.getFormData(value)
            },
            immediate: true
        }
    },
    async created() {
    },
    methods: {

        getShowValue: CalcUtils.getShowValue,

        async setTableSetting() {
            const settingConfig = () => {
                if (!this.isCreateStatus) return {}
                return {
                    contextMenu: {
                        items: {
                            row_above: {name: '向上插入一行'},
                            row_below: {name: '向下插入一行'},
                            remove_row: {name: '删除行'},
                            clear_custom: {
                                name: '清空所有单元格数据',
                                callback() {
                                    this.clear()
                                }
                            }
                        }
                    }
                }
            }
            const instance = this.$refs.excelTable.getHotInstance()
            instance.updateSettings({
                ...settingConfig(),
                cells(row, col, prop) {
                    const cellProperties = {
                        className: 't-ellipsis'
                    }
                    return cellProperties
                }
            })
        },


        getFormData(form) {
            const {...rest} = form
            return {
                ...rest,
                data: rest.data.map((v) => {
                    return {
                        ...v,
                    }
                })
            }
        },

        getSubmitForm() {
            const submitForm = deepClone(this.form)
            const {...rest} = submitForm
            const {getData, getColumns, getFormatEditData} = this.$refs.excelTable
            const data = getFormatEditData(getData(), getColumns())
            return {
                ...rest,
                data: data
            }
        },

        async cancel() {
            this.form = {}
            this.$refs['form'].resetFields()
            return false
        },

        async ok() {
            await this.$refs['form'].validate()
            const status = await this.$refs['excelTable'].validate()
            if (!status) return
            const resp = await this.model.batchUpdateMt(this.getSubmitForm())
            if (resp) {
                TipModal.msgSuccess(`${this.title}成功`)
                this.$emit('ok')
                return this.cancel()
            }

        }
    }
}
</script>

<style lang="scss" scoped>
.table-content {
  width: 100%;
  height: 100%;
}

::v-deep {
  .el-dialog__body {
    padding: 25px 25px;
    height: 400px;
    overflow: auto;
  }

}

</style>
