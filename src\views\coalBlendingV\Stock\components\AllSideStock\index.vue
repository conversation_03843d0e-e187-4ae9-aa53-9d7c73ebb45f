<template>
    <div class="app-container">
        <SnProTable
                :layoutHeightOther="isModalShow ? 50 : 0"
                rowKey="uuid"
                :ref="pageRef"
                :actions="actions"
                :afterFetch="afterFetch"
                :beforeFetch="beforeFetch"
                :model="model"
                @row-class-name="rowClassName"
        >
            <template #date="{ col }">
                <el-table-column v-bind="col">
                    <template #default="{row,$index}">
                        <div style="position: relative;overflow: hidden;">
                            <span>{{ getDate(row[col.prop]) }}</span>
                            <div v-if="row.manualUpdated ==='Y' && row.coalSource !== COAL_SOURCE_TYPE.WAI_BU"
                                 class="tag-left-right">
                                <span class="triangle-text">人工</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </template>

            <template v-for="item in getColSlots" #[item.prop]="{ col }">
                <el-table-column v-bind="col">
                    <template #default="{row}">
                        <template v-if="!row.isEdit">
                            <span v-if="col.decimalPlaces"> {{
                                getShowValue(row[col.prop], col.decimalPlaces, 'ROUND_HALF_UP')
                                }}</span>
                            <span v-else> {{ row[col.prop] }}</span>
                        </template>
                        <template v-else>
                            <el-autocomplete
                                    v-if="item.prop ==='coalCategoryName'"
                                    v-model="row[`${col.prop}${editKey}`]"
                                    :fetch-suggestions="querySearch"
                                    class="noPaddingInput"
                            >
                            </el-autocomplete>
                            <el-input
                                    v-else
                                    v-model="row[`${col.prop}${editKey}`]"
                                    :placeholder="`${col.label}`"
                                    :type="item.type"
                                    @input="handleInputChange(row,col)"
                                    class="noPaddingInput"
                            >
                            </el-input>
                        </template>
                    </template>
                </el-table-column>
            </template>


            <template #coalName="{ col }">
                <el-table-column v-bind="col">
                    <template #default="{row}">
                        <div style="position: relative;overflow: hidden;">
                                <span @click.stop="editName(row)"
                                      style="color:#256DDF;text-decoration: underline">{{ row[col.prop] }}  </span>
                            <div v-if="row.isCoalRock ==='Y'" style="background:#FF8B41" class="tag-left-right">
                                <span class="triangle-text">岩</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </template>

            <template #type="{ col }">
                <el-table-column v-bind="col">
                    <template #default="{row}">
                        <span>
                            {{ row.type === 'wb' ? '外部' : 
                               row.type === 'zsmy' ? '掌上煤源' : 
                               row.type === 'zsmj' ? '掌上煤焦' : 
                               row.type === 'nb' ? '内部' : 
                               row.source || row.type || '未知' }}
                        </span>
                    </template>
                </el-table-column>
            </template>

            <template #activePriceFactoryPriceNoTax="{ col }">
                <el-table-column v-bind="col">
                    <template #default="{row}">
                        <span>{{
                            getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                            }} </span>
                    </template>
                </el-table-column>
            </template>

            <template #factoryPrice="{ col }">
                <el-table-column v-bind="col">
                    <template #default="{row}">
                        <span>{{
                            getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                            }} </span>
                    </template>
                </el-table-column>
            </template>

            <template #reduceMtFactoryPriceNoTax="{ col }">
                <el-table-column v-bind="col">
                    <template #default="{row}">
                        <span>{{
                            getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                            }} </span>
                    </template>
                </el-table-column>
            </template>

            <template #coalBlendingCost="{ col }">
                <el-table-column v-bind="col">
                    <template #default="{row}">
                        <span>{{
                            getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                            }} </span>
                    </template>
                </el-table-column>
            </template>


            <template #opt="{ row,...rest }">
                <template v-if="!isModalShow">
                    <!--                <template v-if="checkPermission([permissions.save])">-->
                    <!--                    <el-button v-if="!row.isEdit" type="text" @click.stop="handleRow(row, 'edit', rest)">-->
                    <!--                        编辑-->
                    <!--                    </el-button>-->
                    <!--                    <template v-else>-->
                    <!--                        <el-button type="text" :loading="row.loading" @click.stop="handleRow(row, 'save', rest)">-->
                    <!--                            保存-->
                    <!--                        </el-button>-->
                    <!--                        <el-divider direction="vertical"></el-divider>-->
                    <!--                        <el-button type="text" :loading="row.loading" @click.stop="handleRow(row, 'cancel', rest)">-->
                    <!--                            取消-->
                    <!--                        </el-button>-->
                    <!--                    </template>-->

                    <!--                    <el-divider direction="vertical"></el-divider>-->
                    <!--                </template>-->

                    <!--                <template v-if="checkPermission([permissions.remove])">-->
                    <!--                    <el-button v-if="MERGE_TYPE.MERGE === row.mergeType" type="text" @click.stop="handleRemove(row)">-->
                    <!--                        取消合并-->
                    <!--                    </el-button>-->
                    <!--                    <el-button v-else type="text" @click.stop="handleRemove(row)">删除</el-button>-->
                    <!--                    <el-divider direction="vertical"></el-divider>-->
                    <!--                </template>-->
                    <el-button v-if="checkPermission([permissions.save])" type="text"
                               @click.stop="handleSortTop(row)">
                        {{ row.sort ? '取消置顶' : '置顶' }}
                    </el-button>
                </template>

            </template>

        </SnProTable>


        <FormModal
                v-if="addInfo.visitable"
                v-model="addInfo.visitable"
                :model="model"
                :categoryNamesList="categoryNamesList.map(v=>v.name)"
                :optName="addInfo.optName"
                :coalSource="coalSource"
                :record="addInfo.record"
                @ok="() => getList(true)"
        />
        <AvgFormModal
                v-if="avgAddInfo.visitable"
                v-model="avgAddInfo.visitable"
                :model="model"
                :categoryNamesList="categoryNamesList.map(v=>v.name)"
                :optName="avgAddInfo.optName"
                :coalSource="coalSource"
                :record="avgAddInfo.record"
                @ok="() => getList(true)"
        />
        <FieldUpdateFormModal
                v-if="fieldUpdateInfo.visitable"
                v-model="fieldUpdateInfo.visitable"
                :model="model"
                :coalSource="coalSource"
                :optName="fieldUpdateInfo.optName"
                :record="fieldUpdateInfo.record"
                @ok="() => getList(true)"
        />

        <ItemCoal
                :is-readonly="coalDetail.coalSource === COAL_SOURCE_TYPE.ZSMJ"
                v-if="addVisible"
                :add-visible="addVisible"
                :details="coalDetail"
                :categoryNamesList="categoryNamesList.map(v=>v.name)"
                :model="model"
                :dialogStatus="dialogStatus"
                @closeVisible="()=>{
                    addVisible = false
                    getList(false)
                }"
        >
        </ItemCoal>

    </div>

</template>

<script>
import TipModal from '@/utils/modal'
import Model, {getEditCols} from './model'
import FormModal from './components/FormModal.vue'
import AvgFormModal from './components/AvgFormModal.vue'
import FieldUpdateFormModal from './components/FieldUpdateFormModal.vue'
import {getDate} from '@/utils/dateUtils'
import SnProTable from '@/components/Common/SnProTable/index.vue'
import checkPermission from '@/utils/permission'
import {COAL_SOURCE_TYPE, MERGE_TYPE} from '@/const'
import CalcUtils from '@/utils/calcUtils'
import SnSimpleSelect from "@/components/Common/SnSimpleSelect/index.vue";
import {listCategoryNames} from "@/api/common/listAllSimple";
import ItemCoal from "@/views/coalBlendingV/Stock/components/OutSideStock/components/ItemCoal.vue";

export default {
    name: 'AllSideStock',
    components: {ItemCoal, SnSimpleSelect, SnProTable, FormModal, FieldUpdateFormModal, AvgFormModal},
    data() {
        return {
            COAL_SOURCE_TYPE,
            addVisible: false,
            coalDetail: {},
            dialogStatus: 'update',


            model: null,
            MERGE_TYPE,
            pageRef: 'page',
            permissions: {
                save: '*',
                remove: '*'
            },
            addInfo: {
                optName: 'create',
                visitable: false,
                record: {}
            },

            avgAddInfo: {
                optName: 'create',
                visitable: false,
                record: {}
            },

            fieldUpdateInfo: {
                optName: 'create',
                visitable: false,
                record: {}
            },

            editKey: '_edit',
            editPropList: getEditCols(),
            categoryNamesList: []

        }
    },
    created() {
        this.model = new Model(this.modelConfig)
        this.getDataList()
        this.getCategoryNamesList().then(list => {
            this.categoryNamesList = list.map(v => {
                return {
                    name: v,
                    id: v,
                    value: v
                }
            })
        })
    },
    activated() {
        this.$nextTick(() => {
            if (this.$refs[this.pageRef]) {
                const columns = this.model.tableConfig.columns;
                this.$refs[this.pageRef].setColumns(columns);
                this.$refs[this.pageRef].getList(true);
            }
        });
    },
    props: {
        coalSource: {
            type: String,
            default: ''
        },
        isModalShow: {
            type: Boolean,
            default: false
        },
        modelConfig: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        getColSlots() {
            const exclude = ['activePriceFactoryPriceNoTax', 'reduceMtFactoryPriceNoTax', 'coalBlendingCost']
            return this.editPropList.filter((item) => !exclude.includes(item.prop))
        },
        getEditList() {
            return [...this.editPropList]
        },
        actions() {
            return [
                // {
                //     type: 'add',
                //     text: '新增',
                //     hasPermission: '*',
                //     onClick: async (item) => {
                //         this.setModal('addInfo')
                //     }
                // },
                // {
                //     type: 'add',
                //     text: '合并数据',
                //     hasPermission: [this.permissions.save],
                //     onClick: async (item) => {
                //         let data = this.$refs[this.pageRef].getSelectRows()
                //         if (data.length) {
                //             this.setModal('avgAddInfo', 'update', {
                //                 data: data
                //             })
                //         } else {
                //             TipModal.msgWarning('请先选择要操作的数据')
                //         }
                //     }
                // },
                // {
                //     type: 'add',
                //     text: '批量编辑',
                //     hasPermission: [this.permissions.save],
                //     onClick: async (item) => {
                //         let data = this.$refs[this.pageRef].getSelectRows()
                //         if (data.length) {
                //             data = data.map((item) => {
                //                 const obj = {...item}
                //                 for (const [k, v] of Object.entries(obj)) {
                //                     if (v === null || v === '/') obj[k] = undefined
                //                 }
                //                 return obj
                //             })
                //             this.setModal('addInfo', 'update', {
                //                 data: data
                //             })
                //         } else {
                //             TipModal.msgWarning('请先选择要操作的数据')
                //         }
                //     }
                // },

                // 批量改
                // {
                //     type: 'add',
                //     text: '改折水/路耗/运费/运费',
                //     hasPermission: [this.permissions.save],
                //     onClick: async (item) => {
                //         let data = this.$refs[this.pageRef].getSelectRows()
                //         if (data.length) {
                //             this.setModal('fieldUpdateInfo', 'update', {
                //                 data: data.map(v => {
                //                     return {
                //                         id: v.id,
                //                         reduceMtStandard: v.reduceMtStandard,
                //                         activePriceTransportCostP1: v.activePriceTransportCostP1,
                //                         coalSource: v.coalSource,
                //                     }
                //                 })
                //             })
                //         } else {
                //             TipModal.msgWarning('请先选择要操作的数据')
                //         }
                //     }
                // },


            ]
        },

    },
    methods: {
        editName(row) {
            this.addVisible = true
            this.coalDetail = {...row}

            console.log(this.coalDetail, 'coal')
        },

        querySearch(queryString, cb) {
            let restaurants = this.categoryNamesList;
            console.log(this.categoryNamesList, 'c')
            let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
            // 调用 callback 返回建议列表的数据
            cb(results);
        },
        createFilter(queryString) {
            return (restaurant) => {
                return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
            };
        },

        async getCategoryNamesList() {
            try {
                const {data} = await listCategoryNames({coalSource: this.coalSource})
                return data
            } catch (e) {
                return []
            }
        },
        async getDataList() {
            try {
                const {data} = await getRangeListApi()
                this.rangeList = data
            } catch (e) {
                this.rangeList = []
            }
        },
        handleInputChange(row, col) {
            const prop = col.prop
            const getEditVal = (key) => {
                const value = row[`${key}${this.editKey}`]
                return [undefined, null, ''].includes(value) ? undefined : Number(value)
            }
            const set = (key, value) => {
                row[`${key}${this.editKey}`] = value
            }
            const isEmpty = (val) => ['', null, undefined].includes(val)


            const v = {
                activePriceCoalPriceWithTax: getEditVal('activePriceCoalPriceWithTax'),
                activePriceTransportPriceNoTax: getEditVal('activePriceTransportPriceNoTax'),
                activePriceTransportCostP1: getEditVal('activePriceTransportCostP1'),
                activePriceCoalPriceNoTax: getEditVal('activePriceCoalPriceNoTax'),
                mt: getEditVal('mt'),
                reduceMtStandard: getEditVal('reduceMtStandard'),
            }

            // 含税煤价
            const taxPriceEffect = () => set('activePriceFactoryPriceNoTax', CalcUtils.calculateTaxPrice(v.activePriceCoalPriceWithTax, v.activePriceTransportPriceNoTax, v.activePriceTransportCostP1))
            // 不含税煤价
            const noTaxPriceEffect = () => set('activePriceFactoryPriceNoTax', CalcUtils.calculateNoTaxPrice(v.activePriceCoalPriceNoTax, v.activePriceTransportPriceNoTax, v.activePriceTransportCostP1))

            if (['activePriceCoalPriceNoTax', 'activePriceCoalPriceWithTax', 'activePriceTransportPriceNoTax', 'activePriceTransportCostP1'].includes(prop)) {
                if (!isEmpty(v.activePriceCoalPriceWithTax) && !isEmpty(v.activePriceCoalPriceNoTax)) { // 如果都不位空则默认使用不含税价格算
                    noTaxPriceEffect()
                } else if (isEmpty(v.activePriceCoalPriceWithTax)) {
                    noTaxPriceEffect()
                } else if (isEmpty(v.activePriceCoalPriceNoTax)) {
                    taxPriceEffect()
                }
            }

            set('reduceMtFactoryPriceNoTax', CalcUtils.calculateRemoveWaterNoTaxPrice(v.mt, v.reduceMtStandard, getEditVal('activePriceFactoryPriceNoTax')))
            set('coalBlendingCost', getEditVal('reduceMtFactoryPriceNoTax'))

        },
        getShowValue: CalcUtils.getShowValue,
        checkPermission,
        setModal(target, optName = 'create', record = {}) {
            this[target].visitable = true
            this[target].optName = optName
            if (optName) this[target].record = {...record}
        },
        afterFetch(data, listKey) {
            data[listKey].forEach(item => {
                item.uuid = `${item.id}_${item.coalSource}`
                item.sourceItem = {...item}
                item.loading = false
                item.isEdit = false
                this.getEditList.forEach((value) => {
                    item[`${value.prop}${this.editKey}`] = item[value.prop]
                })
            })


            return {...data, [listKey]: data[listKey]}
        },

        getDate,
        rowClassName({row, rowIndex, columnIndex, column}) {
            if (rowIndex === 0 && row.base === 1) {
                return 'mark-base-row'
            }
        },
        beforeFetch(params) {
            return {
                ...params,
                coalSource: this.coalSource
            }
        },
        async handleSortTop(row) {
            const status = await this.model.sortTop({
                id: row.id,
                sort: row.sort,
                coalSource: row.coalSource,
            })
            if (status) {
                TipModal.msgSuccess(`置顶成功`)
                this.getList(false)
            }
        },

        async handleRemove({id, coalSource, mergeType}) {
            try {
                const resp = await this.model.remove({id, coalSource, useConfirm: true, type: mergeType})
                if (resp) {
                    TipModal.msgSuccess(`删除成功`)
                    this.getList(true)
                }

            } catch (error) {
            }
        },

        async handleRow(row, type, ...rest) {
            if (type === 'edit') {
                row.isEdit = true
            }
            if (type === 'save') {
                row.loading = true
                const rowIndex = rest[0]['$index'] + 1
                try {
                    const resultRow = {...row, rockList: await this.buildRockList(row)}
                    this.getEditList.forEach((value) => {
                        resultRow[value.prop] = resultRow[`${value.prop}${this.editKey}`]
                    })
                    const resp = await this.model.update({data: [resultRow]})
                    if (resp) {
                        TipModal.msgSuccess(`第${rowIndex}行编辑成功。`)
                        // 刷新界面
                        this.getList(true)
                    }

                } catch (e) {
                    console.log(e, 'e')
                }
                row.loading = false
                row.isEdit = false
            }

            if (type === 'cancel') {
                this.getEditList.forEach((value) => {
                    row[`${value.prop}${this.editKey}`] = row[value.prop]
                })
                row.isEdit = false
            }
        },

        async buildRockList(row) {
            try {
                return this.rangeList.map(v => {
                    const tar = row.rangeValues.find(item => item.rangeName === v.rangeName)
                    return {
                        rangeName: v.rangeName,
                        proportion: tar?.proportion,
                        // sort: v.sort
                    }
                })
            } catch (e) {
                console.log(e, 'e')
                return []
            }
        },

        getList(clear = false) {
            if (clear) {
                this.$refs[this.pageRef].clearSelect(true)
            } else {
                this.$refs[this.pageRef].getList()
            }
        },
        getSelectRows() {
            return this.$refs[this.pageRef].getSelectRows()
        },
        setColumns(data) {
            return this.$refs[this.pageRef].setColumns(data)
        }
    }
}
</script>

<style lang="scss" scoped>

.noPaddingInput {
  padding: 0 5px;

  ::v-deep {
    .el-input__inner {
      padding: 0px 6px !important;
      text-align: center;
      border: 1px solid #256DDF;
    }
  }
}


.tag-left-right {
  font-size: 10px;
  position: absolute;
  right: -35px;
  text-align: center;
  top: -8px;
  width: 76px;
  line-height: 42px;
  height: 28px;
  z-index: 999;
  color: #fff;
  background: #ff9639;
  transform: rotate(50deg);
}


::v-deep {
  .el-divider--vertical {
    margin: 0 5px !important;
  }

  .el-table {
    .el-table__header {
      .cell {
        padding-right: 2px;
        padding-left: 2px;
      }
    }

    .el-table__row {
      .cell {
        min-height: 28px;
        line-height: 28px;
        padding-right: 0;
        padding-left: 0;
      }
    }
  }
}


</style>
