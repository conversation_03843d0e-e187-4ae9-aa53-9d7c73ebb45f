<template>
    <div class="container">
        <SnModal v-model="_value" :cancel="cancel" :fullscreen.sync="fullscreen" :ok="ok" :showFooter="!isViewStatus"
                 :title="title"
                 class="modal" v-bind="$attrs" width="70%">
            <el-form ref="form" :disabled="isViewStatus" :model="form" :rules="rules" labelPosition="top"
                     labelWidth="100px"
                     style="height: 100%">
                <SnFormCard title="基本信息">
                    <VhTable ref="excelTable" :afterChange="afterChange"
                             :list="form.data"
                             class="table-content"
                             @init="setTableSetting">
                        <VHColumn data="date" title="更新日期" formRequire type="text" width="100">
                            <template #editors="slotProps">
                                <VHDate :slotProps="slotProps" format="yyyy-MM-dd" valueFormat="yyyy-MM-dd"/>
                            </template>
                        </VHColumn>

                        <VHColumn data="coalCategoryName" title="煤种名称" type="autocomplete" width="150">
                            <template #default="{ value }">
                                <span class="t-ellipsis">{{ value }}</span>
                            </template>
                        </VHColumn>

                        <VHColumn data="coalName" title="品名" formRequire type="autocomplete" width="100">
                            <template #default="{ value }">
                                <span class="t-ellipsis">{{ value }}</span>
                            </template>
                        </VHColumn>

                        <VHColumn data="coalSource" title="来源" formRequire type="text" width="100">
                            <template #default="{ value }">
                                <SnDictTag :value="value" class="t-ellipsis" type="b_coal_source_type"></SnDictTag>
                            </template>
                            <template #editors="slotProps">
                                <VHSelect :list="coalSourceList" :props="{
                                        value: 'value',
                                        label: 'name',
                                        key: 'value'
                                    }" :slotProps="slotProps"/>
                            </template>
                        </VHColumn>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.cleanMt" formRequire data="stock" title="库存"
                                  type="numeric"
                                  width="80"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.cleanMt" formRequire data="mt" title="水分Mt%"
                                  type="numeric"
                                  width="80"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.cleanAd" formRequire data="ad" title="灰分Ad%"
                                  type="numeric"
                                  width="80"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.cleanStd" formRequire data="std" title="硫分St,d%"
                                  type="numeric"
                                  width="80"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.cleanVdaf" formRequire data="vdaf"
                                  title="挥发分Vdaf%"
                                  type="numeric" width="100"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.procG" data="g" formRequire title="粘结G"
                                  type="numeric"
                                  width="80"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.procY" data="y" title="Y/mm"
                                  type="numeric"
                                  width="80" />
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.procY" data="x" formRequire title="X/mm"
                                  type="numeric"
                                  width="80"/>
                        <VHColumn data="location" title="产地" type="text" width="100"/>


                    </VhTable>
                </SnFormCard>
            </el-form>
        </SnModal>
    </div>
</template>

<script>
import TipModal from '@/utils/modal'
import {deepClone, FetchData} from '@/utils'
import {VhTable, VHColumn, VHDate, VHSelect} from '@/components/ExcelTable'
import {getIndexNumDecimalPlaces, getIndexNumPattern, INDEX_NUM_FORMAT} from '@/const'
import {getDate} from '@/utils/dateUtils'
import CalcUtils from '@/utils/calcUtils'
import {getCoalNameList, getRangeListApi} from "@/api/coal";
import {getDictData, getDictDatas} from '@/utils/dict'

export default {
    inheritAttrs: false,
    components: {
        VHDate,
        VHSelect,
        VhTable,
        VHColumn
    },
    data() {
        return {
            form: {},
            rules: {},
            fullscreen: false,
            rangeList: [],
            coalSourceList: getDictDatas('b_coal_source_type')
        }
    },
    computed: {
        getRangeCols() {
            return this.getFormatRangeList(this.rangeList)
        },
        INDEX_NUM_FORMAT() {
            return INDEX_NUM_FORMAT
        },
        getOptMap() {
            return {
                create: '新增',
                update: '修改',
                view: '查看'
            }
        },
        _value: {
            set(val) {
                this.$emit('input', val)
            },
            get() {
                return this.value
            }
        },
        title() {
            return '合并数据'
        },
        isViewStatus() {
            return this.optName === 'view'
        },
        isUpdateStatus() {
            return this.optName === 'update'
        },
        isCreateStatus() {
            return this.optName === 'create'
        },

    },
    props: {
        useApi: {
            type: Boolean,
            default: false
        },
        getApi: {
            type: Function
        },
        optName: {
            type: String,
            require: true
        },
        value: {
            type: Boolean,
            require: true
        },
        record: {
            type: Object,
            default() {
                return {}
            }
        },
        model: {
            type: Object,
            default() {
                return {}
            }
        },
        categoryNamesList: {
            type: Array,
            default() {
                return []
            }
        },
        coalSource: {
            type: String,
        }
    },
    watch: {
        record: {
            async handler(value) {
                await this.$nextTick()
                if (this.isCreateStatus) {
                    this.form = {
                        ...value,
                        data: Array(15).fill().map(() => ({}))
                    }
                    return true
                }

                this.form = this.getFormData(value)
            },
            immediate: true
        }
    },
    async created() {
        // await this.getDataList()
    },
    methods: {
        getShowValue: CalcUtils.getShowValue,
        afterChange(changes, source, instance) {
            if (source === 'source') return
        },
        async setTableSetting() {
            const categoryNamesList = this.categoryNamesList

            const settingConfig = () => {
                if (!this.isCreateStatus) return {}
                return {
                    contextMenu: {
                        items: {
                            row_above: {name: '向上插入一行'},
                            row_below: {name: '向下插入一行'},
                            remove_row: {name: '删除行'},
                            clear_custom: {
                                name: '清空所有单元格数据',
                                callback() {
                                    this.clear()
                                }
                            }
                        }
                    },
                }
            }
            const instance = this.$refs.excelTable.getHotInstance()
            const _this = this
            const cols = this.$refs.excelTable.columns

            instance.updateSettings({
                ...settingConfig(),
                cells(row, col, prop) {
                    const curRow = _this.form.data ? _this.form.data[row] : {}
                    const cellProperties = {
                        className: 't-ellipsis',
                        readOnly: curRow?.readOnly
                    }

                    if (cols[col].data === 'coalCategoryName') {
                        cellProperties.source = categoryNamesList
                    }

                    return cellProperties
                }
            })
        },

        async getDataList() {
            try {
                const {data} = await getRangeListApi()
                this.rangeList = data
            } catch (e) {
                this.rangeList = []
            }
        },

        getFormatRangeList(list) {
            return list.map(v => {
                const dataKey = v.rangeName.replaceAll('.', 'dot')
                return {
                    ...v,
                    data: dataKey,
                    title: v.rangeName,
                }
            })
        },

        getFormData(form) {
            const {...rest} = form
            const data = rest.data.map((row) => {
                return {
                    ...row,
                    date: getDate(row.date),
                    readOnly: true
                }
            })

            const getAvgRow = (rows) => {
                const avg = [
                    {
                        key: 'mt',
                        decimalPlaces: getIndexNumDecimalPlaces('cleanMt'),
                    },
                    {
                        key: 'ad',
                        decimalPlaces: getIndexNumDecimalPlaces('cleanAd'),
                    },
                    {
                        key: 'std',
                        decimalPlaces: getIndexNumDecimalPlaces('cleanStd'),
                    },
                    {
                        key: 'vdaf',
                        decimalPlaces: getIndexNumDecimalPlaces('cleanVdaf'),
                    },
                    {
                        key: 'g',
                        decimalPlaces: getIndexNumDecimalPlaces('procG'),
                    },
                    {
                        key: 'y',
                        decimalPlaces: getIndexNumDecimalPlaces('procY'),
                    },
                    {
                        key: 'x',
                        decimalPlaces: getIndexNumDecimalPlaces('procX'),
                    }
                ]

                const res = avg.reduce((acc, cur) => {
                    return {
                        ...acc,
                        [cur.key]: CalcUtils.getShowValue(CalcUtils.calculateAverageDecimal(rows.map(v => v[cur.key])), cur.decimalPlaces)
                    }
                }, {
                    date: getDate(new Date()),
                })
                return res
            }


            return {
                ...rest,
                data: [...data, getAvgRow(data)]
            }
        },

        getSubmitForm() {
            const submitForm = deepClone(this.form)
            const {...rest} = submitForm
            const {getData, getColumns, getFormatEditData} = this.$refs.excelTable
            const data = getFormatEditData(getData(), getColumns())

            // 最后一条
            const avgRow = data.pop()

            return {...avgRow, rockList: []}
        },

        async cancel() {
            this.form = {}
            this.$refs['form'].resetFields()
            return false
        },

        async ok() {
            await this.$refs['form'].validate()
            const status = await this.$refs['excelTable'].validate()
            if (!status) return
            const resp = await this.model.mergeData(this.getSubmitForm())
            if (resp) {
                TipModal.msgSuccess(`${this.title}成功`)
                this.$emit('ok')
                return this.cancel()
            }

        }
    }
}
</script>

<style lang="scss" scoped>
.table-content {
  width: 100%;
  height: 100%;
}

::v-deep {
  .el-dialog__body {
    padding: 25px 25px;
    height: 500px;
    overflow: auto;
  }

}
</style>
