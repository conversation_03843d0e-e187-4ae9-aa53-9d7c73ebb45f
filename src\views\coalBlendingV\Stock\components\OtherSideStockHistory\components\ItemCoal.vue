<template>
    <el-dialog :append-to-body="true" :before-close="()=>handleClose('full')"
               :title="details.coalName || '更新'"
               :visible.sync="visible" class="add"
               top="1vh" width="85%">
        <div class="coal" v-if="visible">
            <el-form ref="entityForm" :model="entityForm" label-width="80px">
                <indicators-card v-if="entityForm.basicinformation">
                    <template #title>
                        <div class="title-content">
                            <span>基础信息</span>
                        </div>
                    </template>

                    <el-col>
                        <EditTable ref="editTable"
                                   v-model="entityForm.basicinformation" :columns="getColumnsInfo"
                                   :rowHeaders="true"
                                   @inited="handleEditTableInitInfo">
                        </EditTable>
                    </el-col>
                </indicators-card>


                <indicators-card title="指标" v-if="entityForm.indexlist">
                    <el-col>
                        <EditTable ref="editTable2" v-model="entityForm.indexlist" :columns="getColumns"
                                   :rowHeaders="true"
                                   @inited="handleEditTableInit">
                        </EditTable>
                    </el-col>

                </indicators-card>
                <indicators-card
                        :subtitle="`(当前反射率之和为${entityForm.rate>0?entityForm.rate:'空'},反射率要求在99.5到100.5之间)`"
                        title="反射率分布表">
                    <el-row :gutter="80" type="flex">
                        <el-col>
                            <EditTable ref="editTable1" v-model="editData1" :colHeaders="true" :columns="getColumns1"
                                       @inited="handleEditTableInit1"></EditTable>
                        </el-col>
                    </el-row>
                </indicators-card>

                <indicators-card title="图表信息">
                    <div class="reflectance-chart-container">
                        <!-- 反射率分布柱状图 -->
                        <div v-if="!chartLoaded" style="text-align: center; padding: 20px; background-color: #f8f8f8; margin-bottom: 10px;">
                            <i class="el-icon-loading" style="font-size: 24px; margin-right: 10px;"></i>
                            <span>图表加载中...</span>
                        </div>
                        <div ref="reflectanceChart" style="width: 100%; height: 300px; border: 1px solid #ddd;"></div>
                        
                        <!-- 煤种分布表格 -->
                        <el-table :data="coalTypeDistribution" :show-header="false" border class="chart-table">
                            <el-table-column prop="range" label="范围" width="110" />
                            <el-table-column v-for="(item, index) in coalTypeHeaders" :key="index" :prop="item.prop" :label="item.label" />
                        </el-table>
                    </div>
                </indicators-card>

                <indicators-card :title="details.coalName || '价格趋势图'">
                    <div class="price-trend-section">
                        <sn-date-ranger v-model="dateRange" style="width: 250px;margin-bottom: 10px;" @change="getByPrice()"></sn-date-ranger>
                        <div ref="chart" style="width: 100%;height: 300px;border: 1px solid #CCCCCC"></div>
                    </div>
                </indicators-card>

                <indicators-card title="备注">
                    <el-form-item class="textarea" prop="remarks">
                        <el-input v-model="entityForm.remarks" :rows="6" placeholder="请填写备注信息~" type="textarea">
                        </el-input>
                    </el-form-item>
                </indicators-card>

                <indicators-card title="附件信息">
                    <el-row :gutter="80" type="flex">
                        <el-col>
                            <el-form-item label="">
                                <el-collapse v-model="collapse">
                                    <el-collapse-item name="1" title="上传附件">
                                        <div class="upload">
                                            <div class="upload-list">
                                                <div v-for="(item,index) in uploadList" :key="index"
                                                     class="up-load-warp">
                                                    <i class="el-icon-close icon"
                                                       @click="handleRemoveUpload(index)"></i>
                                                    <span href="#" @click="handleViewUrl(item.uri)"
                                                          :underline=" false"
                                                          class="up-load-warp-link">{{ item.display }}
                                                    </span>
                                                </div>
                                                <upload-attachment ref="upload" :limitNum="limitNum" :size="3"
                                                                   :uploadData="uploadData"
                                                                   accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg"
                                                                   class="upload-attachment"
                                                                   listType="text"
                                                                   source="coalSample"
                                                                   @deleteNotify="handleUploadDelete"
                                                                   @successNotify="handleUploadSuccess"/>
                                            </div>
                                        </div>

                                    </el-collapse-item>
                                </el-collapse>

                            </el-form-item>
                        </el-col>
                    </el-row>
                </indicators-card>
            </el-form>
        </div>
        <div v-if="dialogStatus!=='watch'" slot="footer" class="dialog-footer">
            <el-button class="dialog-footer-btns" @click="handleCloseV">
                取消
            </el-button>
            <el-button :loading="entityFormLoading" class="dialog-footer-btns" type="primary"
                       @click="makeSure('t')">保存
            </el-button>
        </div>

        <ElImageViewer v-if="showPreview" :url-list="urlList" :on-close="()=>{showPreview=false}"/>
    </el-dialog>
</template>

<script>
import Func from '@/utils/func'
import {dateFormat} from '@/filters'
import NewColumnChart from '@/components/Chart/NewColumnChart'
import {IndicatorsCard, IndicatorsTable} from '@/components/Indicators/index'
import {getCoalById, coalAshList, isCheckCoal, saveCoal, getRangeListApi} from '@/api/coal'
import {deepClone} from '@/utils/index'
import EditTable from '@/components/EditTable/index.vue'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import SnDateRanger from '@/components/Common/SnDateRanger/index.vue'
import UploadAttachment from '@/components/UploadAttachment/index.vue'
import echarts from 'echarts'
import dayjs from 'dayjs'
import * as math from 'mathjs'

const entityForm = () => {
    return {
        id: '',
        userId: '',
        name: '',
        batchCode: dateFormat(new Date(), 'yyyymmdd'),
        type: '',
        province: '',
        factoryPrice: '',
        transitFee: '',
        roadCost: '',
        arrivePrice: '',
        arriveFactory: 'JT',
        mineDepth: '',
        rawMt: '',
        rawAd: '',
        rawPointFive: '',
        rawOnePointFour: '',
        rawAdIn: '',
        rawStd: '',
        rawVdaf: '',
        rawG: '',
        cleanVdaf: '',
        cleanVd: '',
        cleanAd: '',
        cleanStd: '',
        cleanMt: '',
        cleanP: '',
        cleanMci: '',
        procG: '',
        procY: '',
        procX: '',
        procMf: '',
        procTp: '',
        procTmax: '',
        procTk: '',
        procCrc: '',
        procA: '',
        procB: '',
        macR0: '',
        macS: '',
        macV: '',
        macI: '',
        macE: '',
        comSiO2: '',
        comAl2O3: '',
        comFe2O3: '',
        comCaO: '',
        comMgO: '',
        comNa2O: '',
        comK2O: '',
        comTiO2: '',
        comP2O5: '',
        comSO3: '',
        cfeCp: '',
        cfeCe: '',
        qualScon: '',
        qualPcon: '',
        qualM40: '',
        qualM10: '',
        qualCsr: '',
        qualCri: '',
        qualTestCond: '',
        crGk: '',
        crBk: '',
        crTk: '',
        city: '',
        dataType: '',
        createBy: '',
        createDate: '',
        updateBy: '',
        updateDate: '',
        remarks: '',
        ext: '',
        location: '',
        area: '',
        longitude: '',
        latitude: '',
        isFavorite: '-',
        rawPrice: '',
        coalTypeProportionList: [],
        indexlist: [],
        basicinformation: [],
        allListRange: []
    }
}

export default {
    name: 'coal',
    components: {
        EditTable,
        IndicatorsCard,
        IndicatorsTable,
        NewColumnChart,
        ElImageViewer,
        SnDateRanger,
        UploadAttachment
    },
    data() {
        return {
            entityForm: entityForm(),
            entityFormLoading: false,
            entityFormLoadingV: false,
            isChangeCoal: false,
            type: '',
            // visible: false,
            editData1: [
                ...Array(10)
                    .fill(null)
                    .map(() => ({}))
            ],
            collapse: '1',
            limitNum: 1000,
            uploadList: [], // 用于展示
            uploadData: {refId: '', refType: 'CoalSample'},
            showPreview: false,
            urlList: [],
            priceTrendChart: null,
            priceTrendData: {
                area: '',
                cleanAd: '',
                cleanMt: '',
                cleanStd: '',
                cleanVdaf: '',
                coalBlendingCost: '',
                coalCategoryName: '',
                coalName: '',
                date: '',
                macR0: '',
                macS: '',
                priceJson: '',
                procG: '',
                procX: '',
                procY: '',
                csr: ''
            },
            priceTrendQuery: {
                id: '',
                beginDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss'),
                endDate: dayjs().format('YYYY-MM-DD HH:mm:ss')
            },
            // 添加反射率区间和煤种分布数据
            coalTypeDistribution: [
                {
                    range: '<0.5',
                    brownCoal: '', // 褐煤
                    longFlame: '', // 长焰
                    gasCoal: '', // 气煤
                    thirdCokingCoal: '', // 1/3焦煤
                    fatCoal: '', // 肥煤 
                    cokingCoal: '91.63', // 焦煤
                    leanCoal: '8.37', // 瘦煤
                    meagerLeanCoal: '', // 贫瘦煤
                    meagerCoal: '', // 贫煤
                    smokelessCoal: '' // 无烟煤
                }
            ],
            coalTypeHeaders: [
                { label: '<0.5', prop: 'brownCoal' },
                { label: '0.5-0.6', prop: 'longFlame' },
                { label: '0.6-0.75', prop: 'gasCoal' },
                { label: '0.75-0.9', prop: 'thirdCokingCoal' },
                { label: '0.9-1.15', prop: 'fatCoal' },
                { label: '1.15-1.55', prop: 'cokingCoal' },
                { label: '1.55-1.7', prop: 'leanCoal' },
                { label: '1.7-1.9', prop: 'meagerLeanCoal' },
                { label: '1.9-2.35', prop: 'meagerCoal' },
                { label: '>2.35', prop: 'smokelessCoal' }
            ],
            reflectanceChartInstance: null,
            reflectanceData: [
                {name: '0.25-0.30', value: 0},
                {name: '0.45-0.50', value: 0},
                {name: '0.65-0.70', value: 0},
                {name: '0.85-0.90', value: 0},
                {name: '1.05-1.10', value: 0},
                {name: '1.25-1.30', value: 7.72},
                {name: '1.45-1.50', value: 12.47},
                {name: '1.65-1.70', value: 2.08},
                {name: '1.85-1.90', value: 0},
                {name: '2.05-2.10', value: 0},
                {name: '2.25-2.30', value: 0},
                {name: '2.45-2.50', value: 0},
                {name: '0.30-0.35', value: 0},
                {name: '0.50-0.55', value: 0},
                {name: '0.70-0.75', value: 0},
                {name: '0.90-0.95', value: 0},
                {name: '1.10-1.15', value: 0},
                {name: '1.30-1.35', value: 16.80},
                {name: '1.50-1.55', value: 7.19},
                {name: '1.70-1.75', value: 0},
                {name: '1.90-1.95', value: 0},
                {name: '2.10-2.15', value: 0},
                {name: '2.30-2.35', value: 0},
                {name: '2.50-2.55', value: 0},
                {name: '0.35-0.40', value: 0},
                {name: '0.55-0.60', value: 0},
                {name: '0.75-0.80', value: 0},
                {name: '0.95-1.00', value: 0},
                {name: '1.15-1.20', value: 1.92},
                {name: '1.35-1.40', value: 26.25},
                {name: '1.55-1.60', value: 3.38},
                {name: '1.75-1.80', value: 0},
                {name: '1.95-2.00', value: 0},
                {name: '2.15-2.20', value: 0},
                {name: '2.35-2.40', value: 0},
                {name: '2.55-2.60', value: 0},
                {name: '0.40-0.45', value: 0},
                {name: '0.60-0.65', value: 0},
                {name: '0.80-0.85', value: 0},
                {name: '1.00-1.05', value: 0},
                {name: '1.20-1.25', value: 0},
                {name: '1.40-1.45', value: 19.28},
                {name: '1.60-1.65', value: 2.91},
                {name: '1.80-1.85', value: 0},
                {name: '2.00-2.05', value: 0},
                {name: '2.20-2.25', value: 0},
                {name: '2.40-2.45', value: 0}
            ],
            chartLoaded: false
        }
    },
    computed: {
        visible: {
            get() {
                return this.addVisible
            },
            set(val) {
                this.$emit('update:addVisible', val);
            }
        },
        region: {
            set(val) {
                this.entityForm = Object.assign(this.entityForm, {
                    province: val[0],
                    city: val[1],
                    area: val[2]
                })
            },
            get() {
                return [this.entityForm.province, this.entityForm.city, this.entityForm.area]
            }
        },
        getColumnsInfo() {
            const dialogStatus = this.dialogStatus
            const readOnly = this.isReadonly
            const watchArr =
                dialogStatus === 'watch'
                    ? []
                    : []
            return [
                ...watchArr,
                {
                    title: '名称',
                    width: 60,
                    data: 'coalName',
                    type: 'text',
                    readOnly
                },

                {
                    title: '煤种',
                    width: 100,
                    data: 'coalCategoryName',
                    formRequire: true, // 自定义字段
                    visibleRows: 15,
                    type: 'dropdown',
                    source: this.categoryNamesList,
                    readOnly
                },

                {
                    title: '产地',
                    width: 100,
                    data: 'location',
                    readOnly
                },

                {
                    title: '配煤成本',
                    width: 60,
                    data: 'coalBlendingCost',
                    type: 'numeric',
                    readOnly,
                    numericFormat: {
                        pattern: '0.00'
                    }
                }
            ]
        },

        getColumns() {
            const readOnly = this.isReadonly
            const dialogStatus = this.dialogStatus
            const len = this.entityForm.indexlist.length
            const watchArr =
                dialogStatus === 'watch'
                    ? []
                    : []
            return [
                ...watchArr,
                {
                    title: 'Ad',
                    width: 60,
                    data: 'ad',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.00'
                    },
                    readOnly
                },
                {
                    title: 'St,d',
                    width: 60,
                    data: 'std',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.00'
                    },
                    readOnly
                },

                {
                    title: 'Vdaf',
                    width: 60,
                    data: 'vdaf',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.00'
                    },
                    readOnly
                },
                {
                    title: 'G',
                    width: 60,
                    data: 'g',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0'
                    },
                    readOnly
                },
                {
                    title: 'Y',
                    width: 60,
                    data: 'y',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.0'
                    },
                    readOnly
                },
                {
                    title: 'X',
                    width: 60,
                    data: 'x',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.0'
                    },
                    readOnly
                },
                {
                    title: 'Mt',
                    width: 60,
                    data: 'mt',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.0'
                    },
                    readOnly
                },
                {
                    title: 'CSR',
                    width: 60,
                    data: 'csr',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.0'
                    },
                    readOnly
                },
                {
                    title: '反射率',
                    width: 60,
                    data: 'macR0',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.000'
                    },
                    readOnly
                },
                {
                    title: '标准差',
                    width: 60,
                    data: 'macS',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.000'
                    },
                    readOnly
                },
            ]
        },

        getColumns1() {
            const createNames = (index) => {
                const res = {
                    0: 'Rran范围',
                    1: '频率%',
                    2: 'Rran范围',
                    3: '频率%',
                    4: 'Rran范围',
                    5: '频率%',
                    6: 'Rran范围',
                    7: '频率%',
                    8: 'Rran范围',
                    9: '频率%'
                }
                return res[index] || 'default'
            }
            return [
                ...Array(10)
                    .fill(null)
                    .map((val, index, arr) => {
                        // index 是偶数
                        const dataProp = index + 1
                        if (index % 2 == 1) {
                            return {
                                title: createNames(index),
                                width: 80,
                                data: dataProp,
                                type: 'numeric',
                                numericFormat: {
                                    pattern: '0.00'
                                }
                            }
                        }
                        return {
                            title: createNames(index),
                            width: 90,
                            data: dataProp,
                            // 禁用列
                            readOnly: true
                        }
                    })
            ]
        },
        dateRange: {
            get() {
                return [this.priceTrendQuery.beginDate, this.priceTrendQuery.endDate]
            },
            set(value) {
                this.priceTrendQuery.beginDate = value[0]
                this.priceTrendQuery.endDate = value[1]
            }
        },
        setFormFields() {
            return ['indexlist', 'basicinformation', 'RranFrequency']
        }
    },
    props: {
        isReadonly: {
            type: Boolean,
            default: false
        },
        categoryNamesList: {
            type: Array,
            default() {
                return []
            }
        },

        model: {
            type: Object,
            default() {
                return {}
            }
        },
        details: {
            type: Object,
            default() {
                return {}
            }
        },
        addVisible: {
            type: Boolean,
            default: false
        },
        isSourceWash: {
            type: Boolean,
            default: false
        },
        dialogStatus: {
            type: String,
            default: 'update'
        }
    },
    methods: {
        handleClose(type) {
            // 关闭弹窗
            this.$emit('closeVisible');
        },
        handleCloseV() {
            this.addVisible = false
            // 初始化表单数据
            this.entityForm = {
                indexlist: [],
                basicinformation: [],
                RranFrequency: [],
            }
        },
        async handleEditTableInitInfo(instance) {
            await this.$nextTick()
            this.entityForm.basicinformation = this.entityForm.basicinformation.map(v => {
                return {
                    ...v,
                }
            })

            const getSetting = () => {
                if (this.isReadonly) {
                    return {
                        readOnly: true,
                        contextMenu: undefined,
                        data: this.entityForm.basicinformation,
                        rowHeaders: false
                    }
                }
                return {
                    contextMenu: {
                        items: {}
                    },
                    data: this.entityForm.basicinformation
                }
            }

            instance.updateSettings({
                columns: this.getColumnsInfo,
                ...getSetting(),
                async cells(row, col) {
                }
            })
        },
        handleEditTableInit(instance) {
            // Implementation of handleEditTableInit method
        },
        handleEditTableInit1(instance) {
            // Implementation of handleEditTableInit1 method
        },
        handleRemoveUpload(index) {
            const {id} = this.uploadList[index]
            this.$refs.upload.attachmentDelete({id, index})
        },
        handleViewUrl(url) {
            this.showPreview = true
            this.urlList = [url]
        },
        handleUploadDelete(file) {
            // 从entityForm的attachmentList中移除
            const index = this.entityForm.attachmentList.findIndex(item => item.id === file.id);
            if (index > -1) {
                this.entityForm.attachmentList.splice(index, 1);
            }
            
            // 从展示列表中移除
            const displayIndex = this.uploadList.findIndex(item => item.id === file.id);
            if (displayIndex > -1) {
                this.uploadList.splice(displayIndex, 1);
            }
        },
        handleUploadSuccess(res) {
            // 添加到展示列表
            this.uploadList = [...this.uploadList, {
                ...res,
                display: res.fileName || '附件'
            }];
            
            // 添加到entityForm的attachmentList
            this.entityForm.attachmentList.push({id: res.id});
        },
        getByPrice() {
            this.getPriceHistory();
        },
        makeSure(type) {
            if (this.entityFormLoading) return;
            
            this.$refs.entityForm.validate(async (valid) => {
                if (valid) {
                    try {
                        this.entityFormLoading = true;
                        
                        // 构建保存数据
                        const saveData = {
                            ...this.entityForm,
                            id: this.details.id
                        };
                        
                        // 调用保存API
                        const res = await saveCoal(saveData);
                        
                        if (res.success) {
                            this.$message.success('保存成功');
                            this.handleCloseV();
                            this.$emit('closeVisible');
                        } else {
                            this.$message.error(res.message || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存失败:', error);
                        this.$message.error('保存失败');
                    } finally {
                        this.entityFormLoading = false;
                    }
                } else {
                    this.$message.warning('请填写必填项');
                    return false;
                }
            });
        },
        // 初始化反射率分布图表
        initReflectanceChart() {
            console.log('开始初始化反射率图表');
            
            // 检查DOM元素是否存在
            if (!this.$refs.reflectanceChart) {
                console.error('图表DOM元素不存在');
                return;
            }
            
            try {
                // 如果图表实例已存在，先销毁
                if (this.reflectanceChartInstance) {
                    this.reflectanceChartInstance.dispose();
                }
                
                // 创建图表实例
                this.reflectanceChartInstance = echarts.init(this.$refs.reflectanceChart);
                console.log('图表实例已创建');
                
                // 处理数据
                const categories = [];
                const values = [];
                
                console.log('反射率数据:', this.reflectanceData);
                
                // 确保有数据可用
                if (!this.reflectanceData || this.reflectanceData.length === 0) {
                    console.warn('没有反射率数据可用，使用默认示例数据');
                    // 使用图片中的示例数据
                    this.reflectanceData = [
                        {name: '1.25-1.30', value: 7.72},
                        {name: '1.30-1.35', value: 16.80},
                        {name: '1.35-1.40', value: 26.25},
                        {name: '1.40-1.45', value: 19.28},
                        {name: '1.45-1.50', value: 12.47},
                        {name: '1.50-1.55', value: 7.19},
                        {name: '1.55-1.60', value: 3.38},
                        {name: '1.60-1.65', value: 2.91},
                        {name: '1.65-1.70', value: 2.08},
                        {name: '1.15-1.20', value: 1.92}
                    ];
                }
                
                // 按名称对数据排序，确保X轴的顺序正确
                const sortedData = [...this.reflectanceData].sort((a, b) => {
                    // 从名称中提取第一个数值进行排序
                    const numA = parseFloat(a.name.split('-')[0]);
                    const numB = parseFloat(b.name.split('-')[0]);
                    return numA - numB;
                });
                
                // 获取类别和值
                sortedData.forEach(item => {
                    if (item.value > 0) { // 只显示有值的数据
                        categories.push(item.name);
                        values.push(item.value);
                    }
                });
                
                console.log('图表类别:', categories);
                console.log('图表数值:', values);
                
                // 设置图表配置
                const option = {
                    tooltip: {
                        trigger: 'axis',
                        formatter: '{b}: {c}%'
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '10%',
                        top: '10%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: categories,
                        axisLabel: {
                            interval: 0,
                            rotate: 0
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    },
                    series: [
                        {
                            name: '频率',
                            type: 'bar',
                            data: values,
                            barWidth: '50%',
                            label: {
                                show: true,
                                position: 'top',
                                formatter: '{c}'
                            },
                            itemStyle: {
                                color: function(params) {
                                    // 设置柱状图颜色
                                    return '#F8C749';
                                }
                            }
                        }
                    ]
                };
                
                console.log('图表配置已准备');
                
                // 应用配置
                this.reflectanceChartInstance.setOption(option);
                console.log('图表配置已应用');
                
                // 更新煤种分布表
                this.updateCoalTypeDistribution(sortedData, 
                    sortedData.reduce((sum, item) => sum + item.value, 0).toFixed(1));
                
                // 添加窗口大小改变监听器
                window.addEventListener('resize', this.resizeReflectanceChart);
            } catch (error) {
                console.error('初始化反射率图表失败:', error);
            } finally {
                this.chartLoaded = true;
            }
        },
        
        // 当窗口大小改变时，调整图表大小
        resizeReflectanceChart() {
            if (this.reflectanceChartInstance) {
                this.reflectanceChartInstance.resize();
            }
        },
        // 处理反射率分布数据
        processReflectanceData(details) {
            console.log('开始处理反射率数据:', details);
            if (!details) {
                console.error('没有煤炭详情数据');
                return;
            }
            
            try {
                // 解析反射率数据
                const proportionValues = details.proportionContent ? JSON.parse(details.proportionContent) : [];
                const rangeNameValues = details.rangeContent ? JSON.parse(details.rangeContent) : [];
                
                console.log('解析的反射率数据:');
                console.log('- 比例值:', proportionValues);
                console.log('- 范围名称:', rangeNameValues);
                
                if (proportionValues.length > 0 && rangeNameValues.length > 0) {
                    // 映射范围名称和数值
                    const newReflectanceData = [];
                    rangeNameValues.forEach((rangeName, index) => {
                        if (index < proportionValues.length) {
                            newReflectanceData.push({
                                name: rangeName,
                                value: parseFloat(proportionValues[index] || 0)
                            });
                        }
                    });
                    
                    console.log('生成的反射率数据:', newReflectanceData);
                    
                    // 更新图表数据
                    if (newReflectanceData.length > 0) {
                        this.reflectanceData = newReflectanceData;
                        
                        // 计算总和以便检查合规性 (99.5-100.5)
                        const total = newReflectanceData.reduce((sum, item) => sum + item.value, 0).toFixed(1);
                        console.log('反射率总和:', total);
                        
                        // 更新煤种分布数据
                        this.updateCoalTypeDistribution(newReflectanceData, total);
                        
                        // 重新初始化图表
                        this.$nextTick(() => {
                            console.log('准备重新初始化图表');
                            this.initReflectanceChart();
                        });
                    } else {
                        console.warn('处理后的反射率数据为空');
                    }
                } else {
                    console.warn('原始反射率数据为空');
                    // 使用默认数据初始化图表
                    this.initReflectanceChart();
                }
            } catch (error) {
                console.error('处理反射率数据失败:', error);
                // 发生错误时仍然尝试用默认数据初始化图表
                this.initReflectanceChart();
            }
        },
        
        // 更新煤种分布数据
        updateCoalTypeDistribution(reflectanceData, total) {
            // 按反射率区间计算煤种分布
            const distribution = {
                brownCoal: 0,      // <0.5
                longFlame: 0,      // 0.5-0.6
                gasCoal: 0,        // 0.6-0.75
                thirdCokingCoal: 0, // 0.75-0.9
                fatCoal: 0,        // 0.9-1.15
                cokingCoal: 0,     // 1.15-1.55
                leanCoal: 0,       // 1.55-1.7
                meagerLeanCoal: 0, // 1.7-1.9
                meagerCoal: 0,     // 1.9-2.35
                smokelessCoal: 0   // >2.35
            };
            
            // 遍历反射率数据，按区间累加
            reflectanceData.forEach(item => {
                const range = parseFloat(item.name.split('-')[0]);
                const value = item.value;
                
                if (range < 0.5) distribution.brownCoal += value;
                else if (range < 0.6) distribution.longFlame += value;
                else if (range < 0.75) distribution.gasCoal += value;
                else if (range < 0.9) distribution.thirdCokingCoal += value;
                else if (range < 1.15) distribution.fatCoal += value;
                else if (range < 1.55) distribution.cokingCoal += value;
                else if (range < 1.7) distribution.leanCoal += value;
                else if (range < 1.9) distribution.meagerLeanCoal += value;
                else if (range < 2.35) distribution.meagerCoal += value;
                else distribution.smokelessCoal += value;
            });
            
            // 更新表格数据
            this.coalTypeDistribution = [{
                range: "反射率分布",
                brownCoal: distribution.brownCoal > 0 ? distribution.brownCoal.toFixed(2) : '',
                longFlame: distribution.longFlame > 0 ? distribution.longFlame.toFixed(2) : '',
                gasCoal: distribution.gasCoal > 0 ? distribution.gasCoal.toFixed(2) : '',
                thirdCokingCoal: distribution.thirdCokingCoal > 0 ? distribution.thirdCokingCoal.toFixed(2) : '',
                fatCoal: distribution.fatCoal > 0 ? distribution.fatCoal.toFixed(2) : '',
                cokingCoal: distribution.cokingCoal > 0 ? distribution.cokingCoal.toFixed(2) : '',
                leanCoal: distribution.leanCoal > 0 ? distribution.leanCoal.toFixed(2) : '',
                meagerLeanCoal: distribution.meagerLeanCoal > 0 ? distribution.meagerLeanCoal.toFixed(2) : '',
                meagerCoal: distribution.meagerCoal > 0 ? distribution.meagerCoal.toFixed(2) : '',
                smokelessCoal: distribution.smokelessCoal > 0 ? distribution.smokelessCoal.toFixed(2) : ''
            }];
        },
        
        // 获取煤炭详情数据
        async fetchDetailInfo() {
            if (!this.details || !this.details.id) return;

            try {
                this.entityFormLoading = true;
                // 使用model来获取煤炭详情
                const { data } = await this.model.getZsmyCoalById(this.details.id);
                
                if (data) {
                    // 将基础信息设置到表格中
                    this.entityForm.basicinformation = [{
                        coalName: data.coalName,
                        coalCategoryName: data.coalCategoryName,
                        location: data.location || data.area,
                        coalBlendingCost: data.coalBlendingCost
                    }];
                    
                    // 设置指标信息
                    this.entityForm.indexlist = [{
                        ad: data.cleanAd,
                        std: data.cleanStd,
                        vdaf: data.cleanVdaf,
                        g: data.procG,
                        y: data.procY,
                        x: data.procX,
                        mt: data.cleanMt,
                        csr: data.cokeIndexCsr,
                        macR0: data.macR0,
                        macS: data.macS
                    }];
                    
                    // 设置备注
                    this.entityForm.remarks = data.remarks;
                    
                    // 处理附件列表
                    if (data.attachmentList && Array.isArray(data.attachmentList)) {
                        this.uploadList = data.attachmentList.map(item => ({
                            ...item,
                            display: item.fileName || '附件'
                        }));
                        this.entityForm.attachmentList = data.attachmentList;
                    }
                    
                    // 处理反射率数据
                    if (data.proportionContent && data.rangeContent) {
                        this.processReflectanceData(data);
                    }
                    
                    // 设置反射率总和
                    this.entityForm.rate = data.rate || 0;
                }
            } catch (error) {
                console.error('获取煤炭详情失败:', error);
            } finally {
                this.entityFormLoading = false;
            }
        },
        
        // 获取价格历史趋势数据
        async getPriceHistory() {
            try {
                if (!this.priceTrendQuery.id) return;
                
                // 初始化图表实例
                this.$nextTick(() => {
                    if (!this.priceTrendChart && this.$refs.chart) {
                        this.priceTrendChart = echarts.init(this.$refs.chart);
                        window.addEventListener('resize', this.resizePriceTrendChart);
                    }
                });
                
                // 获取价格历史数据
                const res = await this.model.getByPrice(this.priceTrendQuery);
                
                if (res && res.data) {
                    this.priceTrendData = res.data;
                    
                    // 解析价格JSON数据
                    if (this.priceTrendData.priceJson) {
                        if (typeof this.priceTrendData.priceJson === 'string') {
                            this.priceTrendData.priceJson = JSON.parse(this.priceTrendData.priceJson);
                        }
                        
                        // 设置图表配置
                        const dates = this.priceTrendData.priceJson.map(item => item.date).reverse();
                        const prices = this.priceTrendData.priceJson.map(item => item.coalBlendingCost).reverse();
                        
                        this.priceTrendChart.setOption({
                            title: {
                                left: 20,
                                top: 10,
                                text: this.priceTrendData.coalName
                            },
                            xAxis: {
                                type: 'category',
                                data: dates,
                                axisTick: {
                                    show: false
                                },
                                axisLine: {
                                    show: true,
                                    width: 5,
                                    symbol: ['none', 'arrow'],
                                    symbolOffset: 30,
                                    lineStyle: {
                                        color: '#F5F5FF',
                                        type: 'solid',
                                        shadowOffsetX: 30,
                                        shadowColor: '#F5F5FF'
                                    }
                                },
                                axisLabel: {
                                    color: '#9A9A9A',
                                    formatter: value => value.substring(0, 10)
                                }
                            },
                            yAxis: {
                                type: 'value',
                                axisTick: {
                                    show: false
                                },
                                scale: true,
                                splitLine: {
                                    lineStyle: {
                                        color: '#F5F5FF'
                                    }
                                },
                                axisLine: {
                                    show: true,
                                    width: 5,
                                    symbol: ['none', 'arrow'],
                                    symbolOffset: 30,
                                    lineStyle: {
                                        color: '#F5F5FF',
                                        type: 'solid',
                                        shadowOffsetY: -30,
                                        shadowColor: '#F5F5FF'
                                    }
                                },
                                axisLabel: {
                                    showMaxLabel: false,
                                    color: '#9A9A9A'
                                }
                            },
                            tooltip: {
                                trigger: 'axis'
                            },
                            legend: {
                                type: 'plain',
                                data: ['价格'],
                                bottom: 0,
                                selected: {
                                    '价格': true
                                }
                            },
                            grid: {
                                left: '25',
                                right: '50',
                                top: '80',
                                bottom: '30',
                                containLabel: true
                            },
                            series: [
                                {
                                    data: prices,
                                    type: 'line',
                                    name: '价格',
                                    label: {
                                        show: true,
                                        color: '#595EC9'
                                    },
                                    showAllSymbol: false,
                                    lineStyle: {
                                        color: '#595EC9'
                                    },
                                    itemStyle: {
                                        color: '#595EC9'
                                    },
                                    areaStyle: {
                                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                            {
                                                offset: 0,
                                                color: '#F5F5FF'
                                            },
                                            {
                                                offset: 1,
                                                color: 'white'
                                            }
                                        ])
                                    }
                                }
                            ]
                        });
                    }
                }
            } catch (error) {
                console.error('获取价格历史数据失败:', error);
            }
        },
        
        // 价格趋势图大小调整
        resizePriceTrendChart() {
            if (this.priceTrendChart) {
                this.priceTrendChart.resize();
            }
        },
    },
    watch: {
        addVisible(v) {
            if (v) {
                console.log('弹窗显示，准备加载数据');
                // 清除上次渲染的数据
                const fields = this.setFormFields
                fields.forEach(f => {
                    this.entityForm[f] = []
                })
                this.entityForm.attachmentList = []
                this.edit = false
                this.chartLoaded = false

                this.fetchDetailInfo()
                this.uploadList = []
                if (this.details.id) {
                    this.priceTrendQuery.id = this.details.id || ''
                    this.getPriceHistory()
                }
                
                // 弹窗显示后立即初始化图表
                this.$nextTick(() => {
                    console.log('弹窗显示后初始化图表');
                    this.initReflectanceChart();
                });
            }
        },
        visible(val) {
            if (val) {
                // 初始化时设置图表数据
                this.$nextTick(() => {
                    this.initReflectanceChart();
                });
            }
        },
        details: {
            handler(newDetails) {
                if (newDetails && newDetails.id) {
                    // 处理反射率分布数据
                    this.processReflectanceData(newDetails);
                }
            },
            deep: true
        }
    },
    mounted() {
        // 确保图表在组件挂载后初始化
        this.$nextTick(() => {
            this.initReflectanceChart();
        });
    },
    beforeDestroy() {
        if (this.reflectanceChartInstance) {
            this.reflectanceChartInstance.dispose();
            window.removeEventListener('resize', this.resizeReflectanceChart);
        }
        if (this.priceTrendChart) {
            this.priceTrendChart.dispose();
            window.removeEventListener('resize', this.resizePriceTrendChart);
        }
    }
}
</script>

<style scoped lang="scss">
:deep(.el-dialog__body) {
  height: 65vh;
  overflow: auto;
}
.upload-demo,
.upload-attachment {
  :deep(.el-upload-dragger){
    width: 100%;
  }
}

.coal {
  height: calc(100vh - 180px);
  overflow-y: auto;
}

.chart-table {
  margin-top: 10px;
}

.upload-attachment {
  .header {
    margin-bottom: 10px;
  }
  .sn-upload {
    margin: 10px 0;
  }
}
.sn-upload {
  .header {
    margin-bottom: 10px;
  }
}

.indicators-th {
  width: 80px;
}

.indicators-td {
  width: 120px;
  text-align: center;
}

.title-content {
  display: flex;
  flex-wrap: wrap;
  span {
    padding-right: 10px;
    padding-top: 5px;
  }
}

.reflectance-chart-container {
  padding: 10px 0;
  width: 100%;
  
  .chart-table {
    margin-top: 20px;
    width: 100%;
    font-size: 12px;
    
    ::v-deep {
      .el-table th {
        background-color: #30BEB1;
        color: white;
        padding: 5px;
      }
      
      .el-table td {
        padding: 5px;
      }
      
      .el-table__row td:nth-child(odd) {
        background-color: #f2f2f2;
      }
    }
  }
}
</style> 