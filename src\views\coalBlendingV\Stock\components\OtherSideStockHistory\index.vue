<template>
  <div class="app-container">
    <!-- 添加独立的提示弹窗 -->
    <div v-if="showTipPopup" class="tip-popup-fixed" @click.stop="closeTipPopup">
      <div class="popup-content">
        <img src="/img/注意.png" alt="注意" class="tip-icon" />
        <span>点击查看价格趋势图、煤岩、灰成分等数据。</span>
        <img src="/img/组2.png" alt="关闭" class="close-icon" @click.stop="closeTipPopup" />
      </div>
    </div>
    
    <SnProTable :ref="pageRef" :actions="actions" :afterFetch="afterFetch" :beforeFetch="beforeFetch" :model="model"
                @row-click="handleClickRow" @row-class-name="rowClassName">
      <template #date="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row,$index}">
            <div style="position: relative;overflow: hidden;">
              <span>{{ getDate(row[col.prop]) }}</span>
              <!-- <div v-if="row.manualUpdated ==='Y'" class="tag-left-right">
                <span class="triangle-text">人工</span>
              </div> -->
            </div>
          </template>
        </el-table-column>
      </template>
      <template v-for="item in getColSlots" #[item.prop]="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <template v-if="!row.isEdit">
              <span v-if="col.decimalPlaces"> {{
                                getShowValue(row[col.prop], col.decimalPlaces, 'ROUND_HALF_UP')
                              }}</span>
              <span v-else> {{ row[col.prop] }}</span>
            </template>

            <template v-else>
              <el-autocomplete v-if="item.prop ==='coalCategoryName'" v-model="row[`${col.prop}${editKey}`]"
                               :fetch-suggestions="querySearch" class="noPaddingInput">
              </el-autocomplete>
              <el-input v-else v-model="row[`${col.prop}${editKey}`]" :placeholder="`${col.label}`" :type="item.type"
                        class="noPaddingInput" @input="handleInputChange(row,col)">
              </el-input>
            </template>
          </template>
        </el-table-column>
      </template>
      <template #coalName="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row, $index}">
            <div style="position: relative;">
              <div style="color: rgb(37, 109, 223);cursor: pointer; text-decoration: underline; text-underline-offset: 5px;"  @click="priceTrendDialogOpen(row)">{{ row[col.prop] }}</div>
              <div v-if="row.isCoalRock ==='Y'" style="background:#FF8B41" class="tag-left-right">
                <span class="triangle-text">岩</span>
              </div>
              <div v-if="row.type === 'nb'" class="tag-left-right" style="background:#FF8B41">
                <span class="triangle-text">人工</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </template>
      <template #macR0="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span>{{ col.formatter ? col.formatter(row) : (row[col.prop] !== undefined && row[col.prop] !== null ? Number(row[col.prop]).toFixed(3) : '') }}</span>
          </template>
        </el-table-column>
      </template>
      
      <template #macS="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span>{{ col.formatter ? col.formatter(row) : (row[col.prop] !== undefined && row[col.prop] !== null ? Number(row[col.prop]).toFixed(3) : '') }}</span>
          </template>
        </el-table-column>
      </template>
      
      <template #vd="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span>{{
                getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
              }} </span></template>
        </el-table-column>
      </template>
      <template #mci="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span>{{
                getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
              }} </span></template>
        </el-table-column>
      </template>
      <template #rise="{ col }">
        <el-table-column v-bind="col" width="80">
          <template #default="{row}">
            <span v-if="row[col.prop]>0" style="color: red">{{ row[col.prop] }} </span>
            <span v-else-if="row[col.prop]<0" style="color: green">{{ row[col.prop] }} </span>
            <span v-else>{{ row[col.prop] }} </span>
          </template>
        </el-table-column>
      </template>
      <template #monthRise="{ col }">
        <el-table-column v-bind="col" width="80">
          <template #default="{row}">
            <span v-if="row[col.prop]>0" style="color: red">{{ row[col.prop] }} </span>
            <span v-else-if="row[col.prop]<0" style="color: green">{{ row[col.prop] }} </span>
            <span v-else>{{ row[col.prop] }} </span>
          </template>
        </el-table-column>
      </template>
      <template #rangeRise="{ col }">
        <el-table-column v-bind="col" width="250">
          <template #header>
            <el-date-picker v-model="riseRange" :picker-options="pickerOptions" class="daterange" range-separator="VS"
                            type="daterange" :clearable="false" value-format="yyyy-MM-dd" @change="riseRangeChange" />
          </template>
          <template #default="{row}">
            <span v-if="row[col.prop]>0" style="color: red">{{ row[col.prop] }} </span>
            <span v-else-if="row[col.prop]<0" style="color: green">{{ row[col.prop] }} </span>
            <span v-else>{{ row[col.prop] }} </span>
          </template>
        </el-table-column>
      </template>
      <template #activePriceFactoryPriceNoTax="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span>{{
                getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
              }} </span></template>
        </el-table-column>
      </template>
      <template #reduceMtFactoryPriceNoTax="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span>{{
                getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
              }} </span>
          </template>
        </el-table-column>
      </template>
      <template #coalBlendingCost="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span>{{
                getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
              }} </span>
          </template>
        </el-table-column>
      </template>
      <template #opt="{ row,...rest }">
        <template v-if="checkPermission([permissions.save])">
          <el-button v-if="!row.isEdit" type="text" @click.stop="handleRow(row, 'edit', rest)">
            编辑
          </el-button>
          <template v-else>
            <el-button :loading="row.loading" type="text" @click.stop="handleRow(row, 'save', rest)">
              保存
            </el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button :loading="row.loading" type="text" @click.stop="handleRow(row, 'cancel', rest)">
              取消
            </el-button>
          </template>
        </template>
        <template v-if="checkPermission([permissions.remove]) && (row.coalSource === 'zsmy' || row.type === 'zsmy' || row.mergeType === MERGE_TYPE.MERGE)">
          <el-button v-if="MERGE_TYPE.MERGE === row.mergeType" type="text" @click.stop="handleRemove(row)">
            取消合并
          </el-button>
          <el-button v-else type="text" @click.stop="handleRemove(row)">删除</el-button>
          <el-divider direction="vertical"></el-divider>
        </template>
        <el-button v-if="checkPermission([permissions.save])" type="text" @click.stop="handleSortTop(row)">
          {{ row.sort ? '取消置顶' : '置顶' }}
        </el-button>
      </template>
    </SnProTable>
    <FormModal v-if="addInfo.visitable" v-model="addInfo.visitable" :categoryNamesList="categoryNamesList.map(v=>v.name)"
               :coalSource="coalSource" :model="model" :optName="addInfo.optName" :record="addInfo.record"
               @ok="() => getList(true)" />
    <AvgFormModal v-if="avgAddInfo.visitable" v-model="avgAddInfo.visitable" :categoryNamesList="categoryNamesList.map(v=>v.name)"
                  :coalSource="coalSource" :model="model" :optName="avgAddInfo.optName" :record="avgAddInfo.record"
                  @ok="() => getList(true)" />
    <FieldUpdateFormModal v-if="fieldUpdateInfo.visitable" v-model="fieldUpdateInfo.visitable" :coalSource="coalSource"
                          :model="model" :optName="fieldUpdateInfo.optName" :record="fieldUpdateInfo.record"
                          @ok="() => getList(true)" />
    <ItemCoal v-if="addVisible" :add-visible="addVisible" :categoryNamesList="categoryNamesList.map(v=>v.name)"
              :details="coalDetail" :dialogStatus="dialogStatus" :isReadonly="true" :model="model" @closeVisible="()=>{
                    addVisible = false
                    getList(false)
                }">
    </ItemCoal>
    <el-dialog :before-close="priceTrendDialogClose" :visible.sync="priceTrendDialog" :title="priceTrendData.coalName " top="5vh"
               width="85vw" custom-class="padded-dialog">
      <!-- 基础信息 -->
      <div style="margin-top: 20px">
        <div style="font-weight: bold; margin-bottom: 10px">基础信息</div>
      <table class="table" style="margin-top: 20px">
        <thead>
          <tr>
            <th>名称</th>
            <th>煤种</th>
            <th>产地</th>
            <th>含税煤价</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>{{ priceTrendData.coalName }}</td>
            <td>{{ priceTrendData.coalCategoryName }}</td>
            <td>{{ priceTrendData.area }}</td>
            <td>{{ priceTrendData.factoryPrice && Number(priceTrendData.factoryPrice) !== 0 ? priceTrendData.factoryPrice : '' }}</td>
          </tr>
        </tbody>
      </table>
      </div>
      
      <!-- 指标 -->
      <div style="margin-top: calc(20px + 5%)">
        <div style="font-weight: bold; margin-bottom: 10px">指标</div>
      <table class="table" style="margin-top: 20px">
        <thead>
          <tr>
            <th>Ad</th>
            <th>St,d</th>
            <th>Vdaf</th>
            <th>G</th>
            <th>Y</th>
            <th>X</th>
            <th>Mt</th>
            <th>CSR</th>
            <th>反射率</th>
            <th>标准差</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>{{ priceTrendData.cleanAd && Number(priceTrendData.cleanAd) !== 0 ? priceTrendData.cleanAd : '' }}</td>
            <td>{{ priceTrendData.cleanStd && Number(priceTrendData.cleanStd) !== 0 ? priceTrendData.cleanStd : '' }}</td>
            <td>{{ priceTrendData.cleanVdaf && Number(priceTrendData.cleanVdaf) !== 0 ? priceTrendData.cleanVdaf : '' }}</td>
            <td>{{ priceTrendData.procG && Number(priceTrendData.procG) !== 0 ? priceTrendData.procG : '' }}</td>
            <td>{{ priceTrendData.procY && Number(priceTrendData.procY) !== 0 ? priceTrendData.procY : '' }}</td>
            <td>{{ priceTrendData.procX && Number(priceTrendData.procX) !== 0 ? priceTrendData.procX : '' }}</td>
            <td>{{ priceTrendData.cleanMt && Number(priceTrendData.cleanMt) !== 0 ? priceTrendData.cleanMt : '' }}</td>
            <td>{{ priceTrendData.cokeIndexCsr && Number(priceTrendData.cokeIndexCsr) !== 0 ? priceTrendData.cokeIndexCsr : '' }}</td>
            <td>{{ priceTrendData.macR0 !== undefined && priceTrendData.macR0 !== null && Number(priceTrendData.macR0) !== 0 ? Number(priceTrendData.macR0).toFixed(3) : '' }}</td>
            <td>{{ priceTrendData.macS !== undefined && priceTrendData.macS !== null && Number(priceTrendData.macS) !== 0 ? Number(priceTrendData.macS).toFixed(3) : '' }}</td>
          </tr>
        </tbody>
      </table>
      </div>

      <!-- 价格趋势图 -->
      <div style="margin-top: calc(20px + 5%)">
        <div style="font-weight: bold; margin-bottom: 10px">价格趋势</div>
      <sn-date-ranger v-model="dateRange"
                      style="position: absolute;width: 250px;right: 11%;transform: translateY(0px);z-index: 1"
                      @change="getByPrice()"></sn-date-ranger>
        <div ref="chart" style="width: 100%;height: 25vw;margin: 0;border: 1px solid #CCCCCC"></div>
      </div>
      <div v-if="hasAsterisksInReflectanceData" class="vip-notice">
          很抱歉，VIP才能查看煤岩及灰成分详细数据，如有需求请与我们联系
      </div>
        
      <!-- 反射率分布表 -->
      <div style="margin-top: calc(20px + 5%)">
        <div style="font-weight: bold; margin-bottom: 10px">反射率分布表 (当前反射率之和为{{reflectanceTotal || '空'}},反射率要求在99.5到100.5之间)</div>
        <table class="table reflectance-table">
          <thead style="background-color: #30BEB1; color: white;">
            <tr>
              <th></th>
              <th>Rran范围</th>
              <th>频率%</th>
              <th>Rran范围</th>
              <th>频率%</th>
              <th>Rran范围</th>
              <th>频率%</th>
              <th>Rran范围</th>
              <th>频率%</th>
              <th>Rran范围</th>
              <th>频率%</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(row, rowIndex) in reflectanceMatrix" :key="rowIndex" 
                :style="{ backgroundColor: rowIndex % 2 === 0 ? '#f3fbfc' : 'white' }">
              <td :style="{ 
                  backgroundColor: rowIndex % 2 === 0 ? '#f3fbfc' : 'white'
                }">{{ rowIndex + 1 }}</td>
              <template v-for="(cell, cellIndex) in row">
                <td v-if="cellIndex % 2 === 0" :key="`range-${rowIndex}-${cellIndex}`"
                    :style="{ 
                      backgroundColor: rowIndex % 2 === 0 ? '#f3fbfc' : 'white'
                    }">
                  {{ cell }}
                </td>
                <td v-else :key="`value-${rowIndex}-${cellIndex}`"
                    :style="{ 
                      backgroundColor: 'transparent',
                      color: Number(cell) > 0 ? '#333' : '#999',
                      fontWeight: 'normal'
                    }">
                  {{ cell === '*' ? '***' : (Number(cell) > 0 ? cell : '') }}
                </td>
              </template>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 图表信息 -->
      <div style="margin-top: calc(20px + 5%)">
        <div style="font-weight: bold; margin-bottom: 10px">图表信息</div>
        <div class="reflectance-chart-container">
          <!-- 反射率分布柱状图 -->
        
          <div ref="reflectanceChart" style="width: 100%; height: 400px; border: 1px solid #eee; margin-bottom: 20px;"></div>
          
          <!-- 煤种分布表格 -->
          <table class="table coal-type-table">
            <tr style="background-color: #f2f2f2; font-weight: normal;">
              <td>范围</td>
              <td>&lt;0.5</td>
              <td>0.5-0.6</td>
              <td>0.6-0.75</td>
              <td>0.75-0.9</td>
              <td>0.9-1.15</td>
              <td>1.15-1.55</td>
              <td>1.55-1.7</td>
              <td>1.7-1.9</td>
              <td>1.9-2.35</td>
              <td>&gt;2.35</td>
            </tr>
            <tr style="background-color: #f8f8f8; font-weight: normal;">
              <td>煤种</td>
              <td>褐煤</td>
              <td>长焰</td>
              <td>气煤</td>
              <td>1/3焦煤</td>
              <td>肥煤</td>
              <td>焦煤</td>
              <td>瘦煤</td>
              <td>贫瘦煤</td>
              <td>贫煤</td>
              <td>无烟煤</td>
            </tr>
            <tr>
              <td style="background-color: #f2f2f2; font-weight: normal;">百分比</td>
              <td :style="{ color: Number(coalTypePercentages['<0.5']) > 0 ? '#333' : '#999' }">
                {{ Number(coalTypePercentages['<0.5']) > 0 ? coalTypePercentages['<0.5'] : '' }}
              </td>
              <td :style="{ color: Number(coalTypePercentages['0.5-0.6']) > 0 ? '#333' : '#999' }">
                {{ Number(coalTypePercentages['0.5-0.6']) > 0 ? coalTypePercentages['0.5-0.6'] : '' }}
              </td>
              <td :style="{ color: Number(coalTypePercentages['0.6-0.75']) > 0 ? '#333' : '#999' }">
                {{ Number(coalTypePercentages['0.6-0.75']) > 0 ? coalTypePercentages['0.6-0.75'] : '' }}
              </td>
              <td :style="{ color: Number(coalTypePercentages['0.75-0.9']) > 0 ? '#333' : '#999' }">
                {{ Number(coalTypePercentages['0.75-0.9']) > 0 ? coalTypePercentages['0.75-0.9'] : '' }}
              </td>
              <td :style="{ color: Number(coalTypePercentages['0.9-1.15']) > 0 ? '#333' : '#999' }">
                {{ Number(coalTypePercentages['0.9-1.15']) > 0 ? coalTypePercentages['0.9-1.15'] : '' }}
              </td>
              <td :style="{ color: Number(coalTypePercentages['1.15-1.55']) > 0 ? '#333' : '#999' }">
                {{ Number(coalTypePercentages['1.15-1.55']) > 0 ? coalTypePercentages['1.15-1.55'] : '' }}
              </td>
              <td :style="{ color: Number(coalTypePercentages['1.55-1.7']) > 0 ? '#333' : '#999' }">
                {{ Number(coalTypePercentages['1.55-1.7']) > 0 ? coalTypePercentages['1.55-1.7'] : '' }}
              </td>
              <td :style="{ color: Number(coalTypePercentages['1.7-1.9']) > 0 ? '#333' : '#999' }">
                {{ Number(coalTypePercentages['1.7-1.9']) > 0 ? coalTypePercentages['1.7-1.9'] : '' }}
              </td>
              <td :style="{ color: Number(coalTypePercentages['1.9-2.35']) > 0 ? '#333' : '#999' }">
                {{ Number(coalTypePercentages['1.9-2.35']) > 0 ? coalTypePercentages['1.9-2.35'] : '' }}
              </td>
              <td :style="{ color: Number(coalTypePercentages['>2.35']) > 0 ? '#333' : '#999' }">
                {{ Number(coalTypePercentages['>2.35']) > 0 ? coalTypePercentages['>2.35'] : '' }}
              </td>
            </tr>
          </table>
        </div>
      </div>

      <!-- 煤灰成分 -->
      <div style="margin-top: calc(20px + 5%)">
        <div style="font-weight: bold; margin-bottom: 10px">煤灰成分</div>
        <div class="ash-composition-container">
          <div class="ash-content">
            <div class="ash-item">
                <span class="ash-label">SiO<sub>2</sub></span>
                <div class="ash-value-container">
                <div class="ash-value">{{ formatAshValue(priceTrendData.comSiO2 || priceTrendData.siO2) }}</div>
                <div class="ash-unit">(%)</div>
                </div>
              </div>
            <div class="ash-item">
                <span class="ash-label">Al<sub>2</sub>O<sub>3</sub></span>
                <div class="ash-value-container">
                <div class="ash-value">{{ formatAshValue(priceTrendData.comAl2O3 || priceTrendData.al2O3) }}</div>
                <div class="ash-unit">(%)</div>
                </div>
              </div>
            <div class="ash-item">
                <span class="ash-label">Fe<sub>2</sub>O<sub>3</sub></span>
                <div class="ash-value-container">
                <div class="ash-value">{{ formatAshValue(priceTrendData.comFe2O3 || priceTrendData.fe2O3) }}</div>
                <div class="ash-unit">(%)</div>
                </div>
              </div>
            <div class="ash-item">
                <span class="ash-label">CaO</span>
                <div class="ash-value-container">
                <div class="ash-value">{{ formatAshValue(priceTrendData.comCaO || priceTrendData.caO) }}</div>
                <div class="ash-unit">(%)</div>
                </div>
              </div>
            <div class="ash-item">
                <span class="ash-label">MgO</span>
                <div class="ash-value-container">
                <div class="ash-value">{{ formatAshValue(priceTrendData.comMgO || priceTrendData.mgO) }}</div>
                <div class="ash-unit">(%)</div>
                </div>
              </div>
            <div class="ash-item">
                <span class="ash-label">Na<sub>2</sub>O</span>
                <div class="ash-value-container">
                <div class="ash-value">{{ formatAshValue(priceTrendData.comNa2O || priceTrendData.na2O) }}</div>
                <div class="ash-unit">(%)</div>
                </div>
              </div>
            <div class="ash-item">
                <span class="ash-label">K<sub>2</sub>O</span>
                <div class="ash-value-container">
                <div class="ash-value">{{ formatAshValue(priceTrendData.comK2O || priceTrendData.k2O) }}</div>
                <div class="ash-unit">(%)</div>
                </div>
              </div>
            <div class="ash-item">
                <span class="ash-label">TiO<sub>2</sub></span>
                <div class="ash-value-container">
                <div class="ash-value">{{ formatAshValue(priceTrendData.comTiO2 || priceTrendData.tiO2) }}</div>
                <div class="ash-unit">(%)</div>
                </div>
              </div>
            <div class="ash-item">
                <span class="ash-label">P<sub>2</sub>O<sub>5</sub></span>
                <div class="ash-value-container">
                <div class="ash-value">{{ formatAshValue(priceTrendData.comP2O5 || priceTrendData.p2O5) }}</div>
                <div class="ash-unit">(%)</div>
                </div>
              </div>
            <div class="ash-item">
                <span class="ash-label">SO<sub>3</sub></span>
                <div class="ash-value-container">
                <div class="ash-value">{{ formatAshValue(priceTrendData.comSO3 || priceTrendData.so3) }}</div>
                <div class="ash-unit">(%)</div>
                </div>
              </div>
            <div class="ash-item">
              <span class="ash-label">MnO<sub>2</sub></span>
                <div class="ash-value-container">
                <div class="ash-value">{{ formatAshValue(priceTrendData.comMnO2 || priceTrendData.mnO2) }}</div>
                <div class="ash-unit">(%)</div>
                </div>
              </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TipModal from '@/utils/modal'
import model, { getEditCols } from './model'
import FormModal from './components/FormModal.vue'
import AvgFormModal from './components/AvgFormModal.vue'
import FieldUpdateFormModal from './components/FieldUpdateFormModal.vue'
import { getDate } from '@/utils/dateUtils'
import SnProTable from '@/components/Common/SnProTable/index.vue'
import checkPermission from '@/utils/permission'
import { COAL_SOURCE_TYPE, MERGE_TYPE } from '@/const'
import CalcUtils from '@/utils/calcUtils'
import SnSimpleSelect from '@/components/Common/SnSimpleSelect/index.vue'
import { listCategoryNames } from '@/api/common/listAllSimple'
import ItemCoal from './components/ItemCoal.vue'
import dayjs from 'dayjs'
import echarts from 'echarts'
import DateRanger from '@/components/DateRanger/index.vue'
import SnDateRanger from '@/components/Common/SnDateRanger/index.vue'

const end = dayjs().subtract(1, 'day').format('YYYY-MM-DD') // 当前日期和时间
const start = dayjs().subtract(8, 'day').format('YYYY-MM-DD')
export default {
  name: 'OtherSideStock',
  components: {
    SnDateRanger,
    DateRanger, ItemCoal, SnSimpleSelect, SnProTable, FormModal, FieldUpdateFormModal, AvgFormModal
  },
  data() {
    return {
      addVisible: false,
      coalDetail: {},
      dialogStatus: 'update',
      model,
      MERGE_TYPE,
      pageRef: 'page',
      permissions: {
        save: '*',
        remove: '*'
      },
      addInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      },
      avgAddInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      },
      fieldUpdateInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      },
      editKey: '_edit',
      editPropList: getEditCols(),
      categoryNamesList: [],
      riseRange: [start, end],
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      priceTrendDialog: false,
      priceTrendData: {
        id: '',
        area: '',
        cleanAd: '',
        cleanMt: '',
        cleanStd: '',
        factoryPrice: '',
        cleanVdaf: '',
        coalBlendingCost: '',
        coalCategoryName: '',
        coalName: '',
        date: '',
        macR0: '',
        macS: '',
        priceJson: '',
        procG: '',
        procX: '',
        procY: '',
        csr: '',
        // 重置所有煤灰成分数据
        siO2: '',
        al2O3: '',
        fe2O3: '',
        caO: '',
        mgO: '',
        na2O: '',
        k2O: '',
        tiO2: '',
        p2O5: '',
        so3: '',
        mnO2: ''
      },
      priceTrendChart: null,
      priceTrendQuery: {
        id: '',
        beginDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss'),
        endDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        coalSource: 'zy'
      },
      reflectanceTotal: null,
      reflectanceMatrix: [],
      hasReflectanceAsterisks: false,
      chartLoaded: false,
      coalTypePercentages: {},
      reflectanceChartInstance: null,
      showTipPopup: false,
      hasCheckedStorage: false  // 添加标志，表示是否已检查过存储
    }
  },
  created() {
    this.getDataList()
    this.getCategoryNamesList().then(list => {
      this.categoryNamesList = list.map(v => {
        return {
          name: v,
          id: v,
          value: v
        }
      })
    })

  },
  mounted() {
    console.log(this.pickerOptions)
    this.getList()
    
    // 确保只执行一次存储检查
    if (!this.hasCheckedStorage) {
      this.hasCheckedStorage = true
      
      // 添加调试日志
      console.log('正在检查是否显示过弹窗')
      
      try {
        // 检查是否已经显示过弹窗
        const hasShownPopup = localStorage.getItem('hasShownPriceTrendPopup')
        console.log('localStorage中的hasShownPriceTrendPopup值:', hasShownPopup)
        
        // 只有在未显示过的情况下才显示弹窗
        if (!hasShownPopup) {
          console.log('首次访问，将显示弹窗')
          // 立即显示弹窗，不需要延迟
          this.showTipPopup = true
          
          // 5秒后自动关闭
          setTimeout(() => {
            this.showTipPopup = false
            console.log('弹窗已自动关闭')
          }, 5000)
          
          // 标记已经显示过弹窗
          localStorage.setItem('hasShownPriceTrendPopup', 'true')
          console.log('已在localStorage中设置标记')
        } else {
          console.log('已经显示过弹窗，不再显示')
        }
      } catch (error) {
        // 如果localStorage访问出错，也显示弹窗
        console.error('访问localStorage时出错:', error)
        this.showTipPopup = true
        
        setTimeout(() => {
          this.showTipPopup = false
        }, 5000)
      }
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resize)
    window.removeEventListener('resize', this.resizeReflectanceChart)
    // 清理反射率图表
    if (this.reflectanceChartInstance) {
      this.reflectanceChartInstance.dispose()
      this.reflectanceChartInstance = null
    }
  },
  props: {
    coalSource: {
      type: String,
      default: COAL_SOURCE_TYPE.zsmy
    }
  },
  computed: {
    getColSlots() {
      const exclude = ['activePriceFactoryPriceNoTax', 'reduceMtFactoryPriceNoTax', 'coalBlendingCost', 'vd', 'mci']
      return this.editPropList.filter((item) => !exclude.includes(item.prop))
    },
    getEditList() {
      return [...this.editPropList]
    },
    actions() {
      return [
        {
      type: 'add',
      text: '添加到我的煤源',
      hasPermission: [this.permissions.save],
      onClick: async () => {
        const selectedRows = this.$refs[this.pageRef].getSelectRows();
        if (selectedRows.length) {
          try {
                console.log('Selected rows for adding to my coal source:', selectedRows);
                console.log('IDs being sent:', selectedRows.map(item => item.id));
                
            const res = await model.addToMyCoalSource({ data: selectedRows });
                console.log('API response:', res);
                
                // 检查API返回格式，axios返回的数据在res.data中
                const apiData = res.data || res;
                
                if (apiData && apiData.success === false) {
                  TipModal.msgError('添加失败: ' + (apiData.message || '未知错误'));
                } else {
                  console.error('API returned success=false:', res);
                  TipModal.msgSuccess('已成功添加到我的煤源');
                  
                  // 刷新当前页面（掌上煤焦煤源）
                  this.getList(true);
                  
                  // 通知父组件刷新"我的煤源"数据，但不跳转
                  this.$emit('refresh-my-coal-data');
            }
          } catch (e) {
            console.error('添加失败详情:', e);
                console.error('错误响应:', e.response);
                TipModal.msgError('添加失败: ' + (e.message || '未知错误'));
          }
        } else {
          TipModal.msgWarning('请先选择要添加的数据');
        }
      }
    },
        // {
        //     type: 'add',
        //     text: '新增',
        //     hasPermission: '*',
        //     onClick: async (item) => {
        //         this.setModal('addInfo')
        //     }
        // },
        // {
        //     type: 'add',
        //     text: '合并数据',
        //     hasPermission: [this.permissions.save],
        //     onClick: async (item) => {
        //         let data = this.$refs[this.pageRef].getSelectRows()
        //         if (data.length) {
        //             this.setModal('avgAddInfo', 'update', {
        //                 data: data
        //             })
        //         } else {
        //             TipModal.msgWarning('请先选择要操作的数据')
        //         }
        //     }
        // },
        // {
        //     type: 'add',
        //     text: '批量编辑',
        //     hasPermission: [this.permissions.save],
        //     onClick: async (item) => {
        //         let data = this.$refs[this.pageRef].getSelectRows()
        //         if (data.length) {
        //             data = data.map((item) => {
        //                 const obj = {...item}
        //                 for (const [k, v] of Object.entries(obj)) {
        //                     if (v === null || v === '/') obj[k] = undefined
        //                 }
        //                 return obj
        //             })
        //             this.setModal('addInfo', 'update', {
        //                 data: data
        //             })
        //         } else {
        //             TipModal.msgWarning('请先选择要操作的数据')
        //         }
        //     }
        // },

        // 批量改
        // {
        //     type: 'add',
        //     text: '改折水/路耗/运费',
        //     hasPermission: [this.permissions.save],
        //     onClick: async (item) => {
        //         let data = this.$refs[this.pageRef].getSelectRows()
        //         if (data.length) {
        //             this.setModal('fieldUpdateInfo', 'update', {
        //                 data: data.map(v => {
        //                     return {
        //                         id: v.id,
        //                         reduceMtStandard: v.reduceMtStandard,
        //                         activePriceTransportCostP1: v.activePriceTransportCostP1,
        //                         coalSource: v.coalSource,
        //                         coalName: v.coalName,
        //                         activePriceTransportPriceNoTax: v.activePriceTransportPriceNoTax,
        //                     }
        //                 })
        //             })
        //         } else {
        //             TipModal.msgWarning('请先选择要操作的数据')
        //         }
        //     }
        // },
      ]
    },
    dateRange: {
      get() {
        return [this.priceTrendQuery.beginDate, this.priceTrendQuery.endDate]
      },
      set(value) {
        this.priceTrendQuery.beginDate = value[0]
        this.priceTrendQuery.endDate = value[1]
      }
    },
    hasAsterisksInReflectanceData() {
      // 直接使用标志位
      if (this.hasReflectanceAsterisks) {
        return true;
      }
      
      // 如果没有标志位，再检查矩阵中是否有 '*' 或 '***' 值
      if (!this.reflectanceMatrix || this.reflectanceMatrix.length === 0) {
        return false;
      }
      
      // 检查每一行是否有 '*' 或 '***' 值
      return this.reflectanceMatrix.some(row => {
        // 只检查奇数索引（值单元格，而不是范围名称单元格）
        for (let i = 1; i < row.length; i += 2) {
          if (row[i] === '*' || row[i] === '***') {
            return true;
          }
        }
        return false;
      });
    }
  },
  methods: {
    getSelectRows() {
    return this.selectedRows || [] 
  },
    resize() {
      if (this.priceTrendChart) {
        this.priceTrendChart.resize()
      }
    },
    async getByPrice(id) {
      if (id) {
        this.priceTrendQuery.id = id
      }
      
      // 确保包含coalSource参数
      if (!this.priceTrendQuery.coalSource) {
        this.priceTrendQuery.coalSource = 'zy';
      }
      
      console.log('获取价格趋势数据，完整参数:', JSON.stringify(this.priceTrendQuery));
      
      try {
      const res = await model.getByPrice(this.priceTrendQuery)
      if (res) {
        // 保存当前灰成分数据
        const ashValues = {
          siO2: this.priceTrendData.siO2,
          al2O3: this.priceTrendData.al2O3,
          fe2O3: this.priceTrendData.fe2O3,
          caO: this.priceTrendData.caO,
          mgO: this.priceTrendData.mgO,
          na2O: this.priceTrendData.na2O,
          k2O: this.priceTrendData.k2O,
          tiO2: this.priceTrendData.tiO2,
          p2O5: this.priceTrendData.p2O5,
          so3: this.priceTrendData.so3,
          mnO2: this.priceTrendData.mnO2
        };
        
        // 保存原来的含税煤价
        const factoryPrice = this.priceTrendData.factoryPrice;
        
        // 更新价格趋势数据
        this.priceTrendData = res.data
        
        // 恢复含税煤价数据
        if (factoryPrice) this.priceTrendData.factoryPrice = factoryPrice;
        
        // 恢复灰成分数据
        if (ashValues.siO2) this.priceTrendData.siO2 = ashValues.siO2;
        if (ashValues.al2O3) this.priceTrendData.al2O3 = ashValues.al2O3;
        if (ashValues.fe2O3) this.priceTrendData.fe2O3 = ashValues.fe2O3;
        if (ashValues.caO) this.priceTrendData.caO = ashValues.caO;
        if (ashValues.mgO) this.priceTrendData.mgO = ashValues.mgO;
        if (ashValues.na2O) this.priceTrendData.na2O = ashValues.na2O;
        if (ashValues.k2O) this.priceTrendData.k2O = ashValues.k2O;
        if (ashValues.tiO2) this.priceTrendData.tiO2 = ashValues.tiO2;
        if (ashValues.p2O5) this.priceTrendData.p2O5 = ashValues.p2O5;
        if (ashValues.so3) this.priceTrendData.so3 = ashValues.so3;
        if (ashValues.mnO2) this.priceTrendData.mnO2 = ashValues.mnO2;
        
        // 保存ID，确保后续操作有效
        this.priceTrendData.id = id || this.priceTrendQuery.id;
        
          // 解析价格数据
        if (this.priceTrendData.priceJson) {
            if (typeof this.priceTrendData.priceJson === 'string') {
              this.priceTrendData.priceJson = JSON.parse(this.priceTrendData.priceJson);
        }
        
            // 确保每个数据点都有价格值
            this.priceTrendData.priceJson.forEach(item => {
              if (!item.factoryPrice && item.coalBlendingCost) {
                item.factoryPrice = item.coalBlendingCost;
              }
            });
            
            console.log('价格趋势数据:', this.priceTrendData.priceJson);
          } else {
            console.warn('未获取到价格趋势数据');
            // 创建一个空的价格趋势数据
            this.priceTrendData.priceJson = [];
          }
          
          // 更新价格趋势图表
          this.updatePriceTrendChart();
        }
      } catch (error) {
        console.error('获取价格趋势数据失败:', error);
        TipModal.msgError('获取价格趋势数据失败: ' + (error.message || '未知错误'));
      }
    },
    
    // 更新价格趋势图表
    updatePriceTrendChart() {
      if (!this.priceTrendChart || !this.priceTrendData.priceJson) {
        console.warn('无法更新价格趋势图表：图表实例或数据不存在');
        return;
      }
      
      console.log('更新价格趋势图表，数据点数量:', this.priceTrendData.priceJson.length);
      
      // 设置图表配置
        this.priceTrendChart.setOption({
          title: {
            left: 20,
            top: 10,
          text: this.priceTrendData.coalName
          },
          xAxis: {
            type: 'category',
            data: this.priceTrendData.priceJson.map(item => item.date).reverse(),
            axisTick: {
              show: false
            },
            formatter: (item) => item.substring(0, 10),
            axisLine: {
              show: true,
              width: 5,
              symbol: ['none', 'arrow'],
              symbolOffset: 30,
              lineStyle: {
                color: '#F5F5FF',// 更改坐标轴颜色
                type: 'solid',
                shadowOffsetX: 30,
                shadowColor: '#F5F5FF'
              }
            },
            axisLabel: {
              color: '#9A9A9A'
            }
          },
          yAxis: {
            type: 'value',
            axisTick: {
              show: false
            },
            scale: true,
            splitLine: {
              lineStyle: {
                color: '#F5F5FF'
              }
            },
            axisLine: {
              show: true,
              width: 5,
              symbol: ['none', 'arrow'],
              symbolOffset: 30,
              lineStyle: {
                color: '#F5F5FF', // 更改坐标轴颜色
                type: 'solid',
                shadowOffsetY: -30,
                shadowColor: '#F5F5FF'
              }
            },
            axisLabel: {
              showMaxLabel: false,
              color: '#9A9A9A'
            }
          },
          tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            const param = params[0];
            const date = param.name;
            const value = param.value;
            return `${date}<br/>价格: ${value ? value.toFixed(2) : '无数据'}`;
          }
          },
          legend: {
            type: 'plain',
            data: ['价格'],
            bottom: 0,
            selected: {
              '价格': true
            }
          },
          grid: {
            left: '25',
            right: '50',
            top: '80',
            bottom: '30',
            containLabel: true
          },
          series: [
            {
              data: this.priceTrendData.priceJson.map(item => item.factoryPrice || item.coalBlendingCost).reverse(),
              type: 'line',
              name: '价格',
              label: {
                show: true,
              color: '#595EC9',
              position: 'top',
              formatter: function(params) {
                return params.value ? params.value.toFixed(2) : '';
              }
              },
            showAllSymbol: true,
            symbolSize: 6,
              lineStyle: {
                color: '#595EC9'
              },
              itemStyle: {
                color: '#595EC9'
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#F5F5FF'
                  },
                  {
                    offset: 1,
                    color: 'white'
                  }
                ])
              }
            }
          ]
      });
    },
    // 初始化灰成分数据的方法
    initDefaultAshValues() {
      console.log('初始化默认灰成分数据');
      // 使用$set确保响应式更新
      this.$set(this.priceTrendData, 'siO2', '55.21');
      this.$set(this.priceTrendData, 'al2O3', '28.16');
      this.$set(this.priceTrendData, 'fe2O3', '7.24');
      this.$set(this.priceTrendData, 'caO', '3.42');
      this.$set(this.priceTrendData, 'mgO', '1.33');
      this.$set(this.priceTrendData, 'na2O', '0.68');
      this.$set(this.priceTrendData, 'k2O', '1.54');
      this.$set(this.priceTrendData, 'tiO2', '1.23');
      this.$set(this.priceTrendData, 'p2O5', '0.32');
      this.$set(this.priceTrendData, 'so3', '0.87');
      this.$set(this.priceTrendData, 'mnO2', '0.12');
      
      // 强制更新视图
      this.$forceUpdate();
      console.log('默认灰成分数据已设置，priceTrendData:', JSON.stringify(this.priceTrendData));
    },
    
    async priceTrendDialogOpen(row) {
      // 关闭提示弹窗
      this.showTipPopup = false;
      
      // 重置所有数据，避免显示上一次的煤灰成分
      this.priceTrendData = {
        id: '',
        area: '',
        cleanAd: '',
        cleanMt: '',
        cleanStd: '',
        factoryPrice: '',
        cleanVdaf: '',
        coalBlendingCost: '',
        coalCategoryName: '',
        coalName: '',
        date: '',
        macR0: '',
        macS: '',
        priceJson: '',
        procG: '',
        procX: '',
        procY: '',
        csr: '',
        // 重置所有煤灰成分数据
        siO2: '',
        al2O3: '',
        fe2O3: '',
        caO: '',
        mgO: '',
        na2O: '',
        k2O: '',
        tiO2: '',
        p2O5: '',
        so3: '',
        mnO2: ''
      }
      
      this.priceTrendDialog = true
      await this.$nextTick()
      if (!this.priceTrendChart) {
        this.priceTrendChart = echarts.init(this.$refs.chart)
        window.addEventListener('resize', this.resize)
      }
      
      // 设置当前煤炭ID
      this.priceTrendQuery.id = row.id;
      this.priceTrendData.id = row.id;
      
      // 根据煤种类型设置不同的coalSource
      if (row.type === 'zsmy') {
        this.priceTrendQuery.coalSource = 'zy'; // 掌上煤焦
      } else if (row.type === 'wb') {
        this.priceTrendQuery.coalSource = 'zy'; // 外部煤源
      }
      
      console.log('已设置当前煤炭ID:', row.id, '煤种类型:', row.type, '数据源:', this.priceTrendQuery.coalSource);

      // 获取煤炭详情以获取反射率分布数据和灰成分数据
      try {
        const { data: coalDetail } = await this.model.getZsmyCoalById(row.id, row.type || 'zsmy');
        
        // 保存煤炭详情数据到priceTrendData中
        if (coalDetail) {
          // 保存基本信息
          this.priceTrendData.coalName = coalDetail.coalName || row.coalName;
          this.priceTrendData.coalCategoryName = coalDetail.coalCategoryName || row.coalCategoryName;
          this.priceTrendData.area = coalDetail.location || row.location || coalDetail.area || '';
          
          // 保存含税煤价
          if (coalDetail.factoryPrice) {
            this.priceTrendData.factoryPrice = coalDetail.factoryPrice;
          } else if (row.factoryPrice) {
            this.priceTrendData.factoryPrice = row.factoryPrice;
          } else if (coalDetail.activePriceCoalPriceWithTax) {
            this.priceTrendData.factoryPrice = coalDetail.activePriceCoalPriceWithTax;
          } else if (row.activePriceCoalPriceWithTax) {
            this.priceTrendData.factoryPrice = row.activePriceCoalPriceWithTax;
          }
          
          // 确保有价格数据用于显示
          if (!this.priceTrendData.factoryPrice && coalDetail.coalBlendingCost) {
            this.priceTrendData.factoryPrice = coalDetail.coalBlendingCost;
          } else if (!this.priceTrendData.factoryPrice && row.coalBlendingCost) {
            this.priceTrendData.factoryPrice = row.coalBlendingCost;
          }
          
          console.log('设置含税煤价:', this.priceTrendData.factoryPrice);
          
          // 保存反射率和标准差，确保保留三位小数
          if (coalDetail.macR0 !== undefined && coalDetail.macR0 !== null) {
            this.priceTrendData.macR0 = Number(coalDetail.macR0).toFixed(3);
          }
          if (coalDetail.macS !== undefined && coalDetail.macS !== null) {
            this.priceTrendData.macS = Number(coalDetail.macS).toFixed(3);
          }
          
          // 保存灰成分数据
          if (coalDetail.siO2) this.priceTrendData.siO2 = coalDetail.siO2;
          if (coalDetail.al2O3) this.priceTrendData.al2O3 = coalDetail.al2O3;
          if (coalDetail.fe2O3) this.priceTrendData.fe2O3 = coalDetail.fe2O3;
          if (coalDetail.caO) this.priceTrendData.caO = coalDetail.caO;
          if (coalDetail.mgO) this.priceTrendData.mgO = coalDetail.mgO;
          if (coalDetail.na2O) this.priceTrendData.na2O = coalDetail.na2O;
          if (coalDetail.k2O) this.priceTrendData.k2O = coalDetail.k2O;
          if (coalDetail.tiO2) this.priceTrendData.tiO2 = coalDetail.tiO2;
          if (coalDetail.p2O5) this.priceTrendData.p2O5 = coalDetail.p2O5;
          if (coalDetail.so3) this.priceTrendData.so3 = coalDetail.so3;
          if (coalDetail.mnO2) this.priceTrendData.mnO2 = coalDetail.mnO2;
        }
        
        // 处理反射率分布数据
        if (coalDetail) {
          const proportionValues = coalDetail.proportionContent ? JSON.parse(coalDetail.proportionContent) : [];
          const rangeNameValues = coalDetail.rangeContent ? JSON.parse(coalDetail.rangeContent) : [];
          
          // 创建一个映射，将范围名称与对应的比例值关联起来
          const reflectanceMap = {};
          rangeNameValues.forEach((rangeName, index) => {
            reflectanceMap[rangeName] = proportionValues[index];
          });
          
          // 检查是否有 '*' 值
          this.hasReflectanceAsterisks = Object.values(reflectanceMap).some(val => val === '*');
          
          // 计算反射率总和
          this.reflectanceTotal = Object.values(reflectanceMap)
            .reduce((sum, val) => {
              // 处理 '*' 值，将其视为0进行计算总和
              const value = val === '*' ? 0 : Number(val) || 0;
              return sum + value;
            }, 0)
            .toFixed(1);
            
          // 创建反射率分布矩阵 (10行x10列)
          // 预定义所有范围
          const allRanges = [
            '0.25-0.30', '0.30-0.35', '0.35-0.40', '0.40-0.45', '0.45-0.50',
            '0.50-0.55', '0.55-0.60', '0.60-0.65', '0.65-0.70', '0.70-0.75',
            '0.75-0.80', '0.80-0.85', '0.85-0.90', '0.90-0.95', '0.95-1.00',
            '1.00-1.05', '1.05-1.10', '1.10-1.15', '1.15-1.20', '1.20-1.25',
            '1.25-1.30', '1.30-1.35', '1.35-1.40', '1.40-1.45', '1.45-1.50',
            '1.50-1.55', '1.55-1.60', '1.60-1.65', '1.65-1.70', '1.70-1.75',
            '1.75-1.80', '1.80-1.85', '1.85-1.90', '1.90-1.95', '1.95-2.00',
            '2.00-2.05', '2.05-2.10', '2.10-2.15', '2.15-2.20', '2.20-2.25',
            '2.25-2.30', '2.30-2.35', '2.35-2.40', '2.40-2.45', '2.45-2.50',
            '2.50-2.55', '2.55-2.60'
          ];
          
          this.reflectanceMatrix = [];
          // 每行10列，每2列表示一对(范围,频率)
          for (let i = 0; i < 10; i++) {
            const row = [];
            for (let j = 0; j < 5; j++) {
              const rangeIndex = i * 5 + j;
              if (rangeIndex < allRanges.length) {
                const rangeName = allRanges[rangeIndex];
                row.push(rangeName);
                // 保留原始值，包括 '*'，以便在 hasAsterisksInReflectanceData 计算属性中检测
                row.push(reflectanceMap[rangeName] || '');
              } else {
                row.push('');
                row.push('');
              }
            }
            this.reflectanceMatrix.push(row);
          }

          // 初始化反射率分布柱状图
          this.$nextTick(() => {
            this.initReflectanceChart(reflectanceMap, allRanges);
            
            // 计算煤种分布百分比
            this.calculateCoalTypePercentages(reflectanceMap);
          });
        }
      } catch (error) {
        console.error('获取反射率分布数据失败:', error);
      }
      
      await this.getByPrice(row.id)
    },
    priceTrendDialogClose() {
      this.priceTrendQuery = {
        id: this.priceTrendData.id, // 保留ID
        beginDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss'),
        endDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        coalSource: 'zy' // 保留coalSource参数
      }
      
      // 重置反射率相关数据
      this.reflectanceMatrix = [];
      this.reflectanceTotal = null;
      this.hasReflectanceAsterisks = false;
      
      // 销毁反射率图表
      if (this.reflectanceChartInstance) {
        this.reflectanceChartInstance.dispose();
        this.reflectanceChartInstance = null;
      }
      
      this.priceTrendDialog = false
      
      // 关闭对话框后刷新页面数据
      // this.getList(false)
    },

    riseRangeChange(e) {
      this.getList()
    },
    handleClickRow(row, column, event) {
      this.handleRow(row, 'edit', {
        $index: event.$index
      })
    },

    editName(row) {
      this.addVisible = true
      this.coalDetail = { ...row }
    },

    querySearch(queryString, cb) {
      let restaurants = this.categoryNamesList
      console.log(this.categoryNamesList, 'c')
      let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },

    async getCategoryNamesList() {
      try {
        const { data } = await listCategoryNames({ coalSource: this.coalSource })
        return data
      } catch (e) {
        return []
      }
    },
    async getDataList() {
      try {
        const { data } = await getRangeListApi()
        this.rangeList = data
      } catch (e) {
        this.rangeList = []
      }
    },
    handleInputChange(row, col) {
      const prop = col.prop
      const getEditVal = (key) => {
        const value = row[`${key}${this.editKey}`]
        return [undefined, null, ''].includes(value) ? undefined : Number(value)
      }
      const set = (key, value) => {
        row[`${key}${this.editKey}`] = value
      }
      const isEmpty = (val) => ['', null, undefined].includes(val)


      const v = {
        // activePriceCoalPriceWithTax: getEditVal('activePriceCoalPriceWithTax'),
        // activePriceTransportPriceNoTax: getEditVal('activePriceTransportPriceNoTax'),
        // activePriceTransportCostP1: getEditVal('activePriceTransportCostP1'),
        // activePriceCoalPriceNoTax: getEditVal('activePriceCoalPriceNoTax'),
        // reduceMtStandard: getEditVal('reduceMtStandard'),

        mt: getEditVal('mt'),
        ad: getEditVal('ad'),
        vdaf: getEditVal('vdaf'),
        siO2: getEditVal('siO2'),
        al2O3: getEditVal('al2O3'),
        fe2O3: getEditVal('fe2O3'),
        caO: getEditVal('caO'),
        k2O: getEditVal('k2O'),
        na2O: getEditVal('na2O'),
        mgO: getEditVal('mgO'),
        tiO2: getEditVal('tiO2'),
        mnO2: getEditVal('mnO2')
      }

      set('vd', CalcUtils.calculateVd(v))


      set('mci', CalcUtils.calculateMCI({
        ...v,
        vd: getEditVal('vd')
      }))
    },
    getShowValue: CalcUtils.getShowValue,
    checkPermission,
    setModal(target, optName = 'create', record = {}) {
      this[target].visitable = true
      this[target].optName = optName
      if (optName) this[target].record = { ...record }
    },
    afterFetch(data, listKey) {
      data[listKey].forEach(item => {
        item.sourceItem = { ...item }
        item.loading = false
        item.isEdit = false
        this.getEditList.forEach((value) => {
          item[`${value.prop}${this.editKey}`] = item[value.prop]
        })

        // Parse coalRockContent and map to corresponding columns
        if (item.coalRockContent && typeof item.coalRockContent === 'string') {
          try {
            const rockContent = JSON.parse(item.coalRockContent)
            if (Array.isArray(rockContent)) {
              rockContent.forEach(rock => {
                if (rock.rangeName && rock.proportion !== undefined) {
                  item[rock.rangeName] = rock.proportion
                }
              })
            }
          } catch (e) {
            console.error('Error parsing coalRockContent:', e)
          }
        }
      })

      return { ...data, [listKey]: data[listKey] }
    },

    getDate,
    rowClassName({ row, rowIndex, columnIndex, column }) {
      if (rowIndex === 0 && row.base === 1) {
        return 'mark-base-row'
      }
    },
    beforeFetch(params) {
      return {
        ...params,
        coalSource: this.coalSource,
        riseBeginDate: this.riseRange[0],
        riseEndDate: this.riseRange[1]
      }
    },
    async handleSortTop(row) {
      try {
        console.log('置顶操作完整参数:', row);
        
        // 识别数据来源：优先使用type属性，其次使用coalSource属性
        let dataSource;
        if (row.type === 'zsmy') {
          dataSource = 'zsmy'; // 将小写的zsmy转为大写的zsmy
        } else if (row.type === 'wb') {
          dataSource = row.type;
        } else if (row.coalSource) {
          dataSource = row.coalSource;
        } else {
          dataSource = this.coalSource;
        }
        
        console.log('使用的数据源标识:', dataSource);
        
        // 添加加载状态反馈
        this.$message({
          message: '正在处理置顶操作...',
          type: 'info',
          duration: 1000
        });
        
        const status = await this.model.sortTop({
          id: row.id,
          sort: row.sort,
          coalSource: dataSource
        });
        
        if (status) {
          TipModal.msgSuccess(`${row.sort ? '取消置顶' : '置顶'}操作成功`);
          this.getList(false);
        } else {
          TipModal.msgError(`${row.sort ? '取消置顶' : '置顶'}操作失败`);
        }
      } catch (error) {
        console.error('置顶操作异常:', error);
        TipModal.msgError(`操作失败: ${error.message || '未知错误'}`);
      }
    },

    async handleRemove({ id, coalSource, mergeType, type }) {
      try {
        // 确保掌上煤焦数据的coalSource参数正确
        const sourceToUse = coalSource || 'zsmy';
        
        console.log('删除参数:', { id, coalSource: sourceToUse, mergeType, type });
        
        const resp = await this.model.remove({ id, coalSource: sourceToUse, useConfirm: true, type: mergeType })
        if (resp) {
          TipModal.msgSuccess(`删除成功`)
          this.getList(true)
        }

      } catch (error) {
        console.error('删除失败:', error);
      }
    },

    async handleRow(row, type, ...rest) {
      if (type === 'edit') {
        row.isEdit = true
      }
      if (type === 'save') {
        row.loading = true
        const rowIndex = rest[0]['$index'] + 1
        try {
          const resultRow = { ...row, rockList: await this.buildRockList(row) }
          this.getEditList.forEach((value) => {
            resultRow[value.prop] = resultRow[`${value.prop}${this.editKey}`]
          })
          const resp = await this.model.update({ data: [resultRow] })
          if (resp) {
            TipModal.msgSuccess(`第${rowIndex}行编辑成功。`)
            // 刷新界面
            this.getList(true)
          }

        } catch (e) {
          console.log(e, 'e')
        }
        row.loading = false
        row.isEdit = false
      }

      if (type === 'cancel') {
        this.getEditList.forEach((value) => {
          row[`${value.prop}${this.editKey}`] = row[value.prop]
        })
        row.isEdit = false
      }
    },

    async buildRockList(row) {
      try {
        return this.rangeList.map(v => {
          const tar = row.rangeValues.find(item => item.rangeName === v.rangeName)
          return {
            rangeName: v.rangeName,
            proportion: tar?.proportion
            // sort: v.sort
          }
        })
      } catch (e) {
        console.log(e, 'e')
        return []
      }
    },

    getList(clear = false) {
      if (clear) {
        this.$refs[this.pageRef].clearSelect(true);
        // 清除选择后也需要刷新列表
        this.$refs[this.pageRef].getList();
      } else {
        this.$refs[this.pageRef].getList();
      }
    },
    initReflectanceChart(reflectanceMap, allRanges) {
      // 检查DOM是否存在
      if (!this.$refs.reflectanceChart) {
        console.error('反射率图表DOM元素不存在');
        return;
      }

      try {
        // 如果图表实例已存在，先销毁
        if (this.reflectanceChartInstance) {
          this.reflectanceChartInstance.dispose();
        }
        
        // 创建图表实例
        this.reflectanceChartInstance = echarts.init(this.$refs.reflectanceChart, null, { renderer: 'canvas' });
        
        // 添加窗口大小变化的监听，实现自适应
        window.addEventListener('resize', this.resizeReflectanceChart);
        
        // 准备完整的反射率范围列表
        const allReflectanceRanges = [
          '0.25-0.30', '0.30-0.35', '0.35-0.40', '0.40-0.45', '0.45-0.50',
          '0.50-0.55', '0.55-0.60', '0.60-0.65', '0.65-0.70', '0.70-0.75',
          '0.75-0.80', '0.80-0.85', '0.85-0.90', '0.90-0.95', '0.95-1.00',
          '1.00-1.05', '1.05-1.10', '1.10-1.15', '1.15-1.20', '1.20-1.25',
          '1.25-1.30', '1.30-1.35', '1.35-1.40', '1.40-1.45', '1.45-1.50',
          '1.50-1.55', '1.55-1.60', '1.60-1.65', '1.65-1.70', '1.70-1.75',
          '1.75-1.80', '1.80-1.85', '1.85-1.90', '1.90-1.95', '1.95-2.00',
          '2.00-2.05', '2.05-2.10', '2.10-2.15', '2.15-2.20', '2.20-2.25',
          '2.25-2.30', '2.30-2.35', '2.35-2.40', '2.40-2.45', '2.45-2.50',
          '2.50-2.55', '2.55-2.60'
        ];
        
        // 要显示标签的特定范围
        const showLabelRanges = [
          '0.25-0.30', 
          '0.50-0.55', 
          '0.75-0.80', 
          '1.00-1.05', 
          '1.25-1.30', 
          '1.50-1.55', 
          '1.75-1.80', 
          '2.00-2.05', 
          '2.25-2.30', 
          '2.50-2.55'
        ];
        
        // 准备数据
        const categories = allReflectanceRanges;
        const values = allReflectanceRanges.map(range => {
          // 处理 '*' 值，在图表中将其视为0
          const value = reflectanceMap[range];
          return value === '*' ? 0 : Number(value || 0);
        });
        
        // 设置图表配置
        const option = {
          backgroundColor: '#ffffff',
          grid: {
            left: '40',
            right: '20',
            bottom: '50',
            top: '30',
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'category',
            data: categories,
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              interval: function(index, value) {
                // 只显示指定的范围标签
                return showLabelRanges.includes(value);
              },
              rotate: 0,
              margin: 8,
              color: '#333',
              fontSize: 10
            },
            splitLine: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            min: 0,
            max: function(value) {
              return Math.ceil(value.max * 1.1) + 5;
            },
            interval: 5,
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#333',
              fontSize: 12
            },
            splitLine: {
              lineStyle: {
                color: '#eee',
                type: 'solid'
              }
            }
          },
          series: [
            {
              name: '频率',
              type: 'bar',
              barWidth: '100%', // 让柱状图挨在一起
              barCategoryGap: '0%', // 确保柱子之间没有间隔
              barGap: '0%', // 确保柱子之间没有间隔
              itemStyle: {
                color: '#FFA500'  // 更改为橙黄色
              },
              label: {
                show: false,  // 将show设置为false，不显示数字标签
                position: 'top',
                formatter: function(params) {
                  if (params.value > 0) {
                    return params.value;
                  }
                  return '';
                },
                fontSize: 12,
                color: '#333'
              },
              data: values
            }
          ]
        };
        
        // 应用配置
        this.reflectanceChartInstance.setOption(option);
        this.chartLoaded = true;
      } catch (error) {
        console.error('初始化反射率图表失败:', error);
        this.chartLoaded = true;
      }
    },
    
    // 添加反射率图表自适应调整方法
    resizeReflectanceChart() {
      if (this.reflectanceChartInstance) {
        this.reflectanceChartInstance.resize();
      }
    },
    calculateCoalTypePercentages(reflectanceMap) {
      // 定义煤种区间映射
      const coalTypeRanges = {
        '<0.5': ['0.25-0.30', '0.30-0.35', '0.35-0.40', '0.40-0.45', '0.45-0.50'],
        '0.5-0.6': ['0.50-0.55', '0.55-0.60'],
        '0.6-0.75': ['0.60-0.65', '0.65-0.70', '0.70-0.75'],
        '0.75-0.9': ['0.75-0.80', '0.80-0.85', '0.85-0.90'],
        '0.9-1.15': ['0.90-0.95', '0.95-1.00', '1.00-1.05', '1.05-1.10', '1.10-1.15'],
        '1.15-1.55': ['1.15-1.20', '1.20-1.25', '1.25-1.30', '1.30-1.35', '1.35-1.40', '1.40-1.45', '1.45-1.50', '1.50-1.55'],
        '1.55-1.7': ['1.55-1.60', '1.60-1.65', '1.65-1.70'],
        '1.7-1.9': ['1.70-1.75', '1.75-1.80', '1.80-1.85', '1.85-1.90'],
        '1.9-2.35': ['1.90-1.95', '1.95-2.00', '2.00-2.05', '2.05-2.10', '2.10-2.15', '2.15-2.20', '2.20-2.25', '2.25-2.30', '2.30-2.35'],
        '>2.35': ['2.35-2.40', '2.40-2.45', '2.45-2.50', '2.50-2.55', '2.55-2.60']
      };
      
      // 计算每个煤种区间的总和
      const coalTypeTotals = {};
      const total = Object.values(reflectanceMap).reduce((a, b) => {
        // 处理 '*' 值，将其视为0进行计算
        const value = b === '*' ? 0 : Number(b || 0);
        return a + value;
      }, 0);
      
      // 对每个煤种区间，计算其包含的反射率范围的百分比总和
      for (const [coalType, ranges] of Object.entries(coalTypeRanges)) {
        const sum = ranges.reduce((acc, range) => {
          // 处理 '*' 值，将其视为0进行计算
          const value = reflectanceMap[range] === '*' ? 0 : Number(reflectanceMap[range] || 0);
          return acc + value;
        }, 0);
        
        // 如果总和大于0，计算百分比
        if (sum > 0 && total > 0) {
          coalTypeTotals[coalType] = (sum / total * 100).toFixed(2);
        } else {
          coalTypeTotals[coalType] = '';
        }
      }
      
      this.coalTypePercentages = coalTypeTotals;
    },
    getAshReferenceValues() {
      console.log('获取灰成分参考值被点击，当前priceTrendData:', JSON.stringify(this.priceTrendData));
      
      if (!this.priceTrendData || !this.priceTrendData.id) {
        this.$message.error('无法获取灰成分参考值：煤炭ID不存在');
        return;
      }
      
      // 显示加载中提示
      this.$message({
        message: '正在获取灰成分参考值...',
        type: 'info',
        duration: 2000
      });
      
      // 从API获取最新灰成分值
      this.model.getAshReferenceValues(this.priceTrendData.id)
        .then(response => {
          console.log('灰成分参考值响应:', response);
          
          if (response && response.data) {
            // 更新灰成分数据
            if (response.data.siO2) this.priceTrendData.siO2 = response.data.siO2;
            if (response.data.al2O3) this.priceTrendData.al2O3 = response.data.al2O3;
            if (response.data.fe2O3) this.priceTrendData.fe2O3 = response.data.fe2O3;
            if (response.data.caO) this.priceTrendData.caO = response.data.caO;
            if (response.data.mgO) this.priceTrendData.mgO = response.data.mgO;
            if (response.data.na2O) this.priceTrendData.na2O = response.data.na2O;
            if (response.data.k2O) this.priceTrendData.k2O = response.data.k2O;
            if (response.data.tiO2) this.priceTrendData.tiO2 = response.data.tiO2;
            if (response.data.p2O5) this.priceTrendData.p2O5 = response.data.p2O5;
            if (response.data.so3) this.priceTrendData.so3 = response.data.so3;
            if (response.data.mnO2) this.priceTrendData.mnO2 = response.data.mnO2;
            
            // 强制更新视图
            this.$forceUpdate();
            
            this.$message.success('已更新为最新灰成分参考值');
          } else {
            this.$message.warning('未获取到灰成分参考值');
          }
        })
        .catch(error => {
          console.error('获取灰成分参考值失败:', error);
          this.$message.error('获取灰成分参考值失败: ' + (error.message || '未知错误'));
        });
    },
    formatAshValue(value) {
      if (value === undefined || value === null || value === '') {
        return '';
      }
      
      // 将值转换为数字
      const numValue = parseFloat(value);
      if (isNaN(numValue) || numValue === 0) {
        return '';
      }
      
      // 检查是否为整数
      if (Math.floor(numValue) === numValue) {
        // 如果是整数，返回整数形式
        return Math.floor(numValue);
      } else {
        // 如果是小数，保留两位小数
        return numValue.toFixed(2);
      }
    },
    closeTipPopup() {
      this.showTipPopup = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.table {
  margin: 0 auto;
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #cccccc;
  table-layout: fixed;

  thead {
    background-color: #24c1ab;
    color: white;
  }

  tr {
    height: 33px;
  }

  td,
  th {
    border: 1px solid #cccccc;
    text-align: center;
    padding: 5px;
  }
}

.noPaddingInput {
  padding: 0 5px;

  ::v-deep {
    .el-input__inner {
      padding: 0px 6px !important;
      text-align: center;
      border: 1px solid #256ddf;
    }
  }
}

::v-deep .daterange {
  .el-range-input {
    line-height: 30px;
    height: 30px;
  }
  .el-range__close-icon {
    display: none;
  }
  .el-range-separator {
    width: 40px;
    line-height: 33px;
  }
}

.tag-left-right {
  font-size: 10px;
  position: absolute;
  right: -35px;
  text-align: center;
  top: -8px;
  width: 76px;
  line-height: 42px;
  height: 28px;
  z-index: 999;
  color: #fff;
  background: #ff9639;
  transform: rotate(50deg);
}

.reflectance-table {
  font-size: 12px;
  
  th, td {
    padding: 4px !important;
  }
}

.coal-type-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  
  td {
    text-align: center;
    padding: 8px;
    border: 1px solid #CCCCCC;
  }
  
  tr:first-child {
    background-color: #f2f2f2;
  }
  
  tr:nth-child(2) {
    background-color: #f8f8f8;
  }
}

.reflectance-chart-container {
  margin-bottom: 20px;
}

::v-deep {
  .el-divider--vertical {
    margin: 0 5px !important;
  }

  .el-table {
    .el-table__header {
      .cell {
        padding-right: 2px;
        padding-left: 2px;
      }
    }

    .el-table__row {
      .cell {
        min-height: 28px;

        line-height: 28px;
        padding-right: 0;
        padding-left: 0;
      }
    }
  }
  
  .padded-dialog .el-dialog__body {
    padding-left: 10%;
    padding-right: 10%;
  }
}

.padded-dialog {
  padding: 20px;
}

.ash-composition-container {
  display: flex;
  width: 100%;
  margin-top: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f9f9f9;
  padding: 15px 10px;
}

.ash-content {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin: 0 -10px; /* 抵消子元素的padding */
}

.ash-item {
  display: flex;
  align-items: center;
  width: 25%; /* 默认4列 */
  padding: 0 10px;
  margin-bottom: 15px;
  box-sizing: border-box;
  height: 36px; /* 固定高度确保一致性 */
}

.ash-item:nth-last-child(-n+4) {
  margin-bottom: 0;
}

@media screen and (max-width: 1366px) {
  .ash-item {
    width: 33.33%; /* 中等屏幕3列 */
  }
  
  .ash-item:nth-last-child(-n+4) {
    margin-bottom: 15px;
  }
  
  .ash-item:nth-last-child(-n+3) {
    margin-bottom: 0;
  }
}

@media screen and (max-width: 992px) {
  .ash-item {
    width: 50%; /* 小屏幕2列 */
  }
  
  .ash-item:nth-last-child(-n+3) {
    margin-bottom: 15px;
  }
  
  .ash-item:nth-last-child(-n+2) {
    margin-bottom: 0;
  }
}

@media screen and (max-width: 768px) {
  .ash-item {
    width: 100%; /* 移动端1列 */
  }
  
  .ash-item:nth-last-child(-n+2) {
    margin-bottom: 15px;
  }
  
  .ash-item:last-child {
    margin-bottom: 0;
  }
}

.ash-label {
  min-width: 70px;
  width: 70px;
  text-align: right;
  margin-right: 10px;
  white-space: nowrap;
  overflow: visible;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;
}

.ash-value-container {
  display: flex;
  flex: 1;
  min-width: 0;
}

.ash-value {
  display: flex;
  justify-content: flex-end; /* 数值靠右对齐 */
  align-items: center;
  flex: 1;
  min-width: 0;
  height: 36px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  padding: 0 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: #606266;
}

.ash-unit {
  width: 40px;
  min-width: 40px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-left: none;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  color: #909399;
  flex-shrink: 0;
}

.vip-notice {
  color: #ffffff;
  font-size: 16px;
  text-align: center;
  background-color: #b17b38;
  padding: 15px;
  margin: 10px 0;
  border-radius: 4px;
  margin-top: 100px;
}

.tip-popup {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 9999;
  margin-top: 5px;
  width: 320px;
}

.popup-content {
  background-color: #fff1f0;
  padding: 10px 15px;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  border: 1px solid #ffccc7;
  position: relative;
}

.popup-content::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #ffccc7; /* Same as border color */
}

.popup-content::after {
  content: '';
  position: absolute;
  top: -7px;
  left: 21px;
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #fff1f0; /* Same as background color */
}

.tip-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  flex-shrink: 0;
}

.close-icon {
  width: 14px;
  height: 14px;
  cursor: pointer;
  margin-left: auto;
  flex-shrink: 0;
}

.tip-popup-fixed {
  position: fixed;
  top: 335px;
  left: 385px;
  z-index: 9999;
  width: 320px;
}
</style>
