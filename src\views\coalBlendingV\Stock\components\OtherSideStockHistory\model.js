// eslint-disable-next-line

import BaseModel, { TableConfig, FormConfig } from '@/components/Common/SnProTable/model'
import CalcUtils from '@/utils/calcUtils'

import { dateUtil, formatToDateTime } from '@/utils/dateUtils'
import { isEmpty } from '@/utils/is'
import { fetchSetting, MERGE_TYPE } from '@/const'
import axios from 'axios'
import { getToken } from '@/utils/auth'

export const name = `/cwe/a/coal`

const createFormConfig = () => {
  return {
    labelHideAll: false,
    fieldMapToTime: [['_dateRange', ['beginDate', 'endDate'], 'YYYY-MM-DD']],
    fieldMapToNumber: [
      ['ad', ['minAd', 'maxAd']],
      ['std', ['minStd', 'maxStd']],
      ['vdaf', ['minVdaf', 'maxVdaf']],
      ['g', ['minG', 'maxG']],
      ['y', ['minY', 'maxY']],
      ['csr', ['minCsr', 'maxCsr']]
    ],
    filters: [
      {
        label: '日期',
        hideLabel: true,
        prop: '_dateRange',
        component: 'SnDateRanger',
        defaultValue: [],
        componentProp: {
          valueFormat: 'yyyy-MM-dd'
        },
        style: {
          width: '220px'
        }
      },
      {
        label: '品名',
        hideLabel: true,
        prop: 'coalName',
        itemStyle: {
          width: '110px'
        },
        componentProp: {}
      },
      {
        label: '煤种名称',
        hideLabel: true,
        prop: 'coalCategoryName',
        itemStyle: {
          width: '110px'
        },
        componentProp: {}
      },
      {
        label: '产地',
        hideLabel: true,
        itemStyle: {
          width: '100px'
        },
        componentProp: {},
        prop: 'location'
      },
      {
        label: 'Ad',
        prop: 'ad',
        hideLabel: true,
        component: 'SnSimpleInputRanger',
        defaultValue: [],
        style: {
          width: '140px'
        },
        componentProp: {
          useProFormItem: false,
          startPlaceholder: '',
          endPlaceholder: '',
          rangeSeparator: '≤Ad≤ '
        }
      },
      {
        label: 'Std',
        prop: 'std',
        itemStyle: {
          width: '140px'
        },
        hideLabel: true,
        component: 'SnSimpleInputRanger',
        defaultValue: [],
        style: {
          width: '140px'
        },
        componentProp: {
          useProFormItem: false,
          startPlaceholder: '',
          endPlaceholder: '',
          rangeSeparator: '≤Std≤ '
        }
      },
      {
        label: 'Vdaf',
        prop: 'vdaf',
        hideLabel: true,
        component: 'SnSimpleInputRanger',
        defaultValue: [],
        style: {
          width: '140px'
        },
        componentProp: {
          useProFormItem: false,
          startPlaceholder: '',
          endPlaceholder: '',
          rangeSeparator: '≤Vdaf≤ '
        }
      },
      {
        label: 'G',
        prop: 'g',
        hideLabel: true,
        component: 'SnSimpleInputRanger',
        defaultValue: [],
        style: {
          width: '140px'
        },
        componentProp: {
          useProFormItem: false,
          startPlaceholder: '',
          endPlaceholder: '',
          rangeSeparator: '≤G≤ '
        }
      },
      {
        label: 'Y',
        prop: 'y',
        hideLabel: true,
        component: 'SnSimpleInputRanger',
        defaultValue: [],
        style: {
          width: '140px'
        },
        componentProp: {
          useProFormItem: false,
          startPlaceholder: '',
          endPlaceholder: '',
          rangeSeparator: '≤Y≤ '
        }
      },
      {
        label: 'CSR',
        prop: 'csr',
        hideLabel: true,
        component: 'SnSimpleInputRanger',
        defaultValue: [],
        style: {
          width: '150px'
        },
        componentProp: {
          useProFormItem: false,
          startPlaceholder: '',
          endPlaceholder: '',
          rangeSeparator: '≤CSR≤ '
        }
      },
      // {
      //   label: 'Fe2O3≤ ',
      //   itemStyle: {
      //     width: '140px'
      //   },
      //   componentProp: {
      //     placeholder: ' '
      //   },
      //   prop: 'maxFe2O'
      // },
      // {
      //   label: 'CaO≤ ',
      //   itemStyle: {
      //     width: '130px'
      //   },
      //   componentProp: {
      //     placeholder: ' '
      //   },
      //   prop: 'maxCaO'
      // },
      {
        label: 'MF≥ ',
        itemStyle: {
          width: '130px'
        },
        componentProp: {
          placeholder: ' '
        },
        prop: 'minProcMf'
      }
    ]
  }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
  return {
    mountedQuery: false,
    showOpt: false, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: true, // 显示选择列
    useApiList: false,
    optConfig: { width: 150, label: '操作', prop: 'opt', fixed: 'right' },
    columns: [
      {
        prop: 'date',
        label: '更新日期',
        width: 100,
        slot: 'date',
        fixed: true,
        align: 'center'
      },
      {
        label: '煤种名称',
        prop: 'coalCategoryName',
        fixed: true,

        width: 70
      },
      {
        label: '品名',
        prop: 'coalName',
        slot: 'coalName',
        isEdit: true,
        width: 110,
        fixed: true,
        renderHeader: function(h) {
            return h('div', {
                style: {
                    width: '100%',
                    position: 'relative',
                    textAlign: 'center'
                }
            }, [
                h('span', '品名'),
                h('img', {
                    attrs: {
                        src: '/img/shuju.png',
                        alt: '数据趋势'
                    },
                    style: {
                        width: '16px',
                        height: '16px',
                        position: 'absolute',
                        right: '20px',
                        top: '50%',
                        transform: 'translateY(-50%)'
                    }
                })
            ]);
        }
    },
      // {
      //     label: '来源',
      //     prop: 'coalSource',
      //     width: 100,
      //     // slot: 'coalSource',
      //     // isEdit: true,
      //     format: 'dict|b_coal_source_type'
      // },
      // cleanMt	水分
      // cleanAd	灰分
      // cleanStd   硫
      // cleanVdaf	挥发分
      // procG	粘结
      // procY	Y值
      // procX	X值
      // macR0	反射率
      // macS	标准差
      // cokeIndexCri cri
      // cokeIndexCsr   csr
      // area		产地
      // {
      //   prop: 'stock',
      //   label: '库存',
      //   align: 'center',
      //   width: 70

      // },
      {
        prop: 'cleanMt',
        label: '水分Mt%',
        align: 'center',
        width: 70

      },
      {
        prop: 'cleanAd',
        label: '灰分Ad%',
        align: 'center',
        isEdit: true
        // slot: 'ad',
      },
      {
        prop: 'cleanStd',
        label: '硫分St,d%',
        align: 'center'
      },
      {
        prop: 'cleanVdaf',
        label: '挥发分Vdaf%',
        width: 90,
        align: 'center',
        isEdit: true
        // slot: 'vdaf',
        // isEdit: true,
      },
      {
        prop: 'procG',
        label: '粘结G',
        align: 'center',
        width: 80
      },
      {
        prop: 'procY',
        align: 'center',
        width: 80,
        label: 'Y/mm'
      },
      {
        prop: 'procX',
        align: 'center',
        width: 80,
        label: 'X/mm'
      },
      {
        'label': '反射率',
        width: 80,
        'prop': 'macR0',
        decimalPlaces: 3,
        formatter: function(row) {
          if (row.macR0 !== undefined && row.macR0 !== null) {
            return Number(row.macR0).toFixed(3);
          }
        }
      },
      {
        'label': '标准差',
        width: 80,
        'prop': 'macS',
        decimalPlaces: 3,
        formatter: function(row) {
          if (row.macS !== undefined && row.macS !== null) {
            return Number(row.macS).toFixed(3);
          }
        }
      },
      {
        'label': 'CRI%',
        width: 80,
        'prop': 'cokeIndexCri'
      },
      {
        'label': 'CSR%',
        width: 80,
        'prop': 'cokeIndexCsr',
      },

      // {
      //   'label': '不含税煤价',
      //   width: 100,
      //   'prop': 'activePriceCoalPriceNoTax'

      // },
      {
        'label': '含税煤价',
        width: 100,
        'prop': 'factoryPrice'
      },
      { prop: 'rise', slot: 'rise', label: '日涨跌', width: 100, isShow: true },
      { prop: 'monthRise', slot: 'monthRise', label: '月涨跌', width: 100, isShow: true },
      { prop: 'rangeRise', slot: 'rangeRise', label: '日期范围涨跌', width: 100, isShow: true },
      // {
      //   'label': '不含税运费',
      //   width: 100,
      //   'prop': 'activePriceTransportPriceNoTax'
      // },
      // {
      //   'label': '路耗%',
      //   'prop': 'activePriceTransportCostP1'
      // },
     
      // {
      //   'label': '不含税到厂煤价',
      //   width: 120,
      //   slot: 'activePriceFactoryPriceNoTax'
      // },
      // {
      //   'label': '折水标准',
      //   'prop': 'reduceMtStandard'
      // },
      // {
      //   'label': '折水不含税进厂价',
      //   width: 120,
      //   'prop': 'reduceMtFactoryPriceNoTax'
      // },
      // {
      //   'label': '配煤成本',
      //   'prop': 'coalBlendingCost'
      // },
      {
        'label': '产地',
        width: 100,
        'prop': 'location'
      },
      {
        'label': '备注',
        'prop': 'remarks',
        showOverflowTooltip: true,
        width: 100
      },
      {
        'label': 'MF/ddpm',
        'prop': 'coalIndexMfddpm'
      },
      {
        'label': '奥亚膨胀b%',
        'prop': 'procB'
      },
      {
        'label': '奥亚收缩a%',
        'prop': 'procA'
      },
      // {
      //   'label': '0.25-0.30',
      //   'prop': '0.25-0.30',
      //   formatter: function(row) {
      //     if (row['0.25-0.30']) {
      //       return row['0.25-0.30'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.30-0.35',
      //   'prop': '0.30-0.35',
      //   formatter: function(row) {
      //     if (row['0.30-0.35']) {
      //       return row['0.30-0.35'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.35-0.40',
      //   'prop': '0.35-0.40',
      //   formatter: function(row) {
      //     if (row['0.35-0.40']) {
      //       return row['0.35-0.40'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.40-0.45',
      //   'prop': '0.40-0.45',
      //   formatter: function(row) {
      //     if (row['0.40-0.45']) {
      //       return row['0.40-0.45'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.45-0.50',
      //   'prop': '0.45-0.50',
      //   formatter: function(row) {
      //     if (row['0.45-0.50']) {
      //       return row['0.45-0.50'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.50-0.55',
      //   'prop': '0.50-0.55',
      //   formatter: function(row) {
      //     if (row['0.50-0.55']) {
      //       return row['0.50-0.55'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.55-0.60',
      //   'prop': '0.55-0.60',
      //   formatter: function(row) {
      //     if (row['0.55-0.60']) {
      //       return row['0.55-0.60'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.60-0.65',
      //   'prop': '0.60-0.65',
      //   formatter: function(row) {
      //     if (row['0.60-0.65']) {
      //       return row['0.60-0.65'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.65-0.70',
      //   'prop': '0.65-0.70',
      //   formatter: function(row) {
      //     if (row['0.65-0.70']) {
      //       return row['0.65-0.70'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.70-0.75',
      //   'prop': '0.70-0.75',
      //   formatter: function(row) {
      //     if (row['0.70-0.75']) {
      //       return row['0.70-0.75'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.75-0.80',
      //   'prop': '0.75-0.80',
      //   formatter: function(row) {
      //     if (row['0.75-0.80']) {
      //       return row['0.75-0.80'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.80-0.85',
      //   'prop': '0.80-0.85',
      //   formatter: function(row) {
      //     if (row['0.80-0.85']) {
      //       return row['0.80-0.85'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.85-0.90',
      //   'prop': '0.85-0.90',
      //   formatter: function(row) {
      //     if (row['0.85-0.90']) {
      //       return row['0.85-0.90'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.90-0.95',
      //   'prop': '0.90-0.95',
      //   formatter: function(row) {
      //     if (row['0.90-0.95']) {
      //       return row['0.90-0.95'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '0.95-1.00',
      //   'prop': '0.95-1.00',
      //   formatter: function(row) {
      //     if (row['0.95-1.00']) {
      //       return row['0.95-1.00'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.00-1.05',
      //   'prop': '1.00-1.05',
      //   formatter: function(row) {
      //     if (row['1.00-1.05']) {
      //       return row['1.00-1.05'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.05-1.10',
      //   'prop': '1.05-1.10',
      //   formatter: function(row) {
      //     if (row['1.05-1.10']) {
      //       return row['1.05-1.10'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.10-1.15',
      //   'prop': '1.10-1.15',
      //   formatter: function(row) {
      //     if (row['1.10-1.15']) {
      //       return row['1.10-1.15'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.15-1.20',
      //   'prop': '1.15-1.20',
      //   formatter: function(row) {
      //     if (row['1.15-1.20']) {
      //       return row['1.15-1.20'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.20-1.25',
      //   'prop': '1.20-1.25',
      //   formatter: function(row) {
      //     if (row['1.20-1.25']) {
      //       return row['1.20-1.25'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.25-1.30',
      //   'prop': '1.25-1.30',
      //   formatter: function(row) {
      //     if (row['1.25-1.30']) {
      //       return row['1.25-1.30'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.30-1.35',
      //   'prop': '1.30-1.35',
      //   formatter: function(row) {
      //     if (row['1.30-1.35']) {
      //       return row['1.30-1.35'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.35-1.40',
      //   'prop': '1.35-1.40',
      //   formatter: function(row) {
      //     if (row['1.35-1.40']) {
      //       return row['1.35-1.40'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.40-1.45',
      //   'prop': '1.40-1.45',
      //   formatter: function(row) {
      //     if (row['1.40-1.45']) {
      //       return row['1.40-1.45'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.45-1.50',
      //   'prop': '1.45-1.50',
      //   formatter: function(row) {
      //     if (row['1.45-1.50']) {
      //       return row['1.45-1.50'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.50-1.55',
      //   'prop': '1.50-1.55',
      //   formatter: function(row) {
      //     if (row['1.50-1.55']) {
      //       return row['1.50-1.55'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.55-1.60',
      //   'prop': '1.55-1.60',
      //   formatter: function(row) {
      //     if (row['1.55-1.60']) {
      //       return row['1.55-1.60'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.60-1.65',
      //   'prop': '1.60-1.65',
      //   formatter: function(row) {
      //     if (row['1.60-1.65']) {
      //       return row['1.60-1.65'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.65-1.70',
      //   'prop': '1.65-1.70',
      //   formatter: function(row) {
      //     if (row['1.65-1.70']) {
      //       return row['1.65-1.70'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.70-1.75',
      //   'prop': '1.70-1.75',
      //   formatter: function(row) {
      //     if (row['1.70-1.75']) {
      //       return row['1.70-1.75'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.75-1.80',
      //   'prop': '1.75-1.80',
      //   formatter: function(row) {
      //     if (row['1.75-1.80']) {
      //       return row['1.75-1.80'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.80-1.85',
      //   'prop': '1.80-1.85',
      //   formatter: function(row) {
      //     if (row['1.80-1.85']) {
      //       return row['1.80-1.85'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.85-1.90',
      //   'prop': '1.85-1.90',
      //   formatter: function(row) {
      //     if (row['1.85-1.90']) {
      //       return row['1.85-1.90'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.90-1.95',
      //   'prop': '1.90-1.95',
      //   formatter: function(row) {
      //     if (row['1.90-1.95']) {
      //       return row['1.90-1.95'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '1.95-2.00',
      //   'prop': '1.95-2.00',
      //   formatter: function(row) {
      //     if (row['1.95-2.00']) {
      //       return row['1.95-2.00'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '2.00-2.05',
      //   'prop': '2.00-2.05',
      //   formatter: function(row) {
      //     if (row['2.00-2.05']) {
      //       return row['2.00-2.05'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '2.05-2.10',
      //   'prop': '2.05-2.10',
      //   formatter: function(row) {
      //     if (row['2.05-2.10']) {
      //       return row['2.05-2.10'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '2.10-2.15',
      //   'prop': '2.10-2.15',
      //   formatter: function(row) {
      //     if (row['2.10-2.15']) {
      //       return row['2.10-2.15'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '2.15-2.20',
      //   'prop': '2.15-2.20',
      //   formatter: function(row) {
      //     if (row['2.15-2.20']) {
      //       return row['2.15-2.20'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '2.20-2.25',
      //   'prop': '2.20-2.25',
      //   formatter: function(row) {
      //     if (row['2.20-2.25']) {
      //       return row['2.20-2.25'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '2.25-2.30',
      //   'prop': '2.25-2.30',
      //   formatter: function(row) {
      //     if (row['2.25-2.30']) {
      //       return row['2.25-2.30'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '2.30-2.35',
      //   'prop': '2.30-2.35',
      //   formatter: function(row) {
      //     if (row['2.30-2.35']) {
      //       return row['2.30-2.35'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '2.35-2.40',
      //   'prop': '2.35-2.40',
      //   formatter: function(row) {
      //     if (row['2.35-2.40']) {
      //       return row['2.35-2.40'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '2.40-2.45',
      //   'prop': '2.40-2.45',
      //   formatter: function(row) {
      //     if (row['2.40-2.45']) {
      //       return row['2.40-2.45'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '2.45-2.50',
      //   'prop': '2.45-2.50',
      //   formatter: function(row) {
      //     if (row['2.45-2.50']) {
      //       return row['2.45-2.50'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '2.50-2.55',
      //   'prop': '2.50-2.55',
      //   formatter: function(row) {
      //     if (row['2.50-2.55']) {
      //       return row['2.50-2.55'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': '2.55-2.60',
      //   'prop': '2.55-2.60',
      //   formatter: function(row) {
      //     if (row['2.55-2.60']) {
      //       return row['2.55-2.60'].toFixed(1);
      //     }
      //     return '';
      //   }
      // },
      // {
      //   'label': 'Vd%',
      //   'prop': 'vd'
      // },
      // {
      //   'label': 'SiO2%',
      //   'prop': 'siO2'

      // },

      // {
      //   'label': 'Al2O3%',
      //   'prop': 'al2O3'

      // },
      // {
      //   'label': 'Fe2O3%',
      //   'prop': 'fe2O3'

      // },

      // {
      //   'label': 'CaO%',
      //   'prop': 'caO'

      // },

      // {
      //   'label': 'K2O%',
      //   'prop': 'k2O'

      // },

      // {
      //   'label': 'Na2O%',
      //   'prop': 'na2O'

      // },

      // {
      //   'label': 'MgO%',
      //   'prop': 'mgO'

      // },

      // {
      //   'label': 'TiO2%',
      //   'prop': 'tiO2'

      // },

      // {
      //   'label': 'MnO2%',
      //   'prop': 'mnO2'

      // },

      // {
      //   'label': 'P205%',
      //   'prop': 'p2O5'

      // },

      // {
      //   'label': 'SO3%',
      //   'prop': 'so3',
      //   formatter: function(row) {
      //     if (row.so3 !== undefined && row.so3 !== null) {
      //       return row.so3;
      //     }
      //     return '0.0';
      //   }
      // },

      // {
      //   'label': 'MnO2%',
      //   'prop': 'mnO2',
      //   formatter: function(row) {
      //     if (row.mnO2 !== undefined && row.mnO2 !== null) {
      //       return row.mnO2;
      //     }
      //     return '0.0';
      //   }
      // },

      {
        'label': 'MCI%',
        'prop': 'mci'

      },


      {
        'label': '活惰比',
        'prop': 'activeInertiaRatio'
      },
      {
        'label': '分类',
        'prop': 'categoryType'
      },
      {
        label: '分析煤种',
        prop: 'analysisCategoryName',
        width: 80
      }
    ]
  }
}

export const getEditCols = () => {
  return createTableConfig().columns.filter(v => v.isEdit)
}

export class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }

  static formatSubmitData(data) {
    data.data.forEach(v => {
      // 不含税煤价为空，含税煤价有值时
      if (!v.activePriceCoalPriceNoTax && v.activePriceCoalPriceWithTax) {
        v.activePriceCoalPriceNoTax = CalcUtils.getShowValue(CalcUtils.divide(v.activePriceCoalPriceWithTax, 1.13), 2)
      }
      if (!v.activePriceCoalPriceWithTax && v.activePriceCoalPriceNoTax) {
        v.activePriceCoalPriceWithTax = CalcUtils.getShowValue(CalcUtils.multiply(v.activePriceCoalPriceNoTax, 1.13), 2)
      }
      const isEmpty = (item) => ['', undefined, null].includes(item)
      if (!isEmpty(v.activePriceFactoryPriceNoTax)) {
        v.activePriceFactoryPriceNoTax = CalcUtils.getShowValue(v.activePriceFactoryPriceNoTax, 2)
      }
      if (!isEmpty(v.reduceMtFactoryPriceNoTax)) {
        v.reduceMtFactoryPriceNoTax = CalcUtils.getShowValue(v.reduceMtFactoryPriceNoTax, 2)
      }
      if (!isEmpty(v.coalBlendingCost)) {
        v.coalBlendingCost = CalcUtils.getShowValue(v.coalBlendingCost, 2)
      }
      
      // 确保factoryPrice字段被正确设置 (含税煤价)
      if (!isEmpty(v.activePriceCoalPriceWithTax)) {
        v.factoryPrice = v.activePriceCoalPriceWithTax;
      }
    })
  }

  get(query) {
    return super.request({
      url: `/cwe/a/coalDatabase/data/get`,
      method: 'get',
      params: query
    })
  }
  getByPrice(query) {//无csr数据
    return super.request({
      url: `/cwe/a/coalSourceOutside/getByPrice`,
      method: 'get',
      params: query
    })
  }

  /**
   * 获取掌上煤焦煤源详情
   * @param id 煤炭ID
   * @returns {Promise<*>}
   */
  getZsmyCoalById(id) {
    return super.request({
      url: `/cwe/a/coalSourceOutside/getOutsideById`,
      method: 'get',
      params: { id }
    })
  }

  async page(query) {
    const resp = await super.request({
      // url: `/cwe/a/coalDatabase/data/zsmy/page`
      url: `/cwe/a/coalSourceOutside/pageByCoalCategory`,
      method: 'get',
      params: query
    })

    const getRangeValues = (v) => {
      try {
        const proportionValues = v.proportionContent ? JSON.parse(v.proportionContent) : []
        const rangeNameValues = v.rangeContent ? JSON.parse(v.rangeContent) : []
        const rangeValues = proportionValues.map((_, index) => ({
          rangeName: rangeNameValues[index],
          proportion: proportionValues[index]
        }))
        return rangeValues
      } catch (e) {
        console.log(e)
        return []
      }
    }

    resp.data[fetchSetting.listField].forEach(v => {
      v.rangeValues = getRangeValues(v)
    })
    return resp
  }

  添加到我的煤源
  addToMyCoalSource(data) {
    const token = getToken();
    const jsonData = JSON.stringify({
      ids: data.data.map(item => item.id)
    });
    console.log('Sending data to API:', jsonData);
    
    return axios({
      url: `${process.env.VUE_APP_CENTER}/cwe/a/coal/saveCoalSourceOutsideByCoal`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        'Authorization': token,
        'App-Key': process.env.VUE_APP_APP_KEY,
        'App-Secret': process.env.VUE_APP_APP_SECRET,
        'App-Ver': process.env.VUE_APP_APP_VER,
        'timestamp': new Date().getTime()
      },
      data: jsonData
    });
  }

  add(data) {
    Model.formatSubmitData(data)
    return super.request({
      url: `/cwe/a/coalDatabase/data/add`,
      method: 'post',
      data: {
        ...data,
        ContentType: 'application/json'
      }
    })
  }

  /**
   * 更新
   * @param data
   */
  update(data) {
    Model.formatSubmitData(data)
    return super.request({
      url: `/cwe/a/coalDatabase/data/update`,
      method: 'post',
      data: {
        ...data,
        ContentType: 'application/json'
      }
    })
  }


  /**
   * 合并外部
   */
  mergeData(data) {
    return super.request({
      url: `/cwe/a/coalDatabase/data/merge`,
      method: 'post',
      data: {
        ...data,
        ContentType: 'application/json'
      }
    })
  }


  /**
   * 批量改折水、路耗
   */
  batchUpdateMt(data) {
    return super.request({
      url: `/cwe/a/coalDatabase/data/reduce-mt-transport-cost/batch-update`,
      method: 'post',
      data: {
        ...data,
        ContentType: 'application/json'
      }
    })
  }


  sortTop({ id, sort, coalSource }) {
    console.log('置顶请求参数:', { id, sort, coalSource, typeParam: coalSource });
    
    // 识别数据来源，使用不同的API
    let url;
    if (coalSource === 'zsmy') {
      // 掌上煤焦数据使用特殊的API
      url = `/cwe/a/coalSourceOutside/${sort ? 'un-sort-top' : 'sort-top'}`;
    } else {
      // 修复多余的斜杠问题
      url = `/cwe/a/coalDatabase/data/${sort ? 'un-sort-top' : 'sort-top'}`;
    }
    
    return super.request({
      url,
      method: 'get',
      params: { id, type: coalSource }
    }).then(res => {
      console.log('置顶请求响应:', res);
      return res;
    }).catch(err => {
      console.error('置顶请求失败:', err);
      return false;
    });
  }

  async remove({ id, coalSource, useConfirm = false, type }) {
    const fetchFn = () => {
      // 根据数据来源选择不同的删除API
      let url = `/cwe/a/coalDatabase/data/delete`;
      
      // 如果是合并数据，则使用取消合并的API
      if (type === MERGE_TYPE.MERGE) {
        url = `/cwe/a/coalDatabase/data/un-merge`;
      } 
      // 如果是掌上煤焦数据，则使用掌上煤焦专用的删除API
      else if (coalSource === 'zsmy') {
        url = `/cwe/a/coalSourceOutside/delete`;
      }
      
      return super.request({
        url: url,
        method: 'get',
        params: { id, coalSource }
      });
    };

    if (useConfirm) {
      try {
        const ctx = type === MERGE_TYPE.MERGE ? '是否取消合并?' : '是否确认删除当前数据?'
        const res = await super.modal.confirm(ctx)
        return fetchFn()
      } catch (error) {
        return Promise.reject(error)
      }
    } else {
      return fetchFn()
    }
  }

  /**
   * 获取灰成分参考值
   * @param id 煤炭ID
   * @returns {Promise<*>}
   */
  getAshReferenceValues(id) {
    console.log('获取灰成分参考值 - 请求参数:', { id });
    if (!id) {
      console.error('煤炭ID为空，无法获取灰成分参考值');
      return Promise.reject(new Error('煤炭ID为空'));
    }
    
    return super.request({
      url: `/cwe/a/coalSourceOutside/getAshReferenceValues`,
      method: 'get',
      params: { id }
    }).then(response => {
      console.log('获取灰成分参考值 - 响应数据:', response);
      return response;
    }).catch(error => {
      console.error('获取灰成分参考值 - 请求失败:', error);
      throw error;
    })
  }

}

export default new Model()
