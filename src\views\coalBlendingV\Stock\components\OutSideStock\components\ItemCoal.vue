<template>
    <el-dialog :append-to-body="true" :before-close="()=>handleClose('full')"
               :title="details.coalName || '更新'"
               :visible.sync="visible" class="add"
               top="1vh" width="80%">
        <div class="coal" v-if="visible">
            <el-form ref="entityForm" :model="entityForm" label-width="80px">
                <indicators-card v-if="entityForm.basicinformation">
                    <template #title>
                        <div class="title-content">
                            <span>基础信息</span>
                        </div>
                    </template>

                    <el-col>
                        <EditTable ref="editTable"
                                   v-model="entityForm.basicinformation" :columns="getColumnsInfo"
                                   :rowHeaders="true"
                                   @inited="handleEditTableInitInfo">
                        </EditTable>
                    </el-col>
                </indicators-card>


                <indicators-card title="指标" v-if="entityForm.indexlist">
                    <el-col>
                        <EditTable ref="editTable2" v-model="entityForm.indexlist" :columns="getColumns"
                                   :rowHeaders="true"
                                   @inited="handleEditTableInit">
                        </EditTable>
                    </el-col>

                </indicators-card>
                
                <div v-if="showVipNotification" class="vip-notification">
                    <i class="el-icon-warning-outline" style="margin-right: 5px;"></i> 很抱歉，VIP才能查看煤岩及灰成分详细数据，如有需求请与我们联系
                </div>
                
                <indicators-card
                        :subtitle="`(当前反射率之和为${entityForm.rate>0?entityForm.rate:'空'},反射率要求在99.5到100.5之间)`"
                        title="反射率分布表">
                    <!-- <el-row>
                      <el-col :span="6" v-for="item in entityForm.coalRockList" :key="item.sort">
                        <el-form-item :label="item.rangeName">
                          <el-input v-model="item.proportion" type="number" @input="changeRate">
                            <template slot="append">(%)</template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                    </el-row> -->
                    <el-row :gutter="80" type="flex">
                        <el-col>
                            <EditTable ref="editTable1" v-model="editData1" :colHeaders="true" :columns="getColumns1"
                                       @inited="handleEditTableInit1"></EditTable>
                        </el-col>
                    </el-row>
                </indicators-card>

                <indicators-card title="图表信息">
                    <template v-if="entityForm && entityForm.coalTypeProportionList.length">
                        <new-column-chart :impChartData="entityForm" isShowLabel style="width: 100%; height: 300px;"/>
                        <el-table :data="entityForm.coalTypeProportionList" :show-header="false" border
                                  class="chart-table">
                            <el-table-column prop="brownCoal"/>
                            <el-table-column prop="longFlame"/>
                            <el-table-column prop="gasCoal"/>
                            <el-table-column prop="thirdCokingCoal"/>
                            <el-table-column prop="fatCoal"/>
                            <el-table-column prop="cokingCoal"/>
                            <el-table-column prop="leanCoal"/>
                            <el-table-column prop="meagerLeanCoal"/>
                            <el-table-column prop="meagerCoal"/>
                            <el-table-column prop="smokelessCoal"/>
                        </el-table>
                    </template>
                    <template v-else-if="isZsmyType">

                    </template>
                </indicators-card>

                <indicators-card title="煤灰成分">
                    <div class="ash-composition-container">
                        <div class="ash-content">
                            <div class="ash-row">
                                <div class="ash-column">
                                    <span class="ash-label">SiO<sub>2</sub></span>
                                    <div class="ash-value-container">
                                        <el-input v-model="entityForm.siO2" name="siO2" class="ash-input" type="text" @input="formatInputValue('siO2')"></el-input>
                                        <span class="ash-unit">(%)</span>
                                    </div>
                                </div>
                                <div class="ash-column">
                                    <span class="ash-label">Al<sub>2</sub>O<sub>3</sub></span>
                                    <div class="ash-value-container">
                                        <el-input v-model="entityForm.al2O3" name="al2O3" class="ash-input" type="text" @input="formatInputValue('al2O3')"></el-input>
                                        <span class="ash-unit">(%)</span>
                                    </div>
                                </div>
                                <div class="ash-column">
                                    <span class="ash-label">Fe<sub>2</sub>O<sub>3</sub></span>
                                    <div class="ash-value-container">
                                        <el-input v-model="entityForm.fe2O3" name="fe2O3" class="ash-input" type="text" @input="formatInputValue('fe2O3')"></el-input>
                                        <span class="ash-unit">(%)</span>
                                    </div>
                                </div>
                                <div class="ash-column">
                                    <span class="ash-label">CaO</span>
                                    <div class="ash-value-container">
                                        <el-input v-model="entityForm.caO" name="caO" class="ash-input" type="text" @input="formatInputValue('caO')"></el-input>
                                        <span class="ash-unit">(%)</span>
                                    </div>
                                </div>
                            
                                <div class="ash-column">
                                    <span class="ash-label">MgO</span>
                                    <div class="ash-value-container">
                                        <el-input v-model="entityForm.mgO" name="mgO" class="ash-input" type="text" @input="formatInputValue('mgO')"></el-input>
                                        <span class="ash-unit">(%)</span>
                                    </div>
                                </div>
                                <div class="ash-column">
                                    <span class="ash-label">Na<sub>2</sub>O</span>
                                    <div class="ash-value-container">
                                        <el-input v-model="entityForm.na2O" name="na2O" class="ash-input" type="text" @input="formatInputValue('na2O')"></el-input>
                                        <span class="ash-unit">(%)</span>
                                    </div>
                                </div>
                                <div class="ash-column">
                                    <span class="ash-label">K<sub>2</sub>O</span>
                                    <div class="ash-value-container">
                                        <el-input v-model="entityForm.k2O" name="k2O" class="ash-input" type="text" @input="formatInputValue('k2O')"></el-input>
                                        <span class="ash-unit">(%)</span>
                                    </div>
                                </div>
                                <div class="ash-column">
                                    <span class="ash-label">TiO<sub>2</sub></span>
                                    <div class="ash-value-container">
                                        <el-input v-model="entityForm.tiO2" name="tiO2" class="ash-input" type="text" @input="formatInputValue('tiO2')"></el-input>
                                        <span class="ash-unit">(%)</span>
                                    </div>
                                </div>
                            
                                <div class="ash-column">
                                    <span class="ash-label">P<sub>2</sub>O<sub>5</sub></span>
                                    <div class="ash-value-container">
                                        <el-input v-model="entityForm.p2O5" name="p2O5" class="ash-input" type="text" @input="formatInputValue('p2O5')"></el-input>
                                        <span class="ash-unit">(%)</span>
                                    </div>
                                </div>
                                <div class="ash-column">
                                    <span class="ash-label">SO<sub>3</sub></span>
                                    <div class="ash-value-container">
                                        <el-input v-model="entityForm.so3" name="so3" class="ash-input" type="text" @input="formatInputValue('so3')"></el-input>
                                        <span class="ash-unit">(%)</span>
                                    </div>
                                </div>
                                <div class="ash-column">
                                    <span class="ash-label">MnO<sub>2</sub></span>
                                    <div class="ash-value-container">
                                        <el-input v-model="entityForm.mnO2" name="mnO2" class="ash-input" type="text" @input="formatInputValue('mnO2')"></el-input>
                                        <span class="ash-unit">(%)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <template v-if="isAshEmpty && isZsmyType">
                    </template>
                </indicators-card>

                <!-- <indicators-card title="价格趋势图">
                    <div class="price-trend-section">
                        <sn-date-ranger v-model="dateRange" style="width: 250px;margin-bottom: 10px;" @change="getByPrice()"></sn-date-ranger>
                        <div ref="chart" style="width: 100%;height: 300px;border: 1px solid #CCCCCC"></div>
                    </div>
                </indicators-card> -->

                <indicators-card title="价格趋势图" v-if="showPriceTrend">
                    <div class="price-trend-section">
                        <sn-date-ranger v-model="dateRange" style="width: 250px;margin-bottom: 10px;" @change="getByPrice()"></sn-date-ranger>
                        <div ref="chart" style="width: 100%;height: 300px;border: 1px solid #CCCCCC"></div>
                    </div>
                </indicators-card>

                <indicators-card title="备注">
                    <el-form-item class="textarea" prop="remarks">
                        <el-input v-model="entityForm.remarks" :rows="6" placeholder="请填写备注信息~" type="textarea">
                        </el-input>
                    </el-form-item>
                </indicators-card>

                <indicators-card title="附件信息">
                    <el-row :gutter="80" type="flex">
                        <el-col>
                            <el-form-item label="">
                                <el-collapse v-model="collapse">
                                    <el-collapse-item name="1" title="上传附件">
                                        <div class="upload">
                                            <div class="upload-list">
                                                <div v-for="(item,index) in uploadList" :key="index"
                                                     class="up-load-warp">
                                                    <i class="el-icon-close icon"
                                                       @click="handleRemoveUpload(index)"></i>
                                                    <span href="#" @click="handleViewUrl(item.uri)"
                                                          :underline=" false"
                                                          class="up-load-warp-link">{{ item.display }}
                                                    </span>
                                                </div>
                                                <upload-attachment ref="upload" :limitNum="limitNum" :size="3"
                                                                   :uploadData="uploadData"
                                                                   accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg"
                                                                   class="upload-attachment"
                                                                   listType="text"
                                                                   source="coalSample"
                                                                   @deleteNotify="handleUploadDelete"
                                                                   @successNotify="handleUploadSuccess"/>
                                            </div>
                                        </div>

                                    </el-collapse-item>
                                </el-collapse>

                            </el-form-item>
                        </el-col>
                    </el-row>
                </indicators-card>
            </el-form>
        </div>
        <div v-if="dialogStatus!=='watch'" slot="footer" class="dialog-footer">
            <el-button class="dialog-footer-btns" @click="handleCloseV">
                取消
            </el-button>
            <el-button :loading="entityFormLoading" class="dialog-footer-btns" type="primary"
                       @click="makeSure('t')">保存
            </el-button>
        </div>

        <ElImageViewer v-if="showPreview" :url-list="urlList" :on-close="()=>{showPreview=false}"/>


    </el-dialog>
</template>

<script>
import Func from '@/utils/func'
import {dateFormat} from '@/filters'
import NewColumnChart from '@/components/Chart/NewColumnChart'
import {IndicatorsCard, IndicatorsTable} from '@/components/Indicators/index'
import {getCoalById, coalAshList, isCheckCoal, saveCoal, getRangeListApi} from '@/api/coal'
import {deepClone} from '@/utils/index'
import EditTable from '@/components/EditTable/index.vue'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import echarts from 'echarts'
import dayjs from 'dayjs'
import SnDateRanger from '@/components/Common/SnDateRanger/index.vue'
import Handsontable from 'handsontable'

const entityForm = () => {
    return {
        id: '',
        userId: '',
        name: '',
        batchCode: dateFormat(new Date(), 'yyyymmdd'),
        type: '',
        province: '',
        factoryPrice: '',
        transitFee: '',
        roadCost: '',
        arrivePrice: '',
        arriveFactory: 'JT',
        mineDepth: '',
        rawMt: '',
        rawAd: '',
        rawPointFive: '',
        rawOnePointFour: '',
        rawAdIn: '',
        rawStd: '',
        rawVdaf: '',
        rawG: '',
        cleanVdaf: '',
        cleanVd: '',
        cleanAd: '',
        cleanStd: '',
        cleanMt: '',
        cleanP: '',
        cleanMci: '',
        procG: '',
        procY: '',
        procX: '',
        procMf: '',
        procTp: '',
        procTmax: '',
        procTk: '',
        procCrc: '',
        procA: '',
        procB: '',
        macR0: '',
        macS: '',
        macV: '',
        macI: '',
        macE: '',
        comSiO2: '',
        comAl2O3: '',
        comFe2O3: '',
        comCaO: '',
        comMgO: '',
        comNa2O: '',
        comK2O: '',
        comTiO2: '',
        comP2O5: '',
        comSO3: '',
        cfeCp: '',
        cfeCe: '',
        qualScon: '',
        qualPcon: '',
        qualM40: '',
        qualM10: '',
        qualCsr: '',
        qualCri: '',
        qualTestCond: '',
        crGk: '',
        crBk: '',
        crTk: '',
        city: '',
        dataType: '',
        createBy: '',
        createDate: '',
        updateBy: '',
        updateDate: '',
        remarks: '',
        ext: '',
        location: '',
        area: '',
        longitude: '',
        latitude: '',
        isFavorite: '-',
        rawPrice: '',
        cri: '',
        csr: '',
        coalTypeProportionList: [],
        indexlist: [],
        basicinformation: [],
        allListRange: []
    }
}
export default {
    name: 'coal',
    components: {
        EditTable,
        IndicatorsCard,
        IndicatorsTable,
        NewColumnChart,
        ElImageViewer,
        SnDateRanger
    },
    data() {
        return {
            entityForm: entityForm(),
            entityFormLoading: false,
            entityFormLoadingV: false,
            isChangeCoal: false,
            type: '',
            // visible: false,
            editData1: [
                ...Array(10)
                    .fill(null)
                    .map(() => ({}))
            ],
            collapse: '1',
            limitNum: 1000,
            uploadList: [], // 用于展示
            uploadData: {refId: '', refType: 'CoalSample'},
            showPreview: false,
            urlList: [],
            priceTrendChart: null,
            showPriceTrend: false, // 控制价格趋势图是否显示
            priceTrendData: {
                area: '',
                cleanAd: '',
                cleanMt: '',
                cleanStd: '',
                cleanVdaf: '',
                coalBlendingCost: '',
                coalCategoryName: '',
                coalName: '',
                date: '',
                macR0: '',
                macS: '',
                priceJson: '',
                procG: '',
                procX: '',
                procY: '',
                csr: ''
            },
            priceTrendQuery: {
                id: '',
                beginDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss'),
                endDate: dayjs().format('YYYY-MM-DD HH:mm:ss')
            },
            allListRange: [], // 缓存反射率范围数据，避免重复请求
            rangeAllList: [] // 存储所有反射率范围数据，用于渲染Rran范围列
        }
    },
    computed: {
        // 判断是否为掌上煤源类型
        isZsmyType() {
            return this.details && (this.details.type === 'zsmy' || this.entityForm.type === 'zsmy');
        },
        // 判断煤灰成分是否为空
        isAshEmpty() {
            const ashFields = ['siO2', 'al2O3', 'fe2O3', 'caO', 'mgO', 'na2O', 'k2O', 'tiO2', 'p2O5', 'so3', 'mnO2'];
            return !ashFields.some(field => {
                const value = this.entityForm[field];
                return value !== null && value !== undefined && value !== '';
            });
        },
        visible: {
            get() {
                return this.addVisible
            },
            set(val) {
                this.$emit('update:addVisible', val)
            }
        },
        region: {
            set(val) {
                this.entityForm = Object.assign(this.entityForm, {
                    province: val[0],
                    city: val[1],
                    area: val[2]
                })
            },
            get() {
                return [this.entityForm.province, this.entityForm.city, this.entityForm.area]
            }
        },
        showVipNotification() {
            // 如果是外部煤源(wb)或内部煤源(nb)，不显示VIP提示
            if (this.entityForm.type === 'wb' || this.entityForm.type === 'nb' || 
                this.details.type === 'wb' || this.details.type === 'nb') {
                return false;
            }
            
            // 检查是否是掌上煤源类型
            const isZsmyType = this.details && (this.details.type === 'zsmy' || this.entityForm.type === 'zsmy');
            if (!isZsmyType) {
                return false;
            }
            
            // 检查反射率分布表中是否有***值
            let hasAsterisks = false;
            if (this.editData1 && this.editData1.length > 0) {
                for (const row of this.editData1) {
                    for (const key in row) {
                        if (row[key] === '***') {
                            hasAsterisks = true;
                            break;
                        }
                    }
                    if (hasAsterisks) break;
                }
            }
            
            // 如果反射率分布表中有***值，显示VIP提示
            if (hasAsterisks) {
                return true;
            }
            
            // 检查rangeValues中是否有***值
            if (this.entityForm.rangeValues) {
                for (const item of this.entityForm.rangeValues) {
                    if (item.proportion === '***' || item._isZeroValue) {
                        return true;
                    }
                }
            }
            
            if (this.details.rangeValues) {
                for (const item of this.details.rangeValues) {
                    if (item.proportion === '***' || item._isZeroValue) {
                        return true;
                    }
                }
            }
            
            // 检查coalRockList中是否有***值
            if (this.entityForm.coalRockList) {
                for (const item of this.entityForm.coalRockList) {
                    if (item.proportion === '***' || item._isZeroValue) {
                        return true;
                    }
                }
            }
            
            if (this.details.coalRockList) {
                for (const item of this.details.coalRockList) {
                    if (item.proportion === '***' || item._isZeroValue) {
                        return true;
                    }
                }
            }
            
            // 如果没有***值，则不显示VIP提示
            return false;
        },
        // size() {
        //     return this.uploadList.length
        // },
        getColumnsInfo() {
            const dialogStatus = this.dialogStatus
            const readOnly = this.isReadonly
            const watchArr =
                dialogStatus === 'watch'
                    ? [
                        // {
                        //     data: null,
                        //     title: '',
                        //     width: 30,
                        //     //只读
                        //     readOnly: true,
                        //     renderer: function (instance, td, row, col, prop, value, cellProperties) {
                        //         Handsontable.renderers.TextRenderer.apply(this, arguments)
                        //         const index = row + 1
                        //         td.style.textAlign = 'center'
                        //         // td.innerHTML = index == len ? '平均' : index
                        //     }
                        // }
                    ]
                    : []
            return [
                ...watchArr,
                {
                    title: '名称',
                    width: 60,
                    data: 'coalName',
                    type: 'text',
                    readOnly
                },

                {
                    title: '煤种',
                    width: 100,
                    data: 'coalCategoryName',
                    formRequire: true, // 自定义字段
                    visibleRows: 15,
                    type: 'dropdown',
                    source: this.categoryNamesList,
                    readOnly
                },

                {
                    title: '产地',
                    width: 100,
                    data: 'location',
                    type: 'text',
                    readOnly: false
                    // formRequire: true, // 自定义字段
                    // visibleRows: 15,
                    // type: 'dropdown',
                    // source: (window.dict['coal_type'] || []).map((item) => item.code)
                },

                {
                    title: '配煤成本',
                    width: 60,
                    data: 'coalBlendingCost',
                    type: 'numeric',
                    readOnly,
                    numericFormat: {
                        pattern: '0.00'
                    },
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },
                
                {
                    title: '含税煤价',
                    width: 60,
                    data: 'factoryPrice',
                    type: 'numeric',
                    readOnly,
                    numericFormat: {
                        pattern: '0.00'
                    },
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },
                
                {
                    title: '运费',
                    width: 60,
                    data: 'activePriceTransportPriceNoTax',
                    type: 'numeric',
                    readOnly,
                    numericFormat: {
                        pattern: '0.00'
                    },
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                }
            ]
        },

        getColumns() {
            const readOnly = this.isReadonly
            const dialogStatus = this.dialogStatus
            const len = this.entityForm.indexlist.length
            const watchArr =
                dialogStatus === 'watch'
                    ? [
                        // {
                        //     data: null,
                        //     title: '',
                        //     width: 30,
                        //     //只读
                        //     readOnly: true,
                        //     renderer: function (instance, td, row, col, prop, value, cellProperties) {
                        //         Handsontable.renderers.TextRenderer.apply(this, arguments)
                        //         const index = row + 1
                        //         td.style.textAlign = 'center'
                        //         td.innerHTML = index == len ? '平均' : index
                        //     }
                        // }
                    ]
                    : []
            return [
                ...watchArr,
                {
                    title: 'Ad',
                    width: 60,
                    data: 'ad',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.00'
                    },
                    readOnly,
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },
                {
                    title: 'St,d',
                    width: 60,
                    data: 'std',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.00'
                    },
                    readOnly,
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },

                {
                    title: 'Vdaf',
                    width: 60,
                    data: 'vdaf',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.00'
                    },
                    readOnly,
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },
                // {
                //     title: '焦渣',
                //     width: 60,
                //     data: 'procCrc',
                //     type: 'crc',
                //     numericFormat: {
                //         pattern: '0'
                //     }
                // },
                {
                    title: 'G',
                    width: 60,
                    data: 'g',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0'
                    },
                    readOnly,
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },
                {
                    title: 'Y',
                    width: 60,
                    data: 'y',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.0'
                    },
                    readOnly,
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },
                {
                    title: 'X',
                    width: 60,
                    data: 'x',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.0'
                    },
                    readOnly,
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },
                {
                    title: 'Mt',
                    width: 60,
                    data: 'mt',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.0'
                    },
                    readOnly,
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },
                {
                    title: 'CRI',
                    width: 60,
                    data: 'cri',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.0'
                    },
                    readOnly,
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },
                {
                    title: 'CSR',
                    width: 60,
                    data: 'csr',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.0'
                    },
                    readOnly,
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },
                {
                    title: '反射率',
                    width: 60,
                    data: 'macR0',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.000'
                    },
                    readOnly,
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },
                {
                    title: '标准差',
                    width: 60,
                    data: 'macS',
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0.000'
                    },
                    readOnly,
                    renderer: function(instance, td, row, col, prop, value, cellProperties) {
                        Handsontable.renderers.NumericRenderer.apply(this, arguments);
                        td.style.textAlign = 'left';
                    }
                },
            ]
        },

        getColumns1() {
            const createNames = (index) => {
                const res = {
                    0: 'Rran范围',
                    1: '频率%',
                    2: 'Rran范围',
                    3: '频率%',
                    4: 'Rran范围',
                    5: '频率%',
                    6: 'Rran范围',
                    7: '频率%',
                    8: 'Rran范围',
                    9: '频率%'
                }
                return res[index] || 'default'
            }
            return [
                ...Array(10)
                    .fill(null)
                    .map((val, index, arr) => {
                        // index 是偶数
                        const dataProp = index + 1
                        if (index % 2 == 1) {
                            return {
                                title: createNames(index),
                                width: 80,
                                data: dataProp,
                                type: 'numeric',
                                numericFormat: {
                                    pattern: '0.00'
                                },
                                renderer: function(instance, td, row, col, prop, value, cellProperties) {
                                    Handsontable.renderers.NumericRenderer.apply(this, arguments);
                                    td.style.textAlign = 'left';
                                }
                            }
                        }
                        return {
                            title: createNames(index),
                            width: 90,
                            data: dataProp,
                            // 禁用列
                            readOnly: true
                        }
                    })
            ]
        },
        dateRange: {
            get() {
                return [this.priceTrendQuery.beginDate, this.priceTrendQuery.endDate]
            },
            set(value) {
                this.priceTrendQuery.beginDate = value[0]
                this.priceTrendQuery.endDate = value[1]
            }
        }
    },
    props: {
        isReadonly: {
            type: Boolean,
            default: false
        },
        categoryNamesList: {
            type: Array,
            default() {
                return []
            }
        },

        model: {
            type: Object,
            default() {
                return {}
            }
        },
        details: {
            type: Object,
            default() {
                return {}
            }
        },
        addVisible: {
            type: Boolean,
            default: false
        },
        isSourceWash: {
            type: Boolean,
            default: false
        },
        dialogStatus: {
            type: String,
            default: 'update'
        }
    },
    methods: {
        formatDecimal(value) {
            // 如果值为空，返回空字符串
            if (value === null || value === undefined || value === '') {
                return '';
            }
            // 转换为数字并始终保留两位小数
            const numValue = parseFloat(value);
            if (isNaN(numValue)) {
                return '';
            }
            // 强制保留两位小数
            return numValue.toFixed(2);
        },
        headClass() {
            return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
        },
        handleViewUrl(url) {
            this.showPreview = true
            this.urlList = [url]

            console.log(this.showPreview, this.urlList)
        },
        handleCloseV() {
            this.addVisible = false
            // 初始化表单数据
            this.entityForm = {
                indexlist: [],
                basicinformation: [],
                RranFrequency: [],

            }
        },
        async handleRemoveUpload(index) {
            const {id} = this.uploadList[index]
            this.$refs.upload.attachmentDelete({id, index})
        },
        handleUploadSuccess(res) {
            this.uploadList = [...this.uploadList, res]
            // 上传完毕后更新展示的列表和把新增的附件id塞入到
            this.entityForm.attachmentList.push({id: res.id})
        },
        handleUploadDelete({status, index}) {
            this.uploadList.splice(index, 1)
        },

        //  基本信息表格
        async handleEditTableInitInfo(instance) {
            await this.$nextTick();
            console.log('初始化基础信息表格');
            
            // 确保有基础数据
            if (!this.entityForm.basicinformation || !this.entityForm.basicinformation.length) {
                this.entityForm.basicinformation = [{
                    coalName: this.entityForm.coalName || '',
                    coalCategoryName: this.entityForm.coalCategoryName || '',
                    location: this.entityForm.location || '',
                    factoryPrice: this.entityForm.factoryPrice || '',
                    activePriceTransportPriceNoTax: this.entityForm.activePriceTransportPriceNoTax || '',
                    coalBlendingCost: this.entityForm.coalBlendingCost || ''
                }];
            }

            console.log('基础信息数据:', this.entityForm.basicinformation);

            try {
                // 简化表格配置
                const settings = {
                    columns: this.getColumnsInfo,
                        data: this.entityForm.basicinformation,
                    readOnly: this.isReadonly,
                    contextMenu: this.isReadonly ? false : {
                        items: {}
                    },
                        rowHeaders: false
                };
                
                // 更新配置并加载数据
                instance.updateSettings(settings);
                
                // 显式加载数据
                instance.loadData(this.entityForm.basicinformation);
                
                console.log('基础信息表格初始化完成');
            } catch (error) {
                console.error('初始化基础信息表格失败:', error);
            }
        },

        // 指标表格
        async handleEditTableInit(instance) {
            await this.$nextTick();
            console.log('初始化指标表格');
            
            // 确保有指标数据
            if (!this.entityForm.indexlist || !this.entityForm.indexlist.length) {
                this.entityForm.indexlist = [{
                    ad: this.entityForm.ad || '',
                    std: this.entityForm.std || '',
                    vdaf: this.entityForm.vdaf || '',
                    g: this.entityForm.g || '',
                    y: this.entityForm.y || '',
                    x: this.entityForm.x || '',
                    mt: this.entityForm.mt || '',
                    macR0: this.entityForm.macR0 || '',
                    macS: this.entityForm.macS || '',
                    cri: this.entityForm.cri || '',
                    csr: this.entityForm.csr || ''
                }];
            }
            
            console.log('指标数据:', this.entityForm.indexlist);
            
            try {
                // 简化表格配置
                const settings = {
                    columns: this.getColumns,
                    data: this.entityForm.indexlist,
                    readOnly: this.isReadonly,
                    contextMenu: this.isReadonly ? false : {
                        items: {}
                    },
                    rowHeaders: false
                };
                
                // 更新配置并加载数据
                instance.updateSettings(settings);
                
                // 显式加载数据
                instance.loadData(this.entityForm.indexlist);
                
                console.log('指标表格初始化完成');
            } catch (error) {
                console.error('初始化指标表格失败:', error);
            }
        },

        // 反射率表格
        async handleEditTableInit1(instance) {
            await this.$nextTick();
            console.log('初始化反射率分布表');
            
            try {
                // 确保rangeAllList有数据
                if (!this.rangeAllList || this.rangeAllList.length === 0) {
                    console.log('rangeAllList为空，尝试获取数据');
                    await this.getDataList();
                }

                // 判断是否为掌上煤源类型
                const isZsmyType = this.details && (this.details.type === 'zsmy' || this.entityForm.type === 'zsmy');
                console.log('是否为掌上煤源类型:', isZsmyType);
                
                // 检查各种可能的反射率数据源，按优先级使用
                let reflectionData = null;
                
                // 1. 首先检查details中的rangeValues
                if (this.details && this.details.rangeValues && this.details.rangeValues.length > 0) {
                    console.log('使用details.rangeValues数据');
                    reflectionData = this.details.rangeValues;
                } 
                // 2. 然后检查details中的coalRockList
                else if (this.details && this.details.coalRockList && this.details.coalRockList.length > 0) {
                    console.log('使用details.coalRockList数据');
                    reflectionData = this.details.coalRockList;
                }
                // 3. 然后检查entityForm中的rangeValues
                else if (this.entityForm && this.entityForm.rangeValues && this.entityForm.rangeValues.length > 0) {
                    console.log('使用entityForm.rangeValues数据');
                    reflectionData = this.entityForm.rangeValues;
                }
                // 4. 然后检查entityForm中的coalRockList
                else if (this.entityForm && this.entityForm.coalRockList && this.entityForm.coalRockList.length > 0) {
                    console.log('使用entityForm.coalRockList数据');
                    reflectionData = this.entityForm.coalRockList;
                }
                // 5. 然后尝试解析coalRockContent
                else if (this.details && this.details.coalRockContent) {
                    try {
                        console.log('尝试解析coalRockContent数据');
                        let rockContent;
                        if (typeof this.details.coalRockContent === 'string') {
                            rockContent = JSON.parse(this.details.coalRockContent);
                        } else {
                            rockContent = this.details.coalRockContent;
                        }
                        
                        if (Array.isArray(rockContent) && rockContent.length > 0) {
                            console.log('成功解析coalRockContent数据');
                            reflectionData = rockContent;
                        }
                    } catch (error) {
                        console.error('解析coalRockContent失败:', error);
                    }
                }
                // 6. 最后尝试解析proportionContent和rangeContent
                else if (this.details && this.details.proportionContent && this.details.rangeContent) {
                    try {
                        console.log('尝试解析proportionContent和rangeContent数据');
                        const proportionValues = typeof this.details.proportionContent === 'string' ? 
                            JSON.parse(this.details.proportionContent) : this.details.proportionContent;
                        const rangeNameValues = typeof this.details.rangeContent === 'string' ? 
                            JSON.parse(this.details.rangeContent) : this.details.rangeContent;
                        
                        if (Array.isArray(proportionValues) && Array.isArray(rangeNameValues) && 
                            proportionValues.length > 0 && rangeNameValues.length > 0) {
                            console.log('成功解析proportionContent和rangeContent数据');
                            reflectionData = proportionValues.map((proportion, index) => ({
                                rangeName: rangeNameValues[index] || '',
                                proportion: proportion || 0
                            }));
                        }
                    } catch (error) {
                        console.error('解析proportionContent和rangeContent失败:', error);
                    }
                }
                
                // 如果找到了反射率数据，格式化并更新表格
                if (reflectionData && reflectionData.length > 0) {
                    // 确保使用rangeAllList来补充完整的反射率范围
                    const formattedData = this.formatRangeDataForTable(reflectionData);
                    if (formattedData.length > 0) {
                        this.editData1 = formattedData;
                        console.log('成功格式化反射率数据:', formattedData);
                    }
                } else {
                    // 如果没有找到反射率数据，使用rangeAllList创建空的表格数据
                    console.log('未找到有效的反射率数据，使用rangeAllList创建空表格');
                    if (this.rangeAllList && this.rangeAllList.length > 0) {
                        this.editData1 = this.formatEmptyRangeData(this.rangeAllList);
                        console.log('使用rangeAllList创建的空表格数据:', this.editData1);
                    }
                }
                
                // 设置表格配置
                const settings = {
                    contextMenu: this.dialogStatus === 'watch' ? false : {
                        items: {}
                    },
                    data: this.editData1,
                    readOnly: this.dialogStatus === 'watch',
                    afterChange: (changes, source) => {
                        if (!changes) return;
                        
                        // 计算反射率之和
                        let total = 0;
                        this.editData1.forEach(row => {
                            for (const key in row) {
                                // 只计算偶数列（频率列）
                                if (parseInt(key) % 2 === 0 && !isNaN(row[key])) {
                                    total += parseFloat(row[key]) || 0;
                                    }
                                }
                            });
                        
                        // 更新反射率之和
                        this.entityForm.rate = total.toFixed(1);
                        console.log('反射率之和更新为:', this.entityForm.rate);
                    },
                    cells: (row, col, prop) => {
                        const cellProperties = {};
                        
                        if (row === 0) {
                            cellProperties.numericFormat = {
                                pattern: '0.00'
                            };
                        }
                        
                        // 如果是频率列(偶数列)，设置文本对齐为左对齐
                        if (col % 2 === 1) {
                            cellProperties.renderer = function(instance, td, row, col, prop, value, cellProperties) {
                                // 对于掌上煤源类型，特殊处理显示
                                if (isZsmyType && (value === 0 || value === '0' || value === '0.00' || value === '***')) {
                                    Handsontable.renderers.TextRenderer.apply(this, arguments);
                                    td.innerHTML = '***';
                                    td.style.textAlign = 'left';
                                } else if (value === '*') {
                                    // 对于参数返回为*的情况，显示为***
                                    Handsontable.renderers.TextRenderer.apply(this, arguments);
                                    td.innerHTML = '***';
                                    td.style.textAlign = 'left';
                                } else {
                                    Handsontable.renderers.NumericRenderer.apply(this, arguments);
                                    td.style.textAlign = 'left';
                                }
                            };
                        }
                        
                        return cellProperties;
                    }
                };
                
                // 更新表格配置
                instance.updateSettings(settings);
                
                // 显式加载数据
                instance.loadData(this.editData1);
                
                console.log('反射率分布表初始化完成');
            } catch (error) {
                console.error('初始化反射率分布表失败:', error);
            }
        },
        
        // 辅助方法：将范围数据格式化为表格数据
        formatRangeDataForTable(rangeValues) {
            if (!rangeValues || !Array.isArray(rangeValues) || rangeValues.length === 0) {
                // 即使没有数据，也使用rangeAllList来渲染Rran范围列
                if (this.rangeAllList && this.rangeAllList.length > 0) {
                    console.log('没有反射率数据，使用rangeAllList创建空表格');
                    return this.formatEmptyRangeData(this.rangeAllList);
                }
                return [];
            }
            
            try {
                // 判断是否为掌上煤源类型
                const isZsmyType = this.details && (this.details.type === 'zsmy' || this.entityForm.type === 'zsmy');
                console.log('是否为掌上煤源类型:', isZsmyType);
                
                // 使用rangeAllList来确保所有Rran范围都被显示
                let completeRangeValues = [...rangeValues];
                
                // 如果rangeAllList存在且有数据，确保所有范围都包含在内
                if (this.rangeAllList && this.rangeAllList.length > 0) {
                    console.log('使用rangeAllList补充完整的反射率范围');
                    
                    // 创建一个映射来快速查找已有的范围
                    const existingRanges = {};
                    rangeValues.forEach(item => {
                        if (item && item.rangeName) {
                            existingRanges[item.rangeName] = item;
                        }
                    });
                    
                    // 添加缺失的范围
                    this.rangeAllList.forEach(item => {
                        if (item && item.rangeName && !existingRanges[item.rangeName]) {
                            completeRangeValues.push({
                                rangeName: item.rangeName,
                                proportion: '', // 使用空字符串而不是0，确保没有值的地方显示为空
                                sort: item.sort || 0
                            });
                        }
                    });
                    
                    // 改进排序逻辑，确保按照范围值从小到大排序
                    completeRangeValues.sort((a, b) => {
                        // 从rangeName提取开始值进行排序
                        const extractStartValue = (rangeName) => {
                            if (!rangeName) return 0;
                            const match = rangeName.match(/(\d+\.\d+)-(\d+\.\d+)/);
                            return match ? parseFloat(match[1]) : 0;
                        };
                        
                        // 首先尝试使用sort属性排序
                        if (a.sort !== undefined && b.sort !== undefined) {
                            return a.sort - b.sort;
                        }
                        
                        // 如果没有sort属性或sort属性相同，则使用范围的开始值排序
                        const startA = extractStartValue(a.rangeName);
                        const startB = extractStartValue(b.rangeName);
                        
                        return startA - startB;
                    });
                    
                    console.log('补充后的反射率数据长度:', completeRangeValues.length);
                }
                
                // 将范围数据按照5个一组分组
                const chunks = [];
                for (let i = 0; i < completeRangeValues.length; i += 5) {
                    chunks.push(completeRangeValues.slice(i, i + 5));
                }
                
                // 将每组数据转换为表格行
                const result = chunks.map(chunk => {
                    const row = {};
                    chunk.forEach((item, index) => {
                        if (!item) return;
                        
                        const baseIndex = index * 2 + 1;
                        row[baseIndex] = item.rangeName || '';
                        
                        // 修改逻辑：对于掌上煤源类型，有值的显示为***，没值的显示为空
                        if (isZsmyType) {
                            // 检查是否有值且值为0.00
                            const isZeroValue = item.proportion === 0 || 
                                                item.proportion === '0' || 
                                                item.proportion === 0.0 || 
                                                item.proportion === '0.0' || 
                                                item.proportion === 0.00 || 
                                                item.proportion === '0.00';
                            
                            if (isZeroValue) {
                                // 值为0.00显示为***
                                row[baseIndex + 1] = '***';
                            } else if (item.proportion === '*') {
                                // 参数返回为*时显示为***
                                row[baseIndex + 1] = '***';
                            } else if (item.proportion === '' || item.proportion === null || item.proportion === undefined) {
                                // 没有值显示为空
                                row[baseIndex + 1] = '';
                            } else {
                                // 其他值正常显示
                                row[baseIndex + 1] = parseFloat(item.proportion) || '';
                            }
                        } else {
                            // 非掌上煤源类型的正常处理
                            if (item.proportion === '*') {
                                // 参数返回为*时显示为***
                                row[baseIndex + 1] = '***';
                            } else if (item.proportion === '' || item.proportion === null || item.proportion === undefined) {
                                // 如果没有值，保持为空
                                row[baseIndex + 1] = '';
                            } else {
                                // 有值则显示数值
                                row[baseIndex + 1] = parseFloat(item.proportion) || '';
                            }
                        }
                    });
                    return row;
                });
                
                console.log('格式化后的表格数据行数:', result.length);
                return result;
            } catch (error) {
                console.error('格式化反射率数据失败:', error);
                return [];
            }
        },

        // 新增方法：格式化空的反射率数据，确保显示所有Rran范围
        formatEmptyRangeData(rangeList) {
            if (!rangeList || !rangeList.length) return [];
            
            try {
                console.log('开始格式化空的反射率数据，范围数量:', rangeList.length);
                
                // 判断是否为掌上煤源类型
                const isZsmyType = this.details && (this.details.type === 'zsmy' || this.entityForm.type === 'zsmy');
                
                // 改进排序逻辑，确保按照范围值从小到大排序
                const sortedRanges = [...rangeList].sort((a, b) => {
                    // 从rangeName提取开始值进行排序
                    const extractStartValue = (rangeName) => {
                        if (!rangeName) return 0;
                        const match = rangeName.match(/(\d+\.\d+)-(\d+\.\d+)/);
                        return match ? parseFloat(match[1]) : 0;
                    };
                    
                    // 首先尝试使用sort属性排序
                    if (a.sort !== undefined && b.sort !== undefined) {
                        return a.sort - b.sort;
                    }
                    
                    // 如果没有sort属性或sort属性相同，则使用范围的开始值排序
                    const startA = extractStartValue(a.rangeName);
                    const startB = extractStartValue(b.rangeName);
                    
                    return startA - startB;
                });
                
                // 打印排序后的范围列表，便于调试
                console.log('排序后的范围列表:', sortedRanges.map(item => item.rangeName).join(', '));
                
                // 将范围数据按照5个一组分组
                const chunks = [];
                for (let i = 0; i < sortedRanges.length; i += 5) {
                    chunks.push(sortedRanges.slice(i, i + 5));
                }
                
                // 将每组数据转换为表格行，频率值为空
                const result = chunks.map(chunk => {
                    const row = {};
                    chunk.forEach((item, index) => {
                        if (!item) return;
                        
                        const baseIndex = index * 2 + 1;
                        row[baseIndex] = item.rangeName || '';
                        row[baseIndex + 1] = ''; // 使用空字符串而不是0，确保没有值的地方显示为空
                    });
                    return row;
                });
                
                console.log('格式化空表格数据完成，行数:', result.length);
                return result;
            } catch (error) {
                console.error('格式化空的反射率数据失败:', error);
                return [];
            }
        },

        async getRangerList() {
            const res = await Func.fetch(getRangeListApi)
            let coalRockList = []
            res.data.forEach((item) => {
                let range = {
                    rangeName: '',
                    proportion: '',
                    sort: '',
                    begin: '',
                    end: ''
                }
                range.rangeName = item.rangeName
                range.sort = item.sort
                range.proportion = item.proportion
                range.begin = item.begin
                range.end = item.end
                coalRockList.push(range)
            })
            
            // 保存完整的反射率范围数据
            this.rangeAllList = res.data;

            let indexlist = [
                ...Array(1)
                    .fill(null)
                    .map(() => ({}))
            ]
            let basicinformation = [
                ...Array(1)
                    .fill(null)
                    .map(() => ({}))
            ]

            await this.getCreateData({
                list: coalRockList
            })

            this.entityForm = {...entityForm(), coalRockList, indexlist, basicinformation}
        },
        // 保存数据
        async save() {
            const valid = await this.$refs.entityForm.validate()
            let that = this
            if (valid) {
                this.entityFormLoading = true
                const status = await this.$refs['editTable'].validate()
                if (!status) return
                const formatEditData1 = () => {
                    const [first, ...rest] = this.getFormatEditData(this.editData1, this.getColumns1, false)
                    let list = []
                    rest.forEach((item) => {
                        list.push({rangeName: item[1], proportion: item[2]})
                        list.push({rangeName: item[3], proportion: item[4]})
                        list.push({rangeName: item[5], proportion: item[6]})
                        list.push({rangeName: item[7], proportion: item[8]})
                        list.push({rangeName: item[9], proportion: item[10]})
                    })
                    list = list.filter((item) => item.rangeName)
                    console.log(list, 'list')
                    return {
                        coalRockList: list
                    }
                }
                formatEditData1()
                this.entityForm.basicinformation.forEach((item) => {
                    this.entityForm.name = item.name
                    this.entityForm.type = item.type
                    this.entityForm.location = item.location
                    this.entityForm.batchCode = item.batchCode
                    // item.coalBlendingCost = this.entityForm.coalBlendingCost
                })

                this.entityForm.indexlist.forEach((item) => {
                    this.entityForm.cleanAd = item.cleanAd
                    this.entityForm.cleanStd = item.cleanStd
                    this.entityForm.cleanVdaf = item.cleanVdaf
                    this.entityForm.procCrc = item.procCrc
                    this.entityForm.rawG = item.rawG
                    this.entityForm.procY = item.procY
                    this.entityForm.procX = item.procX
                    this.entityForm.cleanMt = item.cleanMt
                    this.entityForm.macR0 = item.macR0
                    this.entityForm.macS = item.macS
                })

                const res = await Func.fetch(isCheckCoal, {
                    g: this.entityForm.procG,
                    vdaf: this.entityForm.cleanVdaf,
                    y: this.entityForm.procY
                })
                if (res.data) {
                    if (res.data.name !== this.entityForm.type) {
                        that.isChangeCoal = true
                        that.type = res.data.name
                        that.entityFormLoading = false
                    } else {
                        that.entityFormLoading = true
                        //  基础信息
                        let ting = that.entityForm.basicinformation
                        for (var i = 0; i < ting.length; i++) {
                            that.entityForm.name = ting[i].name
                            that.entityForm.type = ting[i].type
                            that.entityForm.location = ting[i].location
                            that.entityForm.batchCode = ting[i].batchCode
                            that.entityForm.arrivePrice = ting[i].arrivePrice
                        }
                        // 指标信息
                        let Things = this.entityForm.indexlist
                        for (let i = 0; i < Things.length; i++) {
                            // 确保数值类型正确，处理空值
                            this.entityForm.ad = Things[i].ad !== undefined && Things[i].ad !== null ? parseFloat(Things[i].ad) : null;
                            this.entityForm.std = Things[i].std !== undefined && Things[i].std !== null ? parseFloat(Things[i].std) : null;
                            this.entityForm.vdaf = Things[i].vdaf !== undefined && Things[i].vdaf !== null ? parseFloat(Things[i].vdaf) : null;
                            this.entityForm.g = Things[i].g !== undefined && Things[i].g !== null ? parseFloat(Things[i].g) : null;
                            this.entityForm.y = Things[i].y !== undefined && Things[i].y !== null ? parseFloat(Things[i].y) : null;
                            this.entityForm.x = Things[i].x !== undefined && Things[i].x !== null ? parseFloat(Things[i].x) : null;
                            this.entityForm.mt = Things[i].mt !== undefined && Things[i].mt !== null ? parseFloat(Things[i].mt) : null;
                            this.entityForm.macR0 = Things[i].macR0 !== undefined && Things[i].macR0 !== null ? parseFloat(Things[i].macR0) : null;
                            this.entityForm.macS = Things[i].macS !== undefined && Things[i].macS !== null ? parseFloat(Things[i].macS) : null;
                            this.entityForm.csr = Things[i].csr !== undefined && Things[i].csr !== null ? parseFloat(Things[i].csr) : null;
                            this.entityForm.cri = Things[i].cri !== undefined && Things[i].cri !== null ? parseFloat(Things[i].cri) : null;
                        }
                        const entityFormData = {
                            ...that.entityForm,
                            dataType: that.datatype,
                            attachmentList: that.uploadList,
                            ...formatEditData1()
                        }
                        const saveRes = await Func.fetch(saveCoal, entityFormData)

                        this.entityFormLoading = false
                        if (saveRes.data) {
                            this.$message({showClose: true, message: '提交成功', type: 'success'})
                            this.closeDialog('full', true)
                            this.handleClose('full')
                        }
                    }
                }
            }
        },
        /**
         * 过滤掉空数据，校验数据
         * @param target 提供的车牌列表
         * @param isSubmit 是否提交 主要用于控制是否校验车牌号 true时抛出Error
         */
        getFormatEditData(target = [], columns, require = true) {
            const getExcludeDirtyList = (target = []) => {
                const list = deepClone(target)
                list.forEach((v) => {
                    for (const key in v) {
                        if ([null, ''].includes(v[key])) {
                            delete v[key]
                        }
                    }
                })
                return list
            }
            const list = getExcludeDirtyList(target)
            const data = []
            // 获取需要校验的字段
            const validateList = columns
                .filter((col) => col.formRequire)
                .map((v) => {
                    return {key: v.data, msg: v.title}
                })

            list.forEach((item, index) => {
                // 过滤掉空数据 但在编辑的时候会保留id所以不能被删除只能修改当前这条数据
                if (item && Object.keys(item).length) {
                    validateList.forEach((v) => {
                        if (!item[v.key]) {
                            this.$message(`表格第${index + 1}行${v.msg}不能为空`)
                            throw new Error('数据不能为空')
                        }
                    })
                    data.push(item)
                }
            })

            if (!data.length && require) {
                this.$message({message: '表格至少添加一条数据', type: 'warning'})
                throw new Error('数据不能为空')
            }

            return data
        },

        async makeSure(coalType) {
            // 确保所有灰成分数据格式化为两位小数
            this.formatAllAshFields();
            
            // const {arriveFactory} = this.$refs.entityForm
            if (coalType === 't') {
                this.entityFormLoading = true
                this.entityForm.type = coalType || this.entityForm.type
            } else {
                this.entityFormLoadingV = true
                this.entityForm.type = ''
            }
            
            // 直接获取备注信息
            const remarks = this.entityForm.remarks;
            
            try {
                // 保存当前的灰成分数据，避免后续操作覆盖
                const ashData = {};
                const ashFields = ['siO2', 'al2O3', 'fe2O3', 'caO', 'mgO', 'na2O', 'k2O', 'tiO2', 'p2O5', 'so3', 'mnO2'];
                
                // 从DOM中直接获取输入框的值
                ashFields.forEach(field => {
                    // 获取输入框的DOM元素
                    const inputElement = document.querySelector(`input[name="${field}"]`);
                    if (inputElement) {
                        const value = inputElement.value;
                        // 转换为数字
                        const numValue = parseFloat(value);
                        ashData[field] = !isNaN(numValue) ? numValue : 0;
                    } else {
                        // 如果找不到DOM元素，则使用entityForm中的值
                        const value = this.entityForm[field];
                        const numValue = parseFloat(value);
                        ashData[field] = !isNaN(numValue) ? numValue : 0;
                    }
                });
                
                // 记录灰成分数据，确保它们被正确获取
                console.log('保存前的灰成分数据:', ashData);

                // Get basic info from table
                const basicInfo = this.$refs.editTable.getHotInstance().getSourceData()[0] || {};
                
                // 记录表格中的运费值，用于确认是否正确获取
                console.log('表格中的运费值:', basicInfo.activePriceTransportPriceNoTax);
                
                for (const key in basicInfo) {
                    if (basicInfo[key] !== null && basicInfo[key] !== undefined) {
                        this.entityForm[key] = basicInfo[key];
                    }
                }

                // Explicitly handle price and transport fee fields - 确保价格和运费字段被正确处理
                if (basicInfo.factoryPrice !== undefined) {
                    this.entityForm.factoryPrice = parseFloat(basicInfo.factoryPrice);
                }
                
                // 运费字段的特殊处理 - 确保即使是0也能被正确处理
                if (basicInfo.activePriceTransportPriceNoTax !== undefined) {
                    const transportFee = parseFloat(basicInfo.activePriceTransportPriceNoTax);
                    this.entityForm.activePriceTransportPriceNoTax = !isNaN(transportFee) ? transportFee : 0;
                    // 记录处理后的运费值
                    console.log('处理后的运费值:', this.entityForm.activePriceTransportPriceNoTax);
                }
                
                if (basicInfo.coalBlendingCost !== undefined) {
                    this.entityForm.coalBlendingCost = parseFloat(basicInfo.coalBlendingCost);
                }

                // Get indicator data from table
                const indicatorData = this.$refs.editTable2.getHotInstance().getSourceData()[0] || {};
                for (const key in indicatorData) {
                    if (indicatorData[key] !== null && indicatorData[key] !== undefined) {
                        this.entityForm[key] = indicatorData[key];
                    }
                }
                
                // 确保产地信息从表格中获取
                if (basicInfo.location) {
                    this.entityForm.location = basicInfo.location;
                }
                
                // 如果表格中没有产地信息，尝试从其他数据源获取
                const locationValue = this.entityForm.location || 
                                     basicInfo.location || 
                                     (this.entityForm.basicinformation && this.entityForm.basicinformation[0] ? this.entityForm.basicinformation[0].location : null) ||
                                     (this.details ? this.details.location : '');
                                     
                
                // 明确设置产地字段值
                this.entityForm.location = locationValue;
                
                // Process rock list data
                let rockList = [];
                try {
                    // Get reflection rate distribution data
                    const reflectionData = this.$refs.editTable1.getHotInstance().getSourceData();
                    if (reflectionData && Array.isArray(reflectionData)) {
                        reflectionData.forEach(row => {
                            for (let i = 1; i <= 9; i += 2) {
                                const rangeName = row[i];
                                const proportion = row[i+1];
                                
                                if (rangeName && proportion !== undefined && proportion !== null && proportion !== '') {
                                    const proportionValue = parseFloat(proportion);
                                    if (!isNaN(proportionValue)) {
                                        rockList.push({
                                            rangeName: String(rangeName),
                                            proportion: proportionValue
                                        });
                                    }
                                }
                            }
                        });
                    }
                } catch (error) {
                    console.error('处理反射率数据出错:', error);
                    rockList = [];
                }

                // Create range content and proportion content
                const rangeNames = rockList.map(item => String(item.rangeName));
                const proportions = rockList.map(item => Number(item.proportion));
                
                const rockData = {
                    rockList: rockList,
                    rangeContent: JSON.stringify(rangeNames),
                    proportionContent: JSON.stringify(proportions),
                    coalRockContent: JSON.stringify(rockList)
                };

                // 确保获取正确的数据类型
                const type = this.entityForm.type || this.details.type || 'wb';

                // Build the final data object - 合并所有数据，确保灰成分数据被包含
                const formData = {
                    id: this.entityForm.id || this.details.id,
                    type: type, // 确保类型字段正确
                    ...this.entityForm,
                    ...rockData,
                    // 直接使用之前保存的灰成分数据，避免被entityForm中的值覆盖
                    ...ashData,
                    attachmentList: this.uploadList || [],
                    // 确保产地和备注信息被包含
                    location: this.entityForm.location || basicInfo.location || '',
                    remarks: remarks || '',
                    // 强制标记，确保灰成分数据被保存
                    _forceAshSave: true
                };
                
                // 记录最终提交的灰成分数据
                console.log('最终提交的灰成分数据:', {
                    siO2: formData.siO2,
                    al2O3: formData.al2O3,
                    fe2O3: formData.fe2O3,
                    caO: formData.caO,
                    mgO: formData.mgO,
                    na2O: formData.na2O,
                    k2O: formData.k2O,
                    tiO2: formData.tiO2,
                    p2O5: formData.p2O5,
                    so3: formData.so3,
                    mnO2: formData.mnO2
                });
                
                // 确保运费字段被正确包含在提交数据中
                if (this.entityForm.activePriceTransportPriceNoTax !== undefined) {
                    const transportFee = parseFloat(this.entityForm.activePriceTransportPriceNoTax);
                    formData.activePriceTransportPriceNoTax = !isNaN(transportFee) ? transportFee : 0;
                } else if (basicInfo.activePriceTransportPriceNoTax !== undefined) {
                    const transportFee = parseFloat(basicInfo.activePriceTransportPriceNoTax);
                    formData.activePriceTransportPriceNoTax = !isNaN(transportFee) ? transportFee : 0;
                }
                
                // 记录最终提交的运费值
                console.log('最终提交的运费值:', formData.activePriceTransportPriceNoTax);
                
                // 提交数据
                console.log('提交的最终数据:', { data: [formData] });
                
                try {
                    // 使用model.update方法提交数据
                    const response = await this.model.update({ data: [formData] });
                    
                    this.$message({showClose: true, message: '提交成功', type: 'success'});
                    this.closeDialog('full', true);
                } catch (updateError) {
                    console.error('保存失败:', updateError);
                    
                    // 尝试使用saveCoal API直接保存
                    try {
                        const Func = require('@/utils/func').default;
                        const { saveCoal } = require('@/api/coal');
                        
                        const saveResponse = await Func.fetch(saveCoal, { data: [formData] });
                        if (saveResponse && saveResponse.data) {
                            this.$message({showClose: true, message: '提交成功', type: 'success'});
                            this.closeDialog('full', true);
                        } else {
                            throw new Error('保存失败，API返回空数据');
                        }
                    } catch (saveError) {
                        console.error('使用saveCoal API保存失败:', saveError);
                        this.$message({showClose: true, message: '保存失败: ' + (saveError.message || '未知错误'), type: 'error'});
                    }
                }
            } catch (error) {
                console.error('保存失败:', error);
                
                let errorMessage = '保存失败: ';
                if (error.response) {
                    if (error.response.data && error.response.data.message) {
                        errorMessage += error.response.data.message;
                    } else {
                        errorMessage += `服务器返回状态码 ${error.response.status}`;
                    }
                } else if (error.message) {
                    errorMessage += error.message;
                } else {
                    errorMessage += '未知错误';
                }
                
                this.$message({showClose: true, message: errorMessage, type: 'error'});
            } finally {
                this.entityFormLoading = false;
                this.entityFormLoadingV = false;
            }
        },
        closeDialog(type = false, isSave = false) {
            this.dialogFormVisible = false
            this.chartVisible = false
            this.entityForm = {...entityForm()}
            this.uploadList = []
            this.isChangeCoal = false
            this.$emit('closeVisible', {isSave: isSave})
        },
        async getEntityForm() {
            await this.$nextTick()

            const {id} = this.details
            try {
                const res = await getCoalById({id})
                if (res.data) {
                    let rate = 0
                    res.data.coalRockList.map((item) => {
                        if (item.proportion) {
                            rate = Number(item.proportion) + rate
                        }
                    })
                    rate = rate.toFixed(1)
                    let basicinformation = []
                    let obj = {
                        name: res.data.name,
                        type: res.data.type,
                        coalBlendingCost: res.data.coalBlendingCost,
                        location: res.data.location,
                        batchCode: res.data.batchCode
                    }
                    basicinformation.push(obj)
                    res.data.basicinformation = basicinformation

                    let indexlist = []
                    let objv = {
                        cleanAd: res.data.cleanAd,
                        cleanStd: res.data.cleanStd,
                        cleanVdaf: res.data.cleanVdaf,
                        procG: res.data.procG,
                        procCrc: res.data.procCrc,
                        procY: res.data.procY,
                        procX: res.data.procX,
                        cleanMt: res.data.cleanMt,
                        macR0: res.data.macR0,
                        csr: res.data.csr,
                        cri: res.data.cri,
                        macS: res.data.macS
                    }
                    indexlist.push(objv)
                    res.data.indexlist = indexlist
                    this.entityForm = {...res.data, rate}
                    this.uploadList = res.data.attachmentList
                    await this.getCreateData({
                        list: res.data.coalRockList
                    })
                    this.$forceUpdate()
                    this.$nextTick(() => {
                        this.$refs.editTable.getHotInstance().loadData([...this.entityForm.basicinformation])
                        this.$refs.editTable2.getHotInstance().loadData([...this.entityForm.indexlist])
                        
                        // Initialize chart and get price trend data
                        this.initChart();
                        this.getByPrice();
                        
                        // If ash composition data is missing, try to fetch it
                        if (!this.entityForm.comSiO2 && !this.entityForm.comAl2O3) {
                            this.fetchAshCompositionData();
                        }
                    })
                }
                // console.log(this.entityForm, id, 111)
            } catch (error) {
                console.log(error, 'error')
            }
        },

        async getCreateData(obj = {}) {
            try {
                let data = [];
                
                // 优先使用传入的列表数据，避免重复请求
                if (obj.list && obj.list.length > 0) {
                    console.log('使用传入的反射率数据，跳过API请求');
                    data = obj.list;
                } else if (this.allListRange && this.allListRange.length > 0) {
                    console.log('使用缓存的反射率数据，跳过API请求');
                    data = this.allListRange;
                } else if (this.rangeAllList && this.rangeAllList.length > 0) {
                    console.log('使用rangeAllList数据，跳过API请求');
                    data = this.rangeAllList;
                } else {
                    console.log('从API获取反射率数据');
                    const res = await Func.fetch(getRangeListApi);
                    data = res.data;
                    // 缓存获取的数据，避免后续重复请求
                    this.allListRange = data;
                    this.rangeAllList = data;
                }

                // 将数据分组并格式化为表格需要的格式
                const formattedData = this.formatRangeDataForTable(data);
                if (formattedData.length > 0) {
                    this.editData1 = formattedData;
                    
                    // 如果表格实例已经初始化，直接更新数据
                    if (this.$refs.editTable1 && this.$refs.editTable1.getHotInstance) {
                        console.log('更新反射率表格数据');
                        this.$refs.editTable1.getHotInstance().loadData(this.editData1);
                    } else {
                        console.log('反射率表格未初始化，数据将在表格初始化时加载');
                    }
                }
            } catch (e) {
                console.error('获取反射率数据失败:', e);
            }
        },

        handleActive(e) {
            // this.entityForm.activeInertiaRatio = (e / (100 - e)).toFixed(2)
            this.entityForm = {...this.entityForm, activeInertiaRatio: (e / (100 - e)).toFixed(2)}
        },
        handleVdaf(e) {
            if (this.entityForm.cleanAd) {
                this.entityForm = {
                    ...this.entityForm,
                    cleanVd: ((e * (100 - this.entityForm.cleanAd)) / 100).toFixed(2)
                }
                // this.entityForm.cleanVd = ((e * (100 - this.entityForm.cleanAd)) / 100).toFixed(2)
            } else {
                this.entityForm = {...this.entityForm, cleanVd: 0}
                // this.entityForm.cleanVd = 0
            }
        },
        handleAd(e) {
            if (this.entityForm.cleanVdaf) {
                // this.entityForm.cleanVd = ((this.entityForm.cleanVdaf * (100 - e)) / 100).toFixed(2)
                this.entityForm = {
                    ...this.entityForm,
                    cleanVd: ((this.entityForm.cleanVdaf * (100 - e)) / 100).toFixed(2)
                }
            } else {
                // this.entityForm.cleanVd = this.entityForm.cleanVdaf
                this.entityForm = {...this.entityForm, cleanVd: 0}
            }
        },
        handleVd(e) {
            if (this.entityForm.cleanAd) {
                // this.entityForm.cleanVdaf = ((100 * e) / (100 - this.entityForm.cleanAd)).toFixed(2)
                this.entityForm = {
                    ...this.entityForm,
                    cleanVdaf: ((100 * e) / (100 - this.entityForm.cleanAd)).toFixed(2)
                }
            } else {
                // this.entityForm.cleanVdaf = e
                this.entityForm = {...this.entityForm, cleanVdaf: 0}
            }
        },
        changeRate() {
            this.entityForm.rate = 0
            this.entityForm.coalRockList.map((item) => {
                if (item.proportion) {
                    this.entityForm.rate = Number(item.proportion) + this.entityForm.rate
                }
            })
        },
        /*
         * 数值范围验证器
         * */
        RangerValidate(rules, value, cb) {
            if (+value < 0) {
                cb(new Error('数值不能小于0'))
            } else if (+value > 100) {
                cb(new Error('数值不能大于100'))
            } else {
                cb()
            }
        },
        handleClose(type = '', isSave = false) {
            if (type === 'full') {
                this.dialogFormVisible = false
                this.chartVisible = false
                this.entityForm = {...entityForm()}
                this.uploadList = []
                this.isChangeCoal = false
                this.$emit('closeVisible', {isSave: isSave})
            } else {
                this.dialogVisible = false
                this.position = {}
                this.isChangeCoal = false
            }
        },
        handleSubmitPosition() {
            if (Object.keys(this.position).length === 0) {
                return
            }
            this.$set(this.entityForm, 'longitude', this.position.lng)
            this.$set(this.entityForm, 'latitude', this.position.lat)
            this.handleClose()
        },
        /**
         * 获取煤灰成份
         */

        async handleCoalAsh() {
            const location = {filter_EQS_area: this.entityForm.area}
            const res = await Func.fetch(coalAshList, location)
            if (res.data.records.length > 0) {
                delete res.data.records[0].id
                // this.$emit('update:entityForm', { ...this.entityForm, ...res.data.records[0] })
                this.entityForm = {...this.entityForm, ...res.data.records[0]}
            } else {
                this.$message({
                    showClose: true,
                    message: '无灰成分参考数据，请化验',
                    type: 'error'
                })
            }
        },
        /**
         * 关闭煤灰成分弹框
         * @param done
         */
        handleCoalClose(done) {
            done()
        },
        async getByPrice() {
            const id = this.entityForm.id;
            if (!id) {
                this.showPriceTrend = false;
                return;
            }
            
            try {
                const res = await this.model.getByPrice({
                    id: id,
                    beginDate: this.priceTrendQuery.beginDate,
                    endDate: this.priceTrendQuery.endDate
                });
                
                if (res && res.data) {
                    this.priceTrendData = res.data;
                    
                    // 检查是否有价格JSON数据
                    if (this.priceTrendData.priceJson) {
                        try {
                            if (typeof this.priceTrendData.priceJson === 'string') {
                                this.priceTrendData.priceJson = JSON.parse(this.priceTrendData.priceJson);
                            }
                            
                            // 检查解析后的数据是否有效
                            if (!Array.isArray(this.priceTrendData.priceJson) || this.priceTrendData.priceJson.length === 0) {
                                this.showPriceTrend = false;
                                return;
                            }
                            
                            // 过滤掉没有价格数据或价格为0的点
                            this.priceTrendData.priceJson = this.priceTrendData.priceJson.filter(item => {
                                // 检查coalBlendingCost是否为有效数值且不为0
                                const cost = Number(item.coalBlendingCost);
                                return item.coalBlendingCost !== null && 
                                       item.coalBlendingCost !== undefined && 
                                       !isNaN(cost) &&
                                       cost > 0;
                            });
                            
                            // 如果过滤后没有有效数据，则隐藏图表
                            if (this.priceTrendData.priceJson.length === 0) {
                                this.showPriceTrend = false;
                                return;
                            }
                            
                            // 对日期进行排序，确保图表显示正确
                            this.priceTrendData.priceJson.sort((a, b) => {
                                return new Date(a.date) - new Date(b.date);
                            });
                            
                        } catch (e) {
                            console.error('价格数据解析错误:', e);
                            this.showPriceTrend = false;
                            return;
                        }
                    } else {
                        this.showPriceTrend = false;
                        return;
                    }
                    
                    this.updateChartData();
                } else {
                    this.showPriceTrend = false;
                }
            } catch (error) {
                // console.error('获取价格趋势数据失败:', error);
                this.showPriceTrend = false;
            }
        },
        
        initChart() {
            // 如果没有价格数据或DOM元素不存在，则不初始化图表
            if (!this.showPriceTrend || !this.$refs.chart) {
                return;
            }
            
            // 如果图表已经初始化，则先销毁
            if (this.priceTrendChart) {
                this.priceTrendChart.dispose();
            }
            
            // 初始化图表
            this.priceTrendChart = echarts.init(this.$refs.chart);
            
            // 添加窗口大小变化监听
            window.addEventListener('resize', this.resizeChart);
        },
        
        resizeChart() {
            // 只有在图表存在且显示时才调整大小
            if (this.priceTrendChart && this.showPriceTrend) {
                this.priceTrendChart.resize();
            }
        },
        
        updateChartData() {
            // 检查是否有价格数据
            if (!this.priceTrendData.priceJson || !this.priceTrendData.priceJson.length) {
                // 没有数据时隐藏图表
                this.showPriceTrend = false;
                return;
            }
            
            // 有数据时显示图表
            this.showPriceTrend = true;
            
            if (!this.priceTrendChart) {
                this.$nextTick(() => {
                    this.initChart();
                });
                return;
            }
            
            // 准备图表数据
            const dates = this.priceTrendData.priceJson.map(item => {
                // 格式化日期，只保留年月日
                return item.date.substring(0, 10);
            });
            
            const prices = this.priceTrendData.priceJson.map(item => {
                // 确保价格是数字
                return Number(item.coalBlendingCost);
            });
            
            this.priceTrendChart.setOption({
                title: {
                    left: 20,
                    top: 10,
                    text: this.entityForm.coalName || '价格趋势'
                },
                xAxis: {
                    type: 'category',
                    data: dates,
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: true,
                        width: 5,
                        symbol: ['none', 'arrow'],
                        symbolOffset: 30,
                        lineStyle: {
                            color: '#F5F5FF',
                            type: 'solid',
                            shadowOffsetX: 30,
                            shadowColor: '#F5F5FF'
                        }
                    },
                    axisLabel: {
                        color: '#9A9A9A'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisTick: {
                        show: false
                    },
                    scale: true,
                    splitLine: {
                        lineStyle: {
                            color: '#F5F5FF'
                        }
                    },
                    axisLine: {
                        show: true,
                        width: 5,
                        symbol: ['none', 'arrow'],
                        symbolOffset: 30,
                        lineStyle: {
                            color: '#F5F5FF',
                            type: 'solid',
                            shadowOffsetY: -30,
                            shadowColor: '#F5F5FF'
                        }
                    },
                    axisLabel: {
                        showMaxLabel: false,
                        color: '#9A9A9A'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        const param = params[0];
                        return `${param.name}<br/>${param.seriesName}: ${param.value}`;
                    }
                },
                legend: {
                    type: 'plain',
                    data: ['价格'],
                    bottom: 0,
                    selected: {
                        '价格': true
                    }
                },
                grid: {
                    left: '25',
                    right: '50',
                    top: '80',
                    bottom: '30',
                    containLabel: true
                },
                series: [
                    {
                        data: prices,
                        type: 'line',
                        name: '价格',
                        label: {
                            show: true,
                            color: '#595EC9',
                            formatter: '{c}'
                        },
                        showAllSymbol: true,
                        lineStyle: {
                            color: '#595EC9'
                        },
                        itemStyle: {
                            color: '#595EC9'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: '#F5F5FF'
                                },
                                {
                                    offset: 1,
                                    color: 'white'
                                }
                            ])
                        }
                    }
                ]
            });
        },
        /**
         * Fetch coal ash composition data if not included in the initial data
         */
        async fetchAshCompositionData() {
            if (!this.entityForm || !this.entityForm.id) return;
            
            try {
                const res = await Func.fetch(coalAshList, { 
                    filter_EQS_coalId: this.entityForm.id 
                });
                
                if (res.data && res.data.records && res.data.records.length > 0) {
                    const ashData = res.data.records[0];
                    
                    // 确保CRI和CSR数据处理
                    const formatQualityValue = (value) => {
                        if (value === null || value === undefined) return '0.0';
                        return typeof value === 'number' ? value.toFixed(1) : value.toString();
                    };
                    
                    // 处理CRI和CSR数据
                    let criValue = this.entityForm.cri;
                    let csrValue = this.entityForm.csr;
                    
                    // 如果当前cri/csr为空，尝试从ashData获取
                    if ((criValue === null || criValue === undefined || criValue === '') && 
                        ashData.cri !== null && ashData.cri !== undefined && ashData.cri !== '') {
                        criValue = ashData.cri;
                    }
                    
                    // 如果仍然为空，尝试从qualCri获取
                    if ((criValue === null || criValue === undefined || criValue === '') && 
                        ashData.qualCri !== null && ashData.qualCri !== undefined && ashData.qualCri !== '') {
                        criValue = ashData.qualCri;
                    }
                    
                    // 对CSR进行同样处理
                    if ((csrValue === null || csrValue === undefined || csrValue === '') && 
                        ashData.csr !== null && ashData.csr !== undefined && ashData.csr !== '') {
                        csrValue = ashData.csr;
                    }
                    
                    if ((csrValue === null || csrValue === undefined || csrValue === '') && 
                        ashData.qualCsr !== null && ashData.qualCsr !== undefined && ashData.qualCsr !== '') {
                        csrValue = ashData.qualCsr;
                    }
                    
                    // 记录日志，确认数据
                    console.log('fetchAshCompositionData处理后的CRI和CSR数据:', {
                        cri: criValue,
                        csr: csrValue
                    });

                    // 更新entityForm中的灰成分数据和CRI/CSR
                    this.entityForm = {
                        ...this.entityForm,
                        siO2: ashData.siO2 || this.entityForm.siO2 || '',
                        al2O3: ashData.al2O3 || this.entityForm.al2O3 || '',
                        fe2O3: ashData.fe2O3 || this.entityForm.fe2O3 || '',
                        caO: ashData.caO || this.entityForm.caO || '',
                        mgO: ashData.mgO || this.entityForm.mgO || '',
                        na2O: ashData.na2O || this.entityForm.na2O || '',
                        k2O: ashData.k2O || this.entityForm.k2O || '',
                        tiO2: ashData.tiO2 || this.entityForm.tiO2 || '',
                        p2O5: ashData.p2O5 || this.entityForm.p2O5 || '',
                        so3: ashData.so3 || this.entityForm.so3 || '',
                        mnO2: ashData.mnO2 || this.entityForm.mnO2 || '',
                        cri: criValue,
                        csr: csrValue
                    };
                    
                    // 格式化所有灰成分数据
                    this.formatAllAshFields();
                    
                    // 更新indexlist中的CRI和CSR
                    if (this.entityForm.indexlist && this.entityForm.indexlist.length > 0) {
                        this.entityForm.indexlist[0].cri = criValue;
                        this.entityForm.indexlist[0].csr = csrValue;
                        
                        // 刷新表格
                        this.$nextTick(() => {
                            this.$refs.editTable2.getHotInstance().loadData([...this.entityForm.indexlist]);
                        });
                    }
                    
                    console.log('更新后的灰成分和CRI/CSR数据:', this.entityForm);
                }
            } catch (error) {
                console.error('Failed to fetch ash composition data:', error);
            }
        },
        /**
         * 验证表单数据的有效性
         * @returns {Object} 包含验证结果和错误信息
         */
        validateFormData() {
            // 验证基本信息
            if (!this.entityForm.coalName) {
                return { valid: false, error: '煤炭名称不能为空' };
            }
            
            if (!this.entityForm.coalCategoryName) {
                return { valid: false, error: '煤种不能为空' };
            }
            
            // 检查ID
            if (!this.details.id && !this.entityForm.id) {
                return { valid: false, error: '缺少煤炭ID' };
            }
            
            // 验证数值范围
            const numericFields = [
                { name: 'ad', label: '灰分Ad', min: 0, max: 100 },
                { name: 'std', label: '硫分St,d', min: 0, max: 10 },
                { name: 'vdaf', label: '挥发分Vdaf', min: 0, max: 100 },
                { name: 'mt', label: '水分Mt', min: 0, max: 100 },
                { name: 'cri', label: 'CRI', min: 0, max: 100 },
                { name: 'csr', label: 'CSR', min: 0, max: 100 }
            ];
            
            for (const field of numericFields) {
                const value = this.entityForm[field.name];
                if (value !== null && value !== undefined && value !== '') {
                    const numValue = parseFloat(value);
                    if (isNaN(numValue)) {
                        return { valid: false, error: `${field.label}必须是有效的数字` };
                    }
                    
                    if (field.min !== undefined && numValue < field.min) {
                        return { valid: false, error: `${field.label}不能小于${field.min}` };
                    }
                    
                    if (field.max !== undefined && numValue > field.max) {
                        return { valid: false, error: `${field.label}不能大于${field.max}` };
                    }
                }
            }
            
            return { valid: true };
        },
        /**
         * 明确保存运费字段
         * 单独处理运费字段，确保它被正确保存
         */
        saveTransportFee() {
            const id = this.entityForm.id || this.details.id;
            if (!id) {
                console.error('保存运费失败：缺少ID');
                return Promise.reject('缺少ID');
            }
            
            // 获取运费值
            let transportFee = null;
            const basicInfo = this.$refs.editTable.getHotInstance().getSourceData()[0] || {};
            
            if (basicInfo.activePriceTransportPriceNoTax !== undefined) {
                transportFee = parseFloat(basicInfo.activePriceTransportPriceNoTax);
                if (isNaN(transportFee)) transportFee = 0;
            } else if (this.entityForm.activePriceTransportPriceNoTax !== undefined) {
                transportFee = parseFloat(this.entityForm.activePriceTransportPriceNoTax);
                if (isNaN(transportFee)) transportFee = 0;
            }
            
            console.log('独立保存运费值:', transportFee);
            
            // 构建最小数据包，只包含ID和运费字段
            const minimalData = {
                id: id,
                activePriceTransportPriceNoTax: transportFee
            };
            
            // 使用与model.update相同的数据格式
            return Func.fetch(saveCoal, { data: [minimalData] })
                .then(response => {
                    console.log('运费保存成功:', response);
                    return response;
                })
                .catch(error => {
                    console.error('运费保存失败:', error);
                    throw error;
                });
        },
        formatInputValue(field) {
            // 获取当前值
            const value = this.entityForm[field];
            
            // 如果为空，不处理
            if (value === null || value === undefined || value === '') {
                return;
            }
            
            // 转换为数字
            const numValue = parseFloat(value);
            if (isNaN(numValue)) {
                this.entityForm[field] = '';
                return;
            }
            
            // 保留两位小数
            this.entityForm[field] = numValue.toFixed(2);
        },
        formatAllAshFields() {
            // 格式化所有灰成分字段
            const ashFields = ['siO2', 'al2O3', 'fe2O3', 'caO', 'mgO', 'na2O', 'k2O', 'tiO2', 'p2O5', 'so3', 'mnO2'];
            ashFields.forEach(field => {
                this.formatInputValue(field);
            });
        },
        async getDataList() {
            try {
                console.log('开始获取反射率范围数据');
                const { data } = await getRangeListApi();
                this.rangeList = data;
                this.rangeAllList = data; // 保存完整的反射率范围数据
                console.log('获取到完整的反射率范围数据:', this.rangeAllList);
                return data;
            } catch (e) {
                console.error('获取反射率范围数据失败:', e);
                this.rangeList = [];
                this.rangeAllList = [];
                return [];
            }
        },
    },

    watch: {
        addVisible(v) {
            this.visible = v
        },
        details: {
            async handler(value) {
                console.log('详情数据变化:', value);
                
                // 如果数据已经预处理过并且是从列表点击进入的，不需要重复请求API
                if (value && value._dataPreprocessed && value._fromListClick) {
                    console.log('数据已预处理，跳过API请求');
                    
                    try {
                        // 不再重复请求getRangeListApi，只在需要时获取一次
                        if (!this.allListRange || this.allListRange.length === 0) {
                            const allListRange = await Func.fetch(getRangeListApi);
                            this.allListRange = allListRange.data;
                            console.log('获取反射率范围数据完成');
                        }
                        
                        // 确保基础信息格式正确
                        const basicInfo = value.basicinformation && value.basicinformation.length > 0 ? 
                            value.basicinformation[0] : {
                                coalName: value.coalName || '',
                                coalCategoryName: value.coalCategoryName || '',
                                location: value.location || '',
                                factoryPrice: value.factoryPrice || '',
                                activePriceTransportPriceNoTax: value.activePriceTransportPriceNoTax || '',
                                coalBlendingCost: value.coalBlendingCost || ''
                            };
                        
                        // 确保指标数据格式正确
                        const indexInfo = value.indexlist && value.indexlist.length > 0 ?
                            value.indexlist[0] : {
                                ad: value.ad || '',
                                std: value.std || '',
                                vdaf: value.vdaf || '',
                                g: value.g || '',
                                y: value.y || '',
                                x: value.x || '',
                                mt: value.mt || '',
                                macR0: value.macR0 || '',
                                macS: value.macS || '',
                                cri: value.cri || '',
                                csr: value.csr || ''
                            };
                        
                        // 使用已有的rangeValues或coalRockList
                        let rangeList = null;
                        if (value.rangeValues && value.rangeValues.length > 0) {
                            console.log('使用预处理的rangeValues数据');
                            rangeList = value.rangeValues;
                        } else if (value.coalRockList && value.coalRockList.length > 0) {
                            console.log('使用预处理的coalRockList数据');
                            rangeList = value.coalRockList;
                        } else {
                            console.log('使用默认反射率范围数据');
                            rangeList = this.allListRange;
                        }
                        
                        // 更新entityForm
                        this.entityForm = {
                            ...value,
                            basicinformation: [basicInfo],
                            indexlist: [indexInfo]
                        };
                        
                        // 使用已有数据初始化反射率表格
                        await this.getCreateData({
                            list: rangeList
                        });
                        
                        // 延迟更新表格数据
                        this.$nextTick(() => {
                            if (this.$refs.editTable && this.$refs.editTable.getHotInstance) {
                                console.log('更新基础信息表格');
                                this.$refs.editTable.getHotInstance().loadData([basicInfo]);
                            }
                            
                            if (this.$refs.editTable2 && this.$refs.editTable2.getHotInstance) {
                                console.log('更新指标表格');
                                this.$refs.editTable2.getHotInstance().loadData([indexInfo]);
                            }
                            
                            // 初始化图表和获取价格趋势数据
                            this.initChart();
                            this.getByPrice();
                        });
                        
                        return; // 提前返回，不执行后续API请求
                    } catch (error) {
                        console.error('处理预处理数据出错:', error);
                        // 出错时继续执行后续代码，尝试正常流程
                    }
                }
                
                try {
                    // 不再重复请求getRangeListApi，只在需要时获取一次
                    if (!this.allListRange || this.allListRange.length === 0) {
                        const allListRange = await Func.fetch(getRangeListApi);
                        this.allListRange = allListRange.data;
                        console.log('获取反射率范围数据完成');
                    }
                    
                    if (value && value.id) {
                        console.log('加载煤炭ID详情:', value.id);
                        
                        // 确保基础信息格式正确
                        const basicInfo = {
                            coalName: value.coalName || '',
                            coalCategoryName: value.coalCategoryName || '',
                            location: value.location || '',
                            factoryPrice: value.factoryPrice || '',
                            activePriceTransportPriceNoTax: value.activePriceTransportPriceNoTax || '',
                            coalBlendingCost: value.coalBlendingCost || ''
                        };
                        
                        // 确保指标数据格式正确
                        const indexInfo = {
                            ad: value.ad || '',
                            std: value.std || '',
                            vdaf: value.vdaf || '',
                            g: value.g || '',
                            y: value.y || '',
                            x: value.x || '',
                            mt: value.mt || '',
                            macR0: value.macR0 || '',
                            macS: value.macS || '',
                            cri: value.cri || '',
                            csr: value.csr || ''
                        };
                        
                        // 获取完整数据
                        console.log('请求详情数据，参数:', {
                            id: value.id,
                            type: value.type,
                            coalSource: value.coalSource
                        });
                        
                        const {data: info} = await this.model.get({
                            id: value.id,
                            type: value.type,  // Use type for source identification
                            coalSource: value.coalSource
                        });
                        
                        console.log('获取到的详情数据:', info);
                        
                        if (info) {
                            // 更新基础信息和指标数据
                            Object.assign(basicInfo, {
                                coalName: info.coalName || basicInfo.coalName,
                                coalCategoryName: info.coalCategoryName || basicInfo.coalCategoryName,
                                location: info.location || basicInfo.location,
                                factoryPrice: info.factoryPrice || basicInfo.factoryPrice,
                                activePriceTransportPriceNoTax: info.activePriceTransportPriceNoTax || basicInfo.activePriceTransportPriceNoTax,
                                coalBlendingCost: info.coalBlendingCost || basicInfo.coalBlendingCost
                            });
                            
                            Object.assign(indexInfo, {
                                ad: info.ad || indexInfo.ad,
                                std: info.std || indexInfo.std,
                                vdaf: info.vdaf || indexInfo.vdaf,
                                g: info.g || indexInfo.g,
                                y: info.y || indexInfo.y,
                                x: info.x || indexInfo.x,
                                mt: info.mt || indexInfo.mt,
                                macR0: info.macR0 || indexInfo.macR0,
                                macS: info.macS || indexInfo.macS,
                                cri: info.cri || indexInfo.cri,
                                csr: info.csr || indexInfo.csr
                            });
                            
                            // 使用API返回的rangeValues或coalRockList，避免额外请求
                            let rangeList = null;
                            if (info.rangeValues && info.rangeValues.length > 0) {
                                console.log('使用API返回的rangeValues数据');
                                rangeList = info.rangeValues;
                            } else if (info.coalRockList && info.coalRockList.length > 0) {
                                console.log('使用API返回的coalRockList数据');
                                rangeList = info.coalRockList;
                            } else {
                                // 如果API没有返回反射率数据，使用默认的allListRange
                                console.log('使用默认反射率范围数据');
                                rangeList = this.allListRange;
                            }

                    // Format ash composition data for display
                    const formatAshValue = (value) => {
                        if (value === undefined || value === null || value === '') {
                          return '';
                        }
                        
                        // 将值转换为数字
                        const numValue = parseFloat(value);
                        if (isNaN(numValue)) return '';
                        
                        // 检查是否为整数
                        if (Math.floor(numValue) === numValue) {
                          // 如果是整数，返回整数形式
                          return Math.floor(numValue);
                        } else {
                          // 如果是小数，保留两位小数
                          return numValue.toFixed(2);
                        }
                      };

                    // Map ash composition values
                    const ashComposition = {
                        siO2: formatAshValue(info.siO2),
                        al2O3: formatAshValue(info.al2O3),
                        fe2O3: formatAshValue(info.fe2O3),
                        caO: formatAshValue(info.caO),
                        mgO: formatAshValue(info.mgO),
                        na2O: formatAshValue(info.na2O),
                        k2O: formatAshValue(info.k2O),
                        tiO2: formatAshValue(info.tiO2),
                        p2O5: formatAshValue(info.p2O5),
                        so3: formatAshValue(info.so3),
                        mnO2: formatAshValue(info.mnO2)
                    };

                            // 直接更新 entityForm
                    this.entityForm = {
                                ...value,
                        ...info,
                                basicinformation: [basicInfo],
                                indexlist: [indexInfo],
                                ...ashComposition
                            };
                            
                            // 使用已有数据初始化反射率表格，避免额外请求
                            console.log('使用rangeList初始化反射率表格:', rangeList);
                            await this.getCreateData({
                                list: rangeList
                            });
                        } else {
                            // 如果没有获取到详情数据，使用初始数据
                            this.entityForm = {
                                ...value,
                                basicinformation: [basicInfo],
                                indexlist: [indexInfo]
                            };
                            
                            // 使用默认数据初始化反射率表格
                    await this.getCreateData({
                                list: this.allListRange
                            });
                        }
                        
                        console.log('已准备基础信息:', this.entityForm.basicinformation);
                        console.log('已准备指标信息:', this.entityForm.indexlist);
                        
                        // 延迟更新表格数据
                    this.$nextTick(() => {
                            if (this.$refs.editTable && this.$refs.editTable.getHotInstance) {
                                console.log('更新基础信息表格');
                                this.$refs.editTable.getHotInstance().loadData(this.entityForm.basicinformation);
                            } else {
                                console.warn('基础信息表格未初始化');
                            }
                            
                            if (this.$refs.editTable2 && this.$refs.editTable2.getHotInstance) {
                                console.log('更新指标表格');
                                this.$refs.editTable2.getHotInstance().loadData(this.entityForm.indexlist);
                            } else {
                                console.warn('指标表格未初始化');
                            }
                        
                        // Initialize chart and get price trend data
                        this.initChart();
                        this.getByPrice();
                        });
                        }
                } catch (error) {
                    console.error('加载详情数据出错:', error);
                }
            },
            deep: true,
            immediate: true
        }
    },
    created() {
        // 在组件创建时获取一次反射率范围数据，避免重复请求
        this.getDataList().then(data => {
            console.log('组件创建时获取到反射率范围数据，数量:', data.length);
        }).catch(err => {
            console.error('组件创建时获取反射率范围数据失败:', err);
        });
        this.getCategoryNamesList();
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
            if (this.entityForm.id) {
                this.getByPrice();
            }
            
            // 格式化所有灰成分字段
            this.formatAllAshFields();
        });
    },
    beforeDestroy() {
        // 移除事件监听
        window.removeEventListener('resize', this.resizeChart);
        
        // 销毁图表实例
        if (this.priceTrendChart) {
            this.priceTrendChart.dispose();
            this.priceTrendChart = null;
        }
    },
}
</script>

<style lang="scss" scoped>
@import '@/styles/router-page.scss';

::v-deep .el-collapse-item__arrow {
  display: none;
}

.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;

  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      padding: 6px 10px;
      margin-right: 5px;
      font-size: 12px;
      position: relative;

      &-link {
        font-size: 12px;
        opacity: 0.8;
      }

      .icon {
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 9;
        top: 0;
        flex: 0;
      }
    }
  }
}

.add {
  ::v-deep .el-dialog__body {
    padding: 0 35px;
    height: 80vh;
  }
}

::v-deep .el-form-item__label {
  font-weight: 400;
}

::v-deep .indicators .title {
  display: block;
}

.textarea {
  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }
}

::v-deep .el-form-item__content {
  margin-left: 0 !important;
}

.title-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title_ad {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-col-6 {
  display: flex;
  justify-content: flex-start;
}

.saveBtn {
  display: flex;
  justify-content: flex-end;
}

.dictSelected {
  max-width: 100%;

  ::v-deep .el-input__inner {
    max-width: 100%;
  }
}

.chart-table {
  width: 80%;
  margin: 0 auto;
  margin-bottom: 10px;
  // display: flex;
  // justify-content: center;
}

.price-trend-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ash-composition-container {
  display: flex;
  width: 100%;
  margin-top: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f9f9f9;
  padding: 20px 10px;
  box-sizing: border-box;
  overflow: hidden;
}

.ash-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.ash-row {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 10px;
}

.ash-row:last-child {
  margin-bottom: 0;
}

.ash-column {
  display: flex;
  align-items: center;
  width: 25%;
  padding: 0 10px;
  margin-bottom: 10px;
  box-sizing: border-box;
}

.ash-label {
  flex: 0 0 auto;
  width: 70px;
  text-align: right;
  margin-right: 10px;
  white-space: nowrap;
  overflow: visible;
  color: #606266;
  font-size: 14px;
}

.ash-value-container {
  flex: 1;
  display: flex;
  min-width: 0;
}

.ash-value {
  flex: 1;
  height: 36px;
  line-height: 36px;
  background-color: #ffffff;
  border: 1px solid #dcdfe6;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  text-align: center;
  color: #303133;
  padding: 0 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ash-unit {
  flex: 0 0 40px;
  height: 36px;
  line-height: 36px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-left: none;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  color: #909399;
  text-align: center;
}

.ash-input {
  width: 100%;
  max-width: 100%;
  
  ::v-deep .el-input__inner {
    height: 36px;
    padding: 0 8px;
    text-align: right;
    background-color: #f5f7fa;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

@media screen and (max-width: 1366px) {
  .ash-column {
    width: 33.33%;
  }
}

@media screen and (max-width: 992px) {
  .ash-column {
    width: 50%;
  }
  
  .ash-label {
    width: 60px;
  }
}

@media screen and (max-width: 768px) {
  .ash-column {
    width: 100%;
  }
}

.vip-notification {
    color: #ffffff;
  font-size: 16px;
  text-align: center;
  background-color: #b17b38;
  padding: 15px;
  margin: 10px 0;
  border-radius: 4px;
  margin-top: 50px;
}
</style>
