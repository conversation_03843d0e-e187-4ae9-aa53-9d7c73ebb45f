// eslint-disable-next-line

import BaseModel, { TableConfig, FormConfig } from '@/components/Common/SnProTable/model'
import CalcUtils from '@/utils/calcUtils'

import { dateUtil, formatToDateTime } from '@/utils/dateUtils'
import { isEmpty } from "@/utils/is";
import { fetchSetting, MERGE_TYPE } from "@/const";

export const name = `/cwe/a/coal`

const createFormConfig = () => {
    return {
        labelHideAll: false,
        fieldMapToTime: [['_dateRange', ['beginDate', 'endDate'], 'YYYY-MM-DD']],
        fieldMapToNumber: [
            ['ad', ['minAd', 'maxAd']],
            ['std', ['minStd', 'maxStd']],
            ['vdaf', ['minVdaf', 'maxVdaf']],
            ['g', ['minG', 'maxG']],
            ['y', ['minY', 'maxY']],
            ['csr', ['minCsr', 'maxCsr']],
        ],
        filters: [
            {
                label: '日期',
                hideLabel: true,
                prop: '_dateRange',
                component: 'SnDateRanger',
                defaultValue: [],
                componentProp: {
                    valueFormat: 'yyyy-MM-dd'
                },
                style: {
                    width: '220px'
                }
            },
            {
                label: '品名',
                hideLabel: true,
                prop: 'coalName',
                itemStyle: {
                    width: '110px',
                },
                componentProp: {}
            },
            {
                label: '煤种名称',
                hideLabel: true,
                prop: 'coalCategoryName',
                itemStyle: {
                    width: '110px',
                },
                componentProp: {}
            },
            {
                label: '产地',
                hideLabel: true,
                itemStyle: {
                    width: '100px',
                },
                componentProp: {},
                prop: 'location',
            },
            {
                label: 'Ad',
                prop: 'ad',
                hideLabel: true,
                component: 'SnSimpleInputRanger',
                defaultValue: [],
                style: {
                    width: '140px',
                },
                componentProp: {
                    useProFormItem: false,
                    startPlaceholder: '',
                    endPlaceholder: '',
                    rangeSeparator: '≤Ad≤ ',
                }
            },
            {
                label: 'Std',
                prop: 'std',
                itemStyle: {
                    width: '140px',
                },
                hideLabel: true,
                component: 'SnSimpleInputRanger',
                defaultValue: [],
                style: {
                    width: '140px',
                },
                componentProp: {
                    useProFormItem: false,
                    startPlaceholder: '',
                    endPlaceholder: '',
                    rangeSeparator: '≤Std≤ ',
                }
            },
            {
                label: 'Vdaf',
                prop: 'vdaf',
                hideLabel: true,
                component: 'SnSimpleInputRanger',
                defaultValue: [],
                style: {
                    width: '140px',
                },
                componentProp: {
                    useProFormItem: false,
                    startPlaceholder: '',
                    endPlaceholder: '',
                    rangeSeparator: '≤Vdaf≤ ',
                }
            },
            {
                label: 'G',
                prop: 'g',
                hideLabel: true,
                component: 'SnSimpleInputRanger',
                defaultValue: [],
                style: {
                    width: '140px',
                },
                componentProp: {
                    useProFormItem: false,
                    startPlaceholder: '',
                    endPlaceholder: '',
                    rangeSeparator: '≤G≤ ',
                }
            },
            {
                label: 'Y',
                prop: 'y',
                hideLabel: true,
                component: 'SnSimpleInputRanger',
                defaultValue: [],
                style: {
                    width: '140px',
                },
                componentProp: {
                    useProFormItem: false,
                    startPlaceholder: '',
                    endPlaceholder: '',
                    rangeSeparator: '≤Y≤ ',
                }
            },
            {
                label: 'CSR',
                prop: 'csr',
                hideLabel: true,
                component: 'SnSimpleInputRanger',
                defaultValue: [],
                style: {
                    width: '150px',
                },
                componentProp: {
                    useProFormItem: false,
                    startPlaceholder: '',
                    endPlaceholder: '',
                    rangeSeparator: '≤CSR≤ ',
                }
            },
            // {
            //     label: 'Fe2O3≤ ',
            //     itemStyle: {
            //         width: '140px',
            //     },
            //     componentProp: {
            //         placeholder: ' ',
            //     },
            //     prop: 'maxFe2O',
            // },
            // {
            //     label: 'CaO≤ ',
            //     itemStyle: {
            //         width: '130px',
            //     },
            //     componentProp: {
            //         placeholder: ' ',
            //     },
            //     prop: 'maxCaO',
            // },
            {
                label: 'MF≥ ',
                itemStyle: {
                    width: '130px',
                },
                componentProp: {
                    placeholder: ' ',
                },
                prop: 'minProcMf',
            },
        ],
    }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
    return {
        mountedQuery: true,
        showOpt: true, // 显示操作列
        showIndex: true, // 显示序号列
        showSelection: false, // 显示选择列
        useApiList: false,
        optConfig: { width: 150, label: '操作', prop: 'opt', fixed: 'right' },
        columns: [
            {
                prop: 'date',
                label: '更新日期',
                width: 100,
                fixed: true,
                slot: 'date',
                align: 'center',
            },
            {
                label: '煤种',
                prop: 'coalCategoryName',
                slot: 'coalCategoryName',
                isEdit: true,
                width: 70,
                fixed: true
            },
            {
                label: '品名',
                prop: 'coalName',
                slot: 'coalName',
                isEdit: true,
                width: 110,
                fixed: true
            },
            {
                "label": "含税价格",
                width: 90,
                "prop": "factoryPrice",
                slot: 'factoryPrice',
                isEdit: true,
            },
            {
                "label": "运费",
                width: 90,
                "prop": "activePriceTransportPriceNoTax",
                slot: 'activePriceTransportPriceNoTax',
                isEdit: true,
            },
            {
              "label": "配煤成本",
              width: 90,
              "prop": "coalBlendingCost",
              slot: 'coalBlendingCost',
              isEdit: true,
            },
            
            // {
            //     label: '来源',
            //     prop: 'coalSource',
            //     width: 70,
            //     // slot: 'coalSource',
            //     // isEdit: true,
            //     format: 'dict|b_coal_source_type'
            // },

            // {
            //     prop: 'stock',
            //     label: '库存',
            //     align: 'center',
            //     slot: 'stock',
            //     width: 70,
            //     isEdit: true,
            // },
            {
                prop: 'mt',
                label: '水分Mt',
                align: 'center',
                width: 70,
                slot: 'mt',
                isEdit: true,
            },
            {
                prop: 'ad',
                label: '灰分Ad',
                align: 'center',
                slot: 'ad',
                width: 70,
                isEdit: true,

            },
            {
                prop: 'std',
                label: '硫分St,d',
                align: 'center',
                slot: 'std',
                width: 70,
                isEdit: true,

            },
            {
                prop: 'vdaf',
                label: '挥发分Vdaf',
                width: 90,
                align: 'center',
                slot: 'vdaf',
                isEdit: true,

            },
            {
                prop: 'g',
                label: '粘结G',
                align: 'center',
                width: 70,
                slot: 'g',
                isEdit: true,
            },
            {
                prop: 'y',
                align: 'center',
                width: 70,
                label: '胶质层厚度Y',
                slot: 'y',
                isEdit: true,
            },

            {
                prop: 'x',
                align: 'center',
                width: 70,
                label: '最终收缩度X',
                slot: 'x',
                isEdit: true,
            },
            {
                "label": "CRI",
                width: 70,
                "prop": "cri",
                slot: 'cri',
                isEdit: true,
                formatter: function(row) {
                    // 确保显示CRI数据，检查多个可能的来源
                    if (row.cri !== undefined && row.cri !== null && row.cri !== '') {
                        return row.cri;
                    } else if (row.qualCri !== undefined && row.qualCri !== null && row.qualCri !== '') {
                        return row.qualCri;
                    }
                    return row.type === 'zsmy' ? '待获取' : '0.0';
                }
            },
            {
                "label": "CSR",
                width: 70,
                "prop": "csr",
                slot: 'csr',
                isEdit: true,
                formatter: function(row) {
                    // 确保显示CSR数据，检查多个可能的来源
                    if (row.csr !== undefined && row.csr !== null && row.csr !== '') {
                        return row.csr;
                    } else if (row.qualCsr !== undefined && row.qualCsr !== null && row.qualCsr !== '') {
                        return row.qualCsr;
                    }
                    return row.type === 'zsmy' ? '待获取' : '0.0';
                }
            },

            {
                "label": "反射率",
                width: 70,
                "prop": "macR0",
                slot: 'macR0',
                decimalPlaces: 3,
                isEdit: true,
            },
            {
                "label": "标准差",
                width: 70,
                "prop": "macS",
                slot: 'macS',
                decimalPlaces: 3,
                isEdit: true,

            },
            {
                "label": "最大流动度dd/min",
                "prop": "procMf",
                slot: 'procMf',
                isEdit: true,
            },
            {
                "label": "奥亚b",
                "prop": "procB",
                slot: 'procB',
                isEdit: true,

            },
            {
                "label": "奥亚a",
                "prop": "procA",
                slot: 'procA',
                isEdit: true,

            },
            // {
            //     "label": "siO2",
            //     "prop": "siO2",
            //     slot: 'siO2',
            //     isEdit: true,
            // },

            // {
            //     "label": "al2O3",
            //     "prop": "al2O3",
            //     slot: 'al2O3',
            //     isEdit: true,
            // },
            // {
            //     "label": "fe2O3",
            //     "prop": "fe2O3",
            //     slot: 'fe2O3',
            //     isEdit: true,
            // },

            // {
            //     "label": "caO",
            //     "prop": "caO",
            //     slot: 'caO',
            //     isEdit: true,
            // },

            // {
            //     "label": "k2O",
            //     "prop": "k2O",
            //     slot: 'k2O',
            //     isEdit: true,
            // },

            // {
            //     "label": "na2O",
            //     "prop": "na2O",
            //     slot: 'na2O',
            //     isEdit: true,
            // },

            // {
            //     "label": "mgO%",
            //     "prop": "mgO",
            //     slot: 'mgO',
            //     isEdit: true,
            // },

            // {
            //     "label": "tiO2%",
            //     "prop": "tiO2",
            //     slot: 'tiO2',
            //     isEdit: true,
            // },


            // {
            //     "label": "p2O5%",
            //     "prop": "p2O5",
            //     slot: 'p2O5',
            //     isEdit: true,
            // },

            // {
            //     "label": "SO3%",
            //     "prop": "so3",
            //     slot: 'so3',
            //     isEdit: true,
            //     formatter: function(row) {
            //         if (row.so3 !== undefined && row.so3 !== null) {
            //             return row.so3;
            //         }
            //         return '0.0';
            //     }
            // },

            {
                "label": "MCI%",
                "prop": "mci",
                slot: 'mci',
                isEdit: true,
            },
            // {
            //     'label': '0.25-0.30',
            //     'prop': '0.25-0.30',
            //     formatter: function(row) {
            //       if (row['0.25-0.30']) {
            //         return row['0.25-0.30'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.30-0.35',
            //     'prop': '0.30-0.35',
            //     formatter: function(row) {
            //       if (row['0.30-0.35']) {
            //         return row['0.30-0.35'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.35-0.40',
            //     'prop': '0.35-0.40',
            //     formatter: function(row) {
            //       if (row['0.35-0.40']) {
            //         return row['0.35-0.40'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.40-0.45',
            //     'prop': '0.40-0.45',
            //     formatter: function(row) {
            //       if (row['0.40-0.45']) {
            //         return row['0.40-0.45'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.45-0.50',
            //     'prop': '0.45-0.50',
            //     formatter: function(row) {
            //       if (row['0.45-0.50']) {
            //         return row['0.45-0.50'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.50-0.55',
            //     'prop': '0.50-0.55',
            //     formatter: function(row) {
            //       if (row['0.50-0.55']) {
            //         return row['0.50-0.55'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.55-0.60',
            //     'prop': '0.55-0.60',
            //     formatter: function(row) {
            //       if (row['0.55-0.60']) {
            //         return row['0.55-0.60'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.60-0.65',
            //     'prop': '0.60-0.65',
            //     formatter: function(row) {
            //       if (row['0.60-0.65']) {
            //         return row['0.60-0.65'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.65-0.70',
            //     'prop': '0.65-0.70',
            //     formatter: function(row) {
            //       if (row['0.65-0.70']) {
            //         return row['0.65-0.70'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.70-0.75',
            //     'prop': '0.70-0.75',
            //     formatter: function(row) {
            //       if (row['0.70-0.75']) {
            //         return row['0.70-0.75'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.75-0.80',
            //     'prop': '0.75-0.80',
            //     formatter: function(row) {
            //       if (row['0.75-0.80']) {
            //         return row['0.75-0.80'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.80-0.85',
            //     'prop': '0.80-0.85',
            //     formatter: function(row) {
            //       if (row['0.80-0.85']) {
            //         return row['0.80-0.85'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.85-0.90',
            //     'prop': '0.85-0.90',
            //     formatter: function(row) {
            //       if (row['0.85-0.90']) {
            //         return row['0.85-0.90'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.90-0.95',
            //     'prop': '0.90-0.95',
            //     formatter: function(row) {
            //       if (row['0.90-0.95']) {
            //         return row['0.90-0.95'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '0.95-1.00',
            //     'prop': '0.95-1.00',
            //     formatter: function(row) {
            //       if (row['0.95-1.00']) {
            //         return row['0.95-1.00'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.00-1.05',
            //     'prop': '1.00-1.05',
            //     formatter: function(row) {
            //       if (row['1.00-1.05']) {
            //         return row['1.00-1.05'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.05-1.10',
            //     'prop': '1.05-1.10',
            //     formatter: function(row) {
            //       if (row['1.05-1.10']) {
            //         return row['1.05-1.10'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.10-1.15',
            //     'prop': '1.10-1.15',
            //     formatter: function(row) {
            //       if (row['1.10-1.15']) {
            //         return row['1.10-1.15'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.15-1.20',
            //     'prop': '1.15-1.20',
            //     formatter: function(row) {
            //       if (row['1.15-1.20']) {
            //         return row['1.15-1.20'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.20-1.25',
            //     'prop': '1.20-1.25',
            //     formatter: function(row) {
            //       if (row['1.20-1.25']) {
            //         return row['1.20-1.25'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.25-1.30',
            //     'prop': '1.25-1.30',
            //     formatter: function(row) {
            //       if (row['1.25-1.30']) {
            //         return row['1.25-1.30'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.30-1.35',
            //     'prop': '1.30-1.35',
            //     formatter: function(row) {
            //       if (row['1.30-1.35']) {
            //         return row['1.30-1.35'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.35-1.40',
            //     'prop': '1.35-1.40',
            //     formatter: function(row) {
            //       if (row['1.35-1.40']) {
            //         return row['1.35-1.40'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.40-1.45',
            //     'prop': '1.40-1.45',
            //     formatter: function(row) {
            //       if (row['1.40-1.45']) {
            //         return row['1.40-1.45'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.45-1.50',
            //     'prop': '1.45-1.50',
            //     formatter: function(row) {
            //       if (row['1.45-1.50']) {
            //         return row['1.45-1.50'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.50-1.55',
            //     'prop': '1.50-1.55',
            //     formatter: function(row) {
            //       if (row['1.50-1.55']) {
            //         return row['1.50-1.55'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.55-1.60',
            //     'prop': '1.55-1.60',
            //     formatter: function(row) {
            //       if (row['1.55-1.60']) {
            //         return row['1.55-1.60'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.60-1.65',
            //     'prop': '1.60-1.65',
            //     formatter: function(row) {
            //       if (row['1.60-1.65']) {
            //         return row['1.60-1.65'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.65-1.70',
            //     'prop': '1.65-1.70',
            //     formatter: function(row) {
            //       if (row['1.65-1.70']) {
            //         return row['1.65-1.70'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.70-1.75',
            //     'prop': '1.70-1.75',
            //     formatter: function(row) {
            //       if (row['1.70-1.75']) {
            //         return row['1.70-1.75'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.75-1.80',
            //     'prop': '1.75-1.80',
            //     formatter: function(row) {
            //       if (row['1.75-1.80']) {
            //         return row['1.75-1.80'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.80-1.85',
            //     'prop': '1.80-1.85',
            //     formatter: function(row) {
            //       if (row['1.80-1.85']) {
            //         return row['1.80-1.85'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.85-1.90',
            //     'prop': '1.85-1.90',
            //     formatter: function(row) {
            //       if (row['1.85-1.90']) {
            //         return row['1.85-1.90'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.90-1.95',
            //     'prop': '1.90-1.95',
            //     formatter: function(row) {
            //       if (row['1.90-1.95']) {
            //         return row['1.90-1.95'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '1.95-2.00',
            //     'prop': '1.95-2.00',
            //     formatter: function(row) {
            //       if (row['1.95-2.00']) {
            //         return row['1.95-2.00'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '2.00-2.05',
            //     'prop': '2.00-2.05',
            //     formatter: function(row) {
            //       if (row['2.00-2.05']) {
            //         return row['2.00-2.05'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '2.05-2.10',
            //     'prop': '2.05-2.10',
            //     formatter: function(row) {
            //       if (row['2.05-2.10']) {
            //         return row['2.05-2.10'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '2.10-2.15',
            //     'prop': '2.10-2.15',
            //     formatter: function(row) {
            //       if (row['2.10-2.15']) {
            //         return row['2.10-2.15'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '2.15-2.20',
            //     'prop': '2.15-2.20',
            //     formatter: function(row) {
            //       if (row['2.15-2.20']) {
            //         return row['2.15-2.20'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '2.20-2.25',
            //     'prop': '2.20-2.25',
            //     formatter: function(row) {
            //       if (row['2.20-2.25']) {
            //         return row['2.20-2.25'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '2.25-2.30',
            //     'prop': '2.25-2.30',
            //     formatter: function(row) {
            //       if (row['2.25-2.30']) {
            //         return row['2.25-2.30'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '2.30-2.35',
            //     'prop': '2.30-2.35',
            //     formatter: function(row) {
            //       if (row['2.30-2.35']) {
            //         return row['2.30-2.35'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '2.35-2.40',
            //     'prop': '2.35-2.40',
            //     formatter: function(row) {
            //       if (row['2.35-2.40']) {
            //         return row['2.35-2.40'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '2.40-2.45',
            //     'prop': '2.40-2.45',
            //     formatter: function(row) {
            //       if (row['2.40-2.45']) {
            //         return row['2.40-2.45'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '2.45-2.50',
            //     'prop': '2.45-2.50',
            //     formatter: function(row) {
            //       if (row['2.45-2.50']) {
            //         return row['2.45-2.50'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '2.50-2.55',
            //     'prop': '2.50-2.55',
            //     formatter: function(row) {
            //       if (row['2.50-2.55']) {
            //         return row['2.50-2.55'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            //   {
            //     'label': '2.55-2.60',
            //     'prop': '2.55-2.60',
            //     formatter: function(row) {
            //       if (row['2.55-2.60']) {
            //         return row['2.55-2.60'].toFixed(1);
            //       }
            //       return '';
            //     }
            //   },
            // {
            //     "label": "热值",
            //     "prop": "caloricValue",
            //     slot: 'caloricValue',
            //     isEdit: true,
            // },
            {
                "label": "全称",
                "prop": "name",
                slot: 'name',
                isEdit: true,
            },
            // {
            //     "label": "产能",
            //     "prop": "capacity",
            //     slot: 'capacity',
            //     isEdit: true,
            // },
            
            {
                "label": "来源",
                width: 90,
                "prop": "type",
                visible: true,
                formatter: function(row) {
                    if (row.type === 'wb') {
                        return '外部';
                    } else if (row.type === 'zsmy') {
                        return '掌上煤源';
                    } else if (row.type === 'nb') {
                        return '内部';
                    } else if (row.source) {
                        return row.source;
                    }
                    return row.type || '未知';
                }
            },
            {
                "label": "产地",
                width: 100,
                "prop": "location",
                slot: 'location',
                isEdit: true,
            },
            {
                "label": "分类",
                "prop": "categoryType",
                slot: 'categoryType',
                isEdit: true,
            },
            {
                "label": "备注",
                "prop": "remarks",
                showOverflowTooltip: true,
                width: 100,
                slot: 'remarks',
                isEdit: true,
            },
            {
                label: '分析煤种',
                prop: 'analysisCategoryName',
                width: 80
              }
        ]
    }
}

export const getEditCols = () => {
    return createTableConfig().columns.filter(v => v.isEdit)
}

export class Model extends BaseModel {
    constructor() {
        super(name, createFormConfig(), createTableConfig())
    }

    async page(query) {
        const resp = await super.request({
            url: `/cwe/a/coalDatabase/data/page`,
            method: 'get',
            params: query
        })
        console.log('resp', resp)
        const getRangeValues = (v) => {
            try {
                const proportionValues = v.proportionContent ? JSON.parse(v.proportionContent) : []
                const rangeNameValues = v.rangeContent ? JSON.parse(v.rangeContent) : []
                const rangeValues = proportionValues.map((_, index) => ({
                    rangeName: rangeNameValues[index],
                    proportion: proportionValues[index],
                }))
                return rangeValues
            } catch (e) {
                console.log(e)
                return []
            }
        }

        resp.data[fetchSetting.listField].forEach(v => {
            v.rangeValues = getRangeValues(v)
            
            // 处理掌上煤源的CRI和CSR字段
            if (v.type === 'zsmy') {
                // 确保CRI和CSR字段有值
                if (v.cri === null || v.cri === undefined || v.cri === '') {
                    // 尝试从其他可能的字段获取值
                    if (v.qualCri !== null && v.qualCri !== undefined && v.qualCri !== '') {
                        v.cri = v.qualCri;
                    }
                }
                
                if (v.csr === null || v.csr === undefined || v.csr === '') {
                    // 尝试从其他可能的字段获取值
                    if (v.qualCsr !== null && v.qualCsr !== undefined && v.qualCsr !== '') {
                        v.csr = v.qualCsr;
                    }
                }
                
                // 记录日志，便于调试
                console.log('掌上煤源数据处理后:', {
                    id: v.id,
                    coalName: v.coalName,
                    type: v.type,
                    cri: v.cri,
                    csr: v.csr
                });
            }
        })
        return resp
    }


    get(query) {
        // 确保传入了有效的查询参数
        const params = { ...query };
        console.log('获取煤源详情参数:', params);
        
        // 确保有有效的ID
        if (!params.id) {
            console.error('获取煤源详情失败: 缺少ID参数');
            return Promise.reject(new Error('缺少ID参数'));
        }
        
        // 处理不同类型的煤源
        if (params.type === 'zsmy' && !params.coalSource) {
            params.coalSource = 'zy'; // 掌上煤焦
        } else if (params.type === 'wb' && !params.coalSource) {
            params.coalSource = 'zy'; // 外部煤源
        } else if (params.type === 'nb' && !params.coalSource) {
            params.coalSource = 'zy'; // 内部煤源
        } else if (!params.coalSource) {
            params.coalSource = 'zy'; // 默认值
        }
        
        console.log('发送获取煤源详情请求，完整参数:', params);
        
        return super.request({
            url: `/cwe/a/coalDatabase/data/get`,
            method: 'get',
            params: params
        }).then(response => {
            // 检查响应是否有效
            if (!response || !response.data) {
                console.error('煤源详情API返回无效数据', response);
                return Promise.reject(new Error('API返回无效数据'));
            }
            
            console.log('获取煤源详情成功:', response.data);
            
            // 确保煤灰成分数据正确格式化
            if (response.data) {
                // 处理CRI和CSR字段
                if (response.data.qualCri && (!response.data.cri || response.data.cri === '')) {
                    response.data.cri = response.data.qualCri;
                }
                
                if (response.data.qualCsr && (!response.data.csr || response.data.csr === '')) {
                    response.data.csr = response.data.qualCsr;
                }
                
                // 转换灰成分字段的字符串值为数字
                const ashFields = ['siO2', 'al2O3', 'fe2O3', 'caO', 'mgO', 'na2O', 'k2O', 'tiO2', 'p2O5', 'so3', 'mnO2'];
                
                ashFields.forEach(field => {
                    if (response.data[field] !== undefined && response.data[field] !== null) {
                        const numValue = parseFloat(response.data[field]);
                        if (!isNaN(numValue)) {
                            response.data[field] = numValue;
                        }
                    }
                });
                
                // 处理其他数值类型字段
                const numericFields = ['ad', 'std', 'vdaf', 'g', 'y', 'x', 'mt', 'cri', 'csr', 'macR0', 'macS', 'factoryPrice', 'activePriceTransportPriceNoTax', 'coalBlendingCost'];
                numericFields.forEach(field => {
                    if (response.data[field] !== undefined && response.data[field] !== null && response.data[field] !== '') {
                        const numValue = parseFloat(response.data[field]);
                        if (!isNaN(numValue)) {
                            response.data[field] = numValue;
                        }
                    }
                });
                
                // 确保 basicinformation 存在
                if (!response.data.basicinformation || !response.data.basicinformation.length) {
                    response.data.basicinformation = [{
                        coalName: response.data.coalName,
                        coalCategoryName: response.data.coalCategoryName,
                        location: response.data.location,
                        factoryPrice: response.data.factoryPrice,
                        activePriceTransportPriceNoTax: response.data.activePriceTransportPriceNoTax,
                        coalBlendingCost: response.data.coalBlendingCost,
                        type: params.type
                    }];
                }
                
                // 确保 indexlist 存在
                if (!response.data.indexlist || !response.data.indexlist.length) {
                    response.data.indexlist = [{
                        ad: response.data.ad,
                        std: response.data.std,
                        vdaf: response.data.vdaf,
                        g: response.data.g,
                        y: response.data.y,
                        x: response.data.x,
                        mt: response.data.mt,
                        macR0: response.data.macR0,
                        macS: response.data.macS,
                        cri: response.data.cri,
                        csr: response.data.csr
                    }];
                }
                
                // 处理煤岩含量数据，避免额外请求
                if (response.data.coalRockContent && typeof response.data.coalRockContent === 'string') {
                    try {
                        const rockContent = JSON.parse(response.data.coalRockContent);
                        if (Array.isArray(rockContent)) {
                            // 对于掌上煤源类型，处理反射率数据，将0值标记为特殊值
                            if (params.type === 'zsmy') {
                                rockContent.forEach(item => {
                                    if (item.proportion === 0 || item.proportion === '0' || 
                                        item.proportion === 0.0 || item.proportion === '0.0' || 
                                        item.proportion === 0.00 || item.proportion === '0.00' ||
                                        item.proportion === '' || item.proportion === null || item.proportion === undefined) {
                                        item.proportion = '***';
                                        item._isZeroValue = true; // 添加标记，表示这是一个需要特殊处理的零值
                                    }
                                });
                            }
                            
                            response.data.coalRockList = rockContent;
                            response.data.rangeValues = rockContent.map(item => ({
                                rangeName: item.rangeName,
                                proportion: item.proportion,
                                _isZeroValue: item._isZeroValue // 传递标记
                            }));
                        }
                    } catch (e) {
                        console.error('解析煤岩含量数据失败:', e);
                    }
                }
                
                // 如果有proportionContent和rangeContent，构建rangeValues
                if (!response.data.rangeValues && response.data.proportionContent && response.data.rangeContent) {
                    try {
                        const proportionValues = typeof response.data.proportionContent === 'string' ? 
                            JSON.parse(response.data.proportionContent) : response.data.proportionContent;
                        const rangeNameValues = typeof response.data.rangeContent === 'string' ? 
                            JSON.parse(response.data.rangeContent) : response.data.rangeContent;
                        
                        if (Array.isArray(proportionValues) && Array.isArray(rangeNameValues)) {
                            response.data.rangeValues = proportionValues.map((proportion, index) => {
                                // 对于掌上煤源类型，处理反射率数据，将0值标记为特殊值
                                let propValue = proportion || 0;
                                let isZeroValue = false;
                                
                                if (params.type === 'zsmy' && 
                                    (propValue === 0 || propValue === '0' || 
                                     propValue === 0.0 || propValue === '0.0' || 
                                     propValue === 0.00 || propValue === '0.00' ||
                                     propValue === '' || propValue === null || propValue === undefined)) {
                                    propValue = '***';
                                    isZeroValue = true;
                                }
                                
                                return {
                                    rangeName: rangeNameValues[index] || '',
                                    proportion: propValue,
                                    _isZeroValue: isZeroValue // 添加标记
                                };
                            });
                        }
                    } catch (e) {
                        console.error('解析反射率数据失败:', e);
                    }
                }
            }
            
            return response;
        }).catch(error => {
            console.error('获取煤源详情失败:', error);
            return Promise.reject(error);
        });
    }

    // getByPrice(query) {
    //     return super.request({
    //         url: `/cwe/a/coalSourceOutside/getByPrice`,
    //         method: 'get',
    //         params: query
    //     })
    // }

    add(data) {
        Model.formatSubmitData(data)
        return super.request({
            url: `/cwe/a/coalDatabase/data/add`,
            method: 'post',
            data: {
                ...data,
                ContentType: 'application/json'
            }
        })
    }


    static formatSubmitData(data) {
        data.data.forEach(v => {
            // 添加日志跟踪factoryPrice和相关字段的值
            console.log('提交前的数据:', {
                factoryPrice: v.factoryPrice,
                activePriceCoalPriceWithTax: v.activePriceCoalPriceWithTax,
                activePriceCoalPriceNoTax: v.activePriceCoalPriceNoTax,
                activePriceTransportPriceNoTax: v.activePriceTransportPriceNoTax
            });
            
            // 不含税煤价为空，含税煤价有值时
            if (!v.activePriceCoalPriceNoTax && v.activePriceCoalPriceWithTax) {
                v.activePriceCoalPriceNoTax = CalcUtils.getShowValue(CalcUtils.divide(v.activePriceCoalPriceWithTax, 1.13), 2)
            }
            if (!v.activePriceCoalPriceWithTax && v.activePriceCoalPriceNoTax) {
                v.activePriceCoalPriceWithTax = CalcUtils.getShowValue(CalcUtils.multiply(v.activePriceCoalPriceNoTax, 1.13), 2)
            }
            const isEmpty = (item) => ['', undefined, null].includes(item)
            if (!isEmpty(v.activePriceFactoryPriceNoTax)) {
                v.activePriceFactoryPriceNoTax = CalcUtils.getShowValue(v.activePriceFactoryPriceNoTax, 2)
            }
            if (!isEmpty(v.reduceMtFactoryPriceNoTax)) {
                v.reduceMtFactoryPriceNoTax = CalcUtils.getShowValue(v.reduceMtFactoryPriceNoTax, 2)
            }
            if (!isEmpty(v.coalBlendingCost)) {
                v.coalBlendingCost = CalcUtils.getShowValue(v.coalBlendingCost, 2)
            }
            
            // 确保运费字段被正确设置
            if (!isEmpty(v.activePriceTransportPriceNoTax)) {
                v.activePriceTransportPriceNoTax = CalcUtils.getShowValue(v.activePriceTransportPriceNoTax, 2)
            }
            // 即使运费值为0也要保留
            else if (v.activePriceTransportPriceNoTax === 0) {
                v.activePriceTransportPriceNoTax = 0;
            }
            
            // 确保factoryPrice字段被正确设置 (含税煤价)
            if (!isEmpty(v.factoryPrice)) {
                v.factoryPrice = CalcUtils.getShowValue(v.factoryPrice, 2)
                // 同步到activePriceCoalPriceWithTax字段
                v.activePriceCoalPriceWithTax = v.factoryPrice
            } 
            // 如果没有factoryPrice但有activePriceCoalPriceWithTax
            else if (!isEmpty(v.activePriceCoalPriceWithTax)) {
                v.factoryPrice = v.activePriceCoalPriceWithTax
            }
            
            // 检查是否有强制保存灰成分的标记
            const hasForceAshSave = v._forceAshSave === true;
            
            // 处理煤灰成分数据，确保数值有效
            const formatAshValue = (value) => {
                if (value === undefined || value === null || value === '') {
                    return '';
                }
                
                // 将值转换为数字
                const numValue = parseFloat(value);
                if (isNaN(numValue)) return '';
                
                // 检查是否为整数
                if (Math.floor(numValue) === numValue) {
                    // 如果是整数，返回整数形式
                    return Math.floor(numValue);
                } else {
                    // 如果是小数，保留两位小数
                    return numValue.toFixed(2);
                }
            };
            
            // 特殊处理灰成分数据
            const ashFieldNames = ['siO2', 'al2O3', 'fe2O3', 'caO', 'mgO', 'na2O', 'k2O', 'tiO2', 'p2O5', 'so3', 'mnO2'];
            ashFieldNames.forEach(field => {
                if (v[field] !== undefined) {
                    v[field] = formatAshValue(v[field]);
                    // 记录处理后的灰成分值
                    if (hasForceAshSave) {
                        console.log(`灰成分字段 ${field} 处理后的值:`, v[field]);
                    }
                }
            });
            
            // 移除强制保存标记，避免发送到服务器
            if (hasForceAshSave) {
                delete v._forceAshSave;
            }
            
            // 处理煤岩含量数据的格式
            if (v.rockList && Array.isArray(v.rockList) && v.rockList.length > 0) {
                // 提取密度区间名称和对应的比例值
                const rangeNames = v.rockList.map(item => item.rangeName);
                const proportions = v.rockList.map(item => item.proportion);
                
                // 设置JSON格式的密度区间数据
                v.rangeContent = JSON.stringify(rangeNames);
                v.proportionContent = JSON.stringify(proportions);
                
                // 转换为coalRockContent格式
                v.coalRockContent = JSON.stringify(v.rockList);
            }
        })
    }

    /**
     * 更新
     * @param data
     */
    update(data) {
        try {
            // 确保数据结构正确
            if (!data || !data.data || !Array.isArray(data.data) || data.data.length === 0) {
                console.error('提交的数据格式不正确:', data);
                throw new Error('提交数据格式错误');
            }
            
            // 记录原始数据
            console.log('更新前的原始数据:', JSON.stringify(data));
            
            // 直接将数据发送到服务器，避免额外处理
            return super.request({
                url: `/cwe/a/coalDatabase/data/update`,
                method: 'post',
                data: {
                    ...data,
                    ContentType: 'application/json'
                }
            }).then(response => {
                console.log('更新成功响应:', response);
                return response;
            }).catch(error => {
                console.error('更新失败:', error);
                throw error;
            });
            
        } catch (error) {
            console.error('处理更新请求时出错:', error);
            throw error;
        }
    }


    /**
     * 合并外部
     */
    mergeData(data) {
        return super.request({
            url: `/cwe/a/coalDatabase/data/merge`,
            method: 'post',
            data: {
                ...data,
                ContentType: 'application/json'
            }
        })
    }


    /**
     * 批量改折水、路耗
     */
    batchUpdateMt(data) {
        return super.request({
            url: `/cwe/a/coalDatabase/data/reduce-mt-transport-cost/batch-update`,
            method: 'post',
            data: {
                ...data,
                ContentType: 'application/json'
            }
        })
    }


    sortTop({ id, sort, coalSource }) {
        console.log('OutSideStock sortTop 参数:', { id, sort, coalSource });
        
        // 识别数据来源，使用不同的API
        let url;
        if (coalSource === 'zsmy' || coalSource === 'zsmy') {
            // 掌上煤焦数据使用特殊的API
            url = `/cwe/a/coalDatabase/data/${sort ? 'un-sort-top' : 'sort-top'}`;
        } else {
            // 修复多余的斜杠问题
            url = `/cwe/a/coalDatabase/data/${sort ? 'un-sort-top' : 'sort-top'}`;
        }
        
        return super.request({
            url,
            method: 'get',
            params: { id,  coalSource }
        }).then(res => {
            console.log('置顶请求响应:', res);
            return res;
        }).catch(err => {
            console.error('置顶请求失败:', err);
            return false;
        });
    }

    async remove({ id, coalSource, useConfirm = false, type }) {
        const fetchFn = () => {
            // 根据数据来源选择不同的删除API
            let url = `/cwe/a/coalDatabase/data/delete`;
            
            // 如果是合并数据，则使用取消合并的API
            if (type === MERGE_TYPE.MERGE) {
                url = `/cwe/a/coalDatabase/data/un-merge`;
            } 
            // 如果是掌上煤焦数据，则使用掌上煤焦专用的删除API
            else if (coalSource === 'zsmy' || type === 'zsmy') {
                url = `/cwe/a/coalDatabase/data/delete`;
            }
            
            return super.request({
                url: url,
                method: 'get',
                params: { id, type: type || coalSource }
            });
        };

        if (useConfirm) {
            try {
                const ctx = type === MERGE_TYPE.MERGE ? '是否取消合并?' : '是否确认删除当前数据?'
                const res = await super.modal.confirm(ctx)
                return fetchFn()
            } catch (error) {
                return Promise.reject(error)
            }
        } else {
            return fetchFn()
        }
    }

    /**
     * 刷新煤质指标
     * @param {Object} data - 包含id和coalSource的对象
     */
    refreshCoalQuality(data) {
        return super.request({
            url: `/cwe/a/stock/refreshIndicators`,
            method: 'post',
            data: {
                ...data,
                // ContentType: 'application/json'
            }
        })
    }

    /**
     * 获取煤源详情
     * @param id 煤炭ID
     * @param type 煤炭类型 (zsmy:掌上煤焦, wb:外部煤源, nb:内部煤源)
     * @returns {Promise<*>}
     */
    getZsmyCoalById(id, type = 'zsmy') {
        const params = { 
            id, 
            type: type,
            // 根据类型设置不同的coalSource
            coalSource: type === 'zsmy' ? 'zy' : (type === 'wb' ? 'wai' : (type === 'nb' ? 'nb' : ''))
        };
        
        console.log('获取煤源详情请求参数:', params);
        
        return super.request({
            url: `/cwe/a/coalSourceOutside/getDetailWithTypeData`,
            method: 'get',
            params: params
        }).then(response => {
            // 确保灰成分数据正确处理
            if (response && response.data) {
                // 转换灰成分相关数字字段
                const ashFields = ['siO2', 'al2O3', 'fe2O3', 'caO', 'mgO', 'na2O', 'k2O', 'tiO2', 'p2O5', 'so3', 'mnO2'];
                
                ashFields.forEach(field => {
                    if (response.data[field] !== undefined && response.data[field] !== null) {
                        const numValue = parseFloat(response.data[field]);
                        if (!isNaN(numValue)) {
                            response.data[field] = numValue;
                        }
                    }
                });
                
                // 确保CRI和CSR字段有值
                if (response.data.cri === null || response.data.cri === undefined || response.data.cri === '') {
                    // 尝试从其他可能的字段获取值
                    if (response.data.qualCri !== null && response.data.qualCri !== undefined && response.data.qualCri !== '') {
                        response.data.cri = response.data.qualCri;
                    }
                }
                
                if (response.data.csr === null || response.data.csr === undefined || response.data.csr === '') {
                    // 尝试从其他可能的字段获取值
                    if (response.data.qualCsr !== null && response.data.qualCsr !== undefined && response.data.qualCsr !== '') {
                        response.data.csr = response.data.qualCsr;
                    }
                }
                
                // 确保 basicinformation 存在
                if (!response.data.basicinformation || !response.data.basicinformation.length) {
                    response.data.basicinformation = [{
                        coalName: response.data.coalName,
                        coalCategoryName: response.data.coalCategoryName,
                        location: response.data.location,
                        factoryPrice: response.data.factoryPrice,
                        activePriceTransportPriceNoTax: response.data.activePriceTransportPriceNoTax,
                        coalBlendingCost: response.data.coalBlendingCost,
                        type: type
                    }];
                }
                
                // 确保 indexlist 存在
                if (!response.data.indexlist || !response.data.indexlist.length) {
                    response.data.indexlist = [{
                        ad: response.data.ad,
                        std: response.data.std,
                        vdaf: response.data.vdaf,
                        g: response.data.g,
                        y: response.data.y,
                        x: response.data.x,
                        mt: response.data.mt,
                        macR0: response.data.macR0,
                        macS: response.data.macS,
                        cri: response.data.cri,
                        csr: response.data.csr
                    }];
                }
                
                console.log('煤源详情数据处理完成:', {
                    id: response.data.id,
                    coalName: response.data.coalName,
                    cri: response.data.cri,
                    csr: response.data.csr,
                    ashData: ashFields.reduce((acc, field) => {
                        acc[field] = response.data[field];
                        return acc;
                    }, {})
                });
            }
            return response;
        });
    }

}

export default new Model()
