<template>
  <SnTabs v-model="tabActive" :tabs="tabs" @tab-click="handleTabClick">
    <component :is="tabActive" ref="currentTab" @refresh-my-coal="refreshMyCoal" @refresh-my-coal-data="refreshMyCoalData" />
  </SnTabs>
</template>

<script>
import AllSideStock from "./components/AllSideStock";

import InSideStock from "./components/InSideStock";
import OutSideStock from "./components/OutSideStock";
// import OtherSideStock from './components/OtherSideStock'
import OtherSideStockHistory from "./components/OtherSideStockHistory";

import SnTabs from "@/components/Common/SnTabs/index.vue";

export default {
  name: "Stock",
  components: {
    SnTabs,
    AllSideStock,
    InSideStock,
    OutSideStock,
    // OtherSideStock,
    OtherSideStockHistory,
  },
  data() {
    return {
      tabs: [
        {
          label: "我的煤源",
          value: "OutSideStock",
        },

        {
          label: "掌上煤焦煤源",
          value: "OtherSideStockHistory",
        },

        // {
        //   label: "内部煤源",
        //   value: "InSideStock",
        // },
        // {
        //   label: "全部",
        //   value: "AllSideStock",
        // },
        // {
        //     label: '掌上煤焦煤源',
        //     value: 'OtherSideStock'
        // },
      ],
      tabActive: "",
      needRefreshOutSideStock: false,
      previousTabActive: "", // 记录前一个活跃标签
    };
  },
  methods: {
    // 处理标签点击事件
    handleTabClick(tab) {
      console.log('Tab clicked:', tab.label, 'Previous tab:', this.previousTabActive, 'Current tab:', this.tabActive);
      
      // 记录前一个活跃标签
      this.previousTabActive = this.tabActive;
      
      // 在下一个事件循环中刷新组件
      this.$nextTick(() => {
        // 确保组件已完全加载
        if (this.$refs.currentTab && typeof this.$refs.currentTab.getList === 'function') {
          console.log('Refreshing data for tab:', this.tabActive);
          // 强制刷新数据
          this.$refs.currentTab.getList(true);
        }
      });
    },
    
    refreshMyCoal() {
      // 切换到"我的煤源"标签
      this.tabActive = "OutSideStock";
      
      // 使用 $nextTick 确保 DOM 已更新并组件已挂载
      this.$nextTick(() => {
        // 获取当前活动组件的引用
        const outSideStockComponent = this.$refs.currentTab;
        
        // 确保组件存在并具有 getList 方法
        if (outSideStockComponent && typeof outSideStockComponent.getList === 'function') {
          // 使用 true 参数调用 getList 来完全刷新数据
          outSideStockComponent.getList(true);
        }
      });
    },
    
    refreshMyCoalData() {
      // 创建一个本地变量来跟踪需要刷新的组件
      this.needRefreshOutSideStock = true;
      
      // 监听标签页变化
      this.$watch('tabActive', (newVal) => {
        // 如果切换到了"我的煤源"标签且需要刷新
        if (newVal === 'OutSideStock' && this.needRefreshOutSideStock) {
          // 重置标志
          this.needRefreshOutSideStock = false;
          
          // 刷新数据
          this.$nextTick(() => {
            if (this.$refs.currentTab && typeof this.$refs.currentTab.getList === 'function') {
              this.$refs.currentTab.getList(true);
            }
          });
        }
      }, { immediate: false });
    }
  },
  created() {
    this.tabActive = this.tabs[0].value;
    this.previousTabActive = this.tabActive;
  },
};
</script>

<style lang="scss" scoped></style>
