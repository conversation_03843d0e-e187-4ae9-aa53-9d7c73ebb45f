<template>
    <component :is="currentRole"></component>
</template>

<script>
import {mapGetters} from 'vuex'
import comDashboard from './com/index'

export default {
    name: 'coalBlendingV',
    components: {comDashboard},
    data() {
        return {
            currentRole: 'comDashboard'
        }
    },
    computed: {
        ...mapGetters(['roles', 'perms'])
    },
    created() {
        this.currentRole = comDashboard
        // this.currentRole = this.perms['dashboard:view'] ? comDashboard : ''
    }
}
</script>
