<template>
  <div class="app-container">
    <panel-bar type="panel" title="数据看板">
      <el-date-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate" placeholder="选择日期"
                      :clearable="false" />
    </panel-bar>
    <card height="inherit" style="height:43vh">
      <card-item title="近七日产量">
        <div class="item-echarts">
          <echarts width="inherit" height="inherit" v-if="isChart_1" :option="chart_1" />
        </div>
      </card-item>
      <card-item title="配煤日用量">
        <div class="item-echarts">
          <echarts width="inherit" height="inherit" v-if="isChart_2" :option="chart_2" />
        </div>
      </card-item>
    </card>
    <card style="height: 40.5rem" height="inherit">
      <card-item title="品名数据">
        <section class="reports">
          <div class="reports-item">
            <div class="reports-item-section">
              <div class="reports-item-img" style="background:#FF726B" @click="handleToPage('coalProductionList')">
                <img src="@/assets/console/production.png" />
              </div>
              <div class="reports-item-desc" @click="handleToPage('coalProductionList')">
                <span>配煤生产录入</span>
                <span>COAL BLENDING</span>
              </div>
            </div>
          </div>
        </section>
      </card-item>
    </card>
  </div>
</template>

<script>
import { productionUsedData, get7DayOutput } from '@/api/console'
import { PanelBar, Card, CardItem } from '@/components/Console'
const option = {
  animation: false,
  color: ['#33CAD9'],
  tooltip: {
    trigger: 'axis',
    point: [-30, -30],
    backgroundColor: '#EDEEFF',
    padding: [5, 20, 5, 20],
    textStyle: {
      color: '#9f9ea5',
    },
  },
  grid: {
    top: '25px',
    left: '50px',
    right: '20px',
    bottom: '50px',
  },
  legend: {
    right: 20,
    // itemGap: 20,
    // padding: [10, 0, 10, 10],
    align: 'left',
    data: [],
  },
  xAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF', // 坐标轴线的颜色修改--文字也同步修改
        type: 'dashed',
      },
    },
    axisTick: {
      // 坐标轴刻度相关设置
      alignWithLabel: true, // 可以保证刻度线和标签对齐
    },
    axisLabel: {
      color: '#9f9ea5', // 刻度标签文字的颜色
    },
    data: [],
  },
  yAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF',
        type: 'solid',
      },
    },
    axisLabel: {
      // 坐标轴刻度标签的相关设置
      color: '#9f9ea5',
    },
    splitLine: {
      // 坐标轴在 grid 区域中的分隔线。
      show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示。
      lineStyle: {
        color: '#E3F0FF', // 分隔线颜色，可以设置成单个颜色
      },
    },
  },
  series: [
    {
      name: '',
      type: 'bar',
      barWidth: 16,
      itemStyle: {
        normal: {
          barBorderRadius: [30, 30, 0, 0], // 配置样式
          color: () => '#2f79e8',
        },
      },
      data: [],
    },
  ],
}
Object.freeze(option)
export default {
  name: 'coalProductionConsole',
  components: { PanelBar, Card, CardItem },
  data() {
    return {
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      chart_1: {},
      chart_2: {},
    }
  },
  created() {
    this.getData(this.currentDate)
  },
  computed: {
    isChart_1() {
      if (Object.keys(this.chart_1).length) return true
      return false
    },
    isChart_2() {
      if (Object.keys(this.chart_2).length) return true
      return false
    },
  },
  watch: {
    /**
     * 改变日期重新请求数据
     */
    currentDate(v) {
      this.getData(v)
    },
  },
  methods: {
    async getData(date) {
      try {
        const beginDate = this.getWeeks({ type: 'sub', days: 6, date })
        const res = await get7DayOutput(beginDate, date)
        const { data } = await productionUsedData(date)
        res.data.sort((a, b) => b.value - a.value)
        data.sort((a, b) => b.value - a.value)
        this.chart_1 = this.formatData(res.data, 1)
        this.chart_2 = this.formatData(data, 0)
      } catch (error) {}
    },

    formatData(r1, flag) {
      const options = JSON.parse(JSON.stringify(option))
      let dateList = []
      let valueList = []
      if (flag) {
        r1.forEach((item) => {
          dateList.push(item.date)
          valueList.push(item.value)
        })
      } else {
        r1.forEach((item) => {
          dateList.push(item.name)
          valueList.push(item.value)
        })
      }
      options.series[0].data = valueList
      options.xAxis.data = dateList
      return options
    },

    handleToPage(pageName) {
      this.$store.dispatch('changeChildRoute', { parent: pageName })
      this.$router.push({ name: pageName, params: { isOpenAdd: true } })
    },
    getWeeks({ type = '', date = new Date(), days = 6, fmt = 'yyyy-MM-dd' }) {
      const day = 24 * 60 * 60 * 1000
      return type === 'add'
        ? new Date(new Date(date).getTime() + day * days).Format(fmt)
        : new Date(new Date(date).getTime() - day * days).Format(fmt)
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .card-item {
  padding: 5px;
}
::v-deep .item-echarts {
  width: 98.5%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  height: inherit;
}
.reports {
  width: inherit;
  height: inherit;
  display: flex;

  &-item {
    width: inherit;
    height: inherit;
    display: flex;
    flex-flow: column;
    justify-content: space-evenly;
    position: relative;

    &-section {
      display: flex;
      justify-content: center;
      opacity: 0.9;
      transition: all 0.5s;

      .reports-item-img {
        position: relative;
        width: 6rem;
        height: 6rem;
        border-radius: 50%;
        margin-right: 2rem;
        cursor: pointer;

        img {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 50%;
          height: 50%;
        }
      }

      .reports-item-desc {
        display: flex;
        justify-content: center;
        flex-flow: column nowrap;
        cursor: pointer;
        width: 200px;

        span:first-of-type {
          font-size: 2rem;
          color: #424242;
          margin-bottom: 10px;
        }

        span:last-of-type {
          font-size: 1.5rem;
          color: #a8a8a8;
        }
      }
    }

    &-section:hover {
      opacity: 1;
    }
  }

  &-item:first-of-type:after {
    content: '';
    position: absolute;
    opacity: 1;
    right: 0;
    height: 50px;
    width: 2px;
    background: #efefef;
  }
}
</style>
