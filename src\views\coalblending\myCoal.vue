<template>
  <div class="container">
    <coal ref="coal" name="coal" :height="height"></coal>
    <el-dialog :visible.sync="reportVisible" :close-on-press-escape="false" :close-on-click-modal="false" fullscreen>
      <pdf-view :name="dialogNmae" :content="content" @closeDialog="closePdfDialog" :exportPdf="exportPdf"
                :entityForm="entityForm"></pdf-view>
    </el-dialog>
  </div>
</template>
<script>
import { page } from '@/api/evaluationReport'
import Func from '@/utils/func'
import { getReviewReport, deleteReport } from '@/api/coal'
import PdfView from '@/components/PdfView'

let listQuery = {
  keyword: '',
  beginDate: '',
  endDate: '',
}
// 实体模型
const entityForm = {
  id: '',
  name: '',
  type: 'JM', // 默认焦煤
  province: '',
  factoryPrice: '',
  transitFee: '',
  roadCost: '',
  arrivePrice: '',
  arriveFactory: '',
  mineDepth: '',
  rawMt: '',
  rawAd: '',
  rawPointFive: '',
  rawOnePointFour: '',
  rawAdIn: '',
  rawStd: '',
  rawVdaf: '',
  rawG: '',
  cleanVdaf: '',
  cleanAd: '',
  cleanStd: '',
  cleanMt: '',
  cleanP: '',
  procG: '',
  procY: '',
  procX: '',
  procMf: '',
  procTp: '',
  procTmax: '',
  procTk: '',
  procCrc: '',
  procA: '',
  procB: '',
  macR0: '',
  macS: '',
  macV: '',
  macI: '',
  macE: '',
  comSiO2: '',
  comAl2O3: '',
  comFe2O3: '',
  comCaO: '',
  comMgO: '',
  comNa2O: '',
  comK2O: '',
  comTiO2: '',
  comP2O5: '',
  comSO3: '',
  cfeCp: '',
  cfeCe: '',
  qualScon: '',
  qualPcon: '',
  qualM40: '',
  qualM10: '',
  qualCsr: '',
  qualCri: '',
  qualTestCond: '',
  crGk: '',
  crBk: '',
  crTk: '',
  city: '',
  dataBelong: '',
  dataType: '',
  createBy: '',
  createDate: '',
  updateBy: '',
  updateDate: '',
  remarks: '',
  ext: '',
  location: '',
  attachment: '',
  attachmentName: '',
}
export default {
  name: 'myCoal',
  data() {
    return {
      isEmpty: true,
      listQuery: { ...listQuery },
      listFunc: page,
      height: 0,
      superTableConfig: {
        columns: [
          {
            type: 'index',
            isShow: true,
          },
          {
            prop: 'name',
            slot: 'name',
            label: '名称',
            isShow: true,
            minWidth: 80,
          },
          {
            prop: 'coalName',
            label: '类别',
            minWidth: 60,
            isShow: true,
          },
          {
            prop: 'cleanVdaf',
            label: 'Vdaf',
            minWidth: 40,
            isShow: true,
          },
          {
            prop: 'cleanAd',
            label: 'Ad',
            minWidth: 40,
            isShow: true,
          },
          {
            prop: 'cleanStd',
            label: 'St,d',
            minWidth: 40,
            isShow: true,
          },
          {
            prop: 'procG',
            label: 'G',
            minWidth: 40,
            isShow: true,
          },
          {
            prop: 'procY',
            minWidth: 40,
            label: 'Ymm',
            isShow: true,
          },
          {
            prop: 'createDate',
            isShow: true,
            width: 150,
            label: '创建时间',
          },
          {
            prop: 'opt',
            slot: 'opt',
            isShow: true,
            label: '操作',
          },
        ],
        listQuery: {
          orderBy: 'createDate',
          orderDir: 'desc',
          ...listQuery,
        },
      },
      selectedList: [],
      customFilters: [],
      headerName: '我的评价报告',
      // 编辑配置
      entityForm: Object.assign({}, entityForm),
      dialogNmae: '',
      content: '',
      reportVisible: false,
      form: {},
      exportPdf: true,
      isEmptyList: false,
    }
  },
  components: {
    PdfView,
  },
  created() {
    if (this.$route.query.count > 0) {
      // this.getList()
      this.isEmpty = false
    } else {
      this.isEmpty = true
    }
    let tableHeight = document.documentElement.clientHeight
    this.height = tableHeight - 215
  },
  methods: {
    handleAdd() {
      this.$router.push({ name: 'myDetails', params: { title: '新增', current: 'CoalDetails' } })
    },

    handleIsEmptyList(status) {
      this.isEmptyList = status
    },
    resetSearch() {
      this.listQuery = { ...listQuery }
      this.$refs['evaluationReport'].resetSearch()
      this.getList()
    },

    async handleView(form) {
      this.entityForm = { ...form }
      this.form = form
      this.reportVisible = true
      this.dialogNmae = form.name
      const res = await Func.fetch(getReviewReport, {
        id: form.id,
      })
      if (res.data) {
        this.reportVisible = true
        this.content = res.data.content
      }
    },
    handleReset(id) {
      this.$router.push('/system/produceReport?id=' + id)
    },
    handleDelete(id) {
      this.$confirm('是否删除该报告？', '确认', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
      }).then(async () => {
        const res = await Func.fetch(deleteReport, id)
        if (res) {
          this.$message({
            type: 'success',
            message: '该报告已删除',
          })
          this.getList()
        }
      })
    },
    // getList() {
    //     this.$refs['coal'].$emit('refresh')
    // },
    closePdfDialog() {
      this.reportVisible = false
      this.entityForm = {}
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep .is-always-shadow {
  box-shadow: none;
}

::v-deep .el-card {
  background: #ececec;
  border: none;
}

::v-deep .el-card__body {
  background: #fff;
  padding: 15px;
  margin: 0px 0 10px;
}

::v-deep .superTable-container {
  padding: 0 10px;
  background: #ececec;
}

::v-deep .superTable-content {
  background: #fff;
  border-radius: 5px;
}

::v-deep .filter-container {
  padding: 15px 10px;

  border-radius: 5px;
}

.add {
  border: none;
  border-radius: 5px;
  background: #f8a20f;
  width: 100px;
  height: 35px;
  color: #fff;
  font-size: 14px;
}

.container {
  width: 100%;
  background-color: #efefef;
}

::v-deep .filter-button-wrap {
  margin-left: auto;
}

::v-deep .filter-container {
  background: none;
  // padding: 20px 0;
  padding: 0;
}

::v-deep .superTableContent {
  background: none;
  padding: 0px 10px 0;
}

::v-deep .superTable-container {
  background: #fff;
  border-radius: 5px;
  padding: 15px;
}

.filter_container {
  padding-bottom: 10px;

  .filter {
    padding: 10px 20px;
    border-radius: 5px;
    width: 100%;
    display: flex;
    background: #fff;
    align-items: center;
    height: 60px;
    line-height: 60px;
    position: relative;
    // box-shadow: 0 0px 1px rgba(0, 0, 0, 0.1), 0 0 6px rgba(0, 0, 0, 0.01);

    .filter_item {
      margin-right: 10px;

      span {
        font-size: 14px;
        margin-right: 8px;
      }
    }

    .filter_buttons {
      line-height: 30px;
      position: absolute;
      right: 25px;

      .button {
        margin-right: 5px;
      }
    }
  }
}

.under-line {
  cursor: pointer;
  color: #33cad9;
  margin-bottom: 3px;
  text-decoration: underline;
}

.empty_container {
  width: 100%;
  padding: 0 10px;
  height: calc(100vh - 65px);

  .wrap {
    background: #fff;
    border-radius: 5px;
    padding: 50px 15%;
    height: inherit;
    display: flex;
    flex-flow: column;
    /*justify-content: center;*/
    align-items: center;

    .empty_data {
      width: 100%;

      .img {
        display: flex;
        justify-content: center;
        transform: scale(0.7);

        img {
          width: 259px;
          height: 232px;
        }
      }

      .title {
        display: flex;
        justify-content: center;
        font-size: 14px;
        color: #979595;

        span {
          display: flex;
          padding: 20px;
        }
      }
    }
  }

  // background-color: #efefef;
}

.btn {
  color: #909399;
  background: none;
  border: none;
  cursor: pointer;
  outline: none;
}

.dialog-content {
  margin: 5px 10px;
}

.m-b-10 {
  margin: 20px 0;
}

.saveBtn {
  display: flex;
  padding-top: 10px;
  justify-content: flex-end;
}

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.pdfbtn {
  width: 150px;
  height: 50px;
  font-size: 14px;
}

.pdfbtn1 {
  width: 150px;
  height: 40px;
  font-size: 14px;
  padding: 10px;
}

.btnContent {
  position: relative;
  display: flex;
  top: 0;
  justify-content: center;
}

.header {
  padding: 20px 64px 0 64px;
  width: 1024px;
  position: absolute;
  background: #ffffff;
  top: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s;
}

.pdfContent {
  margin-top: 20px;
  box-shadow: 2px 2px 10px #fff, 4px 4px 15px #ccc, 5px 5px 20px #ccc;
  background: #ffffff;
}

.bottom {
  margin-top: 20px;
  border-radius: 5px;
  border: none;

  .tips {
    border: 1px solid #fdcd7c;
    background: #fff4e1;
    padding: 15px;
    margin-top: 20px;
    border-radius: 5px;
    line-height: 20px;
    font-size: 14px;

    .active {
      color: #f8a20f;
      font-size: 16px;
    }
  }
}
</style>
