<template>
    <div class="manyScheme">
        <super-table ref="matchCoal" name="matchCoal" title="煤岩配煤" mountQuery :height="height" :listFunc="listFunc"
                     :defaultConfig="superTableConfig" noOrder>
            <el-table-column label="名称" slot="name" min-width="100px" prop="name">
                <template slot-scope="scope">
                    <div class="under-line" @click="handleView(scope.row)">{{ scope.row['name'] }}</div>
                </template>
            </el-table-column>
            <el-table-column label="操作" slot="opt" prop="opt" align="center" width="80">
                <template slot-scope="scope">
                    <div style="display: flex;margin-top: 5px;">
                        <el-button type="delete" @click="handleDelete(scope.row.id)">删除</el-button>
                        <!--                        <el-button type="sendEmail" @click="handleSend(scope.row.id)">发送邮件</el-button>-->
                        <!--                        <el-button type="btnSave" @click="handleCoal(scope.row.id)">煤质保存</el-button>-->
                    </div>
                </template>
            </el-table-column>
        </super-table>
        <el-dialog fullscreen :visible.sync="dialoglistVisible" :close-on-press-escape="false"
                   :close-on-click-modal="false"
                   :before-close="handleClose" append-to-body title="洗煤方案详情">
            <div v-loading="isLoading">
                <div class="blendingIndex">
                    <p style="display: flex;justify-content: space-between">
                        <span>1.配煤指标</span>
                        <span>
<!--              <el-button type="danger" :loading="redoQualityLoading" @click="handleSend(currentId)">发送邮件</el-button>-->
              <el-button type="success" @click="redoOptimization">重新优化</el-button>
              <el-button type="success" class="mr10" @click="saveOptimization">保存方案</el-button>
            </span>
                    </p>
                    <div class="table">
                        <div class="table-clumn">Vdaf</div>
                        <div class="table-clumn">Ad</div>
                        <div class="table-clumn">St,d</div>
                        <div class="table-clumn">G</div>
                        <div class="table-clumn">Y</div>
                        <div class="table-clumn">标准差S</div>
                    </div>
                    <div class="table">
                        <div class="table-clumn">
                            {{ qualityData.cInVdafL }}≤{{ qualityData.cInVdaf }}≤{{ qualityData.cInVdafH }}
                        </div>
                        <div class="table-clumn">
                            {{ qualityData.cInAdL }}≤{{ qualityData.cInAd }}≤{{ qualityData.cInAdH }}
                        </div>
                        <div class="table-clumn">{{ qualityData.cInStdL }}≤
                            {{ qualityData.cInStd }}≤{{ qualityData.cInStdH }}
                        </div>
                        <div class="table-clumn">
                            <span v-if="qualityData.cInGL"> ≥{{ qualityData.cInGL }}</span>
                        </div>
                        <div class="table-clumn">
                            <span v-if="qualityData.cInYL"> ≥{{ qualityData.cInYL }}</span>
                        </div>
                        <div class="table-clumn">
                            <span v-if="qualityData.cInSH"> ≥{{ qualityData.cInSH }}</span>
                        </div>
                    </div>
                </div>
                <div class="blendingIndex">
                    <p>2.煤种及配比</p>
                    <div class="m5">
                        <el-table :data="qualityData.coalWashingProjectCoalList" border>
                            <el-table-column fixed width="44">
                                <template slot-scope="scope">
                                    <div class="tc">{{ scope.$index + 1 }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column fixed prop="name" label="名称">
                            </el-table-column>
                            <el-table-column fixed prop="type" label="煤种" min-width="80"></el-table-column>
                            <el-table-column label="配比(%)" min-width="80" prop="percent2">
                            </el-table-column>
                            <el-table-column label="Ad(%)" prop="cleanAd" min-width="65">
                            </el-table-column>
                            <el-table-column label="Vdaf(%)" prop="cleanVdaf" min-width="65">
                            </el-table-column>
                            <el-table-column label="St,d(%)" min-width="65" prop="cleanStd">
                            </el-table-column>
                            <el-table-column label="G" min-width="65" prop="procG">
                            </el-table-column>
                            <el-table-column label="X(mm)" prop="procX" min-width="65">
                            </el-table-column>
                            <el-table-column label="Y(mm)" prop="procY" min-width="65">
                            </el-table-column>
                            <el-table-column label="R0(%)" prop="macR0" min-width="65">
                            </el-table-column>
                            <el-table-column label="到厂价（元/吨）" prop="arrivePrice" min-width="100">
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
                <div class="blendingIndex">
                    <p>3.洗煤计算结果</p>
                    <el-table :data="coalRockResultList" border>
                        <el-table-column width="80">
                            <template slot-scope="scope">
                                {{
                                    scope.$index === 0 ? '理论' : scope.$index === 1 ? '实际' : scope.$index === 2 ? '误差' : '允许误差'
                                }}
                            </template>
                        </el-table-column>
                        <el-table-column label="Ad(%)" prop="inAd">
                            <template slot-scope="scope">
                                <el-input v-if='scope.$index===0' v-model="qualityData.inAd" disabled></el-input>
                                <el-input v-if='scope.$index===1' v-model="qualityData.factInAd" type="number" min="0"
                                          @input="handleChangeInput('inAd','factInAd','diffInAd')"></el-input>
                                <el-input v-if='scope.$index===2' v-model="qualityData.diffInAd" disabled></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="Vdaf(%)" prop="inVdaf">
                            <template slot-scope="scope">
                                <el-input v-if='scope.$index===0' v-model="qualityData.inVdaf" disabled></el-input>
                                <el-input v-if='scope.$index===1'
                                          @input="handleChangeInput('inVdaf','factInVdaf','diffInVdaf')"
                                          v-model="qualityData.factInVdaf" type="number" min="0"></el-input>
                                <el-input v-if='scope.$index===2' v-model="qualityData.diffInVdaf" disabled></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="St,d(%)" prop="inStd">
                            <template slot-scope="scope">
                                <el-input v-if='scope.$index===0' v-model="qualityData.inStd" disabled></el-input>
                                <el-input @input="handleChangeInput('inStd','factInStd','diffInStd')"
                                          v-if='scope.$index===1'
                                          v-model="qualityData.factInStd" type="number" min="0"></el-input>
                                <el-input v-if='scope.$index===2' v-model="qualityData.diffInStd" disabled></el-input>
                            </template>
                        </el-table-column>

                        <el-table-column label="G" prop="inG">
                            <template slot-scope="scope">
                                <el-input v-if='scope.$index===0' v-model="qualityData.inG" disabled></el-input>
                                <el-input @input="handleChangeInput('inG','factInG','diffInG')" v-if='scope.$index===1'
                                          v-model="qualityData.factInG" type="number" min="0"></el-input>
                                <el-input v-if='scope.$index===2' v-model="qualityData.diffInG" disabled></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="Y(mm)" prop="inY">
                            <template slot-scope="scope">
                                <el-input v-if='scope.$index===0' v-model="qualityData.inY" disabled></el-input>
                                <el-input @input="handleChangeInput('inY','factInY','diffInY')" v-if='scope.$index===1'
                                          v-model="qualityData.factInY" type="number" min="0"></el-input>
                                <el-input v-if='scope.$index===2' v-model="qualityData.diffInY" disabled></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="X(mm)" prop="inX">
                            <template slot-scope="scope">
                                <el-input v-if='scope.$index===0' v-model="qualityData.inX" disabled></el-input>
                                <el-input v-if='scope.$index===1' v-model="qualityData.factInX" type="number" min="0"
                                          @input="handleChangeInput('inX','factInX','diffInX')"></el-input>
                                <el-input v-if='scope.$index===2' v-model="qualityData.diffInX" disabled></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="R0(%)" prop="theoryMacR0">
                            <template slot-scope="scope">
                                <el-input v-if='scope.$index===0' v-model="qualityData.theoryMacR0" disabled></el-input>
                                <el-input v-if='scope.$index===1' v-model="qualityData.actualMacR0" type="number"
                                          min="0"
                                          @input="handleChangeInput('theoryMacR0','actualMacR0','difMacR0')"></el-input>
                                <el-input v-if='scope.$index===2' v-model="qualityData.difMacR0" disabled></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="S0" prop="theoryMacS">
                            <template slot-scope="scope">
                                <el-input v-if='scope.$index===0' v-model="qualityData.theoryMacS" disabled></el-input>
                                <el-input v-if='scope.$index===1' v-model="qualityData.actualMacS"
                                          @input="handleChangeInput('theoryMacS','actualMacS','difMacS')" type="number"
                                          min="0"></el-input>
                                <el-input v-if='scope.$index===2' v-model="qualityData.difMacS" disabled></el-input>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="blendingIndex">
                    <p>4.备注及审批</p>
                    <div style="display: flex;border: 1px solid #ebeef5;height: 60px;">
                        <div style="width: 80px;border-right: 1px solid #ebeef5;display: flex;align-items: center;padding-left: 10px">
                            备注
                        </div>
                        <div style="flex: 1;padding: 5px;">
                            <el-input type="textarea" style="height: 100%" v-model="qualityData.remarks"></el-input>
                        </div>
                    </div>
                </div>
                <div class="blendingIndex">
                    <p>5.图形显示</p>
                    <column-chart v-if="dialoglistVisible" :impChartData="impChartData"></column-chart>
                    <el-table :data="qualityData.coalTypeProportionList" :show-header="false" border>
                        <el-table-column min-width="60" prop="name">
                        </el-table-column>
                        <el-table-column min-width="60" prop="brownCoal">
                        </el-table-column>
                        <el-table-column min-width="60" prop="longFlame">
                        </el-table-column>
                        <el-table-column min-width="60" prop="gasCoal">
                        </el-table-column>
                        <el-table-column min-width="60" prop="thirdCokingCoal">
                        </el-table-column>
                        <el-table-column min-width="60" prop="fatCoal">
                        </el-table-column>
                        <el-table-column min-width="60" prop="cokingCoal">
                        </el-table-column>
                        <el-table-column min-width="60" prop="leanCoal">
                        </el-table-column>
                        <el-table-column min-width="60" prop="meagerLeanCoal">
                        </el-table-column>
                        <el-table-column min-width="60" prop="meagerCoal">
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </el-dialog>
        <el-dialog :visible.sync="coalVisible" :close-on-press-escape="false" :close-on-click-modal="false"
                   :before-close="handleClose" append-to-body width="400px" title="保存">
            <el-form ref="coalForm" :model="coalForm" label-width="80px">
                <el-row>
                    <el-form-item label="名称" prop="name" :rules="[{required:true, message:'请输入名称'}]">
                        <el-input v-model="coalForm.name"></el-input>
                    </el-form-item>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="saveCoalForm('entityForm')">保存</el-button>
            </div>
        </el-dialog>
        <ManyWash :washVisible.sync="dialogVisible" :details="details" @washDetailClose="handleWashClose"/>
    </div>
</template>
<script>
    import {
        pageList,
        saveProjectApi,
        deleteItem,
        sendEmail,
        saveCoalByCoalWashingProject
    } from '@/api/mulWash'
    import Func from '@/utils/func'
    import ManyWash from "./details/manyWash";

    export default {
        name: 'myWash',
        components: {ManyWash},
        data() {
            return {
                coalForm: {},
                currentId: '',
                dialogVisible: false,
                details: {},
                coalVisible: false,
                list: [],
                headerName: '洗煤方案',
                dialoglistVisible: false,
                isLoading: false,
                qualityData: {
                    coalWashingProjectCoalList: [],
                    coalTypeProportionList: []
                },
                redoQualityLoading: false,
                height: document.documentElement.clientHeight - 215,
                impChartData: {name: '', xData: [], yData: []},
                isEmpty: false,
                superTableConfig: {
                    columns: [
                        {
                            slot: 'name',
                            label: '名称',
                            isShow: true,
                            isFilter: true,
                            prop: 'name',
                            filter: {
                                prop: 'filter_LIKES_name',
                                label: '名称'
                            }
                        },
                        {
                            label: 'Ad',
                            prop: 'inAd',
                            isShow: true,
                            minWidth: 60
                        },
                        {
                            label: 'St,d',
                            prop: 'inStd',
                            isShow: true,
                            minWidth: 60
                        },
                        {
                            label: 'G',
                            prop: 'inG',
                            isShow: true,
                            minWidth: 60
                        },
                        {
                            label: '入炉煤价格',
                            prop: 'inPrice',
                            isShow: true,
                            minWidth: 60
                        },
                        {
                            label: '反射率',
                            prop: 'theoryMacR0',
                            isShow: true,
                            minWidth: 60
                        },
                        {
                            label: '标准差',
                            prop: 'theoryMacS',
                            isShow: true,
                            minWidth: 60
                        },

                        {
                            label: 'CSR',
                            prop: 'gyCsr',
                            isShow: true,
                            minWidth: 60
                        },
                        {
                            label: '焦炭价格',
                            isShow: true,
                            isFilter: true,
                            prop: 'jtPrice',
                            minWidth: 100,
                            filter: {
                                label: '焦炭成本',
                                start: 'filter_GEM_jtPrice',
                                end: 'filter_LEM_jtPrice',
                                component: 'NumberRanger'
                            }
                        },
                        {
                            label: '操作',
                            prop: 'opt',
                            slot: 'opt',
                            isShow: true,
                            minWidth: 60
                        }
                    ],
                    listQuery: {
                        orderBy: 'createDate',
                        orderDir: 'desc'
                    }
                },
                contextMenus: [],
                listFunc: pageList
            }
        },
        computed: {
            coalRockResultList() {
                const coalRockResultList = [{}, {}, {}]
                for (let i in this.qualityData) {
                    for (let k in coalRockResultList) {
                        coalRockResultList[k][i] = this.qualityData[i]
                    }
                }
                return coalRockResultList
            }
        },
        watch: {
            $route(to) {
                if (to.path === '/system/coalRockProject') {
                    this.getList()
                }
            }
        },
        created() {
            if (this.$route.query.count > 0) {
                // this.getList()
                this.isEmpty = false
            } else {
                this.isEmpty = true
            }

            this.doLayout()
        },
        methods: {
            handleWashClose() {
                this.dialogVisible = false
                this.getList()
            },
            handleAdd() {
                this.$router.push({path: '/autoBlending', query: {activeTitle: 'MWASH'}})
            },
            handleCoal(id) {
                this.coalForm.id = id
                this.coalVisible = true
            },
            // 保存到煤质数据库
            async saveCoalForm() {
                const valid = await this.$refs.coalForm.validate()
                if (valid) {
                    const res = await Func.fetch(saveCoalByCoalWashingProject, this.coalForm)
                    if (res) {
                        this.handleClose()
                        this.$message.success('保存到煤质数据库成功')
                    }
                }
            },
            handleSend(id) {
                this.$confirm('此操作将发送邮件, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    this.redoQualityLoading = true
                    let res = await sendEmail({id})
                    this.redoQualityLoading = false
                    if (res) {
                        this.$message({
                            type: 'success',
                            message: '邮件信息已发送'
                        })
                        this.getList()
                    }
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消发送'
                    })
                })
            },
            handleDelete(id) {
                this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    let res = await Func.fetch(deleteItem, {id})
                    if (res) {
                        this.$message({
                            type: 'success',
                            message: '该信息已删除'
                        })
                        this.getList()
                    }
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    })
                })
            },
            doLayout() {
            },
            handleChangeInput(theory, fact, diff) {
                this.qualityData[diff] = (Math.abs(this.qualityData[theory] - this.qualityData[fact])).toFixed(2)
            },
            // 编辑
            async handleView(row) {
                this.dialogVisible = true
                this.details = {...row}

                // this.$router.push({ name: 'myDetails', params: { row, title: row.name, current: 'ManyWashDetails' } })
                // const {id} = row
                // this.currentId = id
                // const res = await Func.fetch(getDetail, { id })
                // console.log(res)
                // if (res.data) {
                //   this.qualityData = { ...res.data }
                //   this.impChartData.xName = '区间'
                //   this.impChartData.xData = res.data.rangeList
                //   this.impChartData.yData = res.data.proportionList
                //   this.impChartData.type = 'bar'
                //   this.dialoglistVisible = true
                // }
            },
            /*
            * 更新数据
            * */
            getList() {
                this.$refs.matchCoal.$emit('refresh')
            },
            /*
            * 关闭方案详情对话框
            * */
            handleClose() {
                this.dialoglistVisible = false
                this.coalForm = {}
                this.coalVisible = false
            },
            // 重新优化
            redoOptimization() {
                this.dialoglistVisible = false
                this.$router.push('/system/newCoalRock?id=' + this.currentId)
            },
            async saveOptimization(data) {
                const res = await Func.fetch(saveProjectApi, {...this.qualityData})
                if (res) {
                    this.dialoglistVisible = false
                    this.getList()
                    this.$message({
                        type: 'success',
                        message: '保存成功'
                    })
                }
            }
        }
    }
</script>
<style lang="scss" scoped>
  ::v-deep .filter-button-wrap {
    margin-left: auto;
  }

  ::v-deep .is-always-shadow {
    box-shadow: none;
  }

  ::v-deep .el-card {
    background: #ececec;
    border: none;
  }

  ::v-deep .el-card__body {
    background: #fff;
    padding: 15px;
    margin: 0px 0 10px;
  }

  ::v-deep .superTable-container {
    padding: 0 10px;
    background: #ececec;
  }

  ::v-deep .superTable-content {
    background: #fff;
    border-radius: 5px;
  }

  ::v-deep .filter-container {
    padding: 15px 10px;

    border-radius: 5px;
  }

  ::v-deep .filter-container {
    background: none;
    // padding: 20px 0;
    padding: 0;
  }

  ::v-deep .superTableContent {
    background: none;
    padding: 0px 10px 0;
    background: #ececec;
  }

  ::v-deep .superTable-container {
    background: #fff;
    border-radius: 5px;
    padding: 15px;
  }

  .empty_container {
    width: 100%;
    padding: 10px;
    height: calc(100vh - 65px);

    .wrap {
      background: #fff;
      border-radius: 5px;
      padding: 50px 15%;
      height: inherit;
      display: flex;
      flex-flow: column;
      /*justify-content: center;*/
      align-items: center;

      .empty_data {
        width: 100%;

        .img {
          display: flex;
          justify-content: center;
          transform: scale(0.7);

          img {
            width: 259px;
            height: 232px;
          }
        }

        .title {
          display: flex;
          justify-content: center;
          font-size: 14px;
          color: #979595;

          span {
            display: flex;
            padding: 20px;
          }
        }

        .bottom {
          margin-top: 20px;

          .add {
            border: none;
            border-radius: 5px;
            background: #f8a20f;
            width: 100px;
            height: 35px;
            color: #fff;
            font-size: 14px;
          }

          .tips {
            border: 1px solid #fdcd7c;
            background: #fff4e1;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            line-height: 20px;
            font-size: 14px;

            .active {
              color: #f8a20f;
              font-size: 16px;
            }
          }
        }
      }
    }

    // background-color: #efefef;
  }

  .under-line {
    cursor: pointer;
    color: #33cad9;
    text-decoration: underline;
  }

  .blendingIndex {
    p {
      color: #595ec9;
      font-size: 14px;
      font-weight: bold;
    }

    .table {
      display: flex;
      color: #888;
      line-height: 30px;

      .table-clumn {
        flex: 1;
        font-size: 12px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #ebeef5;
        border-left: none;
      }

      .table-clumn2 {
        flex: 1;
        font-size: 12px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        border-top: 1px solid #ebeef5;
        border-bottom: 1px solid #ebeef5;
      }
    }
  }

  .mr10 {
    margin-right: 5px;
  }
</style>
