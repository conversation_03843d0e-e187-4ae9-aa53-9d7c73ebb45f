<template>

  <div class="dashboard-editor-container">
    <div class="title">
      <img src="@/assets/home.png">
      <p>欢迎来到首页</p>
    </div>

    <!-- <el-row style="background: #f8f8f8;padding: 20px 10px 0 0;display: flex" v-loading="loading">
      <el-col style="flex:2">
        <div class="card" @click="toDetail('checkPending:apply')"
             :style="{cursor:(perms['checkPending:apply']?'pointer':'text')}">
          <div class="card-content" style="color:#FA8D59;">
            <div class="card-icon" style="background-color:#FA8D59;display: flex;justify-content: center;align-items: center">
              <img src="../../../assets/applay.png" style="width: 50px;height: 48px;" alt="">
            </div>
            <div class="card-body">
              <span class="count">{{dspCount}}</span>
              <p class="info">待审批</p>
            </div>
          </div>
        </div>
      </el-col>
      <el-col style="flex:2">
        <div class="card" @click="toDetail('collection:view')" :style="{cursor:(perms['collection:view']?'pointer':'text')}">
          <div class="card-content" style="color:#EA89BE;">
            <div class="card-icon" style="background-color:#EA89BE; display: flex;justify-content: center;align-items: center">
              <img src="../../../assets/money.png" style="width: 50px;height: 48px;" alt="">
            </div>
            <div class="card-body">
              <span class="count">{{dskCount}}</span>
              <p class="info">待收款</p>
            </div>
          </div>
        </div>
      </el-col>
      <el-col style="flex:2">
        <div class="card" @click="toDetail('checkPending:distribution')"
             :style="{cursor:(perms['checkPending:distribution']?'pointer':'text')}">
          <div class="card-content" style="color:#4FCEAE;">
            <div class="card-icon" style="background-color:#4FCEAE;display: flex;justify-content: center;align-items: center">
              <img src="../../../assets/distribution.png" style="width: 50px;height: 48px;" alt="">
            </div>
            <div class="card-body">
              <span class="count">{{dfpCount}}</span>
              <p class="info">待分配</p>
            </div>
          </div>
        </div>
      </el-col>
      <el-col style="flex:3">
        <div class="card" :style="{cursor:(perms['testTask:view']?'pointer':'text')}">
          <div class="card-content" style="color:#F2C743;">
            <div class="card-icon" style="background-color:#F2C743;display: flex;justify-content: center;align-items: center">
              <img src="../../../assets/GRDDWSH.png" style="width: 100%;height: 100%;" alt="">
            </div>
            <div class="card-body1">
              <div @click="toDetail('testTask:view','/assay/testTask')"
                   style="display: flex;justify-content: center;border-bottom: #efefef solid 1px">
                <span class="count">{{hyrwCount}}</span>
                <p class="info">化验中(外)</p>
              </div>
              <div @click="toDetail('testTask:view','/internalAssay/testTask')" style="display: flex;justify-content: center">
                <span class="count">{{hyrwNBCount}}</span>
                <p class="info">化验中(内)</p>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col style="flex:3">
        <div class="card" :style="{cursor:(perms['checkPending:complete']?'pointer':'text')}">
          <div class="card-content" style="color:rgb(77,198,242);">
            <div class="card-icon" style="background:rgb(77,198,242);display: flex;justify-content: center;align-items: center">
              <img src="../../../assets/complete.png" style="width: 50px;height: 48px;" alt="">
            </div>
            <div class="card-body1">
              <div @click="toDetail('checkPending:complete','/assay/checkPending')"
                   style="display: flex;justify-content: center;border-bottom: #efefef solid 1px">
                <span class="count">{{dwcCount}}</span>
                <p class="info">待完成(外)</p>
              </div>
              <div @click="toDetail('checkPending:complete','/internalAssay/complete')"
                   style="display: flex;justify-content: center">
                <span class="count">{{dwcNBCount}}</span>
                <p class="info">待完成(内)</p>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col style="flex:3">
        <div class="card" :style="{cursor:(perms['checkPending:release']?'pointer':'text')}">
          <div class="card-content" style="color:rgb(255,104,110);">
            <div class="card-icon"
                 style="background-color:rgb(255,104,110);display: flex;justify-content: center;align-items: center">
              <img src="../../../assets/release.png" style="width: 50px;height: 48px;" alt="">
            </div>
            <div class="card-body1">
              <div @click="toDetail('checkPending:release','/assay/checkPending')"
                   style="display: flex;justify-content: center;border-bottom: #efefef solid 1px">
                <span class="count">{{dfbCount}}</span>
                <p class="info">待发布(外)</p>
              </div>
              <div @click="toDetail('checkPending:release','/internalAssay/release')"
                   style="display: flex;justify-content: center">
                <span class="count">{{dfbNBCount}}</span>
                <p class="info">待发布(内)</p>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <h3 class="content-title" style="display:flex;justify-content: space-between;">走势图
      <el-button @click="refresh" type="primary" icon="el-icon-refresh">同步数据</el-button>
    </h3>
    <el-row style="margin-top: 10px">
      <el-col :span="24" style="padding:5px 20px">
        <line-chart :lineData="lineData" @start="startTime" :defaultText='defaultText'></line-chart>
      </el-col>
    </el-row> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import lineChart from './lineChart'
import pieChart from './pieChart'
import { orderCount, orderPie, orderStatAll } from '@/api/home'
import Func from '@/utils/func'
import { formatDate } from '@/filters'
import Cache from '@/utils/cache'

const now = new Date()
const start = formatDate(now.getTime() - 3600 * 1000 * 24 * 3, 'yyyy-mm-dd') + ' 00:00:00'
const end = formatDate(now, 'yyyy-mm-dd') + ' 23:59:59'

// const now = new Date()
// const today = dateFormat(now, 'yyyy-mm-dd')

export default {
  name: 'dashboard-admin',
  components: { lineChart, pieChart },
  data() {
    return {
      statisticsData: {
        article_count: 1024,
        pageviews_count: 1024
      },
      dfbCount: 0,
      dfpCount: 0,
      dskCount: 0,
      dspCount: 0,
      dwcCount: 0,
      dwcNBCount: 0,
      dfbNBCount: 0,
      hyrwCount: 0,
      hyrwNBCount: 0,
      loading: false,
      isRefresh: false,
      pieData: {},
      lineData: {},
      defaultText: ''
    }
  },
  computed: {
    ...mapGetters([
      'user',
      'perms'
    ])
  },
  created() {
    // this.getOrderCount()
    // this.getLineChart()
  },
  methods: {
    toDetail(permission, url) {
      if (this.perms[permission]) {
        switch (permission) {
          case 'checkPending:apply':
            this.$router.push('/assay/checkPending')
            Cache.set('paneName', 'dsp')
            break
          case 'checkPending:distribution':
            this.$router.push('/assay/checkPending')
            Cache.set('paneName', 'ysk')
            break
          case 'collection:view':
            this.$router.push('/assay/collection')
            break
          case 'testTask:view':
            // console.log(url)
            this.$router.push(url)
            break
          case 'checkPending:complete':
            this.$router.push(url)
            Cache.set('paneName', 'hyz')
            break
          case 'checkPending:release':
            this.$router.push(url)
            Cache.set('paneName', 'hywc')
            break
        }
      } else {
        // console.log('没有权限')
      }
    },
    async startTime(e) {
      const res = await Func.fetch(orderStatAll, { dateBegin: e, dateEnd: end })
      this.lineData = res
      this.defaultText = '1'
    },
    refresh() {
      this.getOrderCount()
      this.getLineChart()
    },
    async getOrderCount() {
      this.loading = true
      const response = await Func.fetch(orderCount)
      this.loading = false
      if (response && response.data) {
        const data = response.data
        this.dspCount = data.dspCount
        this.dfpCount = data.dfpCount
        this.dskCount = data.dskCount
        this.dfbCount = data.dfbCount
        this.dwcCount = data.dwcCount
        this.dwcNBCount = data.dwcNBCount
        this.dfbNBCount = data.dfbNBCount
        this.hyrwCount = data.hyrwCount
        this.hyrwNBCount = data.hyrwNBCount
      }
    },
    async getPieChart() {
      const res = await Func.fetch(orderPie)
      this.pieData = res
    },
    async getLineChart() {
      const res = await Func.fetch(orderStatAll, { dateBegin: start, dateEnd: end })
      this.lineData = res
      this.defaultText = '3'
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.dashboard-editor-container {
  display: flex;
  align-content: center;
  justify-content: center;
  height: 100%;
  .title {
    display: flex;
    width: 350px;
    flex-flow: column;
    align-content: center;
    justify-content: center;
    img {
      width: 100%;
    }
    p {
      color: #a9acf3;
      font-weight: 400;
      // margin-left: 15px;
      text-align: center;
      width: 100%;
      font-size: 24px;
      letter-spacing: 0.07em;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // position: relative;
  // text-align: center;
  // margin-top: 50%;
  // transform: translateY(-50%);
  // h1 {
  //   position: absolute;
  //   left: 50%;
  //   top: 50%;
  // }
  // height: 80vh;
  // h1 {
  //   display: flex;
  //   justify-content: center;
  //   align-content: center;
  // }
  // display: flex;
  // justify-content: center;
  // align-content: center;
}

.card {
  padding-left: 10px;

  .card-content {
    background: #ffffff;
    border-radius: 4px;
    font-size: 14px;
    position: relative;
    transition: all 0.2s ease-in-out;
    border: 1px solid #e9eaec;
    display: flex;
    height: 80px;
    text-align: center;

    .card-icon {
      width: 80px;
      color: #ffffff;
      font-size: 48px;
      line-height: 72px;
    }

    .card-body {
      flex: 1;
      padding: 5px 0;

      .count {
        font-size: 30px;
        font-weight: 700;
      }

      .info {
        font-size: 12px;
        font-weight: 500;
        color: #c8c8c8;
      }
    }

    .card-body1 {
      flex: 1;
      padding: 5px 0;

      .count {
        width: 45px;
        font-size: 30px;
        font-weight: 700;
      }

      .info {
        font-size: 12px;
        font-weight: 500;
        color: #c8c8c8;
      }
    }
  }
}
</style>
