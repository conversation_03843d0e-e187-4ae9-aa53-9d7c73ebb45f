<template>
  <div v-loading="loading" class="white">
    <el-radio-group style="margin-left: 10px;height: 50px; display: flex;align-items: center" v-model="dateText"
                    @change="changeDate">
      <el-radio-button label="3">近三天</el-radio-button>
      <el-radio-button label="7">近一周</el-radio-button>
      <el-radio-button label="15">近半月</el-radio-button>
    </el-radio-group>
    <div ref="chart" :class="className" :style="{height:height,width:width}"></div>
  </div>

</template>

<script>
import echarts from 'echarts'
// import {orderStatAll} from '@/api/home'
import { formatDate } from '@/filters'
// import Func from '@/utils/func'
import { debounce } from '@/utils'

require('echarts/theme/macarons') // echarts 主题

const now = new Date()
const start = formatDate(now.getTime() - 3600 * 1000 * 24 * 3, 'yyyy-mm-dd') + ' 00:00:00'
// const end = dateFormat(now, 'yyyy-mm-dd') + ' 23:59:59'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '600px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    lineData: {
      type: Object,
      require: true
    },
    defaultText: {
      type: String,
      default: 3
    }
  },
  data() {
    return {
      chart: null,
      dateText: '3',
      loading: false,
      start: start
    }
  },
  watch: {
    lineData() {
      this.initChart()
    },
    defaultText(val) {
      if (val) {
        this.dateText = val
      }
    }
  },
  mounted() {
    this.initChart()
    if (this.autoResize) {
      this.__resizeHanlder = debounce(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 100)
      window.addEventListener('resize', this.__resizeHanlder)
    }

    // 监听侧边栏的变化
    const sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    sidebarElm.addEventListener('transitionend', this.__resizeHanlder)
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    if (this.autoResize) {
      window.removeEventListener('resize', this.__resizeHanlder)
    }

    const sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    sidebarElm.removeEventListener('transitionend', this.__resizeHanlder)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    changeDate() {
      this.start = formatDate(now.getTime() - 3600 * 1000 * 24 * Number(this.dateText), 'yyyy-mm-dd') + ' 00:00:00'
      this.$emit('start', this.start)
    },
    async getData() {
      if (this.lineData.data) {
        const data = this.lineData.data
        this.chart.setOption({
          xAxis: {
            data: data.xData,
            axisLabel: {
              rotate: 45
            }
          },
          legend: {
            data: ['每日委托单']
          },
          grid: {
            left: 30,
            right: 10,
            bottom: 40,
            top: 30,
            containLabel: true
          },

          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            }
          },
          yAxis: {},
          series: [{
            name: '每日委托单',
            areaStyle: { normal: {} },
            smooth: true,
            type: 'bar',
            data: data.assayApplyCountList,
            animationDuration: 2000,
            animationEasing: 'cubicInOut'
          }
          ]
        })
      }
    },
    initChart() {
      this.chart = echarts.init(this.$refs['chart'], 'macarons')
      this.getData()
    }
  }
}
</script>
<style lang="scss" scoped>
.white {
  background: #fff;
}
</style>
