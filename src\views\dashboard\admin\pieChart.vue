<template>
  <div :class="className" class="white" :style="{height:height,width:width}"></div>
</template>

<script>
import echarts from 'echarts'

require('echarts/theme/macarons') // echarts 主题

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '500px'
    },
    pieData: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  watch: {
    pieData(val) {
      if (val) {
        this.initChart()
      }
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.getData()
    },
    async getData() {
      // this.loading = true
      // const res = await Func.fetch(orderPie)
      // this.loading = false
      if (this.pieData.data) {
        const data = this.pieData.data
        const consumerDataList = data.consumerDataList
        const nameList = data.nameList
        this.chart.setOption({
          title: {
            text: '用户消费积分',
            x: 'center',
            y: '20px'
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
          },
          legend: {
            x: 'center',
            y: 'bottom',
            data: nameList
          },
          calculable: true,
          series: [
            {
              name: '用户消费积分',
              type: 'pie',
              roseType: 'radius',
              data: consumerDataList,
              animationEasing: 'cubicInOut',
              animationDuration: 2600
            }
          ]
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.white {
  background: #fff;
}
</style>
