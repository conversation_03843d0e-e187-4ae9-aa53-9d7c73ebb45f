<template>
  <div class="dashboard-editor-container">
    <section class="report-days">
      <div class="inspection">
        <panel-bar title="进销存数据" :src="images.weeks" style="margin-bottom:10px" />
        <div class="inspection-sections">
          <div class="section">
            <p class="section-title">昨日</p>
            <div class="section-wrap">
              <div class="section-wrap-item" @click="handleToNamePage(
                { parent:`invoicing`,
                  child:`purchase`,
                  Pagename:`purchaseSummarydetails`,
                  params:{}
                })">
                <!-- params:{ date:getDay(1,true) } -->
                <div class="title-wrap">
                  <div class="icon-box">
                    <!-- <img src="@/assets/home/<USER>" alt="icon_buy"> -->
                    <img src="@/assets/home/<USER>" alt="icon_buy">
                  </div>
                  <div class="title">购进(吨)</div>
                </div>
                <div class="number">
                  <countTo :startVal='startVal' :endVal='homeData.yesterdayBuyInReceiveWeight' :duration='duration' :decimals="2">
                  </countTo>
                </div>
              </div>
              <div class="section-wrap-item" @click="handleToNamePage(
                { parent:`invoicing`,
                  child:`salev`,
                  Pagename:`salesSummarydetails`,
                  params:{ }
                })">
                <!--  params:{date:getDay(1,true), } -->
                <div class="title-wrap">
                  <div class="icon-boxV">
                    <img src="@/assets/home/<USER>" alt="icon_buy">
                  </div>
                  <div class="title">销售(吨)</div>
                </div>
                <div class="number" style="color: #ff726b;">
                  <countTo :startVal='startVal' :endVal='homeData.yesterdaySellOutSendWeight' :duration='duration' :decimals="2">
                  </countTo>
                </div>
              </div>
            </div>
          </div>
          <div class="section">
            <p class="section-title">本月</p>
            <div class="section-wrap">
              <div class="section-wrap-item">
                <!--  params:{ date:getDay(7), } -->
                <div class="title-wrap">
                  <div class="icon-box">
                    <img src="@/assets/home/<USER>" alt="icon_buy">
                  </div>
                  <div class="title">购进(吨)</div>
                </div>
                <div class="number">
                  <countTo :startVal='startVal' :endVal='homeData.curMonthBuyInReceiveWeight' :duration='duration' :decimals="2">
                  </countTo>
                </div>

              </div>
              <div class="section-wrap-item">
                <!-- params:{ }  -->
                <div class="title-wrap">
                  <div class="icon-boxV">
                    <img src="@/assets/home/<USER>" alt="icon_buy">
                  </div>
                  <div class="title">销售(吨)</div>
                </div>
                <div class="number" style="color: #ff726b;">
                  <countTo :startVal='startVal' :endVal='homeData.curMonthSellOutSendWeight' :duration='duration' :decimals="2">
                  </countTo>
                </div>

              </div>
            </div>
          </div>
        </div>

        <div class="inspection-sections">
          <div class="section">
            <p class="section-title">本年度</p>
            <div class="section-wrap">
              <div class="section-wrap-item">
                <!-- params:{ date:getDay(365), } -->
                <div class="title-wrap">
                  <div class="icon-box">
                    <img src="@/assets/home/<USER>" alt="icon_buy">
                  </div>
                  <div class="title">购进(吨)</div>
                </div>
                <div class="number">
                  <countTo :startVal='startVal' :endVal='homeData.curYearBuyInReceiveWeight' :duration='duration' :decimals="2">
                  </countTo>
                </div>

              </div>
              <div class="section-wrap-item">
                <div class="title-wrap">
                  <div class="icon-boxV">
                    <img src="@/assets/home/<USER>" alt="icon_buy">
                  </div>
                  <div class="title">销售(吨)</div>
                </div>
                <div class="number" style="color: #ff726b;">
                  <countTo :startVal='startVal' :endVal='homeData.curYearSellOutSendWeight' :duration='duration' :decimals="2">
                  </countTo>
                </div>
              </div>
            </div>
          </div>
          <div class="section">
            <p class="section-title">实时</p>
            <div class="section-wrap">
              <div class="section-wrap-item-no-after" @click="handleToNamePage(
                { parent:`stock`,
                  child:`coalStock`,
                  Pagename:`coalStock`,
                  params:{}
                })">
                <div class="title-wrap">
                  <div class="icon-boxV">
                    <img src="@/assets/home/<USER>" alt="icon_stock">
                  </div>
                  <div class="title">总库存(吨)</div>
                </div>
                <div class="number" style="color:#ff726b;">
                  <countTo :startVal='startVal' :endVal='homeData.totalStock' :duration='duration' :decimals="2"></countTo>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <panel-bar title="生产数据" :src="images.production" style="margin-bottom:10px" />
                <div class="inspection-sections">
                  <div class="wrap" @click="handleToNamePage(
                        { parent:`production`,
                          child:`coalProductionList`,
                          Pagename:`coalProductionList`,
                          params:{}
                        })">
                    <div class="icon-box">
                      <img src="@/assets/home/<USER>" alt="production_yesterday">
                    </div>
                    <div class="title-box">
                      <div class="number">
                        <countTo :startVal='startVal' :endVal='homeData.yesterdayProductionOutputWeight' :duration='duration'
                                 :decimals="2"></countTo>
                      </div>
                      <div class="title">昨日生产(吨)</div>
                    </div>
                  </div>
                  <div class="wrap" @click="handleToNamePage(
                        { parent:`production`,
                          child:`productionMonth`,
                          Pagename:`productionMonth`,
                          params:{}
                        })">
                    <div class="icon-box">
                      <img src="@/assets/home/<USER>" alt="production_month">
                    </div>
                    <div class="title-box">
                      <div class="number">
                        <countTo :startVal='startVal' :endVal='homeData.curMonthProductionOutputWeight' :duration='duration'
                                 :decimals="2"></countTo>
                      </div>
                      <div class="title">本月生产(吨)</div>
                    </div>
                  </div>

                  <div class="wrap" @click="handleToNamePage(
                        { parent:`production`,
                          child:`productionMonth`,
                          Pagename:`productionMonth`,
                          params:{}
                        })">
                    <div class="icon-box">
                      <img src="@/assets/home/<USER>" alt="production_year">
                    </div>
                    <div class="title-box">
                      <div class="number">
                        <countTo :startVal='startVal' :endVal='homeData.curYearProductionOutputWeight' :duration='duration' :decimals="2">
                        </countTo>
                      </div>
                      <div class="title">本年度生产(吨)</div>
                    </div>
                  </div>
                </div> -->

        <div class="inspection-sections">
          <card class="tscla" height="inherit">
            <card-item title="配煤生产" :url="images.production">
              <section class="reports">
                <div class="reports-item" @click="handleToNamePage(
                { parent:`production`,
                  child:`coalProductionList`,
                  Pagename:`coalProductionList`,
                  params:{}
                })">
                  <div class="reports-item-section">
                    <div class="reports-item-number">
                      <div class="number" style="color: #4c4c4c;">
                        <countTo :startVal='startVal' :endVal='Number(homeDatacoalblending.daySum)' :duration='duration'
                                 :decimals="2">
                        </countTo>
                      </div>

                    </div>
                    <div class="reports-item-desc">
                      <span>今日生产（吨）</span>
                    </div>
                  </div>
                </div>
              </section>
              <section class="reports">
                <div class="reports-item">
                  <div class="reports-item-section">
                    <div class="reports-item-number">
                      <div class="number" style="color: #4c4c4c;">
                        <countTo :startVal='startVal' :endVal='Number(homeDatacoalblending.monthSum)' :duration='duration'
                                 :decimals="2">
                        </countTo>
                      </div>
                    </div>
                    <div class="reports-item-desc">
                      <span>本月生产（吨）</span>
                    </div>
                  </div>
                </div>
                <div class="reports-item">
                  <div class="reports-item-section">
                    <div class="reports-item-number">
                      <div class="number" style="color: #4c4c4c;">
                        <countTo :startVal='startVal' :endVal='Number(homeDatacoalblending.yearSum)' :duration='duration'
                                 :decimals="2">
                        </countTo>
                      </div>
                    </div>
                    <div class="reports-item-desc">
                      <span>本年度生产（吨）</span>
                    </div>
                  </div>
                </div>
              </section>
            </card-item>
          </card>
        </div>

        <div class="inspection-sections">
          <card class="tscla" height="inherit">
            <card-item title="洗煤生产" :url="xmimg">
              <section class="reports">
                <div class="reports-item" @click="handleToNamePage(
                { parent:`production`,
                  child:`washMixCoal`,
                  Pagename:`washMixCoal`,
                  params:{}
                })">
                  <div class="reports-item-section">
                    <div class="reports-item-number">
                      <div class="number" style="color:#4c4c4c;">
                        <countTo :startVal='startVal' :endVal='Number(homeDatawashCoal.daySum)' :duration='duration'
                                 :decimals="2">
                        </countTo>
                      </div>
                    </div>
                    <div class="reports-item-desc">
                      <span>今日生产（吨）</span>
                    </div>
                  </div>
                </div>
              </section>
              <section class="reports">
                <div class="reports-item">
                  <div class="reports-item-section">
                    <div class="reports-item-number">
                      <div class="number" style="color: #4c4c4c;">
                        <countTo :startVal='startVal' :endVal='Number(homeDatawashCoal.monthSum)' :duration='duration'
                                 :decimals="2">
                        </countTo>
                      </div>
                    </div>
                    <div class="reports-item-desc">
                      <span>本月生产（吨）</span>
                    </div>
                  </div>
                </div>
                <div class="reports-item">
                  <div class="reports-item-section">
                    <div class="reports-item-number">
                      <div class="number" style="color: #4c4c4c;">
                        <countTo :startVal='startVal' :endVal='Number(homeDatawashCoal.yearSum)' :duration='duration'
                                 :decimals="2">
                        </countTo>
                      </div>
                    </div>
                    <div class="reports-item-desc">
                      <span>本年度生产（吨）</span>
                    </div>
                  </div>
                </div>
              </section>
            </card-item>
          </card>
        </div>

      </div>
    </section>

    <section class="report-weeks">
      <!-- <panel-bar title="待审核事项" :src="images.inspection" />
      <card height="21.5vh" style="margin-bottom:10px">
        <div class="blackone">
          <div class="section">
            <div class="section-wrapbox">
              <div class="section-item">
                <div class="conbox">
                  <div class="tipbox">
                    <div>合同</div>
                    <div class="rednumber">420</div>
                  </div>
                  <div class="imgbox" style="background: rgba(242, 120, 107, 0.2);">
                    <img src="@/assets/home/<USER>" />
                  </div>
                </div>
              </div>
              <div class="section-item" style="margin-right:0">
                <div class="conbox">
                  <div class="tipbox">
                    <div>结算</div>
                    <div class="bluenumber">420</div>
                  </div>
                  <div class="imgbox" style="background: rgba(47,121,232, 0.2);">
                    <img src="@/assets/home/<USER>" />
                  </div>
                </div>

              </div>
              <div class="section-item">
                <div class="conbox">
                  <div class="tipbox">
                    <div>下量</div>
                    <div class="greennumber">420</div>
                  </div>
                  <div class="imgbox" style="background: rgba(58,204,209, 0.2);">
                    <img src="@/assets/home/<USER>" />
                  </div>
                </div>
              </div>
              <div class="section-item" style="margin-right:0">
                <div class="conbox">
                  <div class="tipbox">
                    <div>付款</div>
                    <div class="orangenumber">420</div>
                  </div>
                  <div class="imgbox" style="background: rgba(255,149,60, 0.2);">
                    <img src="@/assets/home/<USER>" />
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </card> -->

      <!-- <card height="33.5vh" style="margin-bottom:10px">
        <div style="width:100%">
          <panel-bar class="report-panel" title="近七日数据" :src="images.weeks">
            <div class="week">
              <el-date-picker style="width: 15rem;margin-right:20px" type="date" value-format="yyyy-MM-dd"
                              v-model="weeksActive.beginDate" placeholder="结束日期" :clearable="false" />
              <el-date-picker disabled style="width: 15rem;" type="date" value-format="yyyy-MM-dd" v-model="weeksActive.endDate"
                              placeholder="开始日期" :clearable="false" />
            </div>
          </panel-bar>
          <div class="card-wrap">
            <div class="card-charts">
              <div class="title">近七日购销（精品煤）</div>
              <echarts v-if="isHouseOption" width="100%" height="100%" :option="houseOption" />
              <el-empty v-else description="七天内暂无数据" :image-size="100"></el-empty>
            </div>
          </div>
        </div>
      </card> -->
      <div class="inspection">
        <div class="inspection-sections">
          <card height="48.8vh">
            <div class="blackone">
              <panel-bar title="待审核事项" :src="images.inspection" style="padding-left:0;padding-top:0;padding-bottom:0;" />
              <div class="section">
                <div class="section-wrapbox" style="height:39vh;">
                  <el-table :data="listAllReviews" style="width:100%;height:100%;" height="39vh" stripe
                            :header-cell-style="headClass">
                    <el-table-column label="名称" prop="name">
                      <template slot-scope="scope">
                        <div v-if="scope.row.productName">{{scope.row.productName}}</div>
                        <div v-else>{{scope.row.name}}</div>
                      </template>
                    </el-table-column>
                    <el-table-column label="时间" prop="itemCode">
                      <template slot-scope="scope">
                        {{scope.row.applyDate}}
                      </template>
                    </el-table-column>
                    <el-table-column label="类型" width="150">
                      <template slot-scope="scope">
                        <div style="width:150px">
                          <span class="royaltag"
                                v-if="scope.row.reviewType=='weightHouseNotice:isnoticePr'">{{scope.row.typeName}}</span>

                          <span class="bluetag" v-if="scope.row.reviewType=='完成'">{{scope.row.typeName}}</span>

                          <span class="bluetag" v-if="scope.row.reviewType=='applyPay:pay'">{{scope.row.typeName}}</span>

                          <span class="bluetag"
                                v-if="scope.row.reviewType=='settleRecvFreight:ismanagerreview'">{{scope.row.typeName}}</span>
                          <span class="bluegreentag"
                                v-if="scope.row.reviewType=='settleRecvFreight:isFinancereview'">{{scope.row.typeName}}</span>
                          <span class="royaltag"
                                v-if="scope.row.reviewType=='settleRecvFreight:isBossreview'">{{scope.row.typeName}}</span>

                          <span class="bluetag"
                                v-if="scope.row.reviewType=='applyPay:ismanagerreview'">{{scope.row.typeName}}</span>

                          <!-- <span class="bluetag" v-if="scope.row.reviewType=='contractSell:review'">{{scope.row.typeName}}</span> -->
                          <span class="bluetag"
                                v-if="scope.row.reviewType=='contractSell:ismanagerreview'">{{scope.row.typeName}}</span>
                          <span class="bluegreentag"
                                v-if="scope.row.reviewType=='contractSell:isFinancereview'">{{scope.row.typeName}}</span>
                          <span class="royaltag"
                                v-if="scope.row.reviewType=='contractSell:isBossreview'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractSell:isFinanceLeaderreview'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractSell:changePriceReview'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractSell:priceFactoryManagerWaitforReviewed'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractSell:priceManagerWaitforReviewed'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractSell:priceBossWaitforReviewed'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractSell:carriageFactoryManagerWaitforReviewed'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractSell:carriageManagerWaitforReviewed'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractSell:carriageBossWaitforReviewed'">{{scope.row.typeName}}</span>

                          <span class="royaltag"
                                v-if="scope.row.reviewType=='contractBuy:ismanagerreview'">{{scope.row.typeName}}</span>
                          <span class="bluegreentag"
                                v-if="scope.row.reviewType=='contractBuy:isFinancereview'">{{scope.row.typeName}}</span>
                          <span class="royaltag"
                                v-if="scope.row.reviewType=='contractBuy:isBossreview'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractBuy:isFinanceLeaderreview'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractBuy:changePriceReview'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractBuy:priceFactoryManagerWaitforReviewed'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractBuy:priceManagerWaitforReviewed'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractBuy:priceBossWaitforReviewed'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractBuy:carriageFactoryManagerWaitforReviewed'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractBuy:carriageManagerWaitforReviewed'">{{scope.row.typeName}}</span>
                          <span class="redtag"
                                v-if="scope.row.reviewType=='contractBuy:carriageBossWaitforReviewed'">{{scope.row.typeName}}</span>

                          <span class="yellowtag"
                                v-if="scope.row.reviewType=='applyPay:isFinancereview'">{{scope.row.typeName}}</span>

                          <span class="yellowtag"
                                v-if="scope.row.reviewType=='applyPay:isBossreview'">{{scope.row.typeName}}</span>

                          <span class="royaltag" v-if="scope.row.reviewType=='applyRecv:review'">{{scope.row.typeName}}</span>

                          <span class="greentag"
                                v-if="scope.row.reviewType=='settlePay:ismanagerreview'">{{scope.row.typeName}}</span>

                          <span class="greentag"
                                v-if="scope.row.reviewType=='settlePay:isFinancereview'">{{scope.row.typeName}}</span>

                          <span class="greentag"
                                v-if="scope.row.reviewType=='settlePay:isBossreview'">{{scope.row.typeName}}</span>

                          <span class="orangetag"
                                v-if="scope.row.reviewType=='settleRecv:isFinancereview'">{{scope.row.typeName}}</span>

                          <span class="greentag"
                                v-if="scope.row.reviewType=='settleRecv:ismanagerreview' ">{{scope.row.typeName}}</span>

                          <span class="greentag"
                                v-if="scope.row.reviewType=='inOrder:WarehousingWaitforreviewed'">{{scope.row.typeName}}</span>

                          <span class="greentag"
                                v-if="scope.row.reviewType=='outOrder:ExwarehouseWaitforreviewed'">{{scope.row.typeName}}</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100">
                      <template slot-scope="scope">
                        <!-- applyPay:applayReviewed -->
                        <el-tag v-if="scope.row.reviewType" class="opt-btn" color="#FF726B"
                                style="color:#fff;cursor: pointer;border:solid 1px #FF726B"
                                @click="applyReturn(scope.row,'update')">
                          <span v-if="scope.row.reviewType=='applyPay:pay'">付款确认</span>
                          <span v-else>去审核</span>
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>

                <!-- <div class="pagination-container" ref="paginationContainer">
                  <el-pagination :current-page="page.current" :page-size="page.size" :total="page.total"
                                 :pager-count="pagination.pageCount" :layout="pagination.layout"
                                 :page-sizes="pagination.pageSizes" next-text="下一页" @size-change="sizeChange"
                                 @current-change="currentChange">
                    <el-button class="confirm">确认</el-button>
                  </el-pagination>
                </div> -->

              </div>
            </div>
          </card>
        </div>
        <div class="inspection-sections">
          <card height="43.6vh" style="margin-top:1rem">
            <div class="blackone">
              <!-- sybf.png -->
              <panel-bar title="库存煤数量" :src="images.invoicing" style="padding-left:0;padding-top:0, padding-bottom:0;" />
              <div class="section">
                <div class="section-wrapbox">
                  <el-table :data="datalist" style="width: 100%" stripe :header-cell-style="headClass" height="34vh">
                    <el-table-column label="品名" prop="name">
                      <template slot-scope="scope">
                        {{scope.row.name}}
                      </template>
                    </el-table-column>
                    <el-table-column label="实时库存" prop="stock">
                      <template slot-scope="scope">
                        {{scope.row.stock}}
                      </template>
                    </el-table-column>
                    <!-- <el-table-column label="价格" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.categoryName" disabled />
                      </template>
                    </el-table-column> -->
                    <el-table-column label="备注" prop="remarks">
                      <template slot-scope="scope">
                        {{scope.row.remarks}}
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </card>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import countTo from 'vue-count-to'
import {
  tdHomeInfo,
  getmixCoalYearMonthDaySum,
  getwashCoalYearMonthDaySum,
  getlistAllReviews,
  getpagelist
} from '@/api/dashboard'
import { PanelBar, Card, CardItem } from '@/components/Console'
import { sumStockByDate, sumWeightByDate, sumWeightByDateBuyIn } from '@/api/weeksData'
import { listQuery } from '@/const'
import { mapGetters } from 'vuex'
import { parseTime } from '@/utils'
import { getStateTextFromDict } from '@/filters/index'
import { pagination } from '@/const'

const stock = {
  animation: true,
  color: ['#FF9639'],
  tooltip: {
    trigger: 'axis',
    point: [-30, -30],
    backgroundColor: '#EDEEFF',
    // padding: [0],

    textStyle: {
      color: '#9f9ea5'
    }
  },
  grid: {
    top: '30px',
    left: '50px',
    right: '15px',
    bottom: '50px'
  },
  legend: {
    // right: 20,
    // itemGap: 30,
    // padding: [10, 10, 10, 10],
    align: 'left',
    data: []
  },
  xAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF', // 坐标轴线的颜色修改--文字也同步修改
        type: 'dashed'
      }
    },
    axisTick: {
      // 坐标轴刻度相关设置
      alignWithLabel: true // 可以保证刻度线和标签对齐
    },
    axisLabel: {
      color: '#9f9ea5' // 刻度标签文字的颜色
    },
    data: []
  },
  yAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF',
        type: 'solid'
      }
    },
    axisLabel: {
      // 坐标轴刻度标签的相关设置
      color: '#9f9ea5'
    },
    splitLine: {
      // 坐标轴在 grid 区域中的分隔线。
      show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示。
      lineStyle: {
        color: '#E3F0FF' // 分隔线颜色，可以设置成单个颜色
      }
    }
  },
  series: [
    {
      name: '',
      type: 'bar',
      barWidth: 16,
      barGap: '30%',
      itemStyle: {
        normal: {
          barBorderRadius: [30, 30, 0, 0], // 配置样式
          color: () => ''
        }
      },
      data: []
    }
  ]
}
const house = {
  color: ['#2f79e8', '#33CAD9'],
  grid: {
    top: '30px',
    left: '50px',
    right: '15px',
    bottom: '50px'
  },

  tooltip: {
    trigger: 'axis',
    point: [-30, -30],
    backgroundColor: '#EDEEFF',
    padding: [5, 20, 5, 20],
    textStyle: {
      color: '#9f9ea5'
    }
  },
  legend: {
    right: 0,
    itemGap: 10,
    align: 'left',
    data: ['购', '销']
  },
  xAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF', // 坐标轴线的颜色修改--文字也同步修改
        type: 'dashed'
      }
    },
    axisTick: {
      // 坐标轴刻度相关设置
      alignWithLabel: true // 可以保证刻度线和标签对齐
    },
    axisLabel: {
      color: '#9f9ea5' // 刻度标签文字的颜色
    },
    data: []
  },
  yAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF',
        type: 'solid'
      }
    },
    axisLabel: {
      // 坐标轴刻度标签的相关设置
      color: '#9f9ea5'
    },
    splitLine: {
      // 坐标轴在 grid 区域中的分隔线。
      show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示。
      lineStyle: {
        color: '#E3F0FF' // 分隔线颜色，可以设置成单个颜色
      }
    }
  },
  series: [
    {
      name: '购',
      type: 'bar',
      barWidth: 13,
      barGap: '30%',
      itemStyle: {
        normal: {
          barBorderRadius: [30, 30, 0, 0], // 配置样式
          color: () => ''
        }
      },
      data: []
    },
    {
      name: '销',
      type: 'bar',
      barWidth: 13,
      barGap: '30%',
      itemStyle: {
        normal: {
          barBorderRadius: [30, 30, 0, 0], // 配置样式
          color: () => ''
        }
      },
      data: []
    }
  ]
}
const pie_option = {
  color: ['#FF9639', '#33CAD9'],
  title: {
    text: '供应商\n  占比',
    align: 'center',
    // left: 'center',
    left: '26.5%',
    y: '42%',
    textStyle: {
      fontSize: 14,
      fontWeight: '400'
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c} ({d}%)'
  },
  legend: {
    y: '35%',
    itemGap: 20,
    orient: 'vertical',
    right: '10%',
    data: []
  },

  series: [
    {
      name: '年度客户数据',
      type: 'pie',
      radius: [35, 60],
      center: ['32%', '50%'],
      label: {
        normal: {
          show: true,
          formatter: '{d}%', // 模板变量有 {a}、{b}、{c}、{d}，分别表示系列名，数据名，数据值，百分比。{d}数据会根据value值计算百分比

          textStyle: {
            align: 'center',
            baseline: 'middle',
            fontFamily: '微软雅黑',
            fontSize: 12,
            fontWeight: 'bolder'
          }
        }
      },

      // roseType: 'area',
      data: [
        // { value: 223, name: '其他' },
        // { value: 1511, name: '山西焦煤' }
      ]
    }
  ]
}
export default {
  components: { PanelBar, Card, CardItem, countTo },
  data() {
    return {
      pie_option: {
      },
      duration: 1000, // 需要滚动的时间
      startVal: 0, // 初始值
      images: {
        inspection: require(`@/assets/dashboard/day_inspection.png`),
        invoicing: require(`@/assets/dashboard/day_invoicing.png`),
        weeks: require(`@/assets/console/panel_icon.png`),
        production: require(`@/assets/home/<USER>
      },
      xmimg: require(`@/assets/home/<USER>
      listQuery: { ...listQuery },
      loading: false,
      // houseOption: {},
      // stockOption: {},
      // weeksActive: { beginDate: '', endDate: '' }, // 近七日数据date
      homeData: {
        buyInChannelSumVoList: []
      },
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      homeDatacoalblending: {},
      homeDatawashCoal: {},
      datalist: [],
      pagination,
      page: {
        current: 1,
        size: pagination.pageSize,
        total: 0
      },
      listAllReviews: []
    }
  },
  computed: {
    ...mapGetters(['showRoutes']),
    isPieOption() {
      // console.log(this.pie_option)
      if (Object.keys(this.pie_option).length) return true
      return false
    }
    // isStockOption() {
    //   if (Object.keys(this.stockOption).length) return true
    //   return false
    // },
    // isHouseOption() {
    //   if (Object.keys(this.houseOption).length) return true
    //   return false
    // }
  },
  created() {
    // this.dateInit()
    this.reqInit()
    this.get_TD_HomeInfo()
    this.getYearMonthDaySumfn(this.currentDate) //首页---配煤汇总接口
    this.getwashCoalYearMonthDaySumfn(this.currentDate) //首页---配煤汇总接口

    this.getlistAllReviewsfn() //获取待审核事项列表
    this.getpagelistfn()
  },
  methods: {
    applyReturn(item) {
      let type = ''
      let code = ''

      if (item.applyType2 == 'SettlePay') {
        //付款结算
        if (item.reviewType == 'settlePay:ismanagerreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settlePay:isFinancereview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settlePay:isBossreview') {
          type = 'NEW,PASS_FINANCE'
        }
        let data = { parent: `finance`, child: `settlement`, Pagename: `settlePay`, params: { id: item.id, type: type } }
        this.handleToNamePage(data)
      }

      if (item.applyType2 == 'SettleRecv') {
        //收款结算
        if (item.reviewType == 'settleRecv:ismanagerreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settleRecv:isFinancereview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settleRecv:isBossreview') {
          type = 'NEW,PASS_FINANCE'
        }
        let data = { parent: `finance`, child: `settlement`, Pagename: `settleRecv`, params: { id: item.id, type: type } }
        this.handleToNamePage(data)
      }

      if (item.applyType2 == 'SettleRecvFreight') {
        //销售运费结算
        if (item.reviewType == 'settleRecvFreight:ismanagerreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settleRecvFreight:isFinancereview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settleRecvFreight:isBossreview') {
          type = 'NEW,PASS_FINANCE'
        }
        let data = { parent: `finance`, child: `settlement`, Pagename: `settleRecvFreight`, params: { id: item.id, type: type } }
        this.handleToNamePage(data)
      }

      if (item.applyType2 == 'ApplyPay') {
        //付款单
        if (item.reviewType == 'applyPay:ismanagerreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'applyPay:isFinancereview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'applyPay:isBossreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'applyPay:pay') {
          type = 'PASS'
        }
        let data = { parent: `finance`, child: `payment`, Pagename: `applyPay`, params: { id: item.id, type: type } }
        this.handleToNamePage(data)
      }

      if (item.applyType2 == 'ContractBuy') {
        let Auditstatus = ''
        //采购合同
        if (item.reviewType == 'contractBuy:ismanagerreview') {
          //  去审核(总经理)
          Auditstatus = 'MANAGER'
        } else if (item.reviewType == 'contractBuy:isFinancereview') {
          //去审核(财务)
          Auditstatus = 'FINANCE'
        } else if (item.reviewType == 'contractBuy:isFinanceLeaderreview') {
          //去审核(财务负责人)
          Auditstatus = 'FINANCE_LEADER'
        } else if (item.reviewType == 'contractBuy:isBossreview') {
          //     去审核(董事长)
          Auditstatus = 'isBossreview'
        } else if (
          item.reviewType == 'contractBuy:priceFactoryManagerWaitforReviewed' ||
          item.reviewType == 'contractBuy:priceManagerWaitforReviewed' ||
          item.reviewType == 'contractBuy:priceBossWaitforReviewed'
        ) {
          Auditstatus = 'AdjustPriceReview'
        } else if (
          item.reviewType == 'contractBuy:carriageFactoryManagerWaitforReviewed' ||
          item.reviewType == 'contractBuy:carriageManagerWaitforReviewed' ||
          item.reviewType == 'contractBuy:carriageBossWaitforReviewed'
        ) {
          Auditstatus = 'freightReview'
        }
        type = 'NEW,PASS_FINANCE'
        let data = {
          parent: `base`,
          child: `buy`,
          Pagename: `contractBuy`,
          params: { id: item.id, type: type, Auditstatus: Auditstatus }
        }
        this.handleToNamePage(data)
      }

      // if (item.applyType2 == 'ContractSell') {
      //       //销售合同
      //       if (item.reviewType == 'contractSell:ismanagerreview') {
      //         type = 'PASS_FINANCE'
      //       } else if (item.reviewType == 'contractSell:isFinancereview') {
      //         type = 'NEW'
      //       }
      //       let data = { parent: `base`, child: `buy`, Pagename: `contractSell`, params: { id: item.id, type: type } }
      //       this.handleToNamePage(data)
      //     }

      if (item.applyType2 == 'ContractSell') {
        let Auditstatus = ''
        //采购合同
        if (item.reviewType == 'contractSell:ismanagerreview') {
          //  去审核(总经理)
          Auditstatus = 'MANAGER'
        } else if (item.reviewType == 'contractSell:isFinancereview') {
          //去审核(财务)
          Auditstatus = 'FINANCE'
        } else if (item.reviewType == 'contractSell:isFinanceLeaderreview') {
          //去审核(财务负责人)
          Auditstatus = 'FINANCE_LEADER'
        } else if (item.reviewType == 'contractSell:isBossreview') {
          //     去审核(董事长)
          Auditstatus = 'isBossreview'
        } else if (
          item.reviewType == 'contractSell:priceFactoryManagerWaitforReviewed' ||
          item.reviewType == 'contractSell:priceManagerWaitforReviewed' ||
          item.reviewType == 'contractSell:priceBossWaitforReviewed'
        ) {
          Auditstatus = 'AdjustPriceReview'
        } else if (
          item.reviewType == 'contractSell:carriageFactoryManagerWaitforReviewed' ||
          item.reviewType == 'contractSell:carriageManagerWaitforReviewed' ||
          item.reviewType == 'contractSell:carriageBossWaitforReviewed'
        ) {
          Auditstatus = 'freightReview'
        }
        type = 'NEW,PASS_FINANCE'
        let data = {
          parent: `base`,
          child: `sell`,
          Pagename: `contractSell`,
          params: { id: item.id, type: type, Auditstatus: Auditstatus }
        }
        this.handleToNamePage(data)
      }

      if (item.applyType2 == 'WeightHouseNotice') {
        //销磅房通知
        if (item.reviewType == 'weightHouseNotice:isnoticePr') {
          type = 'NEW'
        } else if (item.reviewType == 'weightHouseNotice:send') {
          type = 'PASS'
        }
        let data = { parent: `finance`, Pagename: `weightHouseNotice`, params: { id: item.id, type: type } }
        this.handleToNamePage(data)
      }
      if (item.applyType2 == 'InOrder') {
        //入库
        if (item.reviewType == 'inOrder:WarehousingWaitforreviewed') {
          type = 'NEW'
        }
        let data = { parent: `material`, Pagename: `inOrder`, params: { id: item.id, type: type } }
        this.handleToNamePage(data)
      }
      if (item.applyType2 == 'OutOrder') {
        //出库
        if (item.reviewType == 'outOrder:ExwarehouseWaitforreviewed') {
          type = 'NEW'
        }
        let data = { parent: `material`, Pagename: `outOrder`, params: { id: item.id, type: type } }
        this.handleToNamePage(data)
      }
    },
    // applyReturn(item) {
    //   console.log(item)
    //   console.log(item.id)
    //   console.log(item.reviewType)
    //   let type = ''
    //   if (
    //     item.reviewType == 'contractSell:ismanagerreview' ||
    //     item.reviewType == 'contractBuy:ismanagerreview' ||
    //     item.reviewType == 'applyPay:applayReviewed'
    //   ) {
    //     type = 'PASS_FINANCE'
    //   } else if (
    //     item.reviewType == 'settleRecv:managerreview' ||
    //     item.reviewType == 'settlePay:managerreview' ||
    //     item.reviewType == 'settlePay:settlePayManagerwaitforreviewed' ||
    //     item.reviewType == 'settleRecv:settlePayManagerwaitforreviewed' ||
    //     item.reviewType == 'applyPay:review' ||
    //     item.reviewType == 'contractSell:isFinancereview' ||
    //     item.reviewType == 'contractBuy:isFinancereview' ||
    //     item.reviewType == 'weightHouseNotice:waitforreviewed' ||
    //     item.reviewType == 'inOrder:WarehousingWaitforreviewed' ||
    //     item.reviewType == 'outOrder:ExwarehouseWaitforreviewed' ||
    //     item.reviewType == 'applyPay:applayManagerwaitforreviewed'
    //   ) {
    //     type = 'NEW'
    //   } else if (
    //     item.reviewType == 'applyPay:pay' ||
    //     item.reviewType == 'contractBuy:Reviewed' ||
    //     item.reviewType == 'contractSell:Reviewed' ||
    //     item.reviewType == 'settleRecv:Financereview' ||
    //     item.reviewType == 'settlePay:Financereview' ||
    //     item.reviewType == 'settlePay:settlePayFinancewaitforreviewed' ||
    //     item.reviewType == 'settleRecv:settlePayFinancewaitforreviewed' ||
    //     item.reviewType == 'weightHouseNotice:Reviewed' ||
    //     item.reviewType == 'applyPay:applayFinancewaitforreviewed' ||
    //     item.reviewType == 'weightHouseNotice:send'
    //   ) {
    //     type = 'PASS'
    //   }
    //   if (
    //     item.reviewType == 'applyPay:review' ||
    //     item.reviewType == 'applyPay:applayManagerwaitforreviewed' ||
    //     item.reviewType == 'applyPay:applayFinancewaitforreviewed' ||
    //     item.reviewType == 'applyPay:applayReviewed'
    //   ) {
    //     let data = { parent: `finance`, child: `payment`, Pagename: `applyPay`, params: { id: item.id, type: type } }
    //     this.handleToNamePage(data)
    //   } else if (item.reviewType == 'applyRecv:review') {
    //     let data = { parent: `finance`, child: `payment`, Pagename: `applyRecv`, params: { id: item.id, type: type } }
    //     this.handleToNamePage(data)
    //   } else if (
    //     item.reviewType == 'settleRecv:settlePayManagerwaitforreviewed' ||
    //     item.reviewType == 'settleRecv:settlePayFinancewaitforreviewed'
    //   ) {
    //     let data = { parent: `finance`, child: `settlement`, Pagename: `settleRecv`, params: { id: item.id, type: type } }
    //     this.handleToNamePage(data)
    //   } else if (
    //     item.reviewType == 'settlePay:settlePayManagerwaitforreviewed' ||
    //     item.reviewType == 'settlePay:settlePayFinancewaitforreviewed'
    //   ) {
    //     let data = { parent: `finance`, child: `settlement`, Pagename: `settlePay`, params: { id: item.id, type: type } }
    //     this.handleToNamePage(data)
    //   } else if (item.reviewType == 'contractSell:ismanagerreview' || item.reviewType == 'contractSell:isFinancereview') {
    //     //销售合同
    //     let data = { parent: `base`, child: `sell`, Pagename: `contractSell`, params: { id: item.id, type: type } }
    //     this.handleToNamePage(data)
    //   } else if (item.reviewType == 'contractBuy:ismanagerreview' || item.reviewType == 'contractBuy:isFinancereview') {
    //     //采购合同
    //     let data = { parent: `base`, child: `buy`, Pagename: `contractBuy`, params: { id: item.id, type: type } }
    //     this.handleToNamePage(data)
    //   } else if (item.reviewType == 'weightHouseNotice:waitforreviewed') {
    //     // uni.navigateTo({
    //     // 	url:'/packageB/weightHouseNotice/weightHouseNotice?id='+item.id+'&type='+type
    //     // })

    //     let data = { parent: `finance`, Pagename: `weightHouseNotice`, params: { id: item.id, type: type } }
    //     this.handleToNamePage(data)
    //   } else if (item.reviewType == 'inOrder:WarehousingWaitforreviewed') {
    //     let data = { parent: `material`, Pagename: `inOrder`, params: { id: item.id, type: type } }
    //     this.handleToNamePage(data)
    //     // uni.navigateTo({
    //     // 	url:'/packageB/inOrder/inOrder?id='+item.id+'&type='+type
    //     // })
    //   } else if (item.reviewType == 'outOrder:ExwarehouseWaitforreviewed') {
    //     let data = { parent: `material`, Pagename: `outOrder`, params: { id: item.id, type: type } }
    //     this.handleToNamePage(data)
    //     // uni.navigateTo({
    //     // 	url:'/packageB/outOrder/outOrder?id='+item.id+'&type='+type
    //     // })
    //   } else if (item.reviewType == '下量') {
    //     // uni.navigateTo({
    //     //   url: '/packageB/Lowerquantity/Lowerquantity',
    //     // })
    //   }
    //   //  else if (item.reviewType == 'applyPay:pay') {
    //   //   let data = { parent: `finance`, child: `payment`, Pagename: `applyPay`, params: { id: item.id, type: type } }
    //   //   this.handleToNamePage(data)
    //   // }
    // },

    async getlistAllReviewsfn() {
      try {
        const res = await getlistAllReviews()
        if (res.data) {
          this.listAllReviews = res.data
        }
      } catch (error) {}
    },
    async getpagelistfn() {
      this.listQuery.size = 50
      this.listQuery.current = 1
      try {
        const res = await getpagelist(this.listQuery)
        if (res.data.records) {
          this.datalist = res.data.records
        }
      } catch (error) {}
    },
    /**
     * 页码大小改变触发
     * @param size
     */
    sizeChange(size) {
      this.page.size = size
      this.getData()
      // 返回给页面的钩子
      this.$parent.$emit('sizeChange', size)
    },
    /**
     * 当前页变动时候触发
     * @param current 当前页
     */
    currentChange(current) {
      this.page.current = current
      this.getData()
      // 返回给页面的钩子
      this.$parent.$emit('currentChange', current)
    },

    headClass() {
      return 'text-align:left;background:#33CAD9;color:#fff;font-weight:400;border:none;'
    },
    async get_TD_HomeInfo() {
      try {
        const res = await tdHomeInfo()
        if (res.data) {
          this.homeData = res.data
          this.formatPieCharts(res.data.buyInChannelSumVoList)
        }
      } catch (error) {}
    },
    async getYearMonthDaySumfn(date) {
      //首页---配煤汇总接口
      try {
        const res = await Promise.all([getmixCoalYearMonthDaySum(date)])
        this.homeDatacoalblending = res[0].data
        // console.log(this.customer)
        // console.log(this.supplier)
      } catch (error) {}
    },

    async getwashCoalYearMonthDaySumfn(date) {
      //首页--洗煤生产数据接口
      try {
        const res = await Promise.all([getwashCoalYearMonthDaySum(date)])
        this.homeDatawashCoal = res[0].data
        // console.log(this.customer)
        // console.log(this.supplier)
      } catch (error) {}
    },

    reqInit() {
      // this.getWeeksData()
      this.$store.dispatch('formatRoutes')
    },
    // dateInit() {
    //   this.weeksActive.beginDate = this.getWeeks({ type: 'sub', days: 6 })
    // },
    // 获取周数据方法
    // async getWeeksData() {
    //   let endDate = this.getWeeks({ date: this.weeksActive.endDate, type: 'add', days: 1 })
    //   let { beginDate } = this.weeksActive

    //   const params = { beginDate, endDate } // 左闭右开
    //   const res = await Promise.all([sumWeightByDate(params), sumWeightByDateBuyIn(params), sumStockByDate(params)])
    //   if (res.length) {
    //     this.houseOption = this.formatOption({
    //       data: [res[0].data, res[1].data],
    //       option: house,
    //       target: 'weightHouse'
    //     })
    //     this.stockOption = this.formatOption({
    //       data: res[2].data,
    //       option: stock,
    //       par: 'stock',
    //       target: 'stock'
    //     })
    //   }
    // },
    // formatOption({ data, option, par, target }) {
    //   let startDate = this.weeksActive.endDate.slice(5)
    //   let prefix = this.weeksActive.endDate.substring(0, 5)
    //   let dateList = []
    //   let dataList = []
    //   for (let index = 0; index < 7; index++) {
    //     if (index === 0) {
    //       dateList.push(startDate)
    //     } else {
    //       dateList.push(
    //         this.getWeeks({
    //           type: 'sub',
    //           days: 1,
    //           date: prefix + dateList[index - 1]
    //         }).slice(5)
    //       )
    //     }
    //   }
    //   option = JSON.parse(JSON.stringify(option))
    //   dateList.reverse()
    //   if (target === 'weightHouse') {
    //     if (!data[0].length && !data[1].length) return {}
    //     let first = []
    //     let second = []
    //     data.forEach((list, index) => {
    //       if (index === 0) {
    //         list.forEach((item) => {
    //           const dateIndex = dateList.findIndex((date) => date === item.date.slice(5))
    //           first[dateIndex] = item.sendWeight
    //         })
    //         option.series[1].data = first
    //       } else {
    //         list.forEach((item) => {
    //           const dateIndex = dateList.findIndex((date) => date === item.date.slice(5))
    //           second[dateIndex] = item.receiveWeight
    //         })
    //         option.series[0].data = second
    //       }
    //     })
    //   } else {
    //     if (!data.length) return {}
    //     data.forEach((item) => {
    //       dataList[dateList.indexOf(item.date.slice(5))] = item[par]
    //     })
    //     option.series[0].data = dataList
    //   }
    //   option.xAxis.data = dateList
    //   return option
    // },

    handleToPage(parent, child) {
      const route = this.showRoutes.find((route) => route.name === parent)
      this.$store.dispatch('changeRoute', route)
      this.$store.dispatch('changeChildRoute', { parent: child, child: '' })
      this.$router.replace({ path: `${route.path}/${child}` })
    },
    // 通过name去跳转类似post
    handleToNamePage(option) {
      const { parent, child, Pagename, params } = option
      const route = this.showRoutes.find((route) => route.name === parent)
      route.params = option.params
      this.$store.dispatch('changeRoute', route)
      this.$store.dispatch('changeChildRoute', { parent: child, child: '' })
      this.$router.replace({ name: Pagename, params })
    },
    getWeeks({ type = '', date = new Date(), days = 6, fmt = 'yyyy-MM-dd' }) {
      const day = 24 * 60 * 60 * 1000
      return type === 'add'
        ? new Date(new Date(date).getTime() + day * days).Format(fmt)
        : new Date(new Date(date).getTime() - day * days).Format(fmt)
    },

    getDay(day = 1, isYesterday = false) {
      if (isYesterday) {
        let end = new Date().getTime() - 3600 * 1000 * 24 * 1
        return [parseTime(end, '{y}-{m}-{d}'), parseTime(end, '{y}-{m}-{d}')]
      } else {
        const end = new Date(new Date().toDateString())
        const start = new Date()
        start.setTime(end.getTime() - 3600 * 1000 * 24 * day)
        return [parseTime(start, '{y}-{m}-{d}'), parseTime(end, '{y}-{m}-{d}')]
      }
    },
    formatPieCharts(list) {
      // 字典格式化处理
      let name = []
      list.forEach((item) => {
        item.name = getStateTextFromDict(item.name, 'b_supplier_channel')
        name.push(item.name)
      })
      pie_option.legend.data = name
      pie_option.series[0].data = list
      this.pie_option = pie_option
    }
  },
  watch: {
    // 'weeksActive.beginDate'(v) {
    //   if (!v) {
    //     this.weeksActive.endDate = ''
    //     this.getWeeksData()
    //     return
    //   }
    //   this.weeksActive.endDate = this.getWeeks({ type: 'add', date: v })
    //   this.getWeeksData()
    // }
  }
}
</script>

<style scoped>
.bluetag {
  color: #33cad9;
  display: block;
  width: 120px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.redtag {
  color: red;
  display: block;
  width: 120px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.bluegreentag {
  width: 120px;
  color: #6e88f5;
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.greentag {
  /* color: green; */
  width: 120px;
  display: block;
  color: #2f79e8;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.yellowtag {
  color: #d3b916;
  display: block;
  width: 120px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.orangetag {
  width: 120px;
  color: #ff931f;
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.royaltag {
  /* color: #976bf5; */
  width: 120px;
  color: #ff726b;
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.section-wrap:first-of-type:after {
  content: '';
  position: absolute;
  opacity: 1;
  right: 0;
  height: 20px;
  width: 1px;
  /* width: 1.5px; */
  background: #efefef;
  /* background: #eee; */
}
</style>

<style scoped>
.blackone >>> .el-table th.el-table__cell {
  background-color: #33cad9 !important;
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
.pagination-container {
  display: flex;
  margin: 0;
  padding: 15px 0px 0px;
  justify-content: flex-end;
  // position: absolute;
  // right: 20px;
  // bottom: 20px;
  // z-index: 999;
  border: none;

  .el-pager li.active + li {
    border: 1px solid #f1f1f2;
  }

  .el-pager li {
    width: 40px;
    height: 35px;
    line-height: 35px;
    border-radius: 5px;
    border: 1px solid #f1f1f2;
    margin: 0 5px;
  }

  .el-pager li.active {
    background: #2f79e8;
    color: #fff;
  }

  .el-pagination .btn-next,
  .el-pagination .btn-prev {
    margin: 0px;
    border-radius: 5px;
    border: 1px solid #f1f1f2;
    height: 35px;
    line-height: 35px;

    span {
      line-height: 35px;
    }
  }

  .el-pagination button,
  .el-pagination span:not([class*='suffix']) {
    padding: 0 5px;
    // width: 16%;
    border-radius: 5px;
    height: 35px;
    line-height: 35px;
  }

  .el-pagination__editor.el-input .el-input__inner {
    height: 35px;
  }
  .el-pagination .el-select .el-input .el-input__inner {
    height: 35px;
  }
  .confirm {
    margin-left: 15px;
    height: 32px;
    line-height: 32px;
    background: #2f79e8;
    color: #fff;
    border-radius: 5px;
  }
}

.blackone {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 1.25rem;
  .section-wrapbox {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    .section-item {
      margin-right: 10px;
      padding: 1.25rem;
      width: calc(100% / 2 - 5px);
      background-color: #f9f9f9;
      margin-bottom: 10px;
      .conbox {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        .imgbox {
          width: 40px;
          height: 40px;
          // background: rgba(242, 120, 107, 0.3);
          border-radius: 100%;
          text-align: center;
          & > img {
            width: 18px;
            height: 20px;
            margin-top: 10px;
          }
        }
        .tipbox {
          width: calc(100% - 50px);
          font-size: 14px;
          .rednumber {
            color: #ff726b;
            font-size: 18px;
            margin-top: 10px;
          }
          .bluenumber {
            color: #2f79e8;
            font-size: 20px;
            margin-top: 10px;
          }
          .greennumber {
            color: #33cad9;
            font-size: 20px;
            margin-top: 10px;
          }
          .orangenumber {
            color: #ff9639;
            font-size: 20px;
            margin-top: 10px;
          }
        }
      }
    }
  }
}

::v-deep .el-empty {
  padding: 25px 0 30px;
}

::v-deep .el-empty__description p {
  font-size: 12px;
}

.card-item {
  background: #fff;
  flex: 1;
  display: flex;
  flex-flow: column nowrap;
  padding: 0px 10px;

  &-title {
    color: #3d3d3d;
    font-size: 15px;
    // padding: 10px 3px 10px 0;
    display: inline-block;

    .sub {
      font-size: 12px;
      // margin-left: 3px;
      color: #9f9f9f;
    }
  }

  &:nth-of-type(2) {
    margin-left: 10px;
  }
}

.tscla {
  .reports {
    width: inherit;
    height: inherit;
    display: flex;

    &-item {
      width: inherit;
      height: inherit;
      display: flex;
      flex-flow: column;
      justify-content: space-evenly;
      position: relative;

      &-section {
        opacity: 0.9;
        transition: all 0.5s;
        text-align: center;

        .reports-item-number {
          // font-size: 2.5rem;
          // font-size: 2.875rem;
          font-size: 1.875rem;
          font-weight: 900;
          padding: 1.2rem 0;
          margin-right: 2rem;
          cursor: pointer;
        }

        .reports-item-desc {
          cursor: pointer;
          text-align: center;

          span:first-of-type {
            // font-size: 2rem;
            font-size: 2rem;
            padding: 1.2rem 0;
            color: #33cad9;
            letter-spacing: 0.2rem;
            margin-bottom: 10px;
          }

          span:last-of-type {
            font-size: 1.5rem;
            padding: 1.2rem 0;
            color: #9c9c9c;
          }
        }
      }

      &-section:hover {
        opacity: 1;
      }
    }

    &:first-of-type {
      flex: 0 0 30%;
    }

    &-item:first-of-type:after {
      content: '';
      position: absolute;
      opacity: 1;
      right: 0;
      height: 30px;
      // width: 2px;
      // background: #efefef;
      width: 1px;
      background: #eee;
    }
  }
}

.year-custom {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card {
  margin: 0;
}

.card-wrap {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 85%;
  // height: 100%;
  background: #fff;
  padding: 10px 20px 0px;
  box-sizing: border-box;

  .card-charts {
    flex: 1;

    .title {
      opacity: 0.7;
      color: #333333;
      font-size: 1.5rem;
    }
  }
}

.panel {
  margin-bottom: 0;

  .panel-title {
    img {
      width: 1.9rem;
    }

    span:first-of-type {
      font-size: 1.5rem;
    }
  }
}

.dashboard-editor-container {
  width: 100%;
  height: 100vh;
  display: flex;

  .report-days {
    display: flex;
    flex-flow: column;
    // flex: 1;
    width: 65%;
    margin: 0 10px;

    .inspection {
      display: flex;
      flex-flow: column;

      &-sections {
        display: flex;
        // height: 26.3vh;
        height: 21.2vh;
        margin-bottom: 1rem;

        .section {
          background: #fff;
          padding: 1rem 0.6rem 2rem;
          display: flex;
          flex-flow: column;
          flex: 1;
          cursor: pointer;

          &:first-of-type {
            margin-right: 10px;
          }

          &-title {
            opacity: 0.7;
            position: relative;
            margin: 5px 1rem;
            font-size: 1.5rem;

            &::after {
              position: absolute;
              content: '';
              width: 100%;
              height: 1px;
              bottom: -10px;
              left: 0;
              background: #eee;
            }
          }

          &-wrap {
            display: flex;
            height: 100%;

            &-item,
            &-item-no-after {
              margin-top: 13px;
              width: 100%;
              height: 100%;
              display: flex;
              flex-flow: column;
              align-items: center;
              justify-content: center;
              position: relative;
              box-sizing: border-box;

              .number {
                // font-size: 2.875rem;
                font-size: 2rem;
                // font-weight: bold;
                padding: 1.2rem 0;
                color: #33cad9;
                font-weight: 900;
                letter-spacing: 0.2rem;
              }

              .title-wrap {
                width: inherit;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 40px;

                .icon-box {
                  width: 2rem;
                  height: 2rem;
                  margin-right: 10px;
                  img {
                    width: 100%;
                    height: 100%;
                  }
                }
                .icon-boxV {
                  width: 1.8rem;
                  height: 1.8rem;
                  margin-right: 10px;
                  img {
                    width: 100%;
                    height: 100%;
                  }
                }

                .title {
                  font-size: 1.6rem;
                  // color: #323232;
                  color: #9c9c9c;
                }
              }
            }

            &-item:first-of-type:after {
              content: '';
              position: absolute;
              opacity: 1;
              right: 0;
              height: 30px;
              // width: 1.5px;
              // background: #efefef;
              width: 1px;
              background: #eee;
            }
          }
        }

        .wrap {
          background: #fff;
          padding: 1rem 0.6rem 2rem;
          display: flex;
          flex-flow: column;
          align-items: center;
          justify-content: space-evenly;
          flex: 1;
          margin-right: 10px;

          &:last-of-type {
            margin-right: 0px;
          }

          .icon-box {
            width: 7rem;
            height: 7rem;
            img {
              width: 100%;
              height: 100%;
            }
          }

          .title-box {
            height: 75px;
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            flex-flow: column;

            .number {
              // font-size: 2.875rem;
              // font-size: 1.875rem;
              font-size: 2rem;
              font-weight: 900;
              font-weight: bold;
            }

            .title {
              font-size: 1.3rem;
            }
          }
        }
      }
    }
  }

  .report-weeks {
    // flex: 0.5;
    width: 35%;
    .inspection {
      display: flex;
      flex-flow: column;

      &-sections {
        display: flex;
        // height: 26.3vh;
        height: 47.7vh;
        margin-bottom: 1rem;

        .section {
          background: #fff;
          padding: 1rem 0.6rem 2rem;
          display: flex;
          flex-flow: column;
          flex: 1;
          cursor: pointer;

          &:first-of-type {
            margin-right: 10px;
          }

          &-title {
            opacity: 0.7;
            position: relative;
            margin: 5px 1rem;
            font-size: 1.5rem;

            &::after {
              position: absolute;
              content: '';
              width: 100%;
              height: 1px;
              bottom: -10px;
              left: 0;
              background: #eee;
            }
          }

          &-wrap {
            display: flex;
            height: 100%;

            &-item,
            &-item-no-after {
              margin-top: 13px;
              width: 100%;
              height: 100%;
              display: flex;
              flex-flow: column;
              align-items: center;
              justify-content: center;
              position: relative;
              box-sizing: border-box;

              .number {
                // font-size: 2.875rem;
                // font-size: 1.875rem;
                font-size: 2rem;
                // font-weight: bold;
                padding: 1.2rem 0;
                font-weight: 900;
                color: #33cad9;
                letter-spacing: 0.2rem;
              }

              .title-wrap {
                width: inherit;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 40px;

                .icon-box {
                  width: 3rem;
                  height: 3rem;
                  margin-right: 10px;
                  img {
                    width: 100%;
                    height: 100%;
                  }
                }
                .icon-boxV {
                  width: 2.7rem;
                  height: 2.7rem;
                  margin-right: 10px;

                  img {
                    width: 100%;
                    height: 100%;
                  }
                }

                .title {
                  font-size: 1.6rem;
                  // color: #323232;
                  color: #9c9c9c;
                }
              }
            }

            &-item:first-of-type:after {
              content: '';
              position: absolute;
              opacity: 1;
              right: 0;
              height: 30px;
              // width: 1.5px;
              // background: #efefef;
              width: 1px;
              background: #eee;
            }
          }
        }

        .wrap {
          background: #fff;
          padding: 1rem 0.6rem 2rem;
          display: flex;
          flex-flow: column;
          align-items: center;
          justify-content: space-evenly;
          flex: 1;
          margin-right: 10px;

          &:last-of-type {
            margin-right: 0px;
          }

          .icon-box {
            width: 5rem;
            height: 5rem;
            img {
              width: 100%;
              height: 100%;
            }
          }

          .title-box {
            height: 75px;
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            flex-flow: column;

            .number {
              // font-size: 2.875rem;
              // font-size: 1.875rem;
              font-size: 2rem;
              font-weight: 900;
              font-weight: bold;
            }

            .title {
              font-size: 1.3rem;
            }
          }
        }
      }
    }
  }
}
</style>
