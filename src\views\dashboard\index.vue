<template>

  <component :is="currentRole"></component>

</template>

<script>
import { mapGetters } from 'vuex'
import adminDashboard from './admin/index'
import editorDashboard from './editor/index'

export default {
  name: 'dashboard',
  components: { adminDashboard, editorDashboard },
  data () {
    return {
      currentRole: 'adminDashboard'
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'perms'
    ])
  },
  created () {
    this.currentRole = this.perms['dashboard:view'] ? editorDashboard : adminDashboard
  }
}
</script>
