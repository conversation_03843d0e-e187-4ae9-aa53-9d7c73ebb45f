<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :countList="countList" :buttomlist="buttomlist" :showRefresh="perms[`${curd}:Refresh`]||false" />
    <!-- :statetype="activetype" -->
    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :countList="countList" :buttomlist="buttomlist"
            :actionList="actionList" :showSummary='true' title="供应商" otherHeight="120">

      <el-table-column label="支付方式" slot="payWay" align="center">
        <template slot-scope="scope" v-if="scope.row.payWay">
          <el-tag :type="(scope.row.payWay=='1' ? 'danger':(scope.row.payWay == '2' ? 'warning' : (scope.row.payWay == '3' ? 'success' : (scope.row.payWay == '4' ? 'danger' :(scope.row.payWay == '5' ? 'danger' :'')))))"
                  size="mini">
            {{ scope.row.payWay == '1' ? '微信' : (scope.row.payWay == '2' ? '现金' : (scope.row.payWay == '3' ? '转账' : (scope.row.payWay == '4' ? '承兑':(scope.row.payWay == '5' ? '现汇':'')))) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="收款类型" slot="applyType" align="center">
        <template slot-scope="scope" v-if="scope.row.applyType">
          <!-- REFUND -->
          <el-tag :type="(scope.row.applyType=='TRADE' ? 'danger':(scope.row.applyType == 'REFUND' ? 'success' : ''))"
                  size="mini">
            {{scope.row.applyType == 'TRADE' ? '贸易' :  (scope.row.applyType == 'REFUND' ? '供应商退款' : '')}}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="是否结算" slot="isSettle" prop="isSettle" align="center" scope-slot>
        <template slot-scope="scope">
          <el-select v-model="scope.row.isSettle" clearable placeholder="请选择" @change="selectChangeisSettle(scope.row)">
            <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
          </el-select>
        </template>
      </el-table-column>

      <!-- <el-table-column label="状态" slot="state">
        <template slot-scope="scope">
          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :(scope.row.state == 'PAYED' ? 'success' : 'info')))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '待审核' : (scope.row.state == 'PASS' ? '已审核' : (scope.row.state == 'REJECT' ? '拒绝':(scope.row.state == 'PAYED' ? '已付款' : '')))) }}
          </el-tag>
        </template>
      </el-table-column> -->

      <!-- <el-table-column label="附件" slot="attachment" prop="attachment" width="100">
        <template slot-scope="scope">
          <div class="attachment">
            <img class="attachment-img" src="@/assets/attachment.png" @click="handlePreviewAttachment(scope.row)">
          </div>
        </template>
      </el-table-column> -->

      <el-table-column label="附件" slot="attachmentList" prop="attachmentList" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handlePreviewAttachment(scope.row)">查看附件</el-button>
        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" max-width="150" width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">

            <!-- <div v-if="perms[`${curd}:review`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'NEW'" color="#FF726B" @click="handleUpdate(scope.row,'review')">
                去审核
              </el-tag>
            </div> -->

            <div v-if="perms[`${curd}:update`] || false">
              <!-- v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" -->
              <el-tag class="opt-btn" color="#FF726B" @click="handleUpdate(scope.row,'update')">
                编辑
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false" style="margin-left:5px">
              <!-- v-if="scope.row.state == 'PAYED' || scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" -->
              <el-tag class="opt-btn" style="cursor: pointer;" color="#2f79e8" @click="handleDel(scope.row)">删除
              </el-tag>
              <!-- {{scope.row.state}} -->
            </div>
            <div v-if="scope.row.state =='NEW' || scope.row.state =='PASS' ">
              <el-tag class="opt-btn" color="#FF726B" @click="handleUpdate(scope.row,'watch')">
                查看
              </el-tag>
            </div>

          </div>
          <!-- perms[`${curd}:delete`]||false && -->
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">

        <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="reviewLogList">
              <el-steps :active="reviewLogList.length">
                <el-step v-for="(item,index) in reviewLogList" :key="index"
                         :title="item.reviewResult + ' ( 审批意见：'+item.reviewMessage+' )'"
                         :description="item.createDate+' '+item.reviewUserName">
                </el-step>
              </el-steps>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div class="form-title">基础信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="applyDate">
                    <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同" prop="contractId">
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 filterable clearable placeholder="请选择合同" style="width:100%" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="供应商" prop="customerId">
                    <el-select v-model="supplierEntity.value" placeholder="请选择供应商" clearable filterable style="width:100%"
                               @change="handleSupplier" disabled>
                      <el-option v-for="item in supplierEntity.options" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="买方" prop="firstParty">
                    <el-select v-model="secondPartyEntity.value" placeholder="请选择买方" clearable filterable style="width:100%"
                               disabled>
                      <el-option v-for="item in secondPartyEntity.options" :key="item.id" :label="item.name" :value="item.name" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                 disabled />
                  </el-form-item>
                </el-col>
                <el-col>
                </el-col>
              </el-row>

            </div>
            <div class="form-title">收款信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="吨数" prop="amount">
                    <el-input v-model="entityForm.amount" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="单价" prop="price">
                    <el-input v-model="entityForm.price" clearable oninput="value=value.replace(/[^0-9.]/g,'')" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="支付方式" prop="payWay" :rules="[{ required: true, message: '请选择一项' }]">
                    <dict-select v-model="entityForm.payWay" type="payWay_type" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="收款金额" prop="money">
                    <el-input v-model="entityForm.money" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              @blur="setmoney(entityForm.money)" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row>

            </div>
          </el-col>
          <el-col>
            <div class="form-layout">
              <el-row>
                <el-col>
                  <el-form-item label="附件上传">
                    <div class="upload">
                      <div class="upload-list">
                        <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                          <!-- <el-image class="img" :src="item.uri" fit="fill" :preview-src-list="srcList" /> -->
                          <template v-if="isImage(item.uri)">
                            <el-image class="img active" :title="item.display" :src="item.uri" fit="fill"
                                      :preview-src-list="srcList" />
                          </template>
                          <template v-else>
                            <el-link :href="item.uri" target="_blank" :underline="false" class="file-icon">
                              <i class="el-icon-document" style="font-size: 50px;"></i>
                              <div class="file-name">{{ item.display }}</div>
                            </el-link>
                          </template>
                          <img src="@/assets/close.png" class="icon" @click="handleRemoveUpload(index)">
                        </div>
                        <upload-attachment ref="upload" class="upload-attachment" style="text-align: left;"
                                           url="/cwe/a/attachment/upload" :uploadData="uploadData" source="ContractBuy"
                                           :size="size" :limitNum="limitNum" :isShowFile="false"
                                           @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                           listType="text" />
                      </div>
                    </div>

                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer" v-if="dialogStatus != 'watch'">
        <!-- v-if="perms[`${curd}:review`]||false"-->
        <div v-if="dialogStatus != 'review'">
          <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
          <el-button @click="saveEntityForm('entityForm')" type="primary" class="dialog-footer-btns" style="width:100px">
            保存
          </el-button>
        </div>
      </div>
    </el-dialog>

    <el-drawer title="附件列表" :visible.sync="drawer.visible" direction="rtl" :before-close="handleCloseDrawer">
      <template v-if="drawer.list.length">
        <div class="attachment-container">
          <div class="attachment-item" v-for="(item,index) in drawer.list" :key="index">
            <!-- <el-image class="img" :title="item.display" :src="item.uri" fit="fill" :preview-src-list="drawer.preview" /> -->
            <!-- 图片预览 -->
            <div v-if="item.isImage" class="image-preview">
              <el-image class="preview-image" :src="item.uri" fit="contain" :preview-src-list="drawer.preview" />
              <div class="file-name">{{ item.display }}</div>
            </div>

            <!-- 文档展示 -->
            <div v-else-if="item.isDocument" class="document-preview">
              <i class="el-icon-document" style="font-size: 48px; color: #409EFF;"></i>
              <el-link :href="item.uri" :download="item.display" target="_blank" :underline="false" class="file-info">
                <div class="file-name">{{ item.display }}</div>
                <div class="file-type">{{ item.fileType.toUpperCase() }} 文档</div>
              </el-link>
            </div>

            <!-- Excel展示 -->
            <div v-else-if="item.isExcel" class="excel-preview">
              <i class="el-icon-s-data" style="font-size: 48px; color: #67C23A;"></i>
              <el-link :href="item.uri" :download="item.display" target="_blank" :underline="false" class="file-info">
                <div class="file-name">{{ item.display }}</div>
                <div class="file-type">Excel 表格</div>
                <!-- <div class="file-meta">已处理</div> -->
              </el-link>
            </div>

            <!-- 其他文件类型 -->
            <div v-else class="other-file">
              <i class="el-icon-files" style="font-size: 48px;"></i>
              <el-link :href="item.uri" :download="item.display" target="_blank" :underline="false" class="file-info">
                <div class="file-name">{{ item.display }}</div>
                <div class="file-type">{{ item.fileType.toUpperCase() }} 文件</div>
              </el-link>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <el-empty :image="emptyImgPath" :image-size="200" description="暂无图片" />
      </template>
    </el-drawer>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/finance/SupplierRefund'
import { validatePhone } from '@/utils/validate'
import { applyRecvOption } from '@/const'
import CustomerModel from '@/model/user/customer'
import productModel from '@/model/product/productList'
import { getCustomerContract, chooseContract, contractList } from '@/api/quality'
import { createMemoryField } from '@/utils/index'
import Cache from '@/utils/cache'
export default {
  name: 'SupplierRefund',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'SupplierRefund',
      model: Model,
      addressValue: '',
      entityForm: { ...Model.model },
      noticeForm: {},
      nameEntity: { options: [], active: '' },
      uploadList: [], // 用于展示
      uploadData: { refId: '', refType: 'applyRecv' },
      limitNum: 50,
      username: 'ss',
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      filterOption: { ...applyRecvOption },
      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        phone: [{ validator: validateIsPhone, trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      rules: {
        applyDate: { required: true, message: '请输入日期', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' },
        supplierId: { required: true, message: '请输入收款单位', trigger: 'blur' },
        productName: { required: true, message: '请选择输入品名', trigger: 'blur' },
        payWay: { required: true, message: '请选择输入支付方式', trigger: 'blur' },
        bank: { required: true, message: '请选择输入开户银行', trigger: 'blur' },
        money: { required: true, message: '请选择输入付款金额', trigger: 'blur' },
        customerName: { required: true, message: '请选择客户资料', trigger: 'blur' }
      },
      // 采购合同
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      // 销售合同
      contractEntityV: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      supplierEntity: { value: '', options: [] },
      customerEntity: { options: [], active: [] },
      secondPartyEntity: { value: '', options: [] },
      isshow: false,
      drawer: {
        visible: false,
        list: [],
        preview: [],
        dateList: []
      },
      emptyImgPath: require(`@/assets/empty.jpg`),
      reviewLogList: [],
      countList: [],
      isBilllist: [
        { label: '是', type: 'Y' },
        { label: '否', type: 'N' }
      ]
    }
  },
  watch: {
    'entityForm.productName'(name) {
      if (name) {
        const item = this.nameEntity.options.find((item) => item.name === name)
        if (item) {
          this.nameEntity.active = item.name
          this.entityForm.productCode = item.code
          this.entityForm.productId = item.id
          this.entityForm.coalType = item.coalCategory || ''
          this.entityForm.coalCategory = item.type
        }
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
        this.entityForm.coalCategory = ''
      }
    },
    'supplierEntity.value'(id) {
      // console.log(id)
      if (!id) return
      const item = this.supplierEntity.options.find((item) => item.id === id)
      // console.log(item)
      if (item) {
        this.supplierEntity.active = item
        this.entityForm.payee = item.name
        this.entityForm.customerId = item.id
        this.entityForm.customerName = item.name
      } else {
        this.supplierEntity.active = []
      }
    },

    // 'entityForm.contractId'(id) {
    //   if (!id) return
    //   if (this.dialogStatus === 'update' || this.dialogStatus === 'review' || this.dialogStatus === 'watch') {
    //     const value = this.filterContractItem(id, 'contractEntityV')
    //     this.contractEntityV.active = value
    //   }
    // },

    'entityForm.contractId'(id) {
      if (!id) return
      if (this.dialogStatus === 'update' || this.dialogStatus === 'review' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'contractEntity.active'(value) {
      // console.log(value)
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.supplierId
      this.entityForm.contractName = form.secondParty
      this.supplierEntity.value = value[0]

      const item = this.contractEntity.options.find((v) => v.supplierId === value[0])
      if (!item) return
      const itemvv = item.contractBuyList.find((v) => v.id === value[1])
      console.log(itemvv)
      if (!itemvv) return
      // if (itemvv.bank) {
      //   this.entityForm.bank = itemvv.bank
      // }
      // if (itemvv.bankNo) {
      //   this.entityForm.bankNo = itemvv.bankNo
      // }
      // if (itemvv.cardNo) {
      //   this.entityForm.cardNo = itemvv.cardNo
      // }
      this.nameEntity.active = form.productName
      this.entityForm.productName = itemvv.productName
      this.entityForm.supplierId = itemvv.supplierId
      this.entityForm.supplierName = itemvv.secondParty
      this.secondPartyEntity.value = itemvv.firstParty
      this.entityForm.firstParty = itemvv.firstParty

      this.entityForm.customerId = itemvv.supplierId
      this.entityForm.customerName = itemvv.secondParty
    },
    // 在打开更新时watch监听然后通过过滤筛选触发contractEntityFormActive灌入显示数据
    'contractEntityV.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItemV(value, 'contractEntityV')
      console.log(form)
      this.entityForm.contractCode = form.customerName
      this.entityForm.contractId = form.customerId
      this.entityForm.customerName = form.firstParty
      this.entityForm.supplierId = form.customerId
      this.entityForm.customerId = form.customerId

      this.entityForm.contractName = form.firstParty

      this.entityForm.supplierName = form.secondParty
      this.secondPartyEntity.value = form.secondParty

      this.entityForm.productName = form.productName
      this.nameEntity.active = form.productName

      this.customerEntity.active = form.firstParty

      this.entityForm.payee = form.firstParty
      this.entityForm = {
        ...this.entityForm,
        productName: form.productName,
        supplierName: form.firstParty,
        customerName: form.firstParty
      }
    }
  },
  created() {
    this.setbuttomlistV(this.curd)

    this.getName()

    //获取采购合同列表
    this.getContract()
    //获取销售合同列表
    this.getContractV()

    //获取客户列表
    this.getCustomer()

    //获取供应商
    this.getContractList()

    //获取卖方
    this.getSellContractPartyfn()

    this.memoryEntity.fields = createMemoryField({
      fields: ['contractId', 'contractCode', 'supplierId', 'supplierName'],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getnumber()
  },
  computed: {
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    }
  },
  methods: {
    isImage(url) {
      return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url);
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id);
      res.data.attachmentList.forEach((item, index) => {
        const fileType = item.uri.split('.').pop().toLowerCase()
        item.fileType = fileType
        // 添加文件类型判断
        item.isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileType)
        item.isDocument = ['doc', 'docx', 'pdf'].includes(fileType)
        item.isExcel = ['xls', 'xlsx'].includes(fileType)
      })
      this.drawer.list = res.data.attachmentList;
      this.drawer.preview = res.data.attachmentList
        .filter(item => this.isImage(item.uri))
        .map(item => item.uri);
      this.drawer.visible = true;
    },
    async getSellContractPartyfn() {
      const res = await this.model.getSellContractParty()
      if (res.data.length) this.secondPartyEntity.options = res.data
    },

    async selectChangeisSettle(row) {
      try {
        await this.model.getupdateIsSettle({ id: row.id, isSettle: row.isSettle })
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
        this.getList()
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },
    // 合同改变同步
    handleSupplier({ value, label }) {
      // this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label }
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label, customerName: label, customerId: value }
    },
    // changetab(tab, event) {
    //   this.$nextTick(() => {
    //     // 请求成功之后，在这里执行
    //     this.entityForm = { ...Model.model }
    //     this.entityForm.applyType = tab.name
    //     this.supplierEntity.value = ''
    //     this.secondPartyEntity.value = ''
    //     this.nameEntity.active = ''
    //     this.customerEntity.active = ''
    //     this.contractEntity.active = ''
    //     this.contractEntityV.active = ''
    //     this.$forceUpdate()
    //   })
    // },
    setmoney(e) {
      this.entityForm = { ...this.entityForm, money: e }
    },
    async getnumber() {
      const res = await this.model.getcountByState()
      this.countList = res.data
    },
    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      this.uploadList = [] // 清空下载列表
      this.reviewLogList = []
      this.contractEntityV.active = []
      this.customerEntity.active = []
      this.supplierEntity.value = ''
      this.secondPartyEntity.value = ''
      // this.PaymentApplicationVisible = false
      // this.noticeVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) { }
    },
    //获取客户列表
    async getCustomer() {
      try {
        let options = []
        const { data } = await CustomerModel.page({ size: 1000 })
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        this.customerEntity.options = options
      } catch (e) { }
    },

    //供应商列表
    async getContractList() {
      const { data } = await contractList()
      if (data.length) this.supplierEntity.options = data
    },

    // 获取销售合同选择接口
    async getContractV() {
      const { data } = await getCustomerContract()
      // 供应商名称返回id用来查询
      this.contractEntityV.options = this.formatContractV({ data, key: { customerName: 'code', customerId: 'id' } })
    },
    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContractV({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    // 获取采购合同选择接口
    async getContract() {
      try {
        const { data } = await chooseContract()
        this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'displayName', supplierId: 'id' } })
      } catch (error) { }
    },
    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    // 合同改变同步entityForm
    handleCustomer({ value, label }) {
      this.entityForm.customerId = value
      this.entityForm.customerName = label
    },

    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     * @target {contract|contractEntityForm}<String>
     */
    filterContractItemV(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },

    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        // console.log('走string')
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    async handleRemoveUpload(index) {
      const { id } = this.uploadList[index]
      this.$refs.upload.applyPayDelete({ id, index })
    },
    handleUploadSuccess(res) {
      this.uploadList = [...this.uploadList, res]
      this.entityForm.attachmentList = this.uploadList
    },

    handleUploadDelete({ status, index }) {
      this.uploadList.splice(index, 1)
    },

    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    async handleUpdate(item, type) {
      this.entityForm = { ...item }
      if (type == 'review') {
        this.isshow = true
        this.dialogStatus = 'review'
      } else if (type == 'update') {
        this.isshow = true
        this.dialogStatus = 'update'
      } else if (type == 'watch') {
        this.isshow = false
        this.dialogStatus = 'watch'
      }
      this.dialogFormVisible = true
      // 上传所需要的配置
      this.uploadData.refId = item.id
      const { data } = await this.model.getUploadList(item.id)
      this.entityForm = { ...data }

      if (this.entityForm.applyType == 'REFUND') {
        this.supplierEntity.value = this.entityForm.customerId
      }
      this.reviewLogList = data.reviewLogList
      this.uploadList = data.attachmentList
      let newarry = []
      if (data.reviewLogList.length > 0) {
        data.reviewLogList.forEach((item, index) => {
          newarry.push(item.reviewMessage)
        })
        this.entityForm.reviewMessage = newarry.toString()
      }
    },
    async Approved() {
      const res = await this.model.getApproved(this.entityForm)
    },
    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []
    },
    // async handlePreviewAttachment(row) {
    //   const res = await this.model.getUploadList(row.id)
    //   if (res.data.attachmentList) {
    //     this.drawer.list = res.data.attachmentList
    //     this.drawer.preview = res.data.attachmentList.map((item) => item.uri)
    //     this.drawer.visible = true
    //   }
    // },
    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },
    async passfn(id, reviewMessage) {
      const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage })
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    },

    async rejectfn(id, reviewMessage) {
      const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage })
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.contentbox {
  width: 500px;
  margin: 100px auto 0 auto;
  font-size: 12px;
  .line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tsline {
    float: right;
    margin-top: 40px;
  }
  .inputbox {
    width: 100px;
    margin-bottom: 10px;
    border-bottom: solid 1px #ccc;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .file-icon {
        overflow: hidden;
      }
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        // border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}

.attachment-container {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.attachment-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
  }
}

.image-preview {
  width: 100%;

  .preview-image {
    width: 100%;
    height: 150px;
  }

  .file-name {
    margin-top: 10px;
    font-size: 12px;
    color: #606266;
    word-break: break-all;
  }
}

.document-preview,
.excel-preview,
.other-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .file-info {
    margin-top: 10px;
    width: 100%;

    .file-name {
      font-size: 13px;
      color: #303133;
      word-break: break-all;
    }

    .file-type {
      font-size: 12px;
      color: #909399;
      margin-top: 5px;
    }

    .file-meta {
      font-size: 11px;
      color: #67c23a;
      margin-top: 5px;
      font-style: italic;
    }
  }
}

.excel-preview {
  .file-meta {
    background-color: #f0f9eb;
    padding: 2px 5px;
    border-radius: 3px;
  }
}
</style>
<style scoped>
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
</style>