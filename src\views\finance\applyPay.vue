<template>
  <div class="app-container">
    <!-- <el-date-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate" placeholder="选择日期"
                    :clearable="false" /> -->
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :buttomlist="buttomlist" :showRefresh="perms[`${curd}:Refresh`]||false" />

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :statetype="activetype" :buttomlist="buttomlist"
            :showSummary='true' :countList="countList" :actionList="actionList" title="供应商" otherHeight="170">

      <el-table-column label="支付方式" slot="payWay" align="center">
        <template slot-scope="scope" v-if="scope.row.payWay">
          <el-tag :type="(scope.row.payWay=='1' ? 'danger':(scope.row.payWay == '2' ? 'warning' : (scope.row.payWay == '3' ? 'success' : (scope.row.payWay == '4' ? 'danger' :(scope.row.payWay == '5' ? 'danger' :'')))))"
                  size="mini">
            {{ scope.row.payWay == '1' ? '微信' : (scope.row.payWay == '2' ? '现金' : (scope.row.payWay == '3' ? '转账' : (scope.row.payWay == '4' ? '承兑': (scope.row.payWay == '5' ? '现汇':'')))) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="付款类型" slot="applyType" align="center">
        <template slot-scope="scope" v-if="scope.row.applyType">
          <!-- REFUND -->
          <el-tag :type="(scope.row.applyType=='TRADE' ? 'danger':(scope.row.applyType == 'OTHER' ? 'warning' : (scope.row.applyType == 'REFUND' ? 'success' : '')))"
                  size="mini">
            {{scope.row.applyType == 'TRADE' ? '贸易' : (scope.row.applyType == 'OTHER' ? '其他' :  (scope.row.applyType == 'REFUND' ? '客户退款' : '')) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="状态" slot="state" align="center">
        <template slot-scope="scope">
          <!-- 通用审核状态 NEW-待审核、PASS-通过、REJECT-拒绝 -->
          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :(scope.row.state == 'PAYED' ? 'success' : 'info')))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '待审核' : (scope.row.state == 'PASS' ? '待付款' : (scope.row.state == 'REJECT' ? '拒绝':(scope.row.state == 'PAYED' ? '已付款' : '')))) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="是否结算" slot="isSettle" prop="isSettle" align="center" scope-slot>
        <template slot-scope="scope">
          <el-select v-model="scope.row.isSettle" clearable placeholder="请选择" @change="selectChangeisSettle(scope.row)">
            <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column label="备注" slot="remarks" prop="remarks" width="100">
        <template slot-scope="scope">
          <div v-if="scope.row.remarks">
            {{scope.row.remarks}}
          </div>
          <div v-if="scope.row.purpose">
            {{scope.row.purpose}}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="附件" slot="attachmentList" prop="attachmentList" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handlePreviewAttachment(scope.row)">查看附件</el-button>
        </template>
      </el-table-column>

      <el-table-column label="当前流程" slot="curFlowName" align="center" fixed="right">
        <template slot-scope="scope">

          <el-popover placement="right" width="700" trigger="click">
            <div v-for="(item,index) in steplistNEW" :key="index">
              <div>
                <div>{{item.label}}: <span style="color:red">{{item.curFlowName}}</span></div>
              </div>
              <el-row type="flex" :gutter="50" style="margin:20px 0">
                <el-col style="margin-bottom: -13px">
                  <div class="bzboxV">
                    <div class="bzminboxV" v-for="(value,indexc) in item.flowDesignList" :key="indexc">
                      <div class="lin" :class="value.active==true?'bordercolorBLUE':''"></div>
                      <div class="context" :class="value.active==true?'colorBLUE':''">

                        <i class="el-icon-edit icon" v-if="value.code=='APPLY_PAY_DRAFT'"></i>
                        <i class="el-icon-coordinate icon"
                           v-if="value.code=='APPLY_PAY_FINANCE_LEADER' ||  value.code=='APPLY_PAY_MANAGER' ||  value.code=='APPLY_PAY_BOSS' ||  value.code=='APPLY_PAY_PASS'"></i>

                        <i class="el-icon-link icon" v-if="value.code=='PASS'"></i>
                        <i class="el-icon-help icon" v-if="value.code=='APPLY_PAY_REJECT'"></i>

                        <i class="el-icon-document-checked" v-if="value.code=='APPLY_PAY_ PAYED'" style="font-size: 30px;"></i>

                        <div>{{value.aliasName}}</div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <el-button slot="reference" style="padding: 3px 6px;" @click="lookbtn(scope.row)">流程</el-button>
          </el-popover>

        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" max-width="100" width="170" fixed="right" align="center">
        <template slot-scope="scope">
          <div class="footclass" style="display: flex; flex-direction:row;justify-content: center;flex-wrap: wrap;">
            <div v-if="perms[`${curd}:ismanagerreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode =='APPLY_PAY_MANAGER'" color="#FF726B"
                      @click="handleUpdate(scope.row,'review','APPLY_PAY')" style=" margin-bottom: 10px;">
                去审核(财务)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:isFinancereview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode =='APPLY_PAY_FINANCE_LEADER'" color="#FF726B"
                      @click="handleUpdate(scope.row,'review','APPLY_PAY')" style=" margin-bottom: 10px;">
                去审核(财务负责人)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:isBossreview`] || false" style=" margin-bottom: 10px;">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode =='APPLY_PAY_BOSS'" color="#FF726B"
                      @click="handleUpdate(scope.row,'review','APPLY_PAY')">
                去审核(董事长)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:pay`]||false">
              <!-- <div v-if="perms[`${curd}:applayReviewed`]||false"> -->
              <el-tag class="opt-btn" v-if="scope.row.state == 'PASS' " style=" margin-bottom: 10px;" color="#FF9639"
                      @click="tobeCollected(scope.row)">
                付款</el-tag>
            </div>

            <div v-if="perms[`${curd}:pay`]||false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'PAYED' " style=" margin-bottom: 10px;" color="#FF9639"
                      @click="tobeCollected(scope.row,'lookPAYED')">
                付款查看</el-tag>
            </div>

            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT' " color="#FF726B"
                      @click="handleUpdate(scope.row,'update')" style=" margin-bottom: 10px;">
                编辑
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:istovoid`] || false">
              <el-tag class="opt-btn" color="#ff726b" v-if="scope.row.state == 'PASS' || scope.row.state=='PAYED'"
                      @click="handleCancel(scope.row)">作废
              </el-tag>
            </div>
            <div v-if="scope.row.state =='NEW' || scope.row.state =='PASS'|| scope.row.state =='PAYED'">
              <el-tag class="opt-btn" color="#FF726B" @click="handleUpdate(scope.row,'watch')" style="margin-bottom: 10px;">
                查看
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state =='PASS'|| scope.row.state =='PAYED'" color="#FF726B"
                      @click="handleUpdate(scope.row,'change')" style="margin-bottom: 10px;">
                修改
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag class="opt-btn" style="cursor: pointer;margin-bottom: 10px;" color="#2f79e8"
                      v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
    </s-curd>

    <!-- 通知磅房 -->
    <!-- <el-dialog width="880px" top="5vh" title="通知磅房" :visible.sync="dialogVisible" :before-close="handleClose"
               :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="noticeForm" :model="noticeForm" label-width="90px"
               label-position="top" v-if="dialogVisible">
        <el-row>
          <el-col>
            <div class="form-title">短信通知内容</div>
            <div class="form-layout">
              <div class="contentbox">
                <div class="line">
                  <div>准予</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>从</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>年</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>月</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>日起</div>
                </div>

                <div class="line">
                  <div>供应</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>煤</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>吨</div>
                </div>

                <div class="line">
                  <div>硫</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>灰</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>指数</div>
                </div>

                <div class="line">
                  <div>挥发份</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>回收</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>单价</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                </div>

                <div class="line tsline">
                  <div>签字人</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" />
                  </div>
                </div>

              </div>

            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="dialog-footer-btns" style="width:100px" :loading="entityFormLoading">
          保存并审核
        </el-button>
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="send('noticeForm')">发送短信
        </el-button>
      </div>
    </el-dialog> -->

    <el-dialog top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <!-- <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="reviewLogList">
              <el-steps :active="reviewLogList.length">
                <el-step v-for="(item,index) in reviewLogList" :key="index"
                         :title="item.reviewResult + ' ( 审批意见：'+item.reviewMessage+' )'"
                         :description="item.createDate+' '+item.reviewUserName">
                </el-step>
              </el-steps>
            </el-form-item>
          </el-col>
        </el-row> -->

        <el-row type="flex" :gutter="50" v-if="steplist.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="steplist">
              <div class="bzbox">
                <div v-for="(item,index) in steplist" :key="index" class="bzminbox">
                  <div class="lin" :class="item.active==true?'bordercolorBLUE':''"></div>
                  <div class="context" :class="item.active==true?'colorBLUE':''">
                    <i class="el-icon-edit icon" v-if="item.code=='APPLY_PAY_DRAFT'"></i>
                    <i class="el-icon-coordinate icon"
                       v-if="item.code=='APPLY_PAY_FINANCE_LEADER' ||  item.code=='APPLY_PAY_MANAGER' ||  item.code=='APPLY_PAY_BOSS'"></i>

                    <i class="el-icon-link icon" v-if="item.code=='APPLY_PAY_PASS'"></i>
                    <i class="el-icon-help icon" v-if="item.code=='APPLY_PAY_REJECT'"></i>
                    <i class="el-icon-document-checked" v-if="item.code=='APPLY_PAY_ PAYED'" style="font-size: 30px;"></i>
                    <div>{{item.aliasName}}</div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col style="padding:0 15px">
            <el-tabs v-model="entityForm.applyType" @tab-click="changetab">
              <el-tab-pane label="贸易往来付款" name="TRADE"></el-tab-pane>
              <el-tab-pane label="其他付款" name="OTHER"></el-tab-pane>
              <!-- <el-tab-pane label="客户退款" name="REFUND"></el-tab-pane> -->
            </el-tabs>
          </el-col>
          <template v-if="entityForm.applyType=='TRADE'">
            <el-col>
              <div class="form-title">基础信息</div>
              <div class="form-layout">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="日期" prop="applyDate">
                      <!-- :disabled="dialogStatus === 'change'?true:false" -->
                      <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="合同" prop="contractId">
                      <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                   filterable clearable placeholder="请选择合同" style="width:100%" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="卖方(收款单位)" prop="supplierId">
                      <el-select v-model="supplierEntity.value" placeholder="请选择" clearable filterable style="width:100%"
                                 @change="handleSupplier" disabled>
                        <el-option v-for="item in supplierEntity.options" :key="item.id" :label="item.name" :value="item.id" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="品名" prop="productName">
                      <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                   disabled />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="买方" prop="customerName">
                      <el-select v-model="firstpartyEntity.value" placeholder="请选择买方" clearable filterable style="width:100%"
                                 disabled>
                        <el-option v-for="item in firstpartyEntity.options" :key="item.name" :label="item.name"
                                   :value="item.name" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col></el-col>
                  <el-col></el-col>
                  <el-col></el-col>
                </el-row>
              </div>
              <div class="form-title">付款信息</div>
              <div class="form-layout">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="支付方式" prop="payWay" :rules="[{ required: true, message: '请选择一项' }]">
                      <dict-select v-model="entityForm.payWay" type="payWay_type" @change="setpayway" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="收款人" prop="payee">
                      <el-input v-model="entityForm.payee" @input="changepayee" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="开户银行" prop="bank" label-width="140px" v-if="entityForm.payWay!=='1'">
                      <el-input v-model="entityForm.bank" @input="changebank" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="账号或卡号" prop="cardNo" v-if="entityForm.payWay!=='1'">
                      <el-input v-model="entityForm.cardNo" @input="changecardNo" clearable
                                oninput="value=value.replace(/[^0-9.]/g,'')" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <!-- <el-row type="flex" :gutter="50" v-if="entityForm.payWay!=='1'">
                </el-row> -->

                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="行号" prop="bankNo" v-if="entityForm.payWay!=='1'">
                      <el-input v-model="entityForm.bankNo" @input="changebankNo" clearable
                                oninput="value=value.replace(/[^0-9.]/g,'')" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="单价" prop="price">
                      <el-input v-model="entityForm.price" @input="handleBlur(entityForm)" clearable
                                oninput="value=value.replace(/[^0-9.]/g,'')" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="付款金额" prop="money">
                      <el-input v-model="entityForm.money" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="吨数" prop="amount">
                      <el-input v-model="entityForm.amount" @input="handleBlur(entityForm)" :oninput="field.decimal" clearable>
                        <!-- <template slot="append">吨</template> -->
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <!-- <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="数量" prop="amount">
                      <el-input v-model="entityForm.amount" @input="handleBlur(entityForm)" :oninput="field.decimal" clearable>
                        <template slot="append">吨</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="付款金额" prop="money">
                      <el-input v-model="entityForm.money" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                clearable />
                    </el-form-item>
                  </el-col>
                </el-row> -->
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="付款途径" prop="paymentOptions">
                      <el-input type="textarea" v-model="entityForm.paymentOptions" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="备注" prop="remarks">
                      <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <!-- <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
                  <el-col>
                    <el-form-item label="步骤" prop="reviewLogList">
                      <el-steps :active="reviewLogList.length">
                        <el-step v-for="(item,index) in reviewLogList" :key="index"
                                 :title="item.reviewResult + ' ( 审批意见：'+item.reviewMessage+' )'"
                                 :description="item.createDate+' '+item.reviewUserName">
                        </el-step>
                      </el-steps>
                    </el-form-item>
                  </el-col>
                </el-row> -->
              </div>
            </el-col>
            <!-- <el-col>
              <div class="form-layout">
                <el-row>
                  <el-col>
                    <el-form-item label="附件上传">
                      <div class="upload">
                        <div class="upload-list">
                          <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                            <el-image class="img" :src="item.uri" fit="fill" :preview-src-list="srcList" />
                            <img src="@/assets/close.png" class="icon" @click="handleRemoveUpload(index)">
                          </div>
                          <upload-attachment ref="upload" class="upload-attachment" style="text-align: left;"
                                             url="/cwe/a/attachment/upload" :uploadData="uploadData" source="ContractBuy"
                                             :size="size" :limitNum="limitNum" accept=".jpg,.jpeg,.png" :isShowFile="false"
                                             @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                             listType="picture" />
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-col> -->

            <el-col>
              <div class="form-title">附件信息</div>
              <div class="form-layout">
                <el-row type="flex" :gutter="80">
                  <el-col>
                    <el-form-item label="">
                      <el-collapse v-model="collapse">
                        <el-collapse-item title="上传附件" name="1">
                          <div class="upload">
                            <div class="upload-list">
                              <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                                <i class="el-icon-close icon" @click="handleRemoveUpload(index)"></i>
                                <el-link class="up-load-warp-link" target="blank" :underline=" false"
                                         :href="item.uri">{{item.display}}</el-link>
                              </div>
                              <upload-attachment ref="upload" class="upload-attachment" :uploadData="uploadData"
                                                 source="coalSample" :size="size" :limitNum="limitNum"
                                                 accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg"
                                                 @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                                 listType="text" />
                            </div>
                          </div>

                        </el-collapse-item>
                      </el-collapse>

                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-col>

          </template>

          <!-- 其他支付 -->
          <template v-if="entityForm.applyType=='OTHER'">
            <el-col>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="applyDate">
                    <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="付款金额" prop="money">
                    <el-input v-model="entityForm.money" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="支付方式" prop="payWay" :rules="[{ required: true, message: '请选择一项' }]">
                    <dict-select v-model="entityForm.payWay" type="payWay_type" @change="setpayway" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="收款人" prop="payee">
                    <el-input v-model="entityForm.payee" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50" v-if="entityForm.payWay!=='1'">
                <el-col v-if="entityForm.payWay!='1'">
                  <el-form-item label="开户银行" prop="bank" label-width="140px">
                    <el-input v-model="entityForm.bank" @input="changebank" clearable />
                  </el-form-item>
                </el-col>
                <el-col v-if="entityForm.payWay!='1'">
                  <el-form-item label="账号或卡号" prop="cardNo">
                    <el-input v-model="entityForm.cardNo" @input="changecardNo" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="行号" prop="bankNo" v-if="entityForm.payWay!='1'">
                    <el-input v-model="entityForm.bankNo" @input="changebankNo" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="项目名称" prop="purpose">
                    <el-input v-model="entityForm.purpose" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks" label-width="140px">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </template>

          <!-- 客户退款 -->
          <!-- <template v-if="entityForm.applyType=='REFUND'">
            <el-col>
              <div class="form-title">基础信息</div>
              <div class="form-layout">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="日期" prop="applyDate">
                      <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="销售合同" prop="contractId">
                      <el-cascader v-model="contractEntityV.active" :options="contractEntityV.options"
                                   :props="contractEntityV.props" filterable clearable placeholder="请选择合同" style="width:100%" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="客户" prop="customerName">
                      <el-select v-model="customerEntity.active" placeholder="请选择" clearable @change="handleCustomer"
                                 style="width:100%" disabled>
                        <el-option v-for="item in customerEntity.options" :key="item.value" :label="item.label" :value="item">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="品名" prop="productName">
                      <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                   disabled />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <div class="form-title">付款信息</div>
              <div class="form-layout">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="支付方式" prop="payWay" :rules="[{ required: true, message: '请选择一项' }]">
                      <dict-select v-model="entityForm.payWay" type="payWay_type" @change="setpayway" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="收款人" prop="payee">
                      <el-input v-model="entityForm.payee" @input="changepayee" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="开户银行" prop="bank" label-width="140px" v-if="entityForm.payWay!='1'">
                      <el-input v-model="entityForm.bank" @input="changebank" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="账号或卡号" prop="cardNo" v-if="entityForm.payWay!='1'">
                      <el-input v-model="entityForm.cardNo" @input="changecardNo" clearable
                                oninput="value=value.replace(/[^0-9.]/g,'')" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="单价" prop="price">
                      <el-input v-model="entityForm.price" @input="handleBlur(entityForm)" clearable
                                oninput="value=value.replace(/[^0-9.]/g,'')" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="行号" prop="bankNo" v-if="entityForm.payWay!=='1' ">
                      <el-input v-model="entityForm.bankNo" @input="changebankNo" clearable
                                oninput="value=value.replace(/[^0-9.]/g,'')" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="数量" prop="amount">
                      <el-input v-model="entityForm.amount" @input="handleBlur(entityForm)" :oninput="field.decimal" clearable>
                        <template slot="append">吨</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="付款金额" prop="money">
                      <el-input v-model="entityForm.money" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="备注" prop="remarks">
                      <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-col>
            <el-col>
              <div class="form-layout">
                <el-row>
                  <el-col>
                    <el-form-item label="附件上传">
                      <div class="upload">
                        <div class="upload-list">
                          <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                            <el-image class="img" :src="item.uri" fit="fill" :preview-src-list="srcList" />
                            <img src="@/assets/close.png" class="icon" @click="handleRemoveUpload(index)">
                          </div>
                          <upload-attachment ref="upload" class="upload-attachment" style="text-align: left;"
                                             url="/cwe/a/attachment/upload" :uploadData="uploadData" source="ContractBuy"
                                             :size="size" :limitNum="limitNum" accept=".jpg,.jpeg,.png" :isShowFile="false"
                                             @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                             listType="picture" />
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </template> -->
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer" v-if="dialogStatus != 'watch'">
        <div v-if="dialogStatus != 'review'">
          <el-button type="primary" v-if="dialogStatus === 'change'" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="savebtn(entityForm)">
            保存
          </el-button>
          <el-button v-else type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="SubmitReview('entityForm')">
            提交审核
          </el-button>

          <el-button @click="SaveDraft('entityForm')" class="dialog-footer-btns">
            保存草稿
          </el-button>
        </div>

        <div v-else style="width:100%">
          <div v-if="perms[`${curd}:ismanagerreview`] || perms[`${curd}:isFinancereview`] || perms[`${curd}:isBossreview`]"
               style="display: flex;flex-direction: row;">
            <div style="width:calc(100% - 250px)" class="tsinput">
              <el-input style="width:100%; height: 29.5px;" type="text" v-model="entityForm.reviewMessage"
                        placeholder="请输入审批意见" />
            </div>
            <div style="width:250px">
              <el-button v-if="perms[`${curd}:update`]||false" type="primary" class="dialog-footer-btns"
                         :loading="entityFormLoading"
                         @click="passfn(entityForm.id,entityForm.reviewMessage,entityForm.state,entityForm.curFlowCode)">
                审核通过
              </el-button>
              <el-button v-if="perms[`${curd}:update`]||false"
                         @click="rejectfn(entityForm.id,entityForm.reviewMessage,entityForm.curFlowCode)"
                         class="dialog-footer-btns" style="width:100px">拒绝
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog width="980px" top="5vh" ref="dialogStatusV" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisibleV" :before-close="handleCloseV" :close-on-click-modal="false"
               :close-on-press-escape="false">

      <el-form :show-message="true" :status-icon="true" ref="entityFormV" :model="entityFormV" label-width="90px"
               label-position="top" v-if="dialogFormVisibleV">
        <el-row type="flex" :gutter="50">
          <el-col>
            <div class="form-title">付款信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="付款总额" prop="money">
                    <el-input v-model="entityFormV.money" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')" disabled
                              clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
            <el-row>
              <el-col style="margin-bottom: 20px;">
                <div class="form-titlV form-warp">
                  <span style="position: relative;">出纳付款及确认</span>
                  <!-- <el-button type="export-all" @click="handleAdd">新增</el-button> -->
                </div>
                <div class="form-layout">
                  <el-row type="flex" :gutter="50">
                    <el-col>
                      <el-table :data="listV" stripe :header-cell-style="headClass">
                        <el-table-column label="付款次数" align="center">
                          <template slot-scope="scope">
                            {{scope.row.times}}
                          </template>
                        </el-table-column>
                        <el-table-column label="本次付款金额" align="center">
                          <template slot-scope="scope">
                            <el-input v-model="scope.row.money" clearable @input="calculationmethod(scope.row,scope.$index)"
                                      :disabled="dialogStatus==='review'|| dialogStatus === 'change' || dialogStatus==='watch'" />
                          </template>
                        </el-table-column>
                        <el-table-column label="余额" align="center">
                          <template slot-scope="scope">
                            <el-input v-model="scope.row.moneyLeft" clearable disabled />
                          </template>
                        </el-table-column>

                        <el-table-column label="上传凭证截图" align="center">
                          <template slot-scope="scope">
                            <div class="upload">
                              <div class="upload-list">
                                <div class="up-load-warp" v-for="(item,index) in scope.row.attachmentList" :key="index">
                                  <el-image class="img" :src="item.uri" fit="fill" v-if="dialogStatus === 'lookPAYED'"
                                            :preview-src-list="scope.row.srcList" />
                                  <el-image class="img" :src="item.uri" fit="fill" v-if="dialogStatus === 'PaymentConfirmation'"
                                            @click="PictureCardPreview(item.uri)" />
                                  <img src="@/assets/close.png" class="icon" @click="handleRemoveUploadSC(index,scope.$index)">
                                </div>
                                <upload-attachment ref="upload" class="upload-attachment" style="text-align: left;"
                                                   url="/cwe/a/attachment/upload" :uploadData="uploadDataV" source="ContractBuy"
                                                   :limitNum="60" size="50" accept=".jpg,.jpeg,.png" :isShowFile="false"
                                                   @successNotify="handleUploadSuccessSC($event,scope.$index)"
                                                   @deleteNotify="handleUploadDeleteSC($event,scope.$index)" listType="picture" />
                              </div>
                            </div>
                          </template>
                        </el-table-column>
                        <!-- <el-table-column label="备注" align="center">
                          <template slot-scope="scope">
                            <el-input v-model="scope.row.remarks" clearable
                                      :disabled="dialogStatus==='review' || dialogStatus === 'change' || dialogStatus==='watch'" />
                          </template>
                        </el-table-column> -->
                        <el-table-column label="操作" width="80" align="center" v-if="dialogStatus === 'PaymentConfirmation'">
                          <template slot-scope="scope">
                            <el-button type="primary" @click="handleRemove(scope.row)"
                                       v-if="dialogStatus!= 'change'">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-col>
                  </el-row>
                  <el-row v-if="dialogStatus === 'PaymentConfirmation'">
                    <el-col>
                      <div style="margin-top:20px; text-align: center;">
                        <el-button type="primary" :loading="entityFormLoading" style="width:110px;" @click="handleAdd">
                          添加付款确认
                        </el-button>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>

            <!-- <el-row>
                <el-col>
                  <el-form-item label="附件上传">
                    <div class="upload">
                      <div class="upload-list">
                        <div class="up-load-warp" v-for="(item,index) in uploadListV" :key="index">
                          <el-image class="img" :src="item.uri" fit="fill" :preview-src-list="srcList" />
                          <img src="@/assets/close.png" class="icon" @click="handleRemoveUploadV(index)">
                        </div>
                        <upload-attachment ref="upload" class="upload-attachment" style="text-align: left;"
                                           url="/cwe/a/attachment/upload" :uploadData="uploadDataV" source="ContractBuy"
                                           :size="size" :limitNum="limitNum" accept=".jpg,.jpeg,.png" :isShowFile="false"
                                           @successNotify="handleUploadSuccessV" @deleteNotify="handleUploadDeleteV"
                                           listType="picture" />
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row> -->
          </el-col>

        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus === 'PaymentConfirmation'">
        <div>
          <el-button type="primary" :loading="entityFormLoading" class="dialog-footer-btns" style="width:110px"
                     @click="Paymentcompleted('entityFormV')">
            <!-- 添加付款确认 -->
            付款完成
          </el-button>
          <el-button type="primary" :loading="entityFormLoading" class="dialog-footer-btns" style="width:110px"
                     @click="rejectfn(entityFormV.id,entityFormV.reviewMessage,entityFormV.curFlowCode)">
            拒绝付款
          </el-button>
          <el-button type="primary" @click="confirmationSave('entityFormV')" :loading="entityFormLoading"
                     class="dialog-footer-btns" style="width:100px">
            保存
          </el-button>
          <el-button class="dialog-footer-btns" @click="handleCloseV">
            取消
          </el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog width="980px" :visible.sync="dialogVisibleupolad" title="图片">
      <el-image style="width:100%" :src="dialogImageUrl" />
    </el-dialog>

    <el-drawer title="附件列表" :visible.sync="drawer.visible" direction="rtl" :before-close="handleCloseDrawer">
      <template v-if="drawer.list.length">
        <div class="files-warp">
          <div class="file-item" v-for="(item,index) in drawer.list" :key="index">
            <!-- 图片预览 - 使用单独的预览功能 -->
            <template v-if="isImage(item.display)">
              <el-image class="file-img" :title="item.display" :src="item.uri" fit="cover" :preview-src-list="[item.uri]">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
              <div class="file-name">{{item.display}}</div>
            </template>
            <template v-else>
              <div class="file-info">
                <i :class="fileIcon(item.display)" style="font-size: 40px;"></i>
                <el-link type="primary" :href="item.uri" :download="item.display" target="_blank" :underline="false">
                  {{item.display}}
                </el-link>
              </div>
            </template>
          </div>
        </div>
      </template>
      <template v-else>
        <el-empty :image="emptyImgPath" :image-size="200" description="暂无图片" />
      </template>
    </el-drawer>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/finance/applyPay'
import { validatePhone } from '@/utils/validate'
import { detailsdailyreportOption } from '@/const'
import { contractList } from '@/api/quality'
import productModel from '@/model/product/productList'
import { chooseContract, getCustomerContract } from '@/api/quality'
import { createMemoryField } from '@/utils/index'
import CustomerModel from '@/model/user/customer'
import Cache from '@/utils/cache'
import { string } from 'jszip/lib/support'
const form = {
  attachmentList: [],
  srcList: []
}
export default {
  name: 'applyPay',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      dialogImageUrl: '',
      dialogVisibleupolad: false,
      curd: 'applyPay',
      model: Model,
      addressValue: '',
      previewImages: [], // 专门用于图片预览的数组
      entityFormV: {},
      dialogFormVisibleV: false,
      entityForm: { ...Model.model },
      noticeForm: {},
      nameEntity: { options: [], active: '' },
      supplierEntity: { value: '', options: [] },
      uploadList: [], // 用于展示
      uploadData: { refId: '', refType: 'ApplyPay' },
      limitNum: 50,
      username: 'ss',
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      filterOption: { ...detailsdailyreportOption },
      collapse: '1',
      // filterOption: {
      //   showMore: true,
      //   columns: [
      //     {
      //       prop: 'applyDate',
      //       component: 'date-select',
      //       filter: 'filter_EQS_applyDate',
      //       props: {
      //         placeholder: '日期',
      //         clearable: false,
      //       },
      //     },
      //     {
      //       prop: 'supplierName',
      //       filter: 'filter_LIKES_supplierName',
      //       props: {
      //         placeholder: '请输入发货单位',
      //         clearable: true,
      //       },
      //     },
      //     {
      //       prop: 'name',
      //       filter: 'filter_LIKES_productName',
      //       component: 'select-name',
      //       props: { placeholder: '请选择品名', clearable: true, selecttype: 'productName' },
      //     },

      //     {
      //       prop: 'payWay',
      //       filter: 'filter_EQS_payWay',
      //       component: 'dict-select',
      //       props: {
      //         type: 'payWay_type',
      //         placeholder: '请选择支付类型',
      //         clearable: true,
      //       },
      //     },
      //     {
      //       prop: 'applyType',
      //       filter: 'filter_EQS_applyType',
      //       component: 'dict-select',
      //       props: {
      //         type: 'apply_pay_type',
      //         placeholder: '请选择付款类型',
      //         clearable: true,
      //       },
      //     },
      //   ],
      // },

      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        phone: [{ validator: validateIsPhone, trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      rules: {
        applyDate: { required: true, message: '请输入日期', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' },
        supplierId: { required: true, message: '请输入收款单位', trigger: 'blur' },
        productName: { required: true, message: '请选择输入品名', trigger: 'blur' },
        payWay: { required: true, message: '请选择支付方式', trigger: 'blur' },
        // bank: { required: true, message: '请输入开户银行', trigger: 'blur' },
        // cardNo: { required: true, message: '请输入账号或卡号', trigger: 'blur' },
        payee: { required: true, message: '请输入收款人', trigger: 'blur' },
        money: { required: true, message: '请输入付款金额', trigger: 'blur' }
      },
      // 采购合同
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      // 销售合同
      contractEntityV: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      customerEntity: { options: [], active: [] },
      firstpartyEntity: { value: '', options: [] },
      isshow: false,
      drawer: {
        visible: false,
        list: [],
        preview: [],
        dateList: []
      },
      emptyImgPath: require(`@/assets/empty.jpg`),
      reviewLogList: [],
      countList: [],

      uploadListV: [],
      uploadDataV: { refId: '', refType: 'ApplyPayDetail' },
      ispayWay: '',
      isBilllist: [
        { label: '是', type: 'Y' },
        { label: '否', type: 'N' }
      ],
      listV: [{ ...form }],
      actionUrl: process.env.VUE_APP_CENTER,
      uploadHeaders: { Authorization: Cache.get('SYS_TOKEN') },
      imageUrl: '',

      steplist: [],
      steplistNEW: []
    }
  },
  watch: {
    'entityForm.isPublicBoolean': {
      handler(v) {
        this.entityForm.isPublic = v ? 'Y' : 'N'
      }
    },
    'entityForm.productName'(name) {
      if (name) {
        const item = this.nameEntity.options.find((item) => item.name === name)
        if (item) {
          this.nameEntity.active = item.name
          this.entityForm.productCode = item.code
          this.entityForm.productId = item.id
          this.entityForm.coalType = item.coalCategory || ''
          this.entityForm.coalCategory = item.type
        }
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
        this.entityForm.coalCategory = ''
      }
    },

    'supplierEntity.value'(id) {
      if (!id) return
      const item = this.supplierEntity.options.find((item) => item.id === id)
      if (item) {
        this.supplierEntity.active = item
        this.entityForm.payee = item.name
      } else {
        this.supplierEntity.active = []
      }
      // if (item.name) {
      //   this.entityForm.payee = item.name
      // }
    },

    'entityForm.contractId'(id) {
      if (!id) return
      console.log(this.entityForm.applyType)
      if (this.entityForm.applyType == 'TRADE') {
        if (
          this.dialogStatus === 'update' ||
          this.dialogStatus === 'review' ||
          this.dialogStatus === 'watch' ||
          this.dialogStatus === 'change'
        ) {
          const value = this.filterContractItem(id, 'contractEntity')
          this.contractEntity.active = value
          console.log(this.contractEntity.active)
        }
      }
      if (this.entityForm.applyType == 'REFUND') {
        if (
          this.dialogStatus === 'update' ||
          this.dialogStatus === 'review' ||
          this.dialogStatus === 'watch' ||
          this.dialogStatus === 'change'
        ) {
          const value = this.filterContractItemV(id, 'contractEntityV')
          this.contractEntityV.active = value
        }
      }
    },
    'contractEntity.active'(value) {
      // console.log(value)
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      // console.log('333333')
      // console.log(form)
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.id
      this.entityForm.contractName = form.secondParty
      this.supplierEntity.value = value[0]
      const item = this.contractEntity.options.find((v) => v.supplierId === value[0])
      if (!item) return
      const itemvv = item.contractBuyList.find((v) => v.id === value[1])
      if (!itemvv) return
      // console.log(itemvv)
      // let name = '',
      //   bank = '',
      //   bankNo = '',
      //   cardNo = ''
      // item.contractBuyList.forEach((element) => {
      //   name = element.productName
      //   bank = element.bank
      //   bankNo = element.bankNo
      //   cardNo = element.cardNo
      // })
      if (itemvv.bank) {
        this.entityForm.bank = itemvv.bank
      }
      if (itemvv.bankNo) {
        this.entityForm.bankNo = itemvv.bankNo
      }
      if (itemvv.cardNo) {
        this.entityForm.cardNo = itemvv.cardNo
      }
      this.entityForm.productName = itemvv.productName
      // this.entityForm.supplierId = itemvv.supplierId
      this.entityForm.supplierId = value[0]

      this.entityForm.supplierName = itemvv.secondParty

      this.firstpartyEntity.value = itemvv.firstParty
      this.entityForm.customerName = itemvv.firstParty
    },
    'contractEntityV.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItemV(value, 'contractEntityV')
      this.entityForm.contractCode = form.customerName
      this.entityForm.contractId = form.customerId
      this.entityForm.customerName = form.firstParty
      this.entityForm.supplierId = form.customerId
      this.entityForm.customerId = form.customerId

      this.entityForm.contractName = form.firstParty

      this.entityForm.supplierName = form.firstParty
      this.entityForm.productName = form.productName
      this.nameEntity.active = form.productName
      this.customerEntity.active = form.firstParty
      this.supplierEntity.value = form.firstParty
      this.entityForm.payee = form.firstParty
      this.entityForm = {
        ...this.entityForm,
        productName: form.productName,
        supplierName: form.firstParty,
        customerName: form.firstParty
      }
      // this.entityForm = { ...this.entityForm, productName: form.productName, customerName: form.firstParty }
    },

    // activetype: function (newV, oldV) {
    //   this.getlist(newV)
    //   this.getnumber()
    // },

    activetype: function (newV, oldV) {
      if (newV) {
        this.getlist(newV)
      } else {
        this.getlist('DRAFT,REJECT,NEW,PASS_FINANCE,PASS')
      }
      this.getnumber()
    },

    // 'actionList.1.active'(n, o) {
    // },
    'entityForm.applyType'(type) {
      this.entityForm.applyType = type
    }
  },
  created() {
    // this.permsActionLsit(this.curd)
    this.permsActionbtn(this.curd)
    this.setbuttomlistV(this.curd)
    this.getName()
    //获取供应商列表
    this.getContractList()
    //获取采购合同列表
    this.getContract()
    //获取销售合同列表
    this.getContractV()
    //获取客户列表
    this.getCustomer()
    // 获取买方
    this.getContractPartyfn()
    // this.getSellContractPartyfn()

    this.memoryEntity.fields = createMemoryField({
      fields: ['contractId', 'contractCode', 'supplierId', 'supplierName'],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })

    this.getnumber()
    if (this.$route.params.type) {
      if (this.$route.params.id) {
        this.handleUpdate(this.$route.params.id, 'review', 'APPLY_PAY')
      }
      this.activetype = this.$route.params.type
      if (this.$route.params.type == 'PASS') {
        this.dialogFormVisible = false
        let row = {
          id: this.$route.params.id
        }
        this.tobeCollected(row)
      }
    } else {
      this.activetype = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
      this.actionList.forEach((val, index) => {
        if (val.type == this.activetype) {
          val.active = true
        } else {
          val.active = false
        }
      })
    }
  },
  computed: {
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    }
  },

  methods: {
    // 判断是否是图片
    isImage(filename) {
      if (!filename) return false;
      const ext = filename.split('.').pop().toLowerCase()
      return ['jpg', 'jpeg', 'png', 'gif'].includes(ext)
    },
    fileIcon(filename) {
      if (!filename) return 'el-icon-document';
      const ext = filename.split('.').pop().toLowerCase()
      switch (ext) {
        case 'xls':
        case 'xlsx':
          return 'el-icon-document'
        case 'doc':
        case 'docx':
          return 'el-icon-document'
        case 'pdf':
          return 'el-icon-document'
        default:
          return 'el-icon-document'
      }
    },
    PictureCardPreview(file) {
      if (this.dialogStatus === 'PaymentConfirmation') {
        this.dialogImageUrl = file
        this.dialogVisibleupolad = true
      }
    },

    handleRemove(file) {
      console.log(file)
    },

    async savebtn(entityForm) {
      console.log(entityForm)
      // let data = {
      //   paymentOptions: entityForm.paymentOptions,
      //   id: entityForm.id
      // }
      const res = await this.model.save(entityForm)
      if (res) {
        this.$message({ type: 'success', message: '保存成功!' })
        this.entityForm = { ...this.model.model }
        this.getList()
        this.resetVariant()
        this.getnumber()
        this.dialogFormVisible = false
      }
    },

    async handleCancel(row) {
      this.$confirm('确定要作废吗，作废后不可恢复哦！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await this.model.Cancel({
            id: row.id
          })
          if (res) {
            this.$message({ type: 'success', message: '操作成功!' })
            this.getList()
            this.getnumber()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '操作取消' }))
    },
    async lookbtn(item) {
      const { data } = await this.model.findContractBuyFlow(item.id)
      // data.forEach((element, index) => {
      //   element.flowDesignList.forEach((value, indevc) => {
      //     value.active = false
      //     if (element.curFlowCode == value.code) {
      //       // value.active = true
      //       var citrus = element.flowDesignList.slice(0, indevc)
      //       citrus.forEach((value) => {
      //         value.active = true
      //       })
      //     }
      //   })
      // })
      data.forEach((element, index) => {
        element.flowDesignList.forEach((value, indevc) => {
          value.active = false
          if (element.curFlowCode == value.code) {
            var citrus = element.flowDesignList
            citrus.forEach((value) => {
              value.active = true
            })
          }
        })
      })
      this.steplistNEW = data
    },
    // 文件上传之前的钩子
    beforeUploadFile(file) {
      let FileExt = file.name.replace(/.+\./, '')
      if (this.source === 'ContractBuy') {
        if (['jpg', 'png', 'jpeg'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({ type: 'warning', message: '请上传后缀名为jpg、jpeg、png的图片！' })
          return false
        }
      } else if (this.source === 'coalSample') {
        if (['xls', 'xlsx', 'docx', 'jpg', 'png', 'jpeg', 'pdf'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({ type: 'warning', message: '请上传后缀名为xls、xlsx、docx、pdf、jpg、jpeg、png的文件！' })
          return false
        }
      }
    },

    handleAdd() {
      const form = { ...this.form }
      form.ext = Math.random().toFixed(6).slice(-6)
      // form.times =
      form.attachmentList = []
      this.listV.push(form)
      this.listV.forEach((element, index) => {
        element.times = index + 1
      })
    },
    handleRemove({ ext }) {
      const index = this.listV.findIndex((item) => item.ext === ext)
      if (index === -1) return false
      this.listV.splice(index, 1)
    },
    handleUploadSuccessSC(res, index) {
      // this.$set(this.listV[index].attachmentList, 0, res)

      this.listV[index].attachmentList.push(res)
    },

    handleUploadDeleteSC(status, indexv) {
      this.listV[indexv].attachmentList.splice(status.index, 1)
    },
    async handleRemoveUploadSC(index, indexv) {
      const { id } = this.listV[indexv].attachmentList[index]
      this.$refs.upload.attachmentDelete({ id, index })
      this.listV[indexv].attachmentList.splice(index, 1)
    },
    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },

    async selectChangeisSettle(row) {
      try {
        await this.model.getupdateIsSettle({ id: row.id, isSettle: row.isSettle })
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
        this.getList()
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },
    setpayway(val) {
      this.ispayWay = val
      // this.entityForm.payWay = val
      if (val === '1') {
        //支付方式是微信
        // this.entityForm.bank = ' '
        // this.entityForm.cardNo = ' '
        // this.entityForm.payee = ' '
        // this.entityForm.bankNo = ' '
        this.$forceUpdate()
      }
    },
    handleCloseV() {
      this.dialogFormVisibleV = false
    },

    async tobeCollected(row, type) {
      // console.log(type)
      const { data } = await this.model.getUploadList(row.id)
      this.listV = []
      this.uploadListV = []
      this.dialogFormVisibleV = true
      this.entityFormV = { ...data }

      if (type == 'lookPAYED') {
        this.dialogStatus = 'lookPAYED'
        const res = await this.model.findByApplyPayId({ applyPayId: row.id })
        if (res.data.length > 0) {
          for (var i = 0; i < res.data.length; i++) {
            res.data[i].srcList = []
            for (var j = 0; j < res.data[i].attachmentList.length; j++) {
              res.data[i].srcList.push(res.data[i].attachmentList[j].uri)
            }
          }
          this.listV = res.data
        }
      } else {
        this.dialogStatus = 'PaymentConfirmation'
        const res = await this.model.findByApplyPayId({ applyPayId: row.id })
        if (res.data.length > 0) {
          this.listV = res.data
        } else {
          this.handleAdd()
        }
      }

      // this.$confirm('是否确认支付', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // })
      //   .then(async () => {
      //     const res = await this.model.Collected({ id: row.id, reviewMessage: row.reviewMessage })
      //     if (res) {
      //       this.$message({ type: 'success', message: '支付成功!' })
      //       this.getList()
      //     }
      //   })
      //   .catch(() => this.$message({ type: 'info', message: '支付取消' }))
    },
    changetims(index) {
      return index + 1
    },
    async refusePay() {
      // const res = await this.model.Collected({ id: this.entityFormV.id })
    },
    async Paymentcompleted() {
      let total = 0
      this.listV.forEach((element, index) => {
        total += parseFloat(element.money)
      })
      if (total < this.entityFormV.money) {
        this.$confirm('付款金额小于实际金额，是否确认支付？', '确认信息', {
          distinguishCancelAndClose: true,
          confirmButtonText: '保存',
          cancelButtonText: '取消'
        })
          .then(() => {
            this.Collectedfn()
          })
          .catch((action) => { })
      } else {
        this.Collectedfn()
      }
    },
    async Collectedfn() {
      const res = await this.model.Collected({ id: this.entityFormV.id, applyPayDetailList: this.listV })
      if (res) {
        this.$message({ type: 'success', message: '支付成功!' })
        this.getList()
        this.handleCloseV()
      }
    },
    async confirmationSave() {
      const res = await this.model.payOnlySave({
        id: this.entityFormV.id,
        applyPayDetailList: this.listV
      })
      if (res) {
        this.handleCloseV()
        this.$message({ type: 'success', message: '保存成功!' })
        this.getList()
      }
    },
    calculationmethod(row, index) {
      let array = this.listV
      let that = this
      let balance = 0
      array.forEach((v, i) => {
        if (v.money) {
          balance += parseFloat(v.money)
          v.moneyLeft = parseFloat(that.entityFormV.money - balance).toFixed(2)
        } else {
          v.moneyLeft = ''
        }
      })
    },

    handleBlur(entityForm) {
      if (entityForm.amount != '' && entityForm.price != '') {
        this.money = parseInt(entityForm.amount * entityForm.price)
        let moneyv = parseInt(entityForm.amount * entityForm.price)
        if (isNaN(moneyv) == false) {
          this.entityForm = { ...this.entityForm, money: moneyv }
        }
      }
    },
    changetab(tab, event) {
      this.ispayWay = ''
      // this.entityForm.payWay = ''
      this.$nextTick(() => {
        // 请求成功之后，在这里执行
        this.entityForm = { ...Model.model }
        this.entityForm.applyType = tab.name
        this.supplierEntity.value = ''
        this.nameEntity.active = ''
        this.customerEntity.active = ''
        this.firstpartyEntity.value = ''
        this.contractEntity.active = ''
        this.contractEntityV.active = ''
        this.$forceUpdate()
      })
    },

    async getnumber() {
      const res = await this.model.getcountByState()
      this.countList = res.data
    },

    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      this.uploadList = [] // 清空下载列表
      this.reviewLogList = []
      this.entityForm.contractId = ''
      this.contractEntity.active = ''
      this.firstpartyEntity.value = ''
      this.steplist = []
      this.supplierEntity.value = ''
      this.isshow = false
      // this.PaymentApplicationVisible = false
      // this.noticeVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    changebankNo(e) {
      // this.entityForm.bankNo = e
      this.$set(this.entityForm, 'bankNo', e)
      this.$forceUpdate()
    },
    changepayee(e) {
      this.$set(this.entityForm, 'payee', e)
      // this.entityForm.payee = e
      this.$forceUpdate()
    },
    changecardNo(e) {
      this.entityForm = { ...this.entityForm, cardNo: e }
      // this.$set(this.entityForm, 'cardNo', e)
      this.$forceUpdate()
    },
    changebank(e) {
      // this.entityForm.bank = e
      this.$set(this.entityForm, 'bank', e)
      this.$forceUpdate()
    },
    //供应商列表
    async getContractList() {
      const { data } = await contractList()
      if (data.length) this.supplierEntity.options = data
    },

    //获取客户列表
    async getCustomer() {
      try {
        let options = []
        const { data } = await CustomerModel.page({ size: 1000 })
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        this.customerEntity.options = options
      } catch (e) { }
    },

    // 获取卖方
    async getContractPartyfn() {
      //获取买方列表
      const res = await this.model.getContractParty()
      if (res.data.length) this.firstpartyEntity.options = res.data
    },

    handleCustomer({ value, label }) {
      // this.entityForm.customerId = value
      // this.entityForm.customerName = label
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label, customerName: label, customerId: value }
    },
    // 合同改变同步
    handleSupplier({ value, label }) {
      // this.entityForm.supplierId = value
      // this.entityForm.supplierName = label

      // if (this.entityForm.applyType == 'REFUND') {
      // } else {
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label }
      // }
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) { }
    },
    // 获取采购合同选择接口
    async getContract() {
      try {
        const { data } = await chooseContract()
        this.contractEntity.options = this.formatContract({
          data,
          key: { supplierName: 'displayName', supplierId: 'id', contractBuyList: 'contractBuyList' }
        })
        // console.log(this.contractEntity.options)
        // console.log('获取的采购合同')
      } catch (error) { }
    },
    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    // 获取销售合同选择接口
    async getContractV() {
      const { data } = await getCustomerContract()
      // 供应商名称返回id用来查询
      this.contractEntityV.options = this.formatContractV({ data, key: { customerName: 'code', customerId: 'id' } })
    },

    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContractV({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },

    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },

    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     * @target {contract|contractEntityForm}<String>
     */
    filterContractItemV(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },

    async handleRemoveUpload(index) {
      const { id } = this.uploadList[index]
      this.$refs.upload.applyPayDelete({ id, index })
    },

    async SubmitReview(entityForm) {
      this.entityForm = { ...this.entityForm, payWay: this.ispayWay }
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.entityForm.payWay === '1') {
          //支付方式是微信
          this.entityForm.bank = ' '
          this.entityForm.cardNo = ' '
          this.entityForm.bankNo = ' '
          this.$forceUpdate()
        }
        const res = await this.model.getApproved(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '提交成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.resetVariant()
          this.getnumber()
          this.dialogFormVisible = false
        }
      }
    },

    //保存草稿
    async SaveDraft(entityForm) {
      this.entityForm = { ...this.entityForm, payWay: this.ispayWay }
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        // this.ispayWay
        if (this.entityForm.payWay === '1') {
          //支付方式是微信
          this.entityForm.bank = ' '
          this.entityForm.cardNo = ' '
          this.entityForm.bankNo = ' '
          this.$forceUpdate()
        }
        const res = await this.model.SaveDraftfn(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '保存草稿成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.getnumber()
          this.resetVariant()
        }
      }
    },

    getList() {
      this.$refs[this.curd].$emit('refresh')
    },
    handleUploadSuccess(res) {
      this.uploadList = [...this.uploadList, res]
      // this.entityForm.attachmentList = []
      // this.entityForm.attachmentList = this.uploadList
      // 上传完毕后更新展示的列表和把新增的附件id塞入到
      // this.entityForm.attachmentList.push({ id: res.id })

      // this.entityForm.attachmentList.push(res.uri)
      this.entityForm.attachmentList = this.uploadList

      // this.entityForm.attachmentList.push({ uri: res.uri })
    },

    handleUploadSuccessV(res) {
      this.uploadListV = [...this.uploadListV, res]
      this.entityFormV.attachmentList = this.uploadListV
    },
    handleUploadDeleteV({ status, index }) {
      this.uploadListV.splice(index, 1)
    },
    async handleRemoveUploadV(index) {
      const { id } = this.uploadListV[index]
      this.$refs.upload.applyPayDelete({ id, index })
    },

    handleUploadDelete({ status, index }) {
      this.uploadList.splice(index, 1)
    },

    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    async handleUpdate(item, type, gettype) {
      console.log(item)
      if (typeof item == 'object') {
        this.entityForm = { ...item }
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        } else if (type == 'update') {
          this.isshow = true
          this.dialogStatus = 'update'
        } else if (type == 'watch') {
          this.isshow = false
          this.dialogStatus = 'watch'
        } else if (type == 'change') {
          this.isshow = false
          this.dialogStatus = 'change'
        }
        this.dialogFormVisible = true
        // 上传所需要的配置
        this.uploadData.refId = item.id
        const { data } = await this.model.getUploadList(item.id)
        this.entityForm = { ...data }
        console.log(this.entityForm)

        if (gettype) {
          const { data } = await Model.getlclist({ type: gettype })
          this.entityForm.stepcode = this.entityForm.curFlowCode
          this.entityForm.stepName = this.entityForm.curFlowName

          // data.forEach((element, index) => {
          //   if (element.code == this.entityForm.stepcode) {
          //     var citrus = data.slice(0, index)
          //     citrus.forEach((value) => {
          //       value.active = true
          //     })
          //     // element.active = true
          //   }
          // })
          data.forEach((element, index) => {
            element.active = false
            if (element.code === this.entityForm.stepcode) {
              var citrus = data
              citrus.forEach((value) => {
                value.active = true
              })
            }
          })
          this.steplist = data
        }

        this.entityForm.payWay == this.ispayWay
        // this.supplierEntity.value = this.entityForm.supplierId
        this.reviewLogList = data.reviewLogList
        this.uploadList = data.attachmentList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((item, index) => {
            newarry.push(item.reviewMessage)
          })
          // this.entityForm.reviewMessage = newarry.toString()
          this.entityForm.reviewMessage = ''
        }
      } else if (typeof item == 'string') {
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        }
        this.dialogFormVisible = true
        // 上传所需要的配置
        this.uploadData.refId = item
        const { data } = await this.model.getUploadList(item)
        this.entityForm = { ...data }
        if (gettype) {
          const { data } = await Model.getlclist({ type: gettype })
          this.entityForm.stepcode = this.entityForm.curFlowCode
          this.entityForm.stepName = this.entityForm.curFlowName

          // data.forEach((element, index) => {
          //   if (element.code == this.entityForm.stepcode) {
          //     var citrus = data.slice(0, index)
          //     citrus.forEach((value) => {
          //       value.active = true
          //     })
          //     element.active = true
          //   }
          // })
          data.forEach((element, index) => {
            element.active = false
            if (element.code === this.entityForm.stepcode) {
              var citrus = data
              citrus.forEach((value) => {
                value.active = true
              })
            }
          })
          this.steplist = data
        }
        this.supplierEntity.value = this.entityForm.supplierId
        this.reviewLogList = data.reviewLogList
        this.uploadList = data.attachmentList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((itemv, index) => {
            newarry.push(itemv.reviewMessage)
          })
          this.entityForm.reviewMessage = newarry.toString()
        }
      }
    },
    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      if (res.data.attachmentList) {
        this.drawer.list = res.data.attachmentList
        this.previewImages = res.data.attachmentList.filter(item => this.isImage(item.display)).map(item => item.uri)
        // this.drawer.preview = res.data.attachmentList.map((item) => item.uri)
        this.drawer.visible = true
      }
    },
    //点击状态获取数据
    async getlist(newV) {
      this.activetype = newV
      // let indx = ''
      // let obj = {}
      this.actionList.forEach((val, index) => {
        if (val.type == newV) {
          // indx = index
          // obj = val
          val.active = true
          // this.$set(this.actionList, index, val)
          // this.replyTo(index)
        } else {
          val.active = false
        }
      })
      this.filtersSeach.filter_INS_state = newV
      this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    },

    // replyTo(index) {
    //   let obj = {}
    //   this.actionList.forEach((element, indexv) => {
    //     if (index == indexv) {
    //       obj = element
    //     }
    //   })
    //   this.$set(this.actionList, index, obj)
    //   // this.$set(this.actionList[index], 'active', true)
    //   // 最后一个参数为我们需要改变的值
    //   // this.$set(this.actionList, index, 'active', true)
    // },

    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    // // 更新累计Acc
    // async updateAcc() {
    //   const { date, contractId } = this.entityForm
    //   if (!date || !contractId) return false
    //   const paramsAcc = await this.getAccValues({ date, contractId })
    //   Object.keys(paramsAcc).forEach((key) => {
    //     this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
    //     this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
    //   })
    //   this.computeWayCostAcc()
    // },
    // computeWayCostAcc() {
    //   let { receiveWeightAcc, sendWeightAcc } = this.entityForm
    //   receiveWeightAcc = receiveWeightAcc * 1
    //   sendWeightAcc = sendWeightAcc * 1
    //   let wayCostAcc = (((sendWeightAcc - receiveWeightAcc) / sendWeightAcc) * 100).toFixed(2) * 1
    //   this.entityForm.wayCostAcc = !Number.isNaN(wayCostAcc) ? wayCostAcc : 0
    // },
    // async passfn(id, reviewMessage) {
    //   const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage })
    //   this.$message({ type: 'success', message: '审核通过成功!' })
    //   this.dialogFormVisible = false
    //   this.$refs[this.curd].$emit('refresh')
    // },

    async passfn(id, reviewMessage, state, curFlowCode) {
      const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage, curFlowCode: curFlowCode })
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
      this.resetVariant()
      this.getlist(this.activetype)
      this.getnumber()
    },

    async rejectfn(id, reviewMessage, curFlowCode) {
      const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage, curFlowCode: curFlowCode })
      this.getnumber()
      this.$message({ type: 'success', message: '拒绝成功!' })
      this.dialogFormVisible = false
      this.dialogFormVisibleV = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-collapse-item__arrow {
  display: none;
}

@import '@/styles/router-page.scss';
.colorBLUE {
  color: #2f79e8 !important;
}
.bordercolorBLUE {
  border: solid 1px #2f79e8 !important;
}
.bzminbox:first-child {
  width: 50% !important;
  .lin {
    width: 0px !important;
    display: none;
  }
  .context {
    width: 100% !important;
  }
}
.bzbox {
  width: 100%;
  display: flex;
  font-size: 12px;
  .bzminbox {
    width: 100%;
    display: flex;
    .icon {
      font-size: 30px;
    }
    .context {
      width: 50%;
      color: #ccc;
      text-align: center;
    }
    .lin {
      width: 50%;
      height: 1px;
      border: solid 1px #606266;
      margin-top: 15px;
    }
  }
}

.bzminboxV:first-child {
  width: 50% !important;
  .lin {
    width: 0px !important;
    display: none;
  }
  .context {
    width: 100% !important;
  }
}
.bzboxV {
  width: 100%;
  display: flex;
  font-size: 12px;
  .bzminboxV {
    width: 100%;
    display: flex;
    .icon {
      font-size: 30px;
    }
    .context {
      width: 50%;
      color: #ccc;
      text-align: center;
    }
    .lin {
      width: 50%;
      height: 1px;
      border: solid 1px #606266;
      margin-top: 15px;
    }
  }
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.form-titlV {
  font-size: 16px;
  position: relative;
  padding: 7px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    // top: 10px;
    bottom: 11px;
    background: #2f79e8;
    height: 16px;
  }
}

// .bigbox {
//   width: 100%;
//   padding: 15px 15px 0 15px;
//   background-color: #fff;
//   .btnbox {
//     width: 100%;
//     display: flex;
//     flex-direction: row;
//     align-items: center;
//   }
// }
// ::v-deep .el-form-item--mini.el-form-item,
// .el-form-item--small.el-form-item {
//   margin-bottom: -12px;
// }
.contentbox {
  width: 500px;
  margin: 100px auto 0 auto;
  font-size: 12px;
  .line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tsline {
    float: right;
    margin-top: 40px;
  }
  .inputbox {
    width: 100px;
    margin-bottom: 10px;
    border-bottom: solid 1px #ccc;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        // border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
<style scoped>
.tsinput >>> input {
  height: 37px !important;
}
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
.files-warp {
  display: flex;
  flex-wrap: wrap;
  padding-left: 8px;
}

.file-item {
  width: 120px;
  height: 140px;
  margin: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
  text-align: center;
}

.file-img {
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.file-info .el-link {
  margin-top: 10px;
  word-break: break-all;
}
/* .dialog-footer >>> .dialog-footer-btns {
  border: 1px solid #2f79e8;
  color: #2f79e8;
} */
</style>
<style scoped>
/* .el-button {
  border: 1px solid #ff9639 !important;
} */
/* .opt-btn {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
}
.opt-btnV {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
} */
</style>