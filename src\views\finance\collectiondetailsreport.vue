<template>
  <div class="app-container">
    <!-- <el-date-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate" placeholder="选择日期"
                    :clearable="false" /> -->
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" otherHeight="130">

      <el-table-column label="付款类型" slot="applyType" align="center">
        <template slot-scope="scope" v-if="scope.row.applyType">
          <el-tag :type="(scope.row.applyType=='TRADE' ? 'danger':(scope.row.applyType == 'OTHER' ? 'warning' : (scope.row.applyType == 'REFUND' ? 'success' : '')))"
                  size="mini">
            {{scope.row.applyType == 'TRADE' ? '贸易' : (scope.row.applyType == 'OTHER' ? '其他' :(scope.row.applyType == 'REFUND' ? '客户退款' : '')) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="收款方式" slot="payWay" align="center">
        <template slot-scope="scope" v-if="scope.row.payWay">
          <el-tag :type="(scope.row.payWay=='1' ? 'danger':(scope.row.payWay == '2' ? 'warning' : (scope.row.payWay == '3' ? 'success' : (scope.row.payWay == '4' ? 'danger' :(scope.row.payWay == '5' ? 'success' :'')))))"
                  size="mini">
            {{ scope.row.payWay == '1' ? '微信' : (scope.row.payWay == '2' ? '现金' : (scope.row.payWay == '3' ? '转账' : (scope.row.payWay == '4' ? '承兑':(scope.row.payWay == '5' ? '现汇':'')))) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="附件" slot="attachmentList" prop="attachmentList" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handlePreviewAttachment(scope.row)">查看附件</el-button>
        </template>
      </el-table-column>

      <!-- <el-table-column label="操作" slot="opt" max-width="150" width="150" fixed="right" align="center">
        <template slot-scope="scope">

          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <div>
              <el-tag class="opt-btn" color="#33CAD9" @click="handleUpdate(scope.row,'update')">
                编辑
              </el-tag>
            </div>
            <div>
              <el-tag effect="plain" style="color:#33CAD9;border-color: #33CAD9;cursor: pointer;" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column> -->

    </s-curd>

    <!-- <el-dialog width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="applyDate">
                    <date-select v-model="entityForm.applyDate" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="供应商(收款单位)" prop="supplierId">
                    <el-select v-model="supplierEntity.value" placeholder="请选择卖方" clearable filterable style="width:100%"
                               @change="handleSupplier">
                      <el-option v-for="item in supplierEntity.options" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>

                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="煤的类型" prop="coalCategory">
                    <dict-select v-model="entityForm.coalCategory" type="stock_type" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同" prop="contractId">
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 filterable :disabled="dialogStatus==='update'" clearable placeholder="请选择合同"
                                 style="width:100%" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="吨数" prop="amount">
                    <el-input v-model="entityForm.amount" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="单价" prop="price">
                    <el-input v-model="entityForm.price" clearable oninput="value=value.replace(/[^0-9.]/g,'')" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤种" prop="coalType">
                    <category-select :value.sync="entityForm.coalType" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="付款金额" prop="money">
                    <el-input v-model="entityForm.money" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="开户银行" prop="bank" label-width="140px">
                    <el-input v-model="entityForm.bank" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="支付方式" prop="payWay">
                    <el-input v-model="entityForm.payWay" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="账号或卡号" prop="cardNo">
                    <el-input v-model="entityForm.cardNo" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="payWay">
                    <el-input type="textarea" v-model="entityForm.reviewMessage" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
                <el-col>
                  <el-form-item label="步骤" prop="reviewLogList">
                    <el-steps :active="reviewLogList.length">
                      <el-step v-for="(item,index) in reviewLogList" :key="index" :title="item.reviewResult"
                               :description="item.createDate+' '+item.reviewUserName">
                      </el-step>
                    </el-steps>
                  </el-form-item>
                </el-col>
              </el-row>

            </div>
          </el-col>
          <el-col>
            <div class="form-layout">
              <el-row>
                <el-col>
                  <el-form-item label="附件上传">
                    <div class="upload">
                      <div class="upload-list">
                        <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                          <el-image class="img" :src="item.uri" fit="fill" :preview-src-list="srcList" />
                          <img src="@/assets/close.png" class="icon" @click="handleRemoveUpload(index)">
                        </div>
                        <upload-attachment ref="upload" class="upload-attachment" style="text-align: left;"
                                           url="/cwe/a/applyPay/upload" :uploadData="uploadData" source="ContractBuy" :size="size"
                                           :limitNum="limitNum" accept=".jpg,.jpeg,.png" :isShowFile="false"
                                           @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                           listType="picture" />
                      </div>
                    </div>

                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="isshow==false">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="SubmitReview(entityForm)">
          提交审核
        </el-button>
        <el-button @click="SaveDraft(entityForm)" class="dialog-footer-btns" style="width:100px">保存草稿
        </el-button>
      </div>
      <div slot="footer" class="dialog-footer" v-else>
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                   @click="passfn(entityForm.id,entityForm.reviewMessage)">
          审核通过
        </el-button>
        <el-button @click="rejectfn(entityForm.id,entityForm.reviewMessage)" class="dialog-footer-btns" style="width:100px">拒绝
        </el-button>
      </div>
    </el-dialog>
 -->

    <el-drawer title="附件列表" :visible.sync="drawer.visible" direction="rtl" :before-close="handleCloseDrawer">
      <template v-if="drawer.list.length">
        <div class="attachment-container">
          <div class="attachment-item" v-for="(item,index) in drawer.list" :key="index">
            <!-- 图片预览 -->
            <div v-if="item.isImage" class="image-preview">
              <el-image class="preview-image" :src="item.uri" fit="contain" :preview-src-list="drawer.preview" />
              <div class="file-name">{{ item.display }}</div>
            </div>

            <!-- 文档展示 -->
            <div v-else-if="item.isDocument" class="document-preview">
              <i class="el-icon-document" style="font-size: 48px; color: #409EFF;"></i>
              <el-link :href="item.uri" :download="item.display" target="_blank" :underline="false" class="file-info">
                <div class="file-name">{{ item.display }}</div>
                <div class="file-type">{{ item.fileType.toUpperCase() }} 文档</div>
              </el-link>
            </div>

            <!-- Excel展示 -->
            <div v-else-if="item.isExcel" class="excel-preview">
              <i class="el-icon-s-data" style="font-size: 48px; color: #67C23A;"></i>
              <el-link :href="item.uri" :download="item.display" target="_blank" :underline="false" class="file-info">
                <div class="file-name">{{ item.display }}</div>
                <div class="file-type">Excel 表格</div>
                <!-- <div class="file-meta">已处理</div> -->
              </el-link>
            </div>

            <!-- 其他文件类型 -->
            <div v-else class="other-file">
              <i class="el-icon-files" style="font-size: 48px;"></i>
              <el-link :href="item.uri" :download="item.display" target="_blank" :underline="false" class="file-info">
                <div class="file-name">{{ item.display }}</div>
                <div class="file-type">{{ item.fileType.toUpperCase() }} 文件</div>
              </el-link>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <el-empty :image="emptyImgPath" :image-size="200" description="暂无图片" />
      </template>
    </el-drawer>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/finance/collectiondetailsreport'
import { validatePhone } from '@/utils/validate'
import { paymentorderOption } from '@/const'
import { contractList } from '@/api/quality'
import productModel from '@/model/product/productList'
import { chooseContract } from '@/api/quality'
import { createMemoryField } from '@/utils/index'
import Cache from '@/utils/cache'
export default {
  name: 'collectiondetailsreport',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'applyRecv',
      model: Model,
      addressValue: '',
      entityForm: { ...Model.model },
      noticeForm: {},
      nameEntity: { options: [], active: '' },
      supplierEntity: { value: '', options: [] },
      uploadList: [], // 用于展示
      uploadData: { refId: '', refType: 'ContractSell' },
      limitNum: 50,
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      filterOption: { ...paymentorderOption },
      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        phone: [{ validator: validateIsPhone, trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      rules: {
        price: { required: true, message: '请输入金额', trigger: 'blur' },
        number: { required: true, message: '请输入数量', trigger: 'blur' },
        name: { required: true, message: '请输入名称', trigger: 'blur' },
        Paymentmethod: { required: true, message: '请选择输入方式', trigger: 'change' }
      },
      // 合同
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      isshow: false,
      drawer: {
        visible: false,
        list: [],
        preview: [],
        dateList: []
      },
      emptyImgPath: require(`@/assets/empty.jpg`),
      reviewLogList: []
      // activetype: 'PASS',
    }
  },
  watch: {
    // 'entityForm.isPublicBoolean': {
    //   handler(v) {
    //     this.entityForm.isPublic = v ? 'Y' : 'N'
    //   },
    // },
    // 'entityForm.productName'(name) {
    //   if (name) {
    //     const item = this.nameEntity.options.find((item) => item.name === name)
    //     this.nameEntity.active = item.name
    //     this.entityForm.productCode = item.code
    //     this.entityForm.productId = item.id
    //     this.entityForm.coalType = item.coalCategory || ''
    //     this.entityForm.coalCategory = item.type
    //   } else {
    //     this.nameEntity.active = ''
    //     this.entityForm.productCode = ''
    //     this.entityForm.productId = ''
    //     this.entityForm.coalType = ''
    //     this.entityForm.coalCategory = ''
    //   }
    // },
    // 'supplierEntity.value'(id) {
    //   console.log('发货商家id')
    //   console.log(id)
    //   if (!id) return
    //   const item = this.supplierEntity.options.find((item) => item.id === id)
    //   if (item) {
    //     this.supplierEntity.active = item
    //   } else {
    //     this.supplierEntity.active = []
    //   }
    //   this.entityForm.supplierId = id
    // },
    // 'entityForm.contractId'(id) {
    //   this.updateAcc()
    //   if (this.entityForm.applyDate && id) {
    //     this.updateAcc(this.entityForm.applyDate, id)
    //   }
    //   if (this.dialogStatus === 'update' || this.dialogStatus === 'review') {
    //     const value = this.filterContractItem(id, 'contractEntity')
    //     this.contractEntity.active = value
    //   }
    // },
    // 'contractEntity.active'(value) {
    //   if (!value || !Array.isArray(value)) return
    //   const form = this.filterContractItem(value, 'contractEntity')
    //   this.entityForm.contractCode = form.supplierName
    //   this.entityForm.contractId = form.supplierId
    //   this.entityForm.contractName = form.supplierName
    //   console.log(this.entityForm.contractId)
    //   const item = this.contractEntity.options.find((v) => v.supplierId === value[0])
    //   if (!item) return
    //   this.entityForm.supplierId = item.supplierId
    //   this.entityForm.supplierName = item.supplierName
    // },
    // activetype: function (newV, oldV) {
    //   this.getlist(newV)
    // },
  },
  created() {
    this.getName()
    //获取供应商列表
    this.getContractList()
    this.getContract()
    this.memoryEntity.fields = createMemoryField({
      fields: ['contractId', 'contractCode', 'supplierId', 'supplierName'],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
  },
  // computed: {
  //   srcList() {
  //     return this.uploadList.map((item) => {
  //       return item.uri
  //     })
  //   },
  //   size() {
  //     return this.uploadList.length
  //   },
  // },
  methods: {
    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      this.uploadList = [] // 清空下载列表
      this.reviewLogList = []
      this.contractEntity.active = []
      this.supplierEntity.value = ''
      // this.PaymentApplicationVisible = false
      // this.noticeVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    async getContractList() {
      const { data } = await contractList()
      if (data.length) this.supplierEntity.options = data
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) { }
    },
    // 获取合同选择接口
    async getContract() {
      try {
        const { data } = await chooseContract()
        this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'displayName', supplierId: 'id' } })
      } catch (error) { }
    },
    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    // async handleRemoveUpload(index) {
    //   const { id } = this.uploadList[index]
    //   this.$refs.upload.applyPayDelete({ id, index })
    // },
    // 合同改变同步
    // handleSupplier({ value, label }) {
    //   this.entityForm.supplierId = value
    //   this.entityForm.supplierName = label
    // },
    // handleUploadSuccess(res) {
    //   console.log(res)
    //   this.uploadList = [...this.uploadList, res]
    //   // console.log(this.uploadList)
    //   console.log(this.entityForm.attachmentList)
    //   // this.entityForm.attachmentList = this.uploadList
    //   // 上传完毕后更新展示的列表和把新增的附件id塞入到
    //   // this.entityForm.attachmentList.push({ id: res.id })
    //   this.entityForm.attachmentList.push(res)
    //   // this.entityForm.attachmentList.push({ uri: res.uri })

    //   console.log(this.entityForm.attachmentList)
    // },

    // handleUploadDelete({ status, index }) {
    //   this.uploadList.splice(index, 1)
    // },

    // handleNameEntity(val) {
    //   this.entityForm.productName = val
    // },
    // async handleUpdate(item, type) {
    //   this.entityForm = { ...item }
    //   if (type == 'review') {
    //     this.isshow = true
    //     this.dialogStatus = 'review'
    //   } else {
    //     this.isshow = false
    //     this.dialogStatus = 'update'
    //   }
    //   this.dialogFormVisible = true
    //   // 上传所需要的配置
    //   this.uploadData.refId = item.id
    //   const { data } = await this.model.getUploadList(item.id)
    //   this.entityForm = { ...data }
    //   this.supplierEntity.value = this.entityForm.supplierId
    //   this.reviewLogList = data.reviewLogList
    //   this.uploadList = data.attachmentList
    //   let newarry = []
    //   data.reviewLogList.forEach((item, index) => {
    //     newarry.push(item.reviewMessage)
    //   })
    //   this.entityForm.reviewMessage = newarry.toString()
    // },
    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      res.data.attachmentList.forEach((item, index) => {
        const fileType = item.uri.split('.').pop().toLowerCase()
        item.fileType = fileType
        // 添加文件类型判断
        item.isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileType)
        item.isDocument = ['doc', 'docx', 'pdf'].includes(fileType)
        item.isExcel = ['xls', 'xlsx'].includes(fileType)
      })
      if (res.data.attachmentList) {
        this.drawer.list = res.data.attachmentList
        this.drawer.preview = res.data.attachmentList
          .filter(item => item.isImage)
          .map(item => item.uri)
        this.drawer.visible = true
      }
    },
    //点击状态获取数据
    // async getlist(newV) {
    //   console.log(newV)
    //   this.filtersSeach.filter_INS_state = newV
    //   // detailsdailyreport
    //   const res = await this.model.page({ ...this.listQuery, size: 10000 }, true)
    //   // this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    // },

    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    // 更新累计Acc
    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      const paramsAcc = await this.getAccValues({ date, contractId })
      Object.keys(paramsAcc).forEach((key) => {
        this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
        this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      })
      this.computeWayCostAcc()
    }

    // async passfn(id, reviewMessage) {
    //   // console.log(id)
    //   // console.log(reviewMessage)
    //   const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage })
    //   this.dialogFormVisible = false
    //   this.$refs[this.curd].$emit('refresh')
    //   console.log(res)
    // },

    // async rejectfn(id, reviewMessage) {
    //   // console.log(id)
    //   // console.log(reviewMessage)
    //   const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage })
    //   this.dialogFormVisible = false
    //   this.$refs[this.curd].$emit('refresh')
    // },
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// .bigbox {
//   width: 100%;
//   padding: 15px 15px 0 15px;
//   background-color: #fff;
//   .btnbox {
//     width: 100%;
//     display: flex;
//     flex-direction: row;
//     align-items: center;
//   }
// }
.contentbox {
  width: 500px;
  margin: 100px auto 0 auto;
  font-size: 12px;
  .line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tsline {
    float: right;
    margin-top: 40px;
  }
  .inputbox {
    width: 100px;
    margin-bottom: 10px;
    border-bottom: solid 1px #ccc;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        // border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}

.attachment-container {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.attachment-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
  }
}

.image-preview {
  width: 100%;

  .preview-image {
    width: 100%;
    height: 150px;
  }

  .file-name {
    margin-top: 10px;
    font-size: 12px;
    color: #606266;
    word-break: break-all;
  }
}

.document-preview,
.excel-preview,
.other-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .file-info {
    margin-top: 10px;
    width: 100%;

    .file-name {
      font-size: 13px;
      color: #303133;
      word-break: break-all;
    }

    .file-type {
      font-size: 12px;
      color: #909399;
      margin-top: 5px;
    }

    .file-meta {
      font-size: 11px;
      color: #67c23a;
      margin-top: 5px;
      font-style: italic;
    }
  }
}

.excel-preview {
  .file-meta {
    background-color: #f0f9eb;
    padding: 2px 5px;
    border-radius: 3px;
  }
}
</style>
<style scoped>
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
/* .dialog-footer >>> .dialog-footer-btns {
  border: 1px solid #2f79e8;
  color: #2f79e8;
} */
</style>