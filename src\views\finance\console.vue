<template>
  <div class="app-container">
    <panel-bar type="panel" class="tstext" title="数据看板">
      <el-date-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate" placeholder="选择日期"
                      :clearable="false" />
    </panel-bar>

    <card height="inherit" style="height: 25.5rem">
      <card-item class="tstextv" title="日合计">
        <div class="blackbox">
          <div class="blackminbox">
            <div class="redtotall">
              <span v-if="fromfrom.dayProfit">{{fromfrom.dayProfit}}</span>
              <span v-else>0</span>
            </div>
            <div class="totalltext">利润合计</div>
          </div>

          <div class="blackminfootbox">
            <section class="reports">
              <div class="reports-item bdr">
                <div class="graytext">
                  收入合计
                </div>
                <div class="balcktext">
                  <span v-if="fromfrom.dayRecvSum">{{fromfrom.dayRecvSum}}</span>
                  <span v-else>0</span>
                </div>
              </div>
            </section>
            <section class="reports">
              <div class="reports-item ">
                <div class="graytext">
                  支出合计
                </div>
                <div class="balcktext">
                  <span v-if="fromfrom.dayPaySum">{{fromfrom.dayPaySum}}</span>
                  <span v-else>0</span>
                </div>
              </div>
            </section>
          </div>
        </div>
      </card-item>

      <card-item class="tstextv" title="月合计">
        <div class="blackbox">
          <div class="blackminbox">
            <div class="redtotall">
              <span v-if="fromfrom.monthProfit">{{fromfrom.monthProfit}}</span>
              <span v-else>0</span>
            </div>
            <div class="totalltext">利润合计</div>
          </div>

          <div class="blackminfootbox">
            <section class="reports">
              <div class="reports-item bdr">
                <div class="graytext">
                  收入合计
                </div>
                <div class="balcktext">
                  <span v-if="fromfrom.monthRecvSum">{{fromfrom.monthRecvSum}}</span>
                  <span v-else>0</span>
                </div>
              </div>
            </section>
            <section class="reports">
              <div class="reports-item ">
                <div class="graytext">
                  支出合计
                </div>
                <div class="balcktext">
                  <span v-if="fromfrom.monthPaySum">{{fromfrom.monthPaySum}}</span>
                  <span v-else>0</span>
                </div>
              </div>
            </section>
          </div>
        </div>
      </card-item>

      <card-item class="tstextv" title="年合计" style="margin-left: 10px;">
        <div class="blackbox">
          <div class="blackminbox">
            <div class="redtotall">
              <span v-if="fromfrom.yearProfit">{{fromfrom.yearProfit}}</span>
              <span v-else>0</span>
            </div>
            <div class="totalltext">利润合计</div>
          </div>
          <div class="blackminfootbox">
            <section class="reports">
              <div class="reports-item bdr">
                <div class="graytext">
                  收入合计
                </div>
                <div class="balcktext">
                  <span v-if="fromfrom.yearRecvSum">{{fromfrom.yearRecvSum}}</span>
                  <span v-else>0</span>
                </div>
              </div>
            </section>
            <section class="reports">
              <div class="reports-item ">
                <div class="graytext">
                  支出合计
                </div>
                <div class="balcktext">
                  <span v-if="fromfrom.yearPaySum">{{fromfrom.yearPaySum}}</span>
                  <span v-else>0</span>
                </div>
              </div>
            </section>
          </div>
        </div>
      </card-item>
    </card>

    <div class="inspection-sections">
      <card class="tscla" height="inherit" style="height: 20rem">
        <card-item class="tstextv" title="快捷功能">
          <section class="reports">
            <div class="reports-item">
              <div class="reports-item-section">
                <div class="reports-item-img" style="background:#33CAD9;" @click="handleToPage('payment','applyPay')">
                  <img src="@/assets/console/1.png" />
                </div>
                <div class="reports-item-desc" @click="handleToPage('payment','applyPay')">
                  <span>付款申请</span>
                </div>
              </div>
            </div>
          </section>

          <section class="reports">
            <div class="reports-item">
              <div class="reports-item-section">
                <div class="reports-item-img" style="background:#FF726B;" @click="handleToPage('settlement','settlePay')">
                  <img src="@/assets/console/2.png" />
                </div>
                <div class="reports-item-desc" @click="handleToPage('settlement','settlePay')">
                  <span>付款结算</span>
                </div>
              </div>
            </div>
          </section>

          <section class="reports">
            <div class="reports-item">
              <div class="reports-item-section">
                <div class="reports-item-img" style="background:#2F79E8;" @click="handleToPage('payment','applyRecv')">
                  <img src="@/assets/console/3.png" />
                </div>
                <div class="reports-item-desc" @click="handleToPage('payment','applyRecv')">
                  <span>收款登记</span>
                </div>
              </div>
            </div>
          </section>

          <section class="reports">
            <div class="reports-itemV">
              <div class="reports-item-section">
                <div class="reports-item-img" style="background:#F8A20F;" @click="handleToPage('settlement','settleRecv')">
                  <img src="@/assets/console/4.png" />
                </div>
                <div class="reports-item-desc" @click="handleToPage('settlement','settleRecv')">
                  <span>收款结算</span>
                </div>
              </div>
            </div>
          </section>

        </card-item>
      </card>
    </div>

    <card height="inherit" style="height:36vh">
      <!-- <card-item title="供应商榜单">
        <div class="item-echarts">
          <echarts width="inherit" height="inherit" v-if="isCustomer" :option="customer" />
          <el-empty v-else description="暂无数据" :image-size="100"></el-empty>
        </div>
      </card-item>
      <card-item title="客户榜单">
        <div class="item-echarts">
          <echarts width="inherit" height="inherit" v-if="isSupplier" :option="supplier" />
          <el-empty v-else description="暂无数据" :image-size="100"></el-empty>
        </div>
      </card-item> -->
    </card>

  </div>
</template>

<script>
import { supplierBuySum, customerSellSum, getPayRecvHomeSum } from '@/api/console'
import { PanelBar, Card, CardItem } from '@/components/Console'
const option = {
  animation: false,
  color: ['#FF726B'],
  tooltip: {
    trigger: 'axis',
    point: [-30, -30],
    backgroundColor: '#EDEEFF',
    padding: [5, 20, 5, 20],
    textStyle: {
      color: '#9f9ea5',
    },
  },
  grid: {
    top: '50',
    left: '50px',
    right: '20px',
    bottom: '50px',
  },
  // legend: {
  //   right: 20,
  //   itemGap: 20,
  //   padding: [10, 0, 10, 10],
  //   align: 'left',
  //   data: ['吨'],
  // },
  xAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      // show:false, // 是否显示坐标轴轴线
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF', // 坐标轴线的颜色修改--文字也同步修改
        type: 'dashed',
      },
    },
    axisTick: {
      // 坐标轴刻度相关设置
      // show:true, // 是否显示坐标轴刻度
      alignWithLabel: true, // 可以保证刻度线和标签对齐
    },
    axisLabel: {
      // 坐标轴刻度标签的相关设置。
      color: '#9f9ea5', // 刻度标签文字的颜色
      interval: 0,
      // formatter: function (value) {
      //   var ret = ''// 拼接加\n返回的类目项
      //   var maxLength = 4// 每项显示文字个数
      //   var valLength = value.length// X轴类目项的文字个数
      //   var rowN = Math.ceil(valLength / maxLength) // 类目项需要换行的行数
      //   if (rowN > 1) {
      //     for (var i = 0; i < rowN; i++) {
      //       var temp = ''// 每次截取的字符串
      //       var start = i * maxLength// 开始截取的位置
      //       var end = start + maxLength// 结束截取的位置
      //       // 这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
      //       temp = value.substring(start, end) + '\n'
      //       ret += temp // 凭借最终的字符串
      //     }
      //     return ret
      //   } else {
      //     return value
      //   }
      // }
    },
    data: ['鑫飞能源', '焦煤集团', '汇锦', '棋盘井', '双柳'],
  },
  yAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF',
        type: 'solid',
      },
    },
    axisLabel: {
      // 坐标轴刻度标签的相关设置
      color: '#9f9ea5',
    },
    splitLine: {
      // 坐标轴在 grid 区域中的分隔线。
      show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示。
      lineStyle: {
        color: '#E3F0FF', // 分隔线颜色，可以设置成单个颜色
      },
    },
  },
  series: [
    {
      name: '吨',
      type: 'bar',
      barWidth: 16,
      itemStyle: {
        normal: {
          barBorderRadius: [30, 30, 0, 0], // 配置样式
          color: () => '#2f79e8',
        },
      },
      data: [5, 20, 36, 10, 10, 20],
    },
  ],
}
// Object.freeze(option)
export default {
  name: 'baseConsole',
  components: { PanelBar, Card, CardItem },
  data() {
    return {
      loading: false,
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      customer: {},
      supplier: {},
      fromfrom: {},
    }
  },
  created() {
    this.getData(this.currentDate)
    this.getdataNumber(this.currentDate)
  },
  computed: {
    isCustomer() {
      if (Object.keys(this.customer).length) return true
      return false
    },
    isSupplier() {
      if (Object.keys(this.supplier).length) return true
      return false
    },
  },
  methods: {
    handleToPage(par, child = '') {
      if (child) {
        this.$store.dispatch('changeChildRoute', { parent: par, child })
        this.$router.push({ name: child, params: { isOpenAdd: true } })
      } else {
        this.$store.dispatch('changeChildRoute', { parent: par, child: '' })
        this.$router.push({ name: par, params: { isOpenAdd: true } })
      }
    },

    async getData(date) {
      try {
        const res = await Promise.all([customerSellSum(date), supplierBuySum(date)])
        res[0].data.sort((a, b) => b.totalWeight - a.totalWeight)
        res[1].data.sort((a, b) => b.totalWeight - a.totalWeight)
        this.customer = await this.formatOption(res[0].data)
        this.supplier = await this.formatOption(res[1].data)
      } catch (error) {}
    },

    async getdataNumber(date) {
      try {
        const res = await Promise.all([getPayRecvHomeSum(date)])
        this.fromfrom = res[0].data
      } catch (error) {}
    },

    formatOption(data) {
      let options = JSON.parse(JSON.stringify(option))
      if (data.length) {
        let nameList = []
        let totalList = []
        data.forEach((item) => {
          totalList.push(item.totalWeight)
          let name = this.spliceName(item.customerName || item.supplierName)
          nameList.push(name)
        })
        if (data[0].customerName) {
          options.color = ['#33CAD9']
        }
        options.xAxis.data = nameList
        options.series[0].data = totalList
      } else {
        options = ''
      }
      return options
    },

    spliceName(val, max = 6) {
      let valLength = val.length
      let ret = ''
      let rowN = Math.ceil(valLength / max) // 类目项需要换行的行数
      if (rowN > 1) {
        for (let i = 0; i < rowN; i++) {
          let temp = '' // 每次截取的字符串
          let start = i * max // 开始截取的位置
          let end = start + max // 结束截取的位置
          // 这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
          temp = val.substring(start, end) + '\n'
          ret += temp // 凭借最终的字符串
        }
        return ret
      } else {
        return val
      }
    },
  },
  watch: {
    /**
     * 改变日期重新请求数据
     */
    currentDate(v) {
      this.getData(v)
    },
  },
}
</script>
<style lang="scss" scoped>
.blackbox {
  width: 100%;
  .blackminbox {
    width: inherit;
    height: inherit;
    margin-top: 2rem;
    .redtotall {
      font-size: 3rem;
      // color: red;
      color: #ff726b;
      text-align: center;
      padding-bottom: 10px;
    }
    .totalltext {
      color: #323232;
      font-size: 1.8rem;
      text-align: center;
    }
  }
  .blackminfootbox {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    .reports {
      width: inherit;
      height: inherit;
      display: flex;
      .reports-item {
        width: inherit;
        height: inherit;
        display: flex;
        flex-flow: column;
        justify-content: space-evenly;
        position: relative;
        text-align: center;
        padding: 2rem;
        margin-top: 1rem;
        .graytext {
          color: #acacac;
          font-size: 1.5rem;
          padding-bottom: 10px;
        }
        .balcktext {
          color: #4d4d4d;
          font-size: 2.5rem;
        }
      }
    }
    .bdr:first-of-type:after {
      content: '';
      position: absolute;
      opacity: 1;
      right: 0;
      height: 30px;
      width: 2px;
      background: #efefef;
    }
  }
}

::v-deep .card-item {
  padding: 5px;
}
::v-deep .item-echarts {
  width: 98.5%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  height: inherit;
}

.tscla {
  .reports {
    width: inherit;
    height: inherit;
    display: flex;
    &-itemV {
      width: inherit;
      height: inherit;
      display: flex;
      flex-flow: column;
      justify-content: space-evenly;
      position: relative;
      &-section {
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0.9;
        transition: all 0.5s;

        .reports-item-img {
          position: relative;
          width: 6rem;
          height: 6rem;
          border-radius: 50%;
          margin-right: 2rem;
          cursor: pointer;

          img {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 50%;
            height: 50%;
          }
        }

        .reports-item-desc {
          display: flex;
          justify-content: center;
          flex-flow: column nowrap;
          cursor: pointer;
          // width: 200px;
          span:first-of-type {
            font-size: 1.5rem;
            color: #424242;
            // margin-bottom: 10px;
          }

          span:last-of-type {
            font-size: 1rem;
            // color: #a8a8a8;
            color: #424242;
          }
        }
      }

      &-section:hover {
        opacity: 1;
      }
    }
    &-item {
      width: inherit;
      height: inherit;
      display: flex;
      flex-flow: column;
      justify-content: space-evenly;
      position: relative;
      &-section {
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0.9;
        transition: all 0.5s;

        .reports-item-img {
          position: relative;
          width: 6rem;
          height: 6rem;
          border-radius: 50%;
          margin-right: 2rem;
          cursor: pointer;

          img {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 50%;
            height: 50%;
          }
        }

        .reports-item-desc {
          display: flex;
          justify-content: center;
          flex-flow: column nowrap;
          cursor: pointer;
          // width: 200px;
          span:first-of-type {
            font-size: 1.5rem;
            color: #424242;
            // margin-bottom: 10px;
          }

          span:last-of-type {
            font-size: 1.5rem;
            // color: #a8a8a8;
            color: #424242;
          }
        }
      }

      &-section:hover {
        opacity: 1;
      }
    }
    &:first-of-type {
      flex: 0 0 28%;
    }
    &-item:first-of-type:after {
      content: '';
      position: absolute;
      opacity: 1;
      right: 0;
      height: 30px;
      width: 2px;
      background: #efefef;
    }
  }
}
</style>

<style scoped>
.tstext >>> .panel-title span:first-of-type {
  font-size: 17px !important;
}
.tstextv >>> .card-item-title span {
  font-size: 16px !important;
}
</style>