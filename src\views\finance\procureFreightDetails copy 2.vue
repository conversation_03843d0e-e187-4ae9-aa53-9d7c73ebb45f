<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" @titleChanged="getenterpriseName"
                  :showAdd="perms[`${curd}:save`]||false" :useColDeep="useColDeep" :showRefresh="perms[`${curd}:Refresh`]||false"
                  @Refresh="Refreshfn" />
    <!-- <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" :changeactions="changeactions"
            @selectItem="selectItem" :defaultsort="{ prop:'firstWeightDate', order: 'descending'}" otherHeight="120" issync=""> -->

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :showSummary='true' :changeactions="changeactions"
            :actions="actions" @selectItem="selectItem" otherHeight="170">

      <el-table-column label="类型" slot="type" width="100" prop="type" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.type==1">销售</span>
          <span v-if="scope.row.type==2">采购</span>
        </template>
      </el-table-column>

      <el-table-column prop="firstWeight" label="毛重" slot="firstWeight" width="100" align="center" scope-slot>
        <template slot-scope="scope">
          <div>
            <span v-if="!scope.row.active.firstWeight" @click="handleActive(scope.row,'firstWeight')">
              <i class="el-icon-edit"></i> {{scope.row.firstWeight}}</span>
            <el-input v-else placeholder="请输入内容" v-model="scope.row.firstWeight" clearable
                      oninput="value=value.replace(/[^0-9.]/g,'')" size="small" />
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="secondWeight" label="皮重" slot="secondWeight" width="100" align="center" scope-slot>
        <template slot-scope="scope">
          <div>
            <span v-if="!scope.row.active.secondWeight" @click="handleActive(scope.row,'secondWeight')">
              <i class="el-icon-edit"></i> {{scope.row.secondWeight}}</span>
            <el-input v-else placeholder="请输入内容" v-model="scope.row.secondWeight" clearable
                      oninput="value=value.replace(/[^0-9.]/g,'')" size="small" />
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="originalTon" label="原发数" slot="originalTon" width="100" align="center" scope-slot>
        <template slot-scope="scope">
          <div>
            <span v-if="!scope.row.active.originalTon" @click="handleActive(scope.row,'originalTon')">
              <i class="el-icon-edit"></i> {{scope.row.originalTon}}</span>
            <el-input v-else placeholder="请输入内容" v-model="scope.row.originalTon" clearable
                      @input="setmoney1(scope.row,'originalTon')" @blur="handleBlur(scope.row,'originalTon')"
                      oninput="value=value.replace(/[^0-9.]/g,'')" size="small" />
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="weight" label="实收" slot="weight" width="100" align="center" scope-slot>
        <template slot-scope="scope">
          <div>
            <span v-if="!scope.row.active.weight" @click="handleActive(scope.row,'weight')">
              <i class="el-icon-edit"></i> {{scope.row.weight}}</span>
            <el-input v-else placeholder="请输入内容" v-model="scope.row.weight" clearable @blur="handleBlur(scope.row,'weight')"
                      @input="setmoney1(scope.row,'weight')" oninput="value=value.replace(/[^0-9.]/g,'')" size="small" />
          </div>
        </template>
      </el-table-column>

      <!-- issync="" -->
      <el-table-column label="一次计量时间" slot="firstWeightDate" width="180" prop="firstWeightDate" sortable align="center">
        <template slot-scope="scope">
          {{scope.row.firstWeightDate}}
        </template>
      </el-table-column>

      <el-table-column label="二次计量时间" slot="secondWeightDate" width="180" prop="secondWeightDate" align="center">
        <template slot-scope="scope">
          {{scope.row.secondWeightDate}}
        </template>
      </el-table-column>

      <el-table-column label="磅单号" slot="autoBH" prop="autoBH" align="center" width="200">
        <template slot-scope="scope">
          <el-tooltip popper-class="popper" :content="scope.row.autoBH" placement="top" effect="light">
            <span>{{scope.row.autoBH}}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column label="企业名称" slot="companyFullName" prop="companyFullName" align="center" width="200">
        <template slot-scope="scope">
          <el-tooltip popper-class="popper" :content="scope.row.companyFullName" placement="top" effect="light">
            <span>{{scope.row.companyFullName}}</span>
          </el-tooltip>
        </template>
      </el-table-column> -->

      <el-table-column prop="freightTon" label="运费吨数" slot="freightTon" width="160" align="center" scope-slot>
        <template slot-scope="scope">
          <span v-if="!scope.row.active.freightTon" @click="handleActive(scope.row,'freightTon')">
            <i class="el-icon-edit"></i> {{scope.row.freightTon}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.freightTon" clearable
                    oninput="value=value.replace(/[^0-9.]/g,'')" size="small" />
        </template>
      </el-table-column>

      <el-table-column prop="lossNullBase" label="亏吨基准" slot="lossNullBase" width="160" align="center" scope-slot>
        <template slot-scope="scope">
          <span v-if="!scope.row.active.lossNullBase" @click="handleActive(scope.row,'lossNullBase')">
            <i class="el-icon-edit"></i> {{scope.row.lossNullBase}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.lossNullBase" clearable
                    oninput="value=value.replace(/[^0-9.]/g,'')" size="small" />
        </template>
      </el-table-column>

      <el-table-column prop="freightPrice" label="运价" slot="freightPrice" width="160" align="center" scope-slot>
        <template slot-scope="scope">
          <span v-if="!scope.row.active.freightPrice" @click="handleActive(scope.row,'freightPrice')">
            <i class="el-icon-edit"></i> {{scope.row.freightPrice}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.freightPrice" clearable
                    @input="setmoney1(scope.row,'freightPrice')" oninput="value=value.replace(/[^0-9.]/g,'')" size="small"
                    @blur="handleBlur(scope.row,'freightPrice')" />
        </template>
      </el-table-column>

      <el-table-column prop="deductMoney" slot="deductMoney" label="扣款" width="100" align="center" scope-slot>
        <!-- handleBlurkk -->
        <template slot-scope="scope">
          <span v-if="!scope.row.active.deductMoney" @click="handleActive(scope.row,'deductMoney')">
            <i class="el-icon-edit"></i> {{scope.row.deductMoney}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.deductMoney" clearable
                    @input="setmoney2(scope.row,'freighMoney')" oninput="value=value.replace(/[^0-9.]/g,'')" size="small" />
        </template>
      </el-table-column>

      <el-table-column prop="freighMoney" slot="freighMoney" label="应结金额" width="100" align="center" scope-slot>
        <template slot-scope="scope">
          <span v-if="!scope.row.active.freighMoney" @click="handleActive(scope.row,'freighMoney')">
            <i class="el-icon-edit"></i> {{scope.row.freighMoney}}</span>
          <!-- @blur="handleBlur(scope.row,'freighMoney')" 
            @input="setmoney1(scope.row,'freighMoney')" -->
          <el-input v-else placeholder="请输入内容" v-model="scope.row.freighMoney" clearable
                    oninput="value=value.replace(/[^0-9.]/g,'')" size="small" />
        </template>
      </el-table-column>

      <el-table-column prop="settleMoney" slot="settleMoney" label="结算金额" width="100" align="center" scope-slot>
        <template slot-scope="scope">
          <span v-if="!scope.row.active.settleMoney" @click="handleActive(scope.row,'settleMoney')">
            <i class="el-icon-edit"></i> {{scope.row.settleMoney}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.settleMoney" clearable size="small"
                    @input="setmoney4(scope.row,'settleMoney')" />
        </template>
      </el-table-column>

      <el-table-column label="是否有票" slot="isBill" prop="isBill" align="center" scope-slot>
        <template slot-scope="scope">
          <el-select v-model="scope.row.isBill" clearable placeholder="请选择" @change="selectChangeisBill(scope.row)">
            <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column label="是否结算" slot="isSettle" prop="isSettle" align="center" scope-slot>
        <template slot-scope="scope">
          <!-- <el-tag v-if="scope.row.isSettle=='Y'" class="opt-btn" color="#FF9639" @click="selectChangeisSettle(scope.row,'N')">已结
          </el-tag>
          <el-tag v-else class="opt-btn" color="red" @click="selectChangeisSettle(scope.row,'Y')">未结
          </el-tag> -->
          <el-select v-model="scope.row.isSettle" clearable placeholder="请选择" @change="selectChangeisSettle(scope.row)" disabled>
            <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column label="备注" slot="remarks" prop="remarks" align="center" scope-slot width="160">
        <template slot-scope="scope">
          <span v-if="!scope.row.active.remarks" @click="handleActive(scope.row,'remarks')">
            <i class="el-icon-edit"></i> {{scope.row.remarks}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.remarks" clearable @input="setmoney3(scope.row,'remarks')"
                    size="small" @blur="handleBlur(scope.row,'remarks')" />
        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <template v-if="!scope.row._all">
            <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)">编辑
            </el-tag>
          </template>
          <!-- v-if="perms[`${curd}:update`]||false" -->
          <!-- <template v-else>
            <el-tag class="opt-btn" color="#33CAD9" @click="handleSave(scope.row)">保存
            </el-tag>
          </template> -->
          <template v-else>
            <el-tag class="opt-btn" v-if="perms[`${curd}:update`]||false" color="#33CAD9" @click="handleSave(scope.row)">保存
            </el-tag>
            <el-tag class="opt-btn" color="#FF726B" @click="handleCancel(scope.row)">取消
            </el-tag>
          </template>

        </template>
        <!-- <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" v-if="perms[`${curd}:update`]||false" @click="handleUpdate(scope.row)">编辑
          </el-tag>
          <el-tag class="opt-btn" color="#595EC9" v-if="perms[`${curd}:delete`]||false" @click="handleDel(scope.row)">删除</el-tag>
        </template> -->
      </el-table-column>

    </s-curd>

    <el-dialog width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisibleV" :before-close="handleCloseV" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="changeForm" :model="entityFormV" label-position="top">
        <el-row type="flex" :gutter="50">
          <el-col>
            <el-form-item label="日期" prop="date">
              <date-select v-model="entityFormV.date" clearable></date-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="类型" prop="type">
              <el-select v-model="devType" placeholder="请选择类型" @change="selectChanged">
                <el-option v-for="item in devTypes" :key="item" :label="item" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <div style="margin-top:20px">
              <el-button type="primary" class="dialog-footer-btns" :loading="changeeFormLoading"
                         v-if="perms[`${curd}:Refresh`]||false" @click="RefreshForm">刷新
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>

    <el-dialog class="tsdialog" ref="dialogChange" top="20vh" :title="title" :visible.sync="ChangedialogFormVisible"
               :before-close="ChangehandleClose" :close-on-click-modal="false" :close-on-press-escape="false" width="500px">
      <el-form :show-message="true" :status-icon="true" ref="changeForm" :model="entityForm" label-position="top"
               v-if="ChangedialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="运费" prop="freightPrice">
                    <el-input v-model="changeForm.freightPrice" />
                  </el-form-item>
                </el-col>
                <!-- <el-col>
                  <el-form-item label="是否结算" prop="isSettle">
                    <el-select v-model="changeForm.isSettle" clearable placeholder="请选择" @change="batchChangeisSettle">
                      <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="扣款" prop="deductMoney">
                    <el-input v-model="changeForm.deductMoney" />
                  </el-form-item>
                </el-col> -->
              </el-row>
              <!-- <el-row type="flex" :gutter="50">
                <el-col style="width:100%">
                  <el-form-item label="备注" prop="remarks">
                    <el-input v-model="changeForm.remarks" type="textarea" :rows="6" placeholder="请填写备注信息~"></el-input>
                  </el-form-item>
                </el-col>
              </el-row> -->

            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="changeentityFormLoading"
                   v-if="perms[`${curd}:update`]||false" @click="saveChangeEntityForm('changeForm')">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

    <export-excel v-if="excelConfig.dialog" :exportExcelDialogVisible.sync="excelConfig.dialog"
                  :traitSettings="excelConfig.excel.traitSettings" :exportLabelMap="excelConfig.excel.exportHeaderMap"
                  :entityForm="model.tableOption">
      <el-button type="primary" :loading="scope.uploading.state" @click="handleUpload(scope)" slot="handler" slot-scope="scope"
                 :disabled="!scope.settings.data.length">上传</el-button>
    </export-excel>
  </div>
</template>

<script>
import { chooseContract } from '@/api/quality'
import supplierModel from '@/model/user/supplier'
import productModel from '@/model/product/productList'
import Mixins from '@/utils/mixins'
import Model from '@/model/finance/procureFreightDetails'
import { createMemoryField } from '@/utils/index'
// import { weightHouselogtableOption } from '@/const'
import { Decimal } from 'decimal.js'
export default {
  name: 'procureFreightDetails',
  mixins: [Mixins],
  data() {
    return {
      curd: 'procureFreightDetails',
      model: Model,
      //    filterOption: { ...weightHouselogtableOption },
      devType: '购进',
      devTypes: ['购进', '销售'],
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'startSecondWeightDate',
            component: 'date-select',
            filter: 'filter_GES_secondWeightDate',
            title: '开始日期',
            width: '180px',
            props: {
              type: 'datetime',
              formats: 'yyyy-MM-dd HH:mm:ss',
              format: 'yyyy-MM-dd HH:mm:ss',
              placeholder: '二次计量开始时间',
              defaultTime: '00:00:00',
              clearable: false
            }
          },
          {
            prop: 'endSecondWeightDate',
            component: 'date-select',
            filter: 'filter_LES_secondWeightDate',
            title: '结束日期',
            width: 180,
            props: {
              type: 'datetime',
              formats: 'yyyy-MM-dd HH:mm:ss',
              format: 'yyyy-MM-dd HH:mm:ss',
              placeholder: '二次计量结束时间',
              defaultTime: '23:59:59',
              clearable: false
            }
          },
          {
            prop: 'cargoType',
            filter: 'filter_LIKES_cargoType',
            component: 'select-cargotype',
            props: { placeholder: '请选择品名', changeFilter: true, clearable: true, type: 'cargoType' }
          },
          {
            prop: 'plateNumber',
            filter: 'filter_LIKES_plateNumber',
            props: {
              placeholder: '请输入车牌号',
              clearable: true
            }
          },
          {
            prop: 'companyFullName',
            filter: 'filter_LIKES_companyFullName',
            component: 'select-enterprisename',
            props: {
              placeholder: '企业名称',
              clearable: true,
              isnodata: true,
              changeFilter: false,
              type: '2'
            }
          },
          {
            prop: 'customerName',
            filter: 'filter_LIKES_customerName',
            component: 'select-customername',
            props: {
              placeholder: '收货单位',
              clearable: true,
              isnodata: true,
              changeFilter: false
            }
          },

          {
            prop: 'supplierName',
            filter: 'filter_LIKES_supplierName',
            // component: 'select-name',
            component: 'select-suppliername',
            props: {
              placeholder: '供应商/发货单位',
              clearable: true,
              isnodata: true,
              changeFilter: false
            }
          },
          {
            prop: 'isSettle',
            filter: 'filter_EQS_isSettle',
            component: 'dict-select',
            props: {
              name: 'select',
              type: 'sync_type',
              placeholder: '请选择结算状态',
              clearable: true
            }
          },
          // {
          //   prop: 'type',
          //   filter: 'filter_LIKES_type',
          //   component: 'select-type',
          //   props: { placeholder: '请选择类型', changeFilter: false, istype: '1' }
          // },
          {
            prop: 'lossNull',
            filter: 'filter_LIKES_lossNull',
            component: 'select-LossNullList',
            props: {
              placeholder: '亏吨',
              clearable: true,
              isShow: false,
              serchCompanyFullName: ''
            }
          }
        ]
      },

      //   filterOption: Model.filterOption([
      // {
      //   prop: 'senderName',
      //   filter: 'filter_LIKES_senderName',
      //   props: { placeholder: '请输入发货单位', clearable: true },
      // },
      // {
      //   prop: 'name',
      //   filter: 'filter_LIKES_name',
      //   component: 'select-name',
      //   props: { placeholder: '请选择品名', clearable: true },
      // },
      //   ]),
      entityForm: { ...Model.model },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        name: { required: true, message: '请输入品名', trigger: 'blur' },
        productId: { required: true, message: '请输入品名', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' },
        senderName: { required: true, message: '请选择发货单位', trigger: 'blur' },
        receiverName: { required: true, message: '请输入收货单位', trigger: 'blur' },
        coalType: { required: true, message: '请选择煤种类型', trigger: 'change' },
        truckCount: { required: true, message: '请输入车数', trigger: 'blur' },
        sendWeight: { required: true, message: '请输入原发数', trigger: 'blur' },
        receiveWeight: { required: true, message: '请输入实收数', trigger: 'blur' },
        wayCost: { required: true, message: '请输入途耗', trigger: 'blur' },
        amountLeft: { required: true, trigger: 'blur' },
        truckCountAcc: { required: true, message: '请输入车数累计', trigger: 'blur' },
        sendWeightAcc: { required: true, message: '请输入原发累计(本月)', trigger: 'blur' },
        receiveWeightAcc: { required: true, message: '请输入实收累计(本月)', trigger: 'blur' },
        wayCostAcc: { required: true, message: '请输入途耗累计', trigger: 'blur' }
      },
      // excel配置
      excelConfig: { excel: Model.getImportConfig(), dialog: false },
      // 记忆字段
      memoryEntity: { fields: {}, triggered: false },
      // 合同
      // contractEntity: {
      //   active: [],
      //   options: [],
      //   props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' },
      // },
      // 供应商
      supplierEntity: { options: [], active: [] },
      // 品名
      nameEntity: { options: [], active: '' },
      // 累计字段
      entityFormAcc: { receiveWeightAcc: 0, sendWeightAcc: 0, truckCountAcc: 0 },
      values: '',
      SupplierSelect: '',
      contractActive: '1483699287445381121',
      dialogFormVisibleV: false,
      changeeFormLoading: false,
      entityFormV: {
        date: new Date(new Date().getTime()).Format('yyyy-MM-dd')
      },
      isBilllist: [
        { label: '是', type: 'Y' },
        { label: '否', type: 'N' }
      ],

      ChangedialogFormVisible: false,
      selectList: [],
      // batchChangeType: '',
      changeForm: {},
      title: '',
      changeentityFormLoading: false,

      filtersCompanyFullName: '',
      useColDeep: true
    }
  },

  created() {
    // this._requestInit()
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'name',
        'senderName',
        'receiverName',
        'receiverName',
        'coalType',
        'contractId',
        'contractCode',
        'supplierId',
        'supplierName',
        'productCode',
        'productId'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })

    this.permschange(this.curd)
    this.permsAction(this.curd)
  },

  beforeRouteEnter(to, from, next) {
    // console.log(to, from)
    next()
  },
  watch: {},
  methods: {
    getenterpriseName(enterpriseName) {
      // console.log('4444')
      // console.log(enterpriseName)
      this.filtersCompanyFullName = enterpriseName
      this.useColDeep = false
      let name = enterpriseName
      if (enterpriseName) {
        // let bb = {
        //   prop: 'lossNull',
        //   filter: 'filter_EQS_lossNull',
        //   component: 'select-LossNullList',
        //   props: {
        //     placeholder: '亏吨',
        //     clearable: true,
        //     isShow: true,
        //     serchCompanyFullName: name,
        //   },
        // }

        this.filterOption.columns[7].props = {
          ...this.filterOption.columns[7].props,
          serchCompanyFullName: name,
          isShow: true
        }
      } else {
        this.filterOption.columns[7].props = {
          ...this.filterOption.columns[7].props,
          serchCompanyFullName: name,
          isShow: false
        }
        // let bb = {
        //   prop: 'lossNull',
        //   filter: 'filter_EQS_lossNull',
        //   component: 'select-LossNullList',
        //   props: {
        //     placeholder: '亏吨',
        //     clearable: true,
        //     isShow: false,
        //     serchCompanyFullName: ' ',
        //   },
        // }
        // this.filterOption.columns[5] = Object.assign({}, this.filterOption.columns[5], bb)
        // this.$set(this.filterOption.columns, 5, this.filterOption.columns[5])

        // this.filterOption.columns.splice(5, 5, bb)
        // this.filterOption.columns.splice(5, 1)
      }
      // console.log(this.filterOption.columns)
    },
    handleFilter(filters) {
      // console.log(filters)
      if (this.filtersCompanyFullName) {
        filters.filter_LIKES_companyFullName = this.filtersCompanyFullName
      }
      this.filtersSeach = filters
      this.$refs[this.curd].searchChange({ ...this.listQuery, ...filters })
    },

    async batchChangeisSettle(type) {
      this.changeForm.isSettle = type
      // console.log(this.changeForm)
      // try {
      //   await this.model.save({ ...row })
      //   this.$message({ showClose: true, message: '操作成功', type: 'success' })
      // } catch (e) {
      //   this.$message({ message: '保存失败', type: 'warning' })
      // }
    },

    selectItem(list) {
      //批量选择的数据
      this.selectList = list
    },
    //批量修改
    async batchChangePrice(selectList, type) {
      this.changeForm = {}
      this.title = '批量修改运费'
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      this.ChangedialogFormVisible = true
      // console.log(selectList)
      // this.batchChangeType = type
      // if (type == 'batchChangeCarriage') {
      //   //批量修改运费
      //   this.title = '批量修改运费'
      // } else if (type == 'batchChangeRemarks') {
      //   //批量修改备注
      //   this.title = '批量修改备注'
      // } else if (type == 'batchChangeSettle') {
      //   //批量修改是否结算
      //   this.title = '批量修改是否结算'
      // }
    },

    async saveChangeEntityForm(changeForm) {
      let newarry = []
      let selectList = this.selectList
      for (var i = 0; i < selectList.length; i++) {
        // let obj = {}
        // obj.id = selectList[i].id
        // if (this.batchChangeType == 'batchChangeCarriage') {
        //   //批量修改运费
        //   obj.freightPrice = this.changeForm.freightPrice
        // } else if (this.batchChangeType == 'batchChangeRemarks') {
        //   //批量修改备注
        //   obj.remarks = this.changeForm.remarks
        // } else if (this.batchChangeType == 'batchChangeSettle') {
        //   //批量修改是否结算
        //   obj.isSettle = this.changeForm.isSettle
        // }

        // obj.freightPrice = this.changeForm.freightPrice
        // obj.remarks = this.changeForm.remarks
        // obj.isSettle = this.changeForm.isSettle
        // obj.deductMoney = this.changeForm.deductMoney
        newarry.push(selectList[i].id)
      }
      let parm = {
        idList: newarry,
        freightPrice: this.changeForm.freightPrice
        // weightHouseLogList: newarry
      }
      // console.log(parm)
      let valid
      try {
        valid = await this.$refs[changeForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        this.changeentityFormLoading = true
        // if (this.batchChangeType == 'batchChangeCarriage') {
        //   //批量修改运费
        //   let { data } = await Model.savebatchChangeCarriage({ ...this.listQuery, ...parm })
        // } else if (this.batchChangeType == 'batchChangeRemarks') {
        //   //批量修改备注
        //   let { data } = await Model.savebatchChangeRemarks({ ...this.listQuery, ...parm })
        // } else if (this.batchChangeType == 'batchChangeSettle') {
        //   //批量修改是否结算
        //   let { data } = await Model.savebatchChangeSettle({ ...this.listQuery, ...parm })
        // }

        let { data } = await Model.saveupdateiSFrPr({ ...this.listQuery, ...parm })
        if (data) {
          this.changeentityFormLoading = false
          this.getList()
          this.resetVariant()
          this.ChangedialogFormVisible = false
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        }
      }
    },

    ChangehandleClose() {
      this.resetVariant()
    },

    async selectChangeisBill(row) {
      try {
        await this.model.save({ ...row })
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
        this.getList()
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },
    async selectChangeisSettle(row) {
      try {
        await this.model.save({ ...row })
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
        this.getList()
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },

    // async selectChangeisSettle(row, type) {
    //   row.isSettle = type
    //   try {
    //     await this.model.save({ ...row })
    //     this.$message({ showClose: true, message: '操作成功', type: 'success' })
    //   } catch (e) {
    //     this.$message({ message: '保存失败', type: 'warning' })
    //   }
    // },
    changeAll(row) {
      // console.log(row)
      row._all = !row._all
    },
    handleUpdate(row) {
      this.changeAll(row)
      this.changeCurrent(row, true) // 编辑全部
    },
    handleSave(row) {
      this.changeAll(row)
      // console.log(row._all)
      this.operationRow(row, true) // 取消时关闭所有的active
      this.changeCurrent(row, false) // 编辑全部
    },
    handleCancel(row) {
      row._all = false
      this.operationRow(row, false) // 取消时关闭所有的active
    },
    changeCurrent(row, target) {
      if (target === true) {
        for (const [k, v] of Object.entries(row.active)) {
          row.active[k] = !v
        }
      } else if (target === false) {
        for (const key of Object.keys(row.active)) {
          row.active[key] = false
        }
      } else {
        row.active[target] = !row.active[target]
      }
    },
    setmoney1(row, target) {
      //运费freightPrice
      // 实收row.weight
      if (!row.deductMoney) {
        row.deductMoney = 0
      }
      if (!row.weight) {
        row.weight = 0
      }
      if (!row.originalTon) {
        row.originalTon = 0
      }

      if (!row.price) {
        row.price = 0
      }
      if (row.type == 2) {
        if (row.freightPrice) {
          //  扣款=( 煤价*亏吨)
          // 应结金额 =(实收*运费单价-扣款)
          let freighMoneynum = 0
          let lossNullnum = 0
          let deductMoneynum = 0
          if (row.lossNull) {
            // 亏吨=原发-实收
            lossNullnum = new Decimal(row.originalTon).sub(row.weight).toString()
            row.lossNull = lossNullnum

            if (row.lossNull >= 0.2) {
              // 亏吨0.2及以上
              // 扣款=( 煤价*(亏吨 - 0.2))
              let number = new Decimal(lossNullnum).sub(0.2).toString()
              deductMoneynum = new Decimal(row.price).mul(number).toString()
              row.deductMoney = deductMoneynum

              if (row.freightPrice <= 20) {
                //运费20以以下 个位数大于5，保留5，小于5抹0
                // 运费小于等于20 ，并且个位数大于等于5保留5， 小于5个位数抹零
                // 举例：比如运费18，   结算金  17就自动变为15， 14就变为10？

                // console.log('扣款' + row.deductMoney)
                // console.log('运费' + row.freightPrice)
                // console.log('实收' + row.weight)

                // let num = new Decimal(row.price).mul(0.2)
                // freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).sub(row.deductMoney).add(num).toString()
                // (实收*运费单价-扣款)
                freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).sub(row.deductMoney).toString()
                console.log('金额' + freighMoneynum)
                console.log(typeof freighMoneynum)
                console.log('金额' + freighMoneynum.split('.'))
                var result = freighMoneynum.split('.')
                let str = result[0]
                let newnumber = str.charAt(str.length - 1)
                if (newnumber >= 5) {
                  // 个位数大于5，保留5
                  let newStr = str.slice(0, -1) + '5'
                  console.log('个位数直接抹5' + newStr)
                  row.settleMoney = newStr
                  console.log('大于等于五' + newStr)
                } else if (newnumber < 5) {
                  //小于5 ,个位数抹零
                  let newStr = str.slice(0, -1) + '0'
                  console.log('个位数直接抹零666' + newStr)
                  row.settleMoney = newStr
                }
                row.freighMoney = freighMoneynum
              } else {
                // 运费20以上 个位数直接抹0
                // let num = new Decimal(row.price).mul(0.2)
                // freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).sub(row.deductMoney).add(num).toString()
                // (实收*运费单价-扣款)
                freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).sub(row.deductMoney).toString()
                var result = freighMoneynum.split('.')
                let str = result[0]
                let newStr = str.slice(0, -1) + '0'
                console.log('个位数直接抹零222' + newStr)

                row.settleMoney = newStr
                row.freighMoney = freighMoneynum
              }
            } else if (row.lossNull < 0.2) {
              // 亏吨0.2以下

              if (row.freightPrice <= 20) {
                //运费20以以下 个位数大于5，保留5，小于5抹0
                freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).toString()
                console.log('金额' + freighMoneynum)
                console.log(typeof freighMoneynum)
                console.log('金额' + freighMoneynum.split('.'))
                var result = freighMoneynum.split('.')
                let str = result[0]
                let newnumber = str.charAt(str.length - 1)
                if (newnumber >= 5) {
                  // 个位数大于5，保留5
                  let newStr = str.slice(0, -1) + '5'
                  console.log('个位数直接抹5' + newStr)
                  row.settleMoney = newStr
                  console.log('大于等于五' + newStr)
                } else if (newnumber < 5) {
                  //小于5 ,个位数抹零
                  let newStr = str.slice(0, -1) + '0'
                  console.log('个位数直接抹零666' + newStr)
                  row.settleMoney = newStr
                }
                row.freighMoney = freighMoneynum
              } else {
                // 运费20以上 个位数直接抹0
                freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).toString()
                var result = freighMoneynum.split('.')
                let str = result[0]
                let newStr = str.slice(0, -1) + '0'
                console.log('个位数直接抹零222' + newStr)
                row.settleMoney = newStr
                row.freighMoney = freighMoneynum
              }
            }
          } else if (row.lossNull == '') {
            // 亏吨0.2以下
            if (row.freightPrice <= 20) {
              //运费20以以下 个位数大于5，保留5，小于5抹0
              // 运费小于等于20 ，并且个位数大于等于5保留5， 小于5个位数抹零
              // 举例：比如运费18，   结算金  17就自动变为15， 14就变为10？
              freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).toString()
              console.log('金额' + freighMoneynum)
              console.log(typeof freighMoneynum)
              console.log('金额' + freighMoneynum.split('.'))
              var result = freighMoneynum.split('.')
              let str = result[0]
              let newnumber = str.charAt(str.length - 1)
              if (newnumber >= 5) {
                // 个位数大于5，保留5
                let newStr = str.slice(0, -1) + '5'
                console.log('个位数直接抹5' + newStr)
                row.settleMoney = newStr
                console.log('大于等于五' + newStr)
              } else if (newnumber < 5) {
                //小于5 ,个位数抹零
                let newStr = str.slice(0, -1) + '0'
                console.log('个位数直接抹零666' + newStr)
                row.settleMoney = newStr
              }
              row.freighMoney = freighMoneynum
            } else {
              // 运费20以上 个位数直接抹0
              freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).toString()
              var result = freighMoneynum.split('.')
              let str = result[0]
              let newStr = str.slice(0, -1) + '0'
              console.log('个位数直接抹零222' + newStr)
              row.settleMoney = newStr
              row.freighMoney = freighMoneynum
            }


            
          }
          row.active['freighMoney'] = false
        }
      }
    },

    setmoney2(row, target) {
      //运费freightPrice
      // 实收row.weight
      if (!row.deductMoney) {
        row.deductMoney = 0
      }
      if (!row.weight) {
        row.weight = 0
      }
      if (!row.originalTon) {
        row.originalTon = 0
      }

      if (!row.price) {
        row.price = 0
      }
      if (row.type == 2) {
        if (row.freightPrice) {
          //  扣款=( 煤价*亏吨)
          // 应结金额 =(实收*运费单价-扣款)
          let freighMoneynum = 0
          let lossNullnum = 0
          if (row.lossNull) {
            // 亏吨=原发-实收
            lossNullnum = new Decimal(row.originalTon).sub(row.weight).toString()
            row.lossNull = lossNullnum
            if (row.lossNull >= 0.2) {
              // 亏吨0.2及以上
              if (row.freightPrice <= 20) {
                // (实收*运费单价-扣款)
                freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).sub(row.deductMoney).toString()
                console.log('金额' + freighMoneynum)
                console.log(typeof freighMoneynum)
                console.log('金额' + freighMoneynum.split('.'))
                var result = freighMoneynum.split('.')
                let str = result[0]
                let newnumber = str.charAt(str.length - 1)
                if (newnumber >= 5) {
                  // 个位数大于5，保留5
                  let newStr = str.slice(0, -1) + '5'
                  console.log('个位数直接抹5' + newStr)
                  row.settleMoney = newStr
                  console.log('大于等于五' + newStr)
                } else if (newnumber < 5) {
                  //小于5 ,个位数抹零
                  let newStr = str.slice(0, -1) + '0'
                  console.log('个位数直接抹零666' + newStr)
                  row.settleMoney = newStr
                }
                row.freighMoney = freighMoneynum
              } else {
                // 运费20以上 个位数直接抹0
                // let num = new Decimal(row.price).mul(0.2)
                // freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).sub(row.deductMoney).add(num).toString()
                // (实收*运费单价-扣款)
                freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).sub(row.deductMoney).toString()
                var result = freighMoneynum.split('.')
                let str = result[0]
                let newStr = str.slice(0, -1) + '0'
                console.log('个位数直接抹零222' + newStr)

                row.settleMoney = newStr
                row.freighMoney = freighMoneynum
              }
            } else if (row.lossNull < 0.2) {
              // 亏吨0.2以下

              if (row.freightPrice <= 20) {
                //运费20以以下 个位数大于5，保留5，小于5抹0
                freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).toString()
                console.log('金额' + freighMoneynum)
                console.log(typeof freighMoneynum)
                console.log('金额' + freighMoneynum.split('.'))
                var result = freighMoneynum.split('.')
                let str = result[0]
                let newnumber = str.charAt(str.length - 1)
                if (newnumber >= 5) {
                  // 个位数大于5，保留5
                  let newStr = str.slice(0, -1) + '5'
                  console.log('个位数直接抹5' + newStr)
                  row.settleMoney = newStr
                  console.log('大于等于五' + newStr)
                } else if (newnumber < 5) {
                  //小于5 ,个位数抹零
                  let newStr = str.slice(0, -1) + '0'
                  console.log('个位数直接抹零666' + newStr)
                  row.settleMoney = newStr
                }
                row.freighMoney = freighMoneynum
              } else {
                // 运费20以上 个位数直接抹0
                freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).toString()
                var result = freighMoneynum.split('.')
                let str = result[0]
                let newStr = str.slice(0, -1) + '0'
                console.log('个位数直接抹零222' + newStr)
                row.settleMoney = newStr
                row.freighMoney = freighMoneynum
              }
            }
          } else if (row.lossNull == '') {
            // 亏吨0.2以下
            if (row.freightPrice <= 20) {
              //运费20以以下 个位数大于5，保留5，小于5抹0
              // 运费小于等于20 ，并且个位数大于等于5保留5， 小于5个位数抹零
              // 举例：比如运费18，   结算金  17就自动变为15， 14就变为10？
              freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).toString()
              console.log('金额' + freighMoneynum)
              console.log(typeof freighMoneynum)
              console.log('金额' + freighMoneynum.split('.'))
              var result = freighMoneynum.split('.')
              let str = result[0]
              let newnumber = str.charAt(str.length - 1)
              if (newnumber >= 5) {
                // 个位数大于5，保留5
                let newStr = str.slice(0, -1) + '5'
                console.log('个位数直接抹5' + newStr)
                row.settleMoney = newStr
                console.log('大于等于五' + newStr)
              } else if (newnumber < 5) {
                //小于5 ,个位数抹零
                let newStr = str.slice(0, -1) + '0'
                console.log('个位数直接抹零666' + newStr)
                row.settleMoney = newStr
              }
              row.freighMoney = freighMoneynum
            } else {
              // 运费20以上 个位数直接抹0
              freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).toString()
              var result = freighMoneynum.split('.')
              let str = result[0]
              let newStr = str.slice(0, -1) + '0'
              console.log('个位数直接抹零222' + newStr)
              row.settleMoney = newStr
              row.freighMoney = freighMoneynum
            }
          }
          row.active['freighMoney'] = false
        }
      }
    },

    handleBlur(row, target) {
      //运费金额：亏吨 0.2以上：（实收 * 运费单价 - 扣款）  （其中扣款 = 煤价 * (亏吨-0.2)）
      console.log(target)
      if (row.type == 2) {
        //采购类型
        this.changeCurrent(row, target) // 失去事件触发改变时关闭target的active
        if (target != 'freighMoney') {
          // 应结金额
          //金额
          const status = this.isOpenSave(row) // 判断当前事件是否全部关闭
          if (status) return // 为真说明还有blur未关闭
          for (const key of Object.keys(row.bak)) {
            if (row.freightPrice != null || row.freightPrice != '') {
              //金额= (实收*运费单价-扣款) +煤价*0.2
              let freighMoneynum = 0
              let lossNullnum = 0
              let deductMoneynum = 0
              if (!row.deductMoney) {
                row.deductMoney = 0
              }
              if (!row.weight) {
                row.weight = 0
              }
              if (!row.price) {
                row.price = 0
              }
              if (!row.originalTon) {
                row.originalTon = 0
              }
              if (row.lossNull) {
                // 亏吨=原发-实收
                lossNullnum = new Decimal(row.originalTon).sub(row.weight).toString()
                row.lossNull = lossNullnum

                if (row.lossNull >= 0.2) {
                  // 亏吨0.2及以上         // 扣款=( 煤价*亏吨 - 0.2)

                  // 扣款=( 煤价*(亏吨 - 0.2))
                  let number = new Decimal(lossNullnum).sub(0.2).toString()
                  deductMoneynum = new Decimal(row.price).mul(number).toString()

                  if (row.freightPrice <= 20) {
                    //运费20以以下 个位数大于5，保留5，小于5抹0
                    // 运费小于等于20 ，并且个位数大于等于5保留5， 小于5个位数抹零
                    // 举例：比如运费18，   结算金  17就自动变为15， 14就变为10？

                    // let num = new Decimal(row.price).mul(0.2)
                    // freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).sub(row.deductMoney).add(num).toString()
                    // (实收*运费单价-扣款)
                    freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).sub(row.deductMoney).toString()
                    console.log('金额' + freighMoneynum)
                    console.log(typeof freighMoneynum)
                    console.log('金额' + freighMoneynum.split('.'))
                    var result = freighMoneynum.split('.')
                    let str = result[0]
                    let newnumber = str.charAt(str.length - 1)
                    if (newnumber >= 5) {
                      // 个位数大于5，保留5
                      let newStr = str.slice(0, -1) + '5'
                      console.log('个位数直接抹5' + newStr)
                      row.settleMoney = newStr
                      console.log('大于等于五' + newStr)
                    } else if (newnumber < 5) {
                      //小于5 ,个位数抹零
                      let newStr = str.slice(0, -1) + '0'
                      console.log('个位数直接抹零666' + newStr)
                      row.settleMoney = newStr
                    }
                    row.freighMoney = freighMoneynum
                  } else {
                    // 运费20以上 个位数直接抹0
                    // let num = new Decimal(row.price).mul(0.2)
                    // freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).sub(row.deductMoney).add(num).toString()
                    // (实收*运费单价-扣款)
                    freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).sub(row.deductMoney).toString()
                    var result = freighMoneynum.split('.')
                    let str = result[0]
                    let newStr = str.slice(0, -1) + '0'
                    console.log('个位数直接抹零222' + newStr)

                    row.settleMoney = newStr
                    row.freighMoney = freighMoneynum
                  }
                } else if (row.lossNull < 0.2) {
                  // 扣款=( 煤价*亏吨 - 0.2)
                  // 亏吨0.2以下
                  if (row.freightPrice <= 20) {
                    //运费20以以下 个位数大于5，保留5，小于5抹0
                    freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).toString()
                    console.log('金额' + freighMoneynum)
                    console.log(typeof freighMoneynum)
                    console.log('金额' + freighMoneynum.split('.'))
                    var result = freighMoneynum.split('.')
                    let str = result[0]
                    let newnumber = str.charAt(str.length - 1)
                    if (newnumber >= 5) {
                      // 个位数大于5，保留5
                      let newStr = str.slice(0, -1) + '5'
                      console.log('个位数直接抹5' + newStr)
                      row.settleMoney = newStr
                      console.log('大于等于五' + newStr)
                    } else if (newnumber < 5) {
                      //小于5 ,个位数抹零
                      let newStr = str.slice(0, -1) + '0'
                      console.log('个位数直接抹零666' + newStr)
                      row.settleMoney = newStr
                    }
                    row.freighMoney = freighMoneynum
                  } else {
                    // 运费20以上 个位数直接抹0
                    freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).toString()
                    var result = freighMoneynum.split('.')
                    let str = result[0]
                    let newStr = str.slice(0, -1) + '0'
                    console.log('个位数直接抹零222' + newStr)

                    row.settleMoney = newStr
                    row.freighMoney = freighMoneynum
                  }
                }
              } else if (row.lossNull == '') {
                // 亏吨0.2以下
                if (row.freightPrice <= 20) {
                  //运费20以以下 个位数大于5，保留5，小于5抹0
                  freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).toString()
                  console.log('金额' + freighMoneynum)
                  console.log(typeof freighMoneynum)
                  console.log('金额' + freighMoneynum.split('.'))
                  var result = freighMoneynum.split('.')
                  let str = result[0]
                  let newnumber = str.charAt(str.length - 1)
                  if (newnumber >= 5) {
                    // 个位数大于5，保留5
                    let newStr = str.slice(0, -1) + '5'
                    console.log('个位数直接抹5' + newStr)
                    row.settleMoney = newStr
                    console.log('大于等于五' + newStr)
                  } else if (newnumber < 5) {
                    //小于5 ,个位数抹零
                    let newStr = str.slice(0, -1) + '0'
                    console.log('个位数直接抹零666' + newStr)
                    row.settleMoney = newStr
                  }
                  row.freighMoney = freighMoneynum
                } else {
                  // 运费20以上 个位数直接抹0
                  let num = new Decimal(row.price).mul(0.2)
                  freighMoneynum = new Decimal(row.weight).mul(row.freightPrice).toString()
                  var result = freighMoneynum.split('.')
                  let str = result[0]
                  let newStr = str.slice(0, -1) + '0'
                  console.log('个位数直接抹零222' + newStr)
                  row.settleMoney = newStr
                  row.freighMoney = freighMoneynum
                }
              }
              row.freighMoney = freighMoneynum
            }
            if (row.bak[key] + '' !== row[key] + '') return // 判断两个值是否不相等 不相等则直接返回
          }
        }
      }
      setTimeout(() => {
        row._all = false
      }, 300) // 如果上述条件都成立则开回到最初的模式
    },

    // handleBlur(row, target) {
    //   // console.log('运费 ' + target)
    //   if (row.type == 2) {
    //     // console.log('采购类型')
    //     if (row.weight == '') {
    //       row.freighMoney = ''
    //       row.settleMoney = ''
    //     }
    //     //采购类型
    //     this.changeCurrent(row, target) // 失去事件触发改变时关闭target的active
    //     if (target != 'freighMoney') {
    //       //金额
    //       const status = this.isOpenSave(row) // 判断当前事件是否全部关闭
    //       if (status) return // 为真说明还有blur未关闭
    //       for (const key of Object.keys(row.bak)) {
    //         console.log('运费 ' + row.freightPrice)

    //         if (row.freightPrice != null || row.freightPrice != '') {
    //           // 金额=（实收weight + 超扣 beyondNum）*运费freightPrice
    //           let settleMoneynum = 0
    //           let num = 0
    //           if (row.freightPrice <= 20) {
    //             // 运费小于等于20 ，并且个位数大于等于5保留5， 小于5个位数抹零
    //             // 举例：比如运费18，   结算金  17就自动变为15， 14就变为10？
    //             settleMoneynum = Math.round(row.freightPrice * (row.weight + row.beyondNum))
    //             num = parseFloat(row.freightPrice * (row.weight + row.beyondNum)).toFixed(2)
    //             // console.log('结算金额' + num)
    //             var result = num.split('.')
    //             let str = result[0]
    //             let newnumber = str.charAt(str.length - 1)
    //             if (newnumber >= 5) {
    //               let newStr = str.replace(newnumber, 5)
    //               row.settleMoney = newStr
    //               // console.log('大于等于五' + newStr)
    //               //小于5个位数抹零
    //             } else if (newnumber < 5) {
    //               let newStr = str.replace(newnumber, 0)
    //               // console.log('小于五' + newStr)
    //               row.settleMoney = newStr
    //             }
    //           } else {
    //             settleMoneynum = Math.round(row.freightPrice * (row.weight + row.beyondNum))
    //             console.log('扣款' + row)
    //             num = parseFloat(row.freightPrice * (row.weight + row.beyondNum)).toFixed(2)
    //             row.settleMoney = num
    //           }

    //           row.freighMoney = num

    //           if (row.deductMoney != null && row.deductMoney.length != 0) {
    //             // console.log('不为空')
    //             if (row.settleMoney != null || row.settleMoney != '') {
    //               // 结算金额=结算金额-扣款
    //               let num2 = Math.round(row.settleMoney - row.deductMoney)
    //               // console.log(num2)
    //               let numss = num2.toString()
    //               // console.log('四舍五入' + numss)
    //               var str23 = numss.slice(0, -1) + '0'
    //               // console.log('四舍五入抹零' + str23)
    //               row.settleMoney = str23
    //             }
    //             if (row.freighMoney != null || row.freighMoney != '') {
    //               // 金额=金额-扣款
    //               let num1 = parseFloat(row.freighMoney - row.deductMoney).toFixed(2)
    //               // console.log(num1)
    //               row.freighMoney = num1
    //             }
    //           } else {
    //             let nums = settleMoneynum.toString()
    //             var str2 = nums.slice(0, -1) + '0'
    //             row.settleMoney = str2
    //           }
    //         }
    //         if (row.bak[key] + '' !== row[key] + '') return // 判断两个值是否不相等 不相等则直接返回
    //       }
    //     }
    //   } else {
    //     // console.log('销售类型')
    //     if (row.weight == null || row.weight == '') {
    //       row.lossNull = ''
    //       row.freighMoney = ''
    //       row.settleMoney = ''
    //     }
    //     //销售类型  实收*运费=金额  结算金额抹去零
    //     this.changeCurrent(row, target) // 失去事件触发改变时关闭target的active
    //     if (target != 'freighMoney') {
    //       const status = this.isOpenSave(row) // 判断当前事件是否全部关闭
    //       if (status) return // 为真说明还有blur未关闭
    //       for (const key of Object.keys(row.bak)) {
    //         if (row.freightPrice != null || row.freightPrice != '') {
    //           // 金额=实收weight *运费freightPrice
    //           // 亏吨lossNull = 原发数originalTon -机打实收weight
    //           // if (row.weight) {
    //           // console.log('扣款' + row)
    //           // console.log('扣款' + row.deductMoney)

    //           // console.log('运费' + row.freightPrice)

    //           let settleMoneynum = 0
    //           let num = 0
    //           if (row.freightPrice < 20) {
    //             // console.log('运费小于20')
    //             // 运费小于等于20 ，并且个位数大于等于5保留5， 小于5个位数抹零
    //             // 举例：比如运费18，   结算金  17就自动变为15， 14就变为10？
    //             settleMoneynum = Math.round(row.freightPrice * row.weight)
    //             num = parseFloat(row.freightPrice * row.weight).toFixed(2)
    //             // console.log('结算金额' + num)
    //             var result = num.split('.')
    //             let str = result[0]
    //             let newnumber = str.charAt(str.length - 1)
    //             if (newnumber >= 5) {
    //               let newStr = str.replace(newnumber, 5)
    //               row.settleMoney = newStr
    //               // console.log('大于等于五' + newStr)
    //               //小于5个位数抹零
    //             } else if (newnumber < 5) {
    //               let newStr = str.replace(newnumber, 0)
    //               // console.log('小于五' + newStr)
    //               row.settleMoney = newStr
    //             }
    //           } else {
    //             settleMoneynum = Math.round(row.freightPrice * row.weight)
    //             num = parseFloat(row.freightPrice * row.weight).toFixed(2)
    //             row.settleMoney = num
    //           }

    //           row.freighMoney = num
    //           if (row.weight == null) {
    //             row.lossNull = ''
    //           } else {
    //             row.lossNull = parseFloat(row.originalTon - row.weight).toFixed(2)
    //           }
    //           // console.log('扣款' + row.deductMoney)
    //           if (row.deductMoney != null && row.deductMoney.length != 0) {
    //             // console.log('扣款不为空')
    //             if (row.settleMoney != null || row.settleMoney != '') {
    //               // 结算金额=结算金额-扣款
    //               // let num2 = parseFloat(row.settleMoney - row.deductMoney).toFixed(2)
    //               let num2 = Math.round(row.settleMoney - row.deductMoney)
    //               // console.log(num2)
    //               let numss = num2.toString()
    //               var str23 = numss.slice(0, -1) + '0'
    //               row.settleMoney = str23
    //             }
    //             if (row.freighMoney != null || row.freighMoney != '') {
    //               // 金额=金额-扣款
    //               let num1 = parseFloat(row.freighMoney - row.deductMoney).toFixed(2)
    //               // console.log(num1)
    //               row.freighMoney = num1
    //             }
    //           } else {
    //             // console.log('扣款为空')
    //             let nums = settleMoneynum.toString()
    //             var str2 = nums.slice(0, -1) + '0'
    //             row.settleMoney = str2
    //           }
    //         }
    //         if (row.bak[key] + '' !== row[key] + '') return // 判断两个值是否不相等 不相等则直接返回
    //       }
    //     }
    //   }

    //   setTimeout(() => {
    //     row._all = false
    //   }, 300) // 如果上述条件都成立则开回到最初的模式
    // },

    setmoney3(row, target) {
      // row.active['remarks'] = false
    },
    setmoney4(row, target) {
      // console.log(row.freighMoney)
      //  row.freighMoney
    },

    isOpenSave(row) {
      let status = false
      for (const v of Object.values(row.active)) {
        if (v) {
          status = true
          break
        }
      }
      return status
    },
    /**
     * 双击时改变为input狂
     * @target string|true 用于判断是操作一个还是多个
     */
    handleActive(row, target) {
      row._all = true
      this.changeCurrent(row, target)
    },
    /**
     * 保存或取消
     * @action {true:false} true为保存逻辑，false为取消逻辑
     */
    async operationRow(row, action) {
      if (action) {
        // const isChangefreightPrice = row.freightPrice !== row.bak.freightPrice
        // const isChangedeductMoney = row.deductMoney !== row.bak.deductMoney
        // const isChangedefreighMoney = row.freighMoney !== row.bak.freighMoney

        // const remarksTemplate = `已更新,上次运费为:${row.freightPrice},扣款为:${row.deductMoney},金额为:${row.freighMoney}`
        // if (isChangefreightPrice && isChangedeductMoney) {
        //   if (!row.remarks) {
        //     row.remarks = remarksTemplate
        //   } else if (/^已更新,上次灰分为:\d+,硫分为:\d+$/.test(row.remarks)) {
        //     row.remarks = remarksTemplate
        //   }
        // }
        try {
          await this.model.save({ ...row })
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        } catch (e) {
          this.$message({ message: '保存失败', type: 'warning' })
        }
        // this.getList()
      } else {
        for (const key of Object.keys(row.bak)) {
          if (action) {
            // ---------------------- 可写接口
            row.bak[key] = row[key] // 保存就时把当前key赋给bak
          } else {
            row[key] = row.bak[key] // 取消就是bak值赋给当前key
          }
          row.active[key] = false // 更具Key 将active都初始化
        }
      }
    },

    selectChanged(value) {
      // console.log(value)
    },
    Refreshfn() {
      this.dialogStatus = 'Refresh'
      this.dialogFormVisibleV = true
    },
    handleCloseV() {
      this.dialogFormVisibleV = false
    },
    async RefreshForm() {
      this.changeeFormLoading = true
      let date = this.entityFormV.date
      try {
        if (this.devType == '购进') {
          const { data } = await this.model.refresh({ date })
          this.$message({ showClose: true, message: '刷新成功', type: 'success' })
          this.$refs[this.curd].searchChange({ ...this.listQuery })
          this.changeeFormLoading = false
        } else {
          const { data } = await this.model.refreshV({ date })
          this.$message({ showClose: true, message: '刷新成功', type: 'success' })
          this.$refs[this.curd].searchChange({ ...this.listQuery })
          this.changeeFormLoading = false
        }
      } catch (error) {}
      this.changeeFormLoading = false
      this.dialogFormVisibleV = false
    },
    // handleProductChange(product) {
    //   let { name, id: productId, code: productCode, coalCategory: coalType } = product
    //   this.batchUpdateFields({ name, productId, productCode, coalType })
    // },
    // handleSupplierChange(supplier) {
    //   const { id: supplierId, name: supplierName } = supplier
    //   this.batchUpdateFields({ supplierId, supplierName, senderName: supplierName }) // 同步数据 顺便同步原发
    // },
    // handleContractChange(contract) {
    //   console.log(contract, 1)
    // },

    /**
     * @fields <entityForm>
     */
    batchUpdateFields(fields = {}) {
      const fieldsKeys = Object.keys(fields)
      for (const key of Object.keys(this.entityForm)) {
        if (fieldsKeys.includes(key)) {
          this.entityForm[key] = fields[key]
        }
      }
    },

    _requestInit() {
      // this.getContract()
      // this.getSupplier()
      this.getName()
    },
    /**
     * 保存取消时会触发初始化数据
     */
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.ChangedialogFormVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        // this.contractEntity.active = []
        this.supplierEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },

    handleNameEntity(val) {
      this.entityForm.name = val
    },
    // 获取供应商选择接口
    // async getSupplier() {
    //   try {
    //     let options = []
    //     const { data } = await await supplierModel.page({ size: 1000 })
    //     if (data.records.length) {
    //       data.records.forEach((item) => {
    //         const { name: label, id: value } = item
    //         options.push({ label, value })
    //       })
    //     }
    //     this.supplierEntity.options = options
    //   } catch (e) {}
    // },
    // 获取合同选择接口
    async getContract() {
      try {
        const { data } = await chooseContract()
        this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'displayName', supplierId: 'id' } })
      } catch (error) {}
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
        })
      } catch (error) {}
    },
    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    // 合同改变同步
    handleSupplier({ value, label }) {
      // this.entityForm.supplierId = value
      // this.entityForm.supplierName = label
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label }
    },

    paramsNumber(paramsAcc) {
      for (const [k, v] of Object.entries(paramsAcc)) {
        paramsAcc[k] = !Number.isNaN(v * 1) ? v * 1 : 0
      }
      return paramsAcc
    },

    importExcel(unknown, type) {
      this.excelConfig.dialog = true
    },
    async handleUpload(scope) {
      const data = [...scope.settings.data]
      const importList = data.map((v) => {
        const item = {}
        for (const key of Object.keys(this.entityForm)) {
          item[key] = v[key] !== undefined ? v[key] : ''
        }
        return item
      })
      const text = JSON.stringify(importList)
      scope.uploading.state = true
      const res = await Model.saveList({ text })
      scope.uploading.state = false
      if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      if (res.data.length) {
        scope.settings.data = res.data.map((v, i) => v)
      } else {
        this.$message({ showClose: true, message: '导入成功', type: 'success' })
        scope.settings.data = []
        this.getList()
      }
    },
    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    // 更新累计Acc
    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      // const paramsAcc = await this.getAccValues({ date, contractId })
      // Object.keys(paramsAcc).forEach((key) => {
      //   this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
      //   this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      // })
      this.computeWayCostAcc()
    },
    // 处理receiveWeight, sendWeight 值处理成涂好
    updateWayCost() {
      let { receiveWeight, sendWeight } = this.entityForm
      receiveWeight = receiveWeight * 1
      sendWeight = sendWeight * 1
      if (receiveWeight === '' || sendWeight === '') return false // 数据为空时不计算
      if (receiveWeight === 0 && sendWeight === 0) return (this.entityForm.wayCost = 0.0)
      // 途耗累计=实收累计-原发累计/原发累计
      this.entityForm.wayCost = (((sendWeight - receiveWeight) / sendWeight) * 100).toFixed(2)
    },
    // 计算涂耗累计Acc
    computeWayCostAcc() {
      let { receiveWeightAcc, sendWeightAcc } = this.entityForm
      receiveWeightAcc = receiveWeightAcc * 1
      sendWeightAcc = sendWeightAcc * 1
      let wayCostAcc = (((sendWeightAcc - receiveWeightAcc) / sendWeightAcc) * 100).toFixed(2) * 1
      this.entityForm.wayCostAcc = !Number.isNaN(wayCostAcc) ? wayCostAcc : 0
    },
    // 计算累计方法
    computeAcc(accName, value) {
      if (accName === 'truckCountAcc') {
        this.entityForm[accName] = this.entityFormAcc[accName] * 1 + value * 1
        return
      }
      this.entityForm[accName] = (this.entityFormAcc[accName] * 1 + value * 1).toFixed(2)
    }
  },
  watch: {
    'entityForm.name'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.entityForm.coalType = item.coalCategory || ''
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
      }
    },
    dialogFormVisible(v) {
      if (this.dialogStatus === 'create' && v) {
        this.updateAcc()
      } // 用于再打开时触发合同Acc数据
    },
    'entityForm.receiveWeight'(v) {
      this.computeAcc('receiveWeightAcc', v)
      this.updateWayCost()
    },
    'entityForm.sendWeight'(v) {
      this.computeAcc('sendWeightAcc', v)
      this.updateWayCost()
    },
    'entityForm.truckCount'(v) {
      this.computeAcc('truckCountAcc', v)
    },
    'entityForm.date'() {
      this.updateAcc()
    },
    'entityForm.wayCost'() {
      this.computeWayCostAcc()
    },
    'entityForm.contractId'(id) {
      this.updateAcc()
      if (this.entityForm.date && id) {
        this.updateAcc(this.entityForm.date, id)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'entityForm.supplierId'(id) {
      this.contractActive = id
      const item = this.supplierEntity.options.find((item) => item.value === id)
      if (item) {
        this.supplierEntity.active = item
      } else {
        this.supplierEntity.active = []
      }
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.name = form.productName
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.supplierId
      this.entityForm.senderName = form.secondParty
      this.entityForm.receiverName = form.firstParty
      const item = this.contractEntity.options.find((v) => v.supplierId === value[0])
      if (!item) return
      this.entityForm.supplierId = item.supplierId
      this.entityForm.supplierName = item.supplierName
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
</style>
<style scoped>
.tsdialog >>> .el-dialog__body {
  height: 40vh;
}
</style>
