<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />

    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" otherHeight="130"
            :changeactions="changeactions" :showSummary='true'>

      <el-table-column label="附件" slot="attachmentList" prop="attachmentList" width="100" align="center">
        <template slot-scope="scope">
          <div class="attachmentV" style="margin: 0 auto;">
            <span @click="handlePreviewAttachment(scope.row)" style="cursor: pointer;">查看附件</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" slot="opt" width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" v-if="perms[`${curd}:update`]||false"
                  @click="handleUpdate(scope.row,'update')">编辑</el-tag>
          <el-tag class="opt-btn" color="#fa3131" @click="handleUpdate(scope.row,'watch')">查看</el-tag>
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="entityForm.date" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <!-- <el-form-item label="客户名称" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                  </el-form-item> -->
                  <el-form-item label="客户名称" prop="customerName">
                    <el-select v-model="customerNameEntity.value" placeholder="请选择买方" clearable filterable style="width:100%">
                      <el-option v-for="item in customerNameEntity.options" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">附件信息</div>
            <div class="form-layout">
              <el-collapse v-model="collapse">
                <el-collapse-item title="附件上传" name="1">
                  <div class="upload">
                    <div class="upload-list">
                      <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                        <i class="el-icon-close icon" @click="handleRemoveUpload(index)"></i>
                        <el-link class="up-load-warp-link" :underline=" false" :href="item.uri">{{item.display}}</el-link>
                      </div>
                      <upload-attachment ref="upload" class="upload-attachment" :uploadData="uploadData" source="coalSample"
                                         :size="size" :limitNum="limitNum" accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg"
                                         @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                         listType="text" />
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

    <el-drawer title="附件列表" :visible.sync="drawer.visible" direction="rtl" :before-close="handleCloseDrawer">
      <template v-if="drawer.list.length">
        <div class="attachment-container">
          <div class="attachment-item" v-for="(item,index) in drawer.list" :key="index">
            <!-- 图片预览 -->
            <div v-if="item.isImage" class="image-preview">
              <el-image class="preview-image" :src="item.uri" fit="contain" :preview-src-list="drawer.preview" />
              <div class="file-name">{{ item.display }}</div>
            </div>

            <!-- 文档展示 -->
            <div v-else-if="item.isDocument" class="document-preview">
              <i class="el-icon-document" style="font-size: 48px; color: #409EFF;"></i>
              <el-link :href="item.uri" :download="item.display" target="_blank" :underline="false" class="file-info">
                <div class="file-name">{{ item.display }}</div>
                <div class="file-type">{{ item.fileType.toUpperCase() }} 文档</div>
              </el-link>
            </div>

            <!-- Excel展示 -->
            <div v-else-if="item.isExcel" class="excel-preview">
              <i class="el-icon-s-data" style="font-size: 48px; color: #67C23A;"></i>
              <el-link :href="item.uri" :download="item.display" target="_blank" :underline="false" class="file-info">
                <div class="file-name">{{ item.display }}</div>
                <div class="file-type">Excel 表格</div>
                <!-- <div class="file-meta">已处理</div> -->
              </el-link>
            </div>

            <!-- 其他文件类型 -->
            <div v-else class="other-file">
              <i class="el-icon-files" style="font-size: 48px;"></i>
              <el-link :href="item.uri" :download="item.display" target="_blank" :underline="false" class="file-info">
                <div class="file-name">{{ item.display }}</div>
                <div class="file-type">{{ item.fileType.toUpperCase() }} 文件</div>
              </el-link>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <el-empty :image="emptyImgPath" :image-size="200" description="暂无图片" />
      </template>
    </el-drawer>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import { qualInOrOutFilterOption, listQuery } from '@/const'
import Model from '@/model/finance/saleFreightAnnex'
import { createMemoryField } from '@/utils/index'
import CustomerModel from '@/model/user/customer'
import { HotTable, HotColumn } from '@handsontable/vue'
import { registerLanguageDictionary, zhCN } from 'handsontable/i18n'
import { registerAllModules } from 'handsontable/registry'
import 'handsontable/dist/handsontable.full.css'
import { forEach } from 'jszip/lib/object'
registerAllModules()
registerLanguageDictionary(zhCN)

export default {
  name: 'saleFreightAnnex',
  mixins: [Mixins],
  data() {
    return {
      curd: 'saleFreightAnnex',
      model: Model,
      filterOption: { ...qualInOrOutFilterOption },
      dialogFormVisibleV: false,
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        // time: { required: true, message: '请选择采样时间', trigger: 'blur' },
        customerName: { required: true, message: '请输入客户名称', trigger: 'change' }
      },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      contractParams: { query: '' },
      contractActive: [],
      entityForm: { ...Model.model },
      contract: {
        props: {
          label: 'supplierName',
          value: 'supplierId',
          children: 'contractBuyList'
        },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      indicators: { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity },
      memoryEntity: { fields: {}, triggered: false },
      filters: {},
      fromDashboard: false,
      customerNameEntity: { value: '', options: [] },

      uploadList: [], // 用于展示
      limitNum: 1000,
      collapse: '1',
      uploadData: { refId: '', refType: 'CoalSample' },
      selectList: [],
      optName: 'create',

      drawer: {
        visible: false,
        list: [],
        preview: []
      },
      drawerV: {
        visible: false,
        list: [],
        preview: []
      },
      emptyImgPath: require(`@/assets/empty_img.jpg`)
    }
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_LIKES_date: to.params.date }
        vm.fromDashboard = true
      })
    } else {
      next()
    }
  },
  components: {
    HotTable,
    HotColumn
  },
  computed: {
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    }
  },
  async created() {
    this.getContractList()
    this.memoryEntity.fields = createMemoryField({
      fields: ['date'],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.permschange(this.curd)
  },
  watch: {
    'customerNameEntity.value'(id) {
      if (!id) return
      const item = this.customerNameEntity.options.find((item) => item.id === id)
      if (!item) return
      //   if (this.dialogStatus !== 'watch') {
      //     this.getNameCodefn(item.name)
      //   }
      this.entityForm.customerId = id
      this.entityForm.customerName = item.name
      this.entityForm.firstParty = item.name
    }
  },
  methods: {
    handleNameEntity(val) {
      this.entityForm = { ...this.entityForm, productName: val }
    },
    async getContractList() {
      let { data } = await await CustomerModel.page({ size: 999 })
      if (data.records.length) this.customerNameEntity.options = data.records
    },

    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      res.data.attachmentList.forEach((item, index) => {
        const fileType = item.uri.split('.').pop().toLowerCase()
        item.fileType = fileType
        // 添加文件类型判断
        item.isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileType)
        item.isDocument = ['doc', 'docx', 'pdf'].includes(fileType)
        item.isExcel = ['xls', 'xlsx'].includes(fileType)
      })

      console.log(res.data.attachmentList)

      this.drawer.list = res.data.attachmentList
      this.drawer.preview = res.data.attachmentList
        .filter(item => item.isImage)
        .map(item => item.uri)
      this.drawer.visible = true
    },

    async saveEntityForm(formName) {
      this.entityForm = { ...this.entityForm }
      const valid = await this.$refs[formName].validate()
      if (valid) {
        this.entityFormLoading = true
        const res = await Model.save({ ...this.entityForm })
        this.entityFormLoading = false
        if (res) {
          this.getList()
          this.resetVariant()
          this.dialogFormVisible = false
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        }
      }
    },

    async handleUpdate(item, type) {
      if (type == 'update') {
        this.dialogStatus = 'update'
      } else {
        this.dialogStatus = 'watch'
      }
      // 上传所需要的配置
      this.uploadData.refId = item.id
      const { data } = await this.model.getUploadList(item.id)
      this.customerNameEntity.value = data.customerId
      console.log(data)

      this.uploadList = data.attachmentList
      this.entityForm = { ...data }
      this.dialogFormVisible = true
    },

    //文件上传
    async handleRemoveUpload(index) {
      const { id } = this.uploadList[index]
      this.$refs.upload.attachmentDelete({ id, index })
    },
    handleUploadSuccess(res) {
      this.uploadList = [...this.uploadList, res]
      // 上传完毕后更新展示的列表和把新增的附件id塞入到
      this.entityForm.attachmentList.push({ id: res.id })
    },
    handleUploadDelete({ status, index }) {
      this.uploadList.splice(index, 1)
    },

    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogFormVisibleV = false
      this.batchChangeType = ''
      this.uploadList = [] // 清空下载列表
      this.uploadListV = []
      this.customerNameEntity.value = ''
      this.contractEntity.active = []
      this.entityForm = { ...this.model.model }
      this.entityForm.attachmentList = []
      this.entityForm.attachmentCsrList = []
    },
    // @重写方法-添加了合同搜索字段
    handleFilter(filters) {
      this.filters = { ...filters }
      this.$refs[this.curd].searchChange({ ...this.listQuery, ...filters, filter_EQS_contractId: this.contractParams.query })
    },
    // @重写方法-添加了clearContract方法
    handleFilterReset() {
      this.clearContract()
      this.$refs[this.curd].searchChange()
    },

    clearContract() {
      this.listQuery = { ...listQuery }
      this.fromDashboard = false
      this.contractActive = []
      this.contractParams.query = ''
      this.indicators = { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity }
      return false
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.form-titlV {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 11px;
    background: #2f79e8;
    height: 16px;
  }
}
.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.attachmentV {
  & > span {
    font-size: 12px;
    color: blue;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;
    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      padding: 6px 10px;
      margin-right: 5px;
      font-size: 12px;
      position: relative;
      &-link {
        font-size: 12px;
        opacity: 0.8;
      }
      .icon {
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 9;
        top: 0;
        flex: 0;
      }
    }
  }
}
::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}
::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  color: #fff;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 5) {
  border-left: none;
  border-right: none;
}
.excessive {
  width: 100%;
  color: red;
}

.attachment-container {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.attachment-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
  }
}

.image-preview {
  width: 100%;

  .preview-image {
    width: 100%;
    height: 150px;
  }

  .file-name {
    margin-top: 10px;
    font-size: 12px;
    color: #606266;
    word-break: break-all;
  }
}

.document-preview,
.excel-preview,
.other-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .file-info {
    margin-top: 10px;
    width: 100%;

    .file-name {
      font-size: 13px;
      color: #303133;
      word-break: break-all;
    }

    .file-type {
      font-size: 12px;
      color: #909399;
      margin-top: 5px;
    }

    .file-meta {
      font-size: 11px;
      color: #67c23a;
      margin-top: 5px;
      font-style: italic;
    }
  }
}

.excel-preview {
  .file-meta {
    background-color: #f0f9eb;
    padding: 2px 5px;
    border-radius: 3px;
  }
}
</style>
