<template>
  <div class="app-container">
    <!-- <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
          @import="handleImpiort"   :showImport="perms[`${curd}:addimport`]||false"     :showAdd="perms[`${curd}:save`]||false" :showRefresh="perms[`${curd}:Refresh`]||false" @Refresh="Refreshfn" /> -->

    <!-- <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" title="供应商" otherHeight="120"
            :defaultsort="{ prop:'createDate', order: 'descending'}" :span-method="arraySpanMethod">
      <el-table-column label="合同" slot="contractId" prop="contractId" width="300" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          {{scope.row.contractName}} / {{scope.row.contractCode}}
        </template>
      </el-table-column>
    </s-curd> -->
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" @Refresh="Refreshfn" />
    <div class="table-containerV">
      <el-table :data="currentTableData" :span-method="arraySpanMethod" border class="table" :header-cell-style="headClass"
                show-summary :summary-method="getSummaries" style="position: relative;" height="78vh">

        <!-- <el-table-column prop="customerName" label="客户名称" align="center" /> -->
        <!-- <el-table-column label="合同" prop="contractId" :show-overflow-tooltip="true" width="120">
          <template slot-scope="scope">
            <div>{{scope.row.displayName}}</div>
          </template>
        </el-table-column> -->

        <el-table-column label="合同" prop="contractId" width="120" class="testht">
          <template slot-scope="scope">
            {{scope.row.displayName}}
          </template>
        </el-table-column>

        <el-table-column label="合同" prop="customerName" width="120" class="testht">
          <template slot-scope="scope">
            {{scope.row.customerName}}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="secondParty" label="卖方" width="120" /> -->

        <el-table-column prop="secondParty" label="卖方" width="120">
          <template slot-scope="scope">
            <div class="active-name" style="position: relative;">
              <span v-if="scope.row['secondParty'] ==='合计'" style="color:red">{{ scope.row['secondParty'] }}</span>
              <span v-else>{{ scope.row['secondParty'] }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- <el-table-column label="买方" prop="customerId" :show-overflow-tooltip="true" width="120">
          <template slot-scope="scope">
            <div>{{scope.row.customerName}}</div>
          </template>
        </el-table-column> -->

        <el-table-column prop="productName" label="产品名称">
          <template slot-scope="scope">{{scope.row.productName}}</template>
        </el-table-column>

        <el-table-column prop="linkman" label="负责人">
          <template slot-scope="scope">{{scope.row.linkman}}</template>
        </el-table-column>
        <el-table-column prop="actualMoneySum" label="收款" />
        <el-table-column prop="refundMoney" label="退款" />
        <!-- // 这个跟供应商里面的正好相反 -->
        <el-table-column prop="amount" label="原发吨数" />
        <el-table-column prop="receiveAmount" label="实收吨数" />

        <el-table-column prop="price" label="单价" />

        <!-- <el-table-column prop="productName" label="品名" align="center" /> -->

        <!-- <el-table-column prop="actualMoneySum" label="收款" align="center" /> -->

        <el-table-column prop="preSettleMoney" label="预结算金额" />

        <!-- <el-table-column prop="settledMoney" label="已结清金额" align="center" /> -->

        <el-table-column prop="settleMoney" label="结算金额" />
        <el-table-column prop="settleAmount" label="结算吨数" />
        <el-table-column prop="sendAmountSettle" label="结算原发吨数" width="120" />
        <el-table-column prop="unSettledMoney" label="未结算金额" />
        <el-table-column prop="leftMoneySum" label="余额" />
        <el-table-column prop="leftAmountSum" label="余吨" />

        <!-- <el-table-column prop="carriageRemarks" label="运输情况" align="center" width="160">
          <template slot-scope="scope">
            <span v-if="!scope.row.active.carriageRemarks" @click="handleActive(scope.row,'carriageRemarks')">
              <i class="el-icon-edit"></i> {{scope.row.carriageRemarks}}</span>
            <el-input v-else placeholder="请输入内容" v-model="scope.row.carriageRemarks" clearable size="small"
                      @blur="handleBlurre(scope.row,'carriageRemarks')" />
          </template>
        </el-table-column> -->

        <!-- <el-table-column prop="carriageRemarks" label="运输情况" align="center" width="110">
          <template slot-scope="scope">
            <div style="padding: 0 10px; box-sizing: border-box;">
              <el-select v-model="scope.row.carriageRemarks" clearable placeholder="请选择" @change="selectChangeisBill(scope.row)">
                <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="开票情况" prop="isBill" align="center" width="110">
          <template slot-scope="scope">
            <div style="padding:0 10px">
              <el-select v-model="scope.row.isBill" clearable placeholder="请选择" @change="selectChangeisBill(scope.row)">
                <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
              </el-select>
            </div>
          </template>
        </el-table-column> -->

        <!-- <el-table-column prop="isSettle" label="是否结算" align="center">
          <template slot-scope="scope" v-if="scope.row.isBill!=''">
            <el-tag :type="(scope.row.isSettle=='Y' ? 'success':(scope.row.applyType == 'N' ? 'danger' : ''))" size="mini">
              {{scope.row.isSettle == 'Y' ? '是' : (scope.row.applyType == 'N' ? '否' :  '') }}
            </el-tag>
          </template>
        </el-table-column> -->

        <!-- 
        <el-table-column prop="remarks" label="备注" width="160">
          <template slot-scope="scope">
            <span v-if="!scope.row.active.remarks" @click="handleActive(scope.row,'remarks')">
              <i class="el-icon-edit"></i> {{scope.row.remarks}}</span>
            <el-input v-else placeholder="请输入内容" v-model="scope.row.remarks" clearable @input="setmoney3(scope.row,'remarks')"
                      size="small" @blur="handleBlurre(scope.row,'remarks')" />
          </template>
        </el-table-column> -->

        <!-- <el-table-column label="合同名称" prop="contractName" align="center" width="190" class="testht">
          <template slot-scope="scope">
            <div>{{scope.row.contractName}}</div>
          </template>
        </el-table-column> -->

        <el-table-column prop="sbDate" label="更新时间" width="150" />
        <!-- <el-table-column prop="remarks" label="备注" align="center" /> -->

        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" color="#FF9639" @click="settleUpdate(scope.row)">结清</el-tag>
            <!-- <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row.id)" v-if="perms[`${curd}:update`]||false">编辑</el-tag> -->
            <!-- <el-tag class="opt-btn" color="#33CAD9" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除 -->
            <!-- </el-tag> -->
          </template>
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/finance/sbCustomerDay'
import { sbCustomerDayOption } from '@/const'
import productModel from '@/model/product/productList'
// import { chooseContract } from '@/api/quality'
import { getCustomerContract } from '@/api/quality'
export default {
  name: 'sbCustomerDay',
  mixins: [Mixins],
  data() {
    return {
      curd: 'sbCustomerDay',
      model: Model,
      entityForm: { ...Model.model },
      entityFormV: {
        date: new Date(new Date().getTime()).Format('yyyy-MM-dd')
      },
      nameEntity: { options: [], active: '' },
      limitNum: 50,
      filterOption: { ...sbCustomerDayOption },
      isshow: false,
      // 合同
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      dialogFormVisible: false,
      changeForm: {},
      changeentityFormLoading: false,

      currentTableData: [],
      spanArr: [],
      pos: '',
      filters: {},
      isBilllist: [
        { label: '是', type: 'Y' },
        { label: '否', type: 'N' }
      ]
    }
  },
  watch: {
    'entityForm.productName'(name) {
      if (name) {
        const item = this.nameEntity.options.find((item) => item.name === name)
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.entityForm.coalType = item.coalCategory || ''
        this.entityForm.coalCategory = item.type
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
        this.entityForm.coalCategory = ''
      }
    }
  },
  created() {
    // this.permsActionLsit(this.curd)
    this.getName()
    this.getContract()
    this.getList()
  },
  computed: {},
  methods: {
    getSummaries(param) {
      const { columns, data } = param
      const average = []
      columns.forEach((column, index) => {
        if (index === 0) {
          average[index] = '合计'
          return
        }
        if (index === 1 || index === 2 || index === 3 || index === 8 || index === 16 || index === 17) {
          average[index] = ''
          return
        }
        // const values = data.map((item) => {
        //   return Number(item[column.property])
        // })

        const values = data.map((item) => {
          if (item.secondParty != '合计') {
            return Number(item[column.property])
          }
        })
        if (!values.every((value) => isNaN(value))) {
          average[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          average[index] = parseFloat(average[index].toFixed(2))
        } else {
          average[index] = ''
        }
      })
      return average
    },
    async selectChangeisBill(row) {
      try {
        await this.model.save({ ...row })
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },
    changeAll(row) {
      row._all = !row._all
    },
    handleUpdate(row) {
      this.changeAll(row)
      this.changeCurrent(row, true) // 编辑全部
    },
    async settleUpdate(row) {
      // console.log(row.id)
      // console.log(row)
      this.$confirm('是否确认结清？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await this.model.settlef({ id: row.id })
          if (res) {
            this.$message({ type: 'success', message: '结清成功!' })
            this.getList()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '取消' }))
    },
    changeCurrent(row, target) {
      if (target === true) {
        for (const [k, v] of Object.entries(row.active)) {
          row.active[k] = !v
        }
      } else if (target === false) {
        for (const key of Object.keys(row.active)) {
          row.active[key] = false
        }
      } else {
        row.active[target] = !row.active[target]
      }
    },
    handleBlurre(row, target) {
      this.changeCurrent(row, target) // 失去事件触发改变时关闭target的active
      const status = this.isOpenSave(row) // 判断当前事件是否全部关闭
      if (status) return // 为真说明还有blur未关闭

      // for (const key of Object.keys(row.bak)) {
      //   if (row.freightPrice) {
      //     row.freighMoney = parseInt(row.freightPrice * row.weight)
      //   }
      //   if (row.bak[key] + '' !== row[key] + '') return // 判断两个值是否不相等 不相等则直接返回
      // }
      this.changeAll(row)
      this.operationRow(row, true) // 取消时关闭所有的active
      setTimeout(() => {
        row._all = false
      }, 300) // 如果上述条件都成立则开回到最初的模式
    },
    setmoney1(row, target) {
      if (row.freightPrice) {
        row.freighMoney = parseInt(row.freightPrice * row.weight)
        row.active['freighMoney'] = false
      }
    },
    setmoney2(row, target) {
      if (row.freightPrice) {
        row.freighMoney = parseInt(row.freightPrice * row.weight)
        row.active['freightPrice'] = false
      }
    },
    setmoney3(row, target) {
      // row.active['remarks'] = false
    },
    isOpenSave(row) {
      let status = false
      for (const v of Object.values(row.active)) {
        if (v) {
          status = true
          break
        }
      }
      return status
    },
    /**
     * 双击时改变为input狂
     * @target string|true 用于判断是操作一个还是多个
     */
    handleActive(row, target) {
      row._all = true
      this.changeCurrent(row, target)
    },

    async operationRow(row, action) {
      if (action) {
        try {
          await this.model.save({ ...row })
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        } catch (e) {
          this.$message({ message: '保存失败', type: 'warning' })
        }
        this.getList()
      } else {
        for (const key of Object.keys(row.bak)) {
          if (action) {
            // ---------------------- 可写接口
            row.bak[key] = row[key] // 保存就时把当前key赋给bak
          } else {
            row[key] = row.bak[key] // 取消就是bak值赋给当前key
          }
          row.active[key] = false // 更具Key 将active都初始化
        }
      }
    },

    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      // if (
      //   // 去掉已结清的合并列
      //   columnIndex === 3 ||
      //   columnIndex === 4 ||
      //   columnIndex === 12 ||
      //   columnIndex === 13 ||
      //   columnIndex === 14
      // ) {
      if (
        //去掉已结清的合并列
        columnIndex === 0 ||
        columnIndex === 1 ||
        columnIndex === 2 ||
        columnIndex === 5 ||
        columnIndex === 6 ||
        columnIndex === 14 ||
        columnIndex === 15 ||
        columnIndex === 16 ||
        columnIndex === 18
      ) {
        let spanArr = this.getSpanArr(this.currentTableData)
        // 页面列表上 表格合并行 -> 第几列(从0开始)
        // 需要合并多个单元格时 依次增加判断条件即可
        // 数组存储的数据 取出
        const _row = spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      } else {
        //不可以return {rowspan：0， colspan: 0} 会造成数据不渲染， 也可以不写else，eslint过不了的话就返回false
        return false
      }
    },

    // 处理合并行的数据
    getSpanArr: function (data) {
      let spanArr = []
      let pos = ''
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          spanArr.push(1)
          pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].contractId === data[i - 1].contractId) {
            spanArr[pos] += 1
            spanArr.push(0)
          } else {
            spanArr.push(1)
            pos = i
          }
        }
      }
      return spanArr
    },
    // getSpanArr2: function (data) {
    //   let spanArr = []
    //   let pos = ''
    //   for (let i = 0; i < data.length; i++) {
    //     if (i === 0) {
    //       spanArr.push(1)
    //       pos = 0
    //     } else {
    //       // console.log(data[i].contractId)
    //       // 判断当前元素与上一个元素是否相同
    //       if (data[i].contractId === data[i - 1].contractId) {
    //         spanArr[pos] += 1
    //         spanArr.push(0)
    //       } else {
    //         spanArr.push(1)
    //         pos = i
    //       }
    //     }
    //   }
    //   return spanArr
    // },

    // async getList() {
    //     this.tableData = []
    //     this.currentTableData = []
    //     this.locationList = []
    //     this.locationActive = 0
    //     const { data } = await Model.page(this.filters)
    //     if (data.length > 0) {
    //       this.tableData = data
    //       let cache = {} //存储的是键是zhuanye 的值，值是zhuanye 在indeces中数组的下标
    //       let indices = [] //数组中每一个值是一个数组，数组中的每一个元素是原数组中相同zhuanye的下标
    //       data.map((item, index) => {
    //         let customerId = item.customerId
    //         let _index = cache[customerId]
    //         if (_index !== undefined) {
    //           indices[_index].push(index)
    //         } else {
    //           cache[customerId] = indices.length
    //           indices.push([index])
    //         }
    //       })
    //       let result = []
    //       indices.map((item) => {
    //         item.map((index) => {
    //           result.push(data[index])
    //         })
    //       })
    //       this.currentTableData = result
    //       // this.currentTableData = data.records
    //       // this.sortArr(this.currentTableData, 'linkman')
    //       // data.forEach((item) => {
    //       //   this.locationList.push({ label: item.productName })
    //       // })
    //     }
    //   },

    async getList() {
      this.currentTableData = []
      this.locationList = []
      this.locationActive = 0
      const { data } = await Model.page(this.filters)
      // if (data.length > 0) {
      //   let cache = {} //存储的是键是zhuanye 的值，值是zhuanye 在indeces中数组的下标
      //   let indices = [] //数组中每一个值是一个数组，数组中的每一个元素是原数组中相同zhuanye的下标
      //   data.map((item, index) => {
      //     let customerId = item.customerId
      //     let _index = cache[customerId]
      //     if (_index !== undefined) {
      //       indices[_index].push(index)
      //     } else {
      //       cache[customerId] = indices.length
      //       indices.push([index])
      //     }
      //   })
      //   let result = []
      //   indices.map((item) => {
      //     item.map((index) => {
      //       result.push(data[index])
      //     })
      //   })
      //   this.currentTableData = result
      // }

      if (data.length > 0) {
        let newArr = data.reduce((pre, cur) => {
          if (pre.length) {
            let index = pre.findIndex((item) => item[0].secondParty == cur.secondParty)
            if (index > -1) {
              pre[index].push(cur)
            } else {
              pre.push([cur])
            }
          } else {
            pre.push([cur])
          }
          return pre
        }, [])
        newArr.forEach((item) => {
          let actualMoneySumtotal = 0
          let refundMoneytotal = 0
          let amounttotal = 0
          let receiveAmounttotal = 0
          let preSettleMoneytotal = 0
          let settleMoneytotal = 0
          let settleAmounttotal = 0
          let sendAmountSettletotal = 0
          let unSettledMoneytotal = 0
          let leftMoneySumtotal = 0
          let leftAmountSumtotal = 0
          item.forEach((item2) => {
            actualMoneySumtotal += Number(item2.actualMoneySum)
            refundMoneytotal += Number(item2.refundMoney)
            amounttotal += Number(item2.amount)
            receiveAmounttotal += Number(item2.receiveAmount)
            preSettleMoneytotal += Number(item2.preSettleMoney)
            settleMoneytotal += Number(item2.settleMoney)
            settleAmounttotal += Number(item2.settleAmount)
            sendAmountSettletotal += Number(item2.sendAmountSettle)
            unSettledMoneytotal += Number(item2.unSettledMoney)
            leftMoneySumtotal += Number(item2.leftMoneySum)
            leftAmountSumtotal += Number(item2.leftAmountSum)
          })
          let obj = {
            contractId: '',
            customerName: '',
            secondParty: '合计',
            actualMoneySum: actualMoneySumtotal.toFixed(2),
            refundMoney: refundMoneytotal.toFixed(2),
            amount: amounttotal.toFixed(2),
            receiveAmount: receiveAmounttotal.toFixed(2),
            preSettleMoney: preSettleMoneytotal.toFixed(2),
            settleMoney: settleMoneytotal.toFixed(2),
            settleAmount: settleAmounttotal.toFixed(2),
            sendAmountSettle: sendAmountSettletotal.toFixed(2),
            unSettledMoney: unSettledMoneytotal.toFixed(2),
            leftMoneySum: leftMoneySumtotal.toFixed(2),
            leftAmountSum: leftAmountSumtotal.toFixed(2)
          }
          item.push(obj)
        })
        let subArr = newArr.reduce((pre, cur) => {
          return pre.concat(cur)
        }, [])
        this.currentTableData = subArr
      }
    },

    sortArr(arr, str) {
      let _arr = [],
        _t = [],
        // 临时的变量
        _tmp

      // 按照特定的参数将数组排序将具有相同值得排在一起
      arr = arr.sort(function (a, b) {
        let s = a[str],
          t = b[str]
        return s < t ? -1 : 1
      })
      if (arr.length) {
        _tmp = arr[0][str]
      }
      // console.log( arr );
      // 将相同类别的对象添加到统一个数组
      for (let i in arr) {
        if (arr[i][str] === _tmp) {
          // console.log(_tmp)
          _t.push(arr[i])
        } else {
          _tmp = arr[i][str]
          _arr.push(_t)
          _t = [arr[i]]
        }
      }
      // 将最后的内容推出新数组
      _arr.push(_t)
      // console.log(_arr)
      return _arr
    },

    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },
    handleFilter(filters) {
      this.filters = filters
      this.getList()
    },
    handleFilterReset() {
      this.filters = {}
      this.getList()
    },
    // async Refreshfn() {
    //   // this.dialogFormVisible = true
    //   const { data } = await this.model.refresh()
    //   this.$message({ showClose: true, message: '刷新成功', type: 'success' })
    //   this.$refs[this.curd].searchChange({ ...this.listQuery })
    // },

    // async RefreshForm() {
    //   this.changeentityFormLoading = true
    //   // let sbDate = new Date(new Date().getTime()).Format('yyyy-MM-dd')
    //   let date = this.entityFormV.date
    //   try {
    //     const { data } = await this.model.refresh({ date })
    //     // if (data == null) {
    //     this.$message({ showClose: true, message: '刷新成功', type: 'success' })
    //     this.$refs[this.curd].searchChange({ ...this.listQuery })
    //     this.changeentityFormLoading = false
    //     // } else {
    //     //   this.$message({ showClose: true, message: '出错了', type: 'error' })
    //     // }
    //   } catch (error) {}
    //   this.changeentityFormLoading = false
    //   this.dialogFormVisible = false
    // },

    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) {}
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              this.contractName = item.customerName
              this.contractCode = item.customerName
              // let obj = {
              //   contractId: value,
              //   contractName: item.supplierName,
              // }
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    // 获取合同选择接口
    async getContract() {
      const { data } = await getCustomerContract()
      // 供应商名称返回id用来查询
      this.contractEntity.options = this.formatContract({ data, key: { customerName: 'displayName', customerId: 'id' } })
    },
    async handleAreaChange(nowVvlue, row) {
      const form = this.filterContractItem(nowVvlue, 'contractEntity')
      row.contractId = nowVvlue[1]
      row.contractName = form.customerName
      row.contractCode = form.customerName
      try {
        await this.model.save({ ...row })
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    }
  }
}
</script>

<style scoped>
.search-component >>> .el-input__inner {
  border: none;
}
</style>

<style lang="scss" scoped>
@import '@/styles/router-page.scss';
::v-deep .el-table__footer-wrapper {
  position: absolute;
  z-index: 9;
  left: 0;
  bottom: 8px;
}

::v-deep .el-table .cell {
  line-height: 18px;
}
::v-deep .el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border-bottom: 1px solid #ccc;
}

::v-deep .el-table--border .el-table__cell,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right: 1px solid #ccc;
}

.table-containerV {
  // height: 80vh;
  height: 90vh;
  padding: 20px;
  background: #fff;
  box-sizing: border-box;
  padding-bottom: 30px;
}
::v-deep .el-table td > .cell div {
  font-size: 12px;
  line-height: 21px;
  // height: 60px !important;
}

.smart-search {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  width: 47%;
  margin-bottom: 5px;
  border-radius: 3px;

  .prepend {
    .el-dropdown {
      cursor: pointer;
    }

    .el-icon-arrow-down {
      width: 26px;
      text-align: center;
    }

    .search-key {
      display: inline-block;
      height: 30px;
      line-height: 30px;
    }
  }

  .search-component {
    flex: 1;
    .el-input {
      width: 100%;
      height: 30px;
      line-height: 30px;
      .el-input__inner {
        border: none;
      }
      & .is-focus {
        .el-input__inner {
          border-color: #dcdfe6 !important;
        }
      }
    }
  }
}
.form-layoutV {
  .el-row {
    margin-bottom: 10px;
  }
}
.tipbox {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  .tipline {
    margin-left: 20px;
    .redtext {
      font-size: 1.5rem;
      color: red;
    }
  }
}
// .bigbox {
//   width: 100%;
//   padding: 15px 15px 0 15px;
//   background-color: #fff;
//   .btnbox {
//     width: 100%;
//     display: flex;
//     flex-direction: row;
//     align-items: center;
//   }
// }
.contentbox {
  width: 500px;
  margin: 100px auto 0 auto;
  font-size: 12px;
  .line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tsline {
    float: right;
    margin-top: 40px;
  }
  .inputbox {
    width: 100px;
    margin-bottom: 10px;
    border-bottom: solid 1px #ccc;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
<style scoped>
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
/* .dialog-footer >>> .dialog-footer-btns {
  border: 1px solid #2f79e8;
  color: #2f79e8;
} */
</style>
