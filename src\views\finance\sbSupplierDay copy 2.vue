<template>
  <div class="app-container">

    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" @Refresh="Refreshfn" />

    <!-- <panel title="品名" type="location" :active="locationActive" :list='locationList' @locationChange="handleToLocation" /> -->

    <div class="table-containerV">
      <el-table class="table teshutable" :data="currentTableData" :span-method="arraySpanMethod" border
                :header-cell-style="headClass" height="75vh">

        <!-- :show-overflow-tooltip="true" -->
        <el-table-column label="合同" prop="contractId" width="200" class="testht">
          <template slot-scope="scope">
            {{scope.row.displayName}}
          </template>
        </el-table-column>

        <el-table-column label="卖方" prop="contractId" width="200" class="testht">
          <template slot-scope="scope">
            {{scope.row.supplierName}}
          </template>
        </el-table-column>

        <el-table-column prop="productName" label="产品名称">
          <template slot-scope="scope">{{scope.row.productName}}</template>
        </el-table-column>

        <el-table-column prop="linkman" label="供应组">
          <template slot-scope="scope">{{scope.row.linkman}}</template>
        </el-table-column>

        <el-table-column prop="actualMoneySum" label="付款" />
        <el-table-column prop="refundMoney" label="退款" />
        <!-- // 这个跟客户台账里面的正好相反 -->
        <el-table-column prop="amount" label="实收吨数" />
        <el-table-column prop="sendAmount" label="原发吨数" />
        <!-- <el-table-column prop="productName" label="品名" align="center" /> -->

        <el-table-column prop="price" label="单价" />

        <el-table-column prop="preSettleMoney" label="预结算金额" />

        <!-- <el-table-column prop="settledMoney" label="已结清金额" align="center" /> -->

        <el-table-column prop="settleMoney" label="结算金额" />
        <el-table-column prop="settleAmount" label="结算吨数" />

        <el-table-column prop="sendAmountSettle" label="结算原发吨数" />

        <el-table-column prop="recvAmountSettle" label="结算实收吨数" />

        <el-table-column prop="unSettledMoney" label="未结算金额" />
        <el-table-column prop="leftMoneySum" label="余额" />
        <el-table-column prop="leftAmountSum" label="余吨" />
        <!-- <el-table-column prop="refundMoney" label="退款" align="center" /> -->
        <!-- <el-table-column prop="carriageRemarks" label="运输情况" align="center" width="160">
          <template slot-scope="scope">
            <span v-if="!scope.row.active.carriageRemarks" @click="handleActive(scope.row,'carriageRemarks')">
              <i class="el-icon-edit"></i> {{scope.row.carriageRemarks}}</span>
            <el-input v-else placeholder="请输入内容" v-model="scope.row.carriageRemarks" clearable size="small"
                      @blur="handleBlurre(scope.row,'carriageRemarks')" />
          </template>
        </el-table-column> -->

        <!-- <el-table-column prop="carriageRemarks" label="运输情况" align="center" width="110">
          <template slot-scope="scope">
            <div style="padding: 0 10px; box-sizing: border-box;">
              <el-select v-model="scope.row.carriageRemarks" clearable placeholder="请选择" @change="selectChangeisBill(scope.row)">
                <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="开票情况" prop="isBill" align="center" width="110">
          <template slot-scope="scope">
            <div style="padding: 0 10px; box-sizing: border-box;">
              <el-select v-model="scope.row.isBill" clearable placeholder="请选择" @change="selectChangeisBill(scope.row)">
                <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
              </el-select>
            </div>
          </template>
        </el-table-column> -->

        <!-- <el-table-column label="合同名称" prop="contractName" align="center" width="190" class="testht">
          <template slot-scope="scope">
            <div>{{scope.row.contractName}}</div>
          </template>
        </el-table-column> -->

        <el-table-column prop="remarks" label="备注" width="160">
          <template slot-scope="scope">
            <span v-if="!scope.row.active.remarks" @click="handleActive(scope.row,'remarks')">
              <i class="el-icon-edit"></i> {{scope.row.remarks}}</span>
            <el-input v-else placeholder="请输入内容" v-model="scope.row.remarks" clearable @input="setmoney3(scope.row,'remarks')"
                      size="small" @blur="handleBlurre(scope.row,'remarks')" />
          </template>
        </el-table-column>

        <!-- <el-table-column prop="isSettle" label="是否结算" align="center">
          <template slot-scope="scope" v-if="scope.row.isBill!=''">
            <el-tag :type="(scope.row.isSettle=='Y' ? 'success':(scope.row.applyType == 'N' ? 'danger' : ''))" size="mini">
              {{scope.row.isSettle == 'Y' ? '是' : (scope.row.applyType == 'N' ? '否' :  '') }}
            </el-tag>
          </template>
        </el-table-column> -->

        <el-table-column prop="sbDate" label="更新时间" align="center" width="150" />

        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" color="#FF9639" @click="settleUpdate(scope.row)">结清</el-tag>
            <!-- <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row.id)">编辑</el-tag> -->
            <!-- <el-tag class="opt-btn" color="#33CAD9" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除
            </el-tag> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import { findByKeyword } from '@/api/stock'
// import Model from '@/model/production/productionList'
import Model from '@/model/finance/sbSupplierDay'
import productModel from '@/model/product/productList'
import { getCustomerContract } from '@/api/quality'
import Cache from '@/utils/cache'
import { sbSupplierDayOption } from '@/const'
const form = {
  _id: '1',
  shovelCount: '',
  shovelWeight: '',
  weight: '',
  productName: '',
  productId: '',
  productCode: ''
}
export default {
  name: 'sbSupplierDay',
  mixins: [Mixins],
  data() {
    return {
      curd: 'sbSupplierDay',
      model: Model,
      form: { ...form },
      listV: [{ ...form }],
      filterOption: { ...sbSupplierDayOption },
      // filterOption: {
      //   showMore: true,
      //   columns: [
      //     {
      //       prop: 'productionDate',
      //       component: 'date-select',
      //       defaultValue: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      //       resetClearDefault: true,
      //       filter: 'productionDate',
      //       props: {
      //         placeholder: '请选择日期',
      //         'picker-options': {
      //           cellClassName: (time) => {
      //             const flag = this.dateList.includes(time.Format('yyyy-MM-dd'))
      //             if (flag) return 'background'
      //           },
      //         },
      //       },
      //     },
      //   ],
      // },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      entityForm: { ...Model.model },
      rules: {
        productionDate: { required: true, message: '请选择日期', trigger: 'blur' },
        productName: { required: true, message: '请选择品名', trigger: 'change' },
        pileCount: { required: true, message: '请输入堆数', trigger: 'blur' },
        weight: { required: true, message: '请输入吨数', trigger: 'blur' }
      },
      nameEntity: { options: [], active: '' },
      memoryEntity: { fields: {}, triggered: false },
      actions: [],
      tableData: [],
      nameItemEntity: [],
      // filters: { productionDate: new Date().Format('yyyy-MM-dd') },
      filters: {},
      timeValue: [],
      timeValue1: [],
      locationList: [],
      locationActive: 0,
      currentTableData: [],
      screen: { full: false },
      draft: { open: false, entityForm: { ...Model.model }, list: [{ ...form }] },
      productionDraft: { entityForm: { ...Model.model }, list: [{ ...form }] },
      dateList: [],

      spanArr: [],
      pos: '',
      isBilllist: [
        { label: '是', type: 'Y' },
        { label: '否', type: 'N' }
      ]
    }
  },
  created() {
    this.getName()
    this.getContract()
    this.getList()
    // this.getFindByKeyword()
    this.getDateList()
  },
  watch: {
    // 'entityForm.startTime'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.startTime1'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.startTime2'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.stopTime'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.stopTime1'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.stopTime2'(v) {
    //   this.handlePickerChange(false)
    // },
    'entityForm.contractId'(id) {
      const value = this.filterContractItem(id, 'contractEntity')
      this.contractEntity.active = value
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      if (this.dialogStatus === 'create') this.entityForm.productName = form.productName || ''
      this.entityForm.contractCode = form.customerName || ''
      this.entityForm.contractId = form.customerId || ''
      this.entityForm.contractName = form.name || ''
    },

    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code || ''
        this.entityForm.productId = item.id || ''
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
      }
    }
  },
  methods: {
    async selectChangeisBill(row) {
      try {
        await this.model.save({ ...row })
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },

    changeAll(row) {
      row._all = !row._all
    },
    handleUpdate(row) {
      this.changeAll(row)
      this.changeCurrent(row, true) // 编辑全部
    },
    changeCurrent(row, target) {
      if (target === true) {
        for (const [k, v] of Object.entries(row.active)) {
          row.active[k] = !v
        }
      } else if (target === false) {
        for (const key of Object.keys(row.active)) {
          row.active[key] = false
        }
      } else {
        row.active[target] = !row.active[target]
      }
    },
    handleBlurre(row, target) {
      this.changeCurrent(row, target) // 失去事件触发改变时关闭target的active
      const status = this.isOpenSave(row) // 判断当前事件是否全部关闭
      if (status) return // 为真说明还有blur未关闭
      // for (const key of Object.keys(row.bak)) {
      //   if (row.freightPrice) {
      //     row.freighMoney = parseInt(row.freightPrice * row.weight)
      //   }
      //   if (row.bak[key] + '' !== row[key] + '') return // 判断两个值是否不相等 不相等则直接返回
      // }
      this.changeAll(row)
      this.operationRow(row, true) // 取消时关闭所有的active
      setTimeout(() => {
        row._all = false
      }, 300) // 如果上述条件都成立则开回到最初的模式
    },
    setmoney1(row, target) {
      if (row.freightPrice) {
        row.freighMoney = parseInt(row.freightPrice * row.weight)
        row.active['freighMoney'] = false
      }
    },
    setmoney2(row, target) {
      if (row.freightPrice) {
        row.freighMoney = parseInt(row.freightPrice * row.weight)
        row.active['freightPrice'] = false
      }
    },
    setmoney3(row, target) {
      // row.active['remarks'] = false
    },
    isOpenSave(row) {
      let status = false
      for (const v of Object.values(row.active)) {
        if (v) {
          status = true
          break
        }
      }
      return status
    },
    /**
     * 双击时改变为input狂
     * @target string|true 用于判断是操作一个还是多个
     */
    handleActive(row, target) {
      row._all = true
      this.changeCurrent(row, target)
    },

    async operationRow(row, action) {
      if (action) {
        try {
          await this.model.save({ ...row })
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        } catch (e) {
          this.$message({ message: '保存失败', type: 'warning' })
        }
        this.getList()
      } else {
        for (const key of Object.keys(row.bak)) {
          if (action) {
            // ---------------------- 可写接口
            row.bak[key] = row[key] // 保存就时把当前key赋给bak
          } else {
            row[key] = row.bak[key] // 取消就是bak值赋给当前key
          }
          row.active[key] = false // 更具Key 将active都初始化
        }
      }
    },

    //  handleSave(row) {
    //     this.changeAll(row)
    //     this.operationRow(row, true) // 取消时关闭所有的active
    //   },
    //   handleCancel(row) {
    //     row._all = false
    //     this.operationRow(row, false) // 取消时关闭所有的active
    //   },

    async getDateList(params = {}) {
      try {
        const res = await this.model.getDateList(params)
        if (res.data && res.data.length) {
          this.dateList = res.data
        } else {
          this.dateList = []
        }
      } catch (error) {
        this.dateList = []
      }
    },
    handleFullScreen() {
      this.screen.full = !this.screen.full
      const el = this.$refs.dialogStatus.$el
      const el_body = el.querySelector('.el-dialog__body')
      el_body.style.height = this.screen.full ? '85vh' : '70vh'
    },
    // handleToLocation(index) {
    //   this.locationActive = index
    //   this.currentTableData = this.tableData[this.locationActive].mixCoalVoList
    //   console.log(this.currentTableData)
    //   // this.setTime()
    // },
    handleFilter(filters) {
      this.filters = filters
      this.getList()
    },
    handleFilterReset() {
      this.filters = {}
      this.getList()
    },
    // async getFindByKeyword() {
    //   const { data } = await findByKeyword()
    //   this.nameItemEntity = data
    //   console.log(data)
    // },

    async settleUpdate(row) {
      // console.log(row.id)
      // console.log(row)
      this.$confirm('是否确认结清？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await this.model.settlef({ id: row.id })
          if (res) {
            this.$message({ type: 'success', message: '结清成功!' })
            this.getList()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '取消' }))

      // this.dialogStatus = 'update'
      // const { data } = await Model.detail(id)
      // this.entityForm = { ...data }
      // this.listV = data.mixCoalItemList
      // delete this.entityForm.mixCoalItemList
      // this.dialogFormVisible = true
    },

    // tableRowClassName({ row, column, rowIndex, columnIndex }) {
    //   if (rowIndex === this.currentTableData.length - 2) {
    //     return 'orange-row'
    //   } else {
    //     return 'row-layout'
    //   }
    // },

    async getList() {
      this.tableData = []
      this.currentTableData = []
      this.locationList = []
      this.locationActive = 0
      const { data } = await Model.page(this.filters)
      if (data.length > 0) {
        this.tableData = data

        let cache = {} //存储的是键是zhuanye 的值，值是zhuanye 在indeces中数组的下标
        let indices = [] //数组中每一个值是一个数组，数组中的每一个元素是原数组中相同zhuanye的下标

        let newArr = []
        // let cache1 = {} //存储的是键是zhuanye 的值，值是zhuanye 在indeces中数组的下标
        // let indices1 = [] //数组中每一个值是一个数组，数组中的每一个元素是原数组中相同zhuanye的下标
        data.map((item, index) => {
          // console.log(item)
          let supplierId = item.supplierId
          let _index = cache[supplierId]
          if (_index !== undefined) {
            indices[_index].push(index)
          } else {
            cache[supplierId] = indices.length
            indices.push([index])
          }
        })
        let result = []
        indices.map((item, index) => {
          item.map((index) => {
            result.push(data[index])
          })
        })
        // console.log(result)
        this.currentTableData = result
      }
    },

    sortArr(arr, str) {
      // console.log(arr)
      let _arr = [],
        _t = [],
        // 临时的变量
        _tmp
      // 按照特定的参数将数组排序将具有相同值得排在一起
      arr = arr.sort(function (a, b) {
        let s = a[str],
          t = b[str]
        return s < t ? -1 : 1
      })
      if (arr.length) {
        _tmp = arr[0][str]
      }
      // console.log( arr );
      // 将相同类别的对象添加到统一个数组
      for (let i in arr) {
        if (arr[i][str] === _tmp) {
          _t.push(arr[i])
        } else {
          _tmp = arr[i][str]
          _arr.push(_t)
          _t = [arr[i]]
        }
      }
      // 将最后的内容推出新数组
      _arr.push(_t)
      return _arr
    },

    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      // if (
      //   //去掉已结清的合并列
      //   columnIndex === 3 ||
      //   columnIndex === 4 ||
      //   columnIndex === 13 ||
      //   columnIndex === 14 ||
      //   columnIndex === 15
      // ) {
      if (
        //去掉已结清的合并列
        columnIndex === 1 ||
        columnIndex === 4 ||
        columnIndex === 5 ||
        columnIndex === 14 ||
        columnIndex === 15 ||
        columnIndex === 16
      ) {
        let spanArr = this.getSpanArr(this.currentTableData)
        // 页面列表上 表格合并行 -> 第几列(从0开始)
        // 需要合并多个单元格时 依次增加判断条件即可
        // 数组存储的数据 取出
        const _row = spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      } else if (columnIndex === 0) {
        let spanArr = this.getSpanArr2(this.currentTableData)
        // 页面列表上 表格合并行 -> 第几列(从0开始)
        // 需要合并多个单元格时 依次增加判断条件即可
        // 数组存储的数据 取出
        const _row = spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      } else {
        //不可以return {rowspan：0， colspan: 0} 会造成数据不渲染， 也可以不写else，eslint过不了的话就返回false
        return false
      }
    },

    // 处理合并行的数据
    getSpanArr: function (data) {
      let spanArr = []
      let pos = ''
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          spanArr.push(1)
          pos = 0
        } else {
          // console.log(data[i].contractId)
          // 判断当前元素与上一个元素是否相同
          if (data[i].contractId === data[i - 1].contractId) {
            spanArr[pos] += 1
            spanArr.push(0)
          } else {
            spanArr.push(1)
            pos = i
          }
        }
      }
      return spanArr
    },

    getSpanArr2: function (data) {
      let spanArr = []
      let pos = ''
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          spanArr.push(1)
          pos = 0
        } else {
          // console.log(data[i].contractId)
          // 判断当前元素与上一个元素是否相同
          if (data[i].contractId === data[i - 1].contractId) {
            spanArr[pos] += 1
            spanArr.push(0)
          } else {
            spanArr.push(1)
            pos = i
          }
        }
      }
      return spanArr
    },

    // getSpanArr(data) {
    //   let dataList = []
    //   // data.forEach((listItem) => {
    //   //   let flag = undefined
    //   //   dataList.some((dataListItem) => {
    //   //     if (dataListItem.length > 0) {
    //   //       if (dataListItem[0].linkman === listItem.linkman) {
    //   //         flag = dataListItem
    //   //         return true
    //   //       }
    //   //     }
    //   //   })
    //   //   if (typeof flag !== 'undefined') {
    //   //     flag.push(listItem)
    //   //   } else {
    //   //     dataList.push([listItem])
    //   //   }
    //   //   flag = undefined
    //   // })

    //   // this.spanArr = arry

    //   //合并列
    //   // data就是我们从后台拿到的数据
    //   for (var i = 0; i < data.length; i++) {
    //     if (i === 0) {
    //       this.spanArr.push(1)
    //       this.pos = 0
    //     } else {
    //       // 判断当前元素与上一个元素是否相同 linkman contractId
    //       if (data[i].linkman === data[i - 1].linkman) {
    //         this.spanArr[this.pos] += 1
    //         this.spanArr.push(0)
    //       } else {
    //         this.spanArr.push(1)
    //         this.pos = i
    //       }
    //     }
    //     console.log(this.spanArr)
    //   }
    // },
    //     <!--
    // row:代表当前行的值
    // column:代表当前列的值
    // rowIndex:当前行的索引
    // columnIndex:当前列的索引
    // -->
    // objectSpanMethod({ row, column, rowIndex, columnIndex }) {
    //   if (columnIndex === 0 || columnIndex === 5 || columnIndex === 7) {
    //     const _row = this.spanArr[rowIndex]
    //     const _col = _row > 0 ? 1 : 0
    //     return {
    //       // [0,0] 表示这一行不显示， [2,1]表示行的合并数
    //       rowspan: _row,
    //       colspan: _col,
    //     }
    //   }
    // },

    //合并列
    // objectSpanMethod({ row, column, rowIndex, columnIndex }) {
    //   if (columnIndex === 0  || columnIndex === 5 || columnIndex === 7) {

    //     // if (rowIndex % 2 === 0) {
    //     //   return {
    //     //     rowspan: 2,
    //     //     colspan: 1,
    //     //   }
    //     // } else {
    //     //   return {
    //     //     rowspan: 0,
    //     //     colspan: 0,
    //     //   }
    //     // }
    //   }
    // },

    checkParams(list, rule = ['shovelCount', 'shovelWeight', 'weight', 'productName', 'productId', 'productCode', 'id']) {
      for (const item of list) {
        for (const [key, val] of Object.entries(item)) {
          if (rule.includes(key)) continue
          if (val === '') {
            this.$message({ message: `请检查参数${key}`, type: 'warning' })
            return false
          }
        }
      }
      return true
    },
    async saveEntityForm(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.listV.length) {
          // let sum = 0
          const list = []
          // this.listV.forEach((item) => {
          //   let obj = {}
          //   obj.shovelCount = item.shovelCount
          //   obj.shovelWeight = item.shovelWeight
          //   obj.weight = item.weight
          //   obj.productName = item.productName
          //   obj.productId = item.productId
          //   obj.productCode = item.productCode
          //   obj._id = item._id
          //   list.push({ ...obj })
          // })

          this.listV.forEach((item) => {
            let obj = {}
            obj.shovelCount = item.shovelCount
            obj.shovelWeight = item.shovelWeight
            obj.weight = item.weight
            obj.productName = item.productName
            obj.productId = item.productId
            obj.productCode = item.productCode
            obj.id = item.id
            list.push({ ...obj })
          })
          if (!this.checkParams(list)) return false
          this.entityFormLoading = true
          const res = await Model.save({ ...this.entityForm, mixCoalItemList: list })
          this.entityFormLoading = false
          if (res) {
            this.resetVariant()
            this.getList()
            this.clearDraft()
            this.draft.open = false
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else {
          this.$message({ message: '生产数据不能为空', type: 'warning' })
        }
      }
    },

    async getContract() {
      const { data } = await getCustomerContract()
      // 供应商名称返回id用来查询
      this.contractEntity.options = this.formatContract({ data, key: { customerName: 'displayName', customerId: 'id' } })
    },
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return {
            name: item.name,
            id: item.id,
            code: item.code,
            coalCategory: item.coalCategory,
            shovelWeight: item.shovelWeight
          }
        })
      } catch (error) {}
      this.nameItemEntity = this.nameEntity.options
    },

    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.contractEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.listV = []
      this.handleAdd()
    },

    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    handleNameEntity(val) {
      this.entityForm.productName = val
    },

    handleBlur() {
      this.listV.forEach((item, index) => {
        if (item.shovelCount) {
          item.weight = item.shovelCount * item.shovelWeight
        } else {
          item.weight = ''
        }
        this.$set(this.listV, index, item)
      })
      this.handleBlurVV()
    },
    handleBlurVV() {
      let total = 0
      this.listV.forEach((item, index) => {
        total += parseFloat(item.weight)
      })
      this.entityForm.weight = parseFloat(total * this.entityForm.pileCount).toFixed(2)
    },
    // 小数相加计算（建议小数精确值不超过16位）
    addNum(num1, num2) {
      let sq1, sq2, multiple
      sq1 = getLength(num1)
      sq2 = getLength(num2)

      multiple = Math.pow(10, Math.max(sq1, sq2) + 1)

      return (num1 * multiple + num2 * multiple) / multiple
    },

    // 获取小数位数
    getLength(num) {
      let sq
      try {
        const numString = num.toString()
        if (numString.search('e') !== -1) {
          const length = parseInt(numString.split('e')[1])
          sq = Math.abs(length)
        } else {
          sq = num.toString().split('.')[1].length
        }
      } catch {
        sq = 0
      }

      return sq
    },

    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },

    handleAdd() {
      const form = { ...this.form }
      form._id = Math.random().toFixed(6).slice(-6)
      this.listV.push(form)
    },
    handleRemove({ _id }) {
      const index = this.listV.findIndex((item) => item._id === _id)
      if (index === -1) return false
      this.listV.splice(index, 1)
    },
    clearDraft() {
      this.productionDraft = { entityForm: { ...Model.model }, list: [{ ...form }] }
      Cache.remove('productionDraft')
    },
    /**
     * @status {boolean} 真是保存记录，假是展示
     */
    createDraft(status) {
      if (status || this.dialogStatus === 'create') {
        const list = this.listV.map((item) => item)
        return {
          entityForm: { ...this.entityForm },
          list
        }
      } else {
        this.entityForm = { ...this.productionDraft.entityForm }
        this.listV = this.productionDraft.list.map((item) => item)
      }
    },
    async handleCreate(type = true) {
      if (type) {
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
        this.entityForm = { ...this.entityForm }
        const productionDraft = Cache.get('productionDraft')
        if (productionDraft) {
          this.productionDraft = productionDraft
          this.createDraft(false)
        } else {
          this.productionDraft = this.createDraft(true)
          Cache.set('productionDraft', this.productionDraft)
        }
      } else {
        Cache.remove('productionDraft')
        this.productionDraft = this.createDraft(true)
        Cache.set('productionDraft', this.productionDraft)
      }
    },
    contrastField(a, b) {
      // if (a === null) return true
      a = JSON.parse(JSON.stringify(a))
      b = JSON.parse(JSON.stringify(b))
      a.list.forEach((item) => {
        for (const [k] of Object.entries(item)) {
          if (k.includes('_')) {
            item[k] = ''
          }
        }
      })
      b.list.forEach((item) => {
        for (const [k] of Object.entries(item)) {
          if (k.includes('_')) {
            item[k] = ''
          }
        }
      })
      const strA = JSON.stringify(a)
      const strB = JSON.stringify(b)
      return strA === strB
    },
    handleClose() {
      this.resetVariant()
      // const productionDraft = Cache.get('productionDraft')
      // if (this.dialogStatus === 'create') {
      //   const new_productionDraft = this.createDraft(true)
      //   const status = this.contrastField(productionDraft, new_productionDraft)
      //   if (productionDraft === null || !status) {
      //     this.$confirm('当前未保存, 是否保存草稿?', '提示', {
      //       confirmButtonText: '不保存',
      //       cancelButtonText: '保存草稿',
      //       type: 'info',
      //       cancelButtonClass: 'btn-custom-save',
      //       confirmButtonClass: 'btn-custom-cancel',
      //     })
      //       .then(() => {
      //         this.resetVariant()
      //       })
      //       .catch(() => {
      //         Cache.set('productionDraft', new_productionDraft)
      //         this.resetVariant()
      //       })
      //   } else {
      //     this.resetVariant()
      //   }
      // } else {
      //   this.resetVariant()
      // }
    }
  }
}
</script>
<style lang="scss" scoped>
// ::v-deep .el-table .cell {
//   line-height: 18px;
// }

// ::v-deep .el-table td > .cell div {
//   font-size: 12px;
//   line-height: 21px;
//   // height: 60px !important;
// }
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
::v-deep .el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border-bottom: 1px solid #ccc;
}

::v-deep .el-table--border .el-table__cell,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right: 1px solid #ccc;
}
::v-deep .el-table__cell:first-child .cell {
  padding-left: 0;
}
.table-containerV {
  // height: 80vh;
  height: 90vh;
  padding: 20px;
  overflow-y: auto !important;
  background: #fff;
  box-sizing: border-box;
  padding-bottom: 30px;
  // <!-- height:84rem;: auto;background:#fff; -->
}
::v-deep .el-icon-date {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-icon-time {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}
// @media screen and (max-width:480px;) {
//   height: 80vh !important;
// }

::v-deep .el-input__prefix {
  right: 5px;
  left: auto;
}

::v-deep .el-input--prefix .el-input__inner {
  padding-left: 10px;
}
::v-deep .el-table__fixed-right::before {
  width: 0;
}

.dialog {
  width: 980px;
  height: 740px;
}
// .dialog-footer {
//   // margin-right: 44px;
//   display: flex;
//   justify-content: flex-end;
//   color: #ff9639;
//   border: 1px solid #ff9639;
//   border-top: solid 1px #f1f1f2;
//   box-sizing: border-box;
//   &-all {
//     font-size: 14px;
//     margin-left: 34px;
//     // margin-right: auto;
//     flex: 0 0 58px;
//     color: #ff9639;
//     background: #fff;
//     border: 1px solid #ff9639;
//     &:hover {
//       background: transparent;
//     }
//     &:nth-of-type(1) {
//       // margin-left: 15px;
//       margin-right: auto;
//     }
//   }
//   &-btns {
//     width: 85px;
//     height: 35px;
//     border: 1px solid #2f79e8;
//     background: #fff;
//     color: #2f79e8;
//     margin-left: 20px;
//     font-size: 14px;
//     &:last-of-type {
//       border: 1px solid #d5d5d5;
//       color: #747474;
//     }
//   }
// }

.form-layout {
  padding: 0 20px;
}

.form-title {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 17px;
    background: #2f79e8;
    height: 16px;
  }
}
::v-deep .el-input--mini .el-input__inner {
  height: 29.5px;
  line-height: 29.5px;
}

.opt-btn {
  cursor: pointer;
  margin: 0 3px;
  color: #fff;
  border: none;
}
::v-deep .el-form--label-top .el-form-item__label {
  padding: 0;
  font-weight: 400;
  font-size: 13px;
  color: #646464;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 13px;
}
.name {
  // color: blue;
  text-decoration: underline;
  cursor: pointer;
  transition: 0.5s all;
  &:hover,
  :focus {
    color: #3234d3;
  }
}

.excessive {
  width: 100%;
  color: red;
}

::v-deep .percent-column {
  .cell {
    top: 10px;
    height: 42px;
  }
}
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}
.column {
  display: inline-block;
  width: 100%;
  height: 20px;
}
.app-container {
  height: 100vh;
}
.table {
  width: 100%;
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
}
.el-button--export-all:focus,
.el-button--export-all:hover {
  background: #ff9639 !important;
  border-color: #fff !important;
  color: #fff !important;
}

.el-button--export-all {
  color: #ff9639;
  background-color: #fff;
  border-color: #ff9639;
  padding: 8px 13px;
  margin-top: -6px;
}
::v-deep // 合计行样式
.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  height: 50px;
  line-height: 50px;
  color: #ff9639;
  font-size: 13px;
  background: rgb(245, 246, 251);
}
::v-deep .panel-list {
  margin-left: -25px;
}

::v-deep .info {
  display: flex;
  justify-content: space-evenly;
  flex-flow: wrap;
  span {
    padding: 3px;
  }
  // justify-content: start;
  .title {
    span {
      margin-left: 5px;
      color: #f8a20f;
      font-weight: bold;
    }
  }
}
::v-deep .orange-row {
  color: #f8a20f;
  font-size: 14px;
  font-weight: bold;
  height: 60px;
}
::v-deep .row-layout {
  font-size: 14px;
  height: 60px;
}
.panel-list-item {
  height: 40px;
  padding: 5px;
}
</style>
