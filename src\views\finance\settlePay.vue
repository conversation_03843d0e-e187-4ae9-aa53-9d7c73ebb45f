<template>
  <div class="app-container">
    <!-- <el-date-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate" placeholder="选择日期"
                    :clearable="false" /> -->
    <!-- @Refresh="Refreshfn" :changeactions="changeactions"-->
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" :actionList="actionList" :buttomlist="buttomlist" />

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :statetype="activetype" :buttomlist="buttomlist"
            :showSummary='true' :span-method="arraySpanMethod" :countList="countList" :actionList="actionList" title="供应商"
            otherHeight="170">
      <!-- fixed='left' -->
      <el-table-column label="状态" slot="state" align="center" width="150">
        <template slot-scope="scope">
          <!-- 通用审核状态 NEW-待审核、PASS-通过、REJECT-拒绝 -->
          <!-- <el-tag :disable-transitions="true" class="light" style="margin-right: 2px"
                  :type="scope.row.state=='NEW'?'danger':'info'" effect='plain'>
            <span v-if="scope.row.state=='DRAFT'">草稿</span>
            <span v-if="scope.row.state=='NEW'">待审核</span>
            <span v-if="scope.row.state=='PASS'">已审核</span>
            <span v-if="scope.row.state=='REJECT'">拒绝</span>  财务审批
          </el-tag> -->

          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :(scope.row.state == 'PAYED' ? 'success' : 'info')))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '待审核' : (scope.row.state == 'PASS' ? '已审核' : (scope.row.state == 'REJECT' ? '拒绝':(scope.row.state == 'PAYED' ? '已付款' : '')))) }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- fixed='left' -->
      <el-table-column label="结算类型" slot="applyType" align="center" width="150">
        <template slot-scope="scope" v-if="scope.row.applyType">
          <el-tag :type="(scope.row.applyType=='TRADE'? 'danger':(scope.row.applyType == 'CARRIAGE' ? 'warning' : (scope.row.applyType == 'GOOD' ? 'success' :(scope.row.applyType == 'PERSONAL' ? 'success' :'info'))))"
                  size="mini">
            {{ scope.row.applyType == 'TRADE' ? '贸易往来结算' : (scope.row.applyType == 'CARRIAGE' ? '运费结算' : (scope.row.applyType == 'GOOD' ? '物资结算' :  (scope.row.applyType == 'PERSONAL' ? '个人结算' : '')))}}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="品名" slot="productName" align="center" width="100">
        <template slot-scope="scope">
          {{scope.row.productName}}
        </template>
      </el-table-column>

      <el-table-column label="结算周期" slot="beginDate" align="center" width="180">
        <template slot-scope="scope">
          <span v-if="scope.row.beginDate">
            {{scope.row.beginDate}}至{{scope.row.endDate}}
          </span>
        </template>
      </el-table-column>

      <el-table-column slot="isSettle" label="结算状态" align="center">
        <template slot-scope="scope" v-if="scope.row.isSettle">
          <el-tag :type="(scope.row.isSettle=='Y' ? 'success':(scope.row.isSettle == 'N' ? 'danger' : ''))" size="mini">
            {{scope.row.isSettle == 'Y' ? '已结清' : (scope.row.isSettle == 'N' ? '未结清' :  '') }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="当前流程" slot="curFlowName" align="center" fixed="right">
        <template slot-scope="scope">

          <el-popover placement="right" width="700" trigger="click">

            <div v-for="(item,index) in steplistNEW" :key="index">
              <div>
                <div>{{item.label}}: <span style="color:red">{{item.curFlowName}}</span></div>
              </div>
              <el-row type="flex" :gutter="50" style="margin:20px 0">
                <el-col style="margin-bottom: -13px">
                  <div class="bzboxV">
                    <div class="bzminboxV" v-for="(value,indexc) in item.flowDesignList" :key="indexc">
                      <div class="lin" :class="value.active==true?'bordercolorBLUE':''"></div>
                      <div class="context" :class="value.active==true?'colorBLUE':''">

                        <i class="el-icon-edit icon" v-if="value.code=='SETTLE_PAY_DRAFT'"></i>
                        <i class="el-icon-coordinate icon"
                           v-if="value.code=='SETTLE_PAY_FINANCE_LEADER' ||  value.code=='SETTLE_PAY_MANAGER' ||  value.code=='SETTLE_PAY_BOSS' ||  value.code=='SETTLE_PAY_PASS'"></i>

                        <i class="el-icon-link icon" v-if="value.code=='PASS'"></i>
                        <i class="el-icon-help icon" v-if="value.code=='SETTLE_PAY_REJECT'"></i>
                        <i class="el-icon-document-checked" v-if="value.code=='SETTLE_PAY_ PAYED'" style="font-size: 30px;"></i>

                        <div>{{value.aliasName}}</div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <el-button slot="reference" style="padding: 3px 6px;" @click="lookbtn(scope.row)">流程</el-button>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" max-width="100" width="170" fixed="right" align="center">
        <template slot-scope="scope">
          <div style="display: flex; flex-direction:row;justify-content: center;flex-wrap: wrap;">
            <div v-if="perms[`${curd}:ismanagerreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode =='SETTLE_PAY_MANAGER'" color="#FF726B"
                      @click="handleUpdate(scope.row,'review','SETTLE_PAY')">
                去审核(总经理)
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:isFinancereview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode=='SETTLE_PAY_FINANCE_LEADER'" color="#33CAD9"
                      @click="handleUpdate(scope.row,'review','SETTLE_PAY')">
                去审核(财务负责人)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:isBossreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode=='SETTLE_PAY_BOSS'" color="#33CAD9"
                      @click="handleUpdate(scope.row,'review','SETTLE_PAY')">
                去审核(董事长)
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT' || scope.row.state == 'REJECT'" color="#FF726B"
                      @click="handleUpdate(scope.row,'update')">
                编辑
              </el-tag>
            </div>

            <!-- <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'PASS'" color="#FF726B" @click="handlePrint(scope.row,'update')">
                <span v-if="scope.row.isPrint=='Y'">补打</span>
                <span v-else>打印</span>
              </el-tag>
            </div>
            <div v-if="scope.row.state == 'PASS'" @click="handDownload(scope.row)">
              <el-tag class="opt-btn" color="#2f79e8">
                <span>下载</span>
              </el-tag>
            </div> -->

            <!-- <div v-if="scope.row.state =='NEW' || scope.row.state =='PASS'|| scope.row.state =='PASS_FINANCE'">
              <el-tag class="opt-btn" color="#FF726B" @click="handleUpdate(scope.row,'watch')">
                查看
              </el-tag>
            </div> -->

            <div v-if="perms[`${curd}:istovoid`] || false">
              <el-tag class="opt-btn" color="#ff726b" v-if="scope.row.state == 'PASS'" @click="handleCancel(scope.row)">作废
              </el-tag>
            </div>
            <div v-if="scope.row.state=='REJECT'  ||  scope.row.state=='PASS' || 
                 scope.row.state=='PASS_FINANCE' || scope.row.state=='NEW'  ">
              <el-tag class="opt-btn" color="#FF726B" @click="handleUpdate(scope.row,'watch','SETTLE_PAY')">
                查看
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false" style="margin-left:5px">
              <el-tag class="opt-btn" style="cursor: pointer;" color="#2f79e8"
                      v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">

        <!-- <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="reviewLogList">
              <el-steps :active="reviewLogList.length">
                <el-step v-for="(item,index) in reviewLogList" :key="index"
                         :title="item.reviewResult + ' ( 审批意见：'+item.reviewMessage+' )'"
                         :description="item.createDate+' '+item.reviewUserName">
                </el-step>
              </el-steps>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row type="flex" :gutter="50" v-if="steplist.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="steplist">
              <div class="bzbox">
                <div v-for="(item,index) in steplist" :key="index" class="bzminbox">
                  <div class="lin" :class="item.active==true?'bordercolorBLUE':''"></div>
                  <div class="context" :class="item.active==true?'colorBLUE':''">
                    <i class="el-icon-edit icon" v-if="item.code=='SETTLE_PAY_DRAFT'"></i>
                    <i class="el-icon-coordinate icon"
                       v-if="item.code=='SETTLE_PAY_FINANCE_LEADER' ||  item.code=='SETTLE_PAY_MANAGER' ||  item.code=='SETTLE_PAY_BOSS'"></i>

                    <i class="el-icon-link icon" v-if="item.code=='SETTLE_PAY_PASS'"></i>
                    <i class="el-icon-help icon" v-if="item.code=='SETTLE_PAY_REJECT'"></i>
                    <i class="el-icon-document-checked" v-if="item.code=='SETTLE_PAY_PAYED'" style="font-size: 30px;"></i>
                    <div>{{item.aliasName}}</div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-col style="padding:0 15px">
              <el-tabs v-model="entityForm.applyType" @tab-click="changetab">
                <el-tab-pane label="贸易往来结算" name="TRADE"></el-tab-pane>
                <!-- <el-tab-pane label="运费结算" name="CARRIAGE"></el-tab-pane> -->
                <el-tab-pane label="物资结算" name="GOOD"></el-tab-pane>
                <el-tab-pane label="个人结算" name="PERSONAL"></el-tab-pane>
              </el-tabs>
            </el-col>
            <!-- 贸易往来结算 -->
            <template v-if="entityForm.applyType=='TRADE'">
              <div class="form-title">基础信息</div>
              <div class="form-layout form-layoutV">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="日期" prop="applyDate">
                      <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="合同" prop="contractId">
                      <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                   filterable clearable placeholder="请选择合同" @change="handleNameEntityV" style="width:100%" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="品名" prop="productName">
                      <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                   disabled />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算单位" prop="supplierId">
                      <el-select v-model="supplierEntity.value" placeholder="请选择" clearable filterable style="width:100%"
                                 @blur="selectBlur" @change="handleSupplier" disabled>
                        <el-option v-for="item in supplierEntity.options" :key="item.id" :label="item.name" :value="item.id" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row type="flex" :gutter="50">
                  <!-- 采购合同的买方 -->
                  <el-col>
                    <el-form-item label="买方" prop="firstParty">
                      <el-select v-model="firstpartyEntity.value" placeholder="请选择买方" clearable filterable style="width:100%"
                                 disabled>
                        <el-option v-for="item in firstpartyEntity.options" :key="item.name" :label="item.name"
                                   :value="item.name" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="卖方" prop="secondParty">
                      <el-select v-model="secondPartyEntity.value" placeholder="请选择买方" clearable filterable style="width:100%"
                                 disabled>
                        <el-option v-for="item in secondPartyEntity.options" :key="item.name" :label="item.name"
                                   :value="item.name" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算周期" :rules="[{ required: true, message: '请选择结算周期' }]">
                      <el-date-picker v-model="Settlementcycle" type="daterange" range-separator="至" start-placeholder="开始日期"
                                      :picker-options="optionsDisable" value-format="yyyy-MM-dd" end-placeholder="结束日期"
                                      @change="change">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <div style="margin-top:28px">
                      <el-button type="primary" style="float: right;" @click="seachfn"
                                 v-if="dialogStatus=='create' || dialogStatus=='update'">保存</el-button>
                    </div>

                  </el-col>

                </el-row>

                <!-- <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="结算周期" :rules="[{ required: true, message: '请选择结算周期' }]">
                      <el-date-picker v-model="Settlementcycle" type="daterange" range-separator="至" start-placeholder="开始日期"
                                      :picker-options="optionsDisable" value-format="yyyy-MM-dd" end-placeholder="结束日期"
                                      @change="change">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-button type="primary" style="float: right;" @click="seachfn"
                               v-if="dialogStatus=='create' || dialogStatus=='update'">保存</el-button>
                  </el-col>
                </el-row> -->
              </div>

              <div class="form-title" v-show="isallShow">结算明细</div>
              <div class="jsmx" v-if="(dialogStatus=='watch' || dialogStatus=='review') && isallShow">
                <el-table :data="entityForm.weightHouseInList" stripe border highlight-current-row :header-cell-style="headClass"
                          height="300px">
                  <el-table-column label="日期" prop="date" align="center"></el-table-column>
                  <el-table-column label="品名" prop="name" align="center"></el-table-column>
                  <el-table-column label="原发数" prop="sendWeight" align="center"></el-table-column>
                  <el-table-column label="实收数" prop="actualWeight" align="center"></el-table-column>
                  <el-table-column label="运费" prop="carriage" align="center"></el-table-column>
                  <el-table-column label="煤价" prop="price" align="center"></el-table-column>
                  <el-table-column label="是否含税" prop="hasFax" align="center">
                    <template slot-scope="scope">
                      <span v-if="scope.row.hasFax=='Y'">是</span>
                      <span v-if="scope.row.hasFax=='N'">否</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="车牌" prop="plateNumber" align="center"></el-table-column>
                  <el-table-column label="发货单位" prop="senderName" align="center" width="300"></el-table-column>
                  <el-table-column label="收货单位" prop="receiverName" align="center" width="300"></el-table-column>
                  <el-table-column label="途耗" prop="wayCost" align="center"></el-table-column>
                  <el-table-column label="备注" prop="remarks" align="center"></el-table-column>
                </el-table>
              </div>
              <div v-else>
                <div class="form-layout form-layoutV" v-show="isallShow">
                  <el-row type="flex" :gutter="50">
                    <el-col>
                      <div class="form-edit-content">
                        <div class="left-flex">
                          <h3 class="content-title">全部列表</h3>
                          <div class="content">
                            <div class="filter-container">
                              <el-date-picker v-model="Settlementdate" type="daterange" range-separator="至" :clearable="true"
                                              start-placeholder="开始日期" :picker-options="optionsDisable" value-format="yyyy-MM-dd"
                                              end-placeholder="结束日期" @change="changeVV">
                              </el-date-picker>
                              <el-button type="primary" icon="el-icon-search" @click="generateData">查询
                              </el-button>
                            </div>
                            <el-table :data="dataleftlist" height="410px" v-loading="leftListLoading"
                                      element-loading-text="加载中..." stripe border highlight-current-row
                                      @selection-change="handleAddSelectionChange">
                              <el-table-column type="selection" width="40">
                              </el-table-column>
                              <el-table-column label="日期" prop="date" align="center"></el-table-column>
                              <el-table-column label="品名" prop="name" align="center"></el-table-column>
                              <el-table-column label="原发数" prop="sendWeight" align="center"></el-table-column>
                              <el-table-column label="实收数" prop="actualWeight" align="center"></el-table-column>
                              <!-- <el-table-column label="合同编号" prop="contractId"></el-table-column> -->
                              <el-table-column label="运费" prop="carriage" align="center"></el-table-column>
                              <el-table-column label="煤价" prop="price" align="center"></el-table-column>
                              <!-- <el-table-column label="人工费" prop="manCost"></el-table-column> -->
                              <el-table-column label="是否含税" prop="hasFax" align="center">
                                <template slot-scope="scope">
                                  <span v-if="scope.row.hasFax=='Y'">是</span>
                                  <span v-if="scope.row.hasFax=='N'">否</span>
                                </template>
                              </el-table-column>
                              <el-table-column label="车牌" prop="plateNumber" align="center"></el-table-column>
                              <el-table-column label="发货单位" prop="senderName" width="300" align="center"></el-table-column>
                              <el-table-column label="收货单位" prop="receiverName" width="300" align="center"></el-table-column>
                              <!-- <el-table-column label="机打实收" prop="receiveWeight"></el-table-column> -->
                              <el-table-column label="途耗" prop="wayCost" align="center"></el-table-column>
                              <el-table-column label="备注" prop="remarks" align="center"></el-table-column>
                            </el-table>
                            <!-- <div v-show="!leftListLoading" class="pagination-container">
                              <el-pagination @size-change="handleLeftListSizeChange" @current-change="handleLeftListCurrentChange"
                                             :current-page.sync="leftListQuery.page" :layout="pagination.layout"
                                             :page-sizes="pagination.pageSizes" :page-size="leftListQuery.size"
                                             :total="totalleft">
                              </el-pagination>
                            </div> -->
                          </div>
                        </div>
                        <div class="mid-tools">
                          <el-button type="primary" icon="el-icon-arrow-right" @click="saveUserList('add')"
                                     :disabled="rightListLoading || addUserList.length === 0 "></el-button>
                          <br>
                          <el-button type="danger" icon="el-icon-arrow-left" @click="saveUserList('del')"
                                     :disabled="rightListLoading || delUserList.length === 0 "></el-button>
                        </div>
                        <div class="right-form">
                          <h3 class="content-title">选中列表</h3>
                          <div class="content">
                            <!-- <div class="filter-container" style="height:40px"></div> -->
                            <el-table :data="datarighttlist" height="450px" v-loading="rightListLoading"
                                      element-loading-text="加载中..." stripe border highlight-current-row
                                      @selection-change="handleDelSelectionChange">
                              <el-table-column type="selection" width="40">
                              </el-table-column>
                              <el-table-column label="日期" prop="date" align="center"></el-table-column>
                              <el-table-column label="品名" prop="name" align="center"></el-table-column>
                              <el-table-column label="原发数" prop="sendWeight" align="center"></el-table-column>
                              <el-table-column label="实收数" prop="actualWeight" align="center"></el-table-column>
                              <!-- <el-table-column label="合同编号" prop="contractId"></el-table-column> -->

                              <el-table-column label="运费" prop="carriage" align="center"></el-table-column>
                              <el-table-column label="煤价" prop="price" align="center"></el-table-column>
                              <!-- <el-table-column label="人工费" prop="manCost"></el-table-column> -->
                              <el-table-column label="是否含税" prop="hasFax" align="center">
                                <template slot-scope="scope">
                                  <span v-if="scope.row.hasFax=='Y'">是</span>
                                  <span v-if="scope.row.hasFax=='N'">否</span>
                                </template>
                              </el-table-column>
                              <el-table-column label="车牌" prop="plateNumber" align="center"></el-table-column>
                              <el-table-column label="发货单位" prop="senderName" width="300" align="center"></el-table-column>
                              <el-table-column label="收货单位" prop="receiverName" width="300" align="center"></el-table-column>
                              <!-- <el-table-column label="机打实收" prop="receiveWeight"></el-table-column> -->
                              <el-table-column label="途耗" prop="wayCost" align="center"></el-table-column>
                              <el-table-column label="备注" prop="remarks" align="center"></el-table-column>
                            </el-table>
                            <!-- <div v-show="!rightListLoading" class="pagination-container">
                              <el-pagination @size-change="handleRightListSizeChange"
                                             @current-change="handleRightListCurrentChange"
                                             :current-page.sync="rightListQuery.page" :layout="pagination.layout"
                                             :page-sizes="pagination.pageSizes" :page-size="rightListQuery.size"
                                             :total="totalright">
                              </el-pagination>
                            </div> -->
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>

              <div class="form-title" v-show="isallShow">结算信息</div>
              <div class="form-layout form-layoutV" v-show="isallShow">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="拉运车数" prop="truckCount">
                      <el-input v-model="entityForm.truckCount" @input="handleinput1(entityForm)" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <!-- 原发吨数 -->
                    <el-form-item label="原发吨数" prop="sendWeight">
                      <el-input v-model="entityForm.sendWeight" @input="CalculateSettlementTonnage(entityForm)" autocomplete="off"
                                clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="实收吨数" prop="receiveWeight">
                      <el-input v-model="entityForm.receiveWeight" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="补水吨数" prop="moisturizingWeight">
                      <el-input v-model="entityForm.moisturizingWeight" @input="CalculateSettlementTonnage(entityForm)"
                                autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="路耗" prop="roadCost">
                      <el-input v-model="entityForm.roadCost" @input="CalculateSettlementTonnage(entityForm)" autocomplete="off"
                                clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算吨数" prop="settleWeight">
                      <el-input v-model="entityForm.settleWeight" autocomplete="off" clearable
                                @input="CalculateSettlementTonnage(entityForm)" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="合同单价" prop="contractPrice">
                      <el-input v-model="entityForm.contractPrice" autocomplete="off" clearable
                                @input="handleBlurcount(entityForm)" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算单价" prop="price">
                      <el-input v-model="entityForm.price" @input="handleBlurcount(entityForm)" autocomplete="off"
                                oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                    </el-form-item>
                  </el-col>

                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="结算金额" prop="settleMoney">
                      <el-input v-model="entityForm.settleMoney" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                clearable />
                    </el-form-item>
                  </el-col>
                  <el-col></el-col>
                  <el-col></el-col>
                  <el-col></el-col>
                </el-row>
              </div>

              <div class="form-title" v-show="isallShow">付款信息</div>
              <div class="jsmx" v-if="(dialogStatus=='watch' || dialogStatus=='review') && isallShow">
                <el-table :data="entityForm.applyPayList" stripe border highlight-current-row :header-cell-style="headClass"
                          height="300px">
                  <el-table-column label="日期" prop="applyDate" align="center"></el-table-column>
                  <el-table-column label="付款金额" prop="money" align="center"></el-table-column>
                  <el-table-column label="煤炭价格" prop="price" align="center"></el-table-column>
                </el-table>
              </div>
              <div v-else>
                <div class="form-layout form-layoutV" v-show="isallShow">
                  <el-row type="flex" :gutter="50">
                    <el-col>
                      <div class="form-edit-content">
                        <div class="left-flex">
                          <h3 class="content-title">全部列表</h3>
                          <div class="content">
                            <div class="filter-container">
                              <el-date-picker v-model="paydate" type="daterange" range-separator="至" start-placeholder="开始日期"
                                              :picker-options="optionsDisable" value-format="yyyy-MM-dd" end-placeholder="结束日期"
                                              @change="paychangeVV">
                              </el-date-picker>
                              <el-button type="primary" icon="el-icon-search" @click="getpaymentLeftData">查询
                              </el-button>
                            </div>
                            <el-table :data="dataPaymentleftlist" height="450px" v-loading="paymentleftListLoading"
                                      element-loading-text="加载中..." stripe border highlight-current-row
                                      @selection-change="handleAddSelectionChangePaymentLeft">
                              <el-table-column type="selection" width="40">
                              </el-table-column>
                              <el-table-column label="日期" prop="applyDate" align="center"></el-table-column>
                              <el-table-column label="付款金额" prop="money" align="center"></el-table-column>
                              <el-table-column label="煤炭价格" prop="price" align="center"></el-table-column>
                            </el-table>
                            <!-- <div v-show="!paymentleftListLoading" class="pagination-container">
                              <el-pagination @size-change="handlePaymentLeftListSizeChange"
                                             @current-change="handlePaymentLeftListCurrentChange"
                                             :current-page.sync="paymentLeftListQuery.page" :layout="pagination.layout"
                                             :page-sizes="pagination.pageSizes" :page-size="paymentLeftListQuery.size"
                                             :total="totalApplyPayleft">
                              </el-pagination>
                            </div> -->
                          </div>
                        </div>
                        <div class="mid-tools">
                          <el-button type="primary" icon="el-icon-arrow-right" @click="savePaymentList('add')"
                                     :disabled="paymentrightListLoading || addPaymentList.length === 0 "></el-button>
                          <br>
                          <el-button type="danger" icon="el-icon-arrow-left" @click="savePaymentList('del')"
                                     :disabled="paymentrightListLoading || delPaymentList.length === 0 "></el-button>
                        </div>
                        <div class="right-form">
                          <h3 class="content-title">选中列表</h3>
                          <div class="content">
                            <el-table :data="dataPaymentrighttlist" height="450px" v-loading="paymentrightListLoading"
                                      element-loading-text="加载中..." stripe border highlight-current-row
                                      @selection-change="handleDelSelectionChangePayment">
                              <el-table-column type="selection" width="40">
                              </el-table-column>
                              <el-table-column label="日期" prop="applyDate" align="center"></el-table-column>
                              <el-table-column label="付款金额" prop="money" align="center"></el-table-column>
                              <el-table-column label="煤炭价格" prop="price" align="center"></el-table-column>
                            </el-table>
                            <!-- <div v-show="!paymentrightListLoading" class="pagination-container">
                              <el-pagination @size-change="handlePaymentRightListSizeChange"
                                             @current-change="handlePaymentRightListCurrentChange"
                                             :current-page.sync="paymentrightListQuery.page" :layout="pagination.layout"
                                             :page-sizes="pagination.pageSizes" :page-size="paymentrightListQuery.size"
                                             :total="totalApplyPayright">
                              </el-pagination>
                            </div> -->
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>

              <div class="form-title" v-show="isallShow">指标信息</div>
              <div class="form-layout form-layoutV" v-show="isallShow">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <div class="tablebox">
                      <div class="tablehead">
                        <div v-for="(item,index) in tableheadlist" :key="index">
                          {{item.name}}
                        </div>
                      </div>
                      <div class="tablecon">
                        <div>
                          合同指标
                        </div>
                        <div> <el-input v-model="entityForm.cleanStd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.cleanAd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.cleanVdaf" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.procG" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                    clearable /></div>
                        <div> <el-input v-model="entityForm.cleanMt" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.procY" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                    clearable /></div>
                        <div> <el-input v-model="entityForm.qualCsr" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.macR0" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                    clearable /></div>
                        <div> <el-input v-model="entityForm.recovery" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                      </div>
                      <div class="tablecon">
                        <div>
                          化验指标
                        </div>
                        <div> <el-input v-model="entityForm.assayCleanStd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayCleanAd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayCleanVdaf" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayProcG" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayCleanMt" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayProcY" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayQualCsr" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayMacR0" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayRecovery" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                      </div>

                      <div class="tablecon">
                        <div>
                          三方化验
                        </div>
                        <div> <el-input v-model="entityForm.thirdCleanStd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdCleanAd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdCleanVdaf" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdProcG" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdCleanMt" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdProcY" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdQualCsr" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdMacR0" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdRecovery" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                      </div>

                      <div class="tablecon">
                        <div>
                          其它化验
                        </div>
                        <div> <el-input v-model="entityForm.otherCleanStd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherCleanAd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherCleanVdaf" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherProcG" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherCleanMt" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherProcY" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherQualCsr" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherMacR0" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherRecovery" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                      </div>

                      <div class="tablecon">
                        <div>
                          质量扣罚
                        </div>
                        <div> <el-input v-model="entityForm.punishCleanStd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishCleanAd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishCleanVdaf" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishProcG" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishCleanMt" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishProcY" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishQualCsr" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishMacR0" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishRecovery" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="备注" prop="remarks">
                      <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </template>

            <!-- 运费结算 -->
            <!-- <template v-if="entityForm.applyType=='CARRIAGE'">
              <div class="form-title">基础信息</div>
              <div class="form-layout form-layoutV">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="日期" prop="applyDate">
                      <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算单位" prop="supplierId">
                      <el-select v-model="supplierEntity.value" placeholder="请选择结算单位" clearable filterable style="width:100%">
                        <el-option v-for="item in supplierEntity.options" :key="item.id" :label="item.name" :value="item.id" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="运输单位" prop="carriageUnit">
                      <el-input v-model="entityForm.carriageUnit" autocomplete="off" @input="setnumerfn(entityForm.carriageUnit)"
                                clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算编号" prop="carriageSettlePayCode">
                      <el-tooltip popper-class="popper" :content="entityForm.carriageSettlePayCode" placement="top"
                                  effect="light">
                        <span>{{entityForm.carriageSettlePayCode}}</span>
                        <el-input v-model="entityForm.carriageSettlePayCode" clearable disabled></el-input>
                      </el-tooltip>

                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-button type="primary" style="float: right;" @click="CARRIAGEseachfn"
                             v-if="dialogStatus=='create' || dialogStatus=='update'">保存</el-button>
                </el-row>
              </div>
              <div class="form-title" v-show="isFreightallShow">运费明细</div>
              <div class="jsmx" v-if="(dialogStatus=='watch' || dialogStatus=='review') && isFreightallShow">
                <el-table :data="entityForm.weightHouseLogList" stripe border highlight-current-row :header-cell-style="headClass"
                          height="300px">
                  <el-table-column label="收货单位" prop="customerName" align="center"></el-table-column>
                  <el-table-column label="车牌号" prop="plateNumber" align="center"></el-table-column>
                  <el-table-column label="原发数" prop="originalTon" align="center"></el-table-column>
                  <el-table-column label="实收" prop="weight" align="center"></el-table-column>
                  <el-table-column label="运费" prop="freightPrice" align="center"></el-table-column>
                </el-table>
              </div>
              <div v-else>
                <div class="form-layout form-layoutV" v-show="isFreightallShow">
                  <el-row type="flex" :gutter="50">
                    <el-col>
                      <div class="form-edit-content">
                        <div class="left-flex">
                          <h3 class="content-title">全部列表</h3>
                          <div class="content">
                            <div class="filter-container">
                              <el-date-picker v-model="SettlementFreightdate" type="daterange" range-separator="至"
                                              start-placeholder="开始日期" :picker-options="optionsDisable" value-format="yyyy-MM-dd"
                                              end-placeholder="结束日期" @change="changeYY">
                              </el-date-picker>

                              <el-select v-model="customerEntity.active" placeholder="请选择收货单位" clearable @change="handleCustomer"
                                         style="width:100%">
                                <el-option v-for="item in customerEntity.options" :key="item.value" :label="item.label"
                                           :value="item">
                                </el-option>
                              </el-select>
                              <el-button type="primary" icon="el-icon-search" @click="getFreightLeftData">查询
                              </el-button>
                            </div>
                            <el-table :data="dataFreightleftlist" height="410px" v-loading="FreightleftListLoading"
                                      element-loading-text="加载中..." stripe border highlight-current-row
                                      @selection-change="handleAddSelectionChangeFreightLeft">
                              <el-table-column type="selection" width="40">
                              </el-table-column>
                              <el-table-column label="收货单位" prop="customerName" align="center" width="250"></el-table-column>
                              <el-table-column label="车牌号" prop="plateNumber" align="center"></el-table-column>
                              <el-table-column label="原发数" prop="originalTon" align="center"></el-table-column>
                              <el-table-column label="实收" prop="weight" align="center"></el-table-column>
                              <el-table-column label="运费" prop="freightPrice" align="center"></el-table-column>
                              <el-table-column label="日期" prop="date" align="center"></el-table-column>
                            </el-table>
                          </div>
                        </div>
                        <div class="mid-tools">
                          <el-button type="primary" icon="el-icon-arrow-right" @click="saveFreightList('add')"
                                     :disabled="FreightrightListLoading || addFreightList.length === 0 "></el-button>
                          <br>
                          <el-button type="danger" icon="el-icon-arrow-left" @click="saveFreightList('del')"
                                     :disabled="FreightrightListLoading || delFreightList.length === 0 "></el-button>
                        </div>
                        <div class="right-form">
                          <h3 class="content-title">选中列表</h3>
                          <div class="content">
                            <el-table :data="dataFreightrighttlist" height="450px" v-loading="FreightrightListLoading"
                                      element-loading-text="加载中..." stripe border highlight-current-row
                                      @selection-change="handleDelSelectionChangeFreight">
                              <el-table-column type="selection" width="40">
                              </el-table-column>
                              <el-table-column label="收货单位" prop="customerName" align="center" width="250"></el-table-column>
                              <el-table-column label="车牌号" prop="plateNumber" align="center"></el-table-column>
                              <el-table-column label="原发数" prop="originalTon" align="center"></el-table-column>
                              <el-table-column label="实收" prop="weight" align="center"></el-table-column>
                              <el-table-column label="运费" prop="freightPrice" align="center"></el-table-column>
                              <el-table-column label="日期" prop="date" align="center"></el-table-column>
                            </el-table>
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <div class="form-title" v-show="isFreightallShow">结算信息</div>
              <div class="form-layout form-layoutV" v-show="isFreightallShow">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="车数" prop="truckCount">
                      <el-input v-model="entityForm.truckCount" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="原发吨数" prop="sendWeight">
                      <el-input v-model="entityForm.sendWeight" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算吨数" prop="settleWeight">
                      <el-input v-model="entityForm.settleWeight" autocomplete="off" clearable
                                @input="pricechangefn(entityForm)" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算运费均价" prop="price">
                      <el-input v-model="entityForm.price" autocomplete="off" clearable @input="pricechangefn(entityForm)" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="结算金额" prop="settleMoney">
                      <el-input v-model="entityForm.settleMoney" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                clearable />
                    </el-form-item>
                  </el-col>
                  <el-col></el-col>
                  <el-col></el-col>
                  <el-col></el-col>
                </el-row>
              </div>
              <div class="form-title" v-show="isFreightallShow">运费数据</div>
              <div class="form-layout form-layoutV" v-show="isFreightallShow">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-table :data="entityForm.carriageSettlePaySummaryDtoList" stripe border highlight-current-row
                              :header-cell-style="headClass" style="margin-bottom:20px" height="300px">
                      <el-table-column label="发货单位" prop="supplierName" align="center"></el-table-column>
                      <el-table-column label="收货单位" prop="customerName" align="center"></el-table-column>
                      <el-table-column label="品名" prop="cargoType" align="center"></el-table-column>
                      <el-table-column label="日期" prop="date" align="center"></el-table-column>
                      <el-table-column label="车数" prop="truckCount" align="center"></el-table-column>
                      <el-table-column label="原发吨数" prop="originalTon" align="center"></el-table-column>
                      <el-table-column label="结算吨数" prop="settleWeight" align="center"></el-table-column>
                      <el-table-column label="单价" prop="freightPrice" align="center"></el-table-column>
                      <el-table-column label="结算金额" prop="settleMoney" align="center"></el-table-column>
                    </el-table>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="备注" prop="remarks">
                      <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </template> -->

            <!-- 物资结算 -->
            <template v-if="entityForm.applyType=='GOOD'">
              <el-col>
                <div class="form-warp">
                  <span style="font-size: 16px;">物资信息</span>
                  <el-button type="export-all" @click="handleAdd">新增</el-button>
                </div>
                <div class="form-layout">
                  <el-row type="flex" :gutter="50">
                    <el-col>
                      <el-form-item label="日期" prop="applyDate">
                        <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                      </el-form-item>
                    </el-col>
                    <el-col>
                      <el-form-item label="结算单位" prop="supplierName">
                        <el-input v-model="entityForm.supplierName" autocomplete="off" clearable />
                      </el-form-item>
                    </el-col>
                    <el-col> </el-col>
                    <el-col> </el-col>
                  </el-row>
                  <el-row type="flex" :gutter="50">
                    <el-col>
                      <el-table :data="listV" style="width: 100%" stripe :header-cell-style="headClass" :showSummary='true'>
                        <el-table-column label="物资名称" align="center">
                          <template slot-scope="scope">
                            <el-input v-model="scope.row.productName" autocomplete="off" clearable />
                          </template>
                        </el-table-column>
                        <el-table-column label="物资编号" align="center">
                          <template slot-scope="scope">
                            <el-input v-model="scope.row.productCode" clearable @input="handleBlur" />
                          </template>
                        </el-table-column>
                        <el-table-column label="数量" align="center">
                          <template slot-scope="scope">
                            <el-input v-model="scope.row.settleWeight" clearable @input="handleBlurVV" />
                          </template>
                        </el-table-column>
                        <el-table-column label="单价" align="center">
                          <template slot-scope="scope">
                            <el-input v-model="scope.row.price" clearable @input="handleBlurVV" />
                          </template>
                        </el-table-column>
                        <el-table-column label="结算金额" align="center">
                          <template slot-scope="scope">
                            <!--  -->

                            <!-- <el-input v-model="scope.row.settleMoney" clearable disabled /> -->
                            <!-- <div v-if="scope.row.settleMoney!==NaN"> -->
                            <el-input v-model="scope.row.settleMoney" clearable disabled />
                            <!-- </div> -->
                            <!-- <div v-else>
                              <el-input value="0.00" clearable disabled />
                            </div> -->
                          </template>
                        </el-table-column>
                        <el-table-column label="备注" align="center">
                          <template slot-scope="scope">
                            <el-input v-model="scope.row.remarks" clearable />
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="80" align="center">
                          <template slot-scope="scope">
                            <el-tag class="opt-btn" color="#2f79e8" @click="handleRemove(scope.row)">删除</el-tag>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </template>

            <!-- 个人结算 -->
            <template v-if="entityForm.applyType=='PERSONAL'">
              <div class="form-title">基础信息</div>
              <div class="form-layout form-layoutV">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="日期" prop="applyDate">
                      <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="合同" prop="contractId">
                      <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                   filterable clearable placeholder="请选择合同" @change="handleNameEntityV" style="width:100%" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="品名" prop="productName">
                      <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算单位" prop="supplierId">
                      <el-select v-model="supplierEntity.value" placeholder="请选择结算单位" clearable filterable style="width:100%"
                                 @blur="selectBlur" @change="handleSupplier">
                        <el-option v-for="item in supplierEntity.options" :key="item.id" :label="item.name" :value="item.id" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="结算人" prop="settleMan">
                      <el-input v-model="entityForm.settleMan" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col></el-col>
                  <el-col></el-col>
                  <el-col></el-col>
                </el-row>
              </div>
              <div class="form-title">结算信息</div>
              <div class="form-layout form-layoutV">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <!-- 原发车数 -->
                    <el-form-item label="原发车数" prop="truckCount">
                      <el-input v-model="entityForm.truckCount" @input="handleinput1(entityForm)" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <!-- 原发吨数 -->
                    <el-form-item label="原发吨数" prop="sendWeight">
                      <el-input v-model="entityForm.sendWeight" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="实收吨数" prop="receiveWeight">
                      <el-input v-model="entityForm.receiveWeight" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算吨数" prop="settleWeight">
                      <el-input v-model="entityForm.settleWeight" autocomplete="off" clearable @input="handleBlur(entityForm)" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="单价" prop="price">
                      <el-input v-model="entityForm.price" autocomplete="off" clearable @input="handleBlur(entityForm)" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算金额" prop="settleMoney">
                      <el-input v-model="entityForm.settleMoney" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                clearable />
                    </el-form-item>
                  </el-col>
                  <el-col></el-col>
                  <el-col></el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="奖励金" prop="awardType">
                      <div style="display: flex;flex-direction: row; align-items: center;">
                        <div class="smart-search">
                          <div class="prepend">
                            <el-dropdown placement="bottom-start" @command="changeKey" trigger="click">
                              <div class="el-dropdown-link">
                                <i class="el-icon-arrow-down el-icon--right"></i>
                              </div>
                              <el-dropdown-menu slot="dropdown" style="width: 100px; max-height:500px;overflow: auto;">
                                <el-dropdown-item v-for="item in smartSearchItems" :key="item.id" :command="item.id">
                                  {{ item.label }}
                                </el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                            <span class="search-key">{{awardType}}:</span>
                          </div>
                          <div class="search-component">
                            <el-input v-model="entityForm.awardBase" @input="handleBlurH(entityForm)" clearable
                                      oninput="value=value.replace(/[^0-9.]/g,'')" />
                          </div>
                        </div>
                        <div style="margin-bottom: 5px; border-radius: 3px;border: 1px solid #fff;width:54%;margin-left:20px">
                          <el-input v-model="entityForm.awardMoney" @input="sethandleBlur(entityForm)" clearable />
                        </div>
                      </div>
                    </el-form-item>

                  </el-col>
                  <el-col>
                    <el-form-item label="扣罚金" prop="punishMoney">
                      <div style="display: flex;flex-direction: row; align-items: center;">
                        <div class="smart-search">
                          <div class="prepend">
                            <el-dropdown placement="bottom-start" @command="changeKeyV" trigger="click">
                              <div class="el-dropdown-link">
                                <i class="el-icon-arrow-down el-icon--right"></i>
                              </div>
                              <el-dropdown-menu slot="dropdown" style="width: 100px; max-height:500px;overflow: auto;">
                                <el-dropdown-item v-for="item in smartSearchItems" :key="item.id" :command="item.id">
                                  {{ item.label }}
                                </el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                            <span class="search-key">{{ punishType  }}:</span>
                          </div>
                          <div class="search-component">
                            <el-input v-model="entityForm.punishBase" @input="handleBlurcalculation(entityForm)" clearable
                                      oninput="value=value.replace(/[^0-9.]/g,'')" />
                          </div>
                        </div>
                        <div style="margin-bottom: 5px; border-radius: 3px;border: 1px solid #fff;width:54%;margin-left:20px">
                          <el-input v-model="entityForm.punishMoney" clearable @input="sethandleBlurf(entityForm)" />
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>

                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="结算周期" prop="beginDate">
                      <el-date-picker v-model="Settlementcycle" type="daterange" range-separator="至" start-placeholder="开始日期"
                                      :picker-options="optionsDisable" value-format="yyyy-MM-dd" end-placeholder="结束日期"
                                      @change="change">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col />
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="备注" prop="remarks">
                      <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </template>
          </el-col>
        </el-row>
      </el-form>

      <!-- <div slot="footer" class="dialog-footer" v-if="isshow==false">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="SubmitReview('entityForm')">
          提交审核
        </el-button>
        <el-button @click="SaveDraft('entityForm')" class="dialog-footer-btns" style="width:100px">保存草稿
        </el-button>
      </div> -->

      <!-- <div slot="footer" class="dialog-footer" v-else>
        <el-button @click="rejectfn(entityForm.id,entityForm.reviewMessage)" class="dialog-footer-btns" style="width:100px">拒绝
        </el-button>
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                   @click="passfn(entityForm.id,entityForm.reviewMessage,entityForm.state)">
          审核通过
        </el-button>
      </div> -->

      <div slot="footer" class="dialog-footer" v-if="dialogStatus != 'watch'">
        <!-- v-if="perms[`${curd}:review`]||false"-->
        <div v-if="dialogStatus != 'review'">
          <!-- 物资结算提交审核 -->
          <el-button type="primary" v-if="entityForm.applyType=='GOOD'" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="saveEntityForm('entityForm')">
            提交审核
          </el-button>

          <el-button v-else type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="SubmitReview('entityForm')">
            提交审核
          </el-button>

          <el-button @click="SaveDraft('entityForm')" class="dialog-footer-btns">
            保存草稿
          </el-button>
        </div>
        <div v-else style="width:100%">
          <div v-if="perms[`${curd}:ismanagerreview`] || perms[`${curd}:isFinancereview`]"
               style="display: flex;flex-direction: row;">
            <div style="width:calc(100% - 250px)" class="shenpi">
              <el-input style="width:100%" type="text" v-model="entityForm.reviewMessage" placeholder="请输入审批意见" />
            </div>
            <div style="width:250px">
              <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                         @click="passfn(entityForm.id,entityForm.reviewMessage,entityForm.curFlowCode)">
                审核通过
              </el-button>
              <el-button @click="rejectfn(entityForm.id,entityForm.reviewMessage,entityForm.curFlowCode)"
                         class="dialog-footer-btns" style="width:100px">
                拒绝
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import SettlementDetails from '@/model/SettlementDetails/index'
import Model from '@/model/finance/settlePay'
import { validatePhone } from '@/utils/validate'
import { settlePayOption } from '@/const'
import { contractList } from '@/api/quality'
import productModel from '@/model/product/productList'
import { chooseContractNotSettle } from '@/api/quality'
import { createMemoryField } from '@/utils/index'
import Cache from '@/utils/cache'
import Func from '@/utils/func'
import CustomerModel from '@/model/user/customer'
import { pagination } from '@/const'
import { addRoleUser, delRoleUser, findByRoleCode, findNotInRoleCode } from '@/api/common'
import { Decimal } from 'decimal.js'
const form = {
  _id: '1',
  productName: '',
  productCode: '',
  settleWeight: '',
  price: '',
  settleMoney: '',
  remarks: ''
}
const listQuery = {
  size: 50,
  current: 1,
  orderBy: 'createDate',
  orderDir: 'desc',
  filter_LIKES_applyType: ''
}

const rightListQuery = {
  current: 1,
  size: pagination.pageSize,
  orderBy: 'createDate',
  orderDir: 'desc',
  id: '',
  name: ''
}
const leftListQuery = {
  current: 1,
  size: pagination.pageSize,
  orderBy: 'createDate',
  orderDir: 'desc',
  id: '',
  name: ''
}

const paymentLeftListQuery = {
  current: 1,
  size: pagination.pageSize,
  orderBy: 'createDate',
  orderDir: 'desc',
  id: '',
  name: ''
}
const paymentrightListQuery = {
  current: 1,
  size: pagination.pageSize,
  orderBy: 'createDate',
  orderDir: 'desc',
  id: '',
  name: ''
}

const FreightLeftListQuery = {
  current: 1,
  size: pagination.pageSize,
  orderBy: 'createDate',
  orderDir: 'desc',
  id: '',
  name: ''
}

const FreightrightListQuery = {
  current: 1,
  size: pagination.pageSize,
  orderBy: 'createDate',
  orderDir: 'desc',
  id: '',
  name: ''
}

export default {
  name: 'settlePay',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      dataleftlist: [],
      datarighttlist: [],
      pagination: pagination,

      curd: 'settlePay',
      curdv: 'SettlementDetails',
      model: Model,
      modelLog: SettlementDetails,
      addressValue: '',
      entityForm: { ...Model.model },
      nameEntity: { options: [], active: '' },
      supplierEntity: { value: '', options: [] },
      limitNum: 50,
      username: 'ss',
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      filterOption: { ...settlePayOption },
      filterOptionV: {
        showMore: true,
        columns: [
          {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
              placeholder: '开始日期',
              clearable: false
            }
          },
          {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
              placeholder: '结束日期',
              clearable: false
            }
          }
        ]
      },
      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        phone: [{ validator: validateIsPhone, trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      rules: {
        applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' },
        productName: { required: true, message: '请选择品名', trigger: 'blur' },
        supplierId: { required: true, message: '请选择结算单位', trigger: 'blur' },
        beginDate: { required: true, message: '请选择结算周期', trigger: 'blur' },
        endDate: { required: true, message: '请选择结算周期', trigger: 'blur' },
        settleMoney: { required: true, message: '请选输入结算金额', trigger: 'blur' },
        settleWeight: { required: true, message: '请选输入结算吨数', trigger: 'blur' },
        contractPrice: { required: true, message: '请选输入合同单价', trigger: 'blur' }
      },
      // 合同
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      isshow: false,
      emptyImgPath: require(`@/assets/empty.jpg`),
      reviewLogList: [],
      awardType: '',
      punishType: '',
      smartSearchItems: [
        {
          label: '按吨',
          id: 1,
          width: 100
        },
        {
          label: '车数',
          id: 2
        }
      ],
      countList: [],

      listV: [{ ...form }],
      Settlementcycle: [],
      Settlementdate: [],
      paydate: [],
      SettlementFreightdate: [],
      isdisabled: false,
      optionsDisable: {},
      finalMoney1: '',
      finalMoney2: '',
      settleMoney: '',
      dialogFormVisibleV: false,
      listQueryLog: { ...listQuery },
      selectList: [],
      ptext: '请选择',
      tableheadlist: [
        { name: '质量指标' },
        { name: '硫(St,d)' },
        { name: '灰Ad' },
        { name: '挥发份(Vdaf)' },
        { name: '粘结G' },
        { name: '水份(Mt)' },
        { name: '胶质层(Y值)' },
        { name: '热强度(CSR)' },
        { name: '岩相(Ro)' },
        { name: '回收' }
      ],
      rightListLoading: false,
      rightListQuery: {
        ...rightListQuery
      },
      leftListLoading: false,
      leftListQuery: {
        ...leftListQuery
      },
      addUserList: [],
      delUserList: [],

      totalright: null,
      totalleft: null,

      totalApplyPayright: null,
      totalApplyPayleft: null,

      addPaymentList: [],
      delPaymentList: [],
      dataPaymentleftlist: [],
      dataPaymentrighttlist: [],
      paymentleftListLoading: false,
      paymentLeftListQuery: {
        ...paymentLeftListQuery
      },
      paymentrightListLoading: false,
      paymentrightListQuery: {
        ...paymentrightListQuery
      },

      isallShow: false,

      totalFreightRight: null,
      totalFreightLeft: null,
      addFreightList: [],
      delFreightList: [],

      dataFreightleftlist: [],
      dataFreightrighttlist: [],
      FreightleftListLoading: false,
      FreightLeftListQuery: {
        ...FreightLeftListQuery
      },
      isFreightallShow: false,
      FreightrightListLoading: false,
      FreightrightListQuery: {
        ...FreightrightListQuery
      },

      customerEntity: { options: [], active: [] },
      firstpartyEntity: { value: '', options: [] },
      secondPartyEntity: { value: '', options: [] },
      steplist: [],
      steplistNEW: [],
      isCustomerchange: false
    }
  },
  watch: {
    'entityForm.isPublicBoolean': {
      handler(v) {
        this.entityForm.isPublic = v ? 'Y' : 'N'
      }
    },
    'entityForm.productName'(name) {
      if (name) {
        const item = this.nameEntity.options.find((item) => item.name === name)
        if (item) {
          this.nameEntity.active = item.name
          this.entityForm.productCode = item.code
          this.entityForm.productId = item.id
          this.entityForm.coalType = item.coalCategory || ''
          this.entityForm.coalCategory = item.type
        }
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
        this.entityForm.coalCategory = ''
      }
    },

    'supplierEntity.value'(id) {
      if (id == undefined) {
        this.entityForm = { ...this.entityForm, contractName: undefined, supplierName: undefined }
      }
      if (!id) return
      const item = this.supplierEntity.options.find((item) => item.id === id)
      if (item) {
        this.supplierEntity.active = item
        this.entityForm.supplierId = id
        this.entityForm.supplierName = item.name
        if (this.entityForm.applyType == 'CARRIAGE') {
          var date = new Date()
          var year = date.getFullYear()
          var month = date.getMonth() + 1
          var strDate = date.getDate()
          if (month >= 1 && month <= 9) {
            month = '0' + month
          }
          if (strDate >= 0 && strDate <= 9) {
            strDate = '0' + strDate
          }
          var currentdate = year + month + strDate + item.name
          this.entityForm.carriageSettlePayCode = currentdate
        } else {
          this.entityForm.carriageUnit = ''
        }
      } else {
        //手写的结算单位赋值
        this.entityForm.supplierName = id
        this.entityForm.supplierId = ''
        this.supplierEntity.active = []
      }
    },

    // 'entityForm.supplierId'(id) {
    //   console.log(id)
    //   if (id == undefined) {
    //     this.entityForm = { ...this.entityForm, customerName: undefined, supplierName: undefined }
    //   }
    //   if (!id) return
    //   const item = this.supplierEntity.options.find((item) => item.id === id)
    //   console.log(item)
    //   if (item) {
    //     this.supplierEntity.active = item
    //     this.entityForm.supplierId = id
    //     this.entityForm.supplierName = item.name
    //     this.supplierEntity.value = item.name
    //   } else {
    //     //手写的结算单位赋值
    //     this.entityForm.supplierName = id
    //     this.entityForm.supplierId = ''
    //     this.supplierEntity.active = []
    //     this.supplierEntity.value = ''
    //   }
    // },

    // 'entityForm.contractId'(id) {
    //   console.log('hetong')
    //   console.log(id)
    //   if (!id) return
    //   this.updateAcc()
    //   if (this.entityForm.applyDate && id) {
    //     this.updateAcc(this.entityForm.applyDate, id)
    //   }
    //   if (this.dialogStatus === 'update' || this.dialogStatus === 'review') {
    //     const value = this.filterContractItem(id, 'contractEntity')
    //     this.contractEntity.active = value
    //   }
    // },

    'entityForm.contractId'(id) {
      if (!id) return
      if (this.dialogStatus === 'update' || this.dialogStatus === 'review' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      console.log(form)
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.supplierId
      this.entityForm.contractName = form.secondParty
      this.supplierEntity.value = value[0]
      this.firstpartyEntity.value = form.firstParty
      this.entityForm.firstParty = form.firstParty

      this.secondPartyEntity.value = form.secondParty
      this.entityForm.secondParty = form.secondParty
      this.entityForm = { ...this.entityForm, supplierId: value[0] }
      const item = this.contractEntity.options.find((v) => v.supplierId === value[0])
      if (!item) return
      const itemvv = item.contractBuyList.find((v) => v.id === value[1])
      if (!itemvv) return
      // console.log(this.entityForm)
      // console.log(this.entityForm.supplierName + '结算单位')
      // console.log(this.entityForm.contractName + '合同名称')
      if (this.entityForm.contractName == undefined || this.entityForm.supplierName == '') {
        this.entityForm = { ...this.entityForm, supplierName: form.secondParty, contractName: form.secondParty }
      }

      this.entityForm.productName = itemvv.productName
      this.entityForm.productId = itemvv.productId
      this.nameEntity.active = itemvv.productName
      // this.supplierEntity.value = itemvv.supplierId

      // this.entityForm.supplierId = itemvv.supplierId
      this.entityForm.supplierName = itemvv.secondParty
      if (this.isCustomerchange == true) {
        this.entityForm.cleanStd = itemvv.cleanStd
        this.entityForm.cleanAd = itemvv.cleanAd
        this.entityForm.cleanVdaf = itemvv.cleanVdaf
        this.entityForm.procG = itemvv.procG
        this.entityForm.cleanMt = itemvv.cleanMt
        this.entityForm.procY = itemvv.procY
        this.entityForm.qualCsr = itemvv.qualCsr
        this.entityForm.macR0 = itemvv.macR0
        this.entityForm.recovery = itemvv.recovery
        // 获取化验指标
        this.getProductQualIndicator(itemvv.productId)
      }
    },
    activetype: function (newV, oldV) {
      if (newV) {
        this.getlist(newV)
      } else {
        this.getlist('DRAFT,REJECT,NEW,PASS_FINANCE,PASS')
      }
      this.getnumber()
    },
    'entityForm.applyType'(type) {
      this.entityForm.applyType = type
    }
    // 'actionList.1.active'(n, o) {
    //   console.log(n)
    // },
  },
  created() {
    this.activetype = ''
    this.permsActionbtn(this.curd)
    this.setbuttomlistV(this.curd)
    this.getName()
    //获取供应商列表
    this.getContractList()
    this.getContract()

    this.memoryEntity.fields = createMemoryField({
      fields: ['contractId', 'contractCode', 'supplierId', 'supplierName'],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.awardType = this.smartSearchItems[0].label
    this.punishType = this.smartSearchItems[0].label
    this.entityForm.awardType = this.smartSearchItems[0].label
    this.entityForm.punishType = this.smartSearchItems[0].label

    this.getnumber()
    if (this.$route.params.type) {
      this.activetype = this.$route.params.type
      if (this.$route.params.id) {
        this.handleUpdate(this.$route.params.id, 'review', 'SETTLE_PAY')
      }
    } else {
      this.activetype = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
      this.actionList.forEach((val, index) => {
        if (val.type == this.activetype) {
          val.active = true
        } else {
          val.active = false
        }
      })
    }

    this.isdisabled = false
    this.optionsDisable = {
      disabledDate(time) {
        return time.getTime() > Date.now()
      }
    }
    this.getContractPartyfn()
    this.getfirstparty()
  },
  computed: {},
  methods: {
    async getfirstparty() {
      //获取买方列表
      const res = await this.model.getContractParty()
      if (res.data.length) this.firstpartyEntity.options = res.data
    },
    async handleCancel(row) {
      this.$confirm('确定要作废吗，作废后不可恢复哦！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await this.model.Cancel({
            id: row.id
          })
          if (res) {
            this.$message({ type: 'success', message: '操作成功!' })
            this.getList()
            this.getnumber()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '操作取消' }))
    },
    async lookbtn(item) {
      const { data } = await this.model.findContractBuyFlow(item.id)
      // data.forEach((element, index) => {
      //   element.flowDesignList.forEach((value, indevc) => {
      //     value.active = false
      //     if (element.curFlowCode == value.code) {
      //       // value.active = true
      //       var citrus = element.flowDesignList.slice(0, indevc)
      //       citrus.forEach((value) => {
      //         value.active = true
      //       })
      //     }
      //   })
      // })
      data.forEach((element, index) => {
        element.flowDesignList.forEach((value, indevc) => {
          value.active = false
          if (element.curFlowCode == value.code) {
            var citrus = element.flowDesignList
            citrus.forEach((value) => {
              value.active = true
            })
          }
        })
      })
      this.steplistNEW = data
    },
    //获取客户列表
    async getContractPartyfn() {
      let options = []
      const { data } = await this.model.getContractParty()
      if (data.length) {
        data.forEach((item) => {
          const { name: label, id: value, type: type } = item
          options.push({ label, value, type })
        })
        this.customerEntity.options = options
      }
    },

    handleCustomer({ value, label }) {
      this.entityForm = { ...this.entityForm, customerName: label, customerId: value }
    },

    setnumerfn(carriageUnit) {
      this.entityForm.carriageSettlePayCode = ''
      let carriageSettlePayCode = ''
      var date = new Date()
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var strDate = date.getDate()
      if (month >= 1 && month <= 9) {
        month = '0' + month
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = '0' + strDate
      }
      let text = ''
      if (carriageUnit) {
        text = '-' + carriageUnit
      }
      carriageSettlePayCode = year + month + strDate + this.entityForm.supplierName + text
      this.entityForm = { ...this.entityForm, carriageSettlePayCode: carriageSettlePayCode }
    },
    // start付款信息
    /*
     * 获取(左侧的全部付款信息列表)
     * */
    async getpaymentLeftData() {
      let data = { ...this.entityForm }
      // let data = { beginDate: this.entityForm.beginDate, endDate: this.entityForm.endDate }
      if (this.paydate == '') {
        data.beginDate = ''
        data.endDate = ''
      }
      this.paymentleftListLoading = true
      this.entityForm.settlePayId = this.entityForm.id
      const res = await this.model.findBySettlePayapplyPay({ ...data })
      this.paymentleftListLoading = false
      if (res) {
        this.dataPaymentleftlist = res.data
        this.totalApplyPayleft = res.data.length || 0
      }
    },
    /*
     * 获取(右侧的全部付款信息列表)
     * */
    async getpaymentRightList() {
      let data = {
        id: this.entityForm.id
      }
      this.paymentrightListLoading = true
      const res = await this.model.getApplyPayList(data)
      this.paymentrightListLoading = false
      if (res) {
        this.dataPaymentrighttlist = res.data
        this.totalApplyPayright = res.data.length || 0
      }
    },

    handlePaymentLeftListSizeChange(val) {
      this.paymentLeftListQuery.size = val
      this.getpaymentLeftData()
    },
    handlePaymentLeftListCurrentChange(val) {
      this.paymentLeftListQuery.current = val
      this.getpaymentLeftData()
    },
    handleAddSelectionChangePaymentLeft(val) {
      this.addPaymentList = val.map((v) => {
        v.settlePayId = this.entityForm.id
        v.applyPayId = v.id
        return v
      })
    },
    handleDelSelectionChangePayment(val) {
      this.delPaymentList = val.map((v) => {
        v.settlePayId = this.entityForm.id
        v.applyPayId = v.id
        return v
      })
    },
    handlePaymentRightListSizeChange(val) {
      this.paymentrightListQuery.size = val
      this.getpaymentRightList()
    },
    handlePaymentRightListCurrentChange(val) {
      this.paymentrightListQuery.current = val
      this.getpaymentRightList()
    },
    async savePaymentList(type) {
      let res
      this.paymentrightListQuery = true
      this.paymentLeftListQuery = true
      if (type === 'add') {
        res = await this.model.selectApplyPayList({
          settleApplyPayRelList: this.addPaymentList
        })
      } else {
        res = await this.model.deSelectApplyPayList({
          settleApplyPayRelList: this.delPaymentList
        })
      }
      this.paymentrightListQuery = false
      this.paymentLeftListQuery = false
      if (res) {
        this.getpaymentLeftData()
        this.getpaymentRightList()
      }
    },
    // end付款信息

    // start运费明细
    /*
     * 获取(左侧的全部运费明细列表)
     * */
    async getFreightLeftData() {
      this.FreightleftListLoading = true
      this.entityForm.settlePayId = this.entityForm.id
      const res = await this.model.FreightFindBySettlePay({ ...this.entityForm })
      this.FreightleftListLoading = false
      if (res) {
        this.dataFreightleftlist = res.data
        this.totalFreightLeft = res.data.length || 0
      }
    },
    /*
     * 获取(右侧的全部运费明细列表)
     * */
    async getFreightRightList(type) {
      let data = {
        id: this.entityForm.id
      }
      this.FreightrightListLoading = true
      const res = await this.model.getWeightHouseLogList(data)
      this.FreightrightListLoading = false
      if (res) {
        this.dataFreightrighttlist = res.data
        if (type == 'add' || type == 'update') {
          var newArr = []
          for (let i = 0; i < res.data.length; i++) {
            // 结算金额 =每条数据的运费 * 实收数
            res.data[i].totalNumber = parseFloat(res.data[i].freightPrice * res.data[i].weight).toFixed(2)
            newArr.push(res.data[i])
          }
          let array = newArr
          let sendWeight = 0
          let settleWeight = 0
          let truckCount = 0
          let price = 0 //结算运费均价
          let settleMoney = 0 // 结算金额
          array.forEach((element, index) => {
            sendWeight += element.originalTon
            settleWeight += element.weight
            price += element.freightPrice
            // 结算金额 =每条数据的运费 * 实收数
            settleMoney += Number(element.totalNumber)
            truckCount = index + 1
          })
          // 结算运费均价 =所有运费的汇总 / 车数
          this.entityForm.sendWeight = parseFloat(sendWeight).toFixed(2)
          this.entityForm.settleWeight = parseFloat(settleWeight).toFixed(2)
          this.entityForm.truckCount = truckCount
          if (price) {
            this.entityForm.price = parseFloat(price / truckCount).toFixed(2)
          }
          this.entityForm.settleMoney = parseFloat(settleMoney).toFixed(2)
        }
        this.totalFreightRight = res.data.length || 0
      }
    },

    handleFreightLeftListSizeChange(val) {
      this.FreightLeftListQuery.size = val
      this.getFreightLeftData()
    },
    handleFreightLeftListCurrentChange(val) {
      this.FreightLeftListQuery.current = val
      this.getFreightLeftData()
    },
    handleAddSelectionChangeFreightLeft(val) {
      this.addFreightList = val.map((v) => {
        v.settlePayId = this.entityForm.id
        v.applyPayId = v.id
        return v
      })
    },
    handleDelSelectionChangeFreight(val) {
      this.delFreightList = val.map((v) => {
        v.settlePayId = this.entityForm.id
        v.applyPayId = v.id
        return v
      })
    },
    handleFreightRightListSizeChange(val) {
      this.FreightrightListQuery.size = val
      this.getFreightRightList()
    },
    handleFreightRightListCurrentChange(val) {
      this.FreightrightListQuery.current = val
      this.getFreightRightList()
    },
    async saveFreightList(type) {
      let res
      this.FreightrightListQuery = true
      this.FreightLeftListQuery = true
      if (type === 'add') {
        res = await this.model.selectWeightHouseLogList({
          settlePayId: this.entityForm.id,
          weightHouseLogList: this.addFreightList
        })
      } else {
        res = await this.model.deSelectWeightHouseLogList({
          settlePayId: this.entityForm.id,
          weightHouseLogList: this.delFreightList
        })
      }
      this.FreightrightListQuery = false
      this.FreightLeftListQuery = false
      if (res) {
        this.getFreightLeftData()
        this.getFreightRightList('add')
        this.findCarriageSettlePaySummaryfn()
      }
    },
    async CARRIAGEseachfn() {
      let data = {
        supplierId: this.entityForm.supplierId
      }
      if (!this.checkParamsVV(data)) return false
      const res = await this.model.save({ ...this.entityForm })
      if (res.data) {
        this.entityForm = res.data
        this.isFreightallShow = true
        //列表
        this.getFreightLeftData()
        this.getFreightRightList()
      }
    },

    //  获取运费数据的列表
    async findCarriageSettlePaySummaryfn() {
      this.entityForm.settlePayId = this.entityForm.id
      const res = await this.model.findCarriageSettlePaySummary({ ...this.entityForm })
      this.entityForm = { ...this.entityForm, carriageSettlePaySummaryDtoList: res.data }
    },

    //  end运费明细
    // handleCreate() {
    //   this.dialogStatus = 'create'
    //   this.dialogFormVisible = true
    // },

    // start 历史日志搜索
    async seachfn() {
      this.Settlementdate = []
      this.paydate = []
      let data = {
        contractId: this.entityForm.contractId,
        supplierId: this.entityForm.supplierId,
        productId: this.entityForm.productId,
        beginDate: this.entityForm.beginDate,
        endDate: this.entityForm.endDate
      }
      this.Settlementdate.push(this.entityForm.beginDate)
      this.Settlementdate.push(this.entityForm.endDate)
      this.paydate.push(this.entityForm.beginDate)
      this.paydate.push(this.entityForm.endDate)

      if (!this.checkParamsVV(data)) return false
      const res = await this.model.save({ ...this.entityForm })
      if (res.data) {
        this.entityForm = res.data
        this.isallShow = true
        //全部列表
        this.generateData()
        this.getRightList()
        //获取付款信息
        this.getpaymentLeftData()
        this.getpaymentRightList()
      }
    },

    //START 结算明细
    /*
     * 获取(右侧的当前用户列表)
     * */
    async getRightList(type) {
      let data = {
        id: this.entityForm.id
      }
      this.rightListLoading = true
      const res = await this.model.getWeightHouseInList(data)
      this.rightListLoading = false
      if (res) {
        this.datarighttlist = res.data
        // console.log('获取(右侧的当前用户列表)')
        if (type == 'add' || type == 'update') {
          let array = res.data
          let sendWeight = 0
          let receiveWeight = 0
          let truckCount = 0
          array.forEach((element, index) => {
            sendWeight += element.sendWeight
            if (element.actualWeight) {
              receiveWeight += element.actualWeight
            }

            truckCount = index + 1
          })
          if (sendWeight > 0) {
            this.entityForm.sendWeight = parseFloat(sendWeight).toFixed(2)
          } else {
            this.entityForm.sendWeight = 0
          }
          if (receiveWeight > 0) {
            this.entityForm.receiveWeight = parseFloat(receiveWeight).toFixed(2)
          } else {
            this.entityForm.receiveWeight = 0
          }
          this.entityForm.truckCount = truckCount
          if (res.data.length > 0) {
            if (!this.entityForm.contractPrice) {
              this.entityForm.contractPrice = res.data[0].price
            }
            this.handleBlurcount(this.entityForm)
          }

          this.CalculateSettlementTonnage(this.entityForm)
        }
        this.totalright = res.data.length || 0
      }
    },
    /*
     * 获取(左侧的全部用户列表)
     * */
    async generateData() {
      let data = { ...this.entityForm }

      // let data = { beginDate: this.entityForm.beginDate, endDate: this.entityForm.endDate }

      if (this.Settlementdate == '') {
        data.beginDate = ''
        data.endDate = ''
      }

      this.leftListLoading = true
      const res = await this.model.findBySettlePay({ ...data })
      this.leftListLoading = false
      if (res) {
        this.dataleftlist = res.data
        this.totalleft = res.data.length || 0
      }
    },

    checkParamsVV(data, rule = []) {
      for (const [key, val] of Object.entries(data)) {
        if (rule.includes(key)) continue
        if (val === '' || val === undefined) {
          this.$message({ message: `请检查参数${key}`, type: 'warning' })
          return false
        }
      }
      return true
    },
    handleAddSelectionChange(val) {
      this.addUserList = val.map((v) => {
        return v
      })
    },
    async saveUserList(type) {
      let res
      this.rightListLoading = true
      this.leftListLoading = true
      if (type === 'add') {
        res = await this.model.selectWeightHouseInList({
          settlePayId: this.entityForm.id,
          weightHouseInList: this.addUserList
        })
      } else {
        res = await this.model.deSelectWeightHouseInList({
          settlePayId: this.entityForm.id,
          weightHouseInList: this.delUserList
        })
      }
      this.rightListLoading = false
      this.leftListLoading = false
      if (res) {
        this.getRightList('add')
        this.generateData()
      }
    },
    handleLeftListSizeChange(val) {
      this.leftListQuery.size = val
      this.generateData()
    },
    handleLeftListCurrentChange(val) {
      this.leftListQuery.current = val
      this.generateData()
    },
    handleDelSelectionChange(val) {
      this.delUserList = val.map((v) => {
        return v
      })
    },
    handleRightListSizeChange(val) {
      this.rightListQuery.size = val
      this.getRightList()
    },
    handleRightListCurrentChange(val) {
      this.rightListQuery.current = val
      this.getRightList()
    },
    //END 结算明细

    selectItem(list) {
      //结算明细选择的数据
      this.selectList = list
    },

    // 财务管理   //财务报表付款申请
    async SubmitReview(entityForm) {
      if (this.entityForm.contractName == '') {
        this.entityForm = { ...this.entityForm, contractName: undefined }
      }
      if (this.entityForm.contractName != undefined) {
        // console.log(this.entityForm.supplierName + '结算单位')
        // console.log(this.entityForm.contractName + '合同名称')

        if (this.entityForm.supplierName !== this.entityForm.contractName) {
          this.$message({ message: '合同名称和结算单位名称不一致，请修改后再提交审核', type: 'warning' })
          return
        }
      }
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        const res = await this.model.getApproved(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '提交成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.getnumber()
          this.resetVariant()
          this.dialogFormVisible = false
        }
      }
    },
    async saveEntityForm(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.listV.length) {
          const list = []
          this.listV.forEach((item) => {
            let obj = {}
            obj.productName = item.productName
            obj.productCode = item.productCode
            obj.settleWeight = item.settleWeight
            obj.price = item.price
            obj.settleMoney = item.settleMoney
            obj.remarks = item.remarks
            obj.id = item._id
            list.push({ ...obj })
          })
          if (!this.checkParams(list)) return false
          this.entityFormLoading = true
          const res = await Model.getApproved({ ...this.entityForm, settlePayItemList: list })
          this.entityFormLoading = false
          if (res) {
            this.resetVariant()
            this.getList()
            this.getnumber()
            this.draft.open = false
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else {
          this.$message({ message: '生产数据不能为空', type: 'warning' })
        }
      }
    },

    //保存草稿
    async SaveDraft(entityForm) {
      // if (this.entityForm.applyType == 'PERSONAL') {
      //   //个人结算
      //   this.rules = {}
      // } else {
      //   this.rules = {
      //     applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
      //     productName: { required: true, message: '请选择', trigger: 'blur' },
      //     supplierName: { required: true, message: '请选择结算单位', trigger: 'blur' },
      //     settleMoney: { required: true, message: '请选输入结算金额', trigger: 'blur' },
      //     settleWeight: { required: true, message: '请选输入结算吨数', trigger: 'blur' },
      //     truckCount: { required: true, message: '请选输入车数', trigger: 'blur' },
      //   }
      // }
      if (this.entityForm.applyType == 'PERSONAL') {
        this.rules = {}
      }
      if (this.entityForm.applyType == 'TRADE') {
        //贸易往来
        this.rules = {
          applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
          contractId: { required: true, message: '请选择合同', trigger: 'blur' },
          productName: { required: true, message: '请选择品名', trigger: 'blur' },
          supplierId: { required: true, message: '请选择结算单位', trigger: 'blur' },
          beginDate: { required: true, message: '请选择结算周期', trigger: 'blur' },
          endDate: { required: true, message: '请选择结算周期', trigger: 'blur' },
          settleMoney: { required: true, message: '请选输入结算金额', trigger: 'blur' },
          settleWeight: { required: true, message: '请选输入结算吨数', trigger: 'blur' },
          contractPrice: { required: true, message: '请选输入合同单价', trigger: 'blur' }
        }
      }
      if (this.entityForm.applyType == 'CARRIAGE') {
        //运费结算
        this.rules = {
          applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
          supplierId: { required: true, message: '请选择结算单位', trigger: 'blur' },
          // carriageUnit: { required: true, message: '请选输入运输单位', trigger: 'blur' },
          carriageSettlePayCode: { required: true, message: '请选输入运输编号', trigger: 'blur' }
        }
      }
      if (this.entityForm.applyType == 'GOOD') {
        //物资结算
        this.rules = {
          applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
          supplierName: { required: true, message: '请选择结算单位', trigger: 'blur' }
        }
      }

      if (this.entityForm.contractName != undefined) {
        if (this.entityForm.supplierName !== this.entityForm.contractName) {
          this.$message({ message: '合同名称和结算单位名称不一致，请修改后再提交审核', type: 'warning' })
          return
        }
      }
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        const res = await this.model.SaveDraftfn(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '保存草稿成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.getnumber()
          this.resetVariant()
        }
      }
    },
    async getProductQualIndicator(id) {
      const { data } = await this.model.getProductQualIndicator({
        id: id,
        source: 'SETTLE_PAY',
        beginDate: this.entityForm.beginDate,
        endDate: this.entityForm.endDate
      })
      if (data) {
        this.entityForm.assayCleanStd = data.std
        this.entityForm.assayCleanAd = data.ad
        this.entityForm.assayCleanVdaf = data.vdaf
        this.entityForm.assayProcG = data.g
        this.entityForm.assayCleanMt = data.mt
        this.entityForm.assayProcY = data.y
        this.entityForm.assayQualCsr = data.csr
        this.entityForm.assayMacR0 = data.macR0
        this.entityForm.assayRecovery = data.recovery
      }
    },
    change(e) {
      if (e) {
        this.Settlementcycle = e
        this.entityForm.beginDate = e[0]
        this.entityForm.endDate = e[1]
        this.getProductQualIndicator(this.entityForm.productId)
      } else {
        this.Settlementcycle = []
        this.entityForm.beginDate = ''
        this.entityForm.endDate = ''
        this.getProductQualIndicator(this.entityForm.productId)
      }
    },
    changeVV(e) {
      if (e) {
        this.Settlementdate = e
        this.entityForm.beginDate = e[0]
        this.entityForm.endDate = e[1]
      } else {
        this.Settlementdate = []
      }
    },
    paychangeVV(e) {
      // console.log('付款信息' + e)
      if (e) {
        this.paydate = e
        this.entityForm.beginDate = e[0]
        this.entityForm.endDate = e[1]
      } else {
        this.paydate = []
      }
    },
    changeYY(e) {
      if (e) {
        this.SettlementFreightdate = e
        this.entityForm.beginDate = e[0]
        this.entityForm.endDate = e[1]
      } else {
        this.SettlementFreightdate = []
      }
    },

    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0 || columnIndex == 1 || columnIndex == 4 || columnIndex == 5 || column.label === '操作') {
        if (row.settlePayItemList === undefined) {
          return {
            rowspan: 0,
            colspan: 1
          }
        }
        const len = row.settlePayItemList.length
        if (len) {
          return {
            rowspan: len,
            colspan: 1
          }
        } else {
          return {
            rowspan: 1,
            colspan: 1
          }
        }
      }
    },

    changetab(tab, event) {
      if (tab.name == 'PERSONAL') {
        this.rules = {}
      }
      if (tab.name == 'TRADE') {
        //贸易往来
        this.rules = {
          applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
          contractId: { required: true, message: '请选择合同', trigger: 'blur' },
          productName: { required: true, message: '请选择品名', trigger: 'blur' },
          supplierId: { required: true, message: '请选择结算单位', trigger: 'blur' },
          beginDate: { required: true, message: '请选择结算周期', trigger: 'blur' },
          endDate: { required: true, message: '请选择结算周期', trigger: 'blur' },
          settleMoney: { required: true, message: '请选输入结算金额', trigger: 'blur' },
          settleWeight: { required: true, message: '请选输入结算吨数', trigger: 'blur' },
          contractPrice: { required: true, message: '请选输入合同单价', trigger: 'blur' }
        }
      }
      if (tab.name == 'CARRIAGE') {
        //运费结算
        this.rules = {
          applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
          supplierId: { required: true, message: '请选择结算单位', trigger: 'blur' },
          // carriageUnit: { required: true, message: '请选输入运输单位', trigger: 'blur' },
          carriageSettlePayCode: { required: true, message: '请选输入运输编号', trigger: 'blur' }
        }
      }
      if (tab.name == 'GOOD') {
        //物资结算
        this.rules = {
          applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
          supplierName: { required: true, message: '请选择结算单位', trigger: 'blur' }
        }
      }
      if (this.dialogStatus == 'settlePay') {
        this.supplierEntity.value = ''
        this.entityForm.supplierId = ''
        this.firstpartyEntity.value = ''
        this.contractEntity.active = []
        this.$nextTick(() => {
          // 请求成功之后，在这里执行
          this.entityForm = { ...Model.model }
          this.entityForm.applyType = tab.name
          this.$forceUpdate()
        })
      } else {
        this.$nextTick(() => {
          this.entityForm.applyType = tab.name
          this.$forceUpdate()
        })
      }
    },
    async getnumber() {
      const res = await this.model.getcountByState()
      this.countList = res.data
    },
    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },
    checkParams(list, rule = ['remarks']) {
      for (const item of list) {
        for (const [key, val] of Object.entries(item)) {
          if (rule.includes(key)) continue
          if (val === '' || val === undefined) {
            this.$message({ message: `请检查参数${key}`, type: 'warning' })
            return false
          }
        }
      }
      return true
    },
    handleAdd() {
      const form = { ...this.form }
      form._id = Math.random().toFixed(6).slice(-6)
      this.listV.push(form)
    },
    handleRemove({ _id }) {
      const index = this.listV.findIndex((item) => item._id === _id)
      if (index === -1) return false
      this.listV.splice(index, 1)
    },
    handleBlurVV() {
      this.listV.forEach((item, index) => {
        if (!isNaN(item.settleWeight * item.price)) {
          item.settleMoney = parseFloat(item.settleWeight * item.price).toFixed(2)
        }
      })
    },
    handleBluryf(entityForm) {
      if (entityForm.sendWeight != '' && entityForm.price != '') {
        this.settleMoney = (entityForm.sendWeight * entityForm.price).toFixed(2)
        if (isNaN(this.settleMoney) == false) {
          this.entityForm = { ...this.entityForm, settleMoney: this.settleMoney }
        }
      }
    },

    // 打印
    async handlePrint(row) {
      const { data } = await this.model.getprint(row.id)
      let objectUrl = window.URL.createObjectURL(new Blob([data], { type: 'application/pdf' }))
      window.open(objectUrl)
    },
    // 下载
    async handDownload(row) {
      const { data } = await this.model.getdownload(row.id)
      let url = window.URL.createObjectURL(new Blob([data]))
      let a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      let title = '付款结算.xls'
      a.setAttribute('download', title)
      document.body.appendChild(a)
      a.click() //执行下载
      window.URL.revokeObjectURL(a.href) // 清除url
      document.body.removeChild(a) //移除dom
    },

    handleBlur(entityForm) {
      let number = parseFloat(entityForm.settleWeight * entityForm.price).toFixed(2)
      if (entityForm.settleWeight != '' && entityForm.price != '') {
        this.settleMoney = number
        if (isNaN(this.settleMoney) == false) {
          this.entityForm = { ...this.entityForm, settleMoney: this.settleMoney }
        }
      }
      this.handleBlurH(entityForm)
      this.handleBlurcalculation(entityForm)
      this.calculationFinalMoney(entityForm)
    },
    handleBlurcount(entityForm) {
      // 结算单价=合同单价-质量扣罚金
      let total = 0
      if (entityForm.contractPrice) {
        let punishCleanStd = entityForm.punishCleanStd ? entityForm.punishCleanStd : 0
        let punishCleanAd = entityForm.punishCleanAd ? entityForm.punishCleanAd : 0
        let punishCleanVdaf = entityForm.punishCleanVdaf ? entityForm.punishCleanVdaf : 0
        let punishProcG = entityForm.punishProcG ? entityForm.punishProcG : 0
        let punishCleanMt = entityForm.punishCleanMt ? entityForm.punishCleanMt : 0
        let punishProcY = entityForm.punishProcY ? entityForm.punishProcY : 0
        let punishQualCsr = entityForm.punishQualCsr ? entityForm.punishQualCsr : 0
        let punishMacR0 = entityForm.punishMacR0 ? entityForm.punishMacR0 : 0
        let punishRecovery = entityForm.punishRecovery ? entityForm.punishRecovery : 0
        // total =
        //   parseFloat(entityForm.contractPrice) -
        //   parseFloat(punishCleanStd) -
        //   parseFloat(punishCleanAd) -
        //   parseFloat(punishCleanVdaf) -
        //   parseFloat(punishProcG) -
        //   parseFloat(punishCleanMt) -
        //   parseFloat(punishProcY) -
        //   parseFloat(punishQualCsr) -
        //   parseFloat(punishMacR0) -
        //   parseFloat(punishRecovery)

        total = new Decimal(entityForm.contractPrice)
          .sub(punishCleanStd)
          .sub(punishCleanAd)
          .sub(punishCleanVdaf)
          .sub(punishProcG)
          .sub(punishCleanMt)
          .sub(punishProcY)
          .sub(punishQualCsr)
          .sub(punishMacR0)
          .sub(punishRecovery)
          .toString()
      }
      if (total) {
        this.entityForm.price = total
      }
      if (this.entityForm.settleWeight && this.entityForm.price) {
        this.entityForm.settleMoney = parseFloat(this.entityForm.settleWeight * this.entityForm.price).toFixed(2)
      }
    },
    CalculateSettlementTonnage(entityForm) {
      //  结算吨数=原发吨数-补水吨数-路耗
      let total = ''
      // if (entityForm.settleWeight) {
      let sendWeight = entityForm.sendWeight ? entityForm.sendWeight : 0
      let moisturizingWeight = entityForm.moisturizingWeight ? entityForm.moisturizingWeight : 0
      let roadCost = entityForm.roadCost ? entityForm.roadCost : 0
      total = parseFloat(sendWeight) - parseFloat(moisturizingWeight) - parseFloat(roadCost)
      // }
      if (total) {
        this.entityForm.settleWeight = parseFloat(total).toFixed(2)
      }
      if (this.entityForm.settleWeight && this.entityForm.price) {
        this.entityForm.settleMoney = parseFloat(this.entityForm.settleWeight * this.entityForm.price).toFixed(2)
      }
    },
    pricechangefn(entityForm) {
      //结算金额=结算吨数*结算运费均价
      let price = entityForm.price ? entityForm.price : 0
      let settleWeight = entityForm.settleWeight ? entityForm.settleWeight : 0
      this.entityForm.settleMoney = parseFloat(price * settleWeight).toFixed(2)
    },

    calculationFinalMoney(entityForm) {
      //entityForm.punishMoney 扣罚金
      // entityForm.awardMoney 奖励金
      if (this.entityForm.settleMoney) {
        if (this.entityForm.awardMoney) {
          let num1 = parseFloat(Number(this.entityForm.settleMoney) + Number(this.entityForm.awardMoney)).toFixed(2)
          // console.log('加奖励金' + num1)
          this.entityForm = { ...this.entityForm, finalMoney: num1 }
        }
        if (this.entityForm.punishMoney) {
          // let num2 = parseInt(this.entityForm.settleMoney) - parseInt(this.entityForm.punishMoney)
          let num2 = parseFloat(Number(this.entityForm.settleMoney) - Number(this.entityForm.punishMoney)).toFixed(2)
          // console.log('减扣罚金' + num2)
          this.entityForm = { ...this.entityForm, finalMoney: num2 }
        }
        if (this.entityForm.awardMoney && this.entityForm.punishMoney) {
          // let tsnumber = Number(this.entityForm.punishMoney) + Number(this.entityForm.awardMoney)
          // let num3 = Number(this.entityForm.settleMoney) - Number(tsnumber)
          let num3 = parseFloat(
            Number(this.entityForm.settleMoney) - Number(this.entityForm.punishMoney) + Number(this.entityForm.awardMoney)
          ).toFixed(2)
          // console.log('减扣罚金加奖励金' + num3)
          this.entityForm = { ...this.entityForm, finalMoney: num3 }
        }
      }
    },

    handleinput(entityForm) {
      this.entityForm = { ...this.entityForm, truckCount: entityForm.truckCount }
    },
    handleinput1(entityForm) {
      this.entityForm = { ...this.entityForm, truckCount: entityForm.truckCount }
      this.handleBlurH(entityForm)
      this.handleBlurcalculation(entityForm)
    },

    handleBlurH(entityForm) {
      this.entityForm = { ...this.entityForm, settleWeight: entityForm.settleWeight, truckCount: entityForm.truckCount }
      let number = 0
      if (entityForm.awardType == '按吨') {
        number = this.entityForm.settleWeight
      } else if (entityForm.awardType == '车数') {
        number = this.entityForm.truckCount
      }
      if (entityForm.awardBase != undefined && number != undefined) {
        this.entityForm.awardMoney = (entityForm.awardBase * number).toFixed(2)
      }
      this.calculationFinalMoney(entityForm)
    },
    sethandleBlur(entityForm) {
      this.entityForm = { ...this.entityForm, awardMoney: entityForm.awardMoney }
      this.calculationFinalMoney(this.entityForm)
    },
    sethandleBlurf(entityForm) {
      this.entityForm = { ...this.entityForm, punishMoney: entityForm.punishMoney }
      this.calculationFinalMoney(this.entityForm)
    },

    handleBlurcalculation(entityForm) {
      this.entityForm = { ...this.entityForm, settleWeight: entityForm.settleWeight, truckCount: entityForm.truckCount }
      let number = 0
      if (entityForm.punishType == '按吨') {
        number = this.entityForm.settleWeight
      } else if (entityForm.punishType == '车数') {
        number = this.entityForm.truckCount
      }
      if (entityForm.punishBase != undefined && number != undefined) {
        this.entityForm.punishMoney = (entityForm.punishBase * number).toFixed(2)
      }
      this.calculationFinalMoney(entityForm)
    },

    changeKey(key) {
      this.entityForm.awardBase = ''
      this.entityForm.awardMoney = ''
      const index = this.smartSearchItems.findIndex((val) => val.id === key)
      if (~index) {
        this.entityForm.awardType = this.smartSearchItems[index].label
        this.awardType = this.smartSearchItems[index].label
      }
    },
    changeKeyV(key) {
      this.entityForm.punishBase = ''
      this.entityForm.punishMoney = ''
      const index = this.smartSearchItems.findIndex((val) => val.id === key)
      if (~index) {
        this.punishType = this.smartSearchItems[index].label
        this.entityForm.punishType = this.smartSearchItems[index].label
      }
    },

    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      this.isdisabled = false
      this.isCustomerchange = false
      this.reviewLogList = []
      this.steplist = []
      this.contractEntity.active = []
      this.supplierEntity.value = ''
      this.firstpartyEntity.value = ''
      this.entityForm.supplierId = ''
      this.listV = []
      this.handleAdd()
      this.Settlementcycle = ''
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.awardType = this.smartSearchItems[0].label
      this.punishType = this.smartSearchItems[0].label
      this.entityForm.awardType = this.smartSearchItems[0].label
      this.entityForm.punishType = this.smartSearchItems[0].label
      this.getList()
    },
    async getContractList() {
      const { data } = await contractList()
      if (data.length) this.supplierEntity.options = data
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) { }
    },
    // 获取合同选择接口
    async getContract() {
      try {
        const { data } = await chooseContractNotSettle()
        this.contractEntity.options = this.formatContract({
          data,
          key: { supplierName: 'displayName', supplierId: 'id', contractBuyList: 'contractBuyList' }
        })
      } catch (error) { }
    },
    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    // filterContractItem(value, target) {
    //   if (Array.isArray(value) && value.length) {
    //     return this[target].options
    //       .find((item) => item.customerId === value[0])
    //       .contractSellList.find((item) => item.customerId === value[1])
    //   } else if (typeof value === 'string') {
    //     const val = []
    //     for (const list of this[target].options) {
    //       for (const item of list.contractSellList) {
    //         if (item.customerId === value) {
    //           val.push(list.customerId, value)
    //           return val
    //         }
    //       }
    //     }
    //   } else {
    //     return []
    //   }
    // },

    // 合同改变同步
    handleSupplier({ value, label }) {
      // this.entityForm.supplierId = value
      // this.entityForm.supplierName = label
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label }
    },
    selectBlur(value) {
      this.supplierEntity.value = value.target.value
      this.entityForm.supplierId = value.target.value
      this.entityForm = { ...this.entityForm, supplierName: value.target.value }
      // this.entityForm=    e.target.value
    },
    handleNameEntityV() {
      this.isCustomerchange = true
      this.isdisabled = true
    },
    handleNameEntity(val) {
      // this.entityForm.productName = val
      this.entityForm = { ...this.entityForm, productName: val }
    },
    async handleUpdate(item, type, gettype) {
      this.Settlementcycle = []
      this.Settlementdate = []
      this.paydate = []
      this.SettlementFreightdate = []

      if (item.applyType == 'PERSONAL') {
        this.rules = {}
      }
      if (item.applyType == 'TRADE') {
        //贸易往来
        this.rules = {
          applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
          contractId: { required: true, message: '请选择合同', trigger: 'blur' },
          productName: { required: true, message: '请选择品名', trigger: 'blur' },
          supplierId: { required: true, message: '请选择结算单位1', trigger: 'blur' },
          beginDate: { required: true, message: '请选择结算周期', trigger: 'blur' },
          endDate: { required: true, message: '请选择结算周期', trigger: 'blur' },
          settleMoney: { required: true, message: '请选输入结算金额', trigger: 'blur' },
          settleWeight: { required: true, message: '请选输入结算吨数', trigger: 'blur' },
          contractPrice: { required: true, message: '请选输入合同单价', trigger: 'blur' }
        }
      }
      if (item.applyType == 'CARRIAGE') {
        //运费结算
        this.rules = {
          applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
          supplierId: { required: true, message: '请选择结算单位', trigger: 'blur' },
          // carriageUnit: { required: true, message: '请选输入运输单位', trigger: 'blur' },
          carriageSettlePayCode: { required: true, message: '请选输入运输编号', trigger: 'blur' }
        }
      }
      if (item.applyType == 'GOOD') {
        //物资结算
        this.rules = {
          applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
          supplierName: { required: true, message: '请选择结算单位3', trigger: 'blur' }
        }
      }

      if (typeof item == 'object') {
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        } else if (type == 'update') {
          this.isshow = true
          this.dialogStatus = 'update'
        } else if (type == 'watch') {
          this.isshow = false
          this.dialogStatus = 'watch'
        }
        this.dialogFormVisible = true
        const { data } = await this.model.getUploadList(item.id)
        this.listV = data.settlePayItemList
        this.entityForm = { ...data }
        // console.log(this.entityForm)

        if (gettype) {
          const { data } = await Model.getlclist({ type: gettype })
          this.entityForm.stepcode = this.entityForm.curFlowCode
          this.entityForm.stepName = this.entityForm.curFlowName

          // data.forEach((element, index) => {
          //   if (element.code == this.entityForm.stepcode) {
          //     var citrus = data.slice(0, index)
          //     citrus.forEach((value) => {
          //       value.active = true
          //     })
          //     // element.active = true
          //   }
          // })
          data.forEach((element, index) => {
            element.active = false
            if (element.code === this.entityForm.stepcode) {
              var citrus = data
              citrus.forEach((value) => {
                value.active = true
              })
            }
          })
          this.steplist = data
        }

        //全部列表
        if (this.entityForm.applyType == 'TRADE') {
          this.Settlementcycle.push(this.entityForm.beginDate)
          this.Settlementcycle.push(this.entityForm.endDate)
          this.paydate.push(this.entityForm.beginDate)
          this.paydate.push(this.entityForm.endDate)
          this.Settlementdate.push(this.entityForm.beginDate)
          this.Settlementdate.push(this.entityForm.endDate)
          //贸易往来
          this.isallShow = true
          if (this.dialogStatus == 'update' || this.dialogStatus == 'create') {
            this.generateData()
            this.getRightList('update')
            this.getpaymentLeftData()
            this.getpaymentRightList()
          }
        }
        if (this.entityForm.applyType == 'CARRIAGE') {
          this.SettlementFreightdate.push(this.entityForm.beginDate)
          this.SettlementFreightdate.push(this.entityForm.endDate)
          this.isFreightallShow = true
          if (this.dialogStatus == 'update' || this.dialogStatus == 'create') {
            this.getFreightLeftData()
            this.getFreightRightList('update')
          }
          // this.findCarriageSettlePaySummaryfn()
        }

        this.supplierEntity.value = this.entityForm.supplierId

        this.awardType = this.entityForm.awardType
        this.punishType = this.entityForm.punishType
        // this.contractEntity.active = this.entityForm.contractId
        // if (this.entityForm.supplierId) {
        //   this.supplierEntity.value = this.entityForm.supplierId
        // } else {
        //   this.supplierEntity.value = this.entityForm.supplierName
        // }
        this.reviewLogList = data.reviewLogList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((item, index) => {
            newarry.push(item.reviewMessage)
          })
          // this.entityForm.reviewMessage = newarry.toString()
          this.entityForm.reviewMessage = ''
        }
      } else if (typeof item == 'string') {
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        }
        this.dialogFormVisible = true
        const { data } = await this.model.getUploadList(item)
        this.awardType = this.entityForm.awardType
        this.punishType = this.entityForm.punishType
        // this.contractEntity.active = this.entityForm.contractId
        this.supplierEntity.value = this.entityForm.supplierId
        this.reviewLogList = data.reviewLogList
        this.entityForm = { ...data }
        if (gettype) {
          const { data } = await Model.getlclist({ type: gettype })
          this.entityForm.stepcode = this.entityForm.curFlowCode
          this.entityForm.stepName = this.entityForm.curFlowName
          data.forEach((element, index) => {
            element.active = false
            if (element.code === this.entityForm.stepcode) {
              var citrus = data
              citrus.forEach((value) => {
                value.active = true
              })
            }
          })
          this.steplist = data
        }
        //全部列表
        if (this.entityForm.applyType == 'TRADE') {
          this.isallShow = true
          this.Settlementcycle.push(this.entityForm.beginDate)
          this.Settlementcycle.push(this.entityForm.endDate)
          this.paydate.push(this.entityForm.beginDate)
          this.paydate.push(this.entityForm.endDate)
          this.Settlementdate.push(this.entityForm.beginDate)
          this.Settlementdate.push(this.entityForm.endDate)
          if (this.dialogStatus == 'update' || this.dialogStatus == 'create') {
            this.generateData()
            this.getRightList('update')
            this.getpaymentLeftData()
            this.getpaymentRightList()
          }
        }
        if (this.entityForm.applyType == 'CARRIAGE') {
          this.SettlementFreightdate.push(this.entityForm.beginDate)
          this.SettlementFreightdate.push(this.entityForm.endDate)
          this.isFreightallShow = true
          if (this.dialogStatus == 'update' || this.dialogStatus == 'create') {
            this.getFreightLeftData()
            this.getFreightRightList('update')
          }
          // this.findCarriageSettlePaySummaryfn()
        }
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((itemv, index) => {
            newarry.push(itemv.reviewMessage)
          })
          this.entityForm.reviewMessage = newarry.toString()
        }
      }
    },

    //点击状态获取数据
    async getlist(newV) {
      this.activetype = newV
      this.actionList.forEach((val, index) => {
        if (val.type == newV) {
          val.active = true
        } else {
          val.active = false
        }
      })
      this.filtersSeach.filter_INS_state = newV
      this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    },
    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    // 更新累计Acc
    // async updateAcc() {
    //   const { date, contractId } = this.entityForm
    //   if (!date || !contractId) return false
    //   const paramsAcc = await this.getAccValues({ date, contractId })
    //   Object.keys(paramsAcc).forEach((key) => {
    //     this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
    //     this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
    //   })
    //   this.computeWayCostAcc()
    // },
    //去审核
    async passfn(id, reviewMessage, curFlowCode) {
      // if (state == 'NEW') {
      const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage, curFlowCode: curFlowCode })
      // } else if (state == 'PASS') {
      //   const res = await this.model.getfinancePass({ id: id, reviewMessage: reviewMessage })
      // }
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
      this.resetVariant()
      this.getlist(this.activetype)
      this.getnumber()
    },

    async rejectfn(id, reviewMessage, curFlowCode) {
      const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage, curFlowCode: curFlowCode })
      this.getnumber()
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>

<style scoped>
.jsmx {
  margin-bottom: 20px;
}
.jsmx .el-table {
  border-bottom: none;
  border-right: none;
}
.search-component >>> .el-input__inner {
  border: none;
}
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
.filter-item {
  display: inline-table;
}
</style>

<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.colorBLUE {
  color: #2f79e8 !important;
}
.bordercolorBLUE {
  border: solid 1px #2f79e8 !important;
}
.bzminbox:first-child {
  width: 50% !important;
  .lin {
    width: 0px !important;
    display: none;
  }
  .context {
    width: 100% !important;
  }
}
.bzbox {
  width: 100%;
  display: flex;
  font-size: 12px;
  .bzminbox {
    width: 100%;
    display: flex;
    .icon {
      font-size: 30px;
    }
    .context {
      width: 50%;
      color: #ccc;
      text-align: center;
    }
    .lin {
      width: 50%;
      height: 1px;
      border: solid 1px #606266;
      margin-top: 15px;
    }
  }
}

.bzminboxV:first-child {
  width: 50% !important;
  .lin {
    width: 0px !important;
    display: none;
  }
  .context {
    width: 100% !important;
  }
}
.bzboxV {
  width: 100%;
  display: flex;
  font-size: 12px;
  .bzminboxV {
    width: 100%;
    display: flex;
    .icon {
      font-size: 30px;
    }
    .context {
      width: 50%;
      color: #ccc;
      text-align: center;
    }
    .lin {
      width: 50%;
      height: 1px;
      border: solid 1px #606266;
      margin-top: 15px;
    }
  }
}

.form-edit-content {
  padding-bottom: 0px;
  .left-flex,
  .right-form {
    border: 1px solid #f1f1f2;
  }
}

::v-deep .el-transfer-panel {
  width: 40%;
}

.tablebox {
  width: 100%;
  .tablehead {
    width: 100%;
    display: flex;
    & > div {
      width: calc(100% / 10);
      background-color: #2f79e8;
      text-align: center;
      padding: 10px;
      color: #fff;
    }
  }
  .tablecon {
    width: 100%;
    display: flex;
    border-left: solid 1px #f1f1f2;
    border-right: solid 1px #f1f1f2;
    border-bottom: solid 1px #f1f1f2;
    & > div {
      width: calc(100% / 10);
      border-right: solid 1px #f1f1f2;
      text-align: center;
      padding: 10px;
    }
  }
}

::v-deep .el-date-editor .el-range-separator {
  padding: 0 0;
}
::v-deep .el-form-item__label {
  font-weight: 400;
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
}
.smart-search {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  width: 47%;
  margin-bottom: 5px;
  border-radius: 3px;

  .prepend {
    background-color: #f5f7fa;
    padding-right: 10px;
    border-right: solid 1px #dcdfe6;
    .el-dropdown {
      cursor: pointer;
    }

    .el-icon-arrow-down {
      width: 26px;
      text-align: center;
    }

    .search-key {
      display: inline-block;
      height: 30px;
      line-height: 30px;
    }
  }

  .search-component {
    flex: 1;
    .el-input {
      width: 100%;
      height: 30px;
      line-height: 30px;
      .el-input__inner {
        border: none;
      }
      & .is-focus {
        .el-input__inner {
          border-color: #dcdfe6 !important;
        }
      }
    }
  }
}
// .form-layoutV {
//   .el-row {
//     margin-bottom: 10px;
//   }
// }
.tipbox {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  .tipline {
    margin-left: 20px;
    .redtext {
      font-size: 1.5rem;
      color: red;
    }
  }
}
.contentbox {
  width: 500px;
  margin: 100px auto 0 auto;
  font-size: 12px;
  .line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tsline {
    float: right;
    margin-top: 40px;
  }
  .inputbox {
    width: 100px;
    margin-bottom: 10px;
    border-bottom: solid 1px #ccc;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>

