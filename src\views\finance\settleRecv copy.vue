<template>
  <div class="app-container">
    <!-- <el-date-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate" placeholder="选择日期"
                    :clearable="false" /> -->
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :statetype="activetype" :buttomlist="buttomlist"
            :showSummary='true' :countList="countList" :actionList="actionList" title="供应商" otherHeight="220">

      <el-table-column label="状态" slot="state" align="center">
        <template slot-scope="scope">
          <!-- 通用审核状态 NEW-待审核、PASS-通过、REJECT-拒绝 -->
          <!-- <el-tag :disable-transitions="true" class="light" style="margin-right: 2px"
                  :type="scope.row.state=='NEW'?'danger':'info'" effect='plain'>
            <span v-if="scope.row.state=='DRAFT'">草稿</span>
            <span v-if="scope.row.state=='NEW'">待审核</span>
            <span v-if="scope.row.state=='PASS'">已审核</span>
            <span v-if="scope.row.state=='REJECT'">拒绝</span>  财务审批
          </el-tag> -->

          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :(scope.row.state == 'PAYED' ? 'success' : (scope.row.state == 'PASS_FINANCE' ? 'success' : 'info'))))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '总经理待审核' : (scope.row.state == 'PASS' ? '财务待审核' : (scope.row.state == 'REJECT' ? '拒绝':(scope.row.state == 'PAYED' ? '已付款' : (scope.row.state == 'PASS_FINANCE' ? '已审核' : ''))))) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="结算周期" slot="beginDate" align="center" width="180">
        <template slot-scope="scope">
          <span v-if="scope.row.beginDate">
            {{scope.row.beginDate}}至{{scope.row.endDate}}
          </span>
        </template>
      </el-table-column>

      <el-table-column slot="isSettle" label="结算状态" align="center">
        <template slot-scope="scope" v-if="scope.row.isSettle">
          <el-tag :type="(scope.row.isSettle=='Y' ? 'success':(scope.row.isSettle == 'N' ? 'danger' : ''))" size="mini">
            {{scope.row.isSettle == 'Y' ? '已结清' : (scope.row.isSettle == 'N' ? '未结清' :  '') }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" max-width="150" width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <!-- {{scope.row.state}} -->
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <div v-if="perms[`${curd}:ismanagerreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state =='NEW'" color="#FF726B" @click="handleUpdate(scope.row,'review')">
                去审核(总经理)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:isFinancereview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state=='PASS'" color="#33CAD9" @click="handleUpdate(scope.row,'review')">
                去审核(财务)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" color="#FF726B"
                      @click="handleUpdate(scope.row,'update')">
                编辑
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'PASS_FINANCE'" color="#FF726B"
                      @click="handlePrint(scope.row,'update')">
                <span v-if="scope.row.isPrint=='Y'">补打</span>
                <span v-else>打印</span>
              </el-tag>
            </div>

            <div v-if="scope.row.state=='DRAFT,REJECT' || scope.row.state=='PASS' 
                 || scope.row.state == 'PASS_FINANCE' || scope.row.state=='NEW' ">
              <el-tag class="opt-btn" color="#FF726B" @click="handleUpdate(scope.row,'watch')">
                查看
              </el-tag>
            </div>

            <!-- <div v-if="perms[`${curd}:delete`] || false" style="margin-left:5px">
              <el-tag class="opt-btnV" effect="plain" style="color:#33CAD9;border-color: #33CAD9; cursor: pointer;"
                      v-if="scope.row.state == 'PASS_FINANCE' || scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'"
                      @click="handleDel(scope.row)">删除
              </el-tag>
            </div> -->

            <div v-if="perms[`${curd}:delete`] || false" style="margin-left:5px">
              <el-tag class="opt-btn" style="cursor: pointer;" color="#2f79e8"
                      v-if="scope.row.state == 'PASS_FINANCE' || scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'"
                      @click="handleDel(scope.row)">删除
              </el-tag>
            </div>

          </div>
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="right" v-if="dialogFormVisible">

        <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="reviewLogList">
              <el-steps :active="reviewLogList.length">
                <el-step v-for="(item,index) in reviewLogList" :key="index"
                         :title="item.reviewResult + ' ( 审批意见：'+item.reviewMessage+' )'"
                         :description="item.createDate+' '+item.reviewUserName">
                </el-step>
              </el-steps>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div class="form-title">基础信息</div>
            <div class="form-layout form-layoutV">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="applyDate">
                    <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同" prop="contractId">
                    <!-- :disabled="dialogStatus!='update'?true:false" -->
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 @change="handleNameEntityV" filterable clearable placeholder="请选择合同" style="width:100%" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <!-- :disabled="dialogStatus!='update'?true:isdisabled" -->
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="结算单位" prop="customerName">
                    <!-- :disabled="dialogStatus!='update'?true:isdisabled" -->
                    <el-select v-model="customerEntity.active" placeholder="请选择收货单位" filterable clearable @change="handleCustomer"
                               @blur="selectBlur" style="width:100%">
                      <el-option v-for="item in customerEntity.options" :key="item.value" :label="item.label" :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                
              </el-row>
            </div>

            <div class="form-title">结算信息</div>
            <div class="form-layout form-layoutV">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="原发车数" prop="truckCount">
                    <el-input v-model="entityForm.truckCount" @input="handleinput1(entityForm)" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="原发吨数" prop="sendWeight">
                    <el-input v-model="entityForm.sendWeight" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="实收吨数" prop="receiveWeight">
                    <el-input v-model="entityForm.receiveWeight" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="结算吨数" prop="settleWeight">
                    <el-input v-model="entityForm.settleWeight" autocomplete="off" clearable @input="handleBlur(entityForm)" />
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="单价" prop="price">
                    <el-input v-model="entityForm.price" clearable oninput="value=value.replace(/[^0-9.]/g,'')" @clear="clearBtn"
                              @input="handleBlur(entityForm)" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="结算金额" prop="settleMoney">
                    <el-input v-model="entityForm.settleMoney" autocomplete="off" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')" />
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- 
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="煤的类型" prop="coalCategory">
                    <dict-select v-model="entityForm.coalCategory" type="stock_type" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤种" prop="coalType">
                    <category-select :value.sync="entityForm.coalType" />
                  </el-form-item>
                </el-col>
              </el-row> -->

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="奖励金" prop="awardType">
                    <div style="display: flex;flex-direction: row; align-items: center;">
                      <div class="smart-search">
                        <div class="prepend">
                          <el-dropdown placement="bottom-start" @command="changeKey" trigger="click">
                            <div class="el-dropdown-link">
                              <i class="el-icon-arrow-down el-icon--right"></i>
                            </div>
                            <el-dropdown-menu slot="dropdown" style="width: 100px; max-height:500px;overflow: auto;">
                              <el-dropdown-item v-for="item in smartSearchItems" :key="item.id" :command="item.id">
                                {{ item.label }}
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                          <span class="search-key">{{awardType}}:</span>
                        </div>
                        <div class="search-component">
                          <el-input v-model="entityForm.awardBase" @input="handleBlurH(entityForm)" clearable
                                    oninput="value=value.replace(/[^0-9.]/g,'')" />
                        </div>
                      </div>
                      <div style="margin-bottom: 5px; border-radius: 3px;border: 1px solid #fff;width:54%;margin-left:20px">
                        <el-input v-model="entityForm.awardMoney" @input="sethandleBlur(entityForm)" clearable />
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="扣罚金" prop="punishMoney">
                    <div style="display: flex;flex-direction: row; align-items: center;">
                      <div class="smart-search">
                        <div class="prepend">
                          <el-dropdown placement="bottom-start" @command="changeKeyV" trigger="click">
                            <div class="el-dropdown-link">
                              <i class="el-icon-arrow-down el-icon--right"></i>
                            </div>
                            <el-dropdown-menu slot="dropdown" style="width: 100px; max-height:500px;overflow: auto;">
                              <el-dropdown-item v-for="item in smartSearchItems" :key="item.id" :command="item.id">
                                {{ item.label }}
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                          <span class="search-key">{{ punishType  }}:</span>
                        </div>
                        <div class="search-component">
                          <!-- <el-input v-model="entityForm.punishMoney" clearable oninput="value=value.replace(/[^0-9.]/g,'')" /> -->
                          <el-input v-model="entityForm.punishBase" @input="handleBlurcalculation(entityForm)" clearable
                                    oninput="value=value.replace(/[^0-9.]/g,'')" />
                        </div>
                      </div>
                      <div style="margin-bottom: 5px; border-radius: 3px;border: 1px solid #fff;width:54%;margin-left:20px">
                        <el-input v-model="entityForm.punishMoney" clearable @input="sethandleBlurf(entityForm)" />
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="结算周期" prop="remarks">
                    <el-date-picker v-model="Settlementcycle" type="daterange" range-separator="至" start-placeholder="开始日期"
                                    :picker-options="optionsDisable" value-format="yyyy-MM-dd" end-placeholder="结束日期"
                                    @change="change">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="结算额" prop="finalMoney">
                    <el-input v-model="entityForm.finalMoney" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="参考指标" prop="coalCategory">
                    <div class="tipbox">
                      <div class="tipline">水分 <span class="redtext">10</span></div>
                      <div class="tipline">硫分 <span class="redtext">10</span></div>
                      <div class="tipline">灰分 <span class="redtext">10</span></div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row> -->
              <!-- <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
                <el-col>
                  <el-form-item label="步骤" prop="reviewLogList">
                    <el-steps :active="reviewLogList.length">
                      <el-step v-for="(item,index) in reviewLogList" :key="index"
                               :title="item.reviewResult + ' ( 审批意见：'+item.reviewMessage+' )'"
                               :description="item.createDate+' '+item.reviewUserName">
                      </el-step>
                    </el-steps>
                  </el-form-item>
                </el-col>
              </el-row> -->
            </div>

          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer" v-if="dialogStatus != 'watch'">
        <!-- v-if="perms[`${curd}:review`]||false"-->
        <div v-if="dialogStatus != 'review'">
          <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="SubmitReview('entityForm')">
            提交审核
          </el-button>

          <el-button @click="SaveDraft('entityForm')" class="dialog-footer-btns">
            保存草稿
          </el-button>
        </div>

        <div v-else style="width:100%">
          <div v-if="perms[`${curd}:ismanagerreview`] || perms[`${curd}:isFinancereview`]"
               style="display: flex;flex-direction: row;">

            <div style="width:calc(100% - 250px)" class="shenpi">
              <el-input style="width:100%;" type="text" v-model="entityForm.reviewMessage" placeholder="请输入审批意见" />
            </div>
            <div style="width:250px">
              <el-button v-if="perms[`${curd}:update`]||false" type="primary" class="dialog-footer-btns"
                         :loading="entityFormLoading" @click="passfn(entityForm.id,entityForm.reviewMessage,entityForm.state)">
                审核通过
              </el-button>
              <el-button v-if="perms[`${curd}:update`]||false" @click="rejectfn(entityForm.id,entityForm.reviewMessage)"
                         class="dialog-footer-btns" style="width:100px">拒绝
              </el-button>
            </div>
          </div>
        </div>

      </div>
      <!-- 
      <div slot="footer" class="dialog-footer" v-else>
        <el-button v-if="perms[`${curd}:update`]||false" @click="rejectfn(entityForm.id,entityForm.reviewMessage)"
                   class="dialog-footer-btns" style="width:100px">拒绝
        </el-button>
        <el-button v-if="perms[`${curd}:update`]||false" type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                   @click="passfn(entityForm.id,entityForm.reviewMessage,entityForm.state)">
          审核通过
        </el-button>
      </div> -->

    </el-dialog>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/finance/settleRecv'
import { validatePhone } from '@/utils/validate'
import { settleRecvOption } from '@/const'
import productModel from '@/model/product/productList'
import { getCustomerContractNotSettle } from '@/api/quality'
import CustomerModel from '@/model/user/customer'
import { createMemoryField } from '@/utils/index'
import Cache from '@/utils/cache'
export default {
  name: 'settleRecv',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'settleRecv',
      model: Model,
      addressValue: '',
      entityForm: { ...Model.model },
      nameEntity: { options: [], active: '' },
      limitNum: 50,
      username: 'ss',
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      filterOption: { ...settleRecvOption },
      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        phone: [{ validator: validateIsPhone, trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },

      rules: {
        applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
        // contractId: { required: true, message: '请选择合同', trigger: 'blur' },
        customerName: { required: true, message: '请选择结算单位', trigger: 'blur' },
        productName: { required: true, message: '请选择品名', trigger: 'blur' },
        price: { required: true, message: '请选输入价格', trigger: 'blur' },
        // receiveWeight: { required: true, message: '请选输入实收数', trigger: 'blur' },
        settleMoney: { required: true, message: '请选输入结算金额', trigger: 'blur' },
        settleWeight: { required: true, message: '请选输入结算吨数', trigger: 'blur' },
        sendWeight: { required: true, message: '请选输入原发吨数', trigger: 'blur' },
        truckCount: { required: true, message: '请选输入原发车数', trigger: 'blur' }
      },

      // 合同
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      customerEntity: { options: [], active: [] },
      isshow: false,
      emptyImgPath: require(`@/assets/empty.jpg`),
      reviewLogList: [],
      awardType: '',
      punishType: '',
      smartSearchItems: [
        {
          label: '按吨',
          id: 1,
          width: 100
        },
        {
          label: '车数',
          id: 2
        }
      ],
      countList: [],
      Settlementcycle: '',
      isdisabled: false,

      optionsDisable: {}
    }
  },
  watch: {
    // 'entityForm.isPublicBoolean': {
    //   handler(v) {
    //     this.entityForm.isPublic = v ? 'Y' : 'N'
    //   },
    // },
    'entityForm.productName'(name) {
      if (name) {
        const item = this.nameEntity.options.find((item) => item.name === name)
        if (item) {
          this.nameEntity.active = item.name
          this.entityForm.productCode = item.code
          this.entityForm.productId = item.id
          this.entityForm.coalType = item.coalCategory || ''
          this.entityForm.coalCategory = item.type
        }
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
        this.entityForm.coalCategory = ''
      }
    },
    'entityForm.contractId'(id) {
      if (id == undefined) {
        this.entityForm = { ...this.entityForm, customerName: undefined, contractName: undefined }
      }
      if (!id) return
      this.updateAcc()
      if (this.entityForm.applyDate && id) {
        this.updateAcc(this.entityForm.applyDate, id)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch' || this.dialogStatus === 'review') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
        // this.entityForm.contractId = value
      }
    },

    'entityForm.customerId'(id) {
      if (!id) return
      const item = this.customerEntity.options.find((item) => item.value === id)
      if (item) {
        this.customerEntity.active = item
      } else {
        this.customerEntity.active = id
      }
    },
    // 在打开更新时watch监听然后通过过滤筛选触发contractEntityFormActive灌入显示数据
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      if (form) {
        this.entityForm.name = form.productName
        this.entityForm.contractCode = form.customerName
        this.entityForm.contractId = form.customerId
        this.entityForm.contractId = form.customerId

        // console.log(this.entityForm)
        // console.log(this.entityForm.customerName + '结算单位')
        // console.log(this.entityForm.contractName + '合同名称')

        // console.log(form.firstParty + '结算单位11')
        // this.entityForm = { ...this.entityForm, customerName: form.customerName }

        if (this.entityForm.contractName == undefined || this.entityForm.customerName == undefined) {
          // console.log('888')
          this.entityForm = { ...this.entityForm, customerName: form.firstParty, contractName: form.firstParty }
          // this.entityForm.customerName = form.firstParty
          // this.entityForm.contractName = form.firstParty
          // console.log(this.entityForm.customerName)
        } else {
          // console.log('9999')
          if (this.entityForm.customerName == this.entityForm.contractName) {
            this.customerEntity.active = form.firstParty //结算单位
            // this.entityForm.contractName = form.firstParty //合同名称
          } else {
            this.customerEntity.active = this.entityForm.customerName
          }
        }

        // console.log(this.entityForm)

        this.customerEntity.active = form.firstParty //结算单位

        // this.customerEntity.active = form.firstParty //结算单位

        this.nameEntity.active = form.productName
        this.entityForm.productName = form.productName
      }

      // const item = this.contractEntity.options.find((v) => v.customerId === value[0])
      // if (!item) return
      // let name = ''
      // item.contractSellList.forEach((element) => {
      //   name = element.productName
      // })
      // this.entityForm.productName = name
      // this.nameEntity.active = name

      // this.entityForm.customerName = item.customerName
      // this.entityForm.customerId = item.customerId
    },

    activetype: function (newV, oldV) {
      this.getlist(newV)
      this.getnumber()
    }
  },
  created() {
    this.permsActionbtn(this.curd)
    this.setbuttomlistV(this.curd)
    this.getName()
    this.getContract()
    this.getCustomer()
    this.activetype = ''
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'applyDate',
        // 'receiverName',
        'contractId',
        'contractCode',
        'customerId',
        'customerName',
        'productCode',
        'productId'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })

    this.awardType = this.smartSearchItems[0].label
    this.punishType = this.smartSearchItems[0].label
    this.entityForm.awardType = this.smartSearchItems[0].label
    this.entityForm.punishType = this.smartSearchItems[0].label

    this.getnumber()
    if (this.$route.params.type) {
      this.activetype = this.$route.params.type
      if (this.$route.params.id) {
        this.handleUpdate(this.$route.params.id, 'review')
      }
    } else {
      this.activetype = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
      this.actionList.forEach((val, index) => {
        if (val.type == this.activetype) {
          val.active = true
        } else {
          val.active = false
        }
      })
    }

    this.isdisabled = false

    this.optionsDisable = {
      disabledDate(time) {
        return time.getTime() > Date.now()
      }
    }
  },

  computed: {},
  methods: {
    // 财务管理   //财务报表付款申请
    async SubmitReview(entityForm) {
      if (this.entityForm.contractName == '') {
        this.entityForm = { ...this.entityForm, contractName: undefined }
      }
      if (this.entityForm.contractName != undefined) {
        //|| this.entityForm.contractName != ''
        if (this.entityForm.customerName !== this.entityForm.contractName) {
          this.$message({ message: '合同名称和结算单位名称不一致，请修改后再提交审核', type: 'warning' })
          return
        }
      } else {
      }

      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        const res = await this.model.getApproved(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '提交成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.getnumber()
          this.resetVariant()
          this.dialogFormVisible = false
        }
      }
    },
    //保存草稿
    async SaveDraft(entityForm) {
      if (this.entityForm.contractName != undefined) {
        if (this.entityForm.customerName !== this.entityForm.contractName) {
          this.$message({ message: '合同名称和结算单位名称不一致，请修改后再提交审核', type: 'warning' })
          return
        }
      }
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        const res = await this.model.SaveDraftfn(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '保存草稿成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.getnumber()
          this.dialogFormVisible = false
        }
      }
    },

    change(e) {
      // console.log(e)
      // console.log(e[0])
      this.Settlementcycle = e
      this.entityForm.beginDate = e[0]
      this.entityForm.endDate = e[1]
    },
    async getnumber() {
      const res = await this.model.getcountByState()
      this.countList = res.data
    },
    // 打印
    async handlePrint(row) {
      // this.isPrinting = true
      const { data } = await this.model.getprint(row.id)
      let objectUrl = window.URL.createObjectURL(new Blob([data], { type: 'application/pdf' }))

      window.open(objectUrl)
    },
    clearBtn() {
      // console.log('删除爽歪歪')
      // this.entityForm = { ...this.entityForm, settleMoney: '' }
    },
    handleBlur(entityForm) {
      let number = parseFloat(entityForm.settleWeight * entityForm.price).toFixed(2)
      if (entityForm.settleWeight != '' && entityForm.price != '') {
        // this.settleMoney = (entityForm.settleWeight * entityForm.price).toFixed(2)
        this.settleMoney = number
        // console.log(this.settleMoney)
        // console.log(99)
        if (isNaN(this.settleMoney) == false) {
          // this.entityForm.settleMoney = this.settleMoney
          this.entityForm = { ...this.entityForm, settleMoney: this.settleMoney }
        }
      }
      this.handleBlurH(entityForm)
      this.handleBlurcalculation(entityForm)
      this.calculationFinalMoney(entityForm)
    },
    // 计算结算金
    // 计算结算金
    calculationFinalMoney(entityForm) {
      //entityForm.punishMoney 扣罚金
      // entityForm.awardMoney 奖励金
      if (this.entityForm.settleMoney) {
        if (this.entityForm.awardMoney) {
          let num1 = parseFloat(Number(this.entityForm.settleMoney) + Number(this.entityForm.awardMoney)).toFixed(2)

          // console.log('加奖励金' + num1)
          this.entityForm = { ...this.entityForm, finalMoney: num1 }
        }
        if (this.entityForm.punishMoney) {
          // let num2 = parseInt(this.entityForm.settleMoney) - parseInt(this.entityForm.punishMoney)
          let num2 = parseFloat(Number(this.entityForm.settleMoney) - Number(this.entityForm.punishMoney)).toFixed(2)
          // console.log('减扣罚金' + num2)
          this.entityForm = { ...this.entityForm, finalMoney: num2 }
        }

        if (this.entityForm.awardMoney && this.entityForm.punishMoney) {
          // let tsnumber = Number(this.entityForm.punishMoney) + Number(this.entityForm.awardMoney)
          // let num3 = Number(this.entityForm.settleMoney) - Number(tsnumber)

          let num3 = parseFloat(
            Number(this.entityForm.settleMoney) - Number(this.entityForm.punishMoney) + Number(this.entityForm.awardMoney)
          ).toFixed(2)
          // console.log('减扣罚金加奖励金' + num3)
          this.entityForm = { ...this.entityForm, finalMoney: num3 }
        }
      }
    },
    handleinput1(entityForm) {
      this.entityForm = { ...this.entityForm, truckCount: entityForm.truckCount }
      this.handleBlurH(entityForm)
      this.handleBlurcalculation(entityForm)
    },
    handleBlurH(entityForm) {
      this.entityForm = { ...this.entityForm, settleWeight: entityForm.settleWeight, truckCount: entityForm.truckCount }
      let number = 0
      if (entityForm.awardType == '按吨') {
        number = this.entityForm.settleWeight
      } else if (entityForm.awardType == '车数') {
        number = this.entityForm.truckCount
      }
      if (entityForm.awardBase != undefined && number != undefined) {
        this.entityForm.awardMoney = (entityForm.awardBase * number).toFixed(2)
      }
      this.calculationFinalMoney(entityForm)
    },
    sethandleBlur(entityForm) {
      this.entityForm = { ...this.entityForm, awardMoney: entityForm.awardMoney }
      this.calculationFinalMoney(this.entityForm)
    },
    sethandleBlurf(entityForm) {
      this.entityForm = { ...this.entityForm, punishMoney: entityForm.punishMoney }
      this.calculationFinalMoney(this.entityForm)
    },
    handleBlurcalculation(entityForm) {
      this.entityForm = { ...this.entityForm, settleWeight: entityForm.settleWeight, truckCount: entityForm.truckCount }
      let number = 0
      if (entityForm.punishType == '按吨') {
        number = this.entityForm.settleWeight
      } else if (entityForm.punishType == '车数') {
        number = this.entityForm.truckCount
      }
      if (entityForm.punishBase != undefined && number != undefined) {
        this.entityForm.punishMoney = (entityForm.punishBase * number).toFixed(2)
      }
      this.calculationFinalMoney(entityForm)
    },
    handleinput(entityForm) {
      this.entityForm = { ...this.entityForm, truckCount: entityForm.truckCount }
      this.handleBlurH(entityForm)
      this.handleBlurcalculation(entityForm)
    },

    changeKey(key) {
      this.entityForm.awardBase = ''
      this.entityForm.awardMoney = ''
      const index = this.smartSearchItems.findIndex((val) => val.id === key)
      if (~index) {
        this.entityForm.awardType = this.smartSearchItems[index].label
        this.awardType = this.smartSearchItems[index].label
      }
    },
    changeKeyV(key) {
      this.entityForm.punishBase = ''
      this.entityForm.punishMoney = ''
      const index = this.smartSearchItems.findIndex((val) => val.id === key)
      if (~index) {
        this.punishType = this.smartSearchItems[index].label
        this.entityForm.punishType = this.smartSearchItems[index].label
      }
    },

    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      this.isdisabled = false
      this.reviewLogList = []
      this.contractEntity.active = []
      this.customerEntity.active = []
      this.Settlementcycle = ''
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.awardType = this.smartSearchItems[0].label
      this.punishType = this.smartSearchItems[0].label
      this.entityForm.awardType = this.smartSearchItems[0].label
      this.entityForm.punishType = this.smartSearchItems[0].label
      this.getnumber()
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) {}
    },
    // 获取合同选择接口
    async getContract() {
      const { data } = await getCustomerContractNotSettle()
      // 供应商名称返回id用来查询
      this.contractEntity.options = this.formatContract({ data, key: { customerName: 'code', customerId: 'id' } })
    },

    async getCustomer() {
      try {
        let options = []
        const { data } = await CustomerModel.page({ size: 1000 })
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        this.customerEntity.options = options
      } catch (e) {}
    },
    // 合同改变同步entityForm
    handleCustomer({ value, label }) {
      // this.entityForm.customerId = value
      // this.entityForm.customerName = label
      this.entityForm = { ...this.entityForm, customerId: value, customerName: label }
    },
    handleNameEntityV() {
      this.isdisabled = true
      this.entityForm = { ...this.entityForm, customerName: undefined }
    },
    selectBlur(value) {
      this.customerEntity.active = value.target.value
      this.entityForm.customerId = ''
      this.entityForm = { ...this.entityForm, customerName: value.target.value }
      // this.entityForm=    e.target.value
    },
    // 累计接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      const paramsAcc = await this.getAccValues({ date, contractId })
      Object.keys(paramsAcc).forEach((key) => {
        this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
        this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      })
      this.computeWayCostAcc()
    },
    computeWayCostAcc() {
      let { receiveWeightAcc, sendWeightAcc } = this.entityForm
      receiveWeightAcc = receiveWeightAcc * 1
      sendWeightAcc = sendWeightAcc * 1
      let wayCostAcc = (((sendWeightAcc - receiveWeightAcc) / sendWeightAcc) * 100).toFixed(2) * 1
      this.entityForm.wayCostAcc = !Number.isNaN(wayCostAcc) ? wayCostAcc : 0
    },
    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     * @target {contract|contractEntityForm}<String>
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    async handleUpdate(item, type) {
      this.Settlementcycle = []
      if (typeof item == 'object') {
        this.entityForm = { ...item }
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        } else if (type == 'update') {
          this.isshow = true
          this.dialogStatus = 'update'
        } else if (type == 'watch') {
          this.isshow = false
          this.dialogStatus = 'watch'
        }
        this.dialogFormVisible = true
        const { data } = await this.model.getUploadList(item.id)

        this.entityForm = { ...data }
        this.Settlementcycle.push(this.entityForm.beginDate)
        this.Settlementcycle.push(this.entityForm.endDate)
        // this.customerEntity.active
        if (this.entityForm.customerId) {
          this.entityForm.customerId = this.entityForm.customerId
        } else {
          // this.entityForm.customerId = this.entityForm.customerName
        }

        this.awardType = this.entityForm.awardType
        this.punishType = this.entityForm.punishType
        this.reviewLogList = data.reviewLogList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((item, index) => {
            newarry.push(item.reviewMessage)
          })
          // this.entityForm.reviewMessage = newarry.toString()
          this.entityForm.reviewMessage = ''
        }
      } else if (typeof item == 'string') {
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        }
        this.dialogFormVisible = true
        const { data } = await this.model.getUploadList(item)
        this.entityForm = { ...data }

        this.Settlementcycle.push(this.entityForm.beginDate)
        this.Settlementcycle.push(this.entityForm.endDate)

        this.awardType = this.entityForm.awardType
        this.punishType = this.entityForm.punishType
        this.reviewLogList = data.reviewLogList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((itemv, index) => {
            newarry.push(itemv.reviewMessage)
          })
          // this.entityForm.reviewMessage = newarry.toString()
          this.entityForm.reviewMessage = ''
        }
      }
    },

    //点击状态获取数据
    async getlist(newV) {
      this.activetype = newV
      this.actionList.forEach((val, index) => {
        if (val.type == newV) {
          val.active = true
        } else {
          val.active = false
        }
      })
      this.filtersSeach.filter_INS_state = newV
      this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    },
    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    // 更新累计Acc
    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      const paramsAcc = await this.getAccValues({ date, contractId })
      Object.keys(paramsAcc).forEach((key) => {
        this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
        this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      })
      this.computeWayCostAcc()
    },
    //去审核
    async passfn(id, reviewMessage, state) {
      if (state == 'NEW') {
        const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage })
      } else if (state == 'PASS') {
        const res = await this.model.getfinancePass({ id: id, reviewMessage: reviewMessage })
      }
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
      this.resetVariant()
      this.getlist(this.activetype)
      this.getnumber()
    },

    async rejectfn(id, reviewMessage) {
      const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage })
      this.getnumber()
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>

<style scoped>
.search-component >>> .el-input__inner {
  border: none;
}
</style>


<style lang="scss" scoped>
@import '@/styles/router-page.scss';
::v-deep .el-date-editor .el-range-separator {
  padding: 0 0;
}
::v-deep .el-form-item__label {
  font-weight: 400;
}

.smart-search {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  width: 47%;
  margin-bottom: 5px;
  border-radius: 3px;

  .prepend {
    background-color: #f5f7fa;
    padding-right: 10px;
    border-right: solid 1px #dcdfe6;
    .el-dropdown {
      cursor: pointer;
    }

    .el-icon-arrow-down {
      width: 26px;
      text-align: center;
    }

    .search-key {
      display: inline-block;
      height: 30px;
      line-height: 30px;
    }
  }

  .search-component {
    flex: 1;
    .el-input {
      width: 100%;
      height: 30px;
      line-height: 30px;
      .el-input__inner {
        border: none;
      }
      & .is-focus {
        .el-input__inner {
          border-color: #dcdfe6 !important;
        }
      }
    }
  }
}
.form-layoutV {
  .el-row {
    margin-bottom: 10px;
  }
}
.tipbox {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  .tipline {
    margin-left: 20px;
    .redtext {
      font-size: 1.5rem;
      color: red;
    }
  }
}
// .bigbox {
//   width: 100%;
//   padding: 15px 15px 0 15px;
//   background-color: #fff;
//   .btnbox {
//     width: 100%;
//     display: flex;
//     flex-direction: row;
//     align-items: center;
//   }
// }
.contentbox {
  width: 500px;
  margin: 100px auto 0 auto;
  font-size: 12px;
  .line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tsline {
    float: right;
    margin-top: 40px;
  }
  .inputbox {
    width: 100px;
    margin-bottom: 10px;
    border-bottom: solid 1px #ccc;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
<style scoped>
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
/* .dialog-footer >>> .dialog-footer-btns {
  border: 1px solid #2f79e8;
  color: #2f79e8;
} */
/* .opt-btn {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
}
.opt-btnV {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
} */
</style>