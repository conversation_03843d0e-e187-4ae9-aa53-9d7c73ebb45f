<template>
  <div class="app-container">
    <!-- <el-date-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate" placeholder="选择日期"
                    :clearable="false" /> -->
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" :actionList="actionList" :buttomlist="buttomlist" />

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :statetype="activetype" :buttomlist="buttomlist"
            :showSummary='true' :countList="countList" :actionList="actionList" title="供应商" otherHeight="170">

      <el-table-column label="状态" slot="state" align="center" width="120">
        <template slot-scope="scope">
          <!-- 通用审核状态 NEW-待审核、PASS-通过、REJECT-拒绝 -->
          <!-- <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :(scope.row.state == 'PAYED' ? 'success' : (scope.row.state == 'PASS_FINANCE' ? 'success' : 'info'))))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '总经理待审核' : (scope.row.state == 'PASS' ? '财务待审核' : (scope.row.state == 'REJECT' ? '拒绝':(scope.row.state == 'PAYED' ? '已付款' : (scope.row.state == 'PASS_FINANCE' ? '已审核' : ''))))) }}
          </el-tag> -->

          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :(scope.row.state == 'PAYED' ? 'success' : 'info')))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '待审核' : (scope.row.state == 'PASS' ? '已审核' : (scope.row.state == 'REJECT' ? '拒绝':(scope.row.state == 'PAYED' ? '已付款' : '')))) }}
          </el-tag>

        </template>
      </el-table-column>

      <el-table-column label="结算周期" slot="beginDate" align="center" width="180">
        <template slot-scope="scope">
          <span v-if="scope.row.beginDate">
            {{scope.row.beginDate}}至{{scope.row.endDate}}
          </span>
        </template>
      </el-table-column>

      <el-table-column slot="isSettle" label="结算状态" align="center">
        <template slot-scope="scope" v-if="scope.row.isSettle">
          <el-tag :type="(scope.row.isSettle=='Y' ? 'success':(scope.row.isSettle == 'N' ? 'danger' : ''))" size="mini">
            {{scope.row.isSettle == 'Y' ? '已结清' : (scope.row.isSettle == 'N' ? '未结清' :  '') }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="当前流程" slot="curFlowName" align="center" fixed="right">
        <template slot-scope="scope">
          <el-popover placement="right" width="700" trigger="click">
            <div v-for="(item,index) in steplistNEW" :key="index">
              <div>
                <div>{{item.label}}: <span style="color:red">{{item.curFlowName}}</span></div>
              </div>
              <el-row type="flex" :gutter="50" style="margin:20px 0">
                <el-col style="margin-bottom: -13px">
                  <div class="bzboxV">
                    <div class="bzminboxV" v-for="(value,indexc) in item.flowDesignList" :key="indexc">
                      <div class="lin" :class="value.active==true?'bordercolorBLUE':''"></div>
                      <div class="context" :class="value.active==true?'colorBLUE':''">
                        <i class="el-icon-edit icon" v-if="value.code=='SETTLE_RECV_DRAFT'"></i>
                        <i class="el-icon-coordinate icon"
                           v-if="value.code=='SETTLE_RECV_FINANCE_LEADER' ||  value.code=='SETTLE_RECV_MANAGER' ||  value.code=='SETTLE_RECV_BOSS' ||  value.code=='SETTLE_RECV_PASS'"></i>

                        <i class="el-icon-link icon" v-if="value.code=='PASS'"></i>
                        <i class="el-icon-help icon" v-if="value.code=='SETTLE_RECV_REJECT'"></i>
                        <i class="el-icon-document-checked" v-if="value.code=='SETTLE_RECV_PAYED'" style="font-size: 30px;"></i>

                        <div>{{value.aliasName}}</div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <el-button slot="reference" style="padding: 3px 6px;" @click="lookbtn(scope.row)">流程</el-button>
          </el-popover>
        </template>
      </el-table-column>

      <!-- <el-table-column label="操作" slot="opt" max-width="150" width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <div v-if="perms[`${curd}:ismanagerreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state =='NEW'" color="#FF726B" @click="handleUpdate(scope.row,'review')">
                去审核(总经理)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:isFinancereview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state=='PASS'" color="#33CAD9" @click="handleUpdate(scope.row,'review')">
                去审核(财务)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" color="#FF726B"
                      @click="handleUpdate(scope.row,'update')">
                编辑
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'PASS_FINANCE'" color="#FF726B"
                      @click="handlePrint(scope.row,'update')">
                <span v-if="scope.row.isPrint=='Y'">补打</span>
                <span v-else>打印</span>
              </el-tag>
            </div>
            <div v-if="scope.row.state=='DRAFT,REJECT' || scope.row.state=='PASS' 
                 || scope.row.state == 'PASS_FINANCE' || scope.row.state=='NEW' ">
              <el-tag class="opt-btn" color="#FF726B" @click="handleUpdate(scope.row,'watch')">
                查看
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false" style="margin-left:5px">
              <el-tag class="opt-btn" style="cursor: pointer;" color="#2f79e8"
                      v-if="scope.row.state == 'PASS_FINANCE' || scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'"
                      @click="handleDel(scope.row)">删除
              </el-tag>
            </div>

          </div>
        </template>
      </el-table-column> -->

      <el-table-column label="操作" slot="opt" max-width="100" width="170" fixed="right" align="center">
        <template slot-scope="scope">
          <div style="display: flex; flex-direction:row;justify-content: center;flex-wrap: wrap;">
            <div v-if="perms[`${curd}:ismanagerreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode =='SETTLE_RECV_MANAGER'" color="#FF726B"
                      @click="handleUpdate(scope.row,'review','SETTLE_RECV')">
                去审核(总经理)
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:isFinancereview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode=='SETTLE_RECV_FINANCE_LEADER'" color="#33CAD9"
                      @click="handleUpdate(scope.row,'review','SETTLE_RECV')">
                去审核(财务负责人)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:isBossreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode=='SETTLE_RECV_BOSS'" color="#33CAD9"
                      @click="handleUpdate(scope.row,'review','SETTLE_RECV')">
                去审核(董事长)
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT' || scope.row.state == 'REJECT'" color="#FF726B"
                      @click="handleUpdate(scope.row,'update')">
                编辑
              </el-tag>
            </div>

            <!-- <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'PASS'" color="#FF726B" @click="handlePrint(scope.row,'update')">
                <span v-if="scope.row.isPrint=='Y'">补打</span>
                <span v-else>打印</span>
              </el-tag>
            </div>
            <div>
              <el-tag class="opt-btn" color="#FF726B">
                <span>下载</span>
              </el-tag>
            </div> -->
            <div v-if="perms[`${curd}:istovoid`] || false">
              <el-tag class="opt-btn" color="#ff726b" v-if="scope.row.state == 'PASS'" @click="handleCancel(scope.row)">作废
              </el-tag>
            </div>
            <div v-if="scope.row.state=='REJECT'  ||  scope.row.state=='PASS' || 
                 scope.row.state=='PASS_FINANCE' || scope.row.state=='NEW'  ">
              <el-tag class="opt-btn" color="#FF726B" @click="handleUpdate(scope.row,'watch','SETTLE_RECV')">
                查看
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false" style="margin-left:5px">
              <el-tag class="opt-btn" style="cursor: pointer;" color="#2f79e8"
                      v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>

    </s-curd>

    <!-- <el-dialog width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="right" v-if="dialogFormVisible">

        <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="reviewLogList">
              <el-steps :active="reviewLogList.length">
                <el-step v-for="(item,index) in reviewLogList" :key="index"
                         :title="item.reviewResult + ' ( 审批意见：'+item.reviewMessage+' )'"
                         :description="item.createDate+' '+item.reviewUserName">
                </el-step>
              </el-steps>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div class="form-title">基础信息</div>
            <div class="form-layout form-layoutV">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="applyDate">
                    <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同" prop="contractId">
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 @change="handleNameEntityV" filterable clearable placeholder="请选择合同" style="width:100%" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="结算单位" prop="customerName">
                    <el-select v-model="customerEntity.active" placeholder="请选择收货单位" filterable clearable @change="handleCustomer"
                               @blur="selectBlur" style="width:100%">
                      <el-option v-for="item in customerEntity.options" :key="item.value" :label="item.label" :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <div class="form-title">结算信息</div>
            <div class="form-layout form-layoutV">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="原发车数" prop="truckCount">
                    <el-input v-model="entityForm.truckCount" @input="handleinput1(entityForm)" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="原发吨数" prop="sendWeight">
                    <el-input v-model="entityForm.sendWeight" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="实收吨数" prop="receiveWeight">
                    <el-input v-model="entityForm.receiveWeight" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="结算吨数" prop="settleWeight">
                    <el-input v-model="entityForm.settleWeight" autocomplete="off" clearable @input="handleBlur(entityForm)" />
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="单价" prop="price">
                    <el-input v-model="entityForm.price" clearable oninput="value=value.replace(/[^0-9.]/g,'')" @clear="clearBtn"
                              @input="handleBlur(entityForm)" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="结算金额" prop="settleMoney">
                    <el-input v-model="entityForm.settleMoney" autocomplete="off" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="奖励金" prop="awardType">
                    <div style="display: flex;flex-direction: row; align-items: center;">
                      <div class="smart-search">
                        <div class="prepend">
                          <el-dropdown placement="bottom-start" @command="changeKey" trigger="click">
                            <div class="el-dropdown-link">
                              <i class="el-icon-arrow-down el-icon--right"></i>
                            </div>
                            <el-dropdown-menu slot="dropdown" style="width: 100px; max-height:500px;overflow: auto;">
                              <el-dropdown-item v-for="item in smartSearchItems" :key="item.id" :command="item.id">
                                {{ item.label }}
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                          <span class="search-key">{{awardType}}:</span>
                        </div>
                        <div class="search-component">
                          <el-input v-model="entityForm.awardBase" @input="handleBlurH(entityForm)" clearable
                                    oninput="value=value.replace(/[^0-9.]/g,'')" />
                        </div>
                      </div>
                      <div style="margin-bottom: 5px; border-radius: 3px;border: 1px solid #fff;width:54%;margin-left:20px">
                        <el-input v-model="entityForm.awardMoney" @input="sethandleBlur(entityForm)" clearable />
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="扣罚金" prop="punishMoney">
                    <div style="display: flex;flex-direction: row; align-items: center;">
                      <div class="smart-search">
                        <div class="prepend">
                          <el-dropdown placement="bottom-start" @command="changeKeyV" trigger="click">
                            <div class="el-dropdown-link">
                              <i class="el-icon-arrow-down el-icon--right"></i>
                            </div>
                            <el-dropdown-menu slot="dropdown" style="width: 100px; max-height:500px;overflow: auto;">
                              <el-dropdown-item v-for="item in smartSearchItems" :key="item.id" :command="item.id">
                                {{ item.label }}
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                          <span class="search-key">{{ punishType  }}:</span>
                        </div>
                        <div class="search-component">
                          <el-input v-model="entityForm.punishBase" @input="handleBlurcalculation(entityForm)" clearable
                                    oninput="value=value.replace(/[^0-9.]/g,'')" />
                        </div>
                      </div>
                      <div style="margin-bottom: 5px; border-radius: 3px;border: 1px solid #fff;width:54%;margin-left:20px">
                        <el-input v-model="entityForm.punishMoney" clearable @input="sethandleBlurf(entityForm)" />
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="结算周期" prop="remarks">
                    <el-date-picker v-model="Settlementcycle" type="daterange" range-separator="至" start-placeholder="开始日期"
                                    :picker-options="optionsDisable" value-format="yyyy-MM-dd" end-placeholder="结束日期"
                                    @change="change">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="结算额" prop="finalMoney">
                    <el-input v-model="entityForm.finalMoney" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer" v-if="dialogStatus != 'watch'">
        <div v-if="dialogStatus != 'review'">
          <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="SubmitReview('entityForm')">
            提交审核
          </el-button>
          <el-button @click="SaveDraft('entityForm')" class="dialog-footer-btns">
            保存草稿
          </el-button>
        </div>
        <div v-else style="width:100%">
          <div v-if="perms[`${curd}:ismanagerreview`] || perms[`${curd}:isFinancereview`]"
               style="display: flex;flex-direction: row;">
            <div style="width:calc(100% - 250px)" class="shenpi">
              <el-input style="width:100%;" type="text" v-model="entityForm.reviewMessage" placeholder="请输入审批意见" />
            </div>
            <div style="width:250px">
              <el-button v-if="perms[`${curd}:update`]||false" type="primary" class="dialog-footer-btns"
                         :loading="entityFormLoading" @click="passfn(entityForm.id,entityForm.reviewMessage,entityForm.state)">
                审核通过
              </el-button>
              <el-button v-if="perms[`${curd}:update`]||false" @click="rejectfn(entityForm.id,entityForm.reviewMessage)"
                         class="dialog-footer-btns" style="width:100px">拒绝
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog> -->

    <el-dialog top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">

        <el-row type="flex" :gutter="50" v-if="steplist.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="steplist">
              <div class="bzbox">
                <div v-for="(item,index) in steplist" :key="index" class="bzminbox">
                  <div class="lin" :class="item.active==true?'bordercolorBLUE':''"></div>
                  <div class="context" :class="item.active==true?'colorBLUE':''">
                    <i class="el-icon-edit icon" v-if="item.code=='SETTLE_RECV_DRAFT'"></i>
                    <i class="el-icon-coordinate icon"
                       v-if="item.code=='SETTLE_RECV_FINANCE_LEADER' ||  item.code=='SETTLE_RECV_MANAGER' ||  item.code=='SETTLE_RECV_BOSS'"></i>
                    <i class="el-icon-link icon" v-if="item.code=='SETTLE_RECV_PASS'"></i>
                    <i class="el-icon-help icon" v-if="item.code=='SETTLE_RECV_REJECT'"></i>
                    <i class="el-icon-document-checked" v-if="item.code=='SETTLE_RECV_PAYED'" style="font-size: 30px;"></i>
                    <div>{{item.aliasName}}</div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col>
            <template>
              <div class="form-title">基础信息</div>
              <div class="form-layout form-layoutV">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="日期" prop="applyDate">
                      <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="合同" prop="contractId">
                      <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                   @change="handleNameEntityV" filterable clearable placeholder="请选择合同" style="width:100%" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="品名" prop="productName">
                      <!-- :disabled="dialogStatus!='update'?true:isdisabled" -->
                      <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算单位" prop="customerName">
                      <el-select v-model="customerEntity.active" placeholder="请选择" filterable clearable @change="handleCustomer"
                                 @blur="selectBlur" style="width:100%">
                        <el-option v-for="item in customerEntity.options" :key="item.value" :label="item.label" :value="item">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>

                </el-row>

                <el-row type="flex" :gutter="50">
                  <!-- 销售合同的卖方 -->
                  <el-col>
                    <el-form-item label="卖方" prop="secondParty">
                      <el-select v-model="secondPartyEntity.value" placeholder="请选择卖方" clearable filterable style="width:100%"
                                 :disabled="dialogStatus==='review'|| dialogStatus === 'watch' || dialogStatus =='change' ?true:false">
                        <el-option v-for="item in secondPartyEntity.options" :key="item.id" :label="item.name"
                                   :value="item.name" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col> </el-col>
                  <el-col> </el-col>
                  <el-col> </el-col> -->

                  <el-col>
                    <el-form-item label="实收日期" :rules="[{ required: true, message: '请选择实收日期' }]">
                      <el-date-picker v-model="Settlementcycle" type="daterange" range-separator="至" start-placeholder="开始日期"
                                      :picker-options="optionsDisable" value-format="yyyy-MM-dd" end-placeholder="结束日期"
                                      @change="change">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <div style="margin-top:28px">
                      <el-button type="primary" style="float: right;" @click="seachfn"
                                 v-if="dialogStatus=='create' || dialogStatus=='update'">保存</el-button>
                    </div>
                  </el-col>
                </el-row>

                <!-- <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="实收日期" :rules="[{ required: true, message: '请选择实收日期' }]">
                      <el-date-picker v-model="Settlementcycle" type="daterange" range-separator="至" start-placeholder="开始日期"
                                      :picker-options="optionsDisable" value-format="yyyy-MM-dd" end-placeholder="结束日期"
                                      @change="change">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-button type="primary" style="float: right;" @click="seachfn"
                               v-if="dialogStatus=='create' || dialogStatus=='update'">保存</el-button>
                  </el-col>
                </el-row> -->
              </div>

              <div class="form-title" v-show="isallShow">结算明细</div>
              <div class="jsmx" v-if="(dialogStatus=='watch' || dialogStatus=='review') && isallShow">
                <el-table :data="entityForm.weightHouseOutList" stripe border highlight-current-row :header-cell-style="headClass"
                          height="300px">
                  <el-table-column label="实收日期" prop="actualReceiveDate" align="center"></el-table-column>
                  <el-table-column label="扣水" prop="deductWater" align="center"></el-table-column>
                  <el-table-column label="品名" prop="name" align="center"></el-table-column>
                  <el-table-column label="原发数" prop="sendWeight" align="center"></el-table-column>
                  <el-table-column label="实收数" prop="receiveWeight" align="center"></el-table-column>
                  <el-table-column label="运费" prop="carriage" align="center"></el-table-column>
                  <el-table-column label="煤价" prop="price" align="center"></el-table-column>
                  <el-table-column label="是否含税" prop="hasFax" align="center">
                    <template slot-scope="scope">
                      <span v-if="scope.row.hasFax=='Y'">是</span>
                      <span v-if="scope.row.hasFax=='N'">否</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="车牌" prop="plateNumber" align="center"></el-table-column>
                  <el-table-column label="发货单位" prop="senderName" align="center" width="300"></el-table-column>
                  <el-table-column label="收货单位" prop="receiverName" align="center" width="300"></el-table-column>
                  <el-table-column label="途耗" prop="wayCost" align="center"></el-table-column>
                  <el-table-column label="过磅日期" prop="date" align="center"></el-table-column>
                  <el-table-column label="备注" prop="remarks" align="center"></el-table-column>
                </el-table>
              </div>
              <div v-else>
                <div class="form-layout form-layoutV" v-show="isallShow">
                  <el-row type="flex" :gutter="50">
                    <el-col>
                      <div class="form-edit-content">
                        <div class="left-flex">
                          <h3 class="content-title">全部列表</h3>
                          <div class="content">
                            <div class="filter-container">
                              <el-date-picker v-model="Settlementdate" type="daterange" range-separator="至" :clearable="true"
                                              start-placeholder="开始日期" :picker-options="optionsDisable" value-format="yyyy-MM-dd"
                                              end-placeholder="结束日期" @change="changeVV">
                              </el-date-picker>
                              <el-button type="primary" icon="el-icon-search" @click="generateData">查询
                              </el-button>
                            </div>
                            <el-table :data="dataleftlist" height="410px" v-loading="leftListLoading"
                                      element-loading-text="加载中..." stripe border highlight-current-row
                                      @selection-change="handleAddSelectionChange">
                              <el-table-column type="selection" width="40">
                              </el-table-column>
                              <el-table-column label="实收日期" prop="actualReceiveDate" align="center"></el-table-column>
                              <el-table-column label="扣水" prop="deductWater" align="center"></el-table-column>
                              <el-table-column label="品名" prop="name" align="center"></el-table-column>
                              <el-table-column label="原发数" prop="sendWeight" align="center"></el-table-column>
                              <el-table-column label="实收数" prop="receiveWeight" align="center"></el-table-column>
                              <el-table-column label="运费" prop="carriage" align="center"></el-table-column>
                              <el-table-column label="煤价" prop="price" align="center"></el-table-column>
                              <el-table-column label="是否含税" prop="hasFax" align="center">
                                <template slot-scope="scope">
                                  <span v-if="scope.row.hasFax=='Y'">是</span>
                                  <span v-if="scope.row.hasFax=='N'">否</span>
                                </template>
                              </el-table-column>
                              <el-table-column label="车牌" prop="plateNumber" align="center"></el-table-column>
                              <el-table-column label="发货单位" prop="senderName" width="300" align="center"></el-table-column>
                              <el-table-column label="收货单位" prop="receiverName" width="300" align="center"></el-table-column>
                              <el-table-column label="途耗" prop="wayCost" align="center"></el-table-column>
                              <el-table-column label="过磅日期" prop="date" align="center"></el-table-column>
                              <el-table-column label="备注" prop="remarks" align="center"></el-table-column>
                            </el-table>
                            <!-- <div v-show="!leftListLoading" class="pagination-container">
                              <el-pagination @size-change="handleLeftListSizeChange" @current-change="handleLeftListCurrentChange"
                                             :current-page.sync="leftListQuery.page" :layout="pagination.layout"
                                             :page-sizes="pagination.pageSizes" :page-size="leftListQuery.size"
                                             :total="totalleft">
                              </el-pagination>
                            </div> -->
                          </div>
                        </div>
                        <div class="mid-tools">
                          <el-button type="primary" icon="el-icon-arrow-right" @click="saveUserList('add')"
                                     :disabled="rightListLoading || addUserList.length === 0 "></el-button>
                          <br>
                          <el-button type="danger" icon="el-icon-arrow-left" @click="saveUserList('del')"
                                     :disabled="rightListLoading || delUserList.length === 0 "></el-button>
                        </div>
                        <div class="right-form">
                          <h3 class="content-title">选中列表</h3>
                          <div class="content">
                            <!-- <div class="filter-container" style="height:40px"></div> -->
                            <el-table :data="datarighttlist" height="450px" v-loading="rightListLoading"
                                      element-loading-text="加载中..." stripe border highlight-current-row
                                      @selection-change="handleDelSelectionChange">
                              <el-table-column type="selection" width="40">
                              </el-table-column>
                              <el-table-column label="实收日期" prop="actualReceiveDate" align="center"></el-table-column>
                              <el-table-column label="扣水" prop="deductWater" align="center"></el-table-column>

                              <el-table-column label="品名" prop="name" align="center"></el-table-column>
                              <el-table-column label="原发数" prop="sendWeight" align="center"></el-table-column>
                              <el-table-column label="实收数" prop="receiveWeight" align="center"></el-table-column>
                              <!-- <el-table-column label="合同编号" prop="contractId"></el-table-column> -->

                              <el-table-column label="运费" prop="carriage" align="center"></el-table-column>
                              <el-table-column label="煤价" prop="price" align="center"></el-table-column>
                              <!-- <el-table-column label="人工费" prop="manCost"></el-table-column> -->
                              <el-table-column label="是否含税" prop="hasFax" align="center">
                                <template slot-scope="scope">
                                  <span v-if="scope.row.hasFax=='Y'">是</span>
                                  <span v-if="scope.row.hasFax=='N'">否</span>
                                </template>
                              </el-table-column>
                              <el-table-column label="车牌" prop="plateNumber" align="center"></el-table-column>
                              <el-table-column label="发货单位" prop="senderName" width="300" align="center"></el-table-column>
                              <el-table-column label="收货单位" prop="receiverName" width="300" align="center"></el-table-column>
                              <!-- <el-table-column label="机打实收" prop="receiveWeight"></el-table-column> -->
                              <el-table-column label="途耗" prop="wayCost" align="center"></el-table-column>
                              <el-table-column label="过磅日期" prop="date" align="center"></el-table-column>
                              <el-table-column label="备注" prop="remarks" align="center"></el-table-column>
                            </el-table>
                            <!-- <div v-show="!rightListLoading" class="pagination-container">
                              <el-pagination @size-change="handleRightListSizeChange"
                                             @current-change="handleRightListCurrentChange"
                                             :current-page.sync="rightListQuery.page" :layout="pagination.layout"
                                             :page-sizes="pagination.pageSizes" :page-size="rightListQuery.size"
                                             :total="totalright">
                              </el-pagination>
                            </div> -->
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>

              <div class="form-title" v-show="isallShow">结算信息</div>
              <div class="form-layout form-layoutV" v-show="isallShow">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="拉运车数" prop="truckCount">
                      <el-input v-model="entityForm.truckCount" @input="handleinput1(entityForm)" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <!-- 原发吨数 -->
                    <el-form-item label="原发吨数" prop="sendWeight">
                      <el-input v-model="entityForm.sendWeight" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="实收吨数" prop="receiveWeight">
                      <el-input v-model="entityForm.receiveWeight" @input="CalculateSettlementTonnage(entityForm)"
                                autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>

                  <el-col>
                    <el-form-item label="合同单价" prop="contractPrice">
                      <el-input v-model="entityForm.contractPrice" autocomplete="off" clearable
                                @input="handleBlurcount(entityForm)" />
                    </el-form-item>
                  </el-col>

                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="结算单价" prop="price">
                      <el-input v-model="entityForm.price" @input="handleBlurcount(entityForm)" autocomplete="off"
                                oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <!-- <el-form-item label="补水吨数" prop="moisturizingWeight"> -->
                    <el-form-item label="明水" prop="moisturizingWeight">
                      <el-input v-model="entityForm.moisturizingWeight" @input="CalculateSettlementTonnage(entityForm)"
                                autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>

                  <el-col>
                    <el-form-item label="明粉" prop="clearPowder">
                      <el-input v-model="entityForm.clearPowder" @input="CalculateSettlementTonnage(entityForm)"
                                autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>

                  <el-col>
                    <el-form-item label="杂质" prop="foreignSubstance">
                      <el-input v-model="entityForm.foreignSubstance" @input="CalculateSettlementTonnage(entityForm)"
                                autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                  <!-- <el-col>
                    <el-form-item label="路耗" prop="roadCost">
                      <el-input v-model="entityForm.roadCost" @input="CalculateSettlementTonnage(entityForm)" autocomplete="off"
                                clearable />
                    </el-form-item>
                  </el-col> -->

                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="扣重" prop="deWeight">
                      <el-input v-model="entityForm.deWeight" @input="CalculateSettlementTonnage(entityForm)" autocomplete="off"
                                clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算吨数" prop="settleWeight">
                      <el-input v-model="entityForm.settleWeight" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算金额" prop="settleMoney">
                      <el-input v-model="entityForm.settleMoney" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                clearable />
                    </el-form-item>
                  </el-col>
                  <el-col></el-col>
                </el-row>
              </div>

              <div class="form-title" v-show="isallShow">指标信息</div>
              <div class="form-layout form-layoutV" v-show="isallShow">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <div class="tablebox">
                      <div class="tablehead">
                        <div v-for="(item,index) in tableheadlist" :key="index">
                          {{item.name}}
                        </div>
                      </div>
                      <div class="tablecon">
                        <div>
                          合同指标
                        </div>
                        <div> <el-input v-model="entityForm.cleanStd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.cleanAd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.cleanVdaf" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.procG" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                    clearable /></div>
                        <div> <el-input v-model="entityForm.cleanMt" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.procY" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                    clearable /></div>
                        <div> <el-input v-model="entityForm.qualCsr" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.macR0" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                                    clearable /></div>
                        <div> <el-input v-model="entityForm.recovery" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                      </div>
                      <div class="tablecon">
                        <div>
                          化验指标
                        </div>
                        <div> <el-input v-model="entityForm.assayCleanStd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayCleanAd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayCleanVdaf" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayProcG" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayCleanMt" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayProcY" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayQualCsr" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayMacR0" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                        <div> <el-input v-model="entityForm.assayRecovery" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable /></div>
                      </div>

                      <div class="tablecon">
                        <div>
                          三方化验
                        </div>
                        <div> <el-input v-model="entityForm.thirdCleanStd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdCleanAd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdCleanVdaf" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdProcG" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdCleanMt" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdProcY" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdQualCsr" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdMacR0" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.thirdRecovery" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                      </div>

                      <div class="tablecon">
                        <div>
                          其它化验
                        </div>
                        <div> <el-input v-model="entityForm.otherCleanStd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherCleanAd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherCleanVdaf" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherProcG" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherCleanMt" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherProcY" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherQualCsr" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherMacR0" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.otherRecovery" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" clearable />
                        </div>
                      </div>

                      <div class="tablecon">
                        <div>
                          质量扣罚
                        </div>
                        <div> <el-input v-model="entityForm.punishCleanStd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishCleanAd" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishCleanVdaf" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishProcG" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishCleanMt" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishProcY" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishQualCsr" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishMacR0" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                        <div> <el-input v-model="entityForm.punishRecovery" autocomplete="off"
                                    oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlurcount(entityForm)" clearable />
                        </div>
                      </div>

                    </div>
                  </el-col>
                </el-row>
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="备注" prop="remarks">
                      <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </template>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer" v-if="dialogStatus != 'watch'">
        <div v-if="dialogStatus != 'review'">
          <el-button type="primary" v-if="entityForm.applyType=='GOOD'" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="saveEntityForm('entityForm')">
            提交审核
          </el-button>

          <el-button v-else type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="SubmitReview('entityForm')">
            提交审核
          </el-button>

          <el-button @click="SaveDraft('entityForm')" class="dialog-footer-btns">
            保存草稿
          </el-button>
        </div>
        <div v-else style="width:100%">
          <div v-if="perms[`${curd}:ismanagerreview`] || perms[`${curd}:isFinancereview`]"
               style="display: flex;flex-direction: row;">
            <div style="width:calc(100% - 250px)" class="shenpi">
              <el-input style="width:100%" type="text" v-model="entityForm.reviewMessage" placeholder="请输入审批意见" />
            </div>
            <div style="width:250px">
              <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                         @click="passfn(entityForm.id,entityForm.reviewMessage,entityForm.curFlowCode)">
                审核通过
              </el-button>
              <el-button @click="rejectfn(entityForm.id,entityForm.reviewMessage,entityForm.curFlowCode)"
                         class="dialog-footer-btns" style="width:100px">
                拒绝
              </el-button>
            </div>
          </div>
        </div>

      </div>
    </el-dialog>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/finance/settleRecv'
import { validatePhone } from '@/utils/validate'
import { settleRecvOption } from '@/const'
import productModel from '@/model/product/productList'
import { getCustomerContractNotSettle } from '@/api/quality'
import CustomerModel from '@/model/user/customer'
import { createMemoryField } from '@/utils/index'
import Cache from '@/utils/cache'
import { Decimal } from 'decimal.js'
export default {
  name: 'settleRecv',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'settleRecv',
      model: Model,
      addressValue: '',
      entityForm: { ...Model.model },
      nameEntity: { options: [], active: '' },
      limitNum: 50,
      username: 'ss',
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      filterOption: { ...settleRecvOption },
      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        phone: [{ validator: validateIsPhone, trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },

      // rules: {
      //   applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
      //   customerName: { required: true, message: '请选择结算单位', trigger: 'blur' },
      //   productName: { required: true, message: '请选择品名', trigger: 'blur' },
      //   price: { required: true, message: '请选输入价格', trigger: 'blur' },
      //   settleMoney: { required: true, message: '请选输入结算金额', trigger: 'blur' },
      //   settleWeight: { required: true, message: '请选输入结算吨数', trigger: 'blur' },
      //   sendWeight: { required: true, message: '请选输入原发吨数', trigger: 'blur' },
      //   truckCount: { required: true, message: '请选输入原发车数', trigger: 'blur' }
      // },
      rules: {
        applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' },
        productName: { required: true, message: '请选择品名', trigger: 'blur' },
        customerId: { required: true, message: '请选择结算单位', trigger: 'blur' },
        beginDate: { required: true, message: '请选择结算周期', trigger: 'blur' },
        endDate: { required: true, message: '请选择结算周期', trigger: 'blur' },
        settleMoney: { required: true, message: '请选输入结算金额', trigger: 'blur' },
        settleWeight: { required: true, message: '请选输入结算吨数', trigger: 'blur' },
        contractPrice: { required: true, message: '请选输入合同单价', trigger: 'blur' },
        truckCount: { required: true, message: '请选输入原发车数', trigger: 'blur' }
      },

      // 合同
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      customerEntity: { options: [], active: [] },
      secondPartyEntity: { value: '', options: [] },
      isshow: false,
      emptyImgPath: require(`@/assets/empty.jpg`),
      reviewLogList: [],
      awardType: '',
      punishType: '',
      smartSearchItems: [
        {
          label: '按吨',
          id: 1,
          width: 100
        },
        {
          label: '车数',
          id: 2
        }
      ],
      countList: [],
      Settlementcycle: '',
      isdisabled: false,
      optionsDisable: {},

      steplist: [],
      steplistNEW: [],
      rightListLoading: false,
      leftListLoading: false,
      dataleftlist: [],
      datarighttlist: [],
      addUserList: [],
      delUserList: [],
      Settlementdate: [],
      isallShow: false,
      tableheadlist: [
        { name: '质量指标' },
        { name: '硫(St,d)' },
        { name: '灰Ad' },
        { name: '挥发份(Vdaf)' },
        { name: '粘结G' },
        { name: '水份(Mt)' },
        { name: '胶质层(Y值)' },
        { name: '热强度(CSR)' },
        { name: '岩相(Ro)' },
        { name: '回收' }
      ],
      isCustomerchange: false
    }
  },
  watch: {
    'entityForm.productName'(name) {
      if (name) {
        const item = this.nameEntity.options.find((item) => item.name === name)
        if (item) {
          this.nameEntity.active = item.name
          this.entityForm.productCode = item.code
          this.entityForm.productId = item.id
          this.entityForm.coalType = item.coalCategory || ''
          this.entityForm.coalCategory = item.type
        }
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
        this.entityForm.coalCategory = ''
      }
    },
    'entityForm.customerId'(id) {
      if (!id) return
      const item = this.customerEntity.options.find((item) => item.value === id)
      if (item) {
        this.customerEntity.active = item
      } else {
        this.customerEntity.active = id
      }
    },
    // 'entityForm.contractId'(id) {
    //   console.log(id)
    //   if (!id) return
    //   if (this.dialogStatus === 'update' || this.dialogStatus === 'review' || this.dialogStatus === 'watch') {
    //     const value = this.filterContractItem(id, 'contractEntity')
    //     this.contractEntity.active = value
    //   }
    // },

    'entityForm.contractId'(id) {
      if (id == undefined) {
        this.entityForm = { ...this.entityForm, customerName: undefined, contractName: undefined }
      }
      if (!id) return
      this.updateAcc()
      if (this.entityForm.applyDate && id) {
        this.updateAcc(this.entityForm.applyDate, id)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch' || this.dialogStatus === 'review') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
        // this.entityForm.contractId = value
      }
    },

    // 在打开更新时watch监听然后通过过滤筛选触发contractEntityFormActive灌入显示数据
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      console.log(form)
      this.entityForm.productName = form.productName
      this.entityForm.productId = form.productId
      this.nameEntity.active = form.productName
      this.entityForm.contractCode = form.customerName
      this.entityForm.contractId = form.customerId
      this.entityForm.contractName = form.receiverName
      this.entityForm.customerName = form.firstParty
      this.secondPartyEntity.value = form.secondParty
      this.entityForm.firstParty = form.secondParty
      this.entityForm = { ...this.entityForm, customerId: value[0] }
      const item = this.contractEntity.options.find((v) => v.customerId === value[0])
      if (!item) return
      // this.customerEntity.active = item.customerId
      const itemvv = item.contractSellList.find((v) => v.id === value[1])
      if (!itemvv) return
      if (this.entityForm.contractName == undefined || this.entityForm.contractName == '') {
        this.entityForm = { ...this.entityForm, supplierName: form.firstParty, contractName: form.firstParty }
      }
      //同步合同指标
      // this.dialogStatus === 'create' ||
      if (this.isCustomerchange == true) {
        this.entityForm.contractName = itemvv.firstParty
        this.entityForm.cleanStd = itemvv.cleanStd
        this.entityForm.cleanAd = itemvv.cleanAd
        this.entityForm.cleanVdaf = itemvv.cleanVdaf
        this.entityForm.procG = itemvv.procG
        this.entityForm.cleanMt = itemvv.cleanMt
        this.entityForm.procY = itemvv.procY
        this.entityForm.qualCsr = itemvv.qualCsr
        this.entityForm.macR0 = itemvv.macR0
        this.entityForm.recovery = itemvv.recovery
        // 获取化验指标
        this.getProductQualIndicator(form.productId)
      }
    },

    // 在打开更新时watch监听然后通过过滤筛选触发contractEntityFormActive灌入显示数据
    // 'contractEntity.active'(value) {
    //   console.log(value)
    //   if (!value || !Array.isArray(value)) return
    //   const form = this.filterContractItem(value, 'contractEntity')
    //   console.log(form)
    //   if (form) {
    //     this.entityForm.name = form.productName
    //     this.entityForm.contractCode = form.customerName
    //     this.entityForm.contractId = form.customerId
    //     this.customerEntity.active = item.customerId
    //     if (this.entityForm.contractName == undefined || this.entityForm.customerName == undefined) {
    //       this.entityForm = { ...this.entityForm, customerName: form.firstParty, contractName: form.firstParty }
    //     } else {
    //       if (this.entityForm.customerName == this.entityForm.contractName) {
    //         this.customerEntity.active = form.firstParty //结算单位
    //       } else {
    //         this.customerEntity.active = this.entityForm.customerName
    //       }
    //     }
    //     this.customerEntity.active = form.firstParty //结算单位
    //     this.nameEntity.active = form.productName
    //     this.entityForm.productName = form.productName

    //     const item = this.contractEntity.options.find((v) => v.customerId === value[0])
    //     console.log(item)
    //     if (!item) return
    //     const itemvv = item.contractSellList.find((v) => v.id === value[1])
    //     console.log(itemvv)
    //     if (!itemvv) return
    //     if (this.entityForm.contractName == undefined || this.entityForm.contractName == '') {
    //       this.entityForm = { ...this.entityForm, supplierName: form.firstParty, contractName: form.firstParty }
    //     }

    //     this.entityForm.productName = itemvv.productName
    //     this.nameEntity.active = itemvv.productName

    //     this.entityForm.cleanStd = itemvv.cleanStd
    //     this.entityForm.cleanAd = itemvv.cleanAd
    //     this.entityForm.cleanVdaf = itemvv.cleanVdaf
    //     this.entityForm.procG = itemvv.procG
    //     this.entityForm.cleanMt = itemvv.cleanMt
    //     this.entityForm.procY = itemvv.procY
    //     this.entityForm.qualCsr = itemvv.qualCsr
    //     this.entityForm.macR0 = itemvv.macR0
    //     this.entityForm.recovery = itemvv.recovery
    //   }
    // },

    activetype: function (newV, oldV) {
      this.getlist(newV)
      // this.getnumber()
    }
  },
  created() {
    this.permsActionbtn(this.curd)
    this.setbuttomlistV(this.curd)
    this.getName()
    this.getContract()
    this.getCustomer()
    // 获取卖方
    this.getSellContractPartyfn()
    this.activetype = ''
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'applyDate',
        // 'receiverName',
        'contractId',
        'contractCode',
        'customerId',
        'customerName',
        'productCode',
        'productId'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })

    this.awardType = this.smartSearchItems[0].label
    this.punishType = this.smartSearchItems[0].label
    this.entityForm.awardType = this.smartSearchItems[0].label
    this.entityForm.punishType = this.smartSearchItems[0].label

    this.getnumber()
    if (this.$route.params.type) {
      this.activetype = this.$route.params.type
      if (this.$route.params.id) {
        this.handleUpdate(this.$route.params.id, 'review', 'SETTLE_RECV')
      }
    } else {
      this.activetype = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
      this.actionList.forEach((val, index) => {
        if (val.type == this.activetype) {
          val.active = true
        } else {
          val.active = false
        }
      })
    }

    this.isdisabled = false

    this.optionsDisable = {
      disabledDate(time) {
        return time.getTime() > Date.now()
      }
    }
  },

  computed: {},
  methods: {
    async getSellContractPartyfn() {
      const res = await this.model.getSellContractParty()
      if (res.data.length) this.secondPartyEntity.options = res.data
    },
    async handleCancel(row) {
      this.$confirm('确定要作废吗，作废后不可恢复哦！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await this.model.Cancel({
            id: row.id
          })
          if (res) {
            this.$message({ type: 'success', message: '操作成功!' })
            this.getList()
            this.getnumber()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '操作取消' }))
    },
    async getProductQualIndicator(id) {
      const { data } = await Model.getProductQualIndicator({
        id: id,
        source: 'SETTLE_RECV',
        beginDate: this.entityForm.beginDate,
        endDate: this.entityForm.endDate
      })
      if (data) {
        this.entityForm.assayCleanStd = data.std
        this.entityForm.assayCleanAd = data.ad
        this.entityForm.assayCleanVdaf = data.vdaf
        this.entityForm.assayProcG = data.g
        this.entityForm.assayCleanMt = data.mt
        this.entityForm.assayProcY = data.y
        this.entityForm.assayQualCsr = data.csr
        this.entityForm.assayMacR0 = data.macR0
        this.entityForm.assayRecovery = data.recovery
      }
    },
    async lookbtn(item) {
      const { data } = await this.model.findContractBuyFlow(item.id)
      data.forEach((element, index) => {
        element.flowDesignList.forEach((value, indevc) => {
          value.active = false
          if (element.curFlowCode == value.code) {
            var citrus = element.flowDesignList
            citrus.forEach((value) => {
              value.active = true
            })
          }
        })
      })
      this.steplistNEW = data
    },
    /*
     * 获取(左侧的全部用户列表)
     * */
    async generateData() {
      let data = { ...this.entityForm }
      // let data = { beginDate: this.entityForm.beginDate, endDate: this.entityForm.endDate }
      if (this.Settlementdate == '') {
        data.beginDate = ''
        data.endDate = ''
      }
      this.leftListLoading = true
      const res = await this.model.findBySettlePay({ ...data })
      this.leftListLoading = false
      if (res) {
        this.dataleftlist = res.data
        this.totalleft = res.data.length || 0
      }
    },
    handleDelSelectionChange(val) {
      this.delUserList = val.map((v) => {
        return v
      })
    },
    changeVV(e) {
      if (e) {
        this.Settlementdate = e
        this.entityForm.beginDate = e[0]
        this.entityForm.endDate = e[1]
      } else {
        this.Settlementdate = []
      }
    },
    async saveUserList(type) {
      let res
      this.rightListLoading = true
      this.leftListLoading = true
      if (type === 'add') {
        res = await this.model.selectWeightHouseOutList({
          settlePayId: this.entityForm.id,
          weightHouseOutList: this.addUserList
        })
        // this.entityForm = { ...this.entityForm, moisturizingWeight: sum }
      } else {
        res = await this.model.deSelectWeightHouseOutList({
          settlePayId: this.entityForm.id,
          weightHouseOutList: this.delUserList
        })
      }
      this.rightListLoading = false
      this.leftListLoading = false
      if (res) {
        this.getRightList('add')
        this.generateData()
      }
    },
    // sumArr(arr) {
    //   console.log(arr)
    //   var sum = 0
    //   for (var i = 0; i <= arr.length; i++) {
    //     console.log(arr[i].deductWater)
    //     sum += arr[i].deductWater
    //   }
    //   console.log(sum)
    //   // this.entityForm = { ...this.entityForm, moisturizingWeight: sum }
    //   // return sum
    // },
    handleAddSelectionChange(val) {
      this.addUserList = val.map((v) => {
        return v
      })
    },
    handleinput1(entityForm) {
      this.entityForm = { ...this.entityForm, truckCount: entityForm.truckCount }
      this.handleBlurH(entityForm)
      this.handleBlurcalculation(entityForm)
    },
    CalculateSettlementTonnage(entityForm) {
      //  结算吨数=原发吨数-补水吨数-路耗
      // 结算吨数=实收吨数receiveWeight-补水moisturizingWeight-杂质foreignSubstance-明粉clearPowder-扣重deWeight
      let total = ''
      // if (entityForm.settleWeight) {
      let receiveWeight = entityForm.receiveWeight ? entityForm.receiveWeight : 0
      let moisturizingWeight = entityForm.moisturizingWeight ? entityForm.moisturizingWeight : 0
      let foreignSubstance = entityForm.foreignSubstance ? entityForm.foreignSubstance : 0
      let clearPowder = entityForm.clearPowder ? entityForm.clearPowder : 0
      let deWeight = entityForm.deWeight ? entityForm.deWeight : 0
      total =
        parseFloat(receiveWeight) -
        parseFloat(moisturizingWeight) -
        parseFloat(foreignSubstance) -
        parseFloat(clearPowder) -
        parseFloat(deWeight)
      // }
      if (total) {
        this.entityForm.settleWeight = parseFloat(total).toFixed(4)
      }

      // if (moisturizingWeight) {
      //   //明水
      // }
      if (this.entityForm.settleWeight && this.entityForm.price) {
        this.entityForm.settleMoney = parseFloat(this.entityForm.settleWeight * this.entityForm.price).toFixed(2)
      }
    },
    handleBlurcount(entityForm) {
      // 结算单价=合同单价-质量扣罚金
      let total = ''
      if (entityForm.contractPrice) {
        let punishCleanStd = entityForm.punishCleanStd ? entityForm.punishCleanStd : 0
        let punishCleanAd = entityForm.punishCleanAd ? entityForm.punishCleanAd : 0
        let punishCleanVdaf = entityForm.punishCleanVdaf ? entityForm.punishCleanVdaf : 0
        let punishProcG = entityForm.punishProcG ? entityForm.punishProcG : 0
        let punishCleanMt = entityForm.punishCleanMt ? entityForm.punishCleanMt : 0
        let punishProcY = entityForm.punishProcY ? entityForm.punishProcY : 0
        let punishQualCsr = entityForm.punishQualCsr ? entityForm.punishQualCsr : 0
        let punishMacR0 = entityForm.punishMacR0 ? entityForm.punishMacR0 : 0
        let punishRecovery = entityForm.punishRecovery ? entityForm.punishRecovery : 0
        // total =
        //   parseFloat(entityForm.contractPrice) -
        //   parseFloat(punishCleanStd) -
        //   parseFloat(punishCleanAd) -
        //   parseFloat(punishCleanVdaf) -
        //   parseFloat(punishProcG) -
        //   parseFloat(punishCleanMt) -
        //   parseFloat(punishProcY) -
        //   parseFloat(punishQualCsr) -
        //   parseFloat(punishMacR0) -
        //   parseFloat(punishRecovery)

        total = new Decimal(entityForm.contractPrice)
          .sub(punishCleanStd)
          .sub(punishCleanAd)
          .sub(punishCleanVdaf)
          .sub(punishProcG)
          .sub(punishCleanMt)
          .sub(punishProcY)
          .sub(punishQualCsr)
          .sub(punishMacR0)
          .sub(punishRecovery)
          .toString()
      }
      if (total) {
        this.entityForm.price = total
      }
      if (this.entityForm.settleWeight && this.entityForm.price) {
        this.entityForm.settleMoney = parseFloat(this.entityForm.settleWeight * this.entityForm.price).toFixed(2)
      }
    },
    // start 历史日志搜索
    async seachfn() {
      this.Settlementdate = []
      this.paydate = []
      let data = {
        contractId: this.entityForm.contractId,
        customerId: this.entityForm.customerId,
        productId: this.entityForm.productId,
        beginDate: this.entityForm.beginDate,
        endDate: this.entityForm.endDate
      }
      this.Settlementdate.push(this.entityForm.beginDate)
      this.Settlementdate.push(this.entityForm.endDate)
      this.paydate.push(this.entityForm.beginDate)
      this.paydate.push(this.entityForm.endDate)

      if (!this.checkParamsVV(data)) return false
      const res = await this.model.save({ ...this.entityForm })
      if (res.data) {
        this.entityForm = res.data
        this.isallShow = true
        //全部列表
        this.generateData()
        this.getRightList()
      }
    },
    //START 结算明细
    /*
     * 获取(右侧的当前用户列表)
     * */
    async getRightList(type) {
      let data = {
        id: this.entityForm.id
      }
      this.rightListLoading = true
      const res = await this.model.getWeightHouseOutList(data)
      this.rightListLoading = false
      if (res) {
        this.datarighttlist = res.data
        if (type == 'add' || type == 'update') {
          let array = res.data
          let sendWeight = 0
          let receiveWeight = 0
          let truckCount = 0
          let deductWatersum = 0
          array.forEach((element, index) => {
            sendWeight += element.sendWeight
            if (element.receiveWeight) {
              receiveWeight += element.receiveWeight
              deductWatersum += element.deductWater
            }
            truckCount = index + 1
          })

          if (sendWeight > 0) {
            this.entityForm.sendWeight = parseFloat(sendWeight).toFixed(2)
          } else {
            this.entityForm.sendWeight = 0
          }
          if (receiveWeight > 0) {
            this.entityForm.receiveWeight = parseFloat(receiveWeight).toFixed(2)
          } else {
            this.entityForm.receiveWeight = 0
          }

          if (deductWatersum > 0) {
            this.entityForm.moisturizingWeight = parseFloat(deductWatersum).toFixed(4)
          } else {
            this.entityForm.moisturizingWeight = 0
          }

          this.entityForm.truckCount = truckCount
          if (res.data.length > 0) {
            if (!this.entityForm.contractPrice) {
              this.entityForm.contractPrice = res.data[0].price
            }
            this.handleBlurcount(this.entityForm)
          }
          this.CalculateSettlementTonnage(this.entityForm)
        }
        this.totalright = res.data.length || 0
      }
    },
    checkParamsVV(data, rule = []) {
      for (const [key, val] of Object.entries(data)) {
        if (rule.includes(key)) continue
        if (val === '' || val === undefined) {
          this.$message({ message: `请检查参数${key}`, type: 'warning' })
          return false
        }
      }
      return true
    },
    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },
    // 财务管理   //财务报表付款申请
    async SubmitReview(entityForm) {
      if (this.entityForm.contractName == '') {
        this.entityForm = { ...this.entityForm, contractName: undefined }
      }
      if (this.entityForm.contractName != undefined) {
        // console.log(this.entityForm.customerName + '结算单位')
        // console.log(this.entityForm.contractName + '合同名称')

        if (this.entityForm.customerName !== this.entityForm.contractName) {
          this.$message({ message: '合同名称和结算单位名称不一致，请修改后再提交审核', type: 'warning' })
          return
        }
      } else {
      }

      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        const res = await this.model.getApproved(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '提交成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.getnumber()
          this.resetVariant()
          this.dialogFormVisible = false
        }
      }
    },
    //保存草稿
    async SaveDraft(entityForm) {
      if (this.entityForm.contractName != undefined) {
        if (this.entityForm.customerName !== this.entityForm.contractName) {
          this.$message({ message: '合同名称和结算单位名称不一致，请修改后再提交审核', type: 'warning' })
          return
        }
      }
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        const res = await this.model.SaveDraftfn(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '保存草稿成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.getnumber()
          this.dialogFormVisible = false
        }
      }
    },

    change(e) {
      if (e) {
        this.Settlementcycle = e
        this.entityForm.beginDate = e[0]
        this.entityForm.endDate = e[1]
        this.getProductQualIndicator(this.entityForm.productId)
      } else {
        this.Settlementcycle = []
        this.entityForm.beginDate = ''
        this.entityForm.endDate = ''
        this.getProductQualIndicator(this.entityForm.productId)
      }
    },
    async getnumber() {
      const res = await this.model.getcountByState()
      this.countList = res.data
    },
    // 打印
    async handlePrint(row) {
      // this.isPrinting = true
      const { data } = await this.model.getprint(row.id)
      let objectUrl = window.URL.createObjectURL(new Blob([data], { type: 'application/pdf' }))

      window.open(objectUrl)
    },
    clearBtn() {
      // console.log('删除爽歪歪')
      // this.entityForm = { ...this.entityForm, settleMoney: '' }
    },
    handleBlur(entityForm) {
      let number = parseFloat(entityForm.settleWeight * entityForm.price).toFixed(2)
      if (entityForm.settleWeight != '' && entityForm.price != '') {
        // this.settleMoney = (entityForm.settleWeight * entityForm.price).toFixed(2)
        this.settleMoney = number
        // console.log(this.settleMoney)
        // console.log(99)
        if (isNaN(this.settleMoney) == false) {
          // this.entityForm.settleMoney = this.settleMoney
          this.entityForm = { ...this.entityForm, settleMoney: this.settleMoney }
        }
      }
      this.handleBlurH(entityForm)
      this.handleBlurcalculation(entityForm)
      this.calculationFinalMoney(entityForm)
    },
    // 计算结算金
    // 计算结算金
    calculationFinalMoney(entityForm) {
      //entityForm.punishMoney 扣罚金
      // entityForm.awardMoney 奖励金
      if (this.entityForm.settleMoney) {
        if (this.entityForm.awardMoney) {
          let num1 = parseFloat(Number(this.entityForm.settleMoney) + Number(this.entityForm.awardMoney)).toFixed(2)

          // console.log('加奖励金' + num1)
          this.entityForm = { ...this.entityForm, finalMoney: num1 }
        }
        if (this.entityForm.punishMoney) {
          // let num2 = parseInt(this.entityForm.settleMoney) - parseInt(this.entityForm.punishMoney)
          let num2 = parseFloat(Number(this.entityForm.settleMoney) - Number(this.entityForm.punishMoney)).toFixed(2)
          // console.log('减扣罚金' + num2)
          this.entityForm = { ...this.entityForm, finalMoney: num2 }
        }

        if (this.entityForm.awardMoney && this.entityForm.punishMoney) {
          // let tsnumber = Number(this.entityForm.punishMoney) + Number(this.entityForm.awardMoney)
          // let num3 = Number(this.entityForm.settleMoney) - Number(tsnumber)

          let num3 = parseFloat(
            Number(this.entityForm.settleMoney) - Number(this.entityForm.punishMoney) + Number(this.entityForm.awardMoney)
          ).toFixed(2)
          // console.log('减扣罚金加奖励金' + num3)
          this.entityForm = { ...this.entityForm, finalMoney: num3 }
        }
      }
    },
    handleBlurH(entityForm) {
      this.entityForm = { ...this.entityForm, settleWeight: entityForm.settleWeight, truckCount: entityForm.truckCount }
      let number = 0
      if (entityForm.awardType == '按吨') {
        number = this.entityForm.settleWeight
      } else if (entityForm.awardType == '车数') {
        number = this.entityForm.truckCount
      }
      if (entityForm.awardBase != undefined && number != undefined) {
        this.entityForm.awardMoney = (entityForm.awardBase * number).toFixed(2)
      }
      this.calculationFinalMoney(entityForm)
    },
    sethandleBlur(entityForm) {
      this.entityForm = { ...this.entityForm, awardMoney: entityForm.awardMoney }
      this.calculationFinalMoney(this.entityForm)
    },
    sethandleBlurf(entityForm) {
      this.entityForm = { ...this.entityForm, punishMoney: entityForm.punishMoney }
      this.calculationFinalMoney(this.entityForm)
    },
    handleBlurcalculation(entityForm) {
      this.entityForm = { ...this.entityForm, settleWeight: entityForm.settleWeight, truckCount: entityForm.truckCount }
      let number = 0
      if (entityForm.punishType == '按吨') {
        number = this.entityForm.settleWeight
      } else if (entityForm.punishType == '车数') {
        number = this.entityForm.truckCount
      }
      if (entityForm.punishBase != undefined && number != undefined) {
        this.entityForm.punishMoney = (entityForm.punishBase * number).toFixed(2)
      }
      this.calculationFinalMoney(entityForm)
    },
    changeKey(key) {
      this.entityForm.awardBase = ''
      this.entityForm.awardMoney = ''
      const index = this.smartSearchItems.findIndex((val) => val.id === key)
      if (~index) {
        this.entityForm.awardType = this.smartSearchItems[index].label
        this.awardType = this.smartSearchItems[index].label
      }
    },
    changeKeyV(key) {
      this.entityForm.punishBase = ''
      this.entityForm.punishMoney = ''
      const index = this.smartSearchItems.findIndex((val) => val.id === key)
      if (~index) {
        this.punishType = this.smartSearchItems[index].label
        this.entityForm.punishType = this.smartSearchItems[index].label
      }
    },
    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      this.isdisabled = false
      this.isCustomerchange = false
      this.reviewLogList = []
      this.steplist = []
      this.contractEntity.active = []
      this.customerEntity.active = []
      this.secondPartyEntity.value = ''
      this.entityForm.customerId = ''
      this.Settlementcycle = ''
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.awardType = this.smartSearchItems[0].label
      this.punishType = this.smartSearchItems[0].label
      this.entityForm.awardType = this.smartSearchItems[0].label
      this.entityForm.punishType = this.smartSearchItems[0].label
      this.getList()
    },

    // // 保存取消时会触发初始化数据
    // resetVariant() {
    //   this.entityFormLoading = false
    //   this.dialogFormVisible = false
    //   this.dialogVisible = false
    //   this.isdisabled = false
    //   this.reviewLogList = []
    //   this.contractEntity.active = []
    //   this.customerEntity.active = []
    //   this.Settlementcycle = ''
    //   // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
    //   if (!this.memoryEntity.triggered) {
    //     this.nameEntity.active = ''
    //     this.entityForm = { ...this.model.model }
    //   } else {
    //     this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
    //   }
    //   this.awardType = this.smartSearchItems[0].label
    //   this.punishType = this.smartSearchItems[0].label
    //   this.entityForm.awardType = this.smartSearchItems[0].label
    //   this.entityForm.punishType = this.smartSearchItems[0].label
    //   this.getnumber()
    // },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) {}
    },
    // 获取合同选择接口
    async getContract() {
      const { data } = await getCustomerContractNotSettle()
      // 供应商名称返回id用来查询
      this.contractEntity.options = this.formatContract({
        data,
        key: { customerName: 'displayName', customerId: 'id', contractSellList: 'contractSellList' }
      })
    },

    async getCustomer() {
      try {
        let options = []
        const { data } = await CustomerModel.page({ size: 1000 })
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        this.customerEntity.options = options
      } catch (e) {}
    },
    // 合同改变同步entityForm
    handleCustomer({ value, label }) {
      this.entityForm = { ...this.entityForm, customerName: label, customerId: value }
    },
    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    handleNameEntityV(val) {
      console.log(val)

      this.contractEntity.active = val
      this.isCustomerchange = true
      this.isdisabled = true
      // this.entityForm = { ...this.entityForm, customerName: undefined }
    },
    selectBlur(value) {
      this.customerEntity.active = value.target.value
      this.entityForm.customerId = ''
      this.entityForm = { ...this.entityForm, customerName: value.target.value }
      // this.entityForm=    e.target.value
    },
    // 累计接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      const paramsAcc = await this.getAccValues({ date, contractId })
      Object.keys(paramsAcc).forEach((key) => {
        this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
        this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      })
      this.computeWayCostAcc()
    },
    computeWayCostAcc() {
      let { receiveWeightAcc, sendWeightAcc } = this.entityForm
      receiveWeightAcc = receiveWeightAcc * 1
      sendWeightAcc = sendWeightAcc * 1
      let wayCostAcc = (((sendWeightAcc - receiveWeightAcc) / sendWeightAcc) * 100).toFixed(2) * 1
      this.entityForm.wayCostAcc = !Number.isNaN(wayCostAcc) ? wayCostAcc : 0
    },
    /**
     * @用于格式化合约数据因为合约的子customerId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    /**
     * @找到选中的合约项
     * @value {[customerId,customerId]} 第一个父级Id 第二个是子集Id
     * @target {contract|contractEntityForm}<String>
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    async handleUpdate(item, type, gettype) {
      this.Settlementcycle = []
      this.Settlementdate = []
      this.paydate = []
      this.SettlementFreightdate = []
      if (typeof item == 'object') {
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        } else if (type == 'update') {
          this.isshow = true
          this.dialogStatus = 'update'
        } else if (type == 'watch') {
          this.isshow = false
          this.dialogStatus = 'watch'
        }
        this.dialogFormVisible = true
        const { data } = await this.model.getUploadList(item.id)
        this.entityForm = { ...data }

        if (gettype) {
          const { data } = await Model.getlclist({ type: gettype })
          this.entityForm.stepcode = this.entityForm.curFlowCode
          this.entityForm.stepName = this.entityForm.curFlowName
          data.forEach((element, index) => {
            element.active = false
            if (element.code === this.entityForm.stepcode) {
              var citrus = data
              citrus.forEach((value) => {
                value.active = true
              })
            }
          })
          this.steplist = data
        }
        //全部列表
        this.Settlementcycle.push(this.entityForm.beginDate)
        this.Settlementcycle.push(this.entityForm.endDate)
        this.paydate.push(this.entityForm.beginDate)
        this.paydate.push(this.entityForm.endDate)
        this.Settlementdate.push(this.entityForm.beginDate)
        this.Settlementdate.push(this.entityForm.endDate)

        this.isallShow = true
        if (type == 'update' || type == 'create') {
          this.generateData()
          this.getRightList('update')
        }

        this.awardType = this.entityForm.awardType
        this.punishType = this.entityForm.punishType

        this.reviewLogList = data.reviewLogList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((item, index) => {
            newarry.push(item.reviewMessage)
          })
          // this.entityForm.reviewMessage = newarry.toString()
          this.entityForm.reviewMessage = ''
        }
      } else if (typeof item == 'string') {
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        }
        this.dialogFormVisible = true
        const { data } = await this.model.getUploadList(item)
        this.awardType = this.entityForm.awardType
        this.punishType = this.entityForm.punishType
        this.reviewLogList = data.reviewLogList
        this.entityForm = { ...data }
        if (gettype) {
          const { data } = await Model.getlclist({ type: gettype })
          this.entityForm.stepcode = this.entityForm.curFlowCode
          this.entityForm.stepName = this.entityForm.curFlowName
          data.forEach((element, index) => {
            element.active = false
            if (element.code === this.entityForm.stepcode) {
              var citrus = data
              citrus.forEach((value) => {
                value.active = true
              })
            }
          })
          this.steplist = data
        }
        //全部列表
        this.isallShow = true
        this.Settlementcycle.push(this.entityForm.beginDate)
        this.Settlementcycle.push(this.entityForm.endDate)
        this.paydate.push(this.entityForm.beginDate)
        this.paydate.push(this.entityForm.endDate)
        this.Settlementdate.push(this.entityForm.beginDate)
        this.Settlementdate.push(this.entityForm.endDate)
        if (type == 'update' || type == 'create') {
          this.generateData()
          this.getRightList('update')
        }

        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((itemv, index) => {
            newarry.push(itemv.reviewMessage)
          })
          this.entityForm.reviewMessage = newarry.toString()
        }
      }
    },
    // async handleUpdate(item, type) {
    //   this.Settlementcycle = []
    //   if (typeof item == 'object') {
    //     this.entityForm = { ...item }
    //     if (type == 'review') {
    //       this.isshow = true
    //       this.dialogStatus = 'review'
    //     } else if (type == 'update') {
    //       this.isshow = true
    //       this.dialogStatus = 'update'
    //     } else if (type == 'watch') {
    //       this.isshow = false
    //       this.dialogStatus = 'watch'
    //     }
    //     this.dialogFormVisible = true
    //     const { data } = await this.model.getUploadList(item.id)

    //     this.entityForm = { ...data }
    //     this.Settlementcycle.push(this.entityForm.beginDate)
    //     this.Settlementcycle.push(this.entityForm.endDate)
    //     // this.customerEntity.active
    //     if (this.entityForm.customerId) {
    //       this.entityForm.customerId = this.entityForm.customerId
    //     } else {
    //       // this.entityForm.customerId = this.entityForm.customerName
    //     }

    //     this.awardType = this.entityForm.awardType
    //     this.punishType = this.entityForm.punishType
    //     this.reviewLogList = data.reviewLogList
    //     let newarry = []
    //     if (data.reviewLogList.length > 0) {
    //       data.reviewLogList.forEach((item, index) => {
    //         newarry.push(item.reviewMessage)
    //       })
    //       // this.entityForm.reviewMessage = newarry.toString()
    //       this.entityForm.reviewMessage = ''
    //     }
    //   } else if (typeof item == 'string') {
    //     if (type == 'review') {
    //       this.isshow = true
    //       this.dialogStatus = 'review'
    //     }
    //     this.dialogFormVisible = true
    //     const { data } = await this.model.getUploadList(item)
    //     this.entityForm = { ...data }

    //     this.Settlementcycle.push(this.entityForm.beginDate)
    //     this.Settlementcycle.push(this.entityForm.endDate)

    //     this.awardType = this.entityForm.awardType
    //     this.punishType = this.entityForm.punishType
    //     this.reviewLogList = data.reviewLogList
    //     let newarry = []
    //     if (data.reviewLogList.length > 0) {
    //       data.reviewLogList.forEach((itemv, index) => {
    //         newarry.push(itemv.reviewMessage)
    //       })
    //       // this.entityForm.reviewMessage = newarry.toString()
    //       this.entityForm.reviewMessage = ''
    //     }
    //   }
    // },

    //点击状态获取数据
    async getlist(newV) {
      this.activetype = newV
      this.actionList.forEach((val, index) => {
        if (val.type == newV) {
          val.active = true
        } else {
          val.active = false
        }
      })
      this.filtersSeach.filter_INS_state = newV
      this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    },
    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    // 更新累计Acc
    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      const paramsAcc = await this.getAccValues({ date, contractId })
      Object.keys(paramsAcc).forEach((key) => {
        this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
        this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      })
      this.computeWayCostAcc()
    },
    //去审核
    // async passfn(id, reviewMessage, state) {
    //   if (state == 'NEW') {
    //     const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage })
    //   } else if (state == 'PASS') {
    //     const res = await this.model.getfinancePass({ id: id, reviewMessage: reviewMessage })
    //   }
    //   this.dialogFormVisible = false
    //   this.$refs[this.curd].$emit('refresh')
    //   this.resetVariant()
    //   this.getlist(this.activetype)
    //   this.getnumber()
    // },

    // async rejectfn(id, reviewMessage) {
    //   const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage })
    //   this.getnumber()
    //   this.dialogFormVisible = false
    //   this.$refs[this.curd].$emit('refresh')
    // }

    //去审核
    async passfn(id, reviewMessage, curFlowCode) {
      // if (state == 'NEW') {
      const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage, curFlowCode: curFlowCode })
      // } else if (state == 'PASS') {
      //   const res = await this.model.getfinancePass({ id: id, reviewMessage: reviewMessage })
      // }
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
      this.resetVariant()
      this.getlist(this.activetype)
      this.getnumber()
    },

    async rejectfn(id, reviewMessage, curFlowCode) {
      const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage, curFlowCode: curFlowCode })
      this.getnumber()
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>

<style scoped>
.search-component >>> .el-input__inner {
  border: none;
}
</style>


<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.colorBLUE {
  color: #2f79e8 !important;
}
.bordercolorBLUE {
  border: solid 1px #2f79e8 !important;
}
.bzminboxV:first-child {
  width: 50% !important;
  .lin {
    width: 0px !important;
    display: none;
  }
  .context {
    width: 100% !important;
  }
}
.bzboxV {
  width: 100%;
  display: flex;
  font-size: 12px;
  .bzminboxV {
    width: 100%;
    display: flex;
    .icon {
      font-size: 30px;
    }
    .context {
      width: 50%;
      color: #ccc;
      text-align: center;
    }
    .lin {
      width: 50%;
      height: 1px;
      border: solid 1px #606266;
      margin-top: 15px;
    }
  }
}
.bzminbox:first-child {
  width: 50% !important;
  .lin {
    width: 0px !important;
    display: none;
  }
  .context {
    width: 100% !important;
  }
}
.bzbox {
  width: 100%;
  display: flex;
  font-size: 12px;
  .bzminbox {
    width: 100%;
    display: flex;
    .icon {
      font-size: 30px;
    }
    .context {
      width: 50%;
      color: #ccc;
      text-align: center;
    }
    .lin {
      width: 50%;
      height: 1px;
      border: solid 1px #606266;
      margin-top: 15px;
    }
  }
}
// ::v-deep .el-form-item__label {
//   width: 100px !important;
// }
// ::v-deep .el-form-item__content {
//   margin-left: 100px !important;
// }
::v-deep .el-date-editor .el-range-separator {
  padding: 0 0;
}
::v-deep .el-form-item__label {
  font-weight: 400;
}
.tablebox {
  width: 100%;
  .tablehead {
    width: 100%;
    display: flex;
    & > div {
      width: calc(100% / 10);
      background-color: #2f79e8;
      text-align: center;
      padding: 10px;
      color: #fff;
    }
  }
  .tablecon {
    width: 100%;
    display: flex;
    border-left: solid 1px #f1f1f2;
    border-right: solid 1px #f1f1f2;
    border-bottom: solid 1px #f1f1f2;
    & > div {
      width: calc(100% / 10);
      border-right: solid 1px #f1f1f2;
      text-align: center;
      padding: 10px;
    }
  }
}
.smart-search {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  width: 47%;
  margin-bottom: 5px;
  border-radius: 3px;

  .prepend {
    background-color: #f5f7fa;
    padding-right: 10px;
    border-right: solid 1px #dcdfe6;
    .el-dropdown {
      cursor: pointer;
    }

    .el-icon-arrow-down {
      width: 26px;
      text-align: center;
    }

    .search-key {
      display: inline-block;
      height: 30px;
      line-height: 30px;
    }
  }

  .search-component {
    flex: 1;
    .el-input {
      width: 100%;
      height: 30px;
      line-height: 30px;
      .el-input__inner {
        border: none;
      }
      & .is-focus {
        .el-input__inner {
          border-color: #dcdfe6 !important;
        }
      }
    }
  }
}
// .form-layoutV {
//   .el-row {
//     margin-bottom: 10px;
//   }
// }
.tipbox {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  .tipline {
    margin-left: 20px;
    .redtext {
      font-size: 1.5rem;
      color: red;
    }
  }
}
// .bigbox {
//   width: 100%;
//   padding: 15px 15px 0 15px;
//   background-color: #fff;
//   .btnbox {
//     width: 100%;
//     display: flex;
//     flex-direction: row;
//     align-items: center;
//   }
// }
.contentbox {
  width: 500px;
  margin: 100px auto 0 auto;
  font-size: 12px;
  .line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tsline {
    float: right;
    margin-top: 40px;
  }
  .inputbox {
    width: 100px;
    margin-bottom: 10px;
    border-bottom: solid 1px #ccc;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
<style scoped>
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
/* .dialog-footer >>> .dialog-footer-btns {
  border: 1px solid #2f79e8;
  color: #2f79e8;
} */
/* .opt-btn {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
}
.opt-btnV {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
} */
</style>