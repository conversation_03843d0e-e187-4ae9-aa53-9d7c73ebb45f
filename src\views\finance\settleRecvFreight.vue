<template>
  <div class="app-container">
    <!-- <el-date-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate" placeholder="选择日期"
                    :clearable="false" /> -->
    <!-- :showImport="perms[`${curd}:addimport`]||false" -->
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  :showImport="perms[`${curd}:addimport`]||false" @import="handleImpiort" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" :buttomlist="buttomlist" :actionList="actionList" />

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :statetype="activetype" :buttomlist="buttomlist"
            :showSummary='false' :countList="countList" :actionList="actionList" title="供应商" otherHeight="170">

      <el-table-column label="状态" slot="state" align="center" width="120">
        <template slot-scope="scope">
          <!-- 通用审核状态 NEW-待审核、PASS-通过、REJECT-拒绝 -->
          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :'info'))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '待审核' : (scope.row.state == 'PASS' ? '已审核' : (scope.row.state == 'REJECT' ? '拒绝': ''))) }}
          </el-tag>

        </template>
      </el-table-column>

      <el-table-column label="结算周期" slot="beginDate" align="center" width="180">
        <template slot-scope="scope">
          <span v-if="scope.row.beginDate">
            {{scope.row.beginDate}}至{{scope.row.endDate}}
          </span>
        </template>
      </el-table-column>

      <el-table-column slot="isSettle" label="结算状态" align="center">
        <template slot-scope="scope" v-if="scope.row.isSettle">
          <el-tag :type="(scope.row.isSettle=='Y' ? 'success':(scope.row.isSettle == 'N' ? 'danger' : ''))" size="mini">
            {{scope.row.isSettle == 'Y' ? '已结清' : (scope.row.isSettle == 'N' ? '未结清' :  '') }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="当前流程" slot="curFlowName" align="center" fixed="right" width="80">
        <template slot-scope="scope">
          <el-popover placement="right" width="700" trigger="click">
            <div v-for="(item,index) in steplistNEW" :key="index">
              <div>
                <div>{{item.label}}: <span style="color:red">{{item.curFlowName}}</span></div>
              </div>
              <el-row type="flex" :gutter="50" style="margin:20px 0">
                <el-col style="margin-bottom: -13px">
                  <div class="bzboxV">
                    <div class="bzminboxV" v-for="(value,indexc) in item.flowDesignList" :key="indexc">
                      <div class="lin" :class="value.active==true?'bordercolorBLUE':''"></div>
                      <div class="context" :class="value.active==true?'colorBLUE':''">
                        <i class="el-icon-edit icon" v-if="value.code=='SETTLE_RECV_FREIGHT_DRAFT'"></i>
                        <i class="el-icon-coordinate icon"
                           v-if="value.code=='SETTLE_RECV_FREIGHT_FINANCE_LEADER' ||  value.code=='SETTLE_RECV_FREIGHT_MANAGER' ||  value.code=='SETTLE_RECV_FREIGHT_BOSS' ||  value.code=='SETTLE_RECV_FREIGHT_PASS'"></i>

                        <i class="el-icon-link icon" v-if="value.code=='PASS'"></i>
                        <i class="el-icon-help icon" v-if="value.code=='SETTLE_RECV_FREIGHT_REJECT'"></i>
                        <i class="el-icon-document-checked" v-if="value.code=='SETTLE_RECV_PAYED'" style="font-size: 30px;"></i>

                        <div>{{value.aliasName}}</div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <el-button slot="reference" style="padding: 3px 6px;" @click="lookbtn(scope.row)">流程</el-button>
          </el-popover>
        </template>
      </el-table-column>

      <!-- <el-table-column label="操作" slot="opt" max-width="150" width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <div v-if="perms[`${curd}:ismanagerreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state =='NEW'" color="#FF726B" @click="handleUpdate(scope.row,'review')">
                去审核(总经理)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:isFinancereview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state=='PASS'" color="#33CAD9" @click="handleUpdate(scope.row,'review')">
                去审核(财务)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" color="#FF726B"
                      @click="handleUpdate(scope.row,'update')">
                编辑
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'PASS_FINANCE'" color="#FF726B"
                      @click="handlePrint(scope.row,'update')">
                <span v-if="scope.row.isPrint=='Y'">补打</span>
                <span v-else>打印</span>
              </el-tag>
            </div>
            <div v-if="scope.row.state=='DRAFT,REJECT' || scope.row.state=='PASS' 
                 || scope.row.state == 'PASS_FINANCE' || scope.row.state=='NEW' ">
              <el-tag class="opt-btn" color="#FF726B" @click="handleUpdate(scope.row,'watch')">
                查看
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false" style="margin-left:5px">
              <el-tag class="opt-btn" style="cursor: pointer;" color="#2f79e8"
                      v-if="scope.row.state == 'PASS_FINANCE' || scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'"
                      @click="handleDel(scope.row)">删除
              </el-tag>
            </div>

          </div>
        </template>
      </el-table-column> -->

      <el-table-column label="操作" slot="opt" max-width="100" width="170" fixed="right" align="center">
        <template slot-scope="scope">
          <div style="display: flex; flex-direction:row;justify-content: center;flex-wrap: wrap;">
            <div v-if="perms[`${curd}:ismanagerreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode =='SETTLE_RECV_FREIGHT_MANAGER'" color="#FF726B"
                      @click="handleUpdate(scope.row,'review','SETTLE_RECV_FREIGHT')">
                去审核(总经理)
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:isFinancereview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode=='SETTLE_RECV_FREIGHT_FINANCE_LEADER'" color="#33CAD9"
                      @click="handleUpdate(scope.row,'review','SETTLE_RECV_FREIGHT')">
                去审核(财务负责人)
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:isBossreview`] || false">
              <el-tag class="opt-btn" v-if="scope.row.curFlowCode=='SETTLE_RECV_FREIGHT_BOSS'" color="#33CAD9"
                      @click="handleUpdate(scope.row,'review','SETTLE_RECV_FREIGHT')">
                去审核(董事长)
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT' || scope.row.state == 'REJECT'" color="#FF726B"
                      @click="handleUpdate(scope.row,'update')">
                编辑
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'PASS'" color="#FF726B" @click="handlePrint(scope.row,'update')">
                <span v-if="scope.row.isPrint=='Y'">补打</span>
                <span v-else>打印</span>
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.isDeduct=='Y'" color="#FF726B" @click="handlekdPrint(scope.row,'update')">
                <span>打印扣款单</span>
              </el-tag>
            </div>

            <div>
              <el-tag class="opt-btn" v-if="scope.row.state == 'PASS'" color="#2f79e8" @click="handDownload(scope.row)">
                <span>下载</span>
              </el-tag>
            </div>

            <div v-if="perms[`${curd}:view`] || false">
              <el-tag class="opt-btn" color="#FF726B" v-if="scope.row.state=='REJECT'  ||  scope.row.state=='PASS' || 
                 scope.row.state=='PASS_FINANCE' || scope.row.state=='NEW'"
                      @click="handleUpdate(scope.row,'watch','SETTLE_RECV_FREIGHT')">
                查看
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false" style="margin-left:5px">
              <el-tag class="opt-btn" style="cursor: pointer;" color="#2f79e8"
                      v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>

    </s-curd>

    <el-dialog top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :fullscreen="screen.full" :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">

      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="right" v-if="dialogFormVisible">
        <el-row type="flex" :gutter="50" v-if="steplist.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="steplist">
              <div class="bzbox">
                <div v-for="(item,index) in steplist" :key="index" class="bzminbox">
                  <div class="lin" :class="item.active==true?'bordercolorBLUE':''"></div>
                  <div class="context" :class="item.active==true?'colorBLUE':''">
                    <i class="el-icon-edit icon" v-if="item.code=='SETTLE_RECV_FREIGHT_DRAFT'"></i>
                    <i class="el-icon-coordinate icon"
                       v-if="item.code=='SETTLE_RECV_FREIGHT_FINANCE_LEADER' ||  item.code=='SETTLE_RECV_FREIGHT_MANAGER' ||  item.code=='SETTLE_RECV_FREIGHT_BOSS'"></i>
                    <i class="el-icon-link icon" v-if="item.code=='SETTLE_RECV_FREIGHT_PASS'"></i>
                    <i class="el-icon-help icon" v-if="item.code=='SETTLE_RECV_FREIGHT_REJECT'"></i>
                    <div>{{item.aliasName}}</div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col>
            <template>
              <div class="form-title">基础信息</div>
              <div class="form-layout form-layoutV">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-form-item label="日期" prop="applyDate">
                      <date-select v-model="entityForm.applyDate" clearable :isset="true" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="承运商" prop="carrier">
                      <select-down :value.sync="carrier.active" :list="carrier.options" @eventChange="handleCarrierEntity" />
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="结算编号" prop="carriageSettleRecvCode">
                      <el-input v-model="entityForm.carriageSettleRecvCode" placeholder="" disabled></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <div v-if="dialogStatus=='create' || dialogStatus=='update'" class="form-title">运费明细</div>
              <div v-if="dialogStatus=='create' || dialogStatus=='update'">
                <div class="form-layout form-layoutV">
                  <el-row type="flex" :gutter="50">
                    <el-col>
                      <div class="form-edit-content">
                        <div class="left-flex">
                          <h3 class="content-title">全部列表</h3>
                          <div class="content">
                            <div class="filter-container">
                              <div style="width:100%">
                                <el-date-picker v-model="SettlementFreightdate" type="daterange" range-separator="至"
                                                start-placeholder="开始日期" :picker-options="optionsDisable"
                                                value-format="yyyy-MM-dd" end-placeholder="结束日期" @change="changeYY"
                                                style="width:48%;margin-right:10px">
                                </el-date-picker>
                                <select-down :value.sync="nameEntity.active" :list="nameEntity.options" placeholder="请选择品名"
                                             @eventChange="handleNameEntity" style="width:48%" />
                              </div>
                              <div style="margin-top:10px;width:100%; display: flex;width:100%">
                                <div style="width:49%">
                                  <el-select v-model="entityForm.customerName" placeholder="请选择收货单位" clearable filterable
                                             style="width:100%" @change="changereceiverName"
                                             :disabled="dialogStatus==='review'|| dialogStatus === 'watch' || dialogStatus =='change' ?true:false">
                                    <el-option v-for="item in receiverNameEntity.options" :key="item.name+item.type"
                                               :label="item.name" :value="item.name">
                                      <span style="float: left">{{ item.name }}</span>
                                      <el-tag style="float: right;" class="opt-btn" color="#69baff" v-if="item.type=='子公司'">
                                        {{ item.type }}
                                      </el-tag>
                                      <el-tag style="float: right;" class="opt-btn" color="#f20813" v-if="item.type=='供应商'">
                                        {{ item.type }}
                                      </el-tag>
                                      <el-tag style="float: right;" class="opt-btn" color="#f20813" v-if="item.type=='客户'">
                                        {{ item.type }}
                                      </el-tag>
                                    </el-option>
                                  </el-select>
                                </div>
                                <div style="width:49%">
                                  <el-button type="primary" icon="el-icon-search"
                                             @click="getFreightLeftData('qurey')">查询</el-button>
                                </div>
                              </div>
                            </div>

                            <el-table :data="dataFreightleftlist" height="340px" v-loading="FreightleftListLoading"
                                      element-loading-text="加载中..." stripe border highlight-current-row
                                      @selection-change="handleAddSelectionChangeFreightLeft">
                              <el-table-column type="selection" width="40">
                              </el-table-column>
                              <el-table-column label="日期" prop="date" align="center" width="80"></el-table-column>
                              <el-table-column label="收货单位" prop="customerName" align="center" width="120"></el-table-column>
                              <el-table-column label="品名" prop="cargoType" align="center" width="100"></el-table-column>
                              <el-table-column label="车牌号" prop="plateNumber" align="center"></el-table-column>
                              <el-table-column label="原发" prop="originalTon" align="center"></el-table-column>
                              <el-table-column label="实收" prop="weight" align="center"></el-table-column>
                              <el-table-column label="实收日期" prop="actualReceiveDate" align="center"></el-table-column>
                              <el-table-column label="实结吨数" prop="actualSettleWeight" align="center"></el-table-column>
                              <el-table-column label="运价" prop="freightPrice" align="center"></el-table-column>
                              <el-table-column label="煤价" prop="price" align="center"></el-table-column>
                              <el-table-column label="扣款" prop="deductMoney" align="center"></el-table-column>
                              <el-table-column label="结算金额" prop="settleMoney" align="center"></el-table-column>
                            </el-table>

                          </div>
                        </div>
                        <div class="mid-tools">
                          <el-button type="primary" icon="el-icon-arrow-right" @click="saveFreightList('add')"
                                     :disabled="FreightrightListLoading || addFreightList.length === 0 "></el-button>
                          <br>
                          <el-button type="danger" icon="el-icon-arrow-left" @click="saveFreightList('del')"
                                     :disabled="FreightrightListLoading || delFreightList.length === 0 "></el-button>
                        </div>
                        <div class="right-form">
                          <h3 class="content-title">选中列表</h3>
                          <div class="content">
                            <el-table :data="dataFreightrighttlist" height="420px" v-loading="FreightrightListLoading"
                                      element-loading-text="加载中..." stripe border highlight-current-row
                                      @selection-change="handleDelSelectionChangeFreight">
                              <el-table-column type="selection" width="40">
                              </el-table-column>
                              <el-table-column label="日期" prop="date" align="center" width="80"></el-table-column>
                              <el-table-column label="收货单位" prop="customerName" align="center" width="120"></el-table-column>
                              <el-table-column label="品名" prop="cargoType" align="center" width="100"></el-table-column>
                              <el-table-column label="车牌号" prop="plateNumber" align="center"></el-table-column>
                              <el-table-column label="原发" prop="originalTon" align="center"></el-table-column>
                              <el-table-column label="实收" prop="weight" align="center"></el-table-column>
                              <el-table-column label="实收日期" prop="actualReceiveDate" align="center"></el-table-column>
                              <el-table-column label="实结吨数" prop="actualSettleWeight" align="center"></el-table-column>
                              <el-table-column label="运价" prop="freightPrice" align="center"></el-table-column>
                              <el-table-column label="煤价" prop="price" align="center"></el-table-column>
                              <el-table-column label="扣款" prop="deductMoney" align="center"></el-table-column>
                              <el-table-column label="结算金额" prop="settleMoney" align="center"></el-table-column>
                            </el-table>
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <div v-if="dialogStatus=='create'|| dialogStatus=='update'"
                   style="display: flex;justify-content: flex-end;padding: 10px 18px;">
                <el-button type="primary" @click="savedata">
                  保存数据 </el-button>
                <el-button type="primary" class="dialog-footer-btns" @click="cleanup">
                  清除 </el-button>
              </div>

              <div class="form-title">运费数据</div>
              <div class="form-layout form-layoutV">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <div class="tstablebox">
                      <el-table :data="entityForm.settleRecvFreightList" border :header-cell-style="headClass" style="width:100%;"
                                :summary-method="totalOutPrice" height="300" class="tstable" show-summary>
                        <el-table-column label="发货单位" prop="supplierName" align="center"></el-table-column>
                        <el-table-column label="收货单位" prop="customerName" align="center"></el-table-column>
                        <el-table-column label="品名" prop="cargoType" align="center"></el-table-column>
                        <el-table-column label="日期" prop="date" align="center" width="100"></el-table-column>
                        <el-table-column label="车数" prop="truckCount" align="center" width="80"></el-table-column>
                        <el-table-column label="原发吨数" prop="originalTon" align="center" width="100"></el-table-column>
                        <el-table-column label="实结吨数" prop="actualSettleWeight" align="center" width="100"></el-table-column>
                        <el-table-column label="单价" prop="freightPrice" align="center" width="80"></el-table-column>
                        <el-table-column label="扣款" prop="deductMoney" align="center" width="80"></el-table-column>
                        <el-table-column label="结算金额" prop="settleMoney" align="center" width="100"></el-table-column>
                        <el-table-column label="备注" prop="remarks" align="center">
                          <template scope="scope">
                            <el-input type="textarea" v-model="scope.row.remarks"
                                      @input="handleChange(scope.$index, scope.row)"></el-input>
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" prop="caozuo">
                          <template scope="scope">
                            <span class="operation">
                              <el-button size="mini" type="primary" @click="handdetele(scope.$index, scope.row)">删除</el-button>
                            </span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <div class="form-title" v-if="entityForm.weightHouseLogList.length>0">扣款单</div>
              <div class="form-layout form-layoutV" v-if="entityForm.weightHouseLogList.length>0">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <div class="tstableboxV">
                      <el-table :data="entityForm.weightHouseLogList" border :header-cell-style="headClass" style="width:100%;"
                                :summary-method="totalOutPriceV" height="200" class="tstableV" show-summary>
                        <el-table-column label="日期" prop="secondWeightDate" align="center"></el-table-column>
                        <el-table-column label="到达地" prop="cargoType" align="center"></el-table-column>
                        <el-table-column label="车号" prop="plateNumber" align="center"></el-table-column>
                        <el-table-column label="净重" prop="originalTon" align="center"></el-table-column>
                        <el-table-column label="实结吨数" prop="actualSettleWeight" align="center"></el-table-column>
                        <el-table-column label="实结亏吨" prop="lossNull" align="center"></el-table-column>
                        <el-table-column label="路耗" prop="roadCost" align="center">0.3</el-table-column>
                        <el-table-column label="扣水" prop="deductWater" align="center"></el-table-column>
                        <el-table-column label="应扣亏吨" prop="shouldDeductLossNull" align="center"></el-table-column>
                        <el-table-column label="亏吨扣款(元/吨)" prop="deductMoneyCoalPrice" align="center"></el-table-column>
                        <el-table-column label="金额" prop="deductMoney" align="center"></el-table-column>
                      </el-table>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <div class="form-title">备注</div>
              <div class="form-layout form-layoutV">
                <el-row type="flex" :gutter="50">
                  <el-col>
                    <el-input type="textarea" v-model="entityForm.remarks" :autosize="{ minRows: 5, maxRows: 10}"></el-input>
                  </el-col>
                </el-row>
              </div>

            </template>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer" v-if="dialogStatus != 'watch'">
        <el-button class="dialog-footer-all" @click="handleFullScreen">{{screen.full?'取消全屏':'全屏'}}</el-button>

        <div v-if="dialogStatus != 'review'">
          <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="SubmitReview('entityForm')">
            提交审核
          </el-button>
          <el-button @click="SaveDraft('entityForm')" class="dialog-footer-btns">
            保存草稿
          </el-button>
        </div>
        <div v-else style="width:100%">
          <div v-if="perms[`${curd}:ismanagerreview`] || perms[`${curd}:isFinancereview`]"
               style="display: flex;flex-direction: row;">
            <div style="width:calc(100% - 250px)" class="shenpi">
              <el-input style="width:100%" type="text" v-model="entityForm.reviewMessage" placeholder="请输入审批意见" />
            </div>
            <div style="width:250px">
              <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                         @click="passfn(entityForm.id,entityForm.reviewMessage,entityForm.curFlowCode)">
                审核通过
              </el-button>
              <el-button @click="rejectfn(entityForm.id,entityForm.reviewMessage,entityForm.curFlowCode)"
                         class="dialog-footer-btns" style="width:100px">
                拒绝
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/finance/settleRecvFreight'
import { validatePhone } from '@/utils/validate'
import { settleRecvOption } from '@/const'
import productModel from '@/model/product/productList'
import { createMemoryField } from '@/utils/index'
export default {
  name: 'settleRecvFreight',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'settleRecvFreight',
      model: Model,
      addressValue: '',
      screen: { full: false },
      entityForm: { ...Model.model },
      nameEntity: { options: [], active: '' },
      carrier: { options: [], active: '' },
      limitNum: 50,
      username: 'ss',
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      filterOption: { ...settleRecvOption },
      memoryEntity: { fields: {}, triggered: false },
      rules: {
        applyDate: { required: true, message: '请选择日期', trigger: 'blur' },
        carrier: { required: true, message: '请选择承运商', trigger: 'blur' }
      },
      isshow: false,
      reviewLogList: [],

      countList: [],
      isdisabled: false,
      optionsDisable: {},

      steplist: [],
      steplistNEW: [],
      rightListLoading: false,
      leftListLoading: false,
      dataleftlist: [],
      datarighttlist: [],
      addUserList: [],
      delUserList: [],

      isCustomerchange: false,
      totalFreightRight: null,
      totalFreightLeft: null,
      addFreightList: [],
      delFreightList: [],
      customerName: '',
      cargoType: '',
      receiverNameEntity: { value: '', options: [] },
      FreightrightListLoading: false,
      SettlementFreightdate: [],
      dataFreightleftlist: [],
      dataFreightrighttlist: [],
      FreightleftListLoading: false,
      idList: []
      // dates: []
    }
  },
  watch: {
    'carrier.active'(name) {
      if (!name) return
      const item = this.carrier.options.find((item) => item.name === name)
      if (item) {
        var str = this.entityForm.applyDate
        var arr = str.split('-')
        var currentdate = arr[0] + arr[1] + arr[2] + '雅达迅-' + item.name
        this.entityForm.carriageSettleRecvCode = currentdate
        this.carrier.active = item.name
        this.entityForm.carrier = item.name
      }
    },

    activetype: function (newV, oldV) {
      this.getlist(newV)
      // this.getnumber()
    }
  },
  created() {
    this.permsActionbtn(this.curd)
    this.setbuttomlistV(this.curd)
    this.getName()
    this.getCarrier()
    this.activetype = ''
    this.memoryEntity.fields = createMemoryField({
      fields: ['applyDate', 'productCode', 'productId'],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })

    this.getnumber()
    if (this.$route.params.type) {
      this.activetype = this.$route.params.type
      if (this.$route.params.id) {
        this.handleUpdate(this.$route.params.id, 'review', 'SETTLE_RECV_FREIGHT')
      }
    } else {
      this.activetype = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
      this.actionList.forEach((val, index) => {
        if (val.type == this.activetype) {
          val.active = true
        } else {
          val.active = false
        }
      })
    }

    this.isdisabled = false
    this.optionsDisable = {
      disabledDate(time) {
        return time.getTime() > Date.now()
      }
    }
    // this.getFreightLeftData()
    this.getsenderListfn()
  },

  computed: {},
  methods: {
    handleFullScreen() {
      this.screen.full = !this.screen.full
      const el = this.$refs.dialogStatus.$el
      const el_body = el.querySelector('.el-dialog__body')
      el_body.style.height = this.screen.full ? '85vh' : '70vh'
    },
    totalOutPrice(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        const values = data.map((item) => Number(item[column.property]))
        if (
          column.property != 'remarks' &&
          column.property != 'supplierName' &&
          column.property != 'customerName' &&
          column.property != 'cargoType' &&
          column.property != 'date' &&
          column.property != 'caozuo'
        ) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[index] = sums[index].toFixed(2)
        }
      })
      return sums
    },

    totalOutPriceV(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        const values = data.map((item) => Number(item[column.property]))
        if (column.property == 'originalTon' || column.property == 'actualSettleWeight') {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[index] = sums[index].toFixed(2)
        }
      })
      return sums
    },

    handleChange(index, row) {
      //修改运费数据中的备注
      let arry = this.entityForm.settleRecvFreightList
      for (var i = 0; i < arry.length; i++) {
        if (i === index) {
          arry[i].remarks = row.remarks
        }
      }
    },
    handdetele(indexV, row) {
      //将选中的列表勾选回去
      this.entityForm.settleRecvFreightList = this.entityForm.settleRecvFreightList.filter(function (item, index) {
        return index !== indexV
      })
    },
    //点击状态获取数据
    async getlist(newV) {
      this.activetype = newV
      this.actionList.forEach((val, index) => {
        if (val.type == newV) {
          val.active = true
        } else {
          val.active = false
        }
      })
      this.filtersSeach.filter_INS_state = newV
      this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    },
    async getsenderListfn() {
      //获取收货单位列表
      const res = await this.model.getfindBuyAndSupplier()
      if (res.data.length) this.receiverNameEntity.options = res.data
    },
    changereceiverName(val) {
      this.entityForm.customerName = val
    },
    // start运费明细
    /*
     * 获取(左侧的全部运费明细列表)
     * */
    async getFreightLeftData(type) {
      console.log(this.entityForm)
      if (this.entityForm.carrier) {
        this.FreightleftListLoading = true
        const res = await this.model.FreightFindBySettleRecv({ ...this.entityForm })
        this.FreightleftListLoading = false
        if (res) {
          this.dataFreightleftlist = res.data
          this.totalFreightLeft = res.data.length || 0
        }
      } else {
        if (type === 'qurey') {
          this.$message({ type: 'warning', message: '请选择承运商!' })
        }
      }
    },

    handleAddSelectionChangeFreightLeft(val) {
      if (val) {
        this.addFreightList = val.map((v) => {
          v.settlePayId = this.entityForm.id
          v.applyPayId = v.id
          return v
        })
      }
    },
    cleanup() {
      this.dataFreightrighttlist = []
    },
    savedata() {
      if (this.idList.length > 0) {
        this.findCarriageSettlePaySummaryfn()
        this.getDebitNote()
      } else {
        this.$message({ message: '请选择', type: 'warning' })
      }
    },
    async saveFreightList(type) {
      let idList = []
      if (type === 'add') {
        let Things = this.addFreightList
        for (var i = 0; i < Things.length; i++) {
          idList.push(Things[i].id)
          // this.dates.push(new Date(Things[i].date))
        }
        this.dataFreightrighttlist = this.concatArr(this.addFreightList, this.dataFreightrighttlist)
        this.idList = idList
      } else {
        let arr = this.delFreightList
        let arr2 = this.addFreightList
        let arr3 = this.idList
        for (let i = 0; i < arr.length; i++) {
          for (let j = 0; j < arr2.length; j++) {
            if (arr[i].id === arr2[j].id) {
              arr2.splice(j, 1)
              arr3.splice(j, 1)
              break
            }
          }
        }
        this.dataFreightrighttlist = this.addFreightList
      }
    },
    concatArr(arrA, arrB) {
      // 只需要拿到A，再拿到B数组中不在a数组的数据
      let idListA = arrA.map((i) => i.id)
      let target = arrA
      let tempB = arrB.filter((val) => !idListA.includes(val.id))
      target = target.concat(tempB)
      return target
    },

    newDate(time) {
      var date = new Date(time)
      var y = date.getFullYear()
      var m = date.getMonth() + 1
      m = m < 10 ? '0' + m : m
      var d = date.getDate()
      d = d < 10 ? '0' + d : d
      return y + '-' + m + '-' + d
    },
    //  获取运费数据的列表
    async findCarriageSettlePaySummaryfn() {
      let parm = {
        idList: this.idList,
        settleRecvFreightSummaryDtoList: this.entityForm.settleRecvFreightList
      }
      const res = await this.model.generateSettleRecvFreightSummary({ ...parm })
      // console.log(res.data)
      // let truckCount = 0
      // let originalTon = 0
      // let actualSettleWeight = 0
      // let settleMoney = 0
      // let deductMoney = 0
      // let obj = {}
      // for (var i = 0; i < res.data.length; i++) {
      //   obj.total = '合计'
      //   truckCount += Number(res.data[i].truckCount)
      //   originalTon += Number(res.data[i].originalTon)
      //   actualSettleWeight += Number(res.data[i].actualSettleWeight)
      //   settleMoney += Number(res.data[i].settleMoney)
      //   deductMoney += Number(res.data[i].deductMoney)
      //   obj.truckCount = truckCount
      //   obj.originalTon = originalTon
      //   obj.actualSettleWeight = actualSettleWeight
      //   obj.settleMoney = settleMoney
      //   obj.deductMoney = deductMoney
      // }
      // console.log(obj)
      // res.data.push(obj)
      this.entityForm.settleRecvFreightList = res.data

      // if (this.entityForm.settleRecvFreightList.length > 0) {
      //   console.log('有数据')
      //   let newarry = this.entityForm.settleRecvFreightList
      //   for (var i = 0; i < res.data.length; i++) {
      //     for (var j = 0; j < newarry.length; j++) {
      //       if (
      //         res.data[i].supplierName === newarry[j].supplierName &&
      //         res.data[i].customerName === newarry[j].customerName &&
      //         res.data[i].cargoType === newarry[j].cargoType &&
      //         res.data[i].freightPrice === newarry[j].freightPrice
      //       ) {
      //         console.log('符合条件')
      //         newarry[j].truckCount += Number(res.data[i].truckCount)
      //         newarry[j].originalTon += Number(res.data[i].originalTon)
      //         newarry[j].actualSettleWeight += Number(res.data[i].actualSettleWeight)
      //         newarry[j].settleMoney += Number(res.data[i].settleMoney)

      //         // let obj = {}
      //         // obj.total = '合计'
      //         // obj.truckCount += Number(res.data[i].truckCount)
      //         // obj.originalTon += Number(res.data[i].originalTon)
      //         // obj.actualSettleWeight += Number(res.data[i].actualSettleWeight)
      //         // obj.settleMoney += Number(res.data[i].settleMoney)

      //         // newarry.push(obj)
      //       } else {
      //         console.log('不符合条件')
      //         newarry.push(res.data[i])
      //         // let obj = {}
      //         // obj.total = '合计'
      //         // obj.truckCount += Number(res.data[i].truckCount)
      //         // obj.originalTon += Number(res.data[i].originalTon)
      //         // obj.actualSettleWeight += Number(res.data[i].actualSettleWeight)
      //         // obj.settleMoney += Number(res.data[i].settleMoney)
      //         // newarry.push(obj)
      //       }
      //     }
      //   }
      // } else if (this.entityForm.settleRecvFreightList.length < 1) {
      //   console.log('没数据')
      //   // let sum = res.data.reduce((prev, cur) => {
      //   //   if (prev && prev.length) {
      //   //     let result = prev.some((item) => {
      //   //       return (
      //   //         item.supplierName == cur.supplierName &&
      //   //         item.customerName == cur.customerName &&
      //   //         item.cargoType == cur.cargoType &&
      //   //         item.freightPrice == cur.freightPrice
      //   //       )
      //   //     })
      //   //     if (result) {
      //   //       prev.forEach((item) => {
      //   //         if (
      //   //           item.supplierName == cur.supplierName &&
      //   //           item.customerName == cur.customerName &&
      //   //           item.cargoType == cur.cargoType &&
      //   //           item.freightPrice == cur.freightPrice
      //   //         ) {
      //   //           item.truckCount += cur.truckCount
      //   //           item.originalTon += cur.originalTon
      //   //           item.actualSettleWeight += cur.actualSettleWeight
      //   //           item.settleMoney += cur.settleMoney
      //   //         }
      //   //       })
      //   //     } else {
      //   //       prev.push(cur)
      //   //     }
      //   //   } else {
      //   //     prev.push(cur)
      //   //   }
      //   //   return prev
      //   // }, [])
      //   // console.log('sum:', sum)
      //   this.entityForm.settleRecvFreightList = res.data
      // }

      // this.dataFreightrighttlist.forEach((item) => {
      //   item.maxDate = maxdate
      //   item.minDate = mindate
      //   item.date = mindate + '~' + maxdate
      //   const index = this.entityForm.settleRecvFreightList.findIndex(
      //     (_item) =>
      //       _item.supplierName === item.supplierName &&
      //       _item.customerName === item.customerName &&
      //       _item.cargoType === item.cargoType &&
      //       _item.freightPrice === item.freightPrice
      //   )
      //   if (index > -1) {
      //     this.entityForm.settleRecvFreightList[index].truckCount++
      //     this.entityForm.settleRecvFreightList[index].originalTon += Number(item.originalTon)
      //     this.entityForm.settleRecvFreightList[index].actualSettleWeight += Number(item.actualSettleWeight)
      //     this.entityForm.settleRecvFreightList[index].settleMoney += Number(item.settleMoney)
      //   } else {
      //     this.entityForm.settleRecvFreightList.push(item)
      //     this.entityForm.settleRecvFreightList[this.entityForm.settleRecvFreightList.length - 1].truckCount = 1
      //   }
      // })
    },
    //获取扣款单列表
    async getDebitNote() {
      let parm = {
        idList: this.idList,
        settleRecvFreightSummaryDtoList: this.entityForm.settleRecvFreightList
      }
      const res = await this.model.getDebitNotelist({ ...parm })
      this.entityForm.weightHouseLogList = res.data
    },

    formatDate(timeStamp) {
      const date = new Date(timeStamp) // 创建Date对象
      // const year = date.getFullYear() // 获取年份
      // const month = date.getMonth() + 1 // 获取月份，记得+1
      // const day = date.getDate() // 获取日期

      const year = date.getFullYear()
      const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
      const day = date.getDate()

      return `${year}-${month}-${day}` // 返回格式化后的日期字符串
    },
    changeYY(e) {
      if (e) {
        this.SettlementFreightdate = e
        this.entityForm.beginDate = e[0]
        this.entityForm.endDate = e[1]
      } else {
        this.SettlementFreightdate = []
        this.entityForm.beginDate = ''
        this.entityForm.endDate = ''
      }
      this.getFreightLeftData()
    },
    handleDelSelectionChangeFreight(val) {
      this.delFreightList = val.map((v) => {
        v.settlePayId = this.entityForm.id
        v.applyPayId = v.id
        return v
      })
    },

    async lookbtn(item) {
      const { data } = await this.model.findSettleRecvFreightFlow(item.id)
      data.forEach((element, index) => {
        element.flowDesignList.forEach((value, indevc) => {
          value.active = false
          if (element.curFlowCode == value.code) {
            var citrus = element.flowDesignList
            citrus.forEach((value) => {
              value.active = true
            })
          }
        })
      })
      this.steplistNEW = data
    },
    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },
    // 财务管理   //财务报表付款申请
    async SubmitReview(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        // let narry = this.entityForm.weightHouseLogList
        // for (var i = 0; i < narry.length; i++) {
        //   narry[i].roadCost == 0.3
        // }
        const res = await this.model.getApproved(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '提交成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.getnumber()
          this.resetVariant()
          this.dialogFormVisible = false
        }
      }
    },
    //保存草稿
    async SaveDraft(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        // let narry = this.entityForm.settleRecvFreightList
        // for (var i = 0; i < narry.length; i++) {
        //   if (narry[i].total === '合计') {
        //     this.entityForm.settleRecvFreightList.pop()
        //   }
        // }
        const res = await this.model.SaveDraftfn(this.entityForm)
        if (res) {
          this.$message({ type: 'success', message: '保存草稿成功!' })
          this.entityForm = { ...this.model.model }
          this.getList()
          this.getnumber()
          this.resetVariant()
          this.dialogFormVisible = false
        }
      }
    },
    async getnumber() {
      const res = await this.model.getcountByState()
      this.countList = res.data
    },
    // 打印
    async handlePrint(row) {
      // this.isPrinting = true
      const { data } = await this.model.getprint(row.id)
      let objectUrl = window.URL.createObjectURL(new Blob([data], { type: 'application/pdf' }))
      window.open(objectUrl)
    },
    // 打印扣款单
    async handlekdPrint(row) {
      const { data } = await this.model.getkkprint(row.id)
      let objectUrl = window.URL.createObjectURL(new Blob([data], { type: 'application/pdf' }))
      window.open(objectUrl)
    },

    // 下载
    async handDownload(row) {
      const { data } = await this.model.getdownload(row.id)
      let url = window.URL.createObjectURL(new Blob([data]))
      let a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      let title = '销售运费结算.xls'
      a.setAttribute('download', title)
      document.body.appendChild(a)
      a.click() //执行下载
      window.URL.revokeObjectURL(a.href) // 清除url
      document.body.removeChild(a) //移除dom
    },

    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      this.isdisabled = false
      this.isCustomerchange = false
      this.reviewLogList = []
      this.steplist = []
      this.dataFreightrighttlist = []
      this.entityForm.settleRecvFreightList = []
      this.entityForm.weightHouseLogList = []
      this.carrier.active = ''
      this.entityForm.customerName = ''
      this.dataFreightleftlist = []
      this.totalFreightLeft = []
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.getList()
    },

    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) {}
    },

    async getCarrier() {
      try {
        let { data } = await this.model.getfindDistinctCarrierList()
        this.carrier.options = data.map((item) => {
          return { name: item, id: item, code: item }
        })
      } catch (error) {}
    },

    handleNameEntity(val) {
      this.nameEntity.active = val
      this.entityForm = { ...this.entityForm, cargoType: val }
    },
    handleCarrierEntity(val) {
      this.carrier.active = val
      // this.entityForm.carrier = val
      this.entityForm = { ...this.entityForm, carrier: val }
    },
    handleNameEntityV() {
      this.isCustomerchange = true
      this.isdisabled = true
    },

    // 累计接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    computeWayCostAcc() {
      let { receiveWeightAcc, sendWeightAcc } = this.entityForm
      receiveWeightAcc = receiveWeightAcc * 1
      sendWeightAcc = sendWeightAcc * 1
      let wayCostAcc = (((sendWeightAcc - receiveWeightAcc) / sendWeightAcc) * 100).toFixed(2) * 1
      this.entityForm.wayCostAcc = !Number.isNaN(wayCostAcc) ? wayCostAcc : 0
    },
    /**
     * @用于格式化合约数据因为合约的子customerId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },

    async handleUpdate(item, type, gettype) {
      console.log(type)
      this.SettlementFreightdate = []
      if (typeof item == 'object') {
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        } else if (type == 'update') {
          this.isshow = true
          this.dialogStatus = 'update'
        } else if (type == 'watch') {
          this.isshow = false
          this.dialogStatus = 'watch'
        }
        this.dialogFormVisible = true
        const { data } = await this.model.getUploadList(item.id)
        this.entityForm = { ...data }
        this.carrier.active = data.carrier
        console.log(this.entityForm)
        if (type == 'update' || this.dialogStatus == 'create') {
          this.getFreightLeftData()
        }

        // let settleRecvFreightList = this.entityForm.settleRecvFreightList
        // let truckCount = 0
        // let originalTon = 0
        // let actualSettleWeight = 0
        // let settleMoney = 0
        // let deductMoney = 0
        // let obj = {}
        // for (var i = 0; i < settleRecvFreightList.length; i++) {
        //   obj.total = '合计'
        //   truckCount += Number(settleRecvFreightList[i].truckCount)
        //   originalTon += Number(settleRecvFreightList[i].originalTon)
        //   actualSettleWeight += Number(settleRecvFreightList[i].actualSettleWeight)
        //   settleMoney += Number(settleRecvFreightList[i].settleMoney)
        //   deductMoney += Number(settleRecvFreightList[i].deductMoney)
        //   obj.truckCount = truckCount
        //   obj.originalTon = originalTon
        //   obj.actualSettleWeight = actualSettleWeight
        //   obj.settleMoney = settleMoney
        //   obj.deductMoney = deductMoney
        // }
        // settleRecvFreightList.push(obj)
        // this.entityForm.settleRecvFreightList = settleRecvFreightList

        if (gettype) {
          const { data } = await Model.getlclist({ type: gettype })
          this.entityForm.stepcode = this.entityForm.curFlowCode
          this.entityForm.stepName = this.entityForm.curFlowName
          data.forEach((element, index) => {
            element.active = false
            if (element.code === this.entityForm.stepcode) {
              var citrus = data
              citrus.forEach((value) => {
                value.active = true
              })
            }
          })
          this.steplist = data
        }
        this.reviewLogList = data.reviewLogList
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((item, index) => {
            newarry.push(item.reviewMessage)
          })
          this.entityForm.reviewMessage = ''
        }
      } else if (typeof item == 'string') {
        if (type == 'review') {
          this.isshow = true
          this.dialogStatus = 'review'
        }
        this.dialogFormVisible = true
        const { data } = await this.model.getUploadList(item)
        this.reviewLogList = data.reviewLogList
        this.entityForm = { ...data }
        this.carrier.active = data.carrier

        if (type == 'update' || this.dialogStatus == 'create') {
          this.getFreightLeftData()
        }
        // let settleRecvFreightList = this.entityForm.settleRecvFreightList
        // let truckCount = 0
        // let originalTon = 0
        // let actualSettleWeight = 0
        // let settleMoney = 0
        // let deductMoney = 0
        // let obj = {}
        // for (var i = 0; i < settleRecvFreightList.length; i++) {
        //   obj.total = '合计'
        //   truckCount += Number(settleRecvFreightList[i].truckCount)
        //   originalTon += Number(settleRecvFreightList[i].originalTon)
        //   actualSettleWeight += Number(settleRecvFreightList[i].actualSettleWeight)
        //   settleMoney += Number(settleRecvFreightList[i].settleMoney)
        //   deductMoney += Number(settleRecvFreightList[i].deductMoney)
        //   obj.truckCount = truckCount
        //   obj.originalTon = originalTon
        //   obj.actualSettleWeight = actualSettleWeight
        //   obj.settleMoney = settleMoney
        //   obj.deductMoney = deductMoney
        // }
        // settleRecvFreightList.push(obj)
        // this.entityForm.settleRecvFreightList = settleRecvFreightList

        if (gettype) {
          const { data } = await Model.getlclist({ type: gettype })
          this.entityForm.stepcode = this.entityForm.curFlowCode
          this.entityForm.stepName = this.entityForm.curFlowName
          data.forEach((element, index) => {
            element.active = false
            if (element.code === this.entityForm.stepcode) {
              var citrus = data
              citrus.forEach((value) => {
                value.active = true
              })
            }
          })
          this.steplist = data
        }
        let newarry = []
        if (data.reviewLogList.length > 0) {
          data.reviewLogList.forEach((itemv, index) => {
            newarry.push(itemv.reviewMessage)
          })
          this.entityForm.reviewMessage = newarry.toString()
        }
      }
    },
    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    // 更新累计Acc
    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      const paramsAcc = await this.getAccValues({ date, contractId })
      Object.keys(paramsAcc).forEach((key) => {
        this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
        this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      })
      this.computeWayCostAcc()
    },

    async passfn(id, reviewMessage, curFlowCode) {
      // if (state == 'NEW') {
      const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage, curFlowCode: curFlowCode })
      // } else if (state == 'PASS') {
      //   const res = await this.model.getfinancePass({ id: id, reviewMessage: reviewMessage })
      // }
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
      this.resetVariant()
      this.getlist(this.activetype)
      this.getnumber()
    },

    async rejectfn(id, reviewMessage, curFlowCode) {
      const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage, curFlowCode: curFlowCode })
      this.getnumber()
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>

<style>
.search-component >>> .el-input__inner {
  border: none;
}
</style>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.dialog-footer {
  margin-right: 44px;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px #f1f1f2;
  padding: 10px;
  &-all {
    font-size: 14px;
    margin-left: 34px;
    // margin-right: auto;
    flex: 0 0 58px;
    color: #ff9639;
    background: #fff;
    border: 1px solid #ff9639;
    &:hover {
      background: transparent;
    }
    &:nth-of-type(1) {
      // margin-left: 15px;
      margin-right: auto;
    }
  }
  &-btns {
    width: 85px;
    height: 35px;
    border: 1px solid #2f79e8;
    background: #fff;
    color: #2f79e8;
    margin-left: 20px;
    font-size: 14px;
    &:last-of-type {
      border: 1px solid #d5d5d5;
      color: #747474;
    }
  }
}
::v-deep .el-dialog {
  width: 95%;
}
// 合计行样式
.tstablebox {
  width: 100%;
  ::v-deep .el-table__header-wrapper {
    position: absolute;
    z-index: 9;
  }
  ::v-deep .el-table__body-wrapper {
    padding-top: 40px;
  }
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
}
.colorBLUE {
  color: #2f79e8 !important;
}
.bordercolorBLUE {
  border: solid 1px #2f79e8 !important;
}
.bzminboxV:first-child {
  width: 50% !important;
  .lin {
    width: 0px !important;
    display: none;
  }
  .context {
    width: 100% !important;
  }
}
.bzboxV {
  width: 100%;
  display: flex;
  font-size: 12px;
  .bzminboxV {
    width: 100%;
    display: flex;
    .icon {
      font-size: 30px;
    }
    .context {
      width: 50%;
      color: #ccc;
      text-align: center;
    }
    .lin {
      width: 50%;
      height: 1px;
      border: solid 1px #606266;
      margin-top: 15px;
    }
  }
}
.bzminbox:first-child {
  width: 50% !important;
  .lin {
    width: 0px !important;
    display: none;
  }
  .context {
    width: 100% !important;
  }
}
.bzbox {
  width: 100%;
  display: flex;
  font-size: 12px;
  .bzminbox {
    width: 100%;
    display: flex;
    .icon {
      font-size: 30px;
    }
    .context {
      width: 50%;
      color: #ccc;
      text-align: center;
    }
    .lin {
      width: 50%;
      height: 1px;
      border: solid 1px #606266;
      margin-top: 15px;
    }
  }
}

::v-deep .el-date-editor .el-range-separator {
  padding: 0 0;
}
::v-deep .el-form-item__label {
  font-weight: 400;
}

.smart-search {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  width: 47%;
  margin-bottom: 5px;
  border-radius: 3px;

  .prepend {
    background-color: #f5f7fa;
    padding-right: 10px;
    border-right: solid 1px #dcdfe6;
    .el-dropdown {
      cursor: pointer;
    }

    .el-icon-arrow-down {
      width: 26px;
      text-align: center;
    }

    .search-key {
      display: inline-block;
      height: 30px;
      line-height: 30px;
    }
  }

  .search-component {
    flex: 1;
    .el-input {
      width: 100%;
      height: 30px;
      line-height: 30px;
      .el-input__inner {
        border: none;
      }
      & .is-focus {
        .el-input__inner {
          border-color: #dcdfe6 !important;
        }
      }
    }
  }
}
.form-layoutV {
  .el-row {
    margin-bottom: 10px;
  }
}
.tipbox {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  .tipline {
    margin-left: 20px;
    .redtext {
      font-size: 1.5rem;
      color: red;
    }
  }
}

.contentbox {
  width: 500px;
  margin: 100px auto 0 auto;
  font-size: 12px;
  .line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tsline {
    float: right;
    margin-top: 40px;
  }
  .inputbox {
    width: 100px;
    margin-bottom: 10px;
    border-bottom: solid 1px #ccc;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
<style scoped>
/* .inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
} */
</style>