<template>
  <div class="app-container">
    <!-- <el-date-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate" placeholder="选择日期"
                    :clearable="false" /> -->
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" :buttomlist="buttomlist" />
    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :statetype="activetype" :buttomlist="buttomlist"
            :countList="countList" title="供应商" otherHeight="80" :actionList="actionList">

      <el-table-column label="类型" slot="type" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.type =='SELL'">销售</span>
          <span v-if="scope.row.type =='BUY'">采购</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" slot="state" fixed='left'>
        <template slot-scope="scope">
          <!-- 通用审核状态 NEW-待审核、PASS-通过、REJECT-拒绝 -->
          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :(scope.row.state == 'PAYED' ? 'success' : 'info')))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '待审核' : (scope.row.state == 'PASS' ? '已审核' : (scope.row.state == 'REJECT' ? '拒绝':(scope.row.state == 'PAYED' ? '已支付' : '')))) }}
            <!-- PAYED -->
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" max-width="130" width="130" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">

              <div v-if="perms[`${curd}:update`] || false">
                <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT'" color="#FF9639"
                        style="height:26px;line-height:26px;width:60px" @click="handleUpdate(scope.row,'update')">
                  编辑
                </el-tag>
              </div>

              <!-- <div v-if="perms[`${curd}:waitforreviewed`] || false"> -->
              <div v-if="perms[`${curd}:isnoticePr`] || false">
                <el-tag class="opt-btn" style="margin-top:5px;height:26px;line-height:26px;width:60px"
                        v-if="scope.row.state == 'NEW'" color="#FF726B" @click="handleUpdate(scope.row,'review')">去 审 核
                </el-tag>
              </div>
              <view v-if="scope.row.state == 'PASS' || scope.row.state=='DRAFT,REJECT'">
                <el-tag class="opt-btn" style="margin-top:5px;height:26px;line-height:26px;width:60px" color="#FF9639"
                        @click="handleUpdate(scope.row,'watch')">
                  查看</el-tag>
              </view>
              <div v-if="perms[`${curd}:send`] || false">
                <el-tag class="opt-btn" style="margin-top:5px;height:26px;line-height:26px;width:60px"
                        v-if="scope.row.state == 'PASS' " color="#FF9639" @click="SendSMS(scope.row,'Send')">
                  发送短信</el-tag>
              </div>

              <el-tag class="opt-btn" v-if="scope.row.state == 'PAYED'" color="#33CAD9" @click="noticePoundroom(scope.row)">通知磅房
              </el-tag>

              <div v-if="perms[`${curd}:delete`] || false">
                <!-- effect="plain" -->
                <el-tag class="opt-btn" style="cursor: pointer;width:60px;height:26px;line-height:26px;" color="#2f79e8"
                        v-if="scope.row.state == 'PAYED' || scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'"
                        @click="handleDel(scope.row)">删 除
                </el-tag>
              </div>
            </div>
          </div>
          <!-- perms[`${curd}:delete`]||false && -->
        </template>
      </el-table-column>
    </s-curd>

    <!-- 通知磅房 -->
    <!-- <el-dialog width="880px" top="5vh" title="通知磅房" :visible.sync="dialogVisible" :before-close="handleClose"
               :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="noticeForm" :model="noticeForm" label-width="90px"
               label-position="top" v-if="dialogVisible">
        <el-row>
          <el-col>
            <div class="form-title">短信通知内容</div>
            <div class="form-layout">
              <div class="contentbox">
                <div class="line">
                  <div>准予</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>从</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>年</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>月</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>日起</div>
                </div>

                <div class="line">
                  <div>供应</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>煤</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>吨</div>
                </div>

                <div class="line">
                  <div>硫</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>灰</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>指数</div>
                </div>

                <div class="line">
                  <div>挥发份</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>回收</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                  <div>单价</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" clearable />
                  </div>
                </div>

                <div class="line tsline">
                  <div>签字人</div>
                  <div class="inputbox">
                    <el-input v-model="username" style="border:none" />
                  </div>
                </div>

              </div>

            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="dialog-footer-btns" style="width:100px" :loading="entityFormLoading">
          保存并审核
        </el-button>
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="send('noticeForm')">发送短信
        </el-button>
      </div>
    </el-dialog> -->

    <el-dialog class="addV" width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
          <el-col style="margin-bottom: -13px">
            <el-form-item label="步骤" prop="reviewLogList">
              <el-steps :active="reviewLogList.length">
                <el-step v-for="(item,index) in reviewLogList" :key="index"
                         :title="item.reviewResult + ' ( 审批意见：'+item.reviewMessage+' )'"
                         :description="item.createDate+' '+item.reviewUserName">
                </el-step>
              </el-steps>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col style="margin:30px 0 10px 0;padding:0 15px">
            <el-tabs v-model="entityForm.type" @tab-click="changetab">
              <el-tab-pane label="销售" name="SELL"></el-tab-pane>
              <el-tab-pane label="购进" name="BUY"></el-tab-pane>
            </el-tabs>
          </el-col>
          <el-col>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="beginDate">
                    <date-select v-model="entityForm.beginDate" clearable />
                  </el-form-item>
                </el-col>
                <el-col v-if="entityForm.type=='BUY'">
                  <el-form-item label="购进合同" prop="contractId">
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 filterable :disabled="dialogStatus==='update'" clearable placeholder="请选择合同"
                                 style="width:100%" />
                  </el-form-item>
                </el-col>

                <el-col v-if="entityForm.type=='SELL'">
                  <el-form-item label="销售合同" prop="contractId">
                    <el-cascader v-model="contractEntityV.active" :options="contractEntityV.options"
                                 :props="contractEntityV.props" filterable :disabled="dialogStatus==='update'" clearable
                                 placeholder="请选择合同" style="width:100%" />
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                 disabled />
                  </el-form-item>
                </el-col>
                <el-col v-if="entityForm.type=='SELL'">
                  <el-form-item label="客户" prop="otherName">
                    <el-select v-model="customerEntity.active" placeholder="请选择客户" clearable @change="handleCustomer"
                               style="width:100%" disabled>
                      <el-option v-for="item in customerEntity.options" :key="item.value" :label="item.label" :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col v-if="entityForm.type=='BUY'">
                  <el-form-item label="供应商" prop="otherId">
                    <el-select v-model="supplierEntity.value" placeholder="请选择供应商" clearable filterable style="width:100%"
                               @change="handleSupplier" disabled>
                      <el-option v-for="item in supplierEntity.options" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <!-- <el-col>
                  <el-form-item label="煤种" prop="coalType">
                    <category-select :value.sync="entityForm.coalType" />
                  </el-form-item>
                </el-col> -->
                <el-col>
                  <el-form-item label="硫St,d" prop="cleanStd">
                    <el-input v-model="entityForm.cleanStd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="吨数" prop="amount">
                    <el-input v-model="entityForm.amount" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="单价" prop="price">
                    <el-input v-model="entityForm.price" clearable oninput="value=value.replace(/[^0-9.]/g,'')" />
                  </el-form-item>
                </el-col>
                <el-col> </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="灰Ad" prop="cleanAd">
                    <el-input v-model="entityForm.cleanAd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="指数" prop="procG">
                    <el-input v-model="entityForm.procG" clearable oninput="value=value.replace(/[^0-9.]/g,'')" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="挥发分Vdaf" prop="cleanVdaf">
                    <el-input v-model="entityForm.cleanVdaf" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="回收" prop="recover">
                    <el-input v-model="entityForm.recover" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="付款金额" prop="money">
                    <el-input v-model="entityForm.money" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="开户银行" prop="bank" label-width="140px">
                    <el-input v-model="entityForm.bank" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row> -->
              <!-- <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="支付方式" prop="payWay">
                    <el-input v-model="entityForm.payWay" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="账号或卡号" prop="cardNo">
                    <el-input v-model="entityForm.cardNo" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable />
                  </el-form-item>
                </el-col>
              </el-row> -->

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="reviewMessage">
                    <el-input type="textarea" v-model="entityForm.reviewMessage" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
                <el-col>
                  <el-form-item label="步骤" prop="reviewLogList">
                    <el-steps :active="reviewLogList.length">
                      <el-step v-for="(item,index) in reviewLogList" :key="index" :title="item.reviewResult"
                               :description="item.createDate+' '+item.reviewUserName">
                      </el-step>
                    </el-steps>
                  </el-form-item>
                </el-col>
              </el-row> -->
            </div>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer" v-if="dialogStatus =='review'" style="width:100%">
        <div style="width:calc(100% - 250px)" class="shenpi">
          <el-input style="width:100%;" type="text" v-model="entityForm.reviewMessage" placeholder="请输入审批意见" />
        </div>

        <div style="width:250px">
          <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="passfn(entityForm.id,entityForm.reviewMessage)">
            审核通过
          </el-button>
          <el-button v-if="dialogStatus == 'review' " @click="rejectfn(entityForm.id,entityForm.reviewMessage)"
                     class="dialog-footer-btns">拒绝
          </el-button>
        </div>
      </div>

      <div slot="footer" class="dialog-footer" v-else>
        <el-button v-if="dialogStatus == 'create' || dialogStatus == 'update'" type="primary" class="dialog-footer-btns"
                   :loading="entityFormLoading" @click="SubmitReview('entityForm')">
          提交审核
        </el-button>
        <el-button v-if="dialogStatus == 'create' || dialogStatus == 'update'" @click="SaveDraft('entityForm')"
                   class="dialog-footer-btns">
          保存草稿
        </el-button>

      </div>

    </el-dialog>

    <el-dialog class="addV" width="880px" top="5vh" ref="dialogStatusV" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisibleV" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="senfForm" :model="senfForm" label-width="90px" :rules="senfFormrules"
               label-position="top" v-if="dialogFormVisibleV">
        <el-row>
          <el-col>
            <div class="form-layout" style="margin-top:30px">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="手机号码" prop="Telephone">
                    <el-input v-model="senfForm.Telephone" clearable oninput="value=value.replace(/[^0-9.]/g,'')" />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="短信内容" prop="SMScontent">
                    <el-input type="textarea" v-model="senfForm.SMScontent" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                   @click="SendSMSbtn(senfForm.Telephone,senfForm.SMScontent)">
          发送短信
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/finance/weightHouseNotice'
import { validatePhone } from '@/utils/validate'
import { weightHouseNoticeOption } from '@/const'
import { contractList, getCustomerContract } from '@/api/quality'
import productModel from '@/model/product/productList'
import { chooseContract } from '@/api/quality'
import { createMemoryField } from '@/utils/index'
import CustomerModel from '@/model/user/customer'
import { forEach } from 'jszip/lib/object'
// import Cache from '@/utils/cache'
export default {
  name: 'weightHouseNotice',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'weightHouseNotice',
      model: Model,
      addressValue: '',
      entityForm: { ...Model.model },
      senfForm: {
        id: '',
        SMScontent: ''
      },
      noticeForm: {},
      nameEntity: { options: [], active: '' },
      supplierEntity: { value: '', options: [] },
      uploadData: { refId: '', refType: 'ContractSell' },
      limitNum: 50,
      username: 'ss',
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      filterOption: { ...weightHouseNoticeOption },
      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        phone: [{ validator: validateIsPhone, trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      rules: {
        beginDate: { required: true, message: '请输入日期', trigger: 'blur' },
        // price: { required: true, message: '请输入金额', trigger: 'blur' },
        otherId: { required: true, message: '请输入收款单位', trigger: 'blur' },
        productName: { required: true, message: '请选择品名', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' }
      },
      senfFormrules: {
        Telephone: [{ validator: validateIsPhone, trigger: 'blur' }],
        SMScontent: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      // 合同
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      contractEntityV: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },

      customerEntity: { options: [], active: [] },
      isshow: false,
      emptyImgPath: require(`@/assets/empty.jpg`),
      reviewLogList: [],
      dialogFormVisibleV: false,
      countList: []
    }
  },
  watch: {
    'entityForm.isPublicBoolean': {
      handler(v) {
        this.entityForm.isPublic = v ? 'Y' : 'N'
      }
    },
    'entityForm.productName'(name) {
      if (name) {
        const item = this.nameEntity.options.find((item) => item.name === name)
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.entityForm.coalType = item.coalCategory || ''
        this.entityForm.coalCategory = item.type
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
        this.entityForm.coalCategory = ''
      }
    },

    'entityForm.contractId'(id) {
      this.updateAcc()
      if (this.entityForm.beginDate && id) {
        this.updateAcc(this.entityForm.beginDate, id)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'review') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      // console.log('购进')
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.supplierId
      this.entityForm.contractName = form.supplierName
      const item = this.contractEntity.options.find((v) => v.supplierId === value[0])
      // entityForm.productName
      if (!item) return
      let name = ''
      item.contractBuyList.forEach((element) => {
        name = element.productName
      })
      this.entityForm.productName = name
      this.entityForm.supplierId = item.supplierId
      this.entityForm.supplierName = item.supplierName
      this.supplierEntity.value = item.supplierId
      this.entityForm.otherId = item.supplierId
      this.entityForm.otherName = item.supplierName
    },

    'contractEntityV.active'(value) {
      if (!value || !Array.isArray(value)) return
      // console.log('销售')
      const form = this.filterContractItemV(value, 'contractEntityV')
      this.entityForm.name = form.productName
      this.entityForm.contractCode = form.customerName
      this.entityForm.contractId = form.customerId
      this.entityForm.receiverName = form.firstParty
      const item = this.contractEntityV.options.find((v) => v.customerId === value[0])
      if (!item) return
      let name = ''
      item.contractSellList.forEach((element) => {
        name = element.productName
      })
      this.entityForm.productName = name
      this.nameEntity.active = name
      this.entityForm.customerName = item.customerName
      this.entityForm.customerId = item.customerId
      this.customerEntity.active = item.customerName
      this.entityForm.otherName = item.customerName
    },
    'entityForm.otherName'(value) {
      if (!value) return
      this.customerEntity.active = value
    },
    'entityForm.otherId'(id) {
      if (!id) return
      this.supplierEntity.value = id
      const item = this.supplierEntity.options.find((item) => item.id === id)
      if (item) {
        this.entityForm.otherName = item.name
        this.entityForm.otherId = id
      }
    },

    // 'supplierEntity.value'(id) {
    //   console.log('发货商家id')
    //   console.log(id)
    //   if (!id) return
    //   const item = this.supplierEntity.options.find((item) => item.id === id)
    //   console.log(item)
    //   if (item) {
    //     this.entityForm.otherName = item.name
    //     this.entityForm.otherId = id
    //   }
    // },
    'entityForm.type'(type) {
      this.entityForm.type = type
    },
    activetype: function (newV, oldV) {
      this.getlist(newV)
    }
    // 'actionList.1.active'(n, o) {
    //   console.log(n)
    // },
  },
  created() {
    // this.permsActionLsit(this.curd)
    this.activetype = ''
    this.permsActionbtn(this.curd)
    this.setbuttomlistV(this.curd)

    this.getName()
    //获取供应商列表
    this.getContractList()
    this.getContract()
    this.getCustomer()

    this.memoryEntity.fields = createMemoryField({
      fields: ['contractId', 'contractCode', 'supplierId', 'supplierName'],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    if (this.$route.params.type) {
      this.activetype = this.$route.params.type
      this.entityForm.id = this.$route.params.id
      this.handleUpdate(this.$route.params.id, 'review')
    } else {
      this.activetype = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
      this.actionList.forEach((val, index) => {
        if (val.type == this.activetype) {
          val.active = true
        } else {
          val.active = false
        }
      })
    }
    this.getnumber()
  },

  methods: {
    async getnumber() {
      const res = await this.model.getcountByState()
      this.countList = res.data
    },
    changetab(tab, event) {
      this.getContract()

      this.$nextTick(() => {
        // 请求成功之后，在这里执行
        this.entityForm = { ...Model.model }
        this.contractEntity.active = []
        this.customerEntity.active = []
        this.contractEntityV.active = []
        this.supplierEntity.value = ''
        this.entityForm.type = tab.name
        this.$forceUpdate()
      })
    },
    handleCustomer({ value, label }) {
      this.entityForm.otherId = value
      this.entityForm.otherName = label
    },

    // 合同改变同步
    handleSupplier({ value, label }) {
      // this.entityForm.otherId = value
      // this.entityForm.otherName = label
      this.entityForm = { ...this.entityForm, otherId: value, otherName: label }
    },

    async getCustomer() {
      try {
        let options = []
        const { data } = await CustomerModel.page({ size: 1000 })
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        this.customerEntity.options = options
      } catch (e) {}
    },
    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogFormVisibleV = false
      this.dialogVisible = false
      this.reviewLogList = []
      this.contractEntity.active = []
      this.customerEntity.active = []
      this.contractEntityV.active = []
      this.supplierEntity.value = ''
      // this.entityForm.type = 'SELL'
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    async getContractList() {
      const { data } = await contractList()
      if (data.length) this.supplierEntity.options = data
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) {}
    },
    // 获取合同选择接口
    async getContract() {
      if (this.entityForm.type == 'BUY') {
        // const { data } = await chooseContract()
        // this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'code', supplierId: 'id' } })
        // console.log(data)

        try {
          const { data } = await chooseContract()
          this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'displayName', supplierId: 'id' } })
        } catch (error) {}
      } else {
        try {
          const { data } = await getCustomerContract()
          // 供应商名称返回id用来查询
          this.contractEntityV.options = this.formatContractV({ data, key: { customerName: 'code', customerId: 'id' } })
        } catch (error) {}
      }
      // try {
      //   const { data } = await chooseContract()
      //   this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'code', supplierId: 'id' } })
      // } catch (error) {}
    },
    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    formatContractV({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    filterContractItemV(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },

    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    SendSMS(item, type) {
      this.dialogFormVisibleV = true
      this.senfForm.id = item.id
      if (type == 'Send') {
        this.dialogStatus = 'Send'
      }
      this.getsendcontent(item.id)
    },
    async getsendcontent(id) {
      const { data } = await this.model.getSms(id)
      this.senfForm.SMScontent = data.smsMessage
    },
    async SendSMSbtn(phone, SMScontent) {
      const res = await this.model.SendSMSfn({ phoneNum: phone, content: SMScontent })
      // const { data } = await this.model.SendSMSfn(id,phone,SMScontent)
      if (res) {
        this.$message({ type: 'success', message: '发送成功!' })
        this.activetype = 'PASS'
        this.getlist(this.activetype)
        this.resetVariant()
        this.getnumber()
      }
    },

    async handleUpdate(item, type) {
      this.entityForm = { ...item }
      if (type == 'review') {
        this.isshow = true
        this.dialogStatus = 'review'
      } else if (type == 'update') {
        this.isshow = true
        this.dialogStatus = 'update'
      } else if (type == 'watch') {
        this.isshow = false
        this.dialogStatus = 'watch'
      }
      this.dialogFormVisible = true
      // 上传所需要的配置
      this.uploadData.refId = item.id
      const { data } = await this.model.getUploadList(item.id || item)
      this.entityForm = { ...data }
      // this.supplierEntity.value = this.entityForm.supplierId
      this.supplierEntity.value = this.entityForm.otherId
      this.customerEntity.active = this.entityForm.otherName

      this.reviewLogList = data.reviewLogList
      let newarry = []
      if (data.reviewLogList.length > 0) {
        data.reviewLogList.forEach((item, index) => {
          newarry.push(item.reviewMessage)
        })
        // this.entityForm.reviewMessage = newarry.toString()
        this.entityForm.reviewMessage = ''
      }
    },
    async Approved() {
      const res = await this.model.getApproved(this.entityForm)
    },

    //点击状态获取数据
    async getlist(newV) {
      this.activetype = newV
      this.actionList.forEach((val, index) => {
        if (val.type == newV) {
          val.active = true
        } else {
          val.active = false
        }
      })
      // this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, filter_INS_state: newV })
      this.filtersSeach.filter_INS_state = newV
      this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    },
    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    // 更新累计Acc
    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      const paramsAcc = await this.getAccValues({ date, contractId })
      Object.keys(paramsAcc).forEach((key) => {
        this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
        this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      })
      this.computeWayCostAcc()
    },

    async passfn(id, reviewMessage) {
      const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage })
      this.getnumber()
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    },

    async rejectfn(id, reviewMessage) {
      const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage })
      this.getnumber()
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// .bigbox {
//   width: 100%;
//   padding: 15px 15px 0 15px;
//   background-color: #fff;
//   .btnbox {
//     width: 100%;
//     display: flex;
//     flex-direction: row;
//     align-items: center;
//   }
// }
.addV {
  ::v-deep .el-dialog__body {
    padding: 0 35px;
    // height: 65vh;
  }
  ::v-deep .el-input--mini .el-input__inner {
    height: 36px;
  }
  ::v-deep .el-tabs__item {
    font-size: 16px;
  }
  ::v-deep .el-input.is-disabled .el-input__inner {
    // color: #191919;
    color: #606266;
  }
}

.contentbox {
  width: 500px;
  margin: 100px auto 0 auto;
  font-size: 12px;
  .line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tsline {
    float: right;
    margin-top: 40px;
  }
  .inputbox {
    width: 100px;
    margin-bottom: 10px;
    border-bottom: solid 1px #ccc;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
<style scoped>
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
/* .dialog-footer >>> .dialog-footer-btns {
  border: 1px solid #2f79e8;
  color: #2f79e8;
} */
/* .opt-btn {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
}
.opt-btnV {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
} */
</style>