<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @import="handleImpiort"
                  :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" :showSummary='true'>
      <el-table-column label="品名" slot="name" width="200" prop="name" fixed='left' sortable>
        <template slot-scope="scope">
          <span class="name" @click="handleWatchForm(scope.row)">{{scope.row.name}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="price" label="煤价" slot="price" width="120">
        <template slot-scope="scope">
          <span v-if="!scope.row.active.price" @click="handleActive(scope.row,'price')">
            <i class="el-icon-edit"></i> {{scope.row.price}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.price" clearable oninput="value=value.replace(/[^0-9.]/g,'')"
                    size="small" @blur="handleBlur(scope.row,'price')" />
        </template>
      </el-table-column>
      <el-table-column prop="carriage" label="运费" slot="carriage" width="100">
        <template slot-scope="scope">
          <span v-if="!scope.row.active.carriage" @click="handleActive(scope.row,'carriage')">
            <i class="el-icon-edit"></i> {{scope.row.carriage}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.carriage" clearable oninput="value=value.replace(/[^0-9.]/g,'')"
                    size="small" @blur="handleBlur(scope.row,'carriage')" />
        </template>
      </el-table-column>
      <el-table-column prop="remarks" label="备注" slot="remarks" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="!scope.row.active.remarks" @click="handleActive(scope.row,'remarks')">
            <i class="el-icon-edit"></i> {{scope.row.remarks}}
          </span>
          <el-input v-else size="small" placeholder="请输入备注" v-model="scope.row.remarks" clearable
                    @blur="handleBlur(scope.row,'remarks')" />
        </template>
      </el-table-column>
      <el-table-column label="操作" slot="opt" width="100" fixed="right">
        <template slot-scope="scope">
          <template v-if="!scope.row._all">
            <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)" v-if="perms[`${curd}:update`]||false">编辑
            </el-tag>
          </template>
          <template v-else>
            <el-tag class="opt-btn" color="#33CAD9" @click="handleSave(scope.row)" v-if="perms[`${curd}:update`]||false">保存
            </el-tag>
          </template>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">

                <el-col>
                  <el-form-item label="品名" prop="name">
                    <el-input v-model="entityForm.name" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="发货单位" prop="senderName">
                    <el-input v-model="entityForm.senderName" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤种" prop="coalType">
                    <el-input v-model="entityForm.coalType" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">数据信息</div>
            <div class="form-layout">

              <el-row type="flex" :gutter="50">

                <el-col>
                  <el-form-item label="原发数" prop="sendWeight" label-width="140px">
                    <el-input v-model="entityForm.sendWeight" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="实收数" prop="receiveWeight" label-width="140px">
                    <el-input v-model="entityForm.receiveWeight" oninput="value=value.replace(/[^\d.]/g,'')" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="途耗" prop="wayCost">
                    <el-input v-model="entityForm.wayCost" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="车数" prop="truckCount">
                    <el-input v-model.number="entityForm.truckCount" oninput="value=value.replace(/[^\d]/g,'')" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="煤价" prop="truckCount">
                    <el-input v-model="entityForm.price" oninput="value=value.replace(/[^0-9.]/g,'')" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="运费" prop="carriage">
                    <el-input v-model="entityForm.carriage" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col />
                <el-col />
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">其他信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

    <export-excel v-if="exportExcelDialog" top="5vh" :exportExcelDialogVisible.sync="exportExcelDialog"
                  :traitSettings="excel.traitSettings" :exportLabelMap="excel.exportHeaderMap" :entityForm="model.tableOption">
      <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" :loading="scope.uploading.state"
                 @click="updateHandler(scope)" slot="handler" slot-scope="scope" :disabled="!scope.settings.data.length">上传
      </el-button>
    </export-excel>

  </div>
</template>

<script>
// import { listQuery } from '@/const'
import Mixins from '@/utils/mixins'
import Model from '@/model/invoicing/buyInMonth'
import { getYearMonth } from '@/utils'
const option = {
  animation: false,
  color: ['#FF726B'],
  tooltip: {
    trigger: 'axis',
    point: [-30, -30],
    backgroundColor: '#ccc',
    padding: [5, 20, 5, 20],
  },

  grid: {
    top: '50px',
    left: '50px',
    right: '15px',
    bottom: '50px',
  },
  legend: {
    right: 20,
    itemGap: 30,
    padding: [10, 10, 10, 10],
    align: 'center',
    data: [],
  },
  xAxis: {
    axisLine: {
      lineStyle: { color: '#E3F0FF' },
    },
    axisLabel: {
      color: '#D0D0D0', // 刻度标签文字的颜色
    },
    data: ['7:00', '8:00', '9:00', '10:00', '11:00', '12:00', '13:00'],
  },
  yAxis: {
    axisLine: { lineStyle: { color: '#E3F0FF' } },
    axisLabel: { color: '#D0D0D0' },
    splitLine: { show: true, lineStyle: { color: '#E3F0FF' } },
  },
  series: [
    {
      name: '价格',
      type: 'line',
      // symbol: 'none',
      itemStyle: { normal: { label: { show: true } } },
      data: [200, 100, 400, 200, 200, 100],
    },
  ],
}
const listQuery = {
  orderBy: 'senderName',
  orderDir: 'desc',
  filter_EQS_date: getYearMonth(),
}

export default {
  name: 'buyInMonth',
  mixins: [Mixins],
  data() {
    return {
      curd: 'buyInMonth',
      model: Model,
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'date',
            component: 'date-select',
            filter: 'filter_EQS_date',
            defaultValue: getYearMonth(),
            props: {
              placeholder: '请选择月份',
              clearable: false,
              type: 'month',
              format: 'yyyy-MM',
              formats: 'yyyy-MM',
            },
          },
          {
            prop: 'senderName',
            filter: 'filter_LIKES_senderName',
            props: { placeholder: '请输入发货单位', clearable: true },
          },
          {
            prop: 'name',
            filter: 'filter_LIKES_name',
            component: 'select-name',
            props: { placeholder: '请选择品名', clearable: true },
          },
        ],
      },
      listQuery: { ...listQuery },
      entityForm: { ...Model.model },
      excel: Model.getImportConfig(),
      exportExcelDialog: false,
      priceOption: JSON.parse(JSON.stringify(option)),
      showCharts: false,
      actions: [],
    }
  },
  created() {
    let xpt = `${this.curd}:export`
    if (this.perms[xpt]) {
      this.actions.push({
        config: { type: 'export-all' },
        show: true,
        label: '导出',
        click: this.export,
        type: 'current',
      })
    }
    // this.actions.push({
    //   config: {
    //     type: 'export-all'
    //   },
    //   show: true,
    //   label: '刷新报表',
    //   click: this.refresh,
    //   type: 'import'
    // })
  },
  methods: {
    handleFilter(filters) {
      this.listQuery = { ...listQuery, ...filters }
      this.$refs[this.curd].searchChange({ ...this.listQuery, ...filters })
    },

    handleFilterReset() {
      this.listQuery = { ...listQuery }
      this.$refs[this.curd].searchChange({ ...this.listQuery })
    },

    async refresh() {
      let { filter_EQS_date: date } = this.listQuery
      if (!date) {
        return
      }
      try {
        const { data } = await this.model.refresh({ date })

        if (data.length > 0) {
          this.$message({ showClose: true, message: '刷新报表成功', type: 'success' })
        } else {
          this.$message({ showClose: true, message: '本月暂无报表数据', type: 'info' })
        }
        this.$refs[this.curd].searchChange({ ...this.listQuery })
      } catch (error) {}
    },

    importExcel(unknown, type) {
      this.exportExcelDialog = true
    },
    async updateHandler(scope) {
      const data = [...scope.settings.data]
      const importList = data.map((v) => {
        const item = {}
        for (const key of Object.keys(this.entityForm)) {
          item[key] = v[key] !== undefined ? v[key] : ''
        }
        return item
      })
      const text = JSON.stringify(importList)
      scope.uploading.state = true
      const res = await Model.saveList({ text })
      scope.uploading.state = false
      if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      if (res.data.length) {
        scope.settings.data = res.data.map((v, i) => v)
      } else {
        this.$message({ showClose: true, message: '导入成功', type: 'success' })
        scope.settings.data = []
        this.getList()
      }
    },

    formatChartsData(records) {
      const priceList = []
      const dateList = []
      records.forEach((item) => {
        const index = dateList.findIndex((v) => v === item.date)
        if (index !== -1) {
          priceList[index] = priceList[index] * 1 + item.price * 1
        } else {
          dateList.push(item.date)
          priceList.push(item.price)
        }
      })
      priceList.reverse()
      dateList.reverse()
      return { priceList, dateList }
    },
    getWeeks({ type = '', date = new Date(), days = 6, fmt = 'yyyy-MM-dd' }) {
      const day = 24 * 60 * 60 * 1000
      return type === 'add'
        ? new Date(new Date(date).getTime() + day * days).Format(fmt)
        : new Date(new Date(date).getTime() - day * days).Format(fmt)
    },
    changeAll(row) {
      row._all = !row._all
    },
    handleUpdate(row) {
      this.changeAll(row)
      this.changeCurrent(row, true) // 编辑全部
    },
    handleSave(row) {
      this.changeAll(row)
      this.operationRow(row, true) // 取消时关闭所有的active
    },
    handleCancel(row) {
      row._all = false
      this.operationRow(row, false) // 取消时关闭所有的active
    },
    changeCurrent(row, target) {
      if (target === true) {
        for (const [k, v] of Object.entries(row.active)) {
          row.active[k] = !v
        }
      } else if (target === false) {
        for (const key of Object.keys(row.active)) {
          row.active[key] = false
        }
      } else {
        row.active[target] = !row.active[target]
      }
    },
    handleBlur(row, target) {
      this.changeCurrent(row, target) // 失去事件触发改变时关闭target的active
      const status = this.isOpenSave(row) // 判断当前事件是否全部关闭
      if (status) return // 为真说明还有blur未关闭
      for (const key of Object.keys(row.bak)) {
        if (row.bak[key] + '' !== row[key] + '') return // 判断两个值是否不相等 不相等则直接返回
      }
      setTimeout(() => {
        row._all = false
      }, 300) // 如果上述条件都成立则开回到最初的模式
    },
    isOpenSave(row) {
      let status = false
      for (const v of Object.values(row.active)) {
        if (v) {
          status = true
          break
        }
      }
      return status
    },
    /**
     * 双击时改变为input狂
     * @target string|true 用于判断是操作一个还是多个
     */
    handleActive(row, target) {
      row._all = true
      this.changeCurrent(row, target)
    },
    /**
     * 保存或取消
     * @action {true:false} true为保存逻辑，false为取消逻辑
     */
    async operationRow(row, action) {
      if (action) {
        const isChangePrice = row.price !== row.bak.price
        const remarksTemplate = `价格已更新,上次价格为:${row.bak.price},原发数为:${row.sendWeight},实收数为:${row.receiveWeight}`
        if (isChangePrice) {
          if (!row.remarks) {
            row.remarks = remarksTemplate
          } else if (/^价格已更新,上次价格为:\d+,原发数为:\d+,实收数为:\d+$/.test(row.remarks)) {
            row.remarks = remarksTemplate
          }
        }
        try {
          await this.model.save({ ...row })
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        } catch (e) {
          this.$message({ message: '保存失败', type: 'warning' })
        }
        this.getList()
      } else {
        for (const key of Object.keys(row.bak)) {
          if (action) {
            // ---------------------- 可写接口
            row.bak[key] = row[key] // 保存就时把当前key赋给bak
          } else {
            row[key] = row.bak[key] // 取消就是bak值赋给当前key
          }
          row.active[key] = false // 更具Key 将active都初始化
        }
      }
    },
  },
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
::v-deep .el-input--small .el-input__inner {
  // background: transparent;
  border: 1px solid #d8d8d8;
  // color: #2f79e8;
  height: 29.5px;
  line-height: 29.5px;
}
</style>
