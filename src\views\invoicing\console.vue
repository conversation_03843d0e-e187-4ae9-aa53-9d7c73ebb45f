<template>
  <div class="app-container">
    <panel-bar type="panel" title="数据看板">
      <el-date-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate" placeholder="选择日期"
                      :clearable="false" />
    </panel-bar>
    <card height="inherit" style="height:43vh">
      <card-item title="购进单位数量及价格图">
        <div class="item-echarts">
          <echarts width="inherit" height="inherit" v-if="isBuyInOption" :option="buyInOption" />
        </div>
      </card-item>
      <card-item title="销售单位数量及价格图">
        <div class="item-echarts">
          <echarts width="inherit" height="inherit" v-if="isSellOutOption" :option="sellOutOption" />
        </div>
      </card-item>
    </card>
    <panel-bar type="pounds" title="磅房数据" subtitle="(支持excel表导入、支持模板下载、支持数据导出至excel)" />
    <card height="inherit" style="height: 32.5rem">
      <card-item title="新增磅房报表">
        <section class="reports">
          <div class="reports-item">
            <div class="reports-item-section">
              <div class="reports-item-img" style="background:#33CAD9;" @click="handleToPage('weightHouseIn')">
                <img src="@/assets/console/purchase_enter.png" />
              </div>
              <div class="reports-item-desc" @click="handleToPage('weightHouseIn')">
                <span>磅房购进录入</span>
                <span>RAW COAL INVENTORY</span>
              </div>
            </div>
          </div>
          <div class="reports-item">
            <div class="reports-item-section">
              <div class="reports-item-img" style="background:#FF726B;" @click="handleToPage('weightHouseOut')">
                <img src="@/assets/console/sales_test_enter.png" />
              </div>
              <div class="reports-item-desc" @click="handleToPage('weightHouseOut')">
                <span>磅房销售录入</span>
                <span>SALES TEST ENTRY</span>
              </div>
            </div>
          </div>
        </section>
      </card-item>
    </card>
  </div>
</template>

<script>
import { PanelBar, Card, CardItem } from '@/components/Console'
import { listQuery } from '@/const'
import buyInModel from '@/model/invoicing/buyIn'
import sellOutModel from '@/model/invoicing/sellOut'

const option = {
  animation: false,
  color: ['#2f79e8', '#33CAD9', '#FF9639'],
  tooltip: {
    trigger: 'axis',
    point: [-30, -30],
    backgroundColor: '#EDEEFF',
    padding: [5, 20, 5, 20],
    textStyle: {
      color: '#9f9ea5',
    },
  },
  grid: {
    top: '50',
    left: '50px',
    right: '20px',
    bottom: '50px',
  },
  legend: {
    right: 20,
    itemGap: 20,
    padding: [10, 0, 10, 10],
    align: 'left',
    data: ['合同数量', '余量', '价格'],
  },
  xAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      // show:false, // 是否显示坐标轴轴线
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF', // 坐标轴线的颜色修改--文字也同步修改
        type: 'dashed',
      },
    },
    axisTick: {
      // 坐标轴刻度相关设置
      // show:true, // 是否显示坐标轴刻度
      alignWithLabel: true, // 可以保证刻度线和标签对齐
    },
    axisLabel: {
      color: '#9f9ea5', // 刻度标签文字的颜色
    },
    data: ['鑫飞能源', '焦煤集团', '汇锦', '棋盘井', '双柳'],
  },
  yAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF',
        type: 'solid',
      },
    },
    axisLabel: {
      // 坐标轴刻度标签的相关设置
      color: '#9f9ea5',
    },
    splitLine: {
      // 坐标轴在 grid 区域中的分隔线。
      show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示。
      lineStyle: {
        color: '#E3F0FF', // 分隔线颜色，可以设置成单个颜色
      },
    },
  },
  series: [
    {
      name: '合同数量',
      type: 'bar',
      barWidth: 16,
      itemStyle: {
        normal: {
          barBorderRadius: [30, 30, 0, 0], // 配置样式
          color: () => '#2f79e8',
        },
      },
      data: [5, 20, 36, 10, 10, 20],
    },
    {
      name: '余量',
      type: 'bar',
      barWidth: 16,
      itemStyle: {
        normal: {
          barBorderRadius: [30, 30, 0, 0], // 配置样式
          color: () => '#33CAD9',
        },
      },
      data: [15, 10, 62, 30, 11, 30],
    },
    {
      name: '价格',
      type: 'line',
      symbol: 'circle',
      symbolSize: 4,
      itemStyle: {
        normal: {
          color: () => '#FF9639',
        },
      },
      data: [123, 320, 42, 40, 130, 30],
    },
  ],
}
Object.freeze(option)
export default {
  name: 'invoicingConsole',
  components: { PanelBar, Card, CardItem },
  data() {
    return {
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      listQuery: { ...listQuery },
      loading: false,
      buyInOption: {},
      sellOutOption: {},
      buyInColor: ['#2f79e8', '#33CAD9', '#FF9639'],
      sellOutColor: ['#33CAD9', '#FF9639', '#2f79e8'],
    }
  },
  created() {
    this.getData()
  },
  computed: {
    isBuyInOption() {
      if (Object.keys(this.buyInOption).length) return true
      return false
    },
    isSellOutOption() {
      if (Object.keys(this.sellOutOption).length) return true
      return false
    },
  },
  methods: {
    /**
     * 初始化请求数据用于渲染`echarts`
     * @param filter_EQS_date:String 检索当前日期的date 默认为``则查询所有
     * @return void
     */
    async getData(filter_EQS_date = '') {
      if (this.loading) return
      this.loading = true
      try {
        const listQuery = { ...this.listQuery, filter_EQS_date }
        const res = await Promise.all([buyInModel.page(listQuery), sellOutModel.page(listQuery)])
        if (res.length) {
          this.buyInOption = this.formatOption({
            data: res[0].data.records,
            option,
            color: this.buyInColor,
          })
          this.sellOutOption = this.formatOption({
            data: res[1].data.records,
            option,
            color: this.sellOutColor,
          })
        }
        this.loading = false
      } catch (e) {
        this.loading = false
      }
    },

    /**
     * 自定义格式化 echarts.option数据
     * @param optinos:{data:[],option:option,color:[]}
     * @return newOption
     */
    formatOption({ data, option, color }) {
      // 深拷贝
      const newOption = JSON.parse(JSON.stringify(option))
      const nameList = []
      const amountList = []
      const amountLeftList = []
      const priceList = []
      data.forEach((item) => {
        // 不存在则push 存在根据索引去累加
        const nameIndex = nameList.findIndex((name) => name === item.name)
        if (nameIndex <= -1) {
          nameList.push(item.name)
          amountList.push(item.amount)
          amountLeftList.push(item.amountLeft)
          priceList.push(item.price)
        } else {
          amountList[nameIndex] += item.amount
          amountLeftList[nameIndex] += item.amountLeft
          priceList[nameIndex] += item.price
        }
      })
      // 覆盖颜色
      newOption.color = color
      // 覆盖X轴名称data
      newOption.xAxis.data = nameList
      //  覆盖合同数量data
      newOption.series[0].data = amountList
      //  覆盖余量data
      newOption.series[1].data = amountLeftList
      //  覆盖价格data
      newOption.series[2].data = priceList
      return newOption
    },
    handleToPage(pageName) {
      this.$store.dispatch('changeChildRoute', { parent: pageName })
      this.$router.push({ name: pageName, params: { isOpenAdd: true } })
    },
  },
  watch: {
    /**
     * 改变日期重新请求数据
     */
    currentDate: {
      handler(v) {
        this.getData(v)
      },
      immediate: true,
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .card-item {
  padding: 5px;
}
::v-deep .item-echarts {
  width: 98.5%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  height: inherit;
}
.reports {
  width: inherit;
  height: inherit;
  display: flex;

  &-item {
    width: inherit;
    height: inherit;
    display: flex;
    flex-flow: column;
    justify-content: space-evenly;
    position: relative;

    &-section {
      display: flex;
      justify-content: center;
      opacity: 0.9;
      transition: all 0.5s;

      .reports-item-img {
        position: relative;
        width: 6rem;
        height: 6rem;
        border-radius: 50%;
        margin-right: 2rem;
        cursor: pointer;

        img {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 50%;
          height: 50%;
        }
      }

      .reports-item-desc {
        display: flex;
        justify-content: center;
        flex-flow: column nowrap;
        cursor: pointer;
        width: 180px;

        span:first-of-type {
          font-size: 1.5rem;
          color: #424242;
          margin-bottom: 10px;
        }

        span:last-of-type {
          font-size: 1rem;
          color: #a8a8a8;
        }
      }
    }

    &-section:hover {
      opacity: 1;
    }
  }

  &-item:first-of-type:after {
    content: '';
    position: absolute;
    opacity: 1;
    right: 0;
    height: 50px;
    width: 2px;
    background: #efefef;
  }
}
</style>
