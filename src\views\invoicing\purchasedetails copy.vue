<template>
  <div class="app-container">
    <filter-table :options="filterOption" :showAdd="perms[`${curd}:save`]||false" :showImport="perms[`${curd}:addimport`]||false"
                  :selectList="selectList" :showRefresh="perms[`${curd}:Refresh`]||false" @add="handleCreate"
                  @filter="handleFilter" @import="handleImpiort" @reset="handleFilterReset" :actions="actions"
                  :changeactions="changeactions" />
    <s-curd :ref="curd" :list-query="listQuery" :model="model" :name="curd" :showSummary='true' otherHeight="175"
            @selectItem="selectItem">
      <el-table-column slot="firstWeightDate" align="center" label="一次计量时间" prop="firstWeightDate" sortable width="125">
        <template slot-scope="scope">
          {{ scope.row.firstWeightDate }}
        </template>
      </el-table-column>
      <el-table-column slot="secondWeightDate" align="center" label="二次计量时间" prop="secondWeightDate" sortable width="125">
        <template slot-scope="scope">
          {{ scope.row.secondWeightDate }}
        </template>
      </el-table-column>
      <el-table-column slot="name" align="center" label="品名" prop="name" width="100">
        <!-- class="name" @click="handleWatchForm(scope.row)"-->
        <template slot-scope="scope"><span>{{ scope.row.name }}</span></template>
      </el-table-column>
      <el-table-column slot="contractName" align="center" label="合同名称" prop="contractName" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.contractName }}</span>
        </template>
      </el-table-column>
      <el-table-column slot="contractId" align="center" label="合同编号" prop="contractId" width="200">
        <template slot-scope="scope">
          <span @click="handleActive(scope.row,'contractId')" v-if="!scope.row.active.contractId">
            <span v-if="setcontract(scope.row)">{{setcontract(scope.row)}}</span>
            <span v-else>请选择合同</span>
          </span>
          <el-cascader v-else v-model="scope.row.contractId" :disabled="dialogStatus==='update'" :options="contractEntity.options"
                       :props="contractEntity.props" class="tscascader" clearable filterable placeholder="请选择合同"
                       style="width:100%;" @change="handleAreaChange($event,scope.row)" @focus="getactive(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column slot="carriage" align="center" label="运费" prop="carriage" width="100">
        <template slot-scope="scope">
          <span v-if="!scope.row.active.carriage" @click="handleActive(scope.row,'carriage')">
            <i class="el-icon-edit"></i> {{ scope.row.carriage }}</span>
          <el-input v-else v-model="scope.row.carriage" clearable oninput="value=value.replace(/[^0-9.]/g,'')" placeholder="请输入内容"
                    size="small" @blur="handleBlur(scope.row,'carriage')" />
        </template>
      </el-table-column>
      <el-table-column slot="price" align="center" label="煤价" prop="price" width="100">
        <template slot-scope="scope">
          <span v-if="!scope.row.active.price" @click="handleActive(scope.row,'price')">
            <i class="el-icon-edit"></i> {{ scope.row.price }}</span>
          <el-input v-else v-model="scope.row.price" clearable oninput="value=value.replace(/[^0-9.]/g,'')" placeholder="请输入内容"
                    size="small" @blur="handleBlur(scope.row,'price')" />
        </template>
      </el-table-column>
      <el-table-column slot="hasFax" align="center" label="是否含税" prop="hasFax" scope-slot>
        <template slot-scope="scope">
          <el-tag v-if="scope.row.hasFax=='Y'" class="opt-btn" color="#FF9639" @click="selectChangeisSettle(scope.row,'N')">是
          </el-tag>
          <el-tag v-if="scope.row.hasFax=='N'" class="opt-btn" color="red" @click="selectChangeisSettle(scope.row,'Y')">否
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column slot="wayCost" align="center" label="途耗" prop="wayCost">
        <template v-if="scope.row.wayCost" slot-scope="scope">{{ scope.row.wayCost }}</template>
      </el-table-column>
      <el-table-column slot="remarks" align="center" label="备注" prop="remarks" width="160">
        <template slot-scope="scope">
          <span v-if="!scope.row.active.remarks" @click="handleActive(scope.row,'remarks')">
            <i class="el-icon-edit"></i> {{ scope.row.remarks }}</span>
          <el-input v-else v-model="scope.row.remarks" clearable placeholder="请输入内容" size="small"
                    @blur="handleBlur(scope.row,'remarks')" />
        </template>
      </el-table-column>
      <el-table-column slot="opt" align="center" fixed="right" label="操作" width="100">
        <template slot-scope="scope">

          <template v-if="!scope.row._all">
            <el-tag v-if="perms[`${curd}:update`]||false" class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)">编辑
            </el-tag>
            <el-tag v-if="perms[`${curd}:delete`]||false" class="opt-btn" color="#2F79E8" @click="handleDel(scope.row)">
              删除
            </el-tag>
            <el-tag v-if="perms[`${curd}:refresh`]||false" class="opt-btn" color="#F56C6C" @click="Refreshfee(scope.row)">
              刷新费用
            </el-tag>
          </template>

          <template v-else>
            <el-tag v-if="perms[`${curd}:update`]||false" class="opt-btn" color="#33CAD9" @click="handleSave(scope.row)">保存
            </el-tag>
            <el-tag class="opt-btn" color="#FF726B" @click="handleCancel(scope.row)">取消
            </el-tag>
          </template>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogChange" :before-close="ChangehandleClose" :close-on-click-modal="false" :close-on-press-escape="false"
               :title="title" :visible.sync="ChangedialogFormVisible" class="tsdialog" top="20vh" width="980px">
      <el-form v-if="ChangedialogFormVisible" ref="changeForm" :model="entityForm" :show-message="true" :status-icon="true"
               label-position="top">
        <el-row>
          <el-col>
            <!-- <div class="form-title">基础信息</div> -->
            <div class="form-layout">
              <el-row :gutter="50" type="flex">
                <el-col v-if="title=='批量修改水分'" style="width:35%">
                  <el-form-item label="水分" prop="mt">
                    <el-input v-model="changeForm.mt" />
                  </el-form-item>
                </el-col>
                <el-col v-if="title=='批量修改运费'" style="width:35%">
                  <el-form-item label="运费" prop="carriage">
                    <el-input v-model="changeForm.carriage" />
                  </el-form-item>
                </el-col>
                <el-col v-if="title=='批量修改合同'" style="width:35%">
                  <el-form-item label="合同" prop="contractId">
                    <el-cascader v-model="contractEntity.active" :disabled="dialogStatus==='update'"
                                 :options="contractEntity.options" :props="contractEntity.props" clearable filterable
                                 placeholder="请选择合同" style="width:400px" />
                  </el-form-item>
                </el-col>
                <el-col v-if="title=='批量修改煤价'" style="width:50%">
                  <!-- <el-form-item label="煤价" prop="price">
                    <el-input v-model="changeForm.price" />
                  </el-form-item> -->
                  <el-form-item label="煤价" prop="price">
                    <el-input v-model="changeForm.price" autocomplete="off" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')">
                      <template slot="append">
                        <span style="margin-right:10px">是否含税:</span>
                        <el-radio-group v-model="hasFax" size="small" @change="changeTheme">
                          <el-radio :label="'Y'">是</el-radio>
                          <el-radio :label="'N'">否</el-radio>
                        </el-radio-group>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col v-if="title=='批量修改备注'" style="width:100%">
                  <el-form-item label="备注" prop="remarks">
                    <el-input v-model="changeForm.remarks" :rows="6" placeholder="请填写备注信息~" type="textarea"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div v-if="dialogStatus!=='watch'" slot="footer" class="dialog-footer">
        <el-button v-if="perms[`${curd}:update`]||false" :loading="changeentityFormLoading" class="dialog-footer-btns"
                   type="primary" @click="saveChangeEntityForm('changeForm')">保存
        </el-button>
        <el-button class="dialog-footer-btns" @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
    <export-excel v-if="excelConfig.dialog" :entityForm="model.tableOption" :exportExcelDialogVisible.sync="excelConfig.dialog"
                  :exportLabelMap="excelConfig.excel.exportHeaderMap" :traitSettings="excelConfig.excel.traitSettings">
      <el-button slot="handler" slot-scope="scope" :disabled="!scope.settings.data.length" :loading="scope.uploading.state"
                 style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" @click="handleUpload(scope)">
        上传
      </el-button>
    </export-excel>
  </div>
</template>

<script>
import { chooseContract } from '@/api/quality'
import supplierModel from '@/model/user/supplier'
import productModel from '@/model/product/productList'
import Mixins from '@/utils/mixins'
import Model from '@/model/invoicing/purchasedetails'
import { createMemoryField } from '@/utils/index'
import { exportExcel, parseTime } from '@/utils/index'
import { forEach } from 'jszip/lib/object'
import { exportData } from '@/api/system/dict/data'

export default {
  name: 'purchasedetails',
  mixins: [Mixins],
  data() {
    return {
      curd: 'purchasedetails',
      model: Model,
      title: '',
      ChangedialogFormVisible: false,
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
              placeholder: '开始日期',
              clearable: false
            }
          },
          {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
              placeholder: '结束日期',
              clearable: false
            }
          },
          // {
          //   prop: 'startDate',
          //   component: 'date-select',
          //   filter: 'filter_GES_a.secondWeightDate',
          //   title: '二次计量开始时间',
          //   width: 150,
          //   props: {
          //     type: 'datetime',
          //     formats: 'yyyy-MM-dd HH:mm:ss',
          //     format: 'yyyy-MM-dd HH:mm:ss',
          //     placeholder: '二次计量开始时间',
          //     defaultTime: '00:00:00',
          //     clearable: false
          //   }
          // },
          // {
          //   prop: 'endDate',
          //   component: 'date-select',
          //   filter: 'filter_LES_a.secondWeightDate',
          //   title: '二次计量结束时间',
          //   props: {
          //     type: 'datetime',
          //     formats: 'yyyy-MM-dd HH:mm:ss',
          //     format: 'yyyy-MM-dd HH:mm:ss',
          //     defaultTime: '23:59:59',
          //     placeholder: '二次计量结束时间',
          //     clearable: false
          //   }
          // },

          // {
          //   prop: 'startDate',
          //   component: 'date-select',
          //   filter: 'filter_GES_secondWeightDate',
          //   title: '皮重开始日期',
          //   width: 150,
          //   props: {
          //     type: 'datetime',
          //     formats: 'yyyy-MM-dd HH:mm:ss',
          //     format: 'yyyy-MM-dd HH:mm:ss',
          //     placeholder: '皮重开始日期',
          //     clearable: false,
          //   },
          // },
          // {
          //   prop: 'endDate',
          //   component: 'date-select',
          //   filter: 'filter_LES_secondWeightDate',
          //   title: '皮重结束日期',
          //   props: {
          //     type: 'datetime',
          //     formats: 'yyyy-MM-dd HH:mm:ss',
          //     format: 'yyyy-MM-dd HH:mm:ss',
          //     placeholder: '皮重结束日期',
          //     clearable: false,
          //   },
          // },
          {
            prop: 'firstParty',
            filter: 'filter_LIKES_a.firstParty',
            props: { placeholder: '请输入买方', clearable: true }
          },
          {
            prop: 'secondParty',
            filter: 'filter_LIKES_a.secondParty',
            props: { placeholder: '请输入卖方', clearable: true }
          },
          // {
          //   prop: 'senderName',
          //   filter: 'filter_LIKES_senderName',
          //   props: { placeholder: '请输入发货单位', clearable: true }
          // },
          {
            prop: 'senderName',
            filter: 'filter_LIKES_a.senderName',
            component: 'select-Sender-Name',
            props: {
              placeholder: '请输入发货单位',
              clearable: true,
              isNogetlist: true,
              changeFilter: true,
              type: 'senderName',
              selecttype: 'aName'
            }
          },
          {
            prop: 'aName',
            filter: 'filter_LIKES_a.name',
            component: 'select-name',
            props: { placeholder: '请选择品名', changeFilter: true, clearable: true, selecttype: 'aName' }
          },
          // {
          //   prop: 'name',
          //   filter: 'filter_LIKES_name',
          //   component: 'select-name',
          //   props: { placeholder: '请选择品名', changeFilter: true, clearable: true },
          // },
          {
            prop: 'plateNumber',
            filter: 'filter_LIKES_a.plateNumber',
            props: { placeholder: '请输入车牌', clearable: true }
          },
          {
            prop: 'sendWeight',
            filter: 'filter_LIKES_a.sendWeight',
            props: { placeholder: '请输入原发数', clearable: true }
          },
          {
            prop: 'receiveWeight',
            filter: 'filter_LIKES_a.receiveWeight',
            props: { placeholder: '请输入实收数', clearable: true }
          },
          {
            prop: 'contractName',
            filter: 'filter_LIKES_a.contractName',
            component: 'el-input', // 使用 el-input 作为合同名称的搜索框
            props: {
              placeholder: '请输入合同名称',
              clearable: true
            }
          },
        ]
      },
      entityForm: { ...Model.model },
      changeForm: {},
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        name: { required: true, message: '请输入品名', trigger: 'blur' },
        productId: { required: true, message: '请输入品名', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' },
        senderName: { required: true, message: '请选择发货单位', trigger: 'blur' },
        // receiverName: { required: true, message: '请输入收货单位', trigger: 'blur' },
        coalType: { required: true, message: '请选择煤种类型', trigger: 'change' },
        truckCount: { required: true, message: '请输入车数', trigger: 'blur' },
        sendWeight: { required: true, message: '请输入原发数', trigger: 'blur' },
        receiveWeight: { required: true, message: '请输入实收数', trigger: 'blur' },
        wayCost: { required: true, message: '请输入途耗', trigger: 'blur' },
        amountLeft: { required: true, trigger: 'blur' },
        truckCountAcc: { required: true, message: '请输入车数累计', trigger: 'blur' },
        sendWeightAcc: { required: true, message: '请输入原发累计(本月)', trigger: 'blur' },
        receiveWeightAcc: { required: true, message: '请输入实收累计(本月)', trigger: 'blur' },
        wayCostAcc: { required: true, message: '请输入途耗累计', trigger: 'blur' }
      },
      // excel配置
      excelConfig: { excel: Model.getImportConfig(), dialog: false },
      // 记忆字段
      memoryEntity: { fields: {}, triggered: false },
      // 合同
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      // 供应商
      supplierEntity: { options: [], active: [] },
      // 品名
      nameEntity: { options: [], active: '' },
      // 累计字段
      entityFormAcc: { receiveWeightAcc: 0, sendWeightAcc: 0, truckCountAcc: 0 },
      values: '',
      SupplierSelect: '',
      contractActive: '1483699287445381121',
      selectList: [],
      changeentityFormLoading: false,
      parmdata: {},
      batchChangeType: '',
      hasFax: 'Y',
      newcontractId: ''
    }
  },
  created() {
    this._requestInit()
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'name',
        'senderName',
        // 'receiverName',
        'coalType',
        'contractId',
        'contractCode',
        'supplierId',
        'supplierName',
        'productCode',
        'productId'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.permsAction(this.curd)
    this.permschange(this.curd)
    this.hasFax = 'Y'
  },

  beforeRouteEnter(to, from, next) {
    next()
  },
  methods: {
    setcontract(row) {
      if (this.contractEntity.options.length > 0) {
        let Things = this.contractEntity.options
        let name = ''
        for (var i = 0; i < Things.length; i++) {
          if (row.supplierId === Things[i].supplierId) {
            for (var g = 0; g < Things[i].contractBuyList.length; g++) {
              if (Things[i].contractBuyList[g].id === row.contractId) {
                name = Things[i].contractBuyList[g].displayName + '/' + Things[i].supplierName
              }
            }
          }
        }
        console.log("当前行数据:", row);
        console.log("合同数据:", this.contractEntity.options);
        return name
      }
    },
    async selectChangeisSettle(row, type) {
      row.hasFax = type
      try {
        await this.model.save({ ...row })
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },
    changeTheme(val) {
      this.hasFax = val
      this.changeForm.hasFax = val
      // this.changeForm = { ...this.changeForm, hasFax: val }
    },

    // async Refreshfn(e) {
    //   let date = ''
    //   if (e) {
    //     date = e
    //   } else {
    //     date = new Date(new Date().getTime()).Format('yyyy-MM-dd')
    //   }
    //   // let date = new Date(new Date().getTime()).Format('yyyy-MM-dd')
    //   try {
    //     const { data } = await this.model.refresh({ date })
    //     this.$message({ showClose: true, message: '刷新成功', type: 'success' })
    //     this.$refs[this.curd].searchChange({ ...this.listQuery })
    //   } catch (error) {}
    // },

    selectItem(list) {
      //批量选择的数据
      this.selectList = list
    },
    // 删除信息
    // async handleDelete() {
    //   if (this.selectList.length === 0) {
    //     this.$notify.error('请选择一条记录')
    //     return false
    //   }
    //   try {
    //     await this.$confirm('确认修改?')
    //     const ids = this.selectList.map((val) => val.id)
    //     const res = await this.model.delete(...ids)
    //     if (res) {
    //       this.$notify.success('修改成功')
    //       this.getList()
    //     }
    //   } catch (e) {}
    // },
    // 刷新页面
    getList() {
      this.$refs[this.curd].$emit('refresh')
    },
    // handleProductChange(product) {
    //   let { name, id: productId, code: productCode, coalCategory: coalType } = product
    //   this.batchUpdateFields({ name, productId, productCode, coalType })
    // },
    // handleSupplierChange(supplier) {
    //   const { id: supplierId, name: supplierName } = supplier
    //   this.batchUpdateFields({ supplierId, supplierName, senderName: supplierName }) // 同步数据 顺便同步原发
    // },
    // handleContractChange(contract) {
    // },

    /**
     * @fields <entityForm>
     */
    batchUpdateFields(fields = {}) {
      const fieldsKeys = Object.keys(fields)
      for (const key of Object.keys(this.entityForm)) {
        if (fieldsKeys.includes(key)) {
          this.entityForm[key] = fields[key]
        }
      }
    },
    /**
     * 双击时改变为input狂
     * @target string|true 用于判断是操作一个还是多个
     */
    handleActive(row, target) {
      row._all = true
      this.changeCurrent(row, target)
    },
    handleBlur(row, target) {
      this.changeCurrent(row, target) // 失去事件触发改变时关闭target的active
      const status = this.isOpenSave(row) // 判断当前事件是否全部关闭
      if (status) return // 为真说明还有blur未关闭
      for (const key of Object.keys(row.bak)) {
        if (row.bak[key] + '' !== row[key] + '') return // 判断两个值是否不相等 不相等则直接返回
      }
      setTimeout(() => {
        row._all = false
      }, 300) // 如果上述条件都成立则开回到最初的模式
    },
    isOpenSave(row) {
      let status = false
      for (const v of Object.values(row.active)) {
        if (v) {
          status = true
          break
        }
      }
      return status
    },
    async getactive(row) {
      if (typeof row.contractId === 'string') {
        const value = this.filterContractItem(row.contractId, 'contractEntity')
        row.contractId = value
      }
    },
    // async handlevisiblechange(e, row) {
    //   if (!e) {
    //     const form = this.filterContractItem(row.contractId, 'contractEntity')
    //     row.contractId = row.contractId[1]
    //     row.contractName = form.name
    //     row.contractCode = form.supplierName
    //     row.firstParty = form.firstParty
    //     row.secondParty = form.secondParty
    //     try {
    //       await this.model.save({ ...row })
    //       this.getList()
    //       this.$message({ showClose: true, message: '操作成功', type: 'success' })
    //     } catch (e) {
    //       this.$message({ message: '保存失败', type: 'warning' })
    //     }
    //   }
    // },
    async Refreshfee(row) {
      const form = this.filterContractItem(row.contractId, 'contractEntity')
      row.contractId = row.contractId
      row.contractName = form.name
      row.contractCode = form.supplierName
      row.firstParty = form.firstParty
      row.secondParty = form.secondParty
      try {
        await this.model.save({ ...row })
        this.getList()
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },
    async handleAreaChange(nowVvlue, row) {
      let newvalue = ''
      if (nowVvlue != undefined) {
        newvalue = JSON.parse(JSON.stringify(nowVvlue))
      }
      if (newvalue.length > 0) {
        const form = this.filterContractItem(newvalue, 'contractEntity')
        row.contractId = newvalue[1]
        row.contractName = form.name
        row.contractCode = form.supplierName
        row.firstParty = form.firstParty
        row.secondParty = form.secondParty
        try {
          await this.model.save({ ...row })
          this.getList()
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        } catch (e) {
          this.$message({ message: '保存失败', type: 'warning' })
        }
      }
    },
    ChangehandleClose() {
      this.resetVariant()
    },
    //批量修改
    async batchChangePrice(selectList, type) {
      this.changeForm = {}
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      this.batchChangeType = type
      this.ChangedialogFormVisible = true
      if (type == 'batchChangePrice') {
        //修改煤价
        this.title = '批量修改煤价'
      } else if (type == 'batchChangeCarriage') {
        //批量修改运费
        this.title = '批量修改运费'
      } else if (type == 'batchChangeContract') {
        //批量修改合同
        this.title = '批量修改合同'
      } else if (type == 'batchChangeMt') {
        this.title = '批量修改水分'
      } else if (type == 'batchChangeRemarks') {
        //批量修改合同
        this.title = '批量修改备注'
      }

      //批量改煤价 批量改运费 批量改合同

      // let newarry = []
      // for (var i = 0; i < selectList.length; i++) {
      //   let obj = {}
      //   obj.id = selectList[i].id
      //   if (type == 'batchChangePrice') {
      //     //批量修改价格

      //     obj.price = selectList[i].price
      //   } else if (type == 'batchChangeCarriage') {
      //     //批量修改运费
      //     obj.carriage = selectList[i].carriage
      //   } else if (type == 'batchChangeMt') {
      //     //批量修改水分
      //     obj.mt = selectList[i].mt
      //   } else if (type == 'batchChangeContract') {
      //     //批量修改合同
      //     obj.contractId = selectList[i].contractId
      //     obj.contractCode = selectList[i].contractCode
      //     obj.contractName = selectList[i].contractName
      //   }
      //   newarry.push(obj)
      // }
      // let parm = {
      //     weightHouseInList: newarry,
      // }

      // this.parmdata={ weightHouseInList: newarry,}

      // if (type == 'batchChangePrice') {
      //   //修改煤价
      //   let { data } = await Model.savebatchChangePrice({ ...this.listQuery, ...parm })
      // } else if (type == 'batchChangeCarriage') {
      //   //批量修改运费
      //   let { data } = await Model.savebatchChangeCarriage({ ...this.listQuery, ...parm })
      // } else if (type == 'batchChangeContract') {
      //   //批量修改合同
      //   let { data } = await Model.savebatchChangeContract({ ...this.listQuery, ...parm })
      // } else if (type == 'batchChangeMt') {
      //   //批量修改合同
      //   let { data } = await Model.savebatchChangeMt({ ...this.listQuery, ...parm })
      // }
    },
    // start 如果要弹出模态框的编辑就注释掉
    async saveChangeEntityForm(changeForm) {
      let newarry = []
      let selectList = this.selectList
      for (var i = 0; i < selectList.length; i++) {
        let obj = {}
        obj.date = selectList[i].date
        obj.id = selectList[i].id
        if (this.batchChangeType == 'batchChangePrice') {
          //批量修改价格
          obj.price = this.changeForm.price
          //是否含税
          obj.hasFax = this.hasFax
        } else if (this.batchChangeType == 'batchChangeCarriage') {
          //批量修改运费
          obj.carriage = this.changeForm.carriage
        } else if (this.batchChangeType == 'batchChangeMt') {
          //批量修改水分
          obj.mt = this.changeForm.mt
        } else if (this.batchChangeType == 'batchChangeContract') {
          //批量修改合同
          obj.contractId = this.changeForm.contractId
          obj.contractCode = this.changeForm.contractCode
          obj.contractName = this.changeForm.contractName
        } else if (this.batchChangeType == 'batchChangeRemarks') {
          //批量修改备注
          obj.remarks = this.changeForm.remarks
        }
        newarry.push(obj)
      }
      let parm = {
        weightHouseInList: newarry
      }

      let valid
      try {
        valid = await this.$refs[changeForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        this.changeentityFormLoading = true

        if (this.batchChangeType == 'batchChangePrice') {
          //修改煤价
          let { data } = await Model.savebatchChangePrice({ ...this.listQuery, ...parm })
        } else if (this.batchChangeType == 'batchChangeCarriage') {
          //批量修改运费
          let { data } = await Model.savebatchChangeCarriage({ ...this.listQuery, ...parm })
        } else if (this.batchChangeType == 'batchChangeContract') {
          //批量修改合同
          let { data } = await Model.savebatchChangeContract({ ...this.listQuery, ...parm })
        } else if (this.batchChangeType == 'batchChangeMt') {
          //批量修改水分
          let { data } = await Model.savebatchChangeMt({ ...this.listQuery, ...parm })
        } else if (this.batchChangeType == 'batchChangeRemarks') {
          //批量修改备注
          let { data } = await Model.savebatchChangeRemarks({ ...this.listQuery, ...parm })
        }

        this.changeentityFormLoading = false
        // if (data) {
        this.getList()
        this.resetVariant()
        this.ChangedialogFormVisible = false
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
        // }
      }
    },
    changeAll(row) {
      row._all = !row._all
    },
    handleUpdate(row) {
      this.changeAll(row)
      this.changeCurrent(row, true) // 编辑全部
    },
    handleSave(row) {
      this.changeAll(row)
      this.operationRow(row, true) // 取消时关闭所有的active
      this.changeCurrent(row, false) // 编辑全部
    },
    handleCancel(row) {
      row._all = false
      this.operationRow(row, false) // 取消时关闭所有的active
    },
    changeCurrent(row, target) {
      if (target === true) {
        for (const [k, v] of Object.entries(row.active)) {
          row.active[k] = !v
        }
      } else if (target === false) {
        for (const key of Object.keys(row.active)) {
          row.active[key] = false
        }
      } else {
        row.active[target] = !row.active[target]
      }
    },
    /**
     * 保存或取消
     * @action {true:false} true为保存逻辑，false为取消逻辑
     */
    async operationRow(row, action) {
      if (action) {
        const isChangePrice = row.price !== row.bak.price
        const remarksTemplate = `价格已更新,上次价格为:${row.bak.price},原发数为:${row.sendWeight},实收数为:${row.receiveWeight}`
        if (isChangePrice) {
          if (!row.remarks) {
            row.remarks = remarksTemplate
          } else if (/^价格已更新,上次价格为:\d+,原发数为:\d+,实收数为:\d+$/.test(row.remarks)) {
            row.remarks = remarksTemplate
          }
        }
        try {
          await this.model.save({ ...row })
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        } catch (e) {
          this.$message({ message: '保存失败', type: 'warning' })
        }
        // this.getList()
      } else {
        for (const key of Object.keys(row.bak)) {
          if (action) {
            // ---------------------- 可写接口
            row.bak[key] = row[key] // 保存就时把当前key赋给bak
          } else {
            row[key] = row.bak[key] // 取消就是bak值赋给当前key
          }
          row.active[key] = false // 更具Key 将active都初始化
        }
      }
    },
    // end 如果要弹出模态框的编辑就注释掉

    _requestInit() {
      this.getContract()
      this.getSupplier()
      this.getName()
    },
    /**
     * 保存取消时会触发初始化数据
     */
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.ChangedialogFormVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.contractEntity.active = []
        this.supplierEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },

    handleNameEntity(val) {
      this.entityForm.name = val
    },
    // 获取供应商选择接口
    async getSupplier() {
      try {
        let options = []
        const { data } = await await supplierModel.page({ size: 1000 })
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        this.supplierEntity.options = options
      } catch (e) { }
    },
    // 获取合同选择接口
    async getContract() {
      try {
        const { data } = await chooseContract()
        this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'displayName', supplierId: 'id' } })
        console.log('this.contractEntity.options', this.contractEntity.options)
      } catch (error) { }
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
        })
      } catch (error) { }
    },
    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    // 合同改变同步
    handleSupplier({ value, label }) {
      // this.entityForm.supplierId = value
      // this.entityForm.supplierName = label
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label }
    },

    paramsNumber(paramsAcc) {
      for (const [k, v] of Object.entries(paramsAcc)) {
        paramsAcc[k] = !Number.isNaN(v * 1) ? v * 1 : 0
      }
      return paramsAcc
    },

    importExcel(unknown, type) {
      this.excelConfig.dialog = true
    },
    async handleUpload(scope) {
      const data = [...scope.settings.data]
      const importList = data.map((v) => {
        const item = {}
        for (const key of Object.keys(this.entityForm)) {
          item[key] = v[key] !== undefined ? v[key] : ''
        }
        return item
      })
      const text = JSON.stringify(importList)
      scope.uploading.state = true
      const res = await Model.saveList({ text })
      scope.uploading.state = false
      if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      if (res.data.length) {
        scope.settings.data = res.data.map((v, i) => v)
      } else {
        this.$message({ showClose: true, message: '导入成功', type: 'success' })
        scope.settings.data = []
        this.getList()
      }
    },
    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    // 更新累计Acc
    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      const paramsAcc = await this.getAccValues({ date, contractId })
      Object.keys(paramsAcc).forEach((key) => {
        this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
        this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      })
      this.computeWayCostAcc()
    },
    // 处理receiveWeight, sendWeight 值处理成涂好
    updateWayCost() {
      let { receiveWeight, sendWeight } = this.entityForm
      receiveWeight = receiveWeight * 1
      sendWeight = sendWeight * 1
      if (receiveWeight === '' || sendWeight === '') return false // 数据为空时不计算
      if (receiveWeight === 0 && sendWeight === 0) return (this.entityForm.wayCost = 0.0)
      // 途耗累计=实收累计-原发累计/原发累计
      this.entityForm.wayCost = (((sendWeight - receiveWeight) / sendWeight) * 100).toFixed(2)
    },
    // 计算涂耗累计Acc
    computeWayCostAcc() {
      let { receiveWeightAcc, sendWeightAcc } = this.entityForm
      receiveWeightAcc = receiveWeightAcc * 1
      sendWeightAcc = sendWeightAcc * 1
      let wayCostAcc = (((sendWeightAcc - receiveWeightAcc) / sendWeightAcc) * 100).toFixed(2) * 1
      this.entityForm.wayCostAcc = !Number.isNaN(wayCostAcc) ? wayCostAcc : 0
    },
    // 计算累计方法
    computeAcc(accName, value) {
      if (accName === 'truckCountAcc') {
        this.entityForm[accName] = this.entityFormAcc[accName] * 1 + value * 1
        return
      }
      this.entityForm[accName] = (this.entityFormAcc[accName] * 1 + value * 1).toFixed(2)
    }
  },
  watch: {
    changeType: function (newV, oldV) {
    },
    'entityForm.name'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.entityForm.coalType = item.coalCategory || ''
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
      }
    },
    dialogFormVisible(v) {
      if (this.dialogStatus === 'create' && v) {
        this.updateAcc()
      } // 用于再打开时触发合同Acc数据
    },
    'entityForm.receiveWeight'(v) {
      this.computeAcc('receiveWeightAcc', v)
      this.updateWayCost()
    },
    'entityForm.sendWeight'(v) {
      this.computeAcc('sendWeightAcc', v)
      this.updateWayCost()
    },
    'entityForm.truckCount'(v) {
      this.computeAcc('truckCountAcc', v)
    },
    'entityForm.date'() {
      this.updateAcc()
    },
    'entityForm.wayCost'() {
      this.computeWayCostAcc()
    },
    'entityForm.contractId'(id) {
      this.updateAcc()
      if (this.entityForm.date && id) {
        this.updateAcc(this.entityForm.date, id)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'entityForm.supplierId'(id) {
      this.contractActive = id
      const item = this.supplierEntity.options.find((item) => item.value === id)
      if (item) {
        this.supplierEntity.active = item
      } else {
        this.supplierEntity.active = []
      }
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.name = form.productName
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.supplierId

      this.changeForm.contractCode = form.supplierName
      this.changeForm.contractId = form.supplierId
      this.changeForm.contractName = form.name

      this.entityForm.senderName = form.secondParty
      // this.entityForm.receiverName = form.firstParty
      const item = this.contractEntity.options.find((v) => v.supplierId === value[0])
      if (!item) return
      this.entityForm.supplierId = item.supplierId
      this.entityForm.supplierName = item.supplierName
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.ll {
  display: inline-block;
}
</style>
<style scoped>
.tsdialog >>> .el-dialog__body {
  height: 20vh;
}

.tscascader >>> .el-input__inner {
  padding: 0 60px 0 10px !important;
}

/* .opt-btn {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
}
.opt-btnV {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
} */
</style>
