<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :changeactions="changeactions"
                  :selectList="selectList" :actions="actions" @CarriageRemarksChanged="CarriageRemarksType"
                  @isBillChanged="isBillType" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" @Refresh="Refreshfn" />
    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :showSummary='true'
            :defaultsort="{ prop:'date', order: 'descending',issearch:issearch}" @selectItem="selectItem" otherHeight="175">
      <el-table-column label="品名" slot="name" width="100" prop="name" fixed='left' align="center">
        <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{scope.row.name}}</span></template>
      </el-table-column>
      <!-- <el-table-column label="合同" slot="contractId" prop="contractId" width="180" align="center">
        <template slot-scope="scope">
          <el-tooltip popper-class="popper" :content="getname(scope.row.contractId)" placement="top">
            <span style="cursor: pointer;">{{getname(scope.row.contractId)}}</span>
          </el-tooltip>
        </template>
      </el-table-column> -->

      <!-- <el-table-column label="合同" slot="contractId" prop="contractId" width="180" align="center">
        <template slot-scope="scope">
          <span style="cursor: pointer;">{{scope.row.contractCode}}</span>
        </template>
      </el-table-column> -->

      <!-- <el-table-column slot="contractId" align="center" label="合同" prop="contractId" width="200">
        <template slot-scope="scope">
          <span @click="handleActive(scope.row,'contractId')" v-if="!scope.row.active.contractId">
            <span v-if="setcontract(scope.row)">{{setcontract(scope.row)}}</span>
            <span v-else>请选择合同</span>
          </span>
          <el-cascader v-else v-model="scope.row.contractId" :disabled="dialogStatus==='update'" :options="contractEntity.options"
                       :props="contractEntity.props" class="tscascader" clearable filterable placeholder="请选择合同"
                       style="width:100%;" @change="handleAreaChange($event,scope.row)" @focus="getactive(scope.row)" />
        </template>
      </el-table-column> -->

      <el-table-column slot="contractName" align="center" label="合同名称" prop="contractName" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.contractName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="合同编号" slot="contractId" prop="contractId" width="180" align="center">
        <template slot-scope="scope">
          <span v-if="setcontract(scope.row)">{{setcontract(scope.row)}}</span>
          <!-- <span style="cursor: pointer;">{{scope.row.contractCode}}</span> -->
        </template>
      </el-table-column>

      <el-table-column label="结清" slot="isSettle" prop="isSettle" align="center">
        <template slot-scope="scope" v-if="scope.row.isSettle">
          <el-tag :type="(scope.row.isSettle=='Y' ? 'success':(scope.row.isSettle == 'N' ? 'warning' : 'info'))" size="mini">
            {{ scope.row.isSettle == 'Y' ? '已结清' : (scope.row.isSettle == 'N' ? '未结清' : '') }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- <el-table-column label="合同" slot="contractId" prop="contractId" width="150" align="center">
        <template slot-scope="scope">
          <el-cascader v-model="scope.row.contractId" :options="contractEntity.options" :props="contractEntity.props" filterable
                       :disabled="dialogStatus==='update'" clearable placeholder="请选择合同" style="width:100%"
                       @change="handleAreaChange($event,scope.row)" />
        </template>
      </el-table-column> -->

      <el-table-column label="途耗" slot="wayCost" prop="wayCost" align="center">
        <template slot-scope="scope">{{scope.row.wayCost}}</template>
      </el-table-column>
      <el-table-column label="途耗累计" slot="wayCostAcc" prop="wayCostAcc" align="center">
        <template slot-scope="scope">{{scope.row.wayCostAcc}}%</template>
      </el-table-column>
      <!-- 
      <el-table-column label="是否开票" slot="isBill" prop="isBill" align="center">
        <template slot-scope="scope" v-if="scope.row.isBill">
          <el-tag :type="(scope.row.isBill=='Y' ? 'success':(scope.row.isBill == 'N' ? 'warning' : 'info'))" size="mini">
            {{ scope.row.isBill == 'Y' ? '是' : (scope.row.isBill == 'N' ? '否' : '') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="运费情况" slot="carriageRemarks" prop="carriageRemarks" align="center">
        <template slot-scope="scope" v-if="scope.row.carriageRemarks">
          <el-tag :type="(scope.row.carriageRemarks=='Y' ? 'success':(scope.row.carriageRemarks == 'N' ? 'warning' : 'info'))"
                  size="mini">
            {{ scope.row.carriageRemarks == 'Y' ? '是' : (scope.row.carriageRemarks == 'N' ? '否' : '') }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column prop="carriageRemarks" label="运输情况" slot="carriageRemarks" align="center" width="110">
        <template slot-scope="scope">
          <div style="padding: 0 10px; box-sizing: border-box;">
            <el-select v-model="scope.row.carriageRemarks" clearable placeholder="请选择"
                       @change="setupdateCarriageRemarksfn(scope.row)">
              <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
            </el-select>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="开票情况" prop="isBill" align="center" slot="isBill" width="110">
        <template slot-scope="scope">
          <div style="padding: 0 10px; box-sizing: border-box;">
            <el-select v-model="scope.row.isBill" clearable placeholder="请选择" @change="setupdateIsBillfn(scope.row)">
              <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
            </el-select>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="备注" slot="remarks" prop="remarks" align="center">
        <template slot-scope="scope">
          <!-- {{scope.row.remarks}} -->
          <span v-if="!scope.row.active.remarks" @click="handleActive(scope.row,'remarks')">
            <i class="el-icon-edit"></i> {{scope.row.remarks}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.remarks" clearable size="small"
                    @blur="handleBlur(scope.row,'remarks')" />
        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" width="100" fixed="right" align="center">
        <!-- <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" v-if="perms[`${curd}:update`]||false" @click="handleUpdate(scope.row)">编辑
          </el-tag>
          <el-tag class="opt-btn" color="#2f79e8" v-if="perms[`${curd}:delete`]||false" @click="handleDel(scope.row)">删除</el-tag>
        </template> -->
      </el-table-column>
    </s-curd>

    <el-dialog width="980px" ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :rules="rules" :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基础信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="entityForm.date" :disabled="dialogStatus==='update'" clearable></date-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同" prop="contractId">
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 filterable :disabled="dialogStatus==='update'" clearable placeholder="请选择合同" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="品名" prop="name">
                    <select-down :value.sync="nameEntity.active" disabled :list="nameEntity.options"
                                 @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤种" prop="coalType">
                    <category-select :value.sync="entityForm.coalType" disabled />
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="发货单位" prop="senderName">
                    <el-input v-model="entityForm.senderName" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="收货单位" prop="receiverName">
                    <el-select v-model="customerEntity.active" placeholder="请选择收货单位" clearable @change="handleCustomer">
                      <el-option v-for="item in customerEntity.options" :key="item.value" :label="item.label" :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col />
                <el-col />
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">数据信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="车数" prop="truckCount">
                    <el-input v-model="entityForm.truckCount" :oninput="field.int" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="原发数" prop="sendWeight">
                    <el-input v-model="entityForm.sendWeight" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="实收数" prop="receiveWeight">
                    <el-input v-model="entityForm.receiveWeight" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>

                  <el-form-item label="途耗" prop="wayCost">
                    <el-input v-model="entityForm.wayCost" clearable disabled>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="车数累计" prop="truckCountAcc">
                    <el-input v-model="entityForm.truckCountAcc" disabled />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="原发累计(本月)" prop="sendWeightAcc">
                    <el-input v-model="entityForm.sendWeightAcc" disabled>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="实收累计(本月)" prop="receiveWeightAcc">
                    <el-input v-model="entityForm.receiveWeightAcc" disabled>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="途耗累计" prop="wayCostAcc">
                    <el-input v-model="entityForm.wayCostAcc" disabled>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">人员信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="在岗人" prop="stationMan">
                    <el-input v-model="entityForm.stationMan" clearable />
                  </el-form-item>

                </el-col>
                <el-col>
                  <el-form-item label="交班人" prop="transferMan">
                    <el-input v-model="entityForm.transferMan" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="接班人" prop="nextMan">
                    <el-input v-model="entityForm.nextMan" clearable />
                  </el-form-item>
                </el-col>
                <el-col />
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" v-if="perms[`${curd}:update`]||false" :loading="entityFormLoading"
                   @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisibleV" :before-close="handleCloseV" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="changeForm" :model="entityFormV" label-position="top">
        <el-row type="flex" :gutter="50">
          <el-col>
            <el-form-item label="日期" prop="date">
              <date-select v-model="entityFormV.date" clearable></date-select>
            </el-form-item>
          </el-col>
          <el-col>
            <div style="margin-top:20px">
              <el-button type="primary" class="dialog-footer-btns" :loading="changeeFormLoading"
                         v-if="perms[`${curd}:Refresh`]||false" @click="RefreshForm">刷新
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <export-excel v-if="excelConfig.dialog" :exportExcelDialogVisible.sync="excelConfig.dialog"
                  :traitSettings="excelConfig.excel.traitSettings" :exportLabelMap="excelConfig.excel.exportHeaderMap"
                  :entityForm="model.tableOption">
      <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" :loading="scope.uploading.state"
                 @click="handleUpload(scope)" slot="handler" slot-scope="scope" :disabled="!scope.settings.data.length">上传
      </el-button>
    </export-excel>

    <!-- 批量修改模态框 -->
    <el-dialog width="980px" class="tsdialog" ref="dialogChange" top="20vh" :title="title" :visible.sync="ChangedialogFormVisible"
               :before-close="ChangehandleClose" :close-on-click-modal="false" :close-on-press-escape="false">

      <el-form :show-message="true" :status-icon="true" ref="changeForm" :model="entityForm" label-position="top"
               v-if="ChangedialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col style="width:50%">
                  <el-form-item label="开票情况" prop="isBill">
                    <el-select v-model="changeForm.isBill" clearable placeholder="请选择">
                      <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col style="width:50%">
                  <el-form-item label="运输情况" prop="carriageRemarks">
                    <el-select v-model="changeForm.carriageRemarks" clearable placeholder="请选择">
                      <el-option v-for="item in isBilllist" :key="item.label" :label="item.label" :value="item.type" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

        </el-row>

      </el-form>

      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="changeentityFormLoading"
                   @click="saveChangeEntityForm('changeForm')">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCustomerContract } from '@/api/quality'
import Mixins from '@/utils/mixins'
import Model from '@/model/invoicing/salesSummarydetails'
import productModel from '@/model/product/productList'
import CustomerModel from '@/model/user/customer'
import { createMemoryField } from '@/utils/index'
export default {
  name: 'salesSummarydetails',
  mixins: [Mixins],
  data() {
    return {
      curd: 'salesSummarydetails',
      dialogFormVisibleV: false,
      changeeFormLoading: false,
      entityFormV: {
        date: new Date(new Date().getTime()).Format('yyyy-MM-dd')
      },
      model: Model,
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
              placeholder: '开始日期',
              clearable: false
            }
          },
          {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
              placeholder: '结束日期',
              clearable: false
            }
          },
          // {
          //   prop: 'receiverName',
          //   filter: 'filter_LIKES_receiverName',
          //   props: { placeholder: '请输入收货单位', clearable: true },
          // },
          // {
          //   prop: 'receiverName',
          //   filter: 'filter_LIKES_receiverName',
          //   component: 'select-name',
          //   props: {
          //     placeholder: '请输入收货单位',
          //     clearable: true,
          //     isNogetlist: true,
          //     changeFilter: true,
          //     type: 'receiverName',
          //   },
          // },

          {
            prop: 'receiverName',
            filter: 'filter_LIKES_receiverName',
            component: 'select-Sender-Name',
            props: {
              placeholder: '请输入收货单位',
              clearable: true,
              isNogetlist: true,
              changeFilter: true,
              type: 'receiverName'
            }
          },

          {
            prop: 'name',
            filter: 'filter_LIKES_name',
            component: 'select-name',
            props: { placeholder: '请选择品名', changeFilter: true, clearable: true }
          },
          {
            prop: 'price',
            filter: 'filter_LIKES_price',
            props: {
              placeholder: '价格',
              clearable: true
            }
          },
          {
            prop: 'carriage',
            filter: 'filter_LIKES_carriage',
            props: {
              placeholder: '运费',
              clearable: true
            }
          },
          {
            prop: 'isBill',
            filter: 'filter_EQS_isBill',
            component: 'dict-select',
            props: {
              name: 'select',
              type: 'bill_type',
              placeholder: '开票情况',
              clearable: true
            }
          },
          {
            prop: 'carriageRemarks',
            filter: 'filter_EQS_carriageRemarks',
            component: 'dict-select',
            props: {
              name: 'select',
              type: 'bill_type',
              placeholder: '运输情况',
              clearable: true
            }
          },
          {
            prop: 'contractId',
            filter: 'filter_EQL_contractId',
            component: 'contract-select',
            width: 240,
            props: {
              placeholder: '请选择合同',
              type: '销售'
            }
          }
        ]
      },

      // filterOption: Model.filterOption([
      //   {
      //     prop: 'receiverName',
      //     filter: 'filter_LIKES_receiverName',
      //     props: { placeholder: '请输入收货单位', clearable: true },
      //   },
      //   {
      //     prop: 'name',
      //     filter: 'filter_LIKES_name',
      //     component: 'select-name',
      //     props: { placeholder: '请选择品名', clearable: true },
      //   },
      //   {
      //     prop: 'price',
      //     filter: 'filter_LIKES_price',
      //     props: {
      //       placeholder: '价格',
      //       clearable: true,
      //     },
      //   },
      //   {
      //     prop: 'carriage',
      //     filter: 'filter_LIKES_carriage',
      //     props: {
      //       placeholder: '运费',
      //       clearable: true,
      //     },
      //   },
      //   {
      //     prop: 'isBill',
      //     filter: 'filter_EQS_isBill',
      //     component: 'dict-select',
      //     props: {
      //       name: 'select',
      //       type: 'bill_type',
      //       placeholder: '开票情况',
      //       clearable: true,
      //       // change(e) {
      //       //   console.log('开票情况')
      //       //   console.log(e)
      //       // },
      //     },
      //   },
      //   {
      //     prop: 'carriageRemarks',
      //     filter: 'filter_EQS_carriageRemarks',
      //     component: 'dict-select',
      //     props: {
      //       name: 'select',
      //       type: 'sync_type',
      //       placeholder: '运输情况',
      //       clearable: true,
      //     },
      //   },
      // ]),

      entityForm: { ...Model.model },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        name: { required: true, message: '请输入品名', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' },
        senderName: { required: true, message: '请输入发货单位', trigger: 'blur' },
        receiverName: { required: true, message: '请选择收货单位', trigger: 'blur' },
        coalType: { required: true, message: '请选择煤种类型', trigger: 'change' },
        truckCount: { required: true, message: '请输入车数', trigger: 'blur' },
        sendWeight: { required: true, message: '请输入原发数', trigger: 'blur' },
        // customerId: { required: true, message: '请输入实收数', trigger: 'blur' },
        wayCost: { required: true, message: '请输入途耗', trigger: 'blur' },
        truckCountAcc: { required: true, message: '请输入车数累计', trigger: 'blur' },
        sendWeightAcc: { required: true, message: '请输入原发累计(本月)', trigger: 'blur' },
        receiveWeightAcc: { required: true, message: '请输入实发累计(本月)', trigger: 'blur' },
        wayCostAcc: { required: true, message: '请输入途耗累计', trigger: 'blur' }
      },
      excelConfig: { excel: Model.getImportConfig(), dialog: false },
      memoryEntity: { fields: {}, triggered: false },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      changeentityFormLoading: false,
      nameEntity: { options: [], active: '' },
      customerEntity: { options: [], active: [] },
      entityFormAcc: { receiveWeightAcc: 0, sendWeightAcc: 0, truckCountAcc: 0 },
      isBilllist: [
        { label: '是', type: 'Y' },
        { label: '否', type: 'N' }
      ],
      isBilllistV: [
        { label: '是', type: 'Y' },
        { label: '否', type: 'N' },
        { label: '空', type: 'K' }
      ],
      // listQuery: {
      //   size: 50,
      //   current: 1,
      //   orderBy: 'date',
      //   orderDir: 'desc',
      // },
      // listQuery :{ ...this.listQuery, orderBy, orderDir }
      ChangedialogFormVisible: false,
      changeForm: {},
      isBill: 'Y',
      carriageRemarks: 'Y',
      selectList: [],
      batchChangeType: '',
      issearch: false
    }
  },
  async created() {
    this._requestInit()
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'name',
        'senderName',
        'receiverName',
        'coalType',
        'contractId',
        'contractCode',
        'customerId',
        'customerName',
        'productCode',
        'productId'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.permschange(this.curd)
    this.permsAction(this.curd)
  },
  methods: {
    setcontract(row) {
      if (this.contractEntity.options.length > 0) {
        let Things = this.contractEntity.options
        let name = ''
        for (var i = 0; i < Things.length; i++) {
          if (row.customerId === Things[i].customerId) {
            for (var g = 0; g < Things[i].contractSellList.length; g++) {
              if (Things[i].contractSellList[g].id === row.contractId) {
                name = Things[i].customerName + '/' + Things[i].contractSellList[g].displayName
              }
            }
          }
        }
        return name
      }
    },
    isBillType(type) {
      // console.log('选中的是否开票')
      // console.log(type)
      if (type !== 'K') {
        this.filterOption.columns[6] = {
          ...this.filterOption.columns[6],
          filter: 'filter_EQS_isBill'
        }
      } else {
        this.filterOption.columns[6] = {
          ...this.filterOption.columns[6],
          filter: 'filter_ISNS_isBill'
        }
      }
    },
    CarriageRemarksType(type) {
      if (type !== 'K') {
        this.filterOption.columns[7] = {
          ...this.filterOption.columns[7],
          filter: 'filter_EQS_carriageRemarks'
        }
      } else {
        this.filterOption.columns[7] = {
          ...this.filterOption.columns[7],
          filter: 'filter_ISNS_carriageRemarks'
        }
      }
    },
    //批量修改
    async batchChangePrice(selectList, type) {
      this.changeForm = {}
      this.issearch = true
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      this.ChangedialogFormVisible = true
      this.batchChangeType = type
    },
    async saveChangeEntityForm(changeForm) {
      // console.log(this.changeForm)
      this.issearch = false
      let newarry = []
      let selectList = this.selectList
      for (var i = 0; i < selectList.length; i++) {
        let obj = {}
        obj.id = selectList[i].id
        obj.isBill = this.changeForm.isBill
        obj.carriageRemarks = this.changeForm.carriageRemarks
        newarry.push(obj)
      }
      let parm = {
        sellOutDetailList: newarry
      }
      let valid
      try {
        valid = await this.$refs[changeForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        this.changeentityFormLoading = true
        //修改
        let { data } = await Model.savebatchChangeBillCarriage({ ...this.listQuery, ...parm })
        this.changeentityFormLoading = false
        // if (data) {
        this.getList()
        this.resetVariant()
        this.ChangedialogFormVisible = false
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
        // }
      }
    },
    ChangehandleClose() {
      this.issearch = false
      this.resetVariant()
    },

    selectItem(list) {
      //复选框选择时阻止默认刷新
      this.issearch = true;
      //批量选择的数据
      this.selectList = list
    },
    //开票情况
    async setupdateIsBillfn(row) {
      try {
        await this.model.setupdateIsBill({ id: row.id, isBill: row.isBill })
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },
    //运输情况
    async setupdateCarriageRemarksfn(row) {
      try {
        await this.model.setupdateCarriageRemarks({ id: row.id, carriageRemarks: row.carriageRemarks })
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },
    /**
     * 双击时改变为input狂
     * @target string|true 用于判断是操作一个还是多个
     */
    handleActive(row, target) {
      row._all = true
      this.changeCurrent(row, target)
    },
    changeCurrent(row, target) {
      if (target === true) {
        for (const [k, v] of Object.entries(row.active)) {
          row.active[k] = !v
        }
      } else if (target === false) {
        for (const key of Object.keys(row.active)) {
          row.active[key] = false
        }
      } else {
        row.active[target] = !row.active[target]
      }
    },
    handleBlur(row, target) {
      this.changeCurrent(row, target) // 失去事件触发改变时关闭target的active
      const status = this.isOpenSave(row) // 判断当前事件是否全部关闭
      if (status) return // 为真说明还有blur未关闭
      if (target == 'remarks') {
        this.handleSaveRemarks(row, 'remarks')
      } else {
        this.handleSave(row)
      }
      for (const key of Object.keys(row.bak)) {
        if (row.bak[key] + '' !== row[key] + '') return // 判断两个值是否不相等 不相等则直接返回
      }
      setTimeout(() => {
        row._all = false
      }, 300) // 如果上述条件都成立则开回到最初的模式
    },
    handleSaveRemarks(row, type) {
      this.changeAll(row)
      this.operationRow(row, true, type) // 取消时关闭所有的active
    },
    handleSave(row) {
      this.changeAll(row)
      this.operationRow(row, true) // 取消时关闭所有的active
    },
    changeAll(row) {
      row._all = !row._all
    },
    async operationRow(row, action, type) {
      if (action) {
        // const isChangePrice = row.price !== row.bak.price
        // const remarksTemplate = `价格已更新,上次价格为:${row.bak.price},原发数为:${row.sendWeight},实收数为:${row.receiveWeight}`
        // if (isChangePrice) {
        //   if (!row.remarks) {
        //     row.remarks = remarksTemplate
        //   } else if (/^价格已更新,上次价格为:\d+,原发数为:\d+,实收数为:\d+$/.test(row.remarks)) {
        //     row.remarks = remarksTemplate
        //   }
        // }
        if (type == 'remarks') {
          try {
            await this.model.remarkssave({ ...row })
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          } catch (e) {
            this.$message({ message: '保存失败', type: 'warning' })
          }
        } else {
          try {
            await this.model.save({ ...row })
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          } catch (e) {
            this.$message({ message: '保存失败', type: 'warning' })
          }
        }
        this.getList()
      } else {
        for (const key of Object.keys(row.bak)) {
          if (action) {
            // ---------------------- 可写接口
            row.bak[key] = row[key] // 保存就时把当前key赋给bak
          } else {
            row[key] = row.bak[key] // 取消就是bak值赋给当前key
          }
          row.active[key] = false // 更具Key 将active都初始化
        }
      }
    },
    isOpenSave(row) {
      let status = false
      for (const v of Object.values(row.active)) {
        if (v) {
          status = true
          break
        }
      }
      return status
    },

    getname(contractId) {
      if (contractId) {
        const value = this.filterContractItemName(contractId, 'contractEntity')
        return value
      }
    },
    filterContractItemName(value, target) {
      if (typeof value === 'string') {
        let constname = ''
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              constname = list.customerName + ' / ' + item.customerName
              return constname
            }
          }
        }
      } else {
        return ''
      }
    },

    Refreshfn() {
      this.dialogStatus = 'Refresh'
      this.dialogFormVisibleV = true
    },
    handleCloseV() {
      this.dialogFormVisibleV = false
    },
    async RefreshForm() {
      this.changeeFormLoading = true
      let date = this.entityFormV.date
      try {
        const { data } = await this.model.refresh({ date })
        // if (data == null) {
        this.$message({ showClose: true, message: '刷新成功', type: 'success' })
        this.$refs[this.curd].searchChange({ ...this.listQuery })
        this.changeeFormLoading = false
        // } else {
        //   this.$message({ showClose: true, message: '出错了', type: 'error' })
        // }
      } catch (error) { }
      this.changeeFormLoading = false
      this.dialogFormVisibleV = false
    },
    _requestInit() {
      this.getContract()
      this.getCustomer()
      this.getName()
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.ChangedialogFormVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.contractEntity.active = []
        this.customerEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    // 保存时触发字段保留基础信息
    saveMemoryField() {
      this.memoryEntity.fields = createMemoryField({ fields: this.memoryEntity.fields, target: this.entityForm })
      this.memoryEntity.triggered = true
    },
    handleNameEntity(val) {
      this.entityForm.name = val
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
        })
      } catch (error) { }
    },
    async getCustomer() {
      try {
        let options = []
        const { data } = await CustomerModel.page({ size: 1000 })
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        this.customerEntity.options = options
      } catch (e) { }
    },
    async getContract() {
      const { data } = await getCustomerContract()
      // 供应商名称返回id用来查询
      this.contractEntity.options = this.formatContract({ data, key: { customerName: 'displayName', customerId: 'id' } })
    },
    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     * @target {contract|contractEntityForm}<String>
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },

    importExcel(unknown, type) {
      this.excelConfig.dialog = true
    },
    async handleUpload(scope) {
      const data = [...scope.settings.data]
      const importList = data.map((v) => {
        const item = {}
        for (const key of Object.keys(this.entityForm)) {
          item[key] = v[key] !== undefined ? v[key] : ''
        }
        return item
      })
      const text = JSON.stringify(importList)
      scope.uploading.state = true
      const res = await Model.saveList({ text })
      scope.uploading.state = false
      if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      if (res.data.length) {
        scope.settings.data = res.data.map((v, i) => v)
      } else {
        this.$message({ showClose: true, message: '导入成功', type: 'success' })
        scope.settings.data = []
        this.getList()
      }
    },
    // 查看数据
    handleWatchForm(item) {
      this.entityForm = { ...item }
      this.dialogStatus = 'watch'
      this.dialogFormVisible = true
    },
    // 合同改变同步entityForm
    handleCustomer({ value, label }) {
      this.entityForm.customerId = value
      this.entityForm.customerName = label
    },
    // 累计接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      // const paramsAcc = await this.getAccValues({ date, contractId })
      // Object.keys(paramsAcc).forEach((key) => {
      //   this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
      //   this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      // })
      this.computeWayCostAcc()
    },

    paramsNumber(paramsAcc) {
      for (const [k, v] of Object.entries(paramsAcc)) {
        paramsAcc[k] = !Number.isNaN(v * 1) ? v * 1 : 0 // undefined*1=NAN null*1=0 ''*1=0 如果不等于NaN就默认转换
      }
      return paramsAcc
    },
    // 处理receiveWeight, sendWeight 值处理成涂好
    updateWayCost() {
      let { receiveWeight, sendWeight } = this.entityForm
      receiveWeight = receiveWeight * 1
      sendWeight = sendWeight * 1
      if (receiveWeight === '' || sendWeight === '') return false // 数据为空时不计算
      if (receiveWeight === 0 && sendWeight === 0) return (this.entityForm.wayCost = 0.0)
      // 途耗累计=实收累计-原发累计/原发累计
      this.entityForm.wayCost = (((sendWeight - receiveWeight) / sendWeight) * 100).toFixed(2)
    },
    computeWayCostAcc() {
      let { receiveWeightAcc, sendWeightAcc } = this.entityForm
      receiveWeightAcc = receiveWeightAcc * 1
      sendWeightAcc = sendWeightAcc * 1
      let wayCostAcc = (((sendWeightAcc - receiveWeightAcc) / sendWeightAcc) * 100).toFixed(2) * 1
      this.entityForm.wayCostAcc = !Number.isNaN(wayCostAcc) ? wayCostAcc : 0
    },
    computeAcc(accName, value) {
      if (accName === 'truckCountAcc') {
        this.entityForm[accName] = this.entityFormAcc[accName] * 1 + value * 1
        return
      }
      this.entityForm[accName] = (this.entityFormAcc[accName] * 1 + value * 1).toFixed(2)
    }
  },
  watch: {
    'entityForm.name'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.entityForm.coalType = item.coalCategory || ''
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
      }
    },
    dialogFormVisible(v) {
      if (this.dialogStatus === 'create' && v) {
        this.updateAcc()
      }
    },
    'entityForm.receiveWeight'(v) {
      this.computeAcc('receiveWeightAcc', v)
      this.updateWayCost()
    },
    'entityForm.sendWeight'(v) {
      this.computeAcc('sendWeightAcc', v)
      this.updateWayCost()
    },
    'entityForm.truckCount'(v) {
      this.computeAcc('truckCountAcc', v)
    },
    'entityForm.date'() {
      this.updateAcc()
    },
    'entityForm.wayCost'() {
      this.computeWayCostAcc()
    },
    'entityForm.contractId'(id) {
      this.updateAcc()
      if (this.entityForm.date && id) {
        this.updateAcc(this.entityForm.date, id)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'entityForm.customerId'(id) {
      const item = this.customerEntity.options.find((item) => item.value === id)
      if (item) {
        this.customerEntity.active = item
      } else {
        this.customerEntity.active = []
      }
    },
    // 在打开更新时watch监听然后通过过滤筛选触发contractEntityFormActive灌入显示数据
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.name = form.productName
      this.entityForm.contractCode = form.customerName
      this.entityForm.contractId = form.customerId
      this.entityForm.receiverName = form.firstParty
      this.entityForm.senderName = form.secondParty
      const item = this.contractEntity.options.find((v) => v.customerId === value[0])
      if (!item) return
      this.entityForm.customerName = item.customerName
      this.entityForm.customerId = item.customerId
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
// .opt-btnV {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
</style>
