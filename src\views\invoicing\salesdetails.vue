<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :selectList="selectList" :changeactions="changeactions" :actions="actions"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <!-- otherHeight="210" -->
    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :showSummary='true' @selectItem="selectItem"
            otherHeight="175">
      <!-- <el-table-column label="进厂时间" slot="firstWeightDate" width="180" prop="firstWeightDate" sortable align="center">
        <template slot-scope="scope">
          {{scope.row.firstWeightDate}}
        </template>
      </el-table-column> -->

      <el-table-column label="一次计量时间" slot="firstWeightDate" width="125" prop="firstWeightDate" sortable align="center">
        <template slot-scope="scope">
          {{scope.row.firstWeightDate}}
        </template>
      </el-table-column>

      <!-- <el-table-column label="出厂时间" slot="secondWeightDate" width="180" prop="secondWeightDate" sortable align="center">
        <template slot-scope="scope">
          {{scope.row.secondWeightDate}}
        </template>
      </el-table-column> -->

      <el-table-column label="二次计量时间" slot="secondWeightDate" width="125" prop="secondWeightDate" sortable align="center">
        <template slot-scope="scope">
          {{scope.row.secondWeightDate}}
        </template>
      </el-table-column>

      <el-table-column label="品名" slot="name" prop="name" width="100" align="center">
        <template slot-scope="scope">
          <!-- class="name" @click="handleWatchForm(scope.row)" -->
          <span>{{scope.row.name}}</span>
        </template>
      </el-table-column>

      <!-- <el-table-column label="合同" slot="contractId" prop="contractId" width="250" align="center">
        <template slot-scope="scope">
          <el-cascader v-model="scope.row.contractId" :options="contractEntity.options" :props="contractEntity.props" filterable
                       class="tscascader" :disabled="dialogStatus==='update'" clearable placeholder="请选择合同" style="width:100%"
                       @focus="getactive(scope.row)" @change="handleAreaChange($event,scope.row)" />
        </template>
      </el-table-column> -->

      <el-table-column slot="contractName" align="center" label="合同名称" prop="contractName" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.contractName }}</span>
        </template>
      </el-table-column>

      <el-table-column slot="contractId" align="center" label="合同编号" prop="contractId" width="200">
        <template slot-scope="scope">
          <span @click="handleActive(scope.row,'contractId')" v-if="!scope.row.active.contractId">
            <span v-if="setcontract(scope.row)">{{setcontract(scope.row)}}</span>
            <span v-else>请选择合同</span>
          </span>
          <el-cascader v-else v-model="scope.row.contractId" :disabled="dialogStatus==='update'" :options="contractEntity.options"
                       :props="contractEntity.props" class="tscascader" clearable filterable placeholder="请选择合同"
                       style="width:100%;" @change="handleAreaChange($event,scope.row)" @focus="getactive(scope.row)" />
        </template>
      </el-table-column>

      <el-table-column prop="price" slot="price" label="煤价" width="100" align="center">
        <template slot-scope="scope">
          <span v-if="!scope.row.active.price" @click="handleActive(scope.row,'price')">
            <i class="el-icon-edit"></i> {{scope.row.price}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.price" clearable oninput="value=value.replace(/[^0-9.]/g,'')"
                    size="small" @blur="handleBlur(scope.row,'price')" />
        </template>
      </el-table-column>
      <el-table-column label="是否含税" slot="hasFax" prop="hasFax" align="center" scope-slot>
        <template slot-scope="scope">
          <el-tag v-if="scope.row.hasFax=='Y'" class="opt-btn" color="#FF9639" @click="selectChangeisSettle(scope.row,'N')">是
          </el-tag>
          <el-tag v-if="scope.row.hasFax=='N'" class="opt-btn" color="red" @click="selectChangeisSettle(scope.row,'Y')">否
          </el-tag>
        </template>
      </el-table-column>

      <!-- <el-table-column prop="carriage" slot="carriage" label="运费" width="100" align="center">
        <template slot-scope="scope">
          <span v-if="!scope.row.active.carriage" @click="handleActive(scope.row,'carriage')">
            <i class="el-icon-edit"></i> {{scope.row.carriage}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.carriage" clearable oninput="value=value.replace(/[^0-9.]/g,'')"
                    size="small" @blur="handleBlur(scope.row,'carriage')" />
        </template>
      </el-table-column> -->

      <!-- <el-table-column prop="mt" slot="mt" label="水分" width="100" align="center">
        <template slot-scope="scope">
          <span v-if="!scope.row.active.mt" @click="handleActive(scope.row,'mt')">
            <i class="el-icon-edit"></i> {{scope.row.mt}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.mt" clearable oninput="value=value.replace(/[^0-9.]/g,'')"
                    size="small" @blur="handleBlur(scope.row,'mt')" />
        </template>
      </el-table-column> -->

      <el-table-column label="途耗" slot="wayCost" prop="wayCost" align="center">
        <template slot-scope="scope">{{scope.row.wayCost}}</template>
      </el-table-column>

      <el-table-column prop="remarks" slot="remarks" label="备注" align="center" width="160">
        <template slot-scope="scope">
          <span v-if="!scope.row.active.remarks" @click="handleActive(scope.row,'remarks')">
            <i class="el-icon-edit"></i> {{scope.row.remarks}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.remarks" clearable size="small"
                    @blur="handleBlur(scope.row,'remarks')" />
        </template>
      </el-table-column>

      <!--
      <el-table-column label="途耗累计" slot="wayCostAcc" prop="wayCostAcc" align="center">
        <template slot-scope="scope">{{scope.row.wayCostAcc}}%</template>
      </el-table-column> -->

      <el-table-column label="操作" slot="opt" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <template v-if="!scope.row._all">
            <el-tag class="opt-btn" color="#FF9639" v-if="perms[`${curd}:update`]||false" @click="handleUpdate(scope.row)">编辑
            </el-tag>
            <el-tag class="opt-btn" color="#2F79E8" v-if="perms[`${curd}:delete`]||false" @click="handleDel(scope.row)">删除
            </el-tag>
            <el-tag v-if="perms[`${curd}:refresh`]||false" class="opt-btn" color="#F56C6C" @click="Refreshfee(scope.row)">
              刷新费用
            </el-tag>
          </template>
          <template v-else>
            <el-tag class="opt-btn" color="#33CAD9" @click="handleSave(scope.row)" v-if="perms[`${curd}:update`]||false">保存
            </el-tag>
            <el-tag class="opt-btn" color="#FF726B" @click="handleCancel(scope.row)">取消
            </el-tag>
          </template>
        </template>

      </el-table-column>
    </s-curd>
    <!--
    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :rules="rules" :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基础信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="entityForm.date" :disabled="dialogStatus==='update'" clearable></date-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同" prop="contractId">
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 filterable :disabled="dialogStatus==='update'" clearable placeholder="请选择合同" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="品名" prop="name">
                    <select-down :value.sync="nameEntity.active" disabled :list="nameEntity.options"
                                 @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤种" prop="coalType">
                    <category-select :value.sync="entityForm.coalType" disabled />
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="发货单位" prop="senderName">
                    <el-input v-model="entityForm.senderName" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="收货单位" prop="receiverName">
                    <el-select v-model="customerEntity.active" placeholder="请选择收货单位" clearable @change="handleCustomer">
                      <el-option v-for="item in customerEntity.options" :key="item.value" :label="item.label" :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col />
                <el-col />
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">数据信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="车数" prop="truckCount">
                    <el-input v-model="entityForm.truckCount" :oninput="field.int" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="原发数" prop="sendWeight">
                    <el-input v-model="entityForm.sendWeight" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="实收数" prop="receiveWeight">
                    <el-input v-model="entityForm.receiveWeight" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>

                  <el-form-item label="途耗" prop="wayCost">
                    <el-input v-model="entityForm.wayCost" clearable disabled>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="车数累计" prop="truckCountAcc">
                    <el-input v-model="entityForm.truckCountAcc" disabled />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="原发累计(本月)" prop="sendWeightAcc">
                    <el-input v-model="entityForm.sendWeightAcc" disabled>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="实收累计(本月)" prop="receiveWeightAcc">
                    <el-input v-model="entityForm.receiveWeightAcc" disabled>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="途耗累计" prop="wayCostAcc">
                    <el-input v-model="entityForm.wayCostAcc" disabled>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" v-if="perms[`${curd}:update`]||false" :loading="entityFormLoading"
                   @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog> -->

    <el-dialog width="980px" class="tsdialog" ref="dialogChange" top="20vh" :title="title" :visible.sync="ChangedialogFormVisible"
               :before-close="ChangehandleClose" :close-on-click-modal="false" :close-on-press-escape="false">

      <el-form :show-message="true" :status-icon="true" ref="changeForm" :model="entityForm" label-position="top"
               v-if="ChangedialogFormVisible">
        <el-row>
          <el-col>
            <!-- <div class="form-title">基础信息</div> -->
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col v-if="title=='批量修改水分'" style="width:35%">
                  <el-form-item label="水分" prop="mt">
                    <el-input v-model="changeForm.mt" />
                  </el-form-item>
                </el-col>
                <el-col v-if="title=='批量修改运费'" style="width:35%">
                  <el-form-item label="运费" prop="carriage">
                    <el-input v-model="changeForm.carriage" />
                  </el-form-item>
                </el-col>
                <el-col v-if="title=='批量修改合同'" style="width:35%">
                  <el-form-item label="合同" prop="contractId">
                    <!-- <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 style="width:400rpx" filterable :disabled="dialogStatus==='update'" clearable placeholder="请选择合同"
                                 @change="handleAreaChange2($event)" /> -->
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 style="width:400rpx" filterable :disabled="dialogStatus==='update'" clearable
                                 placeholder="请选择合同" />
                  </el-form-item>
                </el-col>
                <el-col v-if="title=='批量修改煤价'" style="width:50%">
                  <!-- <el-form-item label="煤价" prop="price">
                    <el-input v-model="changeForm.price" />
                  </el-form-item> -->
                  <el-form-item label="煤价" prop="price">
                    <el-input v-model="changeForm.price" autocomplete="off" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')">
                      <template slot="append">
                        <span style="margin-right:10px">是否含税:</span>
                        <el-radio-group v-model="hasFax" size="small" @change="changeTheme">
                          <el-radio :label="'Y'">是</el-radio>
                          <el-radio :label="'N'">否</el-radio>
                        </el-radio-group>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col v-if="title=='批量修改备注'" style="width:100%">
                  <el-form-item label="备注" prop="remarks">
                    <el-input v-model="changeForm.remarks" type="textarea" :rows="6" placeholder="请填写备注信息~"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

        </el-row>

      </el-form>

      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="changeentityFormLoading"
                   v-if="perms[`${curd}:update`]||false" @click="saveChangeEntityForm('changeForm')">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

    <export-excel v-if="excelConfig.dialog" :exportExcelDialogVisible.sync="excelConfig.dialog"
                  :traitSettings="excelConfig.excel.traitSettings" :exportLabelMap="excelConfig.excel.exportHeaderMap"
                  :entityForm="model.tableOption">
      <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" :loading="scope.uploading.state"
                 @click="handleUpload(scope)" slot="handler" slot-scope="scope" :disabled="!scope.settings.data.length">上传
      </el-button>
    </export-excel>
  </div>
</template>

<script>
import { getCustomerContract } from '@/api/quality'
import Mixins from '@/utils/mixins'
import Model from '@/model/invoicing/salesdetails'
import productModel from '@/model/product/productList'
import CustomerModel from '@/model/user/customer'
import { createMemoryField } from '@/utils/index'
import { exportExcel, parseTime } from '@/utils/index'
export default {
  name: 'salesdetails',
  mixins: [Mixins],
  data() {
    return {
      title: '',
      ChangedialogFormVisible: false,
      changeForm: {},
      selectList: [],
      changeentityFormLoading: false,
      parmdata: {},
      batchChangeType: '',
      curd: 'salesdetails',
      model: Model,
      // filterOption: Model.filterOption([
      //   // {
      //   //   prop: 'customerName',
      //   //   filter: 'filter_LIKES_customerName',
      //   //   props: { placeholder: '请输入收货单位', clearable: true },
      //   // },
      //   // {
      //   //   prop: 'customerName',
      //   //   filter: 'filter_LIKES_customerName',
      //   //   component: 'select-name',
      //   //   props: {
      //   //     placeholder: '请输入收货单位',
      //   //     clearable: true,
      //   //     isNogetlist: true,
      //   //     changeFilter: true,
      //   //     type: 'customerName',
      //   //   },
      //   // },
      //   {
      //     prop: 'customerName',
      //     filter: 'filter_LIKES_customerName',
      //     component: 'select-Sender-Name',
      //     props: { placeholder: '请输入收货单位', changeFilter: true, isNogetlist: true, clearable: true, type: 'customerName' },
      //   },

      //   {
      //     prop: 'name',
      //     filter: 'filter_LIKES_name',
      //     component: 'select-name',
      //     props: { placeholder: '请选择品名', changeFilter: true, clearable: true },
      //   },
      //   {
      //     prop: 'plateNumber',
      //     filter: 'filter_LIKES_plateNumber',
      //     props: { placeholder: '请输入车牌', clearable: true },
      //   },
      //   {
      //     prop: 'sendWeight',
      //     filter: 'filter_LIKES_sendWeight',
      //     props: { placeholder: '请输入原发数', clearable: true },
      //   },
      //   {
      //     prop: 'receiveWeight',
      //     filter: 'filter_LIKES_receiveWeight',
      //     props: { placeholder: '请输入实收数', clearable: true },
      //   },
      // ]),

      filterOption: {
        showMore: true,
        columns: [
          // {
          //   prop: 'date',
          //   component: 'date-select',
          //   filter: 'filter_EQS_date',
          //   props: {
          //     placeholder: '日期',
          //     clearable: false
          //   }
          // },

          {
            prop: 'startDateV',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
              placeholder: '开始日期',
              clearable: false
            }
          },
          {
            prop: 'endDateV',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
              placeholder: '结束日期',
              clearable: false
            }
          },

          // {
          //   prop: 'startDate',
          //   component: 'date-select',
          //   filter: 'filter_GES_secondWeightDate',
          //   title: '二次计量开始日期',
          //   width: 150,
          //   props: {
          //     type: 'datetime',
          //     formats: 'yyyy-MM-dd HH:mm:ss',
          //     format: 'yyyy-MM-dd HH:mm:ss',
          //     placeholder: '二次计量开始日期',
          //     defaultTime: '00:00:00',
          //     clearable: false
          //   }
          // },
          // {
          //   prop: 'endDate',
          //   component: 'date-select',
          //   filter: 'filter_LES_secondWeightDate',
          //   title: '二次计量结束日期',
          //   props: {
          //     type: 'datetime',
          //     formats: 'yyyy-MM-dd HH:mm:ss',
          //     format: 'yyyy-MM-dd HH:mm:ss',
          //     defaultTime: '23:59:59',
          //     placeholder: '二次计量结束日期',
          //     clearable: false
          //   }
          // },

          // {
          //   prop: 'startDateV',
          //   component: 'date-select',
          //   filter: 'filter_GES_actualReceiveDate',
          //   props: {
          //     placeholder: '实收日期开始',
          //     clearable: false
          //   }
          // },
          // {
          //   prop: 'endDateV',
          //   component: 'date-select',
          //   filter: 'filter_LES_actualReceiveDate',
          //   props: {
          //     placeholder: '实收日期结束',
          //     clearable: false
          //   }
          // },

          {
            prop: 'customerName',
            filter: 'filter_LIKES_customerName',
            component: 'select-Sender-Name',
            props: {
              placeholder: '请输入收货单位',
              changeFilter: true,
              isNogetlist: true,
              clearable: true,
              type: 'customerName'
            }
          },

          {
            prop: 'name',
            filter: 'filter_LIKES_name',
            component: 'select-name',
            props: { placeholder: '请选择品名', changeFilter: true, clearable: true }
          },
          {
            prop: 'plateNumber',
            filter: 'filter_LIKES_plateNumber',
            props: { placeholder: '请输入车牌', clearable: true }
          },
          {
            prop: 'sendWeight',
            filter: 'filter_LIKES_sendWeight',
            props: { placeholder: '请输入原发数', clearable: true }
          },
          {
            prop: 'receiveWeight',
            filter: 'filter_LIKES_receiveWeight',
            props: { placeholder: '请输入实收数', clearable: true }
          }
        ]
      },

      entityForm: { ...Model.model },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        name: { required: true, message: '请输入品名', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' },
        senderName: { required: true, message: '请输入发货单位', trigger: 'blur' },
        receiverName: { required: true, message: '请选择收货单位', trigger: 'blur' },
        coalType: { required: true, message: '请选择煤种类型', trigger: 'change' },
        truckCount: { required: true, message: '请输入车数', trigger: 'blur' },
        sendWeight: { required: true, message: '请输入原发数', trigger: 'blur' },
        // customerId: { required: true, message: '请输入实收数', trigger: 'blur' },
        wayCost: { required: true, message: '请输入途耗', trigger: 'blur' },
        truckCountAcc: { required: true, message: '请输入车数累计', trigger: 'blur' },
        sendWeightAcc: { required: true, message: '请输入原发累计(本月)', trigger: 'blur' },
        receiveWeightAcc: { required: true, message: '请输入实发累计(本月)', trigger: 'blur' },
        wayCostAcc: { required: true, message: '请输入途耗累计', trigger: 'blur' }
      },
      hasFax: 'Y',
      excelConfig: { excel: Model.getImportConfig(), dialog: false },
      memoryEntity: { fields: {}, triggered: false },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      nameEntity: { options: [], active: '' },
      customerEntity: { options: [], active: [] },
      entityFormAcc: { receiveWeightAcc: 0, sendWeightAcc: 0, truckCountAcc: 0 }
    }
  },
  watch: {
    'entityForm.contractId'(id) {
      this.updateAcc()
      if (this.entityForm.date && id) {
        this.updateAcc(this.entityForm.date, id)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },

    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.name = form.productName
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.supplierId
      this.entityForm.senderName = form.secondParty
      this.entityForm.receiverName = form.firstParty
      const item = this.contractEntity.options.find((v) => v.supplierId === value[0])
      if (!item) return
      this.entityForm.supplierId = item.supplierId
      this.entityForm.supplierName = item.supplierName
    }
  },
  async created() {
    this._requestInit()
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'name',
        'senderName',
        'receiverName',
        'coalType',
        'contractId',
        'contractCode',
        'customerId',
        'customerName',
        'productCode',
        'productId'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.permsAction(this.curd)
    this.permschange(this.curd)
    this.hasFax = 'Y'
  },
  methods: {
    setcontract(row) {
      if (this.contractEntity.options.length > 0) {
        let Things = this.contractEntity.options
        let name = ''
        for (var i = 0; i < Things.length; i++) {
          if (row.customerId === Things[i].customerId) {
            for (var g = 0; g < Things[i].contractSellList.length; g++) {
              if (Things[i].contractSellList[g].id === row.contractId) {
                name = Things[i].customerName + '/' + Things[i].contractSellList[g].displayName
              }
            }
          }
        }
        return name
      }
    },
    async export(unknown, type) {
      const dictHashMap = {}
      const batchNo = parseTime(new Date(), '{y}-{m}-{d}')
      const header = [] // 头部
      const columns = [] // prop属性
      const keys = []
      this.model.tableOption.columns.forEach((val) => {
        if (!val.type && val.isShow) {
          if (!val.noExport) {
            const tHeader = val.isHeader ? val.isHeader + val.label : val.label
            header.push(tHeader)
            columns.push(val.prop || val.slot)
          }
          if (val.slot) {
            keys.push(val.slot)
          }
          if (val.format) {
            keys.push(val.prop)
          }
        }
      })
      let list
      if (type === `all`) {
        try {
          await this.$confirm('当前数据量比较大, 是否继续导出?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
        } catch (e) {
          return false
        }
        const res = await this.model.page({ ...this.listQuery, size: 10000 }, true)
        if (res.data.records.length) {
          list = res.data.records
        } else {
          return this.$message.success('当前没有数据可以导出(⊙⊙！)')
        }
      } else {
        list = JSON.parse(JSON.stringify(this.$refs[this.curd].dataList))
        if (!list.length) return this.$message.success('当前没有数据可以导出(⊙⊙！)')
      }
      list.forEach((v) => {
        keys.forEach((key) => {
          if (key == 'hasFax') {
            if (v[key] == 'Y') {
              v[key] = '是'
            } else if (v[key] == 'N') {
              v[key] = '否'
            }
          } else {
            if (dictHashMap[v[key]]) {
              v[key] = dictHashMap[v[key]]
            }
          }
        })
      })
      try {
        let title = this.title + '-' + batchNo
        title += type === 'all' ? '-全部' : ''
        exportExcel({ title, header: header, columns: columns, list: list })
        this.$message.success(`导出任务创建成功 (´・ω・｀)`)
      } catch (error) {
        this.$message.success('导出任务创建失败(o´▽`o)ﾉ')
      }
    },
    async Refreshfee(row) {
      const form = this.filterContractItem(row.contractId, 'contractEntity')
      console.log(row.contractId)
      row.contractId = row.contractId
      row.contractName = form.name
      row.contractCode = form.supplierName
      row.firstParty = form.firstParty
      row.secondParty = form.secondParty
      try {
        await this.model.save({ ...row })
        this.getList()
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },
    /**
     * 双击时改变为input狂
     * @target string|true 用于判断是操作一个还是多个
     */
    handleActive(row, target) {
      row._all = true
      this.changeCurrent(row, target)
    },

    handleBlur(row, target) {
      this.changeCurrent(row, target) // 失去事件触发改变时关闭target的active
      const status = this.isOpenSave(row) // 判断当前事件是否全部关闭
      if (status) return // 为真说明还有blur未关闭
      for (const key of Object.keys(row.bak)) {
        if (row.bak[key] + '' !== row[key] + '') return // 判断两个值是否不相等 不相等则直接返回
      }
      setTimeout(() => {
        row._all = false
      }, 300) // 如果上述条件都成立则开回到最初的模式
    },
    changeCurrent(row, target) {
      if (target === true) {
        for (const [k, v] of Object.entries(row.active)) {
          row.active[k] = !v
        }
      } else if (target === false) {
        for (const key of Object.keys(row.active)) {
          row.active[key] = false
        }
      } else {
        row.active[target] = !row.active[target]
      }
    },
    isOpenSave(row) {
      let status = false
      for (const v of Object.values(row.active)) {
        if (v) {
          status = true
          break
        }
      }
      return status
    },

    async selectChangeisSettle(row, type) {
      row.hasFax = type
      try {
        await this.model.save({ ...row })
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
      } catch (e) {
        this.$message({ message: '保存失败', type: 'warning' })
      }
    },
    changeTheme(val) {
      this.hasFax = val
      this.changeForm.hasFax = val
    },

    // async Refreshfn(e) {
    //   // let date = new Date(new Date().getTime()).Format('yyyy-MM-dd')
    //   let date = ''
    //   if (e) {
    //     date = e
    //   } else {
    //     date = new Date(new Date().getTime()).Format('yyyy-MM-dd')
    //   }
    //   try {
    //     const { data } = await this.model.refresh({ date })
    //     this.$message({ showClose: true, message: '刷新成功', type: 'success' })
    //     this.$refs[this.curd].searchChange({ ...this.listQuery })
    //   } catch (error) {}
    // },
    ChangehandleClose() {
      this.resetVariant()
    },
    //批量修改
    async batchChangePrice(selectList, type) {
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      this.batchChangeType = type
      this.ChangedialogFormVisible = true
      if (type == 'batchChangePrice') {
        //修改煤价
        this.title = '批量修改煤价'
      } else if (type == 'batchChangeCarriage') {
        //批量修改运费
        this.title = '批量修改运费'
      } else if (type == 'batchChangeContract') {
        //批量修改合同
        this.title = '批量修改合同'
      } else if (type == 'batchChangeMt') {
        this.title = '批量修改水分'
      } else if (type == 'batchChangeRemarks') {
        //批量修改合同
        this.title = '批量修改备注'
      }
    },
    // async handleAreaChange2(nowVvlue) {
    //   const form = this.filterContractItem(nowVvlue, 'contractEntity')
    //   this.changeForm.contractId = nowVvlue[1]
    //   this.changeForm.contractName = form.firstParty
    //   this.changeForm.contractCode = form.name
    // },
    async saveChangeEntityForm(changeForm) {
      let newarry = []
      let selectList = this.selectList
      for (var i = 0; i < selectList.length; i++) {
        let obj = {}
        obj.id = selectList[i].id
        obj.date = selectList[i].date
        if (this.batchChangeType == 'batchChangePrice') {
          //批量修改价格
          obj.price = this.changeForm.price
          //是否含税
          obj.hasFax = this.hasFax
        } else if (this.batchChangeType == 'batchChangeCarriage') {
          //批量修改运费
          obj.carriage = this.changeForm.carriage
        } else if (this.batchChangeType == 'batchChangeMt') {
          //批量修改水分
          obj.mt = this.changeForm.mt
        } else if (this.batchChangeType == 'batchChangeContract') {
          //批量修改合同
          obj.contractId = this.changeForm.contractId
          obj.contractCode = this.changeForm.contractCode
          obj.contractName = this.changeForm.contractName
        } else if (this.batchChangeType == 'batchChangeRemarks') {
          //批量修改备注
          obj.remarks = this.changeForm.remarks
        }
        newarry.push(obj)
      }
      let parm = {
        weightHouseOutList: newarry
      }
      let valid
      try {
        valid = await this.$refs[changeForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        this.changeentityFormLoading = true

        if (this.batchChangeType == 'batchChangePrice') {
          //修改煤价
          let { data } = await Model.savebatchChangePrice({ ...this.listQuery, ...parm })
        } else if (this.batchChangeType == 'batchChangeCarriage') {
          //批量修改运费
          let { data } = await Model.savebatchChangeCarriage({ ...this.listQuery, ...parm })
        } else if (this.batchChangeType == 'batchChangeContract') {
          //批量修改合同
          let { data } = await Model.savebatchChangeContract({ ...this.listQuery, ...parm })
        } else if (this.batchChangeType == 'batchChangeMt') {
          //批量修改水分
          let { data } = await Model.savebatchChangeMt({ ...this.listQuery, ...parm })
        } else if (this.batchChangeType == 'batchChangeRemarks') {
          //批量修改备注
          let { data } = await Model.savebatchChangeRemarks({ ...this.listQuery, ...parm })
        }
        this.changeentityFormLoading = false
        // if (data) {
        this.getList()
        this.resetVariant()
        this.ChangedialogFormVisible = false
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
        // }
      }
    },
    /**
     * 双击时改变为input狂
     * @target string|true 用于判断是操作一个还是多个
     */
    handleActive(row, target) {
      row._all = true
      this.changeCurrent(row, target)
    },
    // start 如果要弹出模态框的编辑就注释掉
    handleUpdate(row) {
      this.changeAll(row)
      this.changeCurrent(row, true) // 编辑全部
    },
    changeAll(row) {
      row._all = !row._all
    },
    changeCurrent(row, target) {
      if (target === true) {
        for (const [k, v] of Object.entries(row.active)) {
          row.active[k] = !v
        }
      } else if (target === false) {
        for (const key of Object.keys(row.active)) {
          row.active[key] = false
        }
      } else {
        row.active[target] = !row.active[target]
      }
    },

    handleSave(row) {
      this.changeAll(row)
      this.operationRow(row, true) // 取消时关闭所有的active
      this.changeCurrent(row, false) // 编辑全部
    },
    handleCancel(row) {
      row._all = false
      this.operationRow(row, false) // 取消时关闭所有的active
    },
    /**
     * 保存或取消
     * @action {true:false} true为保存逻辑，false为取消逻辑
     */
    async operationRow(row, action) {
      if (action) {
        const isChangePrice = row.price !== row.bak.price
        const remarksTemplate = `价格已更新,上次价格为:${row.bak.price},原发数为:${row.sendWeight},实收数为:${row.receiveWeight}`
        if (isChangePrice) {
          if (!row.remarks) {
            row.remarks = remarksTemplate
          } else if (/^价格已更新,上次价格为:\d+,原发数为:\d+,实收数为:\d+$/.test(row.remarks)) {
            row.remarks = remarksTemplate
          }
        }
        try {
          await this.model.save({ ...row })
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        } catch (e) {
          this.$message({ message: '保存失败', type: 'warning' })
        }
        this.getList()
      } else {
        for (const key of Object.keys(row.bak)) {
          if (action) {
            // ---------------------- 可写接口
            row.bak[key] = row[key] // 保存就时把当前key赋给bak
          } else {
            row[key] = row.bak[key] // 取消就是bak值赋给当前key
          }
          row.active[key] = false // 更具Key 将active都初始化
        }
      }
    },
    // end 如果要弹出模态框的编辑就注释掉

    handleBlur(row, target) {
      this.changeCurrent(row, target) // 失去事件触发改变时关闭target的active
      const status = this.isOpenSave(row) // 判断当前事件是否全部关闭
      if (status) return // 为真说明还有blur未关闭
      for (const key of Object.keys(row.bak)) {
        if (row.bak[key] + '' !== row[key] + '') return // 判断两个值是否不相等 不相等则直接返回
      }
      setTimeout(() => {
        row._all = false
      }, 300) // 如果上述条件都成立则开回到最初的模式
    },
    changeCurrent(row, target) {
      if (target === true) {
        for (const [k, v] of Object.entries(row.active)) {
          row.active[k] = !v
        }
      } else if (target === false) {
        for (const key of Object.keys(row.active)) {
          row.active[key] = false
        }
      } else {
        row.active[target] = !row.active[target]
      }
    },
    isOpenSave(row) {
      let status = false
      for (const v of Object.values(row.active)) {
        if (v) {
          status = true
          break
        }
      }
      return status
    },
    getactive(row) {
      if (typeof row.contractId === 'string') {
        const value = this.filterContractItem(row.contractId, 'contractEntity')
        row.contractId = value
      }
    },
    async handleAreaChange(nowVvlue, row) {
      let newvalue = ''
      if (nowVvlue != undefined) {
        newvalue = JSON.parse(JSON.stringify(nowVvlue))
      }
      if (newvalue.length > 0) {
        const form = this.filterContractItem(newvalue, 'contractEntity')
        row.contractId = newvalue[1]
        row.contractName = form.name
        row.contractCode = form.customerName
        row.firstParty = form.firstParty
        row.secondParty = form.secondParty
        try {
          await this.model.save({ ...row })
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
          this.getList()
        } catch (e) {
          this.$message({ message: '保存失败', type: 'warning' })
        }
      }
    },
    selectItem(list) {
      //批量选择的数据
      this.selectList = list
    },
    _requestInit() {
      this.getContract()
      this.getCustomer()
      this.getName()
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.ChangedialogFormVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.contractEntity.active = []
        this.customerEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    // 保存时触发字段保留基础信息
    saveMemoryField() {
      this.memoryEntity.fields = createMemoryField({ fields: this.memoryEntity.fields, target: this.entityForm })
      this.memoryEntity.triggered = true
    },
    handleNameEntity(val) {
      this.entityForm.name = val
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
        })
      } catch (error) { }
    },
    async getCustomer() {
      try {
        let options = []
        const { data } = await CustomerModel.page({ size: 1000 })
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        this.customerEntity.options = options
      } catch (e) { }
    },
    async getContract() {
      const { data } = await getCustomerContract()
      // 供应商名称返回id用来查询
      this.contractEntity.options = this.formatContract({ data, key: { customerName: 'displayName', customerId: 'id' } })
    },
    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     * @target {contract|contractEntityForm}<String>
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },

    importExcel(unknown, type) {
      this.excelConfig.dialog = true
    },
    async handleUpload(scope) {
      const data = [...scope.settings.data]
      const importList = data.map((v) => {
        const item = {}
        for (const key of Object.keys(this.entityForm)) {
          item[key] = v[key] !== undefined ? v[key] : ''
        }
        return item
      })
      const text = JSON.stringify(importList)
      scope.uploading.state = true
      const res = await Model.saveList({ text })
      scope.uploading.state = false
      if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      if (res.data.length) {
        scope.settings.data = res.data.map((v, i) => v)
      } else {
        this.$message({ showClose: true, message: '导入成功', type: 'success' })
        scope.settings.data = []
        this.getList()
      }
    },
    // 刷新页面
    getList() {
      this.$refs[this.curd].$emit('refresh')
    },
    // 查看数据
    // handleWatchForm(item) {
    //   this.entityForm = { ...item }
    //   this.dialogStatus = 'watch'
    //   this.title = '查看'
    //   this.dialogFormVisible = true
    // },
    // 合同改变同步entityForm
    handleCustomer({ value, label }) {
      this.entityForm.customerId = value
      this.entityForm.customerName = label
    },
    // 累计接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      const paramsAcc = await this.getAccValues({ date, contractId })
      Object.keys(paramsAcc).forEach((key) => {
        this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
        this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      })
      this.computeWayCostAcc()
    },

    paramsNumber(paramsAcc) {
      for (const [k, v] of Object.entries(paramsAcc)) {
        paramsAcc[k] = !Number.isNaN(v * 1) ? v * 1 : 0 // undefined*1=NAN null*1=0 ''*1=0 如果不等于NaN就默认转换
      }
      return paramsAcc
    },
    // 处理receiveWeight, sendWeight 值处理成涂好
    updateWayCost() {
      let { receiveWeight, sendWeight } = this.entityForm
      receiveWeight = receiveWeight * 1
      sendWeight = sendWeight * 1
      if (receiveWeight === '' || sendWeight === '') return false // 数据为空时不计算
      if (receiveWeight === 0 && sendWeight === 0) return (this.entityForm.wayCost = 0.0)
      // 途耗累计=实收累计-原发累计/原发累计
      this.entityForm.wayCost = (((sendWeight - receiveWeight) / sendWeight) * 100).toFixed(2)
    },
    computeWayCostAcc() {
      let { receiveWeightAcc, sendWeightAcc } = this.entityForm
      receiveWeightAcc = receiveWeightAcc * 1
      sendWeightAcc = sendWeightAcc * 1
      let wayCostAcc = (((sendWeightAcc - receiveWeightAcc) / sendWeightAcc) * 100).toFixed(2) * 1
      this.entityForm.wayCostAcc = !Number.isNaN(wayCostAcc) ? wayCostAcc : 0
    },
    computeAcc(accName, value) {
      if (accName === 'truckCountAcc') {
        this.entityForm[accName] = this.entityFormAcc[accName] * 1 + value * 1
        return
      }
      this.entityForm[accName] = (this.entityFormAcc[accName] * 1 + value * 1).toFixed(2)
    }
  },
  watch: {
    'entityForm.name'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.entityForm.coalType = item.coalCategory || ''
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
      }
    },
    dialogFormVisible(v) {
      if (this.dialogStatus === 'create' && v) {
        this.updateAcc()
      }
    },
    'entityForm.receiveWeight'(v) {
      this.computeAcc('receiveWeightAcc', v)
      this.updateWayCost()
    },
    'entityForm.sendWeight'(v) {
      this.computeAcc('sendWeightAcc', v)
      this.updateWayCost()
    },
    'entityForm.truckCount'(v) {
      this.computeAcc('truckCountAcc', v)
    },
    'entityForm.date'() {
      this.updateAcc()
    },
    'entityForm.wayCost'() {
      this.computeWayCostAcc()
    },
    'entityForm.contractId'(id) {
      this.updateAcc()
      if (this.entityForm.date && id) {
        this.updateAcc(this.entityForm.date, id)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'entityForm.customerId'(id) {
      const item = this.customerEntity.options.find((item) => item.value === id)
      if (item) {
        this.customerEntity.active = item
      } else {
        this.customerEntity.active = []
      }
    },
    // 在打开更新时watch监听然后通过过滤筛选触发contractEntityFormActive灌入显示数据
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.name = form.productName
      this.entityForm.contractCode = form.customerName
      this.entityForm.contractId = form.customerId

      this.changeForm.contractCode = form.customerName
      this.changeForm.contractId = form.customerId
      this.changeForm.contractName = form.name

      this.entityForm.receiverName = form.firstParty
      this.entityForm.senderName = form.secondParty
      const item = this.contractEntity.options.find((v) => v.customerId === value[0])
      if (!item) return
      this.entityForm.customerName = item.customerName
      this.entityForm.customerId = item.customerId
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
</style>
<style scoped>
.tscascader >>> .el-input__inner {
  padding: 0 60px 0 10px !important;
}
.tsdialog >>> .el-dialog__body {
  height: 20vh;
}
</style>
