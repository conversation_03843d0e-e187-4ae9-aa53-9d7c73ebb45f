<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
              @import="handleImpiort"    :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" :showSummary='true'>
      <el-table-column label="品名" slot="name" width="130" prop="name" fixed='left'>
        <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{scope.row.name}}</span></template>
      </el-table-column>
      <el-table-column prop="price" label="煤价(现金含税)" slot="price" width="140" scope-slot>
        <template #header>
          <span style="display:flex;align-items:center;cursor: pointer;" @click="handlePriceColumn('header')">
            <i class="iconfont icon-tubiao-zhexiantu"></i>
            <span style="margin-left:3px">煤价(现金含税)</span>
          </span>
        </template>
        <template slot-scope="scope">
          <span v-if="!scope.row.active.price" @click="handleActive(scope.row,'price')">
            <i class="el-icon-edit"></i> {{scope.row.price}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.price" clearable oninput="value=value.replace(/[^0-9.]/g,'')"
                    size="small" @blur="handleBlur(scope.row,'price')" />
        </template>
      </el-table-column>
      <el-table-column prop="carriage" label="运费" slot="carriage" width="100">
        <template slot-scope="scope">
          <span v-if="!scope.row.active.carriage" @click="handleActive(scope.row,'carriage')">
            <i class="el-icon-edit"></i> {{scope.row.carriage}}</span>
          <el-input v-else placeholder="请输入内容" v-model="scope.row.carriage" clearable oninput="value=value.replace(/[^0-9.]/g,'')"
                    size="small" @blur="handleBlur(scope.row,'carriage')" />
        </template>
      </el-table-column>
      <el-table-column prop="remarks" label="备注" slot="remarks" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="!scope.row.active.remarks" @click="handleActive(scope.row,'remarks')">
            <i class="el-icon-edit"></i> {{scope.row.remarks}}
          </span>
          <el-input v-else placeholder="请输入备注" size="small" v-model="scope.row.remarks" clearable
                    @blur="handleBlur(scope.row,'remarks')" />
        </template>
      </el-table-column>
      <el-table-column label="操作" slot="opt" width="100" fixed="right">
        <template slot-scope="scope">
          <template v-if="!scope.row._all">
            <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)" v-if="perms[`${curd}:update`]||false">编辑
            </el-tag>
            <!-- <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除
            </el-tag> -->
          </template>
          <template v-else>
            <el-tag class="opt-btn" color="#33CAD9" @click="handleSave(scope.row)" v-if="perms[`${curd}:update`]||false">保存
            </el-tag>
            <el-tag class="opt-btn" color="#FF726B" @click="handleCancel(scope.row)" v-if="perms[`${curd}:delete`]||false">取消
            </el-tag>
          </template>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="date" label-width="140px">
                    <date-select v-model="entityForm.date" clearable></date-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="品名" prop="name" label-width="140px">
                    <el-input v-model="entityForm.name" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="收货单位" prop="receiverName">
                    <el-input v-model="entityForm.receiverName" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">数据信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="合同编号" prop="contractCode">
                    <el-input v-model="entityForm.contractCode" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同数量" prop="amount">
                    <el-input v-model.number="entityForm.amount" oninput="value=value.replace(/[^\d]/g,'')" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="剩余数量" prop="amountLeft">
                    <el-input v-model.number="entityForm.amountLeft" oninput="value=value.replace(/[^\d]/g,'')" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤价" prop="truckCount">
                    <el-input v-model="entityForm.price" oninput="value=value.replace(/[^0-9.]/g,'')" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">数据信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="车数" prop="truckCount">
                    <el-input v-model.number="entityForm.truckCount" oninput="value=value.replace(/[^\d]/g,'')" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="原发数" prop="sendWeight" label-width="140px">
                    <el-input v-model="entityForm.sendWeight" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="实收数" prop="receiveWeight" label-width="140px">
                    <el-input v-model="entityForm.receiveWeight" oninput="value=value.replace(/[^\d.]/g,'')" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="途耗" prop="wayCost">
                    <el-input v-model="entityForm.wayCost" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">其他信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
    <export-excel v-if="exportExcelDialog" :exportExcelDialogVisible.sync="exportExcelDialog" :traitSettings="excel.traitSettings"
                  :exportLabelMap="excel.exportHeaderMap" :entityForm="model.tableOption">
      <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" :loading="scope.uploading.state"
                 @click="updateHandler(scope)" slot="handler" slot-scope="scope" :disabled="!scope.settings.data.length">上传
      </el-button>
    </export-excel>
    <el-dialog top="5vh" title="煤价趋势" :visible.sync="priceChartsDialog" :before-close="handleCloseCharts">
      <echarts :option="priceOption" v-if="showCharts" height="400px" />
      <el-empty description="当前没有数据可以对比" v-else />
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/invoicing/sellOut'
// import { sellOutOption } from '@/const'
const option = {
  animation: false,
  color: ['#FF726B'],
  tooltip: {
    trigger: 'axis',
    point: [-30, -30],
    backgroundColor: '#ccc',
    padding: [5, 20, 5, 20],
  },

  grid: {
    top: '50px',
    left: '50px',
    right: '',
    bottom: '50px',
  },
  legend: {
    right: 20,
    itemGap: 30,
    padding: [10, 10, 10, 10],
    align: 'center',
    data: [],
  },
  xAxis: {
    axisLine: {
      lineStyle: { color: '#E3F0FF' },
    },
    axisLabel: {
      color: '#D0D0D0', // 刻度标签文字的颜色
    },
    data: ['7:00', '8:00', '9:00', '10:00', '11:00', '12:00', '13:00'],
  },
  yAxis: {
    axisLine: { lineStyle: { color: '#E3F0FF' } },
    axisLabel: { color: '#D0D0D0' },
    splitLine: { show: true, lineStyle: { color: '#E3F0FF' } },
  },
  series: [
    {
      name: '价格',
      type: 'line',
      itemStyle: { normal: { label: { show: true } } },
      data: [200, 100, 400, 200, 200, 100],
    },
  ],
}
export default {
  name: 'sellOut',
  mixins: [Mixins],
  data() {
    return {
      curd: 'sellOut',
      model: Model,
      textMap: { update: '编辑', create: '新建' },
      dialogStatus: 'create',
      filterOption: Model.filterOption([
        {
          prop: 'receiverName',
          filter: 'filter_LIKES_receiverName',
          props: { placeholder: '请输入收货单位', clearable: true },
        },
        {
          prop: 'name',
          filter: 'filter_LIKES_name',
          component: 'select-name',
          props: { placeholder: '请选择品名', clearable: true },
        },
      ]),
      entityForm: { ...Model.model },
      excel: Model.getImportConfig(),
      exportExcelDialog: false,
      priceChartsDialog: false,
      priceOption: JSON.parse(JSON.stringify(option)),
      showCharts: false,
      actions: [],
    }
  },
  created() {
    this.permsAction(this.curd)
  },
  methods: {
    importExcel(unknown, type) {
      this.exportExcelDialog = true
    },
    async updateHandler(scope) {
      const data = [...scope.settings.data]
      const importList = data.map((v) => {
        const item = {}
        for (const key of Object.keys(this.entityForm)) {
          item[key] = v[key] !== undefined ? v[key] : ''
        }
        return item
      })
      const text = JSON.stringify(importList)
      scope.uploading.state = true
      const res = await Model.saveList({ text })
      scope.uploading.state = false
      if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      if (res.data.length) {
        scope.settings.data = res.data.map((v, i) => v)
      } else {
        this.$message({ showClose: true, message: '导入成功', type: 'success' })
        scope.settings.data = []
        this.getList()
      }
      this.exportExcelDialog = false
    },

    async handlePriceColumn(row) {
      const dataList = this.$refs[this.curd].dataList
      if (dataList.length && dataList.every((item) => dataList[0].receiverName === item.receiverName)) {
        const priceList = [...dataList]
        if (!priceList.length) {
          this.showCharts = false
          this.priceChartsDialog = true
          return false
        }
        priceList.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        let dateList = []
        let valueList = []
        priceList.forEach((item) => {
          const dateIndex = dateList.findIndex((val) => val === item.date)
          if (dateIndex === -1) {
            dateList.push(item.date)
            valueList.push(item.price * 1)
          } else {
            valueList[dateIndex] += item.price * 1
          }
        })
        this.priceOption.xAxis.data = dateList
        this.priceOption.series[0].data = valueList
        this.showCharts = true
        this.priceChartsDialog = true
      } else {
        this.$message({ showClose: true, message: '请先筛选相同的销售单位后再查看价格趋势图!', type: 'warning' })
      }
    },
    handleCloseCharts() {
      this.priceOption = JSON.parse(JSON.stringify(option))
      this.showCharts = false
      this.priceChartsDialog = false
    },
    formatChartsData(records) {
      const priceList = []
      const dateList = []
      records.forEach((item) => {
        const index = dateList.findIndex((v) => v === item.date)
        if (index !== -1) {
          priceList[index] = priceList[index] * 1 + item.price * 1
        } else {
          dateList.push(item.date)
          priceList.push(item.price)
        }
      })
      priceList.reverse()
      dateList.reverse()

      return { priceList, dateList }
    },
    getWeeks({ type = '', date = new Date(), days = 6, fmt = 'yyyy-MM-dd' }) {
      const day = 24 * 60 * 60 * 1000
      return type === 'add'
        ? new Date(new Date(date).getTime() + day * days).Format(fmt)
        : new Date(new Date(date).getTime() - day * days).Format(fmt)
    },
    changeAll(row) {
      row._all = !row._all
    },
    handleUpdate(row) {
      this.changeAll(row)
      this.changeCurrent(row, true) // 编辑全部
    },
    handleSave(row) {
      this.changeAll(row)
      this.operationRow(row, true) // 取消时关闭所有的active
    },
    handleCancel(row) {
      row._all = false
      this.operationRow(row, false) // 取消时关闭所有的active
    },

    changeCurrent(row, target) {
      if (target === true) {
        for (const [k, v] of Object.entries(row.active)) {
          row.active[k] = !v
        }
      } else if (target === false) {
        for (const key of Object.keys(row.active)) {
          row.active[key] = false
        }
      } else {
        row.active[target] = !row.active[target]
      }
    },
    handleBlur(row, target) {
      this.changeCurrent(row, target) // 失去事件触发改变时关闭target的active
      const status = this.isOpenSave(row) // 判断当前事件是否全部关闭
      if (status) return // 为真说明还有blur未关闭
      for (const key of Object.keys(row.bak)) {
        if (row.bak[key] + '' !== row[key] + '') return // 判断两个值是否不相等 不相等则直接返回
      }
      setTimeout(() => {
        row._all = false
      }, 300) // 如果上述条件都成立则开回到最初的模式
    },
    isOpenSave(row) {
      let status = false
      for (const v of Object.values(row.active)) {
        if (v) {
          status = true
          break
        }
      }
      return status
    },
    /**
     * 双击时改变为input狂
     * @target string|true 用于判断是操作一个还是多个
     */
    handleActive(row, target) {
      row._all = true
      this.changeCurrent(row, target)
    },
    /**
     * 保存或取消
     * @action {true:false} true为保存逻辑，false为取消逻辑
     */
    async operationRow(row, action) {
      if (action) {
        const isChangePrice = row.price !== row.bak.price
        const remarksTemplate = `价格已更新,上次价格为:${row.bak.price},原发数为:${row.sendWeight},实收数为:${row.receiveWeight}`
        if (isChangePrice) {
          if (!row.remarks) {
            row.remarks = remarksTemplate
          } else if (/^价格已更新,上次价格为:\d+,原发数为:\d+,实收数为:\d+$/.test(row.remarks)) {
            row.remarks = remarksTemplate
          }
        }
        try {
          await this.model.save({ ...row })
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        } catch (e) {
          this.$message({ message: '保存失败', type: 'warning' })
        }
        this.getList()
      } else {
        for (const key of Object.keys(row.bak)) {
          if (action) {
            // ---------------------- 可写接口
            row.bak[key] = row[key] // 保存就时把当前key赋给bak
          } else {
            row[key] = row.bak[key] // 取消就是bak值赋给当前key
          }
          row.active[key] = false // 更具Key 将active都初始化
        }
      }
    },
  },
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
::v-deep .el-input--small .el-input__inner {
  // background: transparent;
  border: 1px solid #d8d8d8;
  // color: #2f79e8;
}
</style>