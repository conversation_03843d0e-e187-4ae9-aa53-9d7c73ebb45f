<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" :showSummary='true'>
      <!-- fixed='left' -->
      <el-table-column label="品名" slot="name" width="130" prop="name" align="center">
        <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{scope.row.name}}</span></template>
      </el-table-column>

      <el-table-column label="途耗" slot="wayCost" prop="wayCost">
        <template slot-scope="scope">{{scope.row.wayCost}}%</template>
      </el-table-column>
      <el-table-column label="途耗累计" slot="wayCostAcc" prop="wayCostAcc">
        <template slot-scope="scope">{{scope.row.wayCostAcc}}%</template>
      </el-table-column>
      <el-table-column label="操作" slot="opt" width="100" fixed="right">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" v-if="perms[`${curd}:update`]||false" @click="handleUpdate(scope.row)">编辑
          </el-tag>
          <el-tag class="opt-btn" color="#595EC9" v-if="perms[`${curd}:delete`]||false" @click="handleDel(scope.row)">删除</el-tag>
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :rules="rules" :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-position="top"
               :disabled="dialogStatus==='watch'" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基础信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="entityForm.date" clearable :disabled="dialogStatus==='update'" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同" prop="contractId">
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 filterable :disabled="dialogStatus==='update'" clearable placeholder="请选择合同" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="品名" prop="name">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                 disabled />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤种" prop="coalType">
                    <category-select :value.sync="entityForm.coalType" type="COAL_TYPE" disabled />
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="发货单位" prop="senderName">
                    <el-select v-model="supplierEntity.active" placeholder="请选择发货单位" clearable @change="handleSupplier">
                      <el-option v-for="item in supplierEntity.options" :key="item.value" :label="item.label" :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="收货单位" prop="receiverName">
                    <el-input v-model="entityForm.receiverName" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col />
                <el-col />
                <!-- <el-col /> -->
                <!-- <el-col>
                  <el-form-item label="测试品名">
                    <ProductSelect :value.sync="values" @change="handleProductChange" />
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="测试发货">
                    <SupplierSelect :value.sync="SupplierSelect" @change="handleSupplierChange" />
                  </el-form-item>
                </el-col> -->
                <!-- <el-col>
                  <el-form-item label="测试合约">
                    <CustomerContract :value.sync="contractActive" @change="handleContractChange" />
                  </el-form-item>
                </el-col> -->
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">数据信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="车数" prop="truckCount">
                    <el-input v-model="entityForm.truckCount" :oninput="field.int" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="原发数" prop="sendWeight">
                    <el-input v-model="entityForm.sendWeight" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="实收数" prop="receiveWeight">
                    <el-input v-model="entityForm.receiveWeight" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>

                  <el-form-item label="途耗" prop="wayCost">
                    <el-input v-model="entityForm.wayCost" clearable disabled>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="车数累计" prop="truckCountAcc">
                    <el-input v-model="entityForm.truckCountAcc" disabled />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="原发累计(本月)" prop="sendWeightAcc">
                    <el-input v-model="entityForm.sendWeightAcc" disabled>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="实收累计(本月)" prop="receiveWeightAcc">
                    <el-input v-model="entityForm.receiveWeightAcc" disabled>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="途耗累计" prop="wayCostAcc">
                    <el-input v-model="entityForm.wayCostAcc" disabled>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">人员信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="在岗人" prop="stationMan">
                    <el-input v-model="entityForm.stationMan" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="交班人" prop="transferMan">
                    <el-input v-model="entityForm.transferMan" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="接班人" prop="nextMan">
                    <el-input v-model="entityForm.nextMan" clearable />
                  </el-form-item>
                </el-col>
                <el-col />
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" v-if="perms[`${curd}:update`]||false"
                   @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
    <export-excel v-if="excelConfig.dialog" :exportExcelDialogVisible.sync="excelConfig.dialog"
                  :traitSettings="excelConfig.excel.traitSettings" :exportLabelMap="excelConfig.excel.exportHeaderMap"
                  :entityForm="model.tableOption">
      <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" :loading="scope.uploading.state"
                 @click="handleUpload(scope)" slot="handler" slot-scope="scope" :disabled="!scope.settings.data.length">上传
      </el-button>
    </export-excel>
  </div>
</template>

<script>
import { chooseContract } from '@/api/quality'
import supplierModel from '@/model/user/supplier'
import productModel from '@/model/product/productList'
import Mixins from '@/utils/mixins'
import Model from '@/model/invoicing/weightHouseIn'
import { createMemoryField } from '@/utils/index'

export default {
  name: 'weightHouseIn',
  mixins: [Mixins],
  data() {
    return {
      curd: 'weightHouseIn',
      model: Model,
      filterOption: Model.filterOption([
        {
          prop: 'senderName',
          filter: 'filter_LIKES_senderName',
          props: { placeholder: '请输入发货单位', clearable: true }
        },
        {
          prop: 'name',
          filter: 'filter_LIKES_name',
          component: 'select-name',
          props: { placeholder: '请选择品名', clearable: true }
        }
      ]),
      entityForm: { ...Model.model },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        name: { required: true, message: '请输入品名', trigger: 'blur' },
        productId: { required: true, message: '请输入品名', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' },
        senderName: { required: true, message: '请选择发货单位', trigger: 'blur' },
        receiverName: { required: true, message: '请输入收货单位', trigger: 'blur' },
        coalType: { required: true, message: '请选择煤种类型', trigger: 'change' },
        truckCount: { required: true, message: '请输入车数', trigger: 'blur' },
        sendWeight: { required: true, message: '请输入原发数', trigger: 'blur' },
        receiveWeight: { required: true, message: '请输入实收数', trigger: 'blur' },
        wayCost: { required: true, message: '请输入途耗', trigger: 'blur' },
        amountLeft: { required: true, trigger: 'blur' },
        truckCountAcc: { required: true, message: '请输入车数累计', trigger: 'blur' },
        sendWeightAcc: { required: true, message: '请输入原发累计(本月)', trigger: 'blur' },
        receiveWeightAcc: { required: true, message: '请输入实收累计(本月)', trigger: 'blur' },
        wayCostAcc: { required: true, message: '请输入途耗累计', trigger: 'blur' }
      },
      // excel配置
      excelConfig: { excel: Model.getImportConfig(), dialog: false },
      // 记忆字段
      memoryEntity: { fields: {}, triggered: false },
      // 合同
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      // 供应商
      supplierEntity: { options: [], active: [] },
      // 品名
      nameEntity: { options: [], active: '' },
      // 累计字段
      entityFormAcc: { receiveWeightAcc: 0, sendWeightAcc: 0, truckCountAcc: 0 },
      values: '',
      SupplierSelect: '',
      contractActive: '1483699287445381121'
    }
  },

  created() {
    this._requestInit()
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'name',
        'senderName',
        'receiverName',
        'receiverName',
        'coalType',
        'contractId',
        'contractCode',
        'supplierId',
        'supplierName',
        'productCode',
        'productId'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.permsAction(this.curd)
    // console.log(this.actions, 'actions')
  },

  beforeRouteEnter(to, from, next) {
    // console.log(to, from)
    next()
  },
  methods: {
    // handleProductChange(product) {
    //   let { name, id: productId, code: productCode, coalCategory: coalType } = product
    //   this.batchUpdateFields({ name, productId, productCode, coalType })
    // },
    // handleSupplierChange(supplier) {
    //   const { id: supplierId, name: supplierName } = supplier
    //   this.batchUpdateFields({ supplierId, supplierName, senderName: supplierName }) // 同步数据 顺便同步原发
    // },
    // handleContractChange(contract) {
    //   console.log(contract, 1)
    // },

    /**
     * @fields <entityForm>
     */
    batchUpdateFields(fields = {}) {
      const fieldsKeys = Object.keys(fields)
      for (const key of Object.keys(this.entityForm)) {
        if (fieldsKeys.includes(key)) {
          this.entityForm[key] = fields[key]
        }
      }
    },

    _requestInit() {
      this.getContract()
      this.getSupplier()
      this.getName()
    },
    /**
     * 保存取消时会触发初始化数据
     */
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.contractEntity.active = []
        this.supplierEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },

    handleNameEntity(val) {
      this.entityForm.name = val
    },
    // 获取供应商选择接口
    async getSupplier() {
      try {
        let options = []
        const { data } = await await supplierModel.page({ size: 1000 })
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        this.supplierEntity.options = options
      } catch (e) {}
    },
    // 获取合同选择接口
    async getContract() {
      try {
        const { data } = await chooseContract()
        this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'displayName', supplierId: 'id' } })
      } catch (error) {}
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
        })
      } catch (error) {}
    },
    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    // 合同改变同步
    handleSupplier({ value, label }) {
      // this.entityForm.supplierId = value
      // this.entityForm.supplierName = label
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label }
    },

    paramsNumber(paramsAcc) {
      for (const [k, v] of Object.entries(paramsAcc)) {
        paramsAcc[k] = !Number.isNaN(v * 1) ? v * 1 : 0
      }
      return paramsAcc
    },

    importExcel(unknown, type) {
      this.excelConfig.dialog = true
    },
    async handleUpload(scope) {
      const data = [...scope.settings.data]
      const importList = data.map((v) => {
        const item = {}
        for (const key of Object.keys(this.entityForm)) {
          item[key] = v[key] !== undefined ? v[key] : ''
        }
        return item
      })
      const text = JSON.stringify(importList)
      scope.uploading.state = true
      const res = await Model.saveList({ text })
      scope.uploading.state = false
      if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      if (res.data.length) {
        scope.settings.data = res.data.map((v, i) => v)
      } else {
        this.$message({ showClose: true, message: '导入成功', type: 'success' })
        scope.settings.data = []
        this.getList()
      }
    },
    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    // 更新累计Acc
    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      const paramsAcc = await this.getAccValues({ date, contractId })
      Object.keys(paramsAcc).forEach((key) => {
        this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
        this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      })
      this.computeWayCostAcc()
    },
    // 处理receiveWeight, sendWeight 值处理成涂好
    updateWayCost() {
      let { receiveWeight, sendWeight } = this.entityForm
      receiveWeight = receiveWeight * 1
      sendWeight = sendWeight * 1
      if (receiveWeight === '' || sendWeight === '') return false // 数据为空时不计算
      if (receiveWeight === 0 && sendWeight === 0) return (this.entityForm.wayCost = 0.0)
      // 途耗累计=实收累计-原发累计/原发累计
      this.entityForm.wayCost = (((sendWeight - receiveWeight) / sendWeight) * 100).toFixed(2)
    },
    // 计算涂耗累计Acc
    computeWayCostAcc() {
      let { receiveWeightAcc, sendWeightAcc } = this.entityForm
      receiveWeightAcc = receiveWeightAcc * 1
      sendWeightAcc = sendWeightAcc * 1
      let wayCostAcc = (((sendWeightAcc - receiveWeightAcc) / sendWeightAcc) * 100).toFixed(2) * 1
      this.entityForm.wayCostAcc = !Number.isNaN(wayCostAcc) ? wayCostAcc : 0
    },
    // 计算累计方法
    computeAcc(accName, value) {
      if (accName === 'truckCountAcc') {
        this.entityForm[accName] = this.entityFormAcc[accName] * 1 + value * 1
        return
      }
      this.entityForm[accName] = (this.entityFormAcc[accName] * 1 + value * 1).toFixed(2)
    }
  },
  watch: {
    'entityForm.name'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.entityForm.coalType = item.coalCategory || ''
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
      }
    },
    dialogFormVisible(v) {
      if (this.dialogStatus === 'create' && v) {
        this.updateAcc()
      } // 用于再打开时触发合同Acc数据
    },
    'entityForm.receiveWeight'(v) {
      this.computeAcc('receiveWeightAcc', v)
      this.updateWayCost()
    },
    'entityForm.sendWeight'(v) {
      this.computeAcc('sendWeightAcc', v)
      this.updateWayCost()
    },
    'entityForm.truckCount'(v) {
      this.computeAcc('truckCountAcc', v)
    },
    'entityForm.date'() {
      this.updateAcc()
    },
    'entityForm.wayCost'() {
      this.computeWayCostAcc()
    },
    'entityForm.contractId'(id) {
      this.updateAcc()
      if (this.entityForm.date && id) {
        this.updateAcc(this.entityForm.date, id)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'entityForm.supplierId'(id) {
      this.contractActive = id
      const item = this.supplierEntity.options.find((item) => item.value === id)
      if (item) {
        this.supplierEntity.active = item
      } else {
        this.supplierEntity.active = []
      }
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.name = form.productName
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.supplierId
      this.entityForm.senderName = form.secondParty
      this.entityForm.receiverName = form.firstParty
      const item = this.contractEntity.options.find((v) => v.supplierId === value[0])
      if (!item) return
      this.entityForm.supplierId = item.supplierId
      this.entityForm.supplierName = item.supplierName
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
</style>
