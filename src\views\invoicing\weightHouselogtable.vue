<template>
  <div class="app-container">
    <filter-table class="custom-filter-table" :actions="actions" :options="filterOption" :showAdd="perms[`${curd}:save`]||false"
                  :showImport="perms[`${curd}:addimport`]||false" :showRefresh="perms[`${curd}:Refresh`]||false"
                  :showWeightRoomImport="true"
                  @Refresh="Refreshfn" @add="handleCreate" @filter="handleFilter" @import="handleImpiort"
                  @reset="handleFilterReset" @importProcure="importprocure(null, 'procure')" 
                  @importSale="importsale(null, 'sale')" @importProcureDetail="importExcel(null, 'procureimport')"
                  @importSaleDetail="importSaleExcel(null, 'saleimport')" />
    
    <s-curd :ref="curd" :list-query="listQuery" :model="model" :name="curd" :showSummary="true" issync="" otherHeight="170">

      <el-table-column slot="firstWeightDate" align="center" label="一次计量时间" prop="firstWeightDate" sortable width="180">
        <template slot-scope="scope">
          {{ scope.row.firstWeightDate }}
        </template>
      </el-table-column>
      <el-table-column slot="secondWeightDate" align="center" label="二次计量时间" prop="secondWeightDate" width="180">
        <template slot-scope="scope">
          {{ scope.row.secondWeightDate }}
        </template>
      </el-table-column>
      <el-table-column slot="autoBH" align="center" label="磅单号" prop="autoBH" width="120">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.autoBH" effect="light" placement="top" popper-class="popper">
            <span>{{ scope.row.autoBH }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column label="同步状态" slot="sync" prop="sync" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.sync=='Y'">是</span>
          <span v-if="scope.row.sync=='N'">否</span>
        </template>
      </el-table-column> -->
      <el-table-column slot="type" align="center" label="过磅类型" prop="type">
        <template slot-scope="scope">
          <span v-if="scope.row.type==1">销售</span>
          <span v-if="scope.row.type==2">采购</span>
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog ref="dialogStatus" :before-close="handleCloseV" :close-on-click-modal="false" :close-on-press-escape="false"
               :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisibleV" top="5vh" width="880px">
      <el-form ref="changeForm" :model="entityFormV" :show-message="true" :status-icon="true" label-position="top">
        <el-row :gutter="50" type="flex">
          <el-col>
            <el-form-item label="日期" prop="date">
              <date-select v-model="entityFormV.date" clearable></date-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="类型" prop="type">
              <el-select v-model="devType" placeholder="请选择类型" @change="selectChanged">
                <!-- <el-option label="购进" value="shanghai"></el-option>
                <el-option label="销售" value="beijing"></el-option> -->
                <el-option v-for="item in devTypes" :key="item" :label="item" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <div style="margin-top:20px">
              <el-button v-if="perms[`${curd}:Refresh`]||false" :loading="changeeFormLoading" class="dialog-footer-btns"
                         type="primary" @click="RefreshForm">刷新
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>

    <export-excel v-if="excelConfig.dialog" ref="excelref" :entityForm="entityForm"
                  :exportExcelDialogVisible.sync="excelConfig.dialog" :sLoading="sLoading" :tableOption="model.tableOption"
                  :traitSettings="tableOption" optName="create" @setSubmittext="setSubmittext">
      <!-- <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" :loading="scope.uploading.state"
                 @click="handleUpload(scope)" slot="handler" slot-scope="scope" :disabled="!scope.settings.data.length">上传
      </el-button> -->
    </export-excel>
  </div>
</template>
<script>
import { chooseContract } from '@/api/quality'
import supplierModel from '@/model/user/supplier'
import productModel from '@/model/product/productList'
import Mixins from '@/utils/mixins'
import Model from '@/model/invoicing/weightHouselogtable'
import { createMemoryField } from '@/utils/index'
import { List } from 'echarts/lib/export'
import { listCustomerName, listProductName, listSupplierName } from '@/api/common/listAllSimple'

const defaultDate = new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd')
export default {
  name: 'weightHouselogtable',
  mixins: [Mixins],
  data() {
    return {
      curd: 'weightHouselogtable',
      model: Model,
      devType: '购进',
      devTypes: ['购进', '销售'],
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'date',
            component: 'date-select',
            filter: 'filter_EQS_date',
            props: {
              placeholder: '日期',
              clearable: false
            }
          },
          // {
          //   prop: 'startSecondWeightDate',
          //   component: 'date-select',
          //   filter: 'filter_GES_secondWeightDate',
          //   title: '开始日期',
          //   width: '180px',
          //   props: {
          //     type: 'datetime',
          //     formats: 'yyyy-MM-dd HH:mm:ss',
          //     format: 'yyyy-MM-dd HH:mm:ss',
          //     placeholder: '二次计量开始时间',
          //     defaultTime: '00:00:00',
          //     clearable: false
          //   }
          // },
          // {
          //   prop: 'endSecondWeightDate',
          //   component: 'date-select',
          //   filter: 'filter_LES_secondWeightDate',
          //   title: '结束日期',
          //   width: 180,
          //   props: {
          //     type: 'datetime',
          //     formats: 'yyyy-MM-dd HH:mm:ss',
          //     format: 'yyyy-MM-dd HH:mm:ss',
          //     placeholder: '二次计量结束时间',
          //     defaultTime: '23:59:59',
          //     clearable: false
          //   }
          // },
          // {
          //   prop: 'cargoType',
          //   filter: 'filter_LIKES_cargoType',
          //   title: '货物类型',
          //   width: 100,
          //   props: {
          //     placeholder: '货物类型',
          //     clearable: true,
          //   },
          // },
          {
            prop: 'companyFullName',
            filter: 'filter_LIKES_companyFullName',
            title: '企业名称',
            props: {
              placeholder: '企业名称',
              clearable: true
            }
          },
          {
            prop: 'plateNumber',
            filter: 'filter_LIKES_plateNumber',
            title: '车牌号',
            props: {
              placeholder: '车牌号',
              clearable: true
            }
          },
          {
            prop: 'type',
            filter: 'filter_LIKES_type',
            title: '过磅类型',
            component: 'select-type',
            props: { placeholder: '请选择过磅类型' }
          },
          {
            prop: 'sync',
            filter: 'filter_EQS_sync',
            component: 'dict-select',
            props: {
              name: 'select',
              type: 'sync_type',
              placeholder: '同步状态',
              clearable: true
            }
          },
          {
            prop: 'cargoType',
            filter: 'filter_LIKES_cargoType',
            component: 'select-Sender-Name',
            props: { placeholder: '请选择物料', changeFilter: true, clearable: true, type: 'cargoType' }
          },
          {
            prop: 'originalTon',
            filter: 'filter_LIKES_originalTon',
            title: '原发净重',
            props: {
              placeholder: '原发净重',
              clearable: true
            }
          },
          {
            prop: 'weight',
            filter: 'filter_LIKES_weight',
            title: '实收净重',
            props: {
              placeholder: '实收净重',
              clearable: true
            }
          }
        ]
      },
      entityForm: { ...Model.model },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        name: { required: true, message: '请输入品名', trigger: 'blur' },
        productId: { required: true, message: '请输入品名', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' },
        senderName: { required: true, message: '请选择发货单位', trigger: 'blur' },
        receiverName: { required: true, message: '请输入收货单位', trigger: 'blur' },
        coalType: { required: true, message: '请选择煤种类型', trigger: 'change' },
        truckCount: { required: true, message: '请输入车数', trigger: 'blur' },
        sendWeight: { required: true, message: '请输入原发数', trigger: 'blur' },
        receiveWeight: { required: true, message: '请输入实收数', trigger: 'blur' },
        wayCost: { required: true, message: '请输入途耗', trigger: 'blur' },
        amountLeft: { required: true, trigger: 'blur' },
        truckCountAcc: { required: true, message: '请输入车数累计', trigger: 'blur' },
        sendWeightAcc: { required: true, message: '请输入原发累计(本月)', trigger: 'blur' },
        receiveWeightAcc: { required: true, message: '请输入实收累计(本月)', trigger: 'blur' },
        wayCostAcc: { required: true, message: '请输入途耗累计', trigger: 'blur' }
      },
      // excel配置
      excelConfig: { excel: Model.getImportConfig(), dialog: false },
      sLoading: false,
      // 记忆字段
      memoryEntity: { fields: {}, triggered: false },
      // 供应商
      supplierEntity: { options: [], active: [] },
      // 品名
      nameEntity: { options: [], active: '' },
      // 累计字段
      entityFormAcc: { receiveWeightAcc: 0, sendWeightAcc: 0, truckCountAcc: 0, },
      values: '',
      SupplierSelect: '',
      contractActive: '1483699287445381121',
      dialogFormVisibleV: false,
      changeeFormLoading: false,
      entityFormV: {
        date: new Date(new Date().getTime()).Format('yyyy-MM-dd')
      },
      importType: '',
      tableOption: {
        showPage: true,
        columns: [
          {
            label: '磅单号',
            title: '磅单号',
            prop: 'autoBH',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '供应商',
            title: '供应商',
            type: 'text',
            prop: 'supplierName',
            validator: /[\s\S]/,
            allowEmpty: false,
            align: 'center'
          },
          {
            label: '购货单位',
            title: '购货单位',
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'customerName'
          },
          {
            label: '物料名称',
            title: '物料名称',
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'cargoType'
          },
          {
            label: '车号',
            title: '车号',
            type: 'text',
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'plateNumber'
          },
          {
            label: '原发净重',
            title: '原发净重',
            type: 'numeric', //数值
            validator: /[\s\S]/,
            allowEmpty: false,
            prop: 'originalTon'
          },
          {
            label: '毛重',
            title: '毛重',
            allowEmpty: false,
            validator: /[\s\S]/,
            type: 'numeric', //数值
            prop: 'secondWeight'
          },
          {
            label: '皮重',
            title: '皮重',
            type: 'numeric', //数值
            prop: 'firstWeight',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '净重',
            title: '净重',
            type: 'numeric', //数值
            prop: 'weight',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '磅差',
            title: '磅差',
            validator: /[\s\S]/,
            type: 'numeric', //数值
            prop: 'weightDiffNum',
            allowEmpty: false
          },
          {
            label: '盈余',
            title: '盈余',
            type: 'numeric', //数值
            prop: 'surplus',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '一次计量时间',
            title: '一次计量时间',
            type: 'date',
            validator: /[\s\S]/,
            correctFormat: true,
            dateFormat: 'YYYY-MM-DD HH:mm:ss',
            prop: 'firstWeightDate',
            trimWhitespace: false,
            allowEmpty: false
          },
          {
            label: '二次计量时间',
            title: '二次计量时间',
            trimWhitespace: false,
            validator: /[\s\S]/,
            correctFormat: true,
            type: 'date',
            dateFormat: 'YYYY-MM-DD HH:mm:ss',
            prop: 'secondWeightDate',
            allowEmpty: false
          },
          {
            label: '装车时间',
            title: '装车时间',
            validator: /[\s\S]/,
            type: 'date',
            dateFormat: 'YYYY-MM-DD HH:mm:ss',
            prop: 'loadDate',
            allowEmpty: false
          },
          {
            label: '司机姓名',
            title: '司机姓名',
            type: 'text',
            validator: /[\s\S]/,
            prop: 'driver',
            allowEmpty: false
          },
          {
            label: '司机手机号',
            title: '司机手机号',
            validator: /[\s\S]/,
            type: 'numeric', //数值
            prop: 'driverMobile'
          },
          {
            label: '司机身份证',
            title: '司机身份证',
            validator: /[\s\S]/,
            type: 'text',
            prop: 'driverIdNo'
          },
          {
            label: '备注',
            type: 'text',
            title: '备注',
            prop: 'remarks'
            //  validator (val, callback) {
            //   if(that.util.isEmpty(val)) return callback(true)
            //   let pattern = /^.{0,200}$/
            //   if(pattern.test(val)) return callback(true)
            //   return callback(false)
            // }
          }
        ]
      },
      actions: [
        {
          config: {
            type: 'export-all'
          },
          show: true,
          label: '采购日报导入',
          click: this.importprocure,
          type: 'procure',
          isborder: true
        },
        {
          config: {
            type: 'export-all'
          },
          show: true,
          label: '销售日报导入',
          click: this.importsale,
          type: 'sale',
          isborder: true
        },
        {
          config: {
            type: 'export-all'
          },
          show: true,
          label: '新增采购Excel导入',
          click: this.importExcel,
          type: 'procureimport',
          ismargin: true
        },
        {
          config: {
            type: 'export-all'
          },
          show: true,
          label: '新增销售Excel导入',
          click: this.importSaleExcel,
          type: 'saleimport',
          ismargin: true
        }
      ],
      optName: 'create',
      listProduct: [],
      listSupplierName: [],
      listCustomerName: []
    }
  },

  created() {
    this._requestInit()
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'name',
        'senderName',
        'receiverName',
        'receiverName',
        'coalType',
        'contractId',
        'contractCode',
        'supplierId',
        'supplierName',
        'productCode',
        'productId'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.permsAction(this.curd)
    this.getCoalNameList()
    this.getSupplierNameList()
    this.getCustomerNameList()
  },

  beforeRouteEnter(to, from, next) {
    next()
  },
  methods: {
    async getCoalNameList() {
      try {
        const { data } = await listProductName()
        this.listProduct = data.map(v => v.name)
      } catch (e) {

      }
    },
    async getSupplierNameList() {
      try {
        const { data } = await listSupplierName()
        this.listSupplierName = data.map(v => v.name)
      } catch (e) {

      }
    },
    async getCustomerNameList() {
      try {
        const { data } = await listCustomerName()
        this.listCustomerName = data.map(v => v.name)
      } catch (e) {

      }
    },
    async setSubmittext(fList) {
      let that = this
      fList.forEach((e, index) => {
        if (e.firstWeightDate) {
          var str = e.firstWeightDate
          str = str.replace(/\//g, '-')
          if (str.indexOf(' ') == -1) {
            var timearr = str.replace('', ':').replace(/\:/g, '-').split('-')
            // var timearr = e.secondWeightDate.replace('', ':').replace(/\:/g, '/').split('/')
            var nian = timearr[1]
            var yue = timearr[2]
            var ri = timearr[3].substring(0, 2)
            var shi = timearr[3].slice(2)
            var fen = timearr[4]
            var miao = timearr[5]
            if (miao != undefined) {
              let date = nian + '-' + yue + '-' + ri
              let time = shi + ':' + fen + ':' + miao
              let datetime = date + ' ' + time
              var timeStamp = Date.parse(datetime)
              // 此处时间戳以毫秒为单位
              let ndate = new Date(timeStamp)
              let Year = ndate.getFullYear()
              let Moth = ndate.getMonth() + 1 < 10 ? '0' + (ndate.getMonth() + 1) : ndate.getMonth() + 1
              let Day = ndate.getDate() < 10 ? '0' + ndate.getDate() : ndate.getDate()
              let Hour = ndate.getHours() < 10 ? '0' + ndate.getHours() : ndate.getHours()
              let Minute = ndate.getMinutes() < 10 ? '0' + ndate.getMinutes() : ndate.getMinutes()
              let Sechond = ndate.getSeconds() < 10 ? '0' + ndate.getSeconds() : ndate.getSeconds()
              let GMT = Year + '-' + Moth + '-' + Day + ' ' + Hour + ':' + Minute + ':' + Sechond
              e.firstWeightDate = GMT
            } else {
              let date = nian + '-' + yue + '-' + ri
              let time = shi + ':' + fen + ':00'
              let datetime = date + ' ' + time
              var timeStamp = Date.parse(datetime)
              // 此处时间戳以毫秒为单位
              let ndate = new Date(timeStamp)
              let Year = ndate.getFullYear()
              let Moth = ndate.getMonth() + 1 < 10 ? '0' + (ndate.getMonth() + 1) : ndate.getMonth() + 1
              let Day = ndate.getDate() < 10 ? '0' + ndate.getDate() : ndate.getDate()
              let Hour = ndate.getHours() < 10 ? '0' + ndate.getHours() : ndate.getHours()
              let Minute = ndate.getMinutes() < 10 ? '0' + ndate.getMinutes() : ndate.getMinutes()
              let Sechond = ndate.getSeconds() < 10 ? '0' + ndate.getSeconds() : ndate.getSeconds()
              let GMT = Year + '-' + Moth + '-' + Day + ' ' + Hour + ':' + Minute + ':' + Sechond
              e.firstWeightDate = GMT
            }
          }
        }

        if (e.secondWeightDate) {
          var str = e.secondWeightDate
          str = str.replace(/\//g, '-')
          if (str.indexOf(' ') == -1) {
            var timearr = str.replace('', ':').replace(/\:/g, '-').split('-')
            // var timearr = e.secondWeightDate.replace('', ':').replace(/\:/g, '/').split('/')
            var nian = timearr[1]
            var yue = timearr[2]
            var ri = timearr[3].substring(0, 2)
            var shi = timearr[3].slice(2)
            var fen = timearr[4]
            var miao = timearr[5]
            if (miao != undefined) {
              let date = nian + '-' + yue + '-' + ri
              let time = shi + ':' + fen + ':' + miao
              let datetime = date + ' ' + time
              var timeStamp = Date.parse(datetime)
              // 此处时间戳
              let ndate = new Date(timeStamp)
              let Year = ndate.getFullYear()
              let Moth = ndate.getMonth() + 1 < 10 ? '0' + (ndate.getMonth() + 1) : ndate.getMonth() + 1
              let Day = ndate.getDate() < 10 ? '0' + ndate.getDate() : ndate.getDate()
              let Hour = ndate.getHours() < 10 ? '0' + ndate.getHours() : ndate.getHours()
              let Minute = ndate.getMinutes() < 10 ? '0' + ndate.getMinutes() : ndate.getMinutes()
              let Sechond = ndate.getSeconds() < 10 ? '0' + ndate.getSeconds() : ndate.getSeconds()
              let GMT = Year + '-' + Moth + '-' + Day + ' ' + Hour + ':' + Minute + ':' + Sechond
              e.secondWeightDate = GMT
            } else {
              let date = nian + '-' + yue + '-' + ri
              let time = shi + ':' + fen + ':00'
              let datetime = date + ' ' + time
              var timeStamp = Date.parse(datetime)
              // 此处时间戳
              let ndate = new Date(timeStamp)
              let Year = ndate.getFullYear()
              let Moth = ndate.getMonth() + 1 < 10 ? '0' + (ndate.getMonth() + 1) : ndate.getMonth() + 1
              let Day = ndate.getDate() < 10 ? '0' + ndate.getDate() : ndate.getDate()
              let Hour = ndate.getHours() < 10 ? '0' + ndate.getHours() : ndate.getHours()
              let Minute = ndate.getMinutes() < 10 ? '0' + ndate.getMinutes() : ndate.getMinutes()
              let Sechond = ndate.getSeconds() < 10 ? '0' + ndate.getSeconds() : ndate.getSeconds()
              let GMT = Year + '-' + Moth + '-' + Day + ' ' + Hour + ':' + Minute + ':' + Sechond
              e.secondWeightDate = GMT
            }
          }
        }
      })
      const text = JSON.stringify(fList)
      this.sLoading = true
      //采购
      if (this.importType == 'procureimport') {
        const res = await Model.saveList({ text })
        if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
        // if (res.data.length) {
        //   // scope.settings.data = res.data.map((v, i) => v)
        // } else {
        this.$message({ showClose: true, message: '导入成功', type: 'success' })
        // this.$emit('update:exportExcelDialogVisible', false)
        this.excelConfig.dialog = false
        this.sLoading = false
        this.getList()
        // await this.$refs.excelref.setSubmitData()
        // }
      } else if (this.importType == 'saleimport') {
        //销售
        const res = await Model.savesaleList({ text })
        if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })

        this.$message({ showClose: true, message: '导入成功', type: 'success' })
        // this.$emit('update:exportExcelDialogVisible', false)
        this.excelConfig.dialog = false
        this.sLoading = false
        this.getList()
        // await this.$refs.excelref.setSubmitData()
      } else if (this.importType == 'procure') {
        //采购日报导入
        const res = await Model.importInDailyWeightHouseLog({ text })
        if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
        this.$message({ showClose: true, message: '导入成功', type: 'success' })
        this.excelConfig.dialog = false
        this.getList()
      } else if (this.importType == 'sale') {
        //销售日报导入
        const res = await Model.importOutDailyWeightHouseLog({ text })
        if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
        this.$message({ showClose: true, message: '导入成功', type: 'success' })
        this.excelConfig.dialog = false
        this.getList()
      }
    },

    //批量编辑
    async batchChangeexcele(selectList, type) {
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      const list = this.$refs[this.curd].getSelectRows()
      if (list.length) {
        const contractIdList = [...new Set(list.map((item) => item.supplierId))]
        if (contractIdList.length > 1) return this.$message.warning('每次批量修改只能修改相同供应商的数据')
        this.handleUpdateRows(list, 'addInfoTodo', 'update')
      } else {
        this.$message.warning('最少选择一条数据')
      }
    },
    async batchDeleteexcele(selectList, type) {
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
    },

    // 采购日报导入
    importprocure(unknown, type) {
      this.importType = type
      this.tableOption = {
        columns: [
          {
            label: '日期',
            title: '日期',
            type: 'date',
            validator: /[\s\S]/,
            // dateFormat: 'YYYY年MM月DD日', // 表内显示格式
            // datePickerConfig: {
            //   i18n: {
            //     previousMonth: 'Previous Month',
            //     nextMonth: 'Next Month',
            //     months: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
            //     weekdays: ['日', '一', '二', '三', '四', '五', '六'],
            //     weekdaysShort: ['日', '一', '二', '三', '四', '五', '六']
            //   }
            // },
            dateFormat: 'YYYY-MM-DD',
            allowEmpty: false, // 不允许空值
            // defaultDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'), // 返回当前日期作为默认值
            // 默认日期
            prop: 'date',
            width: 100
          },
          {
            label: '品名',
            title: '品名',
            type: 'autocomplete',
            width: 120,
            source: this.listProduct,
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'cargoType'
          },
          {
            label: '供应商',
            title: '供应商',
            type: 'autocomplete',
            source: this.listSupplierName,
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'supplierName'
          },
          {
            label: '车数',
            title: '车数',
            validator: /[\s\S]/,
            type: 'numeric',
            prop: 'truckCount',
            allowEmpty: false
          },
          {
            label: '原发数',
            title: '原发数',
            prop: 'originalTon',
            type: 'numeric',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '实收数',
            title: '实收数',
            prop: 'weight',
            type: 'numeric',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '亏吨',
            title: '亏吨',
            prop: 'lossNull',
            type: 'numeric',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '结算数',
            title: '结算数',
            prop: 'settlement',
            type: 'numeric',
            validator: /[\s\S]/,
            allowEmpty: false
          }
        ]
      }
      this.sLoading = false
      this.excelConfig.dialog = true
    },
    // 销售日报导入
    importsale(unknown, type) {
      this.importType = type
      this.tableOption = {
        columns: [
          {
            label: '日期',
            title: '日期',
            type: 'date',
            validator: /[\s\S]/,
            dateFormat: 'YYYY-MM-DD',
            prop: 'date',
            allowEmpty: false,
            width: 100
          },
          {
            label: '品名',
            title: '品名',
            type: 'autocomplete',
            width: 120,
            source: this.listProduct,
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'cargoType'
          },
          {
            label: '客户',
            title: '客户',
            type: 'autocomplete',
            source: this.listCustomerName,
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'customerName'
          },
          {
            label: '车数',
            title: '车数',
            validator: /[\s\S]/,
            type: 'numeric',
            prop: 'truckCount',
            allowEmpty: false
          },
          {
            label: '原发数',
            title: '原发数',
            prop: 'originalTon',
            type: 'numeric',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '实收数',
            title: '实收数',
            prop: 'weight',
            type: 'numeric',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '亏吨',
            title: '亏吨',
            prop: 'lossNull',
            type: 'numeric',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '结算数',
            title: '结算数',
            prop: 'settlement',
            type: 'numeric',
            validator: /[\s\S]/,
            allowEmpty: false
          }
        ]
      }
      this.sLoading = false
      this.excelConfig.dialog = true
    },

    importExcel(unknown, type) {
      //采购导入
      this.importType = type
      this.tableOption = {
        columns: [
          {
            label: '过磅单号',
            title: '过磅单号',
            prop: 'autoBH',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '订单名称',
            title: '订单名称',
            type: 'text',
            prop: 'orderName',
            validator: /[\s\S]/,
            allowEmpty: false,
            align: 'center'
          },
          {
            label: '发货单位',
            title: '发货单位',
            type: 'text',
            prop: 'supplierName',
            validator: /[\s\S]/,
            allowEmpty: false,
            align: 'center'
          },
          {
            label: '收货单位',
            title: '收货单位',
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'customerName'
          },
          {
            label: '物料',
            title: '物料',
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'cargoType'
          },
          {
            label: '车号',
            title: '车号',
            type: 'text',
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'plateNumber'
          },
          {
            label: '卸车方式',
            title: '卸车方式',
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'unloadMode'
          },
          {
            label: '司机',
            title: '司机',
            type: 'text',
            validator: /[\s\S]/,
            prop: 'driver',
            allowEmpty: false
          },
          {
            label: '一次计量时间',
            title: '一次计量时间',
            type: 'date',
            validator: /[\s\S]/,
            dateFormat: 'YYYY-MM-DD HH:mm:ss',
            prop: 'firstWeightDate',
            trimWhitespace: false,
            allowEmpty: false
          },
          {
            label: '二次计量时间',
            title: '二次计量时间',
            trimWhitespace: false,
            validator: /[\s\S]/,
            type: 'date',
            dateFormat: 'YYYY-MM-DD HH:mm:ss',
            prop: 'secondWeightDate',
            allowEmpty: false
          },
          {
            label: '毛重',
            title: '毛重',
            type: 'numeric', //数值
            prop: 'firstWeight',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '皮重',
            title: '皮重',
            allowEmpty: false,
            validator: /[\s\S]/,
            type: 'numeric', //数值
            prop: 'secondWeight'
          },
          {
            label: '实收净重',
            title: '实收净重',
            type: 'numeric', //数值
            prop: 'weight',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '原发净重',
            title: '原发净重',
            type: 'numeric', //数值
            validator: /[\s\S]/,
            allowEmpty: false,
            prop: 'originalTon'
          },

          {
            label: '亏吨',
            title: '亏吨',
            validator: /[\s\S]/,
            type: 'numeric', //数值
            prop: 'lossNull',
            allowEmpty: false
          },
          {
            label: '备注',
            type: 'text',
            title: '备注',
            prop: 'remarks'
          }
        ]
      }
      this.sLoading = false
      this.excelConfig.dialog = true
    },
    importSaleExcel(unknown, type) {
      //销售导入
      this.importType = type
      this.tableOption = {
        columns: [
          {
            label: '订单号',
            title: '订单号',
            prop: 'orderNo',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '派车单号',
            title: '派车单号',
            prop: 'autoBH',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '合同号',
            title: '合同号',
            prop: 'contractNo',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '收货单位',
            title: '收货单位',
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'customerName'
          },
          {
            label: '承运商',
            title: '承运商',
            prop: 'carrier',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '发货单位',
            title: '发货单位',
            type: 'text',
            prop: 'supplierName',
            validator: /[\s\S]/,
            allowEmpty: false,
            align: 'center'
          },
          {
            label: '物料',
            title: '物料',
            prop: 'cargoType',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '物料规格',
            title: '物料规格',
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'materialSpecification'
          },
          {
            label: '车辆',
            title: '车辆',
            type: 'text',
            allowEmpty: false,
            validator: /[\s\S]/,
            prop: 'plateNumber'
          },
          {
            label: '司机',
            title: '司机',
            type: 'text',
            validator: /[\s\S]/,
            prop: 'driver',
            allowEmpty: false
          },
          {
            label: '集装箱号',
            title: '集装箱号',
            prop: 'containerNo',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '一次计量时间',
            title: '一次计量时间',
            type: 'date',
            validator: /[\s\S]/,
            correctFormat: true,
            dateFormat: 'YYYY-MM-DD HH:mm:ss',
            prop: 'firstWeightDate',
            allowEmpty: false
          },
          {
            label: '二次计量时间',
            title: '二次计量时间',
            validator: /[\s\S]/,
            type: 'date',
            correctFormat: true,
            dateFormat: 'YYYY-MM-DD HH:mm:ss',
            prop: 'secondWeightDate',
            allowEmpty: false
          },
          {
            label: '毛重',
            title: '毛重',
            type: 'numeric',
            prop: 'firstWeight',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '皮重',
            title: '皮重',
            allowEmpty: false,
            validator: /[\s\S]/,
            type: 'numeric',
            prop: 'secondWeight'
          },
          {
            label: '过磅净重',
            title: '过磅净重',
            prop: 'originalTon',
            validator: /[\s\S]/,
            allowEmpty: false
          },
          {
            label: '备注',
            type: 'text',
            title: '备注',
            prop: 'remarks'
          }
        ]
      }
      this.excelConfig.dialog = true
      this.sLoading = false
    },

    selectChanged(value) {
    },
    Refreshfn() {
      this.dialogStatus = 'Refresh'
      this.dialogFormVisibleV = true
    },
    handleCloseV() {
      this.dialogFormVisibleV = false
    },
    async RefreshForm() {
      this.changeeFormLoading = true
      let date = this.entityFormV.date
      try {
        if (this.devType == '购进') {
          const { data } = await this.model.refresh({ date })
          this.$message({ showClose: true, message: '刷新成功', type: 'success' })
          this.$refs[this.curd].searchChange({ ...this.listQuery })
          this.changeeFormLoading = false
        } else {
          const { data } = await this.model.refreshV({ date })
          this.$message({ showClose: true, message: '刷新成功', type: 'success' })
          this.$refs[this.curd].searchChange({ ...this.listQuery })
          this.changeeFormLoading = false
        }
      } catch (error) {
      }
      this.changeeFormLoading = false
      this.dialogFormVisibleV = false
    },
    // handleProductChange(product) {
    //   let { name, id: productId, code: productCode, coalCategory: coalType } = product
    //   this.batchUpdateFields({ name, productId, productCode, coalType })
    // },
    // handleSupplierChange(supplier) {
    //   const { id: supplierId, name: supplierName } = supplier
    //   this.batchUpdateFields({ supplierId, supplierName, senderName: supplierName }) // 同步数据 顺便同步原发
    // },
    // handleContractChange(contract) {
    // },

    /**
     * @fields <entityForm>
     */
    batchUpdateFields(fields = {}) {
      const fieldsKeys = Object.keys(fields)
      for (const key of Object.keys(this.entityForm)) {
        if (fieldsKeys.includes(key)) {
          this.entityForm[key] = fields[key]
        }
      }
    },

    _requestInit() {
      // this.getContract()
      // this.getSupplier()
      this.getName()
    },
    /**
     * 保存取消时会触发初始化数据
     */
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.sLoading = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        // this.contractEntity.active = []
        this.supplierEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },

    handleNameEntity(val) {
      this.entityForm.name = val
    },
    // 获取供应商选择接口
    async getSupplier() {
      try {
        let options = []
        const { data } = await await supplierModel.page({ size: 1000 })
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        this.supplierEntity.options = options
      } catch (e) {
      }
    },
    // 获取合同选择接口
    async getContract() {
      try {
        const { data } = await chooseContract()
        this.contractEntity.options = this.formatContract({
          data,
          key: { supplierName: 'displayName', supplierId: 'id' }
        })
      } catch (error) {
      }
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
        })
      } catch (error) {
      }
    },
    /**
     * @formatContract 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    // 合同改变同步
    handleSupplier({ value, label }) {
      // this.entityForm.supplierId = value
      // this.entityForm.supplierName = label
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label }
    },

    paramsNumber(paramsAcc) {
      for (const [k, v] of Object.entries(paramsAcc)) {
        paramsAcc[k] = !Number.isNaN(v * 1) ? v * 1 : 0
      }
      return paramsAcc
    },

    // async handleUpload(scope) {
    //   const data = [...scope.settings.data]
    //   const importList = data.map((v) => {
    //     const item = {}
    //     Model.tableOption.columns.forEach((e) => {
    //       if (!e.noExport) {
    //         item[e.prop] = v[e.prop]
    //       }
    //     })
    //     return item
    //   })
    //   const text = JSON.stringify(importList)
    //   scope.uploading.state = true
    //   const res = await Model.saveList({ text })
    //   scope.uploading.state = false
    //   if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
    //   if (res.data.length) {
    //     scope.settings.data = res.data.map((v, i) => v)
    //   } else {
    //     this.$message({ showClose: true, message: '导入成功', type: 'success' })
    //     scope.settings.data = []
    //     this.getList()
    //   }
    // },
    getList() {
      this.$refs[this.curd].$emit('refresh')
    },
    // 累计Acc接口
    async getAccValues({ date, contractId }) {
      const res = await Model.getAccValues({ date, contractId })
      if (res.data) {
        const { receiveWeight, sendWeight, truckCount } = res.data
        return this.paramsNumber({ receiveWeight, sendWeight, truckCount })
      } else {
        return { receiveWeight: 0, sendWeight: 0, truckCount: 0 }
      }
    },

    // 更新累计Acc
    async updateAcc() {
      const { date, contractId } = this.entityForm
      if (!date || !contractId) return false
      const paramsAcc = await this.getAccValues({ date, contractId })
      Object.keys(paramsAcc).forEach((key) => {
        this.entityFormAcc[key + 'Acc'] = paramsAcc[key] * 1 // 备份
        this.entityForm[key + 'Acc'] = paramsAcc[key] * 1 + this.entityForm[key] * 1
      })
      this.computeWayCostAcc()
    },
    // 处理receiveWeight, sendWeight 值处理成涂好
    updateWayCost() {
      let { receiveWeight, sendWeight } = this.entityForm
      receiveWeight = receiveWeight * 1
      sendWeight = sendWeight * 1
      if (receiveWeight === '' || sendWeight === '') return false // 数据为空时不计算
      if (receiveWeight === 0 && sendWeight === 0) return (this.entityForm.wayCost = 0.0)
      // 途耗累计=实收累计-原发累计/原发累计
      this.entityForm.wayCost = (((sendWeight - receiveWeight) / sendWeight) * 100).toFixed(2)
    },
    // 计算涂耗累计Acc
    computeWayCostAcc() {
      let { receiveWeightAcc, sendWeightAcc } = this.entityForm
      receiveWeightAcc = receiveWeightAcc * 1
      sendWeightAcc = sendWeightAcc * 1
      let wayCostAcc = (((sendWeightAcc - receiveWeightAcc) / sendWeightAcc) * 100).toFixed(2) * 1
      this.entityForm.wayCostAcc = !Number.isNaN(wayCostAcc) ? wayCostAcc : 0
    },
    // 计算累计方法
    computeAcc(accName, value) {
      if (accName === 'truckCountAcc') {
        this.entityForm[accName] = this.entityFormAcc[accName] * 1 + value * 1
        return
      }
      this.entityForm[accName] = (this.entityFormAcc[accName] * 1 + value * 1).toFixed(2)
    }
  },
  watch: {
    'entityForm.name'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.entityForm.coalType = item.coalCategory || ''
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.coalType = ''
      }
    },
    dialogFormVisible(v) {
      if (this.dialogStatus === 'create' && v) {
        this.updateAcc()
      } // 用于再打开时触发合同Acc数据
    },
    'entityForm.receiveWeight'(v) {
      this.computeAcc('receiveWeightAcc', v)
      this.updateWayCost()
    },
    'entityForm.sendWeight'(v) {
      this.computeAcc('sendWeightAcc', v)
      this.updateWayCost()
    },
    'entityForm.truckCount'(v) {
      this.computeAcc('truckCountAcc', v)
    },
    'entityForm.date'() {
      this.updateAcc()
    },
    'entityForm.wayCost'() {
      this.computeWayCostAcc()
    },
    'entityForm.contractId'(id) {
      this.updateAcc()
      if (this.entityForm.date && id) {
        this.updateAcc(this.entityForm.date, id)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'entityForm.supplierId'(id) {
      this.contractActive = id
      const item = this.supplierEntity.options.find((item) => item.value === id)
      if (item) {
        this.supplierEntity.active = item
      } else {
        this.supplierEntity.active = []
      }
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.name = form.productName
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.supplierId
      this.entityForm.senderName = form.secondParty
      this.entityForm.receiverName = form.firstParty
      const item = this.contractEntity.options.find((v) => v.supplierId === value[0])
      if (!item) return
      this.entityForm.supplierId = item.supplierId
      this.entityForm.supplierName = item.supplierName
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';

::v-deep .filter-form {
  .el-form-item:nth-of-type(-n + 4) {
    width: 200px !important;

    .el-date-editor {
      width: 180px;
      min-width: unset;
      max-width: unset;

      .el-input__inner {
        padding-left: 30px;
        width: 180px;
      }

      .el-input__prefix {
        right: unset;
        left: 5px;
        transition: all 0.3s;
      }

      .el-input__suffix {
        right: 20px;
      }
    }
  }
}
</style>
