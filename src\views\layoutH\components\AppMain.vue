<template>
  <el-container>
    <el-header height="45px">
      <el-menu :collapse="!isFold" default-active="$route.path" menu-trigger="click" mode="horizontal">
        <template v-for="router in currentRoute.children">
          <sidebarItemC v-if="router.children" :key="router.path" :item="router"></sidebarItemC>
          <sidebarItem v-else :key="router.path" :item="router"></sidebarItem>
        </template>
      </el-menu>
    </el-header>
    <el-main style="padding: 0 10px 10px 10px">
      <router-view :key="key"/>
    </el-main>
  </el-container>
</template>

<script>
import {mapGetters} from 'vuex'
import sidebarItem from './Sidebar/sidebarItem'
import sidebarItemC from './Sidebar/sidebarItemC'

export default {
  name: 'AppMain',
  data() {
    return {
      menulist: [],
      activePath: '',
      str: ''
    }
  },
  components: {sidebarItem, sidebarItemC},
  computed: {
    ...mapGetters(['currentRoute', 'isShowChild', 'parentActive', 'childActive', 'childItemActive', 'isFold']),
    key() {
      return this.$route.fullPath
    },
    showChild() {
      return this.$route.fullPath !== '/dashboard' && this.isShowChild
    },
    cesshi() {
      // console.log(this.childActive)
      return this.childActive
    }
  },
  created() {
    this.setRem()
    window.onresize = () => {
      this.setRem()
    }
  },
  // yan
  mounted() {
    // console.log('路由')
    // console.log(this.currentRoute.children)
  },
  // yan

  methods: {
    // yan
    // find__node(data, key) {
    //   for (var i = 0; i < data.length; i++) {
    //     console.log('循環')
    //     console.log(data[i])
    //     console.log(data[i].path)
    //     if (data[i].path === key) return data[i]
    //     if (data[i].children) return this.find__node(data[i].children, key)
    //   }
    // },
    // handleSelect(key) {
    //   const keywords = key.substring(key.indexOf('/') + 1, key.length)
    //   console.log(keywords)
    //   this.activePath = key
    //   console.log(this.activePath)
    //   const result = this.find__node(this.currentRoute.children, keywords)
    // },
    // yan

    isShowTooltip(title) {
      title = title || ''
      return !(title.length >= 9)
    },
    handleChangeRoute(route) {
      // console.log(route)
      const childPath = route.children ? route.children[0] : ''
      this.$store.dispatch('changeChildRoute', {parent: route.path, child: childPath.path || ''})
      if (childPath) {
        this.$router.push({name: childPath.name})
      } else {
        this.$router.push({name: route.name})
      }
    },
    handleChildChangeRoute(parentRoute, childIndex) {
      const childPath = parentRoute.children[childIndex]
      this.$store.dispatch('changeChildRoute', {parent: parentRoute.path, child: childPath.path})
      this.$router.push({name: childPath.name})
    },
    handleChangeFold() {
      this.$store.commit('CHANGE_IS_SHOW_FOLD')
    },
    setRem() {
      const baseSize = 12
      let basePc = baseSize / 1920 // 表示1920的设计图,使用100PX的默认值
      let vW = window.innerWidth // 当前窗口的宽度
      let vH = window.innerHeight // 当前窗口的高度
      // 非正常屏幕下的尺寸换算
      let dueH = (vW * 1080) / 1920
      if (vH < dueH) {
        // 当前屏幕高度小于应有的屏幕高度，就需要根据当前屏幕高度重新计算屏幕宽度
        vW = (vH * 1920) / 1080
      }
      let rem = vW * basePc // 以默认比例值乘以当前窗口宽度,得到该宽度下的相应font-size值
      // console.log(rem)
      document.documentElement.style.fontSize = rem + 'px'
    },

    handleOpen(key, keyPath) {
      // console.log(key, keyPath)
    },
    handleClose(key, keyPath) {
      // console.log(key, keyPath)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-menu--horizontal {
  > .el-menu-item, .el-submenu, .el-submenu .el-submenu__title {
    line-height: 45px;
    height: 45px;
  }
}

::v-deep .el-header {
  padding: 0 10px;
}

.isFold {
  // width: 190px;
  width: 170px;
  // span {
  //   display: block;
  // }
}

.isUnFold {
  width: 50px;

  span {
    display: none;
  }
}

.app-main {
  height: calc(100vh - 72px);
  display: flex;
  width: 100%;
  margin-bottom: 10px;

  &-view {
    flex: 1;
    overflow: hidden;
  }

  &-sidebar {
    background: #fff;
    // min-width: 50px;
    min-width: 70px;
    margin-left: 10px;
    font-size: 14px;
    transition: width 0.5s;

    // overflow: hidden;
    &-fold {
      cursor: pointer;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      height: 50px;
      margin: 0 15px;

      img {
        width: 20px;
        height: 20px;
      }
    }

    &-item {
      height: 21px;

      &-child {
        transform: scale(0.9);
        font-size: 14px;
        padding: 10px 0;
        color: #a2a1a1;
        // text-align: center;
        // margin-left: 3.7rem;
        margin-left: 4.1rem;
        cursor: pointer;
        // transition: 1s all;

        &:hover {
          opacity: 0.7;
          color: #33cad9;
        }

        &-active {
          color: #33cad9;
        }
      }

      opacity: 1;
      color: rgb(117, 117, 117, 0.8);
      display: flex;
      cursor: pointer;
      align-items: center;
      min-height: 52px;
      height: 52px;
      overflow: hidden;
      // transition: 0.3s all;
      &-img {
        margin: 0px 0 0 15px;
        max-width: 21px;
        max-height: 21px;
        display: flex;
        align-items: center;

        img {
          width: 100%;
        }
      }

      span {
        max-height: 21px;
        white-space: nowrap;
        margin-left: 3px;
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        /*当文本溢出包含元素时，以省略号表示超出的文本*/
        text-overflow: ellipsis;
        transform: scale(0.9);
      }
    }

    &-item:hover {
      color: #757575;
      opacity: 1;
    }

    .sidebar-item-active {
      background-color: #33cad9;
      color: #fff;
    }
  }
}
</style>

<style scoped>

</style>
