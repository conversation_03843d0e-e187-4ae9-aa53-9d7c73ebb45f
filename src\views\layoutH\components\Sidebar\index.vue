<template>
    <div class="sidebar-container">
        <section class="parent">
            <div class="parent-sidebar">
                <img class="parent-sidebar-logo" alt="logo" src="@/assets/snxx/logo.png">
            </div>
            <template v-for="route in showRoutes">
                <div class="parent-sidebar-item" :key="route.path" @click="handleToConsole(route)"
                     :class="[route.path===parentActive?'parent-sidebar-item-active':'']">
                    <div class="parent-sidebar-item-img" v-if="route.meta">
                        <img :src="route.path===parentActive?route.meta.iconSelected:route.meta.icon">
                    </div>
                    <div v-if="route.meta">
                        <div v-if="route.meta.title=='首页'">{{ route.meta.title }}</div>
                        <div v-else>{{ route.meta.title }}</div>
                    </div>
                </div>
            </template>
        </section>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
import Cache from '@/utils/cache'

export default {
    computed: {
        ...mapGetters(['showRoutes', 'parentActive', 'activesidebar'])
    },
    mounted() {
        this.$store.dispatch('formatRoutes')
    },
    methods: {
        handleToConsole(route) {
            if (route.path == '/finance') {
                this.$store.commit('CHANGE_activesidebar', true)
            } else {
                this.$store.commit('CHANGE_activesidebar', false)
            }
            this.$store.dispatch('changeRoute', route)
            if (!route.children.length) return this.$router.push({path: `${route.path}`})
            if (route.path === '/') return this.$router.push({path: `/dashboard`})
            if (this.hasConsolePage(route.children)) {
                this.$store.dispatch('changeChildRoute', {parent: route.children[0].path})
                this.$router.push({path: `${route.path}/console`})
            } else {
                const childPath = route.children[0].path
                const chi = route.children[0].children
                if (chi) {
                    this.$store.dispatch('changeChildRoute', {parent: childPath, child: chi[0].path})
                    this.$router.push({path: `${route.path}/${childPath}/${chi[0].path}`})
                } else {
                    this.$store.dispatch('changeChildRoute', {parent: childPath})
                    this.$router.push({path: `${route.path}/${childPath}`})
                }
            }
        },
        hasConsolePage(children) {
            return children.some((child) => {
                return child.path === 'console'
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.activestyle {
  // border: solid 1px red;
  .main-body {
    height: 100%;
  }
}

.sidebar-container {
  position: fixed;

  .parent {
    transition: width 0.28s;
    width: 10.5rem;
    // width: 12.5rem;
    height: 100%;
    font-size: 12px;
    // font-size: 1.4rem;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    // background: #2f79e8;
    background: #2f79e8;
    display: flex;
    flex-flow: column;
    justify-content: flex-start;

    .parent-sidebar {
      text-align: center;
      width: 7rem;
      height: 102px;
      display: flex;
      align-items: center;
      margin: 0 auto 10px;

      &-logo {
        width: 100%;
        margin: 5px auto;
      }
    }

    .parent-sidebar-item {
      // flex: 0 1 90px;
      flex: 0 1 60px;
      width: inherit;
      line-height: 25px;
      display: flex;
      // flex-flow: column;
      justify-content: center;
      align-items: center;
      color: #d5d6fb;
      cursor: pointer;

      &-img {
        // width: 2.6rem;
        width: 2.1rem;
        font-size: 2rem;
        margin-right: 10px;

        img {
          width: 100%;
        }
      }

      &-active {
        background-color: #33cad9;
        color: #fff;
      }
    }
  }
}
</style>
