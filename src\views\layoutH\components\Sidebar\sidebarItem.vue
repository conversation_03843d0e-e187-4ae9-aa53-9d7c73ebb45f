<template>
  <el-menu-item :class="[item.path===childActive?'isactiveN':' ']" :index="item.path" @click="handleChangeRoute(item)">
    <p style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis;">{{ item.meta.title }}</p>
  </el-menu-item>
</template>
<script>
import {mapGetters} from 'vuex'

export default {
  name: 'sideItem',
  props: {
    item: {
      type: Object,
      require: true,
      default: {}
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['currentRoute', 'isShowChild', 'parentActive', 'childActive', 'childItemActive', 'isFold']),
    key() {
      return this.$route.fullPath
    },
    showChild() {
      return this.$route.fullPath !== '/dashboard' && this.isShowChild
    },
    cesshi() {
      // console.log(this.childActive)
      return this.childActive
    }
  },
  methods: {
    isShowTooltip(title) {
      title = title || ''
      return !(title.length >= 9)
    },
    handleChangeRoute(route) {
      // console.log(route)
      const childPath = route.children ? route.children[0] : ''
      this.$store.dispatch('changeChildRoute', {parent: route.path, child: childPath.path || ''})
      if (childPath) {
        this.$router.push({name: childPath.name})
      } else {
        this.$router.push({name: route.name})
      }
    },
    handleChildChangeRoute(parentRoute, childIndex) {
      // console.log(parentRoute)
      // console.log(childIndex)
      const childPath = parentRoute.children[childIndex]
      // console.log(childPath)
      // console.log(childPath.path)
      this.$store.dispatch('changeChildRoute', {parent: parentRoute.path, child: childPath.path})
      this.$router.push({name: childPath.name})
    }
  },
  created() {
  }
}
</script>

<style scoped>
.hid {
  display: none;
}

.el-menu-item {
  font-size: 13px !important;
}

.tstooltip >>> .el-tooltip__popper.is-dark {
  opacity: 0.2;
}

.el-menu {
  border-right: solid 1px #fff !important;
}

.el-menu-item {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.el-menu-item .app-main-sidebar-item-img.tsimf {
  margin-right: 10px;
}

.el-menu-item:hover {
  /* background-color: #33cad9 !important;
  color: #fff !important; */
  /* color: #33cad9 !important; */
}

.el-submenu >>> .el-submenu__title:hover {
  background-color: #33cad9 !important;
  color: #fff !important;
}

.el-submenu >>> .el-submenu__title i:hover {
  color: #fff !important;
}

.isactiveN {
  background-color: #33cad9 !important;
  color: #fff !important;
}

.isactiveN >>> .el-submenu__title {
  color: #fff !important;
}

.el-menu-item.is-active {
  color: #303133;
}

.active {
  color: #33cad9 !important;
  background-color: #f5feff !important;
}

.isactiveN >>> .el-submenu__title i {
  color: #fff !important;
}

.el-submenu >>> .el-submenu__title {
  height: 50px !important;
}

.el-submenu .el-menu-item {
  height: 45px !important;
  min-width: 165px !important;
}

.el-menu >>> .el-menu-item-group__title {
  padding: 0 !important;
}

.hiddev >>> .el-submenu__title i {
  display: none !important;
}
</style>
