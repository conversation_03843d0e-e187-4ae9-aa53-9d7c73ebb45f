<template>
  <el-submenu :class="[item.path===childActive?'isactiveN':' ' , isFold?' ':'hiddev']" :index="item.path">
    <template slot="title">
      <span v-if="item.path===childActive">{{ $route.meta.title }}</span>
      <span v-else>{{ item.meta.title }}</span>
    </template>
    <el-menu-item-group v-if="item.children">
      <el-menu-item v-for="(leveltwo,index) in item.children" :key="index"
                    :class="[leveltwo.path==childItemActive?'active':'']"
                    :index="item.path"
                    :popper-append-to-body="true"
                    @click="handleChildChangeRoute(item,index)">
        {{ leveltwo.meta.title }}
      </el-menu-item>
    </el-menu-item-group>
  </el-submenu>
</template>
<script>
import {mapGetters} from 'vuex'

export default {
  name: 'sideItemC',
  props: {
    item: {
      type: Object,
      require: true,
      default: {}
    }
  },
  data() {
    return {}
  },
  mounted() {
    this.$children[0].$parent = this.$parent
  },
  computed: {
    ...mapGetters(['currentRoute', 'isShowChild', 'parentActive', 'childActive', 'childItemActive', 'isFold']),
    key() {
      return this.$route.fullPath
    },
    showChild() {
      return this.$route.fullPath !== '/dashboard' && this.isShowChild
    },
    cesshi() {
      // console.log(this.childActive)
      return this.childActive
    }
  },
  methods: {
    isShowTooltip(title) {
      title = title || ''
      return !(title.length >= 9)
    },
    handleChangeRoute(route) {
      // console.log(route)
      const childPath = route.children ? route.children[0] : ''
      this.$store.dispatch('changeChildRoute', {parent: route.path, child: childPath.path || ''})
      if (childPath) {
        this.$router.push({name: childPath.name})
      } else {
        this.$router.push({name: route.name})
      }
    },
    handleChildChangeRoute(parentRoute, childIndex) {
      // console.log(parentRoute)
      // console.log(childIndex)
      const childPath = parentRoute.children[childIndex]
      // console.log(childPath)
      // console.log(childPath.path)
      this.$store.dispatch('changeChildRoute', {parent: parentRoute.path, child: childPath.path})
      this.$router.push({name: childPath.name})
    }
  },
  created() {
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-submenu.is-active > .el-submenu__title {
  background: rgb(206, 169, 181) !important;
}

.hid {
  display: none;
}

.el-menu-item {
  font-size: 13px !important;
}

.el-menu {
  border-right: solid 1px #fff !important;
}

.el-menu-item {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.el-menu-item .app-main-sidebar-item-img.tsimf {
  margin-right: 10px;
}

.el-menu-item:hover {
  /* background-color: #33cad9 !important;
  color: #fff !important; */
  /* color: #33cad9 !important; */
}

.isactiveN {
  background-color: #33cad9 !important;
  color: #fff !important;

  ::v-deep .el-submenu__title {
    color: white !important;

    &:hover {
      color: white;
      background-color: #33cad9;
    }
  }
}

.el-menu-item.is-active {
  color: #303133;
}

.active {
  color: #33cad9 !important;
  background-color: #f5feff !important;
}

.el-submenu .el-menu-item {
  height: 45px !important;
  min-width: 165px !important;
}

</style>
