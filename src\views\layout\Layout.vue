<template>
  <div :class="classObj" class="app-wrapper">
    <div class="main-container">
      <div style="display:flex">
        <sidebar class="sidebar-container" />
        <div class="main-body" :class="[activesidebar?'width0':'']">
          <navbar />
          <app-main />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Navbar, Sidebar, AppMain } from './components'
import { mapGetters } from 'vuex'
import ResizeMixin from './mixin/ResizeHandler'
import { getlistAllReviews } from '@/api/dashboard'
import { gethomePushMessage } from '@/api/user'
import { getIscl } from '@/utils/auth'
import Cache from '@/utils/cache'
export default {
  name: 'Layout',
  components: {
    Navbar,
    Sidebar,
    AppMain
  },
  data() {
    return {
      listdata: [
        { name: '测试合同', context: '的好机会受打击啊手机打开' },
        { name: '测试合同', context: '的好机会受打击啊手机打开' }
      ],
      isclear: false
    }
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapGetters(['showRoutes', 'activesidebar']),
    sidebar() {
      return this.$store.state.app.sidebar
    },
    activesidebar() {
      return this.$store.state.app.activesidebar
    },

    device() {
      return this.$store.state.app.device
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  mounted() {
    this.init()
    //  五分钟调用一次
    // setInterval(() => {
    //   this.getlistAllReviewsfn()
    // }, 300000)
  },
  methods: {
    async getlistAllReviewsfn() {
      // console.log('调用列表数据')
      try {
        const res = await gethomePushMessage()
        if (res.code == 401) {
          return
        }
        if (res.data) {
          let data = {
            visible: true,
            listdata: res.data.pushMessageList,
            totalCount: res.data.totalCount
          }
          if (res.data.pushMessageList.length > 0) {
            this.$executeResult(data)
          }
        }
      } catch (error) {}
    },
    async init() {
      // this.getlistAllReviewsfn()

      // setInterval(() => {
      //   //两分钟调一次
      //   this.getlistAllReviewsfn()
      // }, 60 * 1000)

      var interval = setInterval(() => {
        this.isclear = getIscl()
        // console.log(this.isclear)
        if (this.isclear == null) {
          clearInterval(interval)
        } else {
          // console.log('调用')
          this.getlistAllReviewsfn()
        }
        // if (this.isclear == 'true') {
        //   clearInterval(interval)
        // } else {
        //   console.log('调用')
        //   this.getlistAllReviewsfn()
        // }
      }, 60 * 1000)

      // var loveArr = [
      //   { name: '测试合同', context: '的好机会受打击啊手机打开', id: '1' },
      //   { name: '测试合同', context: '的好机会受打击啊手机打开', id: '2' },
      //   { name: '测试合同', context: '的好机会受打击啊手机打开', id: '3' },
      //   { name: '测试合同', context: '的好机会受打击啊手机打开', id: '4' },
      //   { name: '测试合同', context: '的好机会受打击啊手机打开', id: '5' },
      // ]
      // var loveStr = ''
      // for (let i = 0; i < loveArr.length; i++) {
      //   loveStr +=
      //     '<div style="margin-top:10px;">' +
      //     '<div style="display:flex;padding-bottom:10px;cursor:pointer;" class="myclass" onclick="' +
      //     this.goAlarmList(loveArr[i].id) +
      //     '"id=myButton' +
      //     loveArr[i].id +
      //     '>' +
      //     '<div style="margin:0 3px;color:#fff;border:none;height:20px;padding:0 5px;line-height:19px;border-radius:4px;box-sizing:border-box;white-space:nowrap;display:inline-block;background-color:red">' +
      //     loveArr[i].name +
      //     '</div>' +
      //     '<div style="line-height:20px;padding-left:10px;color:#5194f8;overflow: hidden;text-overflow: ellipsis;white-space: Nowrap;">' +
      //     loveArr[i].context +
      //     '</div>' +
      //     '</div>' +
      //     '</div>'
      // }

      // // console.log(loveStr)
      // let total = 3
      // let that = this
      // this.$notify({
      //   title: `新消息（${total}）`,
      //   // duration: 10000000,
      //   dangerouslyUseHTMLString: true,
      //   message: loveStr,
      //   // message:
      //   //   '<div style="margin:10px 0">' + '<div style="display:flex;padding-bottom:20px;cursor:pointer;"  v-for='(itemV, index) in
      //   //   this.listdata +
      //   //     '@click=' +
      //   //     this.goAlarmList() +
      //   //     '>' +
      //   //     '<div style="margin:0 3px;color:#fff;border:none;height:20px;padding:0 5px;line-height:19px;border-radius:4px;box-sizing:border-box;white-space:nowrap;display:inline-block;background-color:red">' +
      //   //     itemV.name +
      //   //     "</div><div style='line-height:20px;padding-left:10px;color:#5194f8;'>" +
      //   //     itemV.context +
      //   //     '</div></div></div>',
      //   customClass: 'notify-warn',
      //   type: 'warning',
      //   position: 'bottom-right',
      //   onClick: function () {
      //     that.chufa()
      //   },
      // })
    }
    // goAlarmList(id) {
    //   console.log(id)
    // },

    // chufa() {
    //   var elements = document.getElementsByClassName('myclass')
    //   // console.log(elements)
    //   for (var i = 0; i < elements.length; i++) {
    //     var element = elements[i]
    //     console.log(element)
    //     // 对每个具有 "my-class" class 名称的元素进行操作
    //   }
    //   const button = document.getElementById('myButton1') // 获取html+=添加的button元素
    //   button.addEventListener('click', function () {
    //     console.log('You clicked the button')
    //   })
    // },
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import '../../styles/mixin.scss';
p {
  margin-block-start: 0em;
  margin-block-end: 0em;
}
.contebox {
  display: flex;
  padding-bottom: 20px;
}

.context {
  line-height: 20px;
  padding-left: 10px;
  color: #5194f8;
}
::v-deep .el-notification {
  width: 500px !important;
}

.width0 {
  height: 100% !important;
}
.main-body {
  display: flex;
  width: 100%;
  height: 100vh;
  flex-direction: column;
}

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.footer {
  transition: all 0.5s;
  background: none repeat scroll 0 0 #fff;
  border-top: 1px solid #e7eaec;
  overflow: hidden;
  padding: 0 20px;
  height: 34px;
  width: 100%;
  text-align: right;
  font-size: 12px;
  line-height: 34px;
  color: #495060;

  a {
    color: #2d8cf0;
    background: 0 0;
    text-decoration: none;
    outline: 0;
    cursor: pointer;
    transition: color 0.2s ease;
  }
}
</style>
