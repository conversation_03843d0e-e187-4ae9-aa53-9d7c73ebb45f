<template>
    <section class="app-main" :class="[activesidebar?'width0':'']">
        <section class="app-main-sidebar" v-if="!showChild" :class="[isFold?'isFold':'isUnFold']">
            <section class="app-main-sidebar-fold" @click="handleChangeFold">
                <img src="@/assets/sidebar-item/unfold.png" v-if="isFold"/>
                <img src="@/assets/sidebar-item/fold.png" v-else/>
            </section>
            <section>
                <el-menu ref="menus" class="el-menu-vertical-demo" default-active="$route.path" :collapse="!isFold"
                         :default-openeds="openeds" @close="handleClose">
                    <!-- {{router.path}} -->
                    <sidebarItem v-for="router in currentRoute.children" :key="router.path"
                                 :item="router"></sidebarItem>
                </el-menu>
            </section>

        </section>
        <div class="app-main-view">
            <router-view :key="key"/>
        </div>
    </section>
</template>

<script>
import {mapGetters} from 'vuex'
import sidebarItem from '@/views/layout/components/Sidebar/sidebarItem'
import Cache from '@/utils/cache'

export default {
    name: 'AppMain',
    data() {
        return {
            menulist: [],
            activePath: '',
            str: '',
            openeds: [
                'buy',
                'purchase',
                'salev',
                'DeletedTab',
                'stockdate',
                'BuyInsalev',
                'qualitysalev',
                'sell',
                '',
                'standingbook',
                'payment',
                'settlement',
                'reportform'
            ]
        }
    },
    components: {sidebarItem},
    computed: {
        ...mapGetters(['currentRoute', 'isShowChild', 'parentActive', 'childActive', 'childItemActive', 'isFold', 'activesidebar']),
        key() {
            return this.$route.fullPath
        },
        showChild() {
            return this.$route.fullPath === '/coalBlendingV/coalBlendingV' || this.$route.fullPath === '/dashboard'
            // return this.$route.fullPath !== '/dashboard' && this.isShowChild
        },
        cesshi() {
            return this.childActive
        },
        activesidebar() {
            return this.$store.state.app.activesidebar
        }
    },
    created() {
        this.setRem()
        window.onresize = () => {
            this.setRem()
        }
    },
    // yan
    mounted() {
        // console.log('路由')
        // console.log(this.currentRoute.children)
    },
    // yan
    methods: {
        // yan
        // find__node(data, key) {
        //   for (var i = 0; i < data.length; i++) {
        //     console.log('循環')
        //     console.log(data[i])
        //     console.log(data[i].path)
        //     if (data[i].path === key) return data[i]
        //     if (data[i].children) return this.find__node(data[i].children, key)
        //   }
        // },
        // handleSelect(key) {
        //   const keywords = key.substring(key.indexOf('/') + 1, key.length)
        //   console.log(keywords)
        //   this.activePath = key
        //   console.log(this.activePath)
        //   const result = this.find__node(this.currentRoute.children, keywords)
        // },
        // yan

        isShowTooltip(title) {
            title = title || ''
            return !(title.length >= 9)
        },
        handleChangeRoute(route) {
            const childPath = route.children ? route.children[0] : ''
            this.$store.dispatch('changeChildRoute', {parent: route.path, child: childPath.path || ''})
            if (childPath) {
                this.$router.push({name: childPath.name})
            } else {
                this.$router.push({name: route.name})
            }
        },
        handleChildChangeRoute(parentRoute, childIndex) {
            const childPath = parentRoute.children[childIndex]
            this.$store.dispatch('changeChildRoute', {parent: parentRoute.path, child: childPath.path})
            this.$router.push({name: childPath.name})
        },
        handleChangeFold() {
            this.$store.commit('CHANGE_IS_SHOW_FOLD')
            // console.log(this.$route.path)
            // this.handleClose('', this.$route.path)
        },
        setRem() {
            const baseSize = 12
            let basePc = baseSize / 1920 // 表示1920的设计图,使用100PX的默认值
            let vW = window.innerWidth // 当前窗口的宽度
            let vH = window.innerHeight // 当前窗口的高度
            // 非正常屏幕下的尺寸换算
            let dueH = (vW * 1080) / 1920
            if (vH < dueH) {
                // 当前屏幕高度小于应有的屏幕高度，就需要根据当前屏幕高度重新计算屏幕宽度
                vW = (vH * 1920) / 1080
            }
            let rem = vW * basePc // 以默认比例值乘以当前窗口宽度,得到该宽度下的相应font-size值
            // console.log(rem)
            document.documentElement.style.fontSize = rem + 'px'
        },
        // handleOpen(key, keyPath) {
        //   console.log(key, keyPath)
        // },
        handleClose(key, keyPath) {
            // console.log(key, keyPath)
            this.$refs.menus.open(keyPath)
        }
    }
}
</script>

<style scoped>
.el-menu >>> .el-submenu .el-submenu__title .el-submenu__icon-arrow {
    visibility: hidden;
}

.app-main-sidebar {
    /* overflow-x: hidden;
    overflow-y: scroll; */
    min-width: 45px !important;
}
</style>

<style scoped lang="scss">
::v-deep .el-tooltip__popper.is-light {
  border: 1px solid #d6d6d6;
  color: #616162;
}

.isFold {
  // width: 190px;
  width: 120px;
  // span {
  //   display: block;
  // }
}

.isUnFold {
  width: 45px;

  span {
    display: none;
  }
}

.width0 {
  height: 100% !important;
}

.app-main {
  height: calc(100vh - 72px);
  // height: 100%;
  display: flex;
  width: 100%;
  margin-bottom: 10px;

  &-view {
    flex: 1;
    overflow: hidden;
  }

  &-sidebar {
    background: #fff;
    // min-width: 50px;
    min-width: 70px;
    margin-left: 10px;
    font-size: 14px;
    transition: width 0.5s;

    // overflow: hidden;
    &-fold {
      cursor: pointer;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      height: 40px;
      margin: 0 15px;

      img {
        width: 20px;
        height: 20px;
      }
    }

    .section {
      display: flex;
      flex-flow: column;
    }

    &-item {
      height: 21px;

      &-child {
        transform: scale(0.9);
        font-size: 14px;
        padding: 10px 0;
        color: #a2a1a1;
        // text-align: center;
        // margin-left: 3.7rem;
        margin-left: 4.1rem;
        cursor: pointer;
        // transition: 1s all;

        &:hover {
          opacity: 0.7;
          color: #33cad9;
        }

        &-active {
          color: #33cad9;
        }
      }

      opacity: 1;
      color: rgb(117, 117, 117, 0.8);
      display: flex;
      cursor: pointer;
      align-items: center;
      min-height: 52px;
      height: 52px;
      overflow: hidden;
      // transition: 0.3s all;
      &-img {
        margin: 0px 0 0 15px;
        max-width: 21px;
        max-height: 21px;
        display: flex;
        align-items: center;

        img {
          width: 100%;
        }
      }

      span {
        max-height: 21px;
        white-space: nowrap;
        margin-left: 3px;
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        /*当文本溢出包含元素时，以省略号表示超出的文本*/
        text-overflow: ellipsis;
        transform: scale(0.9);
      }
    }

    &-item:hover {
      color: #757575;
      opacity: 1;
    }

    .sidebar-item-active {
      background-color: #33cad9;
      color: #fff;
    }
  }
}
</style>

<style scoped>
.el-menu {
    border-right: solid 1px #fff !important;
}

.el-menu-item {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.el-menu >>> .el-menu-item {
    /* 设置文本不换行 */
    white-space: nowrap !important;
    /* 溢出隐藏 */
    overflow: hidden !important;
    /* 溢出的处理方式 省略*/
    text-overflow: ellipsis !important;
}

.el-menu-item .app-main-sidebar-item-img.tsimf {
    margin-right: 10px;
}

.el-menu-item:hover {
    /* background-color: #33cad9 !important;
    color: #fff !important; */
    /* color: #33cad9 !important; */
}

.el-submenu >>> .el-submenu__title:hover {
    background-color: #33cad9 !important;
    color: #fff !important;
}

.el-submenu >>> .el-submenu__title i:hover {
    color: #fff !important;
}

.isactiveN {
    background-color: #33cad9 !important;
    color: #fff !important;
}

.isactiveN >>> .el-submenu__title {
    color: #fff !important;
}

.el-menu-item.is-active {
    color: #303133;
}

.active {
    color: #33cad9 !important;
    background-color: #f5feff !important;
}

.isactiveN >>> .el-submenu__title i {
    color: #fff !important;
}

.el-submenu >>> .el-submenu__title {
    height: 50px !important;
}

/* .el-submenu .el-menu-item {
  height: 45px !important;
  min-width: 165px !important;
} */
.el-menu >>> .el-menu-item-group__title {
    padding: 0 !important;
}
</style>
