<template>
  <div class="navbar">
    <hamburger :toggle-click="toggleSideBar" :is-active="sidebar.opened" class="hamburger-container" />
    <tags-view ref="tags" />
    <button class="nav-btn2 nav-btn-right nav-bell">
      <img @click="bellClick" style="width: 22px;height:20px;margin: 0 10px" src="@/assets/ring.png" alt="">
      <el-dropdown class="avatar-container" @visible-change="initCompanyInfo" trigger="click">
        <div class="avatar-wrapper">
          <span class="name">{{user.userName}}, 您好!</span>
        </div>
        <el-dropdown-menu class="user-dropdown companyInfo" slot="dropdown">
          <template v-if="dropdownOpen">
            <el-dropdown-item>
              <span @click="showDownloadCenterHandler" style="display:block;">修改密码</span>
            </el-dropdown-item>
            <el-dropdown-item>
              <span @click="logout" style="display:block;">退出登录</span>
            </el-dropdown-item>
            <el-dropdown-item>
              <span style="display:block;">移动App下载二维码</span>
              <div style="display: flex;justify-content: flex-start;">
                <img src="../../../assets/scan.png" style="width: 80px;height: 80px;" alt="">
              </div>
            </el-dropdown-item>
          </template>
        </el-dropdown-menu>
      </el-dropdown>
    </button>
    <el-dialog :append-to-body="true" class="sp-modal" :before-close="handleClose" :visible.sync="dialogExportDownVisible"
               :close-on-press-escape="false" :close-on-click-modal="false" title="修改密码" :width="'500px'" key="downTraitDialog">
      <el-form :model="editForm" ref="editForm" label-width="100px">
        <el-form-item label="旧密码" prop="oldPassword" :rules="[{ required: true, message: '密码不能为空'}]">
          <el-input type="password" v-model="editForm.oldPassword"></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword" :rules="[{ required: true, message: '密码不能为空'}]">
          <el-input type="password" v-model="editForm.newPassword"></el-input>
        </el-form-item>
        <!--                <el-form-item-->
        <!--                        label="确认密码"-->
        <!--                        prop="age"-->
        <!--                        :rules="[{ required: true, message: '密码不能为空'}]"-->
        <!--                >-->
        <!--                    <el-input type="age" v-model="editForm.newPassword"></el-input>-->
        <!--                </el-form-item>-->
        <div style="display: flex;justify-content: flex-end">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </div>
      </el-form>
    </el-dialog>

    <el-drawer title="系统更新" :visible.sync="drawer" :with-header="false">
      <el-timeline>
        <el-timeline-item v-for="item in sysLogList" :key="item.id" :timestamp="item.createDate" placement="top">
          <el-card>
            <h4>{{item.title}}</h4>
            <p v-for="(text,index) in item.messageList" :key="index">{{text.message}}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-drawer>
  </div>
</template>

<script>
import Func from '@/utils/func'
import { modifyPassword } from '@/api/user'
import { mapGetters } from 'vuex'
import TagsView from './TagsView'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import LangSelect from '@/components/LangSelect'
import ThemePicker from '@/components/ThemePicker'
import avatar from '@/assets/avatar.png'
import screenfull from 'screenfull'
import routeObj from '@/lang/zh'
import Model from '@/model/export'
import { findNewsPage } from '@/api/public'

export default {
  components: {
    Breadcrumb,
    Hamburger,
    Screenfull,
    SizeSelect,
    LangSelect,
    ThemePicker,
    TagsView,
  },
  data() {
    return {
      model: Model,
      avatar: avatar,
      menuList: [],
      isRefreshLoading: false,
      dropdownOpen: false,
      info: {},
      editForm: {
        oldPassword: '',
        newPassword: '',
      },
      sysLogList: [],
      drawer: false,
      dialogExportDownVisible: false,
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'user', 'device', 'currentView']),
  },
  watch: {
    $route: {
      handler({ name, query } = {}) {
        const matched = this.$route.matched
        this.menuList = []
        matched.forEach((v) => {
          const item = {
            path: v.path,
            name: v.name,
            title: routeObj.route[v.name],
          }
          this.menuList.push(item)
        })
      },
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    async handleSubmit() {
      const valid = await this.$refs.editForm.validate()
      if (valid) {
        const res = await Func.fetch(modifyPassword, this.editForm)
        if (res.data) {
          this.$message({
            type: 'success',
            message: '已修改密码',
          })
          this.handleClose()
        }
      }
    },
    async bellClick() {
      this.drawer = true
      let res = await Func.fetch(findNewsPage, {
        size: 999,
        orderBy: 'createDate',
        orderDir: 'desc',
      })
      if (res.data) {
        this.sysLogList = res.data.records
      }
    },
    handleClose() {
      this.editForm = {}
      this.dialogExportDownVisible = false
    },
    backward() {
      this.$refs['tags'].$emit('backward')
    },
    forward() {
      this.$refs['tags'].$emit('forward')
    },
    toggleSideBar() {
      this.$store.dispatch('toggleSideBar')
    },
    toogleScreenFull() {
      if (!screenfull.enabled) {
        this.$message({
          message: '浏览器不支持',
          type: 'warning',
        })
        return false
      }
      screenfull.toggle()
    },
    handleCommand(command) {
      switch (command) {
        case 'logout':
          this.logout()
          break
        case 'changePwd':
          this.$router.push('/profile/password')
          break
      }
    },
    logout() {
      // console.log(data)
      localStorage.clear()
      location.reload()
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    refresh() {
      this.$store.dispatch('delCachedView', this.currentView).then(() => {
        const { fullPath } = this.currentView
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect' + fullPath,
          })
        })
      })
    },
    closeCurrentView() {
      this.$store.dispatch('delView', this.currentView).then(({ visitedViews }) => {
        if (this.isActive(this.currentView)) {
          const latestView = visitedViews.slice(-1)[0]
          if (latestView) {
            this.$router.push(latestView)
          } else {
            this.$router.push('/')
          }
        }
      })
    },
    closeOthersTags() {
      // this.$router.push(this.currentView)
      this.$store.dispatch('delOthersViews', this.currentView)
    },
    closeAllTags() {
      this.$store.dispatch('delAllViews')
      this.$router.push('/')
    },
    back() {
      this.$router.back()
    },
    async initCompanyInfo(status) {
      this.dropdownOpen = status
      if (status && JSON.stringify(this.info) === '{}') {
        // await this.refreshInfo()
      }
    },
    /**
     * 显示下载中心
     * */
    showDownloadCenterHandler() {
      this.dialogExportDownVisible = true
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
$tabHeight: 34px;
.navbar {
  height: 42px;
  border-radius: 0 !important;
  border-bottom: 2px solid #2f4050;
  background-color: #fafafa !important;
  overflow: hidden;
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .hamburger-container {
    line-height: 40px;
    height: 40px;
    padding: 0 10px;
    border-right: 1px solid #e6e6e6;
    position: absolute;
    left: 0;
    background: #fafafa;
    z-index: 2;
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      margin: 0 8px;
    }

    .screenfull {
      height: 20px;
    }

    .international {
      vertical-align: top;
    }

    .theme-switch {
      vertical-align: 15px;
    }

    .avatar-container {
      height: 50px;
      margin-right: 30px;

      .avatar-wrapper {
        cursor: pointer;
        margin-top: 5px;
        position: relative;

        .user-avatar {
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }

  .nav-btn {
    position: absolute;
    /*width: 40px;*/
    height: 40px;
    text-align: center;
    color: #999;
    z-index: 2;
    top: 0;
    border: none;
    outline: 0;
    font-size: 12px;

    &:hover {
      background: #f2f2f2;
      color: #777;
    }
  }

  .nav-btn-right {
    right: 0;
    border-left: solid 1px #eee;
  }

  .nav-backward {
    right: 300px;
  }

  .nav-forward {
    right: 260px;
  }

  .nav-close {
    width: 60px;
    right: 200px;

    .el-dropdown-link {
      font-size: 12px;
    }
  }

  .nav-back {
    right: 160px;
  }

  .nav-refresh {
    right: 120px;
  }

  .nav-full-screen {
    right: 80px;
  }

  .nav-bell {
    /*right: 40px;*/
  }

  .nav-setting {
    right: 0;
  }

  .badge {
    display: inline-block;
    min-width: 10px;
    font-size: 12px;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    border-radius: 10px;
    padding: 3px;
    position: absolute;
    z-index: 100;
    background-color: #ed5565;
    color: #fff;
    top: 2px;
    right: 2px;
  }
}

.companyInfo {
  width: 180px;
  max-height: 600px !important;
}

.breadcrumb-div {
  padding-left: 50px;
  font-size: 14px;
}

.active {
  font-weight: 600;
  color: #000000;
}

.company-view {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.company {
  max-width: 100px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
  color: #000000;

  &:hover {
    /*background: #f2f2f2;*/
    color: #1e6abc !important;
  }
}

.nav-btn2 {
  position: absolute;
  /*width: 40px;*/
  height: 40px;
  text-align: center;
  z-index: 2;
  top: 0;
  border: none;
  outline: 0;
  font-size: 12px;
  background-color: #fafafa;
  display: flex;
  align-items: center;
  border: none !important;
  padding-right: 20px;
}

.select-menu {
  min-width: 120px;

  .menu-item {
    font-size: 14px;
    padding: 10px 20px;
    /*border-bottom: 1px solid #eeeeee;*/
  }

  &:last-child {
    border: none !important;
  }
}
</style>
