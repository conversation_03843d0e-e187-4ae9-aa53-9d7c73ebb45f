<template>
  <section>
    <template v-if="!item.children">
      <el-menu-item :index="item.path" @click="handleChangeRoute(item)" :class="[item.path===childActive?'isactiveN':' ']" style=" padding-left: 10px !important;
  padding: 0 10px !important;">
        <img :src="item.path===childActive?item.meta.iconSelected:item.meta.icon"
             style="width:20px;height:20px;margin-right:10px">

        <el-tooltip class="tstooltip" popper-class="popper" effect="dark" :disabled="isShowTooltip(item.meta.title)"
                    :content="item.meta.title" placement="left-end">
          <p style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis; color:#646464;font-weight:600;"
             :class="[item.path===childActive?'isactiveN':' ']">{{item.meta.title}}
          </p>
        </el-tooltip>
      </el-menu-item>
    </template>

    <el-submenu v-if="item.children" :index="item.path" :class="[item.path===childActive?'isactiveN':' ' , isFold?' ':'hiddev']">
      <template slot="title">
        <img :src="item.path===childActive?item.meta.iconSelected:item.meta.icon"
             style="width:20px;height:20px;margin-right:10px">
        <el-tooltip popper-class="popper" effect="dark" :disabled="isShowTooltip(item.meta.title)" :content="item.meta.title"
                    placement="left-end">
          <span style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis;" v-if="isFold?'hid':''">{{item.meta.title}}
          </span>
        </el-tooltip>
      </template>
      <el-menu-item-group v-if="item.children">
        <el-menu-item v-for="(leveltwo,index) in item.children" :key="index" :class="[leveltwo.path==childItemActive?'active':'']"
                      @click="handleChildChangeRoute(item,index)">
          {{ leveltwo.meta.title }}
        </el-menu-item>
      </el-menu-item-group>
    </el-submenu>
  </section>
  <!-- <section class="app-main">
    <template v-if="!item.children">
      <el-menu-item :index="item.path" >
        <img :src="item.path===childActive?item.meta.iconSelected:item.meta.icon"
             style="width:20px;height:20px;margin-right:10px">
        <span slot="title">{{item.meta.title}}</span>
      </el-menu-item>
      <el-submenu :index="item.path" v-if="item.children">
        <template slot="title">
          <img :src="item.path===childActive?item.meta.iconSelected:item.meta.icon"
               style="width:20px;height:20px;margin-right:10px">
          <span slot="title">{{item.meta.title}}</span>
        </template>
        
        <sidebarItem v-for="leveltwo in item.children" :key="leveltwo.path" :item="leveltwo"></sidebarItem>
      </el-submenu>
    </template>
  </section> -->
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  name: 'sideItem',
  props: {
    item: {
      type: Object,
      require: true,
      default: {}
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['currentRoute', 'isShowChild', 'parentActive', 'childActive', 'childItemActive', 'isFold']),
    key() {
      return this.$route.fullPath
    },
    showChild() {
      return this.$route.fullPath !== '/dashboard' && this.isShowChild
    },
    cesshi() {
      // console.log(this.childActive)
      return this.childActive
    }
  },
  methods: {
    isShowTooltip(title) {
      title = title || ''
      return !(title.length >= 9)
    },
    handleChangeRoute(route) {
      const childPath = route.children ? route.children[0] : ''
      this.$store.dispatch('changeChildRoute', { parent: route.path, child: childPath.path || '' })
      if (childPath) {
        this.$router.push({ name: childPath.name })
      } else {
        this.$router.push({ name: route.name })
      }
      // this.$parent.handleClose()
    },
    handleChildChangeRoute(parentRoute, childIndex) {
      const childPath = parentRoute.children[childIndex]
      this.$store.dispatch('changeChildRoute', { parent: parentRoute.path, child: childPath.path })
      this.$router.push({ name: childPath.name })
    }
  },
  created() {
    // console.log(this.item)
  }
}
</script>
<style lang="scss" scoped>
// ::v-deep .el-menu {
//   display: block !important;
// }
</style>
<style scoped>
.hid {
  display: none;
}

.el-menu-item,
.el-submenu__title {
  height: 40px !important;
  line-height: 40px !important;
}
.el-submenu >>> .el-submenu__title {
  height: 40px !important;
  line-height: 40px !important;
}
.el-submenu {
}
.el-menu-item {
  font-size: 11px !important;
}
.tstooltip >>> .el-tooltip__popper.is-dark {
  opacity: 0.2;
}
.el-menu {
  border-right: solid 1px #fff !important;
}
.el-menu-item {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.el-menu-item .app-main-sidebar-item-img.tsimf {
  margin-right: 10px;
}

.el-menu-item:hover {
  /* background-color: #33cad9 !important;
  color: #fff !important; */
  /* color: #33cad9 !important; */
}
.el-submenu >>> .el-submenu__title:hover {
  background-color: #33cad9 !important;
  color: #fff !important;
}
.el-submenu >>> .el-submenu__title i:hover {
  color: #fff !important;
}
.el-submenu >>> .el-submenu__title {
  font-weight: 600 !important;
  color: #646464 !important;
}
.isactiveN {
  background-color: #33cad9 !important;
  color: #fff !important;
}
.isactiveN >>> .el-submenu__title {
  color: #fff !important;
}
.el-menu-item.is-active {
  color: #303133;
}
.active {
  color: #33cad9 !important;
  background-color: #f5feff !important;
}
.isactiveN >>> .el-submenu__title i {
  color: #fff !important;
}
.el-submenu >>> .el-submenu__title {
  font-size: 12px !important;
  height: 40px !important;
}

.el-submenu .el-menu-item {
  height: 40px !important;
  line-height: 40px !important;
  min-width: 120px !important;
}
.el-menu >>> .el-menu-item-group__title {
  padding: 0 !important;
}
.el-submenu >>> .el-submenu__title {
  padding-left: 10px !important;
  padding: 0 10px !important;
}
.hiddev >>> .el-submenu__title i {
  display: none !important;
}
</style>