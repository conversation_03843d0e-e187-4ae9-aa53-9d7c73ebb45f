<template>
    <div class="tags-view-container">
        <div ref="scrollPane" class="tags-view-wrapper" :style="{marginLeft:left+'px'}">
            <router-link
                    v-for="tag in visitedViews"
                    ref="tag"
                    :class="isActive(tag)?'active':''"
                    :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
                    :key="tag.path"
                    tag="span"
                    class="tags-view-item"
            >
                {{ generateTitle(tag.title) }}
                <span v-if="tag.path!=='/dashboard'"
                      class="el-icon-close"
                      @click.prevent.stop="closeSelectedTag(tag)"
                />
            </router-link>
        </div>
    </div>
</template>

<script>
    import ScrollPane from '@/components/ScrollPane'
    import {generateTitle} from '@/utils/i18n'

    let tagLocation = []
    export default {
        components: {ScrollPane},
        data() {
            return {
                visible: false,
                top: 0,
                left: 0,
                selectedTag: {},
                max: 92,
                time: 0
            }
        },
        computed: {
            visitedViews() {
                return this.$store.state.tagsView.visitedViews
            }
        },
        watch: {
            $route() {
                this.addViewTags()
                this.moveToCurrentTag()
            }
        },
        mounted() {
            this.addViewTags()
            this.$on('backward', this.backward)
            this.$on('forward', this.forward)
        },
        methods: {
            generateTitle, // generateTitle by vue-i18n
            isActive(route) {
                return route.path === this.$route.path
            },
            addViewTags() {
                const {name} = this.$route
                if (name) {
                    this.$store.dispatch('addView', this.$route)
                }
                return false
            },
            backward() {
                this.time = 0
                this.left = 0
                this.updateTags()
            },
            forward() {
                this.time = this.time + 2
                this.left = -(tagLocation[this.time] !== undefined ? tagLocation[this.time] : this.max)
                this.updateTags()
            },
            updateTags() {
                const tags = this.$refs.tag
                tagLocation = []
                tags.forEach((tag, i) => {
                    tagLocation.push(tag.$el.offsetLeft)
                    if (i === tags.length - 1) {
                        this.max = tag.$el.offsetLeft
                    }
                })
            },
            moveToCurrentTag() {
                const tags = this.$refs.tag
                this.updateTags()
                const that = this
                this.$nextTick(() => {
                    tags.forEach((tag, i) => {
                        if (tag.to.path === this.$route.path) {
                            if (i > 4) {
                                that.left = -tagLocation[i - 4]
                            } else {
                                that.left = 0
                            }
                            // when query is different then update
                            if (tag.to.fullPath !== this.$route.fullPath) {
                                this.$store.dispatch('updateVisitedView', this.$route)
                            }
                            return false
                        }
                    })
                })
            },
            closeSelectedTag(view) {
                this.$store.dispatch('delView', view).then(({visitedViews}) => {
                    if (this.isActive(view)) {
                        const latestView = visitedViews.slice(-1)[0]
                        if (latestView) {
                            this.$router.push(latestView)
                        } else {
                            this.$router.push('/')
                        }
                    }
                })
            }
        }
    }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
    .tags-view-container {
        width: 100%;
        height: 40px;
        margin-left: 37px;
        overflow: hidden;

        .tags-view-wrapper {
            transition: all .5s;
            position: relative;

            .tags-view-item {
                display: inline-block;
                position: relative;
                cursor: pointer;
                height: 40px;
                line-height: 40px;
                color: #999;
                padding: 0 15px;
                font-size: 12px;

                &.active {
                    background-color: #efefef;
                    color: #000;
                }
            }
        }
    }
</style>

<style rel="stylesheet/scss" lang="scss">
    //reset element css of el-icon-close
    .tags-view-wrapper {
        .tags-view-item {
            .el-icon-close {
                width: 16px;
                height: 16px;
                vertical-align: 2px;
                border-radius: 50%;
                text-align: center;
                transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
                transform-origin: 100% 50%;

                &:before {
                    transform: scale(0.6);
                    display: inline-block;
                    vertical-align: -3px;
                }

                &:hover {
                    background-color: #888;
                    color: #fff;
                }
            }
        }
    }
</style>
