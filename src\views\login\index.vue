<template>
  <div class="login-bg">
    <div class="bg"></div>
    <!-- <div class="login-logo" v-if="COMPANY==='TD'">
      <img src="@/assets/login/td_logo.png" alt="logo" style="width:30px;height:30px;" />
      <p>泰达煤业有限公司</p>
    </div> -->
    <div class="login-logo" style="margin-top:40px">
      <img src="@/assets/login/snlogo.png" alt="logo" style="width:100%;height:50px"/>
      <!-- <p>山西汇锦数能有限公司</p> -->
    </div>
    <div class="bg-overlay">
      <img src="@/assets/login/hysn.png"/>
    </div>
    <login-page></login-page>
    <footer>
      <p class="login-copyright">©2022 智慧用煤有限公司</p>
    </footer>
  </div>
</template>

<script>
import loginPage from './login'

export default {
  name: 'AppLogin',
  components: {loginPage},
  data() {
    return {
      COMPANY: process.env.VUE_APP_COMPANY
    }
  },
  created() {
  },
  methods: {}
}
</script>
<style scoped rel="stylesheet/scss" lang="scss">
.bg {
  background-image: url('../../assets/login/login_bg.png');
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
}

.login-bg {
  width: 100%;
  height: 100%;
  position: relative;
}

.login-logo {
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 14px 0 0 23px;

  img {
    width: 280px;
    height: 60px;
  }

  p {
    text-indent: 0.5em;
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #ffffff;
  }
}

.bg-overlay {
  position: absolute;
  top: 45vh;
  // left: 179px;
  left: 20vh;
  // top: 350px;
  & > img {
    width: 500px;
    height: 200px;
  }
}

footer {
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  bottom: 10px;
  text-align: center;

  .login-copyright {
    color: #d6d7d7;
  }
}
</style>
