<template>
  <div class="center-container">
    <div class="panel">
      <div class="content">
        <el-form :show-message="true" :status-icon="true" class="card-box login-form" autoComplete="on" :model="loginForm"
                 :rules="loginRules" ref="loginForm" @submit.native.prevent="handleLogin" label-position="left">

          <!-- <h3 class="title">
            <img src="@/assets/login/logo.png" alt="Logo" v-if="COMPANY==='TD'" />
            <img src="@/assets/login/login_sn.png" alt="Logo" v-else style="width:60px" />
            <p>煤焦智慧管理系统</p>
          </h3> -->
          <div class="title">欢迎登录</div>
          <el-form-item prop="username">
            <el-input name="username" size="medium" clearable v-model="loginForm.username" autoComplete="on" placeholder="请输入用户名"
                      @focus="activebtn">
              <!-- <img src="@/assets/login/noselect.png" slot="prefix" style="width: 15px;margin: 14px 10px 0;" /> -->

              <img v-if="active" src="@/assets/login/activeuse.png" slot="prefix" style="width: 15px;margin: 14px 10px 0;" />
              <img v-else src="@/assets/login/noselect.png" slot="prefix" style="width: 15px;margin: 14px 10px 0;" />

            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input name="password" type="password" size="medium" v-model="loginForm.password" placeholder="请输入密码"
                      @focus="activebtnV">

              <img v-if="activeV" src="@/assets/login/activepwd.png" slot="prefix" style="width: 15px;margin: 14px 10px 0;" />
              <img v-else src="@/assets/login/nopass.png" slot="prefix" style="width: 15px;margin: 14px 10px 0;" />

            </el-input>
          </el-form-item>
          <div class="login-row">
            <el-button type="submit" class="login-btn el-button " :class="{ processing: loading }" :disabled="loading"
                       style="width: 100%; margin-bottom: 30px;" @click.native.prevent="handleLogin">
              <span>登录</span>
            </el-button>
          </div>
        </el-form>
      </div>
    </div>
    <el-dialog :before-close="handelClose" :visible.sync="visible" append-to-body title="提示" width="600px">
      <el-result :title="reminderMessage" icon="warning"></el-result>
      <div slot="footer">
        <el-button type="primary"
                   @click="handelConfirm">{{reminderMessage==='尊敬的客户，您的费用即将到期，请尽快缴费，以免影响正常使用，联系电话：19935360195(微信同号).'?'知道了':'确定'}}</el-button>
        <el-button @click="handelClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { setToken, setIscl } from '@/utils/auth'
import router from '@/router'

export default {
  name: 'login',
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value.length < 3) {
        callback(new Error('密码不能小于3位'))
      } else {
        callback()
      }
    }
    return {
      COMPANY: process.env.VUE_APP_COMPANY,
      loginForm: {
        username: process.env.NODE_ENV === 'development' ? 'admin' : '',
        password: process.env.NODE_ENV === 'development' ? 'admin123' : ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      pwdType: 'password',
      loading: false,
      active: false,
      activeV: false,
      reminderMessage: '',
      visible: false,
      token: ''
    }
  },
  computed: {
    ...mapGetters(['company'])
  },
  beforeCreate() {
    console.log(window.name)
    if (window.name) {
      this.$store.commit('SET_TOKEN', window.name)
      setToken(window.name)
      setIscl(false)
      this.$router.push('./')
    }
  },
  methods: {
    handelClose() {
      this.visible = false
      if (this.token) {
        this.$router.push({ path: '/dashboard' })
      }
    },
    handelConfirm() {
      this.visible = false
      if (this.token) {
        this.$router.push({ path: '/dashboard' })
      }
    },
    async handleLogin() {
      const valid = await this.$refs.loginForm.validate()
      if (valid) {
        this.loading = true
        try {
          const res = await this.$store.dispatch('LoginByUsername', this.loginForm)
          if (res) {
            this.token = res.data.token
            if (res.data.reminderMessage) {
              this.reminderMessage = res.data.reminderMessage
              this.visible = true
            } else {
              this.$router.push({ path: '/dashboard' })
            }
          }
        } catch (e) {
          console.log(e)
        } finally {
          this.loading = false
        }
      }
    },
    activebtn() {
      this.active = !this.active
      this.activeV = false
    },
    activebtnV() {
      this.activeV = !this.activeV
      this.active = false
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__header {
  border-radius: 10px;
  background-color: #fff;

  .el-dialog__title {
    color: #303133;
    font-size: 18px;
    font-weight: bold;
  }

  .el-icon-close:before {
    color: #909399;
  }
}

::v-deep .el-result {
  flex-direction: row;
  padding: 20px;

  .el-result__icon {
    margin-right: 10px;
    svg {
      width: 32px;
    }
  }

  .el-result__title {
    margin: 0;

    p {
      font-size: 15px;
      text-align: left;
    }
  }
}
</style>

<style lang="scss" rel="stylesheet/scss">
.center-container {
  width: 20%;
  position: absolute;
  // top: 330px;
  // right: 179px;
  // top: 300px;
  top: 35vh;
  right: 20vh;

  .panel {
    // width: 400px;
    width: 100%;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    padding: 20px;
    // border-radius: 10px;

    .content {
      position: relative;
      .title {
        display: flex;
        align-items: center;
        flex-flow: column;
        justify-content: center;
        font-size: 30px;
        color: #2f79e8;
        padding: 20px;
        // img {
        //   width: 48px;
        // }
        // p {
        //   margin-top: 15px;
        //   font-size: 28px;
        //   font-weight: 400;
        //   font-family: SimHei;
        //   color: #101820;
        //   text-align: center;
        //   margin-bottom: 44.5px;
        // }
      }

      .show-pwd {
        position: absolute;
        right: 10px;
        top: 5px;
        color: #000;
      }

      input {
        height: 40.1px;
        width: 100%;
        font-size: 1rem;
        font-family: Ubuntu, Roboto, Helvetica, Arial, sans-serif;
        color: #000;
        margin: 3px 0 10px;
        padding: 0 40px;
        // border-radius: 3px;
        // background: #f4faff;
        border: solid 1px #e3e3e3;
        transition: opacity 0.3s ease;
      }

      .el-input__suffix {
        i {
          font-size: 18px;
        }
      }
      .el-input__inner {
        // border-radius: 10px;
        // border: 1px solid #fff;
        color: #000;
      }

      button {
        color: rgba(255, 255, 255, 0.8);
        height: 40.1px;
        border-radius: 5px;
        margin: 40px auto;
        background: #2f79e8;
        border: 0;
        font-size: 14.5px;
        padding: 8px 0;
        font-family: Ubuntu, Roboto, Helvetica, Arial, sans-serif;
        text-transform: uppercase;
        font-weight: 400;
        width: 100%;
        cursor: pointer;
      }

      .login-row {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .el-form-item {
        margin-bottom: 15px !important;
      }

      .login-btn {
        cursor: pointer;
        overflow: hidden;
        transition: width 0.3s 0.15s, font-size 0.1s 0.15s;
        position: relative;
        &:after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          margin-left: -15px;
          margin-top: -15px;
          width: 30px;
          height: 30px;
          border: 2px dotted #fff;
          border-radius: 50%;
          border-left: none;
          border-bottom: none;
          animation: rotate 0.5s infinite linear;
          transition: opacity 0.1s 0.4s;
          opacity: 0;
        }

        ::v-deep .el-dialog .el-result .el-result__title p {
          font-size: 300px !important;
        }

        &.processing {
          width: 40px !important;
          font-size: 0;

          &:after {
            opacity: 1;
          }
        }
      }
    }
  }
}
</style>
