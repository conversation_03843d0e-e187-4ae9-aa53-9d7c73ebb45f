<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :buttomlist="buttomlist" :actionList="actionList"
            title="物资分类" otherHeight="130">

      <!-- <el-table-column label="状态" slot="state">
        <template slot-scope="scope">
          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'success' : (scope.row.state == 'PASS' ? 'warning' : (scope.row.state == 'REJECT' ? 'danger' :(scope.row.state == 'PAYED' ? 'success' : 'info')))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '待审核' : (scope.row.state == 'PASS' ? '已审核' : (scope.row.state == 'REJECT' ? '拒绝':(scope.row.state == 'PAYED' ? '已付款' : '')))) }}
          </el-tag>
        </template>
      </el-table-column> -->

      <!-- <el-table-column label="附件" slot="attachmentList" prop="attachmentList" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handlePreviewAttachment(scope.row)">查看附件</el-button>
        </template>
      </el-table-column> -->

      <el-table-column label="操作" slot="opt" max-width="150" width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row,'update')">
                编辑
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag class="opt-btn" style="cursor: pointer;" color="#2f79e8" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>

        </template>
      </el-table-column>
    </s-curd>

    <el-dialog width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
               label-position="top" v-if="dialogFormVisible" :rules="rules">
        <el-row>
          <el-col :span="12">
            <el-form-item label="分类名称" prop="name">
              <el-input v-model="entityForm.name" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息" prop="remarks">
              <el-input v-model="entityForm.remarks" type="textarea" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button v-if="perms[`${curd}:save`]||false" type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                   @click="saveEntityForm('entityForm')">
          保存
        </el-button>
        <el-button @click="cancel" class="dialog-footer-btns">取消</el-button>
        <!-- v-if="isshow"  -->
        <!-- <el-button v-else @click="SaveDraft('entityForm')" class="dialog-footer-btns">保存草稿</el-button> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/material/MaterialClassification'
import { validatePhone } from '@/utils/validate'
import { MaterialClassificationOption } from '@/const'
// import { contractList } from '@/api/quality'
// import productModel from '@/model/product/productList'
// import { chooseContract } from '@/api/quality'
// import { createMemoryField } from '@/utils/index'
export default {
  name: 'MaterialClassification',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'MaterialClassification',
      model: Model,
      addressValue: '',
      entityForm: { ...Model.model },
      noticeForm: {},
      uploadData: { refId: '', refType: 'ContractSell' },
      limitNum: 50,
      username: 'ss',
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      filterOption: { ...MaterialClassificationOption },
      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        phone: [{ validator: validateIsPhone, trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      rules: {
        name: { required: true, message: '请输入分类名称', trigger: 'blur' }
      },
      // isshow: false,
      drawer: {
        visible: false,
        list: [],
        preview: [],
        dateList: []
      },
      emptyImgPath: require(`@/assets/empty.jpg`)
    }
  },
  methods: {
    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      // this.isshow = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    cancel() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      // this.isshow = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    async handleUpdate(item, type) {
      this.entityForm = { ...item }
      if (type == 'update') {
        // this.isshow = true
        this.dialogStatus = 'update'
      }
      this.dialogFormVisible = true
      // 上传所需要的配置
      this.uploadData.refId = item.id
      const { data } = await this.model.getUploadList(item.id)
      this.entityForm = { ...data }
    },
    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      if (res.data.attachmentList) {
        this.drawer.list = res.data.attachmentList
        this.drawer.preview = res.data.attachmentList.map((item) => item.uri)
        this.drawer.visible = true
      }
    },

    async passfn(id) {
      const res = await this.model.savepass({ id: id })
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    },

    async rejectfn(id) {
      const res = await this.model.savereject({ id: id })
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.contentbox {
  width: 500px;
  margin: 100px auto 0 auto;
  font-size: 12px;
  .line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tsline {
    float: right;
    margin-top: 40px;
  }
  .inputbox {
    width: 100px;
    margin-bottom: 10px;
    border-bottom: solid 1px #ccc;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
<style scoped>
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
/* .dialog-footer >>> .dialog-footer-btns {
  border: 1px solid #2f79e8;
  color: #2f79e8;
} */
/* .opt-btn {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
}
.opt-btnV {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
} */
</style>