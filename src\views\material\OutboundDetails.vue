<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :buttomlist="buttomlist" :actionList="actionList"
            :showSummary='true' title="物资信息" otherHeight="130">

      <el-table-column label="是否含税" slot="hasFax" align="center">
        <template slot-scope="scope" v-if="scope.row.hasFax">
          <el-tag :type="(scope.row.hasFax=='Y' ? 'success':(scope.row.hasFax == 'N' ? 'warning' :''))" size="mini">
            {{ scope.row.hasFax == 'Y' ? '是' : (scope.row.hasFax == 'N' ? '否' : '')}}
          </el-tag>
        </template>
      </el-table-column>

      <!-- <el-table-column label="附件" slot="attachmentList" prop="attachmentList" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handlePreviewAttachment(scope.row)">查看附件</el-button>
        </template>
      </el-table-column> -->

      <el-table-column label="操作" slot="opt" max-width="200" width="200" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <!-- <div>
              <el-tag class="opt-btn" color="#FF726B" @click="handleViewLog(scope.row)">
                库存日志
              </el-tag>
            </div> -->
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row,'update')">
                编辑
              </el-tag>
            </div>
            <!-- <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" color="#33CAD9" @click="handleUpdate(scope.row,'Inventory')">
                盘库
              </el-tag>
            </div> -->
            <!-- <div v-if="perms[`${curd}:delete`] || false">
              <el-tag class="opt-btnV" effect="plain" style="color:#33CAD9;border-color: #33CAD9;cursor: pointer;"
                      @click="handleDel(scope.row)">删除
              </el-tag>
            </div> -->

            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag class="opt-btn" style="cursor: pointer;" color="#2f79e8" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>

          </div>
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <!-- :rules="rules" -->
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
               label-position="top" v-if="dialogFormVisible" :rules="rules">
        <el-row>
          <el-col>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="物资名称" prop="name">
                    <el-input v-model="entityForm.name" autocomplete="off" @input="handleBlurV(entityForm)" clearable
                              :disabled="isAble" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="物资编号" prop="code">
                    <el-input v-model="entityForm.code" autocomplete="off" clearable :disabled="isAble" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="物资类型" prop="categoryId">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                 :disabled="isAble" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="单位" prop="stockUnit">
                    <el-input v-model="entityForm.stockUnit" autocomplete="off" clearable :disabled="isAble" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="规格" prop="spec">
                    <el-input v-model="entityForm.spec" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="购买单位" prop="buyName">
                    <!-- <el-input v-model="entityForm.buyName" autocomplete="off" clearable /> -->
                    <!-- @input="handleHistoryRecord" -->
                    <el-autocomplete v-model="entityForm.buyName" :fetch-suggestions="queryApplicantCompanySearchAsync"
                                     placeholder="请输入委托单位" style="width: 100%" @focus="applicantCompanyFocus()"></el-autocomplete>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50" v-if="dialogStatus =='Inventory'">
                <el-col>
                  <el-form-item label="原库存" prop="stockAmount">
                    <el-input v-model="entityForm.stockAmount" autocomplete="off" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')" @blur="handleBlur(entityForm)" :disabled="isAble" />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
              <el-row type="flex" :gutter="50" v-if="dialogStatus =='Inventory'">
                <el-col>
                  <el-form-item label="盘库日期" prop="date">
                    <date-select v-model="entityForm.date" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="盘库数量" prop="newAmount">
                    <el-input v-model="entityForm.newAmount" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- <el-row type="flex" :gutter="50" v-if="dialogStatus !='Inventory'">
                <el-col>
                  <el-form-item label="单价" prop="price">
                    <el-input v-model="entityForm.price" autocomplete="off" clearable oninput="value=value.replace(/[^0-9.]/g,'')"
                              @input="handleBlur(entityForm)">
                      <template slot="append">
                        <span style="margin-right:20px">是否含税:</span>
                        <el-radio-group v-model="hasFax" size="small" @change="changeTheme">
                          <el-radio :label="'Y'">是</el-radio>
                          <el-radio :label="'N'">否</el-radio>
                        </el-radio-group>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col v-if="dialogStatus=='create'">
                  <el-form-item label="初始库存" prop="initStock">
                    <el-input v-model="entityForm.initStock" autocomplete="off" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleBlur(entityForm)" :disabled="isAble" />
                  </el-form-item>
                </el-col>
              </el-row> -->
              <!-- <el-row type="flex" :gutter="50" v-if="dialogStatus !='Inventory'">
                <el-col v-if="dialogStatus=='update'">
                  <el-form-item label="库存" prop="stockAmount">
                    <el-input v-model="entityForm.stockAmount" autocomplete="off" clearable disabled />
                  </el-form-item>
                </el-col>
                <el-col v-if="dialogStatus=='update'">
                  <el-form-item label="金额" prop="totalMoney">
                    <el-input v-model="entityForm.totalMoney" autocomplete="off" clearable :disabled="isAble" />
                  </el-form-item>
                </el-col>
              </el-row> -->

              <el-row type="flex" :gutter="50">
                <!-- <el-col>
                  <el-form-item label="用途" prop="useDesc">
                    <el-input type="textarea" v-model="entityForm.useDesc" autocomplete="off" clearable />
                  </el-form-item>
                </el-col> -->
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div v-if="dialogStatus=='Inventory'">
          <el-button v-if="perms[`${curd}:save`]||false" type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="saveEntityForm('entityForm' ,'Inventory')">
            保存
          </el-button>
        </div>
        <div v-else>
          <el-button v-if="perms[`${curd}:save`]||false" type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="saveEntityForm('entityForm')">

            保存
          </el-button>
        </div>
        <el-button @click="handleClose" class="dialog-footer-btns">取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看库存日志 -->
    <el-dialog width="900px" top="5vh" title="库存日志" :visible.sync="log.visible" :destroy-on-close="false" class="historyLog">
      <s-curd class="curdLog" ref="stockLog" v-if="log.visible" name="stockLog" :model="modelLog" :actions="[]"
              :list-query="listQueryLog" otherHeight="180">
        <el-table-column label="类型" slot="logType">
          <!-- ,CK 出库 RK 入库 PD 盘点 -->
          <template slot-scope="scope">
            <el-tag :type="(scope.row.logType=='CK' ? 'danger':(scope.row.logType == 'RK' ? 'success' : (scope.row.logType == 'STOCK_CHECK' ? 'warning':(scope.row.logType == 'XJ' ? 'danger' : ''))))"
                    size="mini">
              {{ scope.row.logType == 'CK' ? '出库' : (scope.row.logType == 'RK' ? '入库' : (scope.row.logType == 'STOCK_CHECK' ? '盘库' : (scope.row.logType == 'XJ' ? '新建' : ''))) }}
            </el-tag>
          </template>
        </el-table-column>
      </s-curd>
    </el-dialog>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/material/OutboundDetails'
import StockLog from '@/model/material/stockLog'
import { validatePhone } from '@/utils/validate'
import { OutboundDetailsOption } from '@/const'
import { contractList } from '@/api/quality'
// import productModel from '@/model/product/productList'
import pinyin from 'js-pinyin'

import { chooseContract } from '@/api/quality'
import { createMemoryField } from '@/utils/index'
export default {
  name: 'OutboundDetails',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'OutboundDetails',
      model: Model,
      addressValue: '',
      entityForm: { ...Model.model },
      noticeForm: {},
      uploadList: [], // 用于展示
      uploadData: { refId: '', refType: 'ContractSell' },
      limitNum: 50,
      username: 'ss',
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      filterOption: { ...OutboundDetailsOption },
      memoryEntity: { fields: {}, triggered: false },
      rules: {
        name: { required: true, message: '请输入物资名称', trigger: 'blur' },
        code: { required: true, message: '请输入物资编号', trigger: 'blur' },
        categoryId: { required: true, message: '请输入物资类型', trigger: 'blur' },
        stockUnit: { required: true, message: '请输入单位', trigger: 'blur' },
        hasFax: { required: true, message: '请输入选择是否含税', trigger: 'change' }
      },
      drawer: {
        visible: false,
        list: [],
        preview: [],
        dateList: []
      },
      emptyImgPath: require(`@/assets/empty.jpg`),
      totalMoney: '',

      nameEntity: { options: [], active: '' },

      modelLog: StockLog,
      log: { visible: false, loading: null },
      listQueryLog: {},
      isAble: false,
      hasFax: 'Y',
      applicantCompanyOftenList: []
    }
  },
  created() {
    this.entityForm = { ...this.entityForm, date: new Date().Format('yyyy-MM-dd') }
  },
  computed: {
    totalMoneyc() {
      if (this.entityForm.entryAmount != '' && this.entityForm.price != '') {
        this.totalMoney = parseInt(entityForm.entryAmount * entityForm.price)
        if (isNaN(this.totalMoney) == false) {
          this.entityForm.totalMoney = this.totalMoney
        }
      }
    }
  },
  watch: {
    'entityForm.categoryName'(name) {
      if (name) {
        const item = this.nameEntity.options.find((item) => item.name === name)
        this.nameEntity.active = item.name
        this.entityForm.categoryName = item.name
        this.entityForm.categoryId = item.id
      } else {
        this.nameEntity.active = ''
        this.entityForm.categoryName = ''
        this.entityForm.categoryId = ''
      }
    }
  },

  methods: {
    async applicantCompanyFocus() {
      // var r = localStorage.getItem('applicant_company')
      let { data } = await this.model.findDistinctBuyName()
      console.log('获取购买单位历史记录')
      // console.log(data)
      var r = data.map((item) => {
        return { value: item.buyName }
      })
      console.log(r)
      if (r != null) {
        // this.applicantCompanyOftenList = JSON.parse(r)
        this.applicantCompanyOftenList = r
      }
    },
    queryApplicantCompanySearchAsync(queryString, cb) {
      var applicantCompanyOftenList = this.applicantCompanyOftenList
      console.log(applicantCompanyOftenList)
      var results = queryString ? applicantCompanyOftenList.filter(this.createFilter(queryString)) : applicantCompanyOftenList
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        cb(results)
      }, 1000 * Math.random())
    },
    createFilter(queryString) {
      return (item) => {
        return item.value.toString().toLowerCase().includes(queryString.toString().toLowerCase()) === true
      }
    },

    handleBlurV(entityForm) {
      //将汉字转义成汉字的大写首字母
      let char = ''
      pinyin.setOptions({ checkPolyphone: false, charCase: 0 })
      char = pinyin.getCamelChars(this.entityForm.name)
      this.entityForm.code = char
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.entityForm.hasFax = 'Y'
    },
    changeTheme(val) {
      this.hasFax = val
      this.entityForm.hasFax = val
    },

    handleBlur(entityForm) {
      if (this.dialogStatus == 'create') {
        entityForm.initStock = 0
      } else if (this.dialogStatus == 'update') {
        if (entityForm.stockAmount != '' && entityForm.price != '') {
          this.totalMoney = parseInt(entityForm.stockAmount * entityForm.price)
          if (isNaN(this.totalMoney) == false) {
            this.entityForm.totalMoney = this.totalMoney
          }
        }
      }
    },
    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      this.isAble = false
      // this.uploadList = [] // 清空下载列表
      // this.contractEntity.active = []
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      this.entityForm = { ...this.entityForm, date: new Date().Format('yyyy-MM-dd') }
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    handleNameEntity(val) {
      this.entityForm.categoryName = val
    },
    async handleUpdate(item, type) {
      this.entityForm = { ...item }
      if (type == 'update') {
        this.isAble = true
        this.dialogStatus = 'update'
      } else if (type == 'Inventory') {
        //盘库
        this.dialogStatus = 'Inventory'
        this.isAble = true
      }
      this.dialogFormVisible = true
      const { data } = await this.model.getUploadList(item.id)
      this.entityForm = { ...data }
      this.entityForm = { ...this.entityForm, date: new Date().Format('yyyy-MM-dd') }
      this.hasFax = this.entityForm.hasFax
    },

    async handleViewLog(row) {
      this.log.visible = true
      await this.$nextTick()
      // this.listQueryLog = { ...this.listQuery }
      this.listQueryLog = { ...this.listQueryLog, filter_EQS_itemId: row.id }
      this.$refs['stockLog'].searchChange()
    },

    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      if (res.data.attachmentList) {
        this.drawer.list = res.data.attachmentList
        this.drawer.preview = res.data.attachmentList.map((item) => item.uri)
        this.drawer.visible = true
      }
    },

    async passfn(id) {
      const res = await this.model.savepass({ id: id })
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    },

    async rejectfn(id) {
      const res = await this.model.savereject({ id: id })
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.curdLog {
  height: 62vh !important;
}
.smart-search {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  width: 100%;
  margin-bottom: 5px;
  border-radius: 3px;

  .prepend {
    .el-dropdown {
      cursor: pointer;
    }

    .el-icon-arrow-down {
      width: 26px;
      text-align: center;
    }

    .search-key {
      display: inline-block;
      height: 30px;
      line-height: 30px;
    }
  }

  .search-component {
    width: 100%;
    flex: 1;
    .el-input {
      width: 100%;
      height: 30px;
      line-height: 30px;
      .el-input__inner {
        border: none;
      }
      & .is-focus {
        .el-input__inner {
          border-color: #dcdfe6 !important;
        }
      }
    }
  }
}
.contentbox {
  width: 500px;
  margin: 100px auto 0 auto;
  font-size: 12px;
  .line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tsline {
    float: right;
    margin-top: 40px;
  }
  .inputbox {
    width: 100px;
    margin-bottom: 10px;
    border-bottom: solid 1px #ccc;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      margin-right: 15px;
      width: 75px;
      height: 75px;
      position: relative;
      .icon {
        width: 9px;
        height: 9px;
        cursor: pointer;
        position: absolute;
        right: 5px;
        z-index: 9;
        top: 6px;
      }
      .img {
        border-radius: 4px;
        border: 1px solid #ff9639;
        background: #f4f4f4;
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
<style scoped>
.search-component >>> .el-input__inner {
  border: none;
}
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
</style>