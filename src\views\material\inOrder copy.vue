<template>
  <div class="app-container">
    <filter-table :options="filterOption" :showAdd="perms[`${curd}:save`]||false" :showImport="perms[`${curd}:addimport`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" @add="handleCreate" @filter="handleFilter"
                  @import="handleImpiort" @reset="handleFilterReset" />
    <!-- :searchDate="searchDate"-->
    <s-curd :ref="curd" :actionList="actionList" :buttomlist="buttomlist" :countList="countList" :list-query="listQuery"
            :model="model" :name="curd" :statetype="activetype" otherHeight="180" title="供应商">

      <el-table-column slot="inOrderItemList" label="物资列表" type="expand" width="80" align="center">
        <template slot-scope="scope">
          <!-- #ECF1FE :header-cell-style="{background:'#33cad9',color:'#fff'}"-->
          <el-table :data="scope.row.inOrderItemList" :default-expand-all='true' :header-cell-style="{background:'#ECF1FE'}"
                    style="width: 100%">
            <el-table-column label="物资名称" prop="itemName" width="120">
            </el-table-column>
            <el-table-column label="批次号" prop="batchNo" width="120">
            </el-table-column>
            <el-table-column label="物资类型" prop="categoryName" width="120">
            </el-table-column>
            <el-table-column label="日期" prop="createDate" width="150">
            </el-table-column>
            <el-table-column label="单位" prop="stockUnit">
            </el-table-column>
            <el-table-column label="数量" prop="entryAmount">
            </el-table-column>
            <el-table-column label="价格" prop="price">
            </el-table-column>
            <el-table-column label="用途" prop="useDesc">
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column slot="ext" label="物资" align="center">
        <template slot-scope="scope">
          {{scope.row.ext}}
        </template>
      </el-table-column>

      <el-table-column slot="state" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :'info'))))"
                  size="mini">
            {{
              scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '待审核' : (scope.row.state == 'PASS' ? '已审核' : (scope.row.state == 'REJECT' ? '拒绝' : '')))
            }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column slot="opt" align="center" fixed="right" label="操作" max-width="150" width="150">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <el-tag v-if="scope.row.state == 'NEW'" class="opt-btn" color="#FF726B" @click="handleUpdate(scope.row.id,'review')">
              去审核
            </el-tag>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag v-if="scope.row.state == 'DRAFT'" class="opt-btn" color="#FF726B"
                      @click="handleUpdate(scope.row.id,'update')">
                编辑
              </el-tag>
            </div>
            <div>
              <el-tag v-if="scope.row.state == 'PASS'" class="opt-btn" color="#FF726B"
                      @click="handleUpdate(scope.row.id,'watch')">
                查看详情
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" class="opt-btn" color="#2f79e8"
                      style="cursor: pointer;" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog ref="dialogStatus" :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false"
               width="1080px" :fullscreen="screen.full" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" top="5vh">
      <el-form v-if="dialogFormVisible" ref="entityForm" :model="entityForm" :rules="rules" :show-message="true"
               :status-icon="true" label-position="top" label-width="90px">
        <el-row>
          <el-row v-if="reviewLogList.length>0" :gutter="50" type="flex">
            <el-col>
              <el-form-item label="步骤" prop="reviewLogList">
                <el-steps :active="reviewLogList.length">
                  <el-step v-for="(item,index) in reviewLogList" :key="index"
                           :description="item.createDate+' '+item.reviewUserName" :title="item.reviewResult">
                  </el-step>
                </el-steps>
              </el-form-item>
            </el-col>
          </el-row>
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row :gutter="50" type="flex">
                <el-col>
                  <el-form-item label="入库日期" prop="commitDate">
                    <date-select v-model="entityForm.commitDate" :disabled="dialogStatus==='update'" clearable />
                  </el-form-item>
                </el-col>
                <!-- <el-col>
                  <el-form-item label="存放人" prop="commitBy">
                    <el-input v-model="entityForm.commitBy" clearable />
                  </el-form-item>
                </el-col> -->
                <el-col>
                  <el-form-item label="保管人" prop="keepMan">
                    <el-autocomplete v-model="entityForm.keepMan" :fetch-suggestions="queryKeepManList" placeholder="请输入委托单位"
                                     style="width: 100%" @focus="appKeepManListFocus()"></el-autocomplete>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="50" type="flex">
                <el-col>
                  <el-form-item label="送货人" prop="sendMan">
                    <el-autocomplete v-model="entityForm.sendMan" :fetch-suggestions="querySendManList" placeholder="请输入送货人"
                                     style="width: 100%" @focus="appSendManListFocus()"></el-autocomplete>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="负责人" prop="chargeMan">
                    <el-autocomplete v-model="entityForm.chargeMan" :fetch-suggestions="queryChargeManList" placeholder="请输入负责人"
                                     style="width: 100%" @focus="appChargeManListFocus()"></el-autocomplete>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="50" type="flex">
                <el-col>
                  <el-form-item label="送货单位" prop="supplierName">
                    <el-autocomplete v-model="entityForm.supplierName" :fetch-suggestions="querySupplierNameList"
                                     placeholder="请输入送货单位" style="width: 100%"
                                     @focus="appSupplierNameListFocus()"></el-autocomplete>
                  </el-form-item>
                </el-col>
                <el-col> </el-col>
              </el-row>

              <el-row :gutter="50" type="flex">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input v-model="entityForm.remarks" :rows="3" clearable type="textarea" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col style="margin-top: 20px;">
            <div class="form-titl3 form-warp">
              <span>新增物资</span>
              <el-button v-if="dialogStatus!='review'" type="export-all" @click="handleAdd">添加物资
              </el-button>
            </div>
            <div class="form-layout">
              <el-row :gutter="50" type="flex">
                <el-col>
                  <el-table :data="list" :header-cell-style="headClass" stripe style="width: 100%">
                    <el-table-column align="center" label="物资名称" prop="itemName" width="180">
                      <template slot-scope="scope">
                        <!-- <el-input v-model="scope.row.itemName" /> -->
                        <select-down :list="nameItemEntity" :value.sync="scope.row.itemName"
                                     @eventChange="handleNameEntity(scope.row)" />
                      </template>
                    </el-table-column>

                    <el-table-column align="center" label="批次号" prop="batchNo" width="180">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.batchNo" disabled />
                      </template>
                    </el-table-column>

                    <el-table-column align="center" label="物资编号" prop="itemCode" width="180">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.itemCode" disabled />
                      </template>
                    </el-table-column>

                    <el-table-column align="center" label="物资类型" width="180">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.categoryName" disabled />
                        <!-- <select-down :value.sync="scope.row.categoryName" :code.sync="scope.row.categoryId"
                                     :id.sync="scope.row.categoryId" :list="nameItemEntity" /> -->

                        <!-- <dict-select v-model="scope.row.categoryId" type="b_b_blending_production_measuring_scale" /> -->

                        <!-- <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" /> -->
                        <!--
                        <select-down :value.sync="scope.row.categoryName  " :code.sync="scope.row.categoryId"
                                     :id.sync="scope.row.categoryId" :list="nameItemEntity" /> -->
                      </template>
                    </el-table-column>

                    <el-table-column align="center" label="单位" prop="stockUnit" width="180">
                      <template slot-scope="scope">
                        <code-select :value.sync="scope.row.stockUnit" disabled></code-select>
                      </template>
                    </el-table-column>

                    <el-table-column align="center" label="数量" prop="entryAmount" width="170">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.entryAmount" oninput="value=value.replace(/[^0-9.]/g,'')"
                                  @input="ceshi(scope.row.entryAmount)">
                        </el-input>
                      </template>
                    </el-table-column>

                    <el-table-column align="center" label="价格" prop="price" width="170">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.price" oninput="value=value.replace(/[^0-9.]/g,'')"></el-input>
                      </template>
                    </el-table-column>

                    <el-table-column align="center" label="用途" prop="useDesc" width="180">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.useDesc" />
                      </template>
                    </el-table-column>

                    <el-table-column align="center" label="操作" width="100">
                      <template slot-scope="scope">
                        <el-tag class="opt-btn" color="#2f79e8" @click="handleRemove(scope.row)">删除</el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>

              <el-row :gutter="50" type="flex">
                <el-col>
                  <!-- <el-form-item label="合计金额" prop="commitDate">
                    <date-select v-model="entityForm.commitDate" :disabled="dialogStatus==='update'" clearable />
                  </el-form-item> -->
                </el-col>
                <el-col></el-col>
              </el-row>

            </div>
          </el-col>
          <el-col />
        </el-row>
      </el-form>
      <!-- @click="handleClose" -->

      <template v-if="isshoebottom">

        <div v-if="isshow==false" slot="footer" class="dialog-footer">
          <el-button class="dialog-footer-all" style="color: #ACACAC; border: 1px solid #ACACAC;" @click="handleFullScreen">
            {{ screen.full ? '取消全屏' : '全屏' }}
          </el-button>

          <el-button v-if="perms[`${curd}:update`]||false" :loading="entityFormLoading" class="dialog-footer-btns" type="primary"
                     @click="saveEntityForm('entityForm')">保存
          </el-button>
          <el-button class="dialog-footer-btns" @click="SaveDraft('entityForm')">保存草稿</el-button>

        </div>

        <div v-else slot="footer" class="dialog-footer">
          <!-- v-if="perms[`${curd}:review`]||false" -->
          <el-button :loading="entityFormLoading" class="dialog-footer-btns"
                     @click="passfn(entityForm.id,entityForm.reviewMessage)">
            审核通过
          </el-button>
          <el-button class="dialog-footer-btns" type="primary" @click="rejectfn(entityForm.id,entityForm.reviewMessage)">拒绝
          </el-button>
        </div>

      </template>

    </el-dialog>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/material/inOrder'
import { validatePhone } from '@/utils/validate'
import { inOrderOption } from '@/const'
import { createMemoryField } from '@/utils/index'
import MaterialinformationModel from '@/model/material/Materialinformation'

const form = {
  _id: '1',
  itemId: 0,
  code: '',
  stockUnit: '', //单位
  entryAmount: '', //数量
  itemCode: '', //物资编号
  itemName: '', //物资名称
  useDesc: '', //用途
  categoryId: '', //分类id
  categoryName: '' //分类名称
}
export default {
  name: 'inOrder',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'inOrder',
      screen: { full: false },
      model: Model,
      entityForm: { ...Model.model },
      filterOption: { ...inOrderOption },
      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        phone: [{ validator: validateIsPhone, trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      rules: {
        commitDate: { required: true, message: '请输入选择日期', trigger: 'change' },
        keepMan: { required: true, message: '请输入保管人', trigger: 'blur' },
        sendMan: { required: true, message: '请输入送货人', trigger: 'blur' },
        chargeMan: { required: true, message: '请输入负责人', trigger: 'blur' },
        supplierName: { required: true, message: '请输入送货单位', trigger: 'blur' }
      },
      nameItemEntity: [],
      form: { ...form },
      list: [{ ...form }],
      nameEntity: { options: [], active: '' },
      isshow: false,
      reviewLogList: [],
      isshoebottom: true,
      // searchDate: [],
      countList: [],
      KeepManList: [],
      SendManList: [],
      ChargeManList: [],
      SupplierNameList: []
    }
  },
  watch: {
    activetype: function (newV, oldV) {
      this.getlist(newV)
    }
  },
  computed: {},
  created() {
    this.activetype = 'DRAFT,REJECT'
    // this.permsActionLsit(this.curd)
    this.permsActionbtn(this.curd)
    this.getMaterialType()

    this.list = []
    this.memoryEntity.fields = createMemoryField({
      fields: ['contractId', 'contractCode', 'supplierId', 'supplierName'],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })

    this.getnumber()
    if (this.$route.params.type) {
      this.activetype = this.$route.params.type
      if (this.$route.params.id) {
        this.entityForm.id = this.$route.params.id
        this.handleUpdate(this.$route.params.id, 'review')
      }
    } else {
      this.activetype = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
      this.actionList.forEach((val, index) => {
        if (val.type == this.activetype) {
          val.active = true
        } else {
          val.active = false
        }
      })
    }

    /**
     * 设置默认近一周的时间
     */
    // const end = new Date()
    // const start = new Date()
    // start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
    // this.searchDate = [{ start: start, end: end }]
  },
  methods: {
    createFilter(queryString) {
      return (item) => {
        return item.value.toString().toLowerCase().includes(queryString.toString().toLowerCase()) === true
      }
    },
    queryKeepManList(queryString, cb) {
      var KeepManList = this.KeepManList
      var results = queryString ? KeepManList.filter(this.createFilter(queryString)) : KeepManList
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        cb(results)
      }, 1000 * Math.random())
    },
    async appKeepManListFocus() {
      let { data } = await this.model.findDistinctKeepManList()
      // console.log('获取保管人记录')
      var r = data.map((item) => {
        return { value: item }
      })
      console.log(r)
      if (r != null) {
        this.KeepManList = r
      }
    },

    querySendManList(queryString, cb) {
      var SendManList = this.SendManList
      var results = queryString ? SendManList.filter(this.createFilter(queryString)) : SendManList
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        cb(results)
      }, 1000 * Math.random())
    },

    async appSendManListFocus() {
      let { data } = await this.model.findDistinctSendManList()
      // console.log('获取送货人记录')
      var r = data.map((item) => {
        return { value: item }
      })
      console.log(r)
      if (r != null) {
        this.SendManList = r
      }
    },

    queryChargeManList(queryString, cb) {
      var ChargeManList = this.ChargeManList
      var results = queryString ? ChargeManList.filter(this.createFilter(queryString)) : ChargeManList
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        cb(results)
      }, 1000 * Math.random())
    },

    async appChargeManListFocus() {
      let { data } = await this.model.findDistinctChargeManList()
      // console.log('获取负责人记录')
      var r = data.map((item) => {
        return { value: item }
      })
      console.log(r)
      if (r != null) {
        this.ChargeManList = r
      }
    },

    querySupplierNameList(queryString, cb) {
      var SupplierNameList = this.SupplierNameList
      var results = queryString ? SupplierNameList.filter(this.createFilter(queryString)) : SupplierNameList
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        cb(results)
      }, 1000 * Math.random())
    },

    async appSupplierNameListFocus() {
      let { data } = await this.model.findDistinctSupplierNameList()
      // console.log('获取送货单位记录')
      var r = data.map((item) => {
        return { value: item }
      })
      console.log(r)
      if (r != null) {
        this.SupplierNameList = r
      }
    },

    async getnumber() {
      const res = await this.model.getcountByState()
      // console.log(res)
      this.countList = res.data
    },
    // 获取物资类型
    async getMaterialType() {
      // try {
      //   let { data } = await MaterialinformationModel.gettype({ ...this.listQuery, size: 999 })
      //   this.nameEntity.options = data.records.map((item) => {
      //     return { name: item.name, id: item.id }
      //   })
      // } catch (error) {}
      let { data } = await MaterialinformationModel.gettype({ ...this.listQuery, size: 999 })
      // console.log(data)
      this.nameItemEntity = data.records
      this.handleAdd()
    },
    getSite(e) {
      // console.log(e)
    },
    handleNameEntity(val) {
      // console.log(val)
      let data = this.nameItemEntity

      let data2 = this.list
      this.$nextTick(() => {
        for (var i = 0; i < data.length; i++) {
          if (val.itemName == data[i].name) {
            // console.log(data[i])
            for (var j = 0; j < data2.length; j++) {
              if (val._id == data2[j]._id) {
                let obj = {}
                obj._id = val._id
                obj.itemName = val.itemName
                if (val.code) {
                  obj.itemCode = val.code
                } else {
                  obj.itemCode = data[i].code
                }
                if (data[i].entryAmount) {
                  obj.entryAmount = data[i].entryAmount
                } else {
                  obj.entryAmount = data[i].entryAmount
                }
                obj.categoryName = data[i].categoryName
                obj.categoryId = data[i].categoryId
                obj.itemId = data[i].id
                obj.stockUnit = data[i].stockUnit
                obj.useDesc = data[i].useDesc
                this.$set(data2, [j], obj)
                // this.$set(data2, [j], data[i])
                //  dd = Object.assign({}, data2[j], data[i])
              }
            }
          }
        }
        this.list = data2
        // console.log(this.list)
      })
      this.entityForm.inOrderItemList = this.list
    },

    handleFullScreen() {
      this.screen.full = !this.screen.full
      const el = this.$refs.dialogStatus.$el
      const el_body = el.querySelector('.el-dialog__body')
      el_body.style.height = this.screen.full ? '85vh' : '70vh'
    },
    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      this.isshow = false
      this.isshoebottom = true
      this.reviewLogList = []
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.list = []
      this.handleAdd()
    },
    handleAdd() {
      const form = { ...this.form }
      form._id = Math.random().toFixed(6).slice(-6)
      this.list.push(form)
    },
    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },

    handleRemove({ _id }) {
      const index = this.list.findIndex((item) => item._id === _id)
      if (index === -1) return false
      this.list.splice(index, 1)
    },
    ceshi(number) {
      let Things = this.list
      for (var i = 0; i < Things.length; i++) {
        if (Things[i].entryAmount == undefined) {
          Things[i].entryAmount = number
        }
      }
    },
    async saveEntityForm(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.list.length) {
          // if (this.list.some((val) => val.entryAmount == undefined)) {
          //   this.$message({
          //     type: 'error',
          //     message: '数量不能为空',
          //   })
          //   return
          // }
          if (
            this.list.some(
              (val) =>
                val.entryAmount == undefined ||
                val.categoryId == '' ||
                val.categoryName == '' ||
                val.code == '' ||
                val.itemCode == '' ||
                val.itemId == '' ||
                val.itemName == '' ||
                val.stockUnit == '' ||
                val.useDesc == ''
            )
          ) {
            this.$message({
              type: 'error',
              message: '请将物资信息填写完整！'
            })
            return
          }

          const list = []
          this.list.forEach((item) => {
            list.push({ ...item })
          })
          this.entityFormLoading = true
          this.activetype = 'NEW'
          const res = await Model.getApproved({ ...this.entityForm, inOrderItemList: list })
          this.entityFormLoading = false
          this.getnumber()
          if (res) {
            this.resetVariant()
            this.getList()
            this.getnumber()
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else {
          this.$message({ message: '请将物资信息填写完整！', type: 'warning' })
        }
      }
    },

    //保存草稿
    async SaveDraft(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.list.length) {
          if (
            this.list.some(
              (val) =>
                val.entryAmount == undefined ||
                val.categoryId == '' ||
                val.categoryName == '' ||
                val.code == '' ||
                val.itemCode == '' ||
                val.itemId == '' ||
                val.itemName == '' ||
                val.stockUnit == '' ||
                val.useDesc == ''
            )
          ) {
            this.$message({
              type: 'error',
              message: '请将物资信息填写完整！'
            })
            return
          }
          const list = []
          this.list.forEach((item) => {
            list.push({ ...item })
          })
          const res = await Model.SaveDraftfn({ ...this.entityForm, inOrderItemList: list })
          if (res) {
            this.$message({ type: 'success', message: '保存草稿成功!' })
            this.entityForm = { ...this.model.model }
            this.getList()
            this.getnumber()
            this.dialogFormVisible = false
          }
        } else {
          this.$message({ message: '请将物资信息填写完整！', type: 'warning' })
        }
      }
    },

    getList() {
      this.$refs[this.curd].$emit('refresh')
    },
    async handleUpdate(id, type) {
      if (type == 'review') {
        this.isshow = true
        this.dialogStatus = 'review'
      } else if (type == 'update') {
        this.isshow = false
        this.dialogStatus = 'update'
      }

      if (type == 'watch') {
        this.dialogStatus = 'watch'
        this.isshoebottom = false
      } else {
        this.isshoebottom = true
      }
      const { data } = await this.model.getUploadList(id)
      this.entityForm = { ...data }
      this.list = data.inOrderItemList
      this.reviewLogList = data.reviewLogList
      delete this.entityForm.inOrderItemList
      this.dialogFormVisible = true
      let newarry = []
      if (data.reviewLogList.length > 0) {
        data.reviewLogList.forEach((item, index) => {
          newarry.push(item.reviewMessage)
        })
        this.entityForm.reviewMessage = newarry.toString()
      }
    },
    //点击状态获取数据
    async getlist(newV) {
      this.activetype = newV
      this.actionList.forEach((val, index) => {
        if (val.type == newV) {
          val.active = true
        } else {
          val.active = false
        }
      })
      this.filtersSeach.filter_INS_state = newV
      this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    },

    async passfn(id, reviewMessage) {
      const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage })
      this.getnumber()
      this.$message({ type: 'success', message: '审核通过成功!' })
      this.entityForm = { ...this.model.model }
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    },

    async rejectfn(id, reviewMessage) {
      const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage })
      this.getnumber()
      this.entityForm = { ...this.model.model }
      this.$message({ type: 'success', message: '拒绝成功!' })
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>


<style scoped>
/* .opt-btn {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
}
.opt-btnV {
  margin: 5px 10px 0 0;
  height: 26px;
  line-height: 26px;
} */
.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}

.el-dialog.is-fullscreen {
  width: 100% !important;
  margin-top: 0;
  margin-bottom: 0;
  height: 100% !important;
  overflow: auto;
}

/* .inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
} */
/* .dialog-footer >>> .dialog-footer-btns {
  border: 1px solid #2f79e8;
  color: #2f79e8;
} */
</style>


<style lang="scss" scoped>
::v-deep .el-dialog__footer {
  padding: 0px 0px 0px;
}

::v-deep .el-input.is-disabled .el-input__inner {
  color: #606266;
}

::v-deep .el-icon-date {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-icon-time {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}

.form-titleV {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 13px;
    background: #2f79e8;
    height: 16px;
  }
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
}

::v-deep .el-icon-date {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-icon-time {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}

::v-deep .el-input__prefix {
  right: 5px;
  left: auto;
}

::v-deep .el-input--prefix .el-input__inner {
  padding-left: 10px;
}

::v-deep .el-table__fixed-right::before {
  width: 0;
}

.dialog {
  width: 980px;
  height: 740px;
}

.dialog-footer {
  // margin-right: 44px;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px #f1f1f2;
  padding: 10px;

  &-all {
    font-size: 14px;
    margin-left: 34px;
    // margin-right: auto;
    flex: 0 0 58px;
    color: #ff9639;
    background: #fff;
    border: 1px solid #ff9639;

    &:hover {
      background: transparent;
    }

    &:nth-of-type(1) {
      // margin-left: 15px;
      margin-right: auto;
    }
  }

  &-btns {
    width: 85px;
    height: 35px;
    border: 1px solid #2f79e8;
    background: #fff;
    color: #2f79e8;
    margin-left: 20px;
    font-size: 14px;

    &:last-of-type {
      border: 1px solid #d5d5d5;
      color: #747474;
    }
  }
}

.form-layout {
  padding: 0 20px;
}

.form-title {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 17px;
    background: #2f79e8;
    height: 16px;
  }
}

.form-titl3 {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;
  margin-bottom: 5px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 10px;
    background: #2f79e8;
    height: 16px;
  }
}

::v-deep .el-input--mini .el-input__inner {
  height: 29.5px;
  line-height: 29.5px;
}

.opt-btn {
  cursor: pointer;
  margin: 0 3px;
  color: #fff;
  border: none;
}

::v-deep .el-form--label-top .el-form-item__label {
  padding: 0;
  font-weight: 400;
  font-size: 13px;
  color: #646464;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 13px;
}

.name {
  text-decoration: underline;
  cursor: pointer;
  transition: 0.5s all;

  &:hover,
  :focus {
    color: #3234d3;
  }
}

.excessive {
  width: 100%;
  color: red;
}

::v-deep .percent-column {
  .cell {
    top: 10px;
    height: 42px;
  }
}

.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);

  &-contract {
    width: 140px;
  }

  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;

    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }

    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;

      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

.column {
  display: inline-block;
  width: 100%;
  height: 20px;
}

.app-container {
  height: 100vh;
}

.table {
  width: 100%;
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
}

.el-button--export-all:focus,
.el-button--export-all:hover {
  background: #ff9639 !important;
  border-color: #fff !important;
  color: #fff !important;
}

.el-button--export-all {
  color: #ff9639;
  background-color: #fff;
  border-color: #ff9639;
  padding: 5px 8px;
  margin-top: -6px;
}

::v-deep // 合计行样式
.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  height: 50px;
  line-height: 50px;
  color: #ff9639;
  font-size: 13px;
  background: rgb(245, 246, 251);
}

::v-deep .panel-list {
  margin-left: -25px;
}

::v-deep .info {
  display: flex;
  justify-content: space-evenly;
  flex-flow: wrap;

  span {
    padding: 3px;
  }

  // justify-content: start;
  .title {
    span {
      margin-left: 5px;
      color: #f8a20f;
      font-weight: bold;
    }
  }
}

::v-deep .orange-row {
  color: #f8a20f;
  font-size: 14px;
  font-weight: bold;
  height: 60px;
}

::v-deep .row-layout {
  font-size: 14px;
  height: 60px;
}

.panel-list-item {
  height: 40px;
  padding: 5px;
}
</style>


<style lang="scss" scoped>
// @import '@/styles/router-page.scss';
// ::v-deep .el-icon-date {
//   transform: scale(1.3);
//   margin-top: 1px;
// }

// ::v-deep .el-icon-time {
//   transform: scale(1.3);
//   margin-top: 1px;
// }

// ::v-deep .el-dialog__body {
//   padding: 20px 35px;
//   overflow: auto;
//   height: 70vh;
// }

// .form-titleV {
//   font-size: 16px;
//   position: relative;
//   padding: 15px 18px;

//   &::before {
//     content: '';
//     left: 7px;
//     position: absolute;
//     width: 3px;
//     bottom: 22px;
//     background: #2f79e8;
//     height: 16px;
//   }
// }
// .form-warp {
//   padding: 10px 20px 6px 18px;
//   display: flex;
//   justify-content: space-between;
// }
</style>



