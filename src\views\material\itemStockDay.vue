<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate" :showAdd="false"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" @Refresh="Refreshfn" />
    <!-- :showSummary='true' -->
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" otherHeight="175" :list-query="listQuery">
      <el-table-column label="操作" slot="opt" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <template v-if="!scope.row._all">
            <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)" v-if="perms[`${curd}:update`]||false">编辑
            </el-tag>
          </template>
          <template v-else>
            <el-tag class="opt-btn" color="#33CAD9" @click="handleSave(scope.row)" v-if="perms[`${curd}:update`]||false">保存
            </el-tag>
          </template>
          <el-tag class="opt-btn" color="#2f79e8" v-if="perms[`${curd}:Refresh`]||false" @click="handleRefresh(scope.row)">刷新
          </el-tag>
        </template>
      </el-table-column>
    </s-curd>

    <!-- <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">库存信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="entityForm.date"></date-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="品名" prop="name">
                    <el-input v-model="entityForm.name" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="灰分" prop="ad">
                    <el-input v-model="entityForm.ad" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="硫分" prop="std">
                    <el-input v-model="entityForm.std" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="上日库存" prop="lastStock">
                    <el-input v-model.number="entityForm.lastStock">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="本日购产" prop="todayIn">
                    <el-input v-model.number="entityForm.todayIn">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="本日配销" prop="todayOut">
                    <el-input v-model.number="entityForm.todayOut">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="本日库存" prop="stock">
                    <el-input v-model.number="entityForm.stock">
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">其他信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable :rows="2" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog> -->

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import { itemStockDayFilterOption } from '@/const'
// import Model from '@/model/stock/chinaCoalStock'
import Model from '@/model/material/StockDay'
export default {
  name: 'itemStockDay',
  data() {
    return {
      curd: 'itemStockDay',
      model: Model,
      filterOption: { ...itemStockDayFilterOption },
      entityForm: { ...Model.model },
      listQuery: { orderBy: 'date', orderDir: 'DESC' },
      rules: {
        name: { required: true, message: '请输入品名', trigger: 'blur' },
        stock: { required: true, message: '请输入库存', trigger: 'blur' }
      },
      actions: [],
      // actions: [
      //   {
      //     config: {
      //       type: 'export-all',
      //     },
      //     show: true,
      //     label: '导出当前页',
      //     click: this.export,
      //     type: 'current',
      //   },
      //   {
      //     config: {
      //       type: 'export-all',
      //     },
      //     show: true,
      //     label: '导出所有',
      //     click: this.export,
      //     type: 'all',
      //   },
      // ],
      batch: {
        visible: false,
        list: []
      }
    }
  },
  created() {
    this.permsAction(this.curd)
  },
  mixins: [Mixins],
  methods: {
    async Refreshfn() {
      let date = new Date(new Date().getTime()).Format('yyyy-MM-dd')
      try {
        const { data } = await this.model.refresh({ date })
        this.$message({ showClose: true, message: '刷新成功', type: 'success' })
        this.$refs[this.curd].searchChange({ ...this.listQuery })

        // if (data == null) {
        //   this.$message({ showClose: true, message: '刷新成功', type: 'success' })
        //   this.$refs[this.curd].searchChange({ ...this.listQuery })
        // } else {
        //   this.$message({ showClose: true, message: '出错了', type: 'error' })
        // }
        this.$refs[this.curd].searchChange({ ...this.listQuery })
      } catch (error) {}
    },
    handleName(item) {
      this.entityForm = { ...item }
      this.dialogStatus = 'watch'
      this.dialogFormVisible = true
    },
    changeAll(row) {
      row._all = !row._all
    },
    handleUpdate(row) {
      this.changeAll(row)
      this.changeCurrent(row, true) // 编辑全部
    },
    handleSave(row) {
      this.changeAll(row)
      this.operationRow(row, true) // 取消时关闭所有的active
    },
    handleCancel(row) {
      row._all = false
      this.operationRow(row, false) // 取消时关闭所有的active
    },
    changeCurrent(row, target) {
      if (target === true) {
        for (const [k, v] of Object.entries(row.active)) {
          row.active[k] = !v
        }
      } else if (target === false) {
        for (const key of Object.keys(row.active)) {
          row.active[key] = false
        }
      } else {
        row.active[target] = !row.active[target]
      }
    },
    handleBlur(row, target) {
      this.changeCurrent(row, target) // 失去事件触发改变时关闭target的active
      const status = this.isOpenSave(row) // 判断当前事件是否全部关闭
      if (status) return // 为真说明还有blur未关闭
      for (const key of Object.keys(row.bak)) {
        if (row.bak[key] + '' !== row[key] + '') return // 判断两个值是否不相等 不相等则直接返回
      }
      setTimeout(() => {
        row._all = false
      }, 300) // 如果上述条件都成立则开回到最初的模式
    },
    isOpenSave(row) {
      let status = false
      for (const v of Object.values(row.active)) {
        if (v) {
          status = true
          break
        }
      }
      return status
    },
    /**
     * 双击时改变为input狂
     * @target string|true 用于判断是操作一个还是多个
     */
    handleActive(row, target) {
      row._all = true
      this.changeCurrent(row, target)
    },
    /**
     * 保存或取消
     * @action {true:false} true为保存逻辑，false为取消逻辑
     */
    async operationRow(row, action) {
      if (action) {
        const isChangeAd = row.ad !== row.bak.ad
        const isChangeStd = row.std !== row.bak.std
        const remarksTemplate = `已更新,上次灰分为:${row.ad},硫分为:${row.std}`
        if (isChangeAd && isChangeStd) {
          if (!row.remarks) {
            row.remarks = remarksTemplate
          } else if (/^已更新,上次灰分为:\d+,硫分为:\d+$/.test(row.remarks)) {
            row.remarks = remarksTemplate
          }
        }
        try {
          await this.model.save({ ...row })
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        } catch (e) {
          this.$message({ message: '保存失败', type: 'warning' })
        }
        this.getList()
      } else {
        for (const key of Object.keys(row.bak)) {
          if (action) {
            // ---------------------- 可写接口
            row.bak[key] = row[key] // 保存就时把当前key赋给bak
          } else {
            row[key] = row.bak[key] // 取消就是bak值赋给当前key
          }
          row.active[key] = false // 更具Key 将active都初始化
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.el-button--opt_stock:focus,
.el-button--opt_stock:hover {
  opacity: 1;
}
.el-button--opt_stock {
  transition: 0.5s all;
  color: #b42020;
  opacity: 0.85;
  background-color: transparent;
  border-color: transparent;
}
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
// .opt-btnV {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
</style>
