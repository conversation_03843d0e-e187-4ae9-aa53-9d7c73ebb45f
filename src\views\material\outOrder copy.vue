<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :statetype="activetype" :buttomlist="buttomlist"
            :countList="countList" :actionList="actionList" title="供应商" otherHeight="180">

      <el-table-column slot="outOrderItemList" label="物资列表" width="180" type="expand">
        <template slot-scope="scope">
          <!-- #ECF1FE :header-cell-style="{background:'#33cad9',color:'#fff'}"-->
          <el-table :data="scope.row.outOrderItemList" style="width: 100%" :header-cell-style="{background:'#ECF1FE'}">
            <el-table-column prop="itemName" label="物资名称" width="180">
            </el-table-column>
            <el-table-column prop="batchNo" label="批次号" width="180">
            </el-table-column>
            <el-table-column prop="categoryName" label="物资类型" width="180">
            </el-table-column>
            <el-table-column prop="createDate" label="日期">
            </el-table-column>
            <el-table-column prop="stockUnit" label="单位">
            </el-table-column>
            <el-table-column prop="deliveryAmount" label="数量">
            </el-table-column>
            <el-table-column prop="price" label="价格">
            </el-table-column>
            <el-table-column prop="useDesc" label="用途">
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>

      <el-table-column label="状态" slot="state">
        <template slot-scope="scope">
          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :'info'))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '待审核' : (scope.row.state == 'PASS' ? '已审核' : (scope.row.state == 'REJECT' ? '拒绝':''))) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" max-width="150" width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <el-tag class="opt-btn" v-if="scope.row.state == 'NEW'" color="#FF726B" @click="handleUpdate(scope.row.id,'review')">
              去审核
            </el-tag>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT,REJECT'" color="#FF726B"
                      @click="handleUpdate(scope.row.id,'update')">
                编辑
              </el-tag>
            </div>
            <div>
              <el-tag class="opt-btn" v-if="scope.row.state == 'PASS'" color="#FF726B"
                      @click="handleUpdate(scope.row.id,'watch')">
                查看详情
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" color="#2f79e8"
                      style="cursor: pointer;" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog :fullscreen="screen.full" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
            <el-col>
              <el-form-item label="步骤" prop="reviewLogList">
                <el-steps :active="reviewLogList.length">
                  <el-step v-for="(item,index) in reviewLogList" :key="index" :title="item.reviewResult"
                           :description="item.createDate+' '+item.reviewUserName">
                  </el-step>
                </el-steps>
              </el-form-item>
            </el-col>
          </el-row>
          <el-col>
            <div class="form-title">基本类型</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="出库日期" prop="commitDate">
                    <date-select v-model="entityForm.commitDate" clearable :disabled="dialogStatus==='update'" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="提交人" prop="commitBy">
                    <el-input v-model="entityForm.commitBy" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row>

            </div>
          </el-col>
          <el-col style="margin-top: 20px;">
            <div class="form-titl3  form-warp">
              <span>新增物资</span>
              <el-button type="export-all" @click="handleAdd" v-if="dialogStatus!='review'">添加物资</el-button>
            </div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-table :data="list" style="width: 100%" stripe :header-cell-style="headClass">
                    <el-table-column label="物资名称" width="150" align="center" prop="itemName">
                      <template slot-scope="scope">
                        <select-down :value.sync="scope.row.itemName" :list="nameItemEntity"
                                     @eventChange="handleNameEntity(scope.row)" />
                      </template>
                    </el-table-column>

                    <el-table-column label="批次号" width="150" align="center" prop="batchNo">
                      <template slot-scope="scope">
                        <el-select class="select" v-model="scope.row.batchNo" @change="handleBatchNo(scope.row)" filterable>
                          <el-option v-for="item in BatchNoEntity" :key="item.id" :label="item.batchNo" :value="item.batchNo">
                          </el-option>
                        </el-select>
                      </template>
                    </el-table-column>

                    <el-table-column label="物资编号" width="150" align="center" prop="itemCode">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.itemCode" disabled />
                      </template>
                    </el-table-column>
                    <el-table-column label="物资类型" width="150" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.categoryName" disabled />
                      </template>
                    </el-table-column>
                    <el-table-column label="单位" width="180" align="center" prop="stockUnit">
                      <template slot-scope="scope">
                        <code-select :value.sync="scope.row.stockUnit" disabled></code-select>
                      </template>
                    </el-table-column>

                    <el-table-column label="剩余数量" width="100" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.leftAmount" disabled />
                      </template>
                    </el-table-column>

                    <el-table-column label="出库数量" width="170" align="center" prop="deliveryAmount">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.deliveryAmount" @blur="ceshi(scope.row.deliveryAmount,scope.row.leftAmount)"
                                  oninput="value=value.replace(/[^0-9.]/g,'')">
                        </el-input>
                      </template>
                    </el-table-column>

                    <el-table-column label="价格" width="170" align="center" prop="price">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.price" oninput="value=value.replace(/[^0-9.]/g,'')"></el-input>
                      </template>
                    </el-table-column>
                    <el-table-column label="用途" width="180" align="center" prop="useDesc">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.useDesc" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="center">
                      <template slot-scope="scope">
                        <el-tag class="opt-btn" color="#2f79e8" @click="handleRemove(scope.row)">删除</el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col />
        </el-row>
      </el-form>

      <template v-if="isshoebottom">
        <div slot="footer" class="dialog-footer" v-if="isshow==false">
          <el-button class="dialog-footer-all" @click="handleFullScreen" style="color: #ACACAC; border: 1px solid #ACACAC;">
            {{screen.full?'取消全屏':'全屏'}}</el-button>

          <el-button style="border: 1px solid #2f79e8;color: #2f79e8;" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="saveEntityForm('entityForm')" v-if="perms[`${curd}:update`]||false">保存
          </el-button>
          <el-button class="dialog-footer-btns" @click="SaveDraft('entityForm')">保存草稿</el-button>
        </div>
        <div slot="footer" class="dialog-footer" v-else>
          <el-button @click="rejectfn(entityForm.id,entityForm.reviewMessage)" class="dialog-footer-btns" style="width:100px">拒绝
          </el-button>
          <el-button style="border: 1px solid #2f79e8;color: #2f79e8;" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="passfn(entityForm.id,entityForm.reviewMessage)">
            审核通过
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/material/outOrder'
import { validatePhone } from '@/utils/validate'
import { inOrderOption } from '@/const'
import { createMemoryField } from '@/utils/index'
import MaterialinformationModel from '@/model/material/Materialinformation'
const form = {
  _id: '1',
  itemId: 0,
  batchNo: '', //批次号
  code: '',
  stockUnit: '', //单位
  deliveryAmount: '', //数量
  leftAmount: '', //剩余数量
  itemCode: '', //物资编号
  itemName: '', //物资名称
  useDesc: '', //用途
  categoryId: '', //分类id
  categoryName: '' //分类名称
}
export default {
  name: 'inOrder',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'outOrder',
      screen: { full: false },
      model: Model,
      entityForm: { ...Model.model },
      filterOption: { ...inOrderOption },
      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        phone: [{ validator: validateIsPhone, trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      rules: {
        commitDate: { required: true, message: '请输入选择日期', trigger: 'change' },
        commitBy: { required: true, message: '请输入存放人', trigger: 'blur' }
      },
      nameItemEntity: [],
      BatchNoEntity: [],
      form: { ...form },
      list: [{ ...form }],
      nameEntity: { options: [], active: '' },
      isshow: false,
      reviewLogList: [],
      isshoebottom: true,
      countList: []
    }
  },
  watch: {
    activetype: function (newV, oldV) {
      this.getlist(newV)
    }
  },
  computed: {},
  created() {
    this.activetype = 'DRAFT,REJECT'
    this.permsActionbtn(this.curd)
    // this.permsActionLsit(this.curd)
    this.getMaterialType()

    this.list = []
    this.memoryEntity.fields = createMemoryField({
      fields: ['contractId', 'contractCode', 'supplierId', 'supplierName'],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })

    if (this.$route.params.type) {
      this.activetype = this.$route.params.type
      if (this.$route.params.id) {
        this.handleUpdate(this.$route.params.id, 'review')
      }
    } else {
      this.activetype = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
      this.actionList.forEach((val, index) => {
        if (val.type == this.activetype) {
          val.active = true
        } else {
          val.active = false
        }
      })
    }

    this.getnumber()
  },
  methods: {
    async getnumber() {
      const res = await this.model.getcountByState()
      this.countList = res.data
    },
    // 获取物资类型
    async getMaterialType() {
      let { data } = await MaterialinformationModel.getpageAmountGt0({ ...this.listQuery, size: 999 })
      this.nameItemEntity = data.records
      this.handleAdd()
    },
    getSite(e) {
      // console.log(e)
    },
    handleNameEntity(val) {
      let data = this.nameItemEntity
      let data2 = this.list
      this.$nextTick(() => {
        for (var i = 0; i < data.length; i++) {
          if (val.itemName == data[i].name) {
            for (var j = 0; j < data2.length; j++) {
              if (val._id == data2[j]._id) {
                let obj = {}
                obj._id = val._id
                obj.itemName = val.itemName
                if (val.code) {
                  obj.itemCode = val.code
                } else {
                  obj.itemCode = data[i].code
                }
                if (data[i].deliveryAmount) {
                  obj.deliveryAmount = data[i].deliveryAmount
                } else {
                  obj.deliveryAmount = data[i].deliveryAmount
                }
                obj.categoryName = data[i].categoryName
                obj.categoryId = data[i].categoryId
                obj.itemId = data[i].id
                obj.stockUnit = data[i].stockUnit
                obj.useDesc = data[i].useDesc
                this.getBatchNoFn(data[i].id)
                this.$set(data2, [j], obj)
              }
            }
          }
        }
        this.list = data2
      })
      this.entityForm.outOrderItemList = this.list
    },
    async getBatchNoFn(itemId) {
      const { data } = await this.model.getBatchNoList(itemId)
      this.BatchNoEntity = data
    },
    handleBatchNo(val) {
      let data = this.BatchNoEntity
      let data2 = this.list
      this.$nextTick(() => {
        for (var i = 0; i < data.length; i++) {
          if (val.batchNo == data[i].batchNo) {
            for (var j = 0; j < data2.length; j++) {
              if (val._id == data2[j]._id) {
                let obj = {}
                obj._id = val._id
                obj.batchNo = val.batchNo
                obj.itemName = data2[j].itemName
                obj.itemId = data2[j].itemId
                obj.itemCode = data2[j].itemCode
                if (data[i].leftAmount) {
                  obj.leftAmount = data[i].leftAmount
                }
                obj.deliveryAmount = data2[j].deliveryAmount
                obj.categoryName = data2[j].categoryName
                obj.categoryId = data2[j].categoryId
                obj.stockUnit = data2[j].stockUnit
                obj.useDesc = data2[j].useDesc
                this.$set(data2, [j], obj)
              }
            }
          }
        }
        this.list = data2
      })
      this.entityForm.outOrderItemList = this.list
    },

    handleFullScreen() {
      this.screen.full = !this.screen.full
      const el = this.$refs.dialogStatus.$el
      const el_body = el.querySelector('.el-dialog__body')
      el_body.style.height = this.screen.full ? '85vh' : '70vh'
    },
    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      this.isshow = false
      this.isshoebottom = true
      this.reviewLogList = []
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.list = []
      this.handleAdd()
    },
    handleAdd() {
      const form = { ...this.form }
      form._id = Math.random().toFixed(6).slice(-6)
      this.list.push(form)
    },
    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },

    handleRemove({ _id }) {
      const index = this.list.findIndex((item) => item._id === _id)
      if (index === -1) return false
      this.list.splice(index, 1)
    },
    ceshi(number, leftAmount) {
      let Things = this.list
      if (Number(number) > Number(leftAmount)) {
        this.$message({ type: 'warning', message: '出库数量不能大于剩余数量!' })
        for (var i = 0; i < Things.length; i++) {
          Things[i].deliveryAmount = ''
        }
      } else {
        for (var i = 0; i < Things.length; i++) {
          if (Things[i].deliveryAmount == undefined) {
            Things[i].deliveryAmount = number
          }
        }
      }
    },

    async saveEntityForm(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.list.length) {
          // if (this.list.some((val) => val.deliveryAmount == undefined)) {
          //   this.$message({
          //     type: 'error',
          //     message: '数量不能为空',
          //   })
          //   return
          // }
          if (
            this.list.some(
              (val) =>
                val.deliveryAmount == undefined ||
                val.categoryId == '' ||
                val.categoryName == '' ||
                val.code == '' ||
                val.itemCode == '' ||
                val.itemId == '' ||
                val.itemName == '' ||
                val.stockUnit == '' ||
                val.useDesc == ''
            )
          ) {
            this.$message({
              type: 'error',
              message: '请将物资信息填写完整！'
            })
            return
          }
          const list = []
          this.list.forEach((item) => {
            if (item.batchNo) {
              list.push({ ...item })
            }
          })
          this.entityFormLoading = true
          this.activetype = 'NEW'
          const res = await Model.getApproved({ ...this.entityForm, outOrderItemList: list })
          this.entityFormLoading = false
          this.getnumber()
          if (res) {
            this.resetVariant()
            this.getList()
            this.getnumber()
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else {
          this.$message({ message: '请将物资信息填写完整！', type: 'warning' })
        }
      }
    },

    //保存草稿
    async SaveDraft(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.list.length) {
          if (
            this.list.some(
              (val) =>
                val.deliveryAmount == undefined ||
                val.categoryId == '' ||
                val.categoryName == '' ||
                val.code == '' ||
                val.itemCode == '' ||
                val.itemId == '' ||
                val.itemName == '' ||
                val.stockUnit == '' ||
                val.useDesc == ''
            )
          ) {
            this.$message({
              type: 'error',
              message: '请将物资信息填写完整！'
            })
            return
          }
          const list = []
          this.list.forEach((item) => {
            list.push({ ...item })
          })
          const res = await Model.SaveDraftfn({ ...this.entityForm, inOrderItemList: list })
          if (res) {
            this.$message({ type: 'success', message: '保存草稿成功!' })
            this.entityForm = { ...this.model.model }
            this.getList()
            this.getnumber()
            this.dialogFormVisible = false
          }
        } else {
          this.$message({ message: '请将物资信息填写完整！', type: 'warning' })
        }
      }
    },

    async handleUpdate(id, type) {
      if (type == 'review') {
        this.isshow = true
        this.dialogStatus = 'review'
      } else if (type == 'update') {
        this.isshow = false
        this.dialogStatus = 'update'
      }

      if (type == 'watch') {
        this.isshoebottom = false
        this.dialogStatus = 'watch'
      } else {
        this.isshoebottom = true
      }
      const { data } = await this.model.getUploadList(id)
      this.entityForm = { ...data }
      this.list = data.outOrderItemList
      this.reviewLogList = data.reviewLogList
      delete this.entityForm.outOrderItemList
      this.dialogFormVisible = true
      let newarry = []
      if (data.reviewLogList.length > 0) {
        data.reviewLogList.forEach((item, index) => {
          newarry.push(item.reviewMessage)
        })
        this.entityForm.reviewMessage = newarry.toString()
      }
    },
    //点击状态获取数据
    async getlist(newV) {
      this.activetype = newV
      this.actionList.forEach((val, index) => {
        if (val.type == newV) {
          val.active = true
        } else {
          val.active = false
        }
      })
      this.filtersSeach.filter_INS_state = newV
      this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    },

    async passfn(id, reviewMessage) {
      const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage })
      this.getnumber()
      this.$message({ type: 'success', message: '审核通过成功!' })
      this.entityForm = { ...this.model.model }
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    },

    async rejectfn(id, reviewMessage) {
      const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage })
      this.getnumber()
      this.entityForm = { ...this.model.model }
      this.$message({ type: 'success', message: '拒绝成功!' })
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>


 <style lang="scss" scoped>
// @import '@/styles/router-page.scss';
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
// .opt-btnV {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
</style>

<style lang="scss" scoped>
::v-deep .el-dialog__footer {
  padding: 0px 0px 0px;
}
::v-deep .el-input.is-disabled .el-input__inner {
  color: #606266;
}
::v-deep .el-icon-date {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-icon-time {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}

.form-titleV {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;
  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 13px;
    background: #2f79e8;
    height: 16px;
  }
}
.form-titl3 {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;
  margin-bottom: 5px;
  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 10px;
    background: #2f79e8;
    height: 16px;
  }
}
.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
}

::v-deep .el-icon-date {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-icon-time {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}

::v-deep .el-input__prefix {
  right: 5px;
  left: auto;
}

::v-deep .el-input--prefix .el-input__inner {
  padding-left: 10px;
}
::v-deep .el-table__fixed-right::before {
  width: 0;
}

.dialog {
  width: 980px;
  height: 740px;
}
.dialog-footer {
  // margin-right: 44px;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px #f1f1f2;
  padding: 10px;
  &-all {
    font-size: 14px;
    margin-left: 34px;
    // margin-right: auto;
    flex: 0 0 58px;
    color: #ff9639;
    background: #fff;
    border: 1px solid #ff9639;
    &:hover {
      background: transparent;
    }
    &:nth-of-type(1) {
      // margin-left: 15px;
      margin-right: auto;
    }
  }
  &-btns {
    width: 85px;
    height: 35px;
    border: 1px solid #2f79e8;
    background: #fff;
    color: #2f79e8;
    margin-left: 20px;
    font-size: 14px;
    &:last-of-type {
      border: 1px solid #d5d5d5;
      color: #747474;
    }
  }
}

.form-layout {
  padding: 0 20px;
}

.form-title {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 17px;
    background: #2f79e8;
    height: 16px;
  }
}
::v-deep .el-input--mini .el-input__inner {
  height: 29.5px;
  line-height: 29.5px;
}

.opt-btn {
  cursor: pointer;
  margin: 0 3px;
  color: #fff;
  border: none;
}
::v-deep .el-form--label-top .el-form-item__label {
  padding: 0;
  font-weight: 400;
  font-size: 13px;
  color: #646464;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 13px;
}
.name {
  text-decoration: underline;
  cursor: pointer;
  transition: 0.5s all;
  &:hover,
  :focus {
    color: #3234d3;
  }
}

.excessive {
  width: 100%;
  color: red;
}

::v-deep .percent-column {
  .cell {
    top: 10px;
    height: 42px;
  }
}
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}
.column {
  display: inline-block;
  width: 100%;
  height: 20px;
}
.app-container {
  height: 100vh;
}
.table {
  width: 100%;
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
}
.el-button--export-all:focus,
.el-button--export-all:hover {
  background: #ff9639 !important;
  border-color: #fff !important;
  color: #fff !important;
}

.el-button--export-all {
  color: #ff9639;
  background-color: #fff;
  border-color: #ff9639;
  padding: 5px 8px;
  margin-top: -6px;
}
::v-deep // 合计行样式
.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  height: 50px;
  line-height: 50px;
  color: #ff9639;
  font-size: 13px;
  background: rgb(245, 246, 251);
}
::v-deep .panel-list {
  margin-left: -25px;
}

::v-deep .info {
  display: flex;
  justify-content: space-evenly;
  flex-flow: wrap;
  span {
    padding: 3px;
  }
  // justify-content: start;
  .title {
    span {
      margin-left: 5px;
      color: #f8a20f;
      font-weight: bold;
    }
  }
}
::v-deep .orange-row {
  color: #f8a20f;
  font-size: 14px;
  font-weight: bold;
  height: 60px;
}
::v-deep .row-layout {
  font-size: 14px;
  height: 60px;
}
.panel-list-item {
  height: 40px;
  padding: 5px;
}
</style>


<style scoped>
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
.el-dialog.is-fullscreen {
  width: 100% !important;
  margin-top: 0;
  margin-bottom: 0;
  height: 100% !important;
  overflow: auto;
}
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
</style>