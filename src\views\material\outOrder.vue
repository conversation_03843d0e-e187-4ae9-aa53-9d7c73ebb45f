<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />

    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :statetype="activetype" :buttomlist="buttomlist"
            :showSummary='true' :countList="countList" :actionList="actionList" title="供应商" otherHeight="180">

      <el-table-column slot="outOrderItemList" label="物资列表" width="80" align="center" type="expand">
        <template slot-scope="scope">
          <!-- :show-header="false" -->
          <el-table :data="scope.row.outOrderItemList" style="width: 100%" :header-cell-style="{background:'#ECF1FE'}">
            <el-table-column prop="itemName" label="物资名称" width="120">
            </el-table-column>
            <el-table-column prop="batchNo" label="批次号" width="120">
            </el-table-column>
            <el-table-column prop="categoryName" label="物资类型" width="120">
            </el-table-column>
            <el-table-column prop="createDate" label="日期">
            </el-table-column>
            <el-table-column prop="stockUnit" label="单位">
            </el-table-column>
            <el-table-column prop="deliveryAmount" label="数量">
            </el-table-column>
            <el-table-column prop="price" label="价格">
            </el-table-column>
            <!-- <el-table-column prop="useDesc" label="用途">
            </el-table-column> -->
          </el-table>
        </template>
      </el-table-column>
      <el-table-column slot="ext" label="物资" align="center">
        <template slot-scope="scope">
          {{scope.row.ext}}
        </template>
      </el-table-column>

      <el-table-column label="状态" slot="state" align="center">
        <template slot-scope="scope">
          <el-tag :type="(scope.row.state=='DRAFT' ? 'info':(scope.row.state == 'NEW' ? 'warning' : (scope.row.state == 'PASS' ? 'success' : (scope.row.state == 'REJECT' ? 'danger' :'info'))))"
                  size="mini">
            {{ scope.row.state == 'DRAFT' ? '草稿' : (scope.row.state == 'NEW' ? '待审核' : (scope.row.state == 'PASS' ? '已审核' : (scope.row.state == 'REJECT' ? '拒绝':''))) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" max-width="150" width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <el-tag class="opt-btn" v-if="scope.row.state == 'NEW'" color="#FF726B" @click="handleUpdate(scope.row.id,'review')">
              去审核
            </el-tag>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT,REJECT'" color="#FF726B"
                      @click="handleUpdate(scope.row.id,'update')">
                编辑
              </el-tag>
            </div>
            <div>
              <el-tag class="opt-btn" v-if="scope.row.state == 'PASS'" color="#FF726B"
                      @click="handleUpdate(scope.row.id,'watch')">
                查看详情
              </el-tag>
            </div>
            <div>
              <el-tag class="opt-btn" v-if="scope.row.state == 'PASS'" color="#FF726B" @click="handlePrint(scope.row,'update')">
                <!-- <span v-if="scope.row.isPrint=='Y'">补打</span> -->
                <span>打印</span>
              </el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag class="opt-btn" v-if="scope.row.state == 'DRAFT'  || scope.row.state == 'REJECT'" color="#2f79e8"
                      style="cursor: pointer;" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog :fullscreen="screen.full" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`" width="1080px"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-row type="flex" :gutter="50" v-if="reviewLogList.length>0">
            <el-col>
              <el-form-item label="步骤" prop="reviewLogList">
                <el-steps :active="reviewLogList.length">
                  <el-step v-for="(item,index) in reviewLogList" :key="index" :title="item.reviewResult"
                           :description="item.createDate+' '+item.reviewUserName">
                  </el-step>
                </el-steps>
              </el-form-item>
            </el-col>
          </el-row>
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="出库日期" prop="commitDate">
                    <date-select v-model="entityForm.commitDate" clearable :disabled="dialogStatus==='update'" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="领用部门" prop="usedDept">
                    <el-autocomplete v-model="entityForm.usedDept" :fetch-suggestions="queryUsedDeptList" placeholder="请输入委托单位"
                                     style="width: 100%" @focus="appUsedDeptListFocus()"></el-autocomplete>
                  </el-form-item>
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
              <el-row :gutter="50" type="flex">
                <el-col>
                  <el-form-item label="负责人" prop="chargeMan">
                    <el-autocomplete v-model="entityForm.chargeMan" :fetch-suggestions="queryChargeManList" placeholder="请输入负责人"
                                     style="width: 100%" @focus="appChargeManListFocus()"></el-autocomplete>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="会计" prop="accountMan">
                    <el-autocomplete v-model="entityForm.accountMan" :fetch-suggestions="queryAccountManList" placeholder="请输入送货人"
                                     style="width: 100%" @focus="appAccountManListFocus()"></el-autocomplete>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="保管人" prop="keepMan">
                    <el-autocomplete v-model="entityForm.keepMan" :fetch-suggestions="queryKeepManList" placeholder="请输入委托单位"
                                     style="width: 100%" @focus="appKeepManListFocus()"></el-autocomplete>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="领用人" prop="usedMan">
                    <el-autocomplete v-model="entityForm.usedMan" :fetch-suggestions="queryUsedManList" placeholder="请输入送货人"
                                     style="width: 100%" @focus="appUsedManListFocus()"></el-autocomplete>
                  </el-form-item>
                </el-col>
              </el-row>

            </div>
          </el-col>
          <el-col style="margin-top: 20px;">
            <div class="form-titl3  form-warp">
              <span>新增物资</span>
              <el-button type="export-all" @click="handleAdd" v-if="dialogStatus!='review'">添加物资</el-button>
            </div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-table :data="list" style="width: 100%" stripe :header-cell-style="headClass">
                    <el-table-column label="物资名称" align="center" prop="itemName">
                      <template slot-scope="scope">
                        <select-down :value.sync="scope.row.itemName" :list="nameItemEntity"
                                     @eventChange="handleNameEntity(scope.row)" />
                      </template>
                    </el-table-column>

                    <el-table-column label="批次号" align="center" prop="batchNo">
                      <template slot-scope="scope">
                        <!-- @change="handleBatchNo(scope.row) -->
                        <el-select class="select" v-model="scope.row.displayName" @change="handleBatchNo($event,scope.row)"
                                   filterable>
                          <el-option v-for="item in BatchNoEntity" :key="item.id" :label="item.displayName" :value="item">
                          </el-option>
                        </el-select>
                      </template>
                    </el-table-column>

                    <!-- <el-table-column label="物资编号" width="150" align="center" prop="itemCode">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.itemCode" disabled />
                      </template>
                    </el-table-column> -->
                    <el-table-column label="物资类型" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.categoryName" disabled />
                      </template>
                    </el-table-column>
                    <!-- <el-table-column label="单位" width="180" align="center" prop="stockUnit">
                      <template slot-scope="scope">
                        <code-select :value.sync="scope.row.stockUnit" disabled></code-select>
                      </template>
                    </el-table-column> -->

                    <el-table-column label="剩余数量" align="center" v-if="dialogStatus==='create'">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.leftAmount" disabled />
                      </template>
                    </el-table-column>

                    <el-table-column label="出库数量" align="center" prop="deliveryAmount">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.deliveryAmount" @blur="ceshi(scope.row.deliveryAmount,scope.row.leftAmount)"
                                  oninput="value=value.replace(/[^0-9.]/g,'')" @input="changePrice">
                        </el-input>
                      </template>
                    </el-table-column>

                    <el-table-column label="价格" align="center" prop="price">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.price" disabled oninput="value=value.replace(/[^0-9.]/g,'')"
                                  @input="changePrice"></el-input>
                      </template>
                    </el-table-column>

                    <el-table-column label="合计" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.totalMoney" disabled />
                      </template>
                    </el-table-column>

                    <!-- <el-table-column label="用途" width="180" align="center" prop="useDesc">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.useDesc" />
                      </template>
                    </el-table-column> -->
                    <el-table-column label="操作" width="120" align="center">
                      <template slot-scope="scope">
                        <el-tag class="opt-btn" color="#2f79e8" @click="handleRemove(scope.row)">删除</el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>

              <el-row :gutter="50" type="flex">
                <el-col>
                  <el-form-item label="合计金额" prop="totalMoney">
                    <el-input v-model="entityForm.totalMoney" disabled />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row>

            </div>
          </el-col>
          <el-col />
        </el-row>
      </el-form>

      <template v-if="isshoebottom">
        <div slot="footer" class="dialog-footer" v-if="isshow==false">
          <!-- <el-button class="dialog-footer-all" @click="handleFullScreen" style="color: #ACACAC; border: 1px solid #ACACAC;">
            {{screen.full?'取消全屏':'全屏'}}</el-button> -->

          <el-button style="border: 1px solid #2f79e8;color: #2f79e8;" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="saveEntityForm('entityForm')" v-if="perms[`${curd}:update`]||false">保存
          </el-button>
          <el-button class="dialog-footer-btns" @click="SaveDraft('entityForm')">保存草稿</el-button>
        </div>
        <div slot="footer" class="dialog-footer" v-else>
          <el-button @click="rejectfn(entityForm.id,entityForm.reviewMessage)" class="dialog-footer-btns" style="width:100px">拒绝
          </el-button>
          <el-button style="border: 1px solid #2f79e8;color: #2f79e8;" class="dialog-footer-btns" :loading="entityFormLoading"
                     @click="passfn(entityForm.id,entityForm.reviewMessage)">
            审核通过
          </el-button>
        </div>
      </template>
    </el-dialog>

    <div class="print-div" id="print_area">
      <div class="title">出库单</div>
      <div class="print_h">
        <div class="width40">领用部门 {{Printdata.usedDept}}</div>
        <div class="width30">{{Printdata.commitDate}}</div>
        <div class="width30 textr">第&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;号</div>
      </div>
      <table border="1" width="100%" height="100%" cellspacing="0" cellpadding="0">
        <thead>
          <tr>
            <th class="width150">品名</th>
            <th class="width60">规格</th>
            <th class="width100">单位</th>
            <th class="width80">数量</th>
            <th class="width60">单价</th>
            <th class="width80">金额</th>
            <th class="width200">说明</th>
          </tr>
        </thead>
        <tbody align="center">
          <tr class="trtdheight" v-for="(item,index) in Printdata.outOrderItemList" :key="index">
            <td class="width100">{{item.itemName}}</td>
            <td></td>
            <td>{{item.stockUnit}}</td>
            <td class="width80">{{item.deliveryAmount}}</td>
            <td class="width60">{{item.price}}</td>
            <td>{{item.totalMoney}}</td>
            <td></td>
          </tr>
          <!-- <tr class="trtdheight">
            <td>合计</td>
            <td></td>
            <td></td>
            <td>{{item.deliveryAmount}}</td>
            <td>{{item.price}}</td>
            <td></td>
            <td></td>
          </tr> -->
        </tbody>
      </table>
      <div class="print_h">
        <div class="width25">负责人 {{Printdata.chargeMan}}</div>
        <div class="width25">会计 {{Printdata.accountMan}}</div>
        <div class="width25">保管员 {{Printdata.keepMan}}</div>
        <div class="width25">领用人 {{Printdata.usedMan}}</div>
      </div>
    </div>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/material/outOrder'
import { validatePhone } from '@/utils/validate'
import { inOrderOption } from '@/const'
import { createMemoryField } from '@/utils/index'
import MaterialinformationModel from '@/model/material/Materialinformation'
import { Decimal } from 'decimal.js'
import print from 'print-js'
const form = {
  _id: '1',
  itemId: 0,
  batchNo: '', //批次号
  code: '',
  stockUnit: '', //单位
  deliveryAmount: '', //数量
  leftAmount: '', //剩余数量
  itemCode: '', //物资编号
  itemName: '', //物资名称
  totalMoney: '',
  price: '',
  // useDesc: '', //用途
  categoryId: '', //分类id
  categoryName: '' //分类名称
}
export default {
  name: 'inOrder',
  mixins: [Mixins],
  data() {
    const validateIsPhone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validatePhone(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      curd: 'outOrder',
      screen: { full: false },
      model: Model,
      entityForm: { ...Model.model },
      filterOption: { ...inOrderOption },
      memoryEntity: { fields: {}, triggered: false },
      entityFormRules: {
        phone: [{ validator: validateIsPhone, trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      rules: {
        commitDate: { required: true, message: '请输入选择日期', trigger: 'change' },
        commitBy: { required: true, message: '请输入存放人', trigger: 'blur' }
      },
      nameItemEntity: [],
      BatchNoEntity: [],
      form: { ...form },
      list: [{ ...form }],
      nameEntity: { options: [], active: '' },
      isshow: false,
      reviewLogList: [],
      isshoebottom: true,
      countList: [],

      UsedDeptList: [],
      ChargeManList: [],
      AccountManList: [],
      UsedManList: [],
      KeepManList: [],
      Printdata: {}
    }
  },
  watch: {
    activetype: function (newV, oldV) {
      this.getlist(newV)
    }
  },
  computed: {},
  created() {
    this.activetype = 'DRAFT,REJECT'
    this.permsActionbtn(this.curd)
    // this.permsActionLsit(this.curd)
    this.getMaterialType()

    this.list = []
    this.memoryEntity.fields = createMemoryField({
      fields: ['contractId', 'contractCode', 'supplierId', 'supplierName'],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })

    if (this.$route.params.type) {
      this.activetype = this.$route.params.type
      if (this.$route.params.id) {
        this.handleUpdate(this.$route.params.id, 'review')
      }
    } else {
      this.activetype = 'DRAFT,REJECT,NEW,PASS_FINANCE,PASS'
      this.actionList.forEach((val, index) => {
        if (val.type == this.activetype) {
          val.active = true
        } else {
          val.active = false
        }
      })
    }

    this.getnumber()
  },
  methods: {
    // 打印
    async handlePrint(row) {
      let sum = 0
      let obj = {}
      for (let i = 0; i < row.outOrderItemList.length; i++) {
        if (row.outOrderItemList[i].itemName != '合计') {
          sum += row.outOrderItemList[i].deliveryAmount
        }
        if (row.outOrderItemList[i].itemName === '合计') {
          row.outOrderItemList.splice(i, 1)
        }
      }
      obj = {
        itemName: '合计',
        stockUnit: '',
        deliveryAmount: sum,
        price: '',
        totalMoney: ''
      }
      row.outOrderItemList.push(obj)
      this.Printdata = row

      // this.handleUpdate(row.id, 'watch')
      // const { data } = await this.model.getprint(row.id)
      // let objectUrl = window.URL.createObjectURL(new Blob([data], { type: 'application/pdf' }))
      // window.open(objectUrl)

      this.$nextTick(() => {
        // 此处的style即为打印时的样式
        const style =
          '@page {  } ' +
          '@media print{.print-div{padding:8px;background-color:#cccccc;line-height:30px} .trtdheight td{height:35px} .title{text-align:center;border-bottom:solid 2px #000;width:300px;margin:0 auto;font-size:30px;font-weight:900;padding:10px 0} .width25{width:25%} .print_h{display:flex;align-items:center;margin:10px 0} .width40{width:40%} .width30{width: 30%; } .textr{text-align:right;} .width150{width:150px} .width100{width:100px} .width80{width:80px} .width60{width:60px} .width200{width:200px}'
        print({
          printable: 'print_area',
          type: 'html',
          style: style, // 亦可使用引入的外部css;
          scanStyles: false
        })
      })
    },

    createFilter(queryString) {
      return (item) => {
        return item.value.toString().toLowerCase().includes(queryString.toString().toLowerCase()) === true
      }
    },
    // 获取领用部门
    queryUsedDeptList(queryString, cb) {
      var UsedDeptList = this.UsedDeptList
      var results = queryString ? UsedDeptList.filter(this.createFilter(queryString)) : UsedDeptList
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        cb(results)
      }, 1000 * Math.random())
    },
    async appUsedDeptListFocus() {
      let { data } = await this.model.findDistinctUsedDeptList()
      var r = data.map((item) => {
        return { value: item }
      })
      console.log(r)
      if (r != null) {
        this.UsedDeptList = r
      }
    },
    // 获取负责人
    queryChargeManList(queryString, cb) {
      var ChargeManList = this.ChargeManList
      var results = queryString ? ChargeManList.filter(this.createFilter(queryString)) : ChargeManList
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        cb(results)
      }, 1000 * Math.random())
    },
    async appChargeManListFocus() {
      let { data } = await this.model.findDistinctChargeManList()
      var r = data.map((item) => {
        return { value: item }
      })
      console.log(r)
      if (r != null) {
        this.ChargeManList = r
      }
    },

    // 获取负会计
    queryAccountManList(queryString, cb) {
      var AccountManList = this.AccountManList
      var results = queryString ? AccountManList.filter(this.createFilter(queryString)) : AccountManList
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        cb(results)
      }, 1000 * Math.random())
    },
    async appAccountManListFocus() {
      let { data } = await this.model.findDistinctAccountManList()
      var r = data.map((item) => {
        return { value: item }
      })
      console.log(r)
      if (r != null) {
        this.AccountManList = r
      }
    },

    // 获取保管
    queryKeepManList(queryString, cb) {
      var KeepManList = this.KeepManList
      var results = queryString ? KeepManList.filter(this.createFilter(queryString)) : KeepManList
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        cb(results)
      }, 1000 * Math.random())
    },
    async appKeepManListFocus() {
      let { data } = await this.model.findDistinctKeepManList()
      var r = data.map((item) => {
        return { value: item }
      })
      console.log(r)
      if (r != null) {
        this.KeepManList = r
      }
    },

    // 获取领用人
    queryUsedManList(queryString, cb) {
      var UsedManList = this.UsedManList
      var results = queryString ? UsedManList.filter(this.createFilter(queryString)) : UsedManList
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        cb(results)
      }, 1000 * Math.random())
    },
    async appUsedManListFocus() {
      let { data } = await this.model.findDistinctUsedManList()
      // console.log('获取保管人记录')
      var r = data.map((item) => {
        return { value: item }
      })
      console.log(r)
      if (r != null) {
        this.UsedManList = r
      }
    },

    async getnumber() {
      const res = await this.model.getcountByState()
      this.countList = res.data
    },
    // 获取物资类型
    async getMaterialType() {
      let { data } = await MaterialinformationModel.getpageAmountGt0({ ...this.listQuery, size: 999 })
      this.nameItemEntity = data.records
      this.handleAdd()
    },
    getSite(e) {
      // console.log(e)
    },
    handleNameEntity(val) {
      let data = this.nameItemEntity
      let data2 = this.list
      this.$nextTick(() => {
        for (var i = 0; i < data.length; i++) {
          if (val.itemName == data[i].name) {
            for (var j = 0; j < data2.length; j++) {
              if (val._id == data2[j]._id) {
                let obj = {}
                obj._id = val._id
                obj.itemName = val.itemName
                obj.totalMoney = data2[j].totalMoney
                obj.price = data2[j].price
                if (val.code) {
                  obj.itemCode = val.code
                } else {
                  obj.itemCode = data[i].code
                }
                if (data[i].deliveryAmount) {
                  obj.deliveryAmount = data[i].deliveryAmount
                } else {
                  obj.deliveryAmount = data[i].deliveryAmount
                }
                obj.categoryName = data[i].categoryName
                obj.categoryId = data[i].categoryId
                obj.itemId = data[i].id
                obj.stockUnit = data[i].stockUnit
                // obj.useDesc = data[i].useDesc

                this.getBatchNoFn(data[i].id)
                this.$set(data2, [j], obj)
              }
            }
          }
        }
        this.list = data2
      })
      this.entityForm.outOrderItemList = this.list
    },
    async getBatchNoFn(itemId) {
      const { data } = await this.model.getBatchNoList(itemId)
      // console.log(data)
      this.BatchNoEntity = data
    },
    handleBatchNo(eve, val) {
      let newdata = val.displayName

      let data = this.BatchNoEntity
      let data2 = this.list
      console.log(data2)
      console.log(data)
      this.$nextTick(() => {
        for (var i = 0; i < data.length; i++) {
          if (newdata.batchNo == data[i].batchNo) {
            for (var j = 0; j < data2.length; j++) {
              if (val._id == data2[j]._id) {
                console.log(data[i].leftAmount)
                let obj = {}
                obj._id = val._id
                obj.batchNo = newdata.batchNo
                obj.displayName = newdata.displayName
                obj.price = newdata.price
                obj.itemName = data2[j].itemName
                obj.itemId = data2[j].itemId
                obj.itemCode = data2[j].itemCode
                if (data[i].leftAmount) {
                  obj.leftAmount = data[i].leftAmount
                }
                obj.deliveryAmount = data2[j].deliveryAmount
                obj.categoryName = data2[j].categoryName
                obj.categoryId = data2[j].categoryId
                obj.stockUnit = data2[j].stockUnit

                obj.totalMoney = data2[j].totalMoney
                // obj.useDesc = data2[j].useDesc
                this.$set(data2, [j], obj)
              }
            }
          }
        }
        this.list = data2
      })
      this.entityForm.outOrderItemList = this.list
    },

    handleFullScreen() {
      this.screen.full = !this.screen.full
      const el = this.$refs.dialogStatus.$el
      const el_body = el.querySelector('.el-dialog__body')
      el_body.style.height = this.screen.full ? '85vh' : '70vh'
    },
    // 保存取消时会触发初始化数据
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogVisible = false
      this.isshow = false
      this.isshoebottom = true
      this.reviewLogList = []
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.list = []
      this.handleAdd()
    },
    handleAdd() {
      const form = { ...this.form }
      form._id = Math.random().toFixed(6).slice(-6)
      this.list.push(form)
    },
    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },

    handleRemove({ _id }) {
      const index = this.list.findIndex((item) => item._id === _id)
      if (index === -1) return false
      this.list.splice(index, 1)
      this.settotalMoney()
    },
    changePrice() {
      let Things = this.list
      for (var i = 0; i < Things.length; i++) {
        if (Things[i].deliveryAmount && Things[i].price) {
          Things[i].totalMoney = new Decimal(Things[i].deliveryAmount).mul(Things[i].price).toNumber()
          this.settotalMoney()
        }
      }
    },
    settotalMoney() {
      let Things = this.list
      let number = 0
      for (var i = 0; i < Things.length; i++) {
        if (Things[i].totalMoney) {
          number += Things[i].totalMoney
        }
      }
      this.entityForm = { ...this.entityForm, totalMoney: number }
    },
    ceshi(number, leftAmount) {
      let Things = this.list
      if (Number(number) > Number(leftAmount)) {
        this.$message({ type: 'warning', message: '出库数量不能大于剩余数量!' })
        for (var i = 0; i < Things.length; i++) {
          Things[i].deliveryAmount = ''
        }
      } else {
        for (var i = 0; i < Things.length; i++) {
          if (Things[i].deliveryAmount == undefined) {
            Things[i].deliveryAmount = number
          }
        }
      }
    },

    async saveEntityForm(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.list.length) {
          // if (this.list.some((val) => val.deliveryAmount == undefined)) {
          //   this.$message({
          //     type: 'error',
          //     message: '数量不能为空',
          //   })
          //   return
          // }
          if (
            this.list.some(
              (val) =>
                val.deliveryAmount == undefined ||
                val.categoryId == '' ||
                val.categoryName == '' ||
                val.code == '' ||
                val.itemCode == '' ||
                val.itemId == '' ||
                val.itemName == '' ||
                val.stockUnit == '' ||
                val.useDesc == ''
            )
          ) {
            this.$message({
              type: 'error',
              message: '请将物资信息填写完整！'
            })
            return
          }
          const list = []
          this.list.forEach((item) => {
            if (item.batchNo) {
              list.push({ ...item })
            }
          })
          this.entityFormLoading = true
          this.activetype = 'NEW'
          const res = await Model.getApproved({ ...this.entityForm, outOrderItemList: list })
          this.entityFormLoading = false
          this.getnumber()
          if (res) {
            this.resetVariant()
            this.getList()
            this.getnumber()
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else {
          this.$message({ message: '请将物资信息填写完整！', type: 'warning' })
        }
      }
    },

    //保存草稿
    async SaveDraft(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.list.length) {
          if (
            this.list.some(
              (val) =>
                val.deliveryAmount == undefined ||
                val.categoryId == '' ||
                val.categoryName == '' ||
                val.code == '' ||
                val.itemCode == '' ||
                val.itemId == '' ||
                val.itemName == '' ||
                val.stockUnit == '' ||
                val.useDesc == ''
            )
          ) {
            this.$message({
              type: 'error',
              message: '请将物资信息填写完整！'
            })
            return
          }
          const list = []
          this.list.forEach((item) => {
            list.push({ ...item })
          })
          const res = await Model.SaveDraftfn({ ...this.entityForm, inOrderItemList: list })
          if (res) {
            this.$message({ type: 'success', message: '保存草稿成功!' })
            this.entityForm = { ...this.model.model }
            this.getList()
            this.getnumber()
            this.dialogFormVisible = false
          }
        } else {
          this.$message({ message: '请将物资信息填写完整！', type: 'warning' })
        }
      }
    },

    async handleUpdate(id, type) {
      if (type == 'review') {
        this.isshow = true
        this.dialogStatus = 'review'
      } else if (type == 'update') {
        this.isshow = false
        this.dialogStatus = 'update'
      }

      if (type == 'watch') {
        this.isshoebottom = false
        this.dialogStatus = 'watch'
      } else {
        this.isshoebottom = true
      }
      const { data } = await this.model.getUploadList(id)
      this.entityForm = { ...data }
      this.list = data.outOrderItemList
      this.reviewLogList = data.reviewLogList
      delete this.entityForm.outOrderItemList
      this.dialogFormVisible = true
      let newarry = []
      if (data.reviewLogList.length > 0) {
        data.reviewLogList.forEach((item, index) => {
          newarry.push(item.reviewMessage)
        })
        this.entityForm.reviewMessage = newarry.toString()
      }
    },
    //点击状态获取数据
    async getlist(newV) {
      this.activetype = newV
      this.actionList.forEach((val, index) => {
        if (val.type == newV) {
          val.active = true
        } else {
          val.active = false
        }
      })
      this.filtersSeach.filter_INS_state = newV
      this.$refs[this.curd].searchChange({ ...this.listQuery, size: 50, ...this.filtersSeach })
    },

    async passfn(id, reviewMessage) {
      const res = await this.model.savepass({ id: id, reviewMessage: reviewMessage })
      this.getnumber()
      this.$message({ type: 'success', message: '审核通过成功!' })
      this.entityForm = { ...this.model.model }
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    },

    async rejectfn(id, reviewMessage) {
      const res = await this.model.savereject({ id: id, reviewMessage: reviewMessage })
      this.getnumber()
      this.entityForm = { ...this.model.model }
      this.$message({ type: 'success', message: '拒绝成功!' })
      this.dialogFormVisible = false
      this.$refs[this.curd].$emit('refresh')
    }
  }
}
</script>


 <style lang="scss" scoped>
// @import '@/styles/router-page.scss';
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
// .opt-btnV {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
</style>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  height: 65vh !important;
  padding: 20px 35px 0 35px !important;
}
::v-deep .el-dialog {
  width: 60% !important;
}

::v-deep .el-dialog__footer {
  padding: 0px 0px 0px;
}
::v-deep .el-input.is-disabled .el-input__inner {
  color: #606266;
}
::v-deep .el-icon-date {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-icon-time {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}

.form-titleV {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;
  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 13px;
    background: #2f79e8;
    height: 16px;
  }
}
.form-titl3 {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;
  margin-bottom: 5px;
  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 10px;
    background: #2f79e8;
    height: 16px;
  }
}
.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
}

::v-deep .el-icon-date {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-icon-time {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}

::v-deep .el-input__prefix {
  right: 5px;
  left: auto;
}

::v-deep .el-input--prefix .el-input__inner {
  padding-left: 10px;
}
::v-deep .el-table__fixed-right::before {
  width: 0;
}

.dialog {
  width: 980px;
  height: 740px;
}
.dialog-footer {
  // margin-right: 44px;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px #f1f1f2;
  padding: 10px;
  &-all {
    font-size: 14px;
    margin-left: 34px;
    // margin-right: auto;
    flex: 0 0 58px;
    color: #ff9639;
    background: #fff;
    border: 1px solid #ff9639;
    &:hover {
      background: transparent;
    }
    &:nth-of-type(1) {
      // margin-left: 15px;
      margin-right: auto;
    }
  }
  &-btns {
    width: 85px;
    height: 35px;
    border: 1px solid #2f79e8;
    background: #fff;
    color: #2f79e8;
    margin-left: 20px;
    font-size: 14px;
    &:last-of-type {
      border: 1px solid #d5d5d5;
      color: #747474;
    }
  }
}

.form-layout {
  padding: 0 20px;
}

.form-title {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 17px;
    background: #2f79e8;
    height: 16px;
  }
}
::v-deep .el-input--mini .el-input__inner {
  height: 29.5px;
  line-height: 29.5px;
}

.opt-btn {
  cursor: pointer;
  margin: 0 3px;
  color: #fff;
  border: none;
}
::v-deep .el-form--label-top .el-form-item__label {
  padding: 0;
  font-weight: 400;
  font-size: 13px;
  color: #646464;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 13px;
}
.name {
  text-decoration: underline;
  cursor: pointer;
  transition: 0.5s all;
  &:hover,
  :focus {
    color: #3234d3;
  }
}

.excessive {
  width: 100%;
  color: red;
}

::v-deep .percent-column {
  .cell {
    top: 10px;
    height: 42px;
  }
}
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}
.column {
  display: inline-block;
  width: 100%;
  height: 20px;
}
.app-container {
  height: 100vh;
}
.table {
  width: 100%;
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
}
.el-button--export-all:focus,
.el-button--export-all:hover {
  background: #ff9639 !important;
  border-color: #fff !important;
  color: #fff !important;
}

.el-button--export-all {
  color: #ff9639;
  background-color: #fff;
  border-color: #ff9639;
  padding: 5px 8px;
  margin-top: -6px;
}
::v-deep // 合计行样式
.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  height: 50px;
  line-height: 50px;
  color: #ff9639;
  font-size: 13px;
  background: rgb(245, 246, 251);
}
::v-deep .panel-list {
  margin-left: -25px;
}

::v-deep .info {
  display: flex;
  justify-content: space-evenly;
  flex-flow: wrap;
  span {
    padding: 3px;
  }
  // justify-content: start;
  .title {
    span {
      margin-left: 5px;
      color: #f8a20f;
      font-weight: bold;
    }
  }
}
::v-deep .orange-row {
  color: #f8a20f;
  font-size: 14px;
  font-weight: bold;
  height: 60px;
}
::v-deep .row-layout {
  font-size: 14px;
  height: 60px;
}
.panel-list-item {
  height: 40px;
  padding: 5px;
}
</style>


<style scoped>
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
.el-dialog.is-fullscreen {
  width: 100% !important;
  margin-top: 0;
  margin-bottom: 0;
  height: 100% !important;
  overflow: auto;
}
.inputbox >>> .el-input__inner {
  border: none;
  text-align: center;
}
</style>