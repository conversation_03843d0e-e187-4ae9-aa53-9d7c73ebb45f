<template>
  <div class="app-container">
    <!-- <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                  :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" /> -->

    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate" :showAdd="false"
                  @import="handleImpiort" :showRefresh="perms[`${curd}:Refresh`]||false"
                  :showImport="perms[`${curd}:addimport`]||false" />
    <!-- 
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">
      <el-table-column label="品名" slot="name" prop="name" width="150" align="center">
        <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{ scope.row.name }}</span></template>
      </el-table-column>
      <el-table-column label="挥发分Vdaf" slot="cleanVdaf" min-width="100" align="center">
        <template slot-scope="scope">
          <div class="tags">
            <el-tag class="tag" effect="plain" type="info">
              {{ scope.row.cleanVdaf }}
            </el-tag>
            <span>-</span>
            <el-tag class="tag" effect="plain" type="info">
              {{ scope.row.cleanVdaf2 }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Y" slot="procY" min-width="100" align="center">
        <template slot-scope="scope">
          <div class="tags">
            <el-tag class="tag" effect="plain" type="info">
              {{ scope.row.procY }}
            </el-tag>
            <span>-</span>
            <el-tag class="tag" effect="plain" type="info">
              {{ scope.row.procY2 }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否开放接口" slot="isPublic" prop="isPublic" width="100" v-if="perms[`${curd}:interface`] || false">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isPublicBoolean === true ? 'success' : 'warning'">
            {{ scope.row.isPublicBoolean === true ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" slot="opt" max-width="100" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)">编辑</el-tag>
          <el-tag class="opt-btn" color="#FF726B" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`] || false">删除
          </el-tag>
        </template>
      </el-table-column>
    </s-curd> -->

    <div class="s-curd">
      <div style="padding:15px 10px">
        <span style="font-size:13px; font-weight: 900;">所有消息</span>
      </div>
      <div class="listbox">
        <ul>
          <li v-for="(item,index) in currentTableData" :key="index">
            <div class="form-titlV" :class="item.isView=='N'?'form-title':''">
              <div class="context">
                <div class="leftcon">
                  <span style="position: relative;">{{item.messageContent}}</span>
                  <span style="font-size:12px;color:blue;" @click.stop="gotolink(item)"> 详情 > </span>
                </div>
                <div class="rightcon">{{item.createDate}}</div>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class="pagination-container">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="filters.current"
                       :page-sizes="[10, 20,50]" :page-size="filters.size" layout="total, sizes, prev, pager, next, jumper"
                       :total="filters.total">
        </el-pagination>
      </div>
    </div>

    <el-dialog width="880px" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               :disabled="dialogStatus === 'watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本类型</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="name">
                    <el-input v-model="entityForm.name" clearable :disabled="dialogStatus==='update'" />
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="矿井名称" prop="mineName">
                    <el-input v-model="entityForm.mineName" clearable placeholder="矿井名称" />
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="编码" prop="code">
                    <el-input v-model="entityForm.code" clearable placeholder="编码" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="类型" prop="type">
                    <dict-select v-model="entityForm.type" type="stock_type" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="来源" prop="source">
                    <el-select v-model="entityForm.source" clearable placeholder="请选择来源">
                      <el-option v-for="item in sourceOptions.list" :key="item.label" :label="item.label" :value="item.label" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤种" prop="coalCategory">
                    <category-select :value.sync="entityForm.coalCategory" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="单铲重量" prop="shovelWeight">
                    <el-input v-model="entityForm.shovelWeight" clearable placeholder="单铲重量" autocomplete="off"
                              oninput="value=value.replace(/[^0-9.]/g,'')">
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col />
              </el-row>
            </div>
          </el-col>
          <el-col v-show="sourceOptions.show || ''">
            <div class="form-title">质量标准</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="水分" prop="cleanMt" label-width="140px">
                    <el-input v-model="entityForm.cleanMt" autocomplete="off" oninput="value=value.replace(/[^0-9.]/g,'')"
                              clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="灰分Ad" prop="cleanAd">
                    <el-input v-model="entityForm.cleanAd" autocomplete="off" clearable
                              oninput="value=value.replace(/[^0-9.]/g,'')">
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="硫St,d" prop="cleanStd">
                    <el-input v-model="entityForm.cleanStd" clearable oninput="value=value.replace(/[^0-9.]/g,'')">
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="挥发分Vdaf">
                    <el-row :gutter="10">
                      <el-col :span="11">
                        <el-form-item prop="cleanVdaf">
                          <el-input v-model="entityForm.cleanVdaf" clearable />
                        </el-form-item>
                      </el-col>
                      <el-col :span="2">
                        <el-form-item>
                          -
                        </el-form-item>
                      </el-col>
                      <el-col :span="11">
                        <el-form-item prop="cleanVdaf2">
                          <el-input v-model="entityForm.cleanVdaf2" clearable />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="特征" prop="crc">
                    <el-input v-model="entityForm.crc" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="粘结" prop="procG">
                    <el-input v-model="entityForm.procG" clearable> </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="X值" prop="procX">
                    <el-input v-model="entityForm.procX" oninput="value=value.replace(/[^0-9.]/g,'')" clearable>
                      <template slot="append">mm</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Y值">
                    <el-row :gutter="10">
                      <el-col :span="11">
                        <el-form-item prop="procY">
                          <el-input v-model="entityForm.procY" clearable />
                        </el-form-item>
                      </el-col>
                      <el-col :span="2">
                        <el-form-item>
                          -
                        </el-form-item>
                      </el-col>
                      <el-col :span="11">
                        <el-form-item prop="procY2">
                          <el-input v-model="entityForm.procY2" clearable />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col />
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">其他信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50" v-if="perms[`${curd}:interface`] || false">
                <el-col>
                  <el-form-item label="是否开放接口" prop="isPublicBoolean">
                    <el-switch v-model="entityForm.isPublicBoolean"></el-switch>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus !== 'watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`] || false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/messagereminder/productList'
import { createMemoryField } from '@/utils/index'
import { viewPushMessage } from '@/api/user'
export default {
  name: 'product',
  mixins: [Mixins],
  data() {
    return {
      curd: 'product',
      model: Model,
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'messageContent',
            filter: 'filter_LIKES_messageContent',
            props: {
              placeholder: '请输入消息内容',
              clearable: true
            }
          },
          // {
          //   prop: 'categoryName',
          //   filter: 'filter_LIKES_categoryName',
          //   component: 'select-categoryName',
          //   props: { placeholder: '请选择消息类型' },
          // },
          {
            prop: 'isView',
            filter: 'filter_EQS_isView',
            component: 'dict-select',
            props: {
              name: 'select',
              type: 'sync_type',
              placeholder: '请选择消息类型',
              clearable: true
            }
          }
        ]
      },
      entityForm: { ...Model.model, filter_EQS_type: '', code: '', type: '' },
      rules: {
        messageContent: { required: true, message: '请输入品名', trigger: 'blur' },
        messageType: { required: true, message: '请选择类型', trigger: 'blur' }
        // coalCategory: { required: true, message: '请选择煤种类型', trigger: 'change' },
      },
      actions: [],
      sourceOptions: {
        show: true,
        list: [{ label: '自产' }, { label: '采购煤' }]
      },
      memoryEntity: { fields: {}, triggered: false },

      currentTableData: [],
      filters: { messageContent: '', messageType: '', current: 1, size: 0, total: 0 }
    }
  },
  created() {
    this.memoryEntity.fields = createMemoryField({ fields: ['name', 'coalCategory', 'source'], target: this.entityForm })
    this.getList()
  },
  watch: {
    'entityForm.isPublicBoolean': {
      handler(v) {
        this.entityForm.isPublic = v ? 'Y' : 'N'
      }
    },
    'entityForm.source'(v) {
      if (v === '自产') {
        this.sourceOptions.show = true
      } else if (v === '采购煤') {
        this.sourceOptions.show = false
        this.hiddenQuality()
      } else if (v === '') {
        // console.log('00000')
        this.sourceOptions.show = true
      }
    }
  },
  mounted() {
    this.showRoutes = JSON.parse(localStorage.getItem('showRoutes'))
  },
  methods: {
    async gotolink(item) {
      if (item.viewType === 'MANUAL' && item.isView == 'N') {
        //消息查看
        // console.log('消息查看')
        const res = await viewPushMessage({ id: item.id })
        // console.log(res)
      }

      let type = ''
      if (item.type == 'SettlePay') {
        //付款结算
        if (item.reviewType == 'settlePay:ismanagerreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settlePay:isFinancereview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'settlePay:isBossreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == '') {
          type = ''
        }
        let data = { parent: `finance`, child: `settlement`, Pagename: `settlePay`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }

      if (item.type == 'SettleRecv') {
        //收款结算
        if (item.reviewType == 'settleRecv:ismanagerreview') {
          type = 'NEW'
        } else if (item.reviewType == 'settleRecv:isFinancereview') {
          type = 'PASS'
        } else if (item.reviewType == '') {
          type = ''
        }
        let data = { parent: `finance`, child: `settlement`, Pagename: `settleRecv`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }

      if (item.type == 'ApplyPay') {
        //付款单
        if (item.reviewType == 'applyPay:ismanagerreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'applyPay:isFinancereview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'applyPay:isBossreview') {
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'applyPay:pay') {
          type = 'PASS'
        } else if (item.reviewType == '') {
          type = ''
        }
        let data = { parent: `finance`, child: `payment`, Pagename: `applyPay`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }

      if (item.type == 'ContractBuy') {
        let Auditstatus = ''
        //采购合同
        if (item.reviewType == 'contractBuy:ismanagerreview') {
          //  去审核(总经理)
          Auditstatus = 'MANAGER'
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'contractBuy:isFinancereview') {
          //去审核(财务)
          Auditstatus = 'FINANCE'
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'contractBuy:isFinanceLeaderreview') {
          //去审核(财务负责人)
          Auditstatus = 'FINANCE_LEADER'
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == 'contractBuy:isBossreview') {
          //     去审核(董事长)
          Auditstatus = 'isBossreview'
          type = 'NEW,PASS_FINANCE'
        } else if (
          item.reviewType == 'contractBuy:priceFactoryManagerWaitforReviewed' ||
          item.reviewType == 'contractBuy:priceManagerWaitforReviewed' ||
          item.reviewType == 'contractBuy:priceBossWaitforReviewed'
        ) {
          Auditstatus = 'AdjustPriceReview'
          type = 'NEW,PASS_FINANCE'
        } else if (
          item.reviewType == 'contractBuy:carriageFactoryManagerWaitforReviewed' ||
          item.reviewType == 'contractBuy:carriageManagerWaitforReviewed' ||
          item.reviewType == 'contractBuy:carriageBossWaitforReviewed'
        ) {
          Auditstatus = 'freightReview'
          type = 'NEW,PASS_FINANCE'
        } else if (item.reviewType == '') {
          Auditstatus = ''
          type = ''
        }

        let data = {
          parent: `base`,
          child: `buy`,
          Pagename: `contractBuy`,
          params: { id: item.refId, type: type, Auditstatus: Auditstatus }
        }
        this.handleToNamePage(data)
      }
      if (item.type == 'ContractSell') {
        //销售合同
        if (item.reviewType == 'contractSell:ismanagerreview') {
          type = 'PASS_FINANCE'
        } else if (item.reviewType == 'contractSell:isFinancereview') {
          type = 'NEW'
        } else if (item.reviewType == '') {
          type = ''
        }

        let data = { parent: `base`, child: `buy`, Pagename: `contractSell`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }
      if (item.type == 'WeightHouseNotice') {
        //销磅房通知
        if (item.reviewType == 'weightHouseNotice:isnoticePr') {
          type = 'NEW'
        } else if (item.reviewType == 'weightHouseNotice:send') {
          type = 'PASS'
        } else if (item.reviewType == '') {
          type = ''
        }
        let data = { parent: `finance`, Pagename: `weightHouseNotice`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }
      if (item.type == 'InOrder') {
        //入库
        if (item.reviewType == 'inOrder:WarehousingWaitforreviewed') {
          type = 'NEW'
        } else if (item.reviewType == '') {
          type = ''
        }
        let data = { parent: `material`, Pagename: `inOrder`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }
      if (item.type == 'OutOrder') {
        //出库
        if (item.reviewType == 'outOrder:ExwarehouseWaitforreviewed') {
          type = 'NEW'
        } else if (item.reviewType == '') {
          type = ''
        }
        let data = { parent: `material`, Pagename: `outOrder`, params: { id: item.refId, type: type } }
        this.handleToNamePage(data)
      }
    },
    // 通过name去跳转类似post
    handleToNamePage(option) {
      // console.log(option)
      const { parent, child, Pagename, params } = option
      const route = this.showRoutes.find((route) => route.name === parent)
      route.params = option.params
      this.$store.dispatch('changeRoute', route)
      this.$store.dispatch('changeChildRoute', { parent: child, child: '' })
      this.$router.replace({ name: Pagename, params })
    },
    handleSizeChange(val) {
      this.filters.size = val
      this.getList()
      // console.log(`每页 ${val} 条`)
    },
    handleCurrentChange(val) {
      this.filters.current = val
      // console.log(`当前页: ${val}`)
      this.getList()
    },
    handleFilter(filters) {
      this.filters = filters
      this.getList()
    },
    handleFilterReset() {
      this.filters = {}
      this.getList()
    },
    async getList() {
      this.currentTableData = []
      this.loading = true
      const { data } = await Model.page(this.filters)
      // console.log('888888')
      // console.log(data)

      if (data.records.length > 0) {
        // console.log(data.records)
        // for (var i = 0; i < data.length; i++) {
        //   console.log(data[i])
        //   for (var j = 0; j < data[i].mixCoalVoList.length; j++) {
        //     let obj = {}
        //     obj = data[i].mixCoalVoList[j]
        //     this.currentTableData.push(obj)
        //     this.loading = false
        //   }
        // }
        this.filters.total = data.total
        this.currentTableData = data.records
        // console.log(this.currentTableData)
        this.loading = false
      } else {
        this.loading = false
      }
    },

    baseVariant() {
      if (!this.memoryEntity.triggered) {
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    hiddenQuality() {
      const { id, name, coalCategory, source, code, type, isPublicBoolean } = this.entityForm
      this.entityForm = { ...Model.model, id, name, coalCategory, source, code, type, isPublicBoolean }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.pagination-container {
  display: flex;
  margin: 0;
  padding: 15px 0px 0px;
  -ms-flex-pack: end;
  justify-content: flex-end;
  border: none;
}
.listbox {
  padding: 0 10px;
  height: 68vh;
  overflow-y: scroll;
  & > ul {
    width: 100%;
    & > li {
      width: 100%;
      list-style: none;
      padding: 19px 0;
      border-bottom: dashed 1px #ccc;
      cursor: pointer;
      .form-titlV {
        font-size: 14px;
        position: relative;
        padding: 3px 20px;
        .context {
          width: 100%;
          display: flex;
          .leftcon {
            width: 70%;
          }
          .rightcon {
            width: 30%;
            text-align: right;
          }
        }
        // &::before {
        //   content: '';
        //   left: 0px;
        //   position: absolute;
        //   width: 8px;
        //   height: 8px;
        //   border-radius: 100%;
        //   top: 7px;
        //   bottom: 11px;
        //   background: red;
        // }
      }
      .form-title {
        &::before {
          content: '';
          left: 0px;
          position: absolute;
          width: 8px;
          height: 8px;
          border-radius: 100%;
          top: 7px;
          bottom: 11px;
          background: red;
        }
      }
    }
  }
}
.s-curd {
  position: relative;
  background: #fff;
  padding: 5px 15px 15px;
  height: 100vh;
}

// ::v-deep .el-table .cell {
//   display: flex;
//   justify-content: center;
// }
::v-deep .el-dialog__body {
  height: 65vh;
}
.tags {
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    margin: 0 3px;
  }
  .tag {
    width: 30px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    // margin: 0 auto;
    // padding: 10px;
    // line-height: 0px;
  }
}
</style>
<style scoped>
ul {
  margin-block-start: 0em;
  margin-block-end: 0em;
  padding-inline-start: 0px;
}
</style>