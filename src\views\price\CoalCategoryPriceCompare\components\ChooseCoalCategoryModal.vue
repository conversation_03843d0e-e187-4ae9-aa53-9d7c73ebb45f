<template>
  <div class="container">
    <SnModal v-model="_value" :cancel="cancel" :fullscreen.sync="fullscreen"
             :ok="ok" :submitText="'添加到价格对比'" :title="title"
             class="modal" v-bind="$attrs" width="70%">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="外部煤源" name="first">
          <SnProTable v-if="activeName==='first'" :ref="pageRefY" :actions="actions"
                      :afterFetch="afterFetch"
                      :beforeFetch="beforeFetch" :isSelectionChange="true" :model="model"
                      @selectlist="getselectlist"
                      @search-query="$emit('refresh')">
            <template v-for="item in getColSlots" #[item.prop]="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span v-if="!row.isEdit">{{ row[col.prop] }} </span>
                  <el-input v-else v-model="row[`${col.prop}${editKey}`]" :type="item.type"
                            class="noPaddingInput"
                            @input="handleInputChange(row, col, ...arguments)"></el-input>
                </template>
              </el-table-column>
            </template>

            <template #date="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span v-if="!row.isEdit">{{ getDate(row[col.prop]) }}</span>
                  <SnDateSelect v-else="row.isEdit" v-model="row[`${col.prop}${editKey}`]"
                                :clearable="false"
                                class="formatDate"/>
                </template>
              </el-table-column>
            </template>

            <template #activePriceCoalPriceNoTax="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span>{{
                      getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 0, 'ROUND_HALF_UP')
                    }} </span>
                </template>
              </el-table-column>
            </template>

            <template #activePriceTransportCostP1="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span>{{
                      getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                    }} </span>
                </template>
              </el-table-column>
            </template>

            <template #activePriceFactoryPriceNoTax="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span>{{
                      getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 1, 'ROUND_HALF_UP')
                    }} </span>
                </template>
              </el-table-column>
            </template>

            <template #activePriceFactorDryBasisCost="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span>{{
                      getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 0, 'ROUND_HALF_UP')
                    }} </span>
                </template>
              </el-table-column>
            </template>

            <template #cokeIndexMjb="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span>{{
                      getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                    }} </span>
                </template>
              </el-table-column>
            </template>

            <template #cokeIndexAd="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span>{{
                      getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                    }} </span>
                </template>
              </el-table-column>
            </template>
          </SnProTable>
        </el-tab-pane>

        <el-tab-pane label="内部煤源" name="second">
          <SnProTable v-if="activeName==='second'" :ref="pageRefV" :actions="actions"
                      :afterFetch="afterFetchV"
                      :beforeFetch="beforeFetchV" :isSelectionChange="true"
                      :model="modelV" @selectlist="getselectlist">
            <template v-for="item in getColSlots" #[item.prop]="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span v-if="!row.isEdit">{{ row[col.prop] }} </span>
                  <el-input v-else="row.isEdit" v-model="row[`${col.prop}${editKey}`]"
                            :type="item.type" class="noPaddingInput"
                            @input="handleInputChange(row, col, ...arguments)"></el-input>
                </template>
              </el-table-column>
            </template>
            <template #activePriceCoalPriceNoTax="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span>{{
                      getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 0, 'ROUND_HALF_UP')
                    }} </span>
                </template>
              </el-table-column>
            </template>

            <template #activePriceTransportCostP1="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span>{{
                      getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                    }} </span>
                </template>
              </el-table-column>
            </template>

            <template #activePriceFactoryPriceNoTax="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span>{{
                      getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 1, 'ROUND_HALF_UP')
                    }} </span>
                </template>
              </el-table-column>
            </template>

            <template #activePriceFactorDryBasisCost="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span>{{
                      getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 0, 'ROUND_HALF_UP')
                    }} </span>
                </template>
              </el-table-column>
            </template>

            <template #cokeIndexMjb="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span>{{
                      getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                    }} </span>
                </template>
              </el-table-column>
            </template>

            <template #cokeIndexAd="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <span>{{
                      getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                    }} </span>
                </template>
              </el-table-column>
            </template>

            <template #date="{ col }">
              <el-table-column v-bind="col">
                <template #default="{row}">
                  <div style="position: relative;overflow: hidden;">
                    <span>{{ getDate(row[col.prop]) }}</span>
                    <div v-if="row.manualUpdated" class="tag-left-right">
                      <span class="triangle-text">手工</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </template>
          </SnProTable>
        </el-tab-pane>
      </el-tabs>
    </SnModal>
  </div>
</template>

<script>
import model from './Outside/model'
import modelV from './Inside/model'
import TipModal from '@/utils/modal'
import {createPermissionMap} from '@/utils'
import {getDate} from '@/utils/dateUtils'
import CalcUtils from '@/utils/calcUtils'
import SnProTable from '@/components/Common/SnProTable/index.vue'
import SnDateSelect from '@/components/Common/SnDateSelect/index.vue'

export default {
  name: 'Outside',
  inheritAttrs: false,
  components: {SnDateSelect, SnProTable},
  data() {
    return {
      activeName: 'first',
      pageRefY: 'page',
      pageRefV: 'page',
      model,
      modelV,
      permissions: createPermissionMap('outside'),
      fullscreen: true,
      editPropList: [
        {
          label: '更新日期',
          prop: 'date'
        },
        {
          label: '煤种名称',
          prop: 'coalCategoryName'
        },
        {
          label: '地区',
          prop: 'area'
        },
        {
          label: '品名简称',
          prop: 'coalShortname'
        },
        {
          label: '灰分Ad%',
          prop: 'cleanAd',
          type: 'number'
        },
        {
          label: '挥发分Vdaf%',
          prop: 'cleanVdaf',
          type: 'number'
        },
        {
          label: '硫分St,d%',
          prop: 'cleanStd',
          type: 'number'
        },
        {
          label: '粘结G',
          prop: 'procG',
          type: 'number'
        },
        {
          label: 'Y/mm',
          prop: 'procY',
          type: 'number'
        },
        {
          label: '含税煤价',
          prop: 'activePriceCoalPriceWithTax',
          type: 'number'
        },

        {
          label: '不含税运费',
          prop: 'activePriceTransportPriceNoTax',
          type: 'number'
        },

        {
          label: '水分%',
          prop: 'cleanMt',
          type: 'number'
        },
        {
          label: '供应情况',
          prop: 'activePriceSupplyInfo'
        },

        {
          label: 'MF/ddpm',
          prop: 'coalIndexMfddpm'
        },
        {
          label: '奥亚膨胀b%',
          prop: 'coalIndexAypz'
        },
        {
          label: '奥亚收缩a%',
          prop: 'coalIndexAyss'
        },

        {
          label: '反射率R0Max',
          prop: 'coalIndexFslRMax'
        },

        {
          label: '方差',
          prop: 'coalIndexFc'
        },

        {
          label: 'MCI%',
          prop: 'coalIndexMci',
          type: 'number'
        },

        {
          label: '硫分',
          prop: 'cokeIndexStd',
          type: 'number'
        },
        {
          label: 'CRI%',
          prop: 'cokeIndexCri',
          type: 'number'
        },
        {
          label: 'CSR%',
          prop: 'cokeIndexCsr',
          type: 'number'
        },
        {
          label: 'M40',
          prop: 'cokeIndexM40',
          type: 'number'
        },
        {
          label: 'M10',
          prop: 'cokeIndexM10',
          type: 'number'
        },

        {
          label: '回收率%',
          prop: 'recoveryRate'
        },

        {
          label: '分类',
          prop: 'categoryType'
        },

        {
          label: '内部煤种',
          prop: 'insideCoalCategory'
        },
        {
          label: '涨跌',
          prop: 'rise'
        }
      ],
      calculateList: [
        {
          label: '不含税煤价',
          prop: 'activePriceCoalPriceNoTax'
        },
        {
          label: '路耗1%',
          prop: 'activePriceTransportCostP1'
        },
        {
          label: '不含税进厂价',
          prop: 'activePriceFactoryPriceNoTax'
        },
        {
          label: '进厂干基成本',
          prop: 'activePriceFactorDryBasisCost'
        },
        {
          label: '煤焦比',
          prop: 'cokeIndexMjb'
        },
        {
          label: '灰分',
          prop: 'cokeIndexAd'
        }
      ],

      editPropListV: [
        {
          label: '灰分Ad%',
          prop: 'cleanAd',
          type: 'number'
        },
        {
          label: '挥发分Vdaf%',
          prop: 'cleanVdaf',
          type: 'number'
        },
        {
          label: '硫分St,d%',
          prop: 'cleanStd',
          type: 'number'
        },
        {
          label: '粘结G',
          prop: 'procG',
          type: 'number'
        },
        {
          label: 'Y/mm',
          prop: 'procY',
          type: 'number'
        },
        {
          label: '含税煤价',
          prop: 'activePriceCoalPriceWithTax',
          type: 'number'
        },

        {
          label: '不含税运费',
          prop: 'activePriceTransportPriceNoTax',
          type: 'number'
        },

        {
          label: '水分%',
          prop: 'cleanMt',
          type: 'number'
        },
        {
          label: '供应情况',
          prop: 'activePriceSupplyInfo'
        },

        {
          label: 'MF/ddpm',
          prop: 'coalIndexMfddpm'
        },
        {
          label: '奥亚膨胀b%',
          prop: 'coalIndexAypz'
        },
        {
          label: '奥亚收缩a%',
          prop: 'coalIndexAyss'
        },

        {
          label: '反射率R0Max',
          prop: 'coalIndexFslRMax'
        },

        {
          label: '方差',
          prop: 'coalIndexFc'
        },

        {
          label: 'MCI%',
          prop: 'coalIndexMci',
          type: 'number'
        },

        {
          label: '硫分',
          prop: 'cokeIndexStd',
          type: 'number'
        },
        {
          label: 'CRI%',
          prop: 'cokeIndexCri',
          type: 'number'
        },
        {
          label: 'CSR%',
          prop: 'cokeIndexCsr',
          type: 'number'
        },
        {
          label: 'M40',
          prop: 'cokeIndexM40',
          type: 'number'
        },
        {
          label: 'M10',
          prop: 'cokeIndexM10',
          type: 'number'
        },

        {
          label: '备注',
          prop: 'remarks'
        }
      ],
      calculateListV: [
        {
          label: '不含税煤价',
          prop: 'activePriceCoalPriceNoTax'
        },
        {
          label: '路耗1%',
          prop: 'activePriceTransportCostP1'
        },
        {
          label: '不含税进厂价',
          prop: 'activePriceFactoryPriceNoTax'
        },
        {
          label: '进厂干基成本',
          prop: 'activePriceFactorDryBasisCost'
        },
        {
          label: '煤焦比',
          prop: 'cokeIndexMjb'
        },
        {
          label: '灰分',
          prop: 'cokeIndexAd'
        }
      ],
      slectlist: []
    }
  },
  computed: {
    _value: {
      set(val) {
        this.$emit('input', val)
      },
      get() {
        return this.value
      }
    },
    title() {
      return '选择煤源'
    },
    actions() {
      return []
    },
    getColSlots() {
      const exclude = ['date']
      if (this.activeName === 'second') {
        return this.editPropListV.filter((item) => !exclude.includes(item.prop))
      } else {
        return this.editPropList.filter((item) => !exclude.includes(item.prop))
      }
    },
    getEditList() {
      if (this.activeName === 'second') {
        return [...this.editPropListV, ...this.calculateListV]
      } else {
        return [...this.editPropList, ...this.calculateList]
      }
    }
  },
  props: {
    optName: {
      type: String,
      require: true
    },
    value: {
      type: Boolean,
      require: true
    },
    record: {
      type: Object,
      default() {
        return {}
      }
    },
    activekey: {
      type: String
    }
  },
  watch: {
    record: {
      async handler(value) {
        await this.$nextTick()
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    // handleSelectionChange(list) {
    //   this.slectlist = []
    //   console.log('777')
    //   console.log(list)
    //   if (list.length > 1) {
    //     this.$refs['page'].clearSelection()
    //     this.$refs['page'].toggleRowSelection(list)
    //     TipModal.msgWarning('只能选择一条数据')
    //   }
    //   this.slectlist = list[list.length - 1] ? [list[list.length - 1]] : []
    //   // this.$refs['page'].getList()
    // },

    // handleSelectionChange(data) {
    //   console.log('777')
    //   console.log(data)
    //   if (data.length > 1) {
    //     TipModal.msgWarning('只能选择一条数据')
    //   } else {
    //     console.log(this.$refs[this.pageRefY])
    //     console.log(this.$refs[this.pageRefV])
    //     //  this.$refs[this.pageRefV].getSelectRows()
    //     this.slectlist = data
    //   }
    // },
    // getselectFN() {
    // console.log('666')
    // this.getselectlist()
    // console.log(this.$refs['page'].selectlist)
    // },
    getselectlist(selectlist) {
      console.log(selectlist)
      this.slectlist = selectlist
    },
    // getselectFNV() {
    // console.log('555')
    // this.getselectlist()
    // console.log(this.$refs['page'].selectlist)
    // },

    async cancel() {
      return false
    },
    async ok() {
      console.log(this.slectlist)
      if (this.slectlist.length < 1) {
        TipModal.msgWarning('请选择提条数据!')
        return
      }
      let dara = {
        selectCoalType: this.activeName,
        slectlist: this.slectlist
      }
      this.$emit('setsavedata', dara)
      return this.cancel()
    },

    handleClick(tab, event) {
      console.log(this.$refs['page'])
      // this.$refs['page'].getList()
      this.$refs['page'].clearSelect()
      this.slectlist = []
    },
    beforeFetch(params) {
      return {
        ...params
      }
    },
    beforeFetchV(params) {
      return {
        ...params
      }
    },
    getShowValue: CalcUtils.getShowValue,
    handleInputChange(row, col, value) {
      const v = row
      const getEditVal = (key) => {
        const value = v[`${key}${this.editKey}`]
        return [undefined, null, ''].includes(value) ? undefined : Number(value)
      }
      const set = (key, value) => {
        v[`${key}${this.editKey}`] = value
      }
      // 不含税煤价
      set('activePriceCoalPriceNoTax', CalcUtils.calculateCoalPriceWithoutTax(getEditVal('activePriceCoalPriceWithTax')))
      // 路耗
      set('activePriceTransportCostP1', CalcUtils.calculateRoadLoss(getEditVal('activePriceCoalPriceNoTax')))
      // 不含税进厂价
      set(
        'activePriceFactoryPriceNoTax',
        CalcUtils.calculateFactoryPriceWithoutTax(
          getEditVal('activePriceCoalPriceNoTax'),
          getEditVal('activePriceTransportPriceNoTax'),
          getEditVal('activePriceTransportCostP1')
        )
      )
      // 进厂干基成本
      set(
        'activePriceFactorDryBasisCost',
        CalcUtils.calculateDryBasisCost(getEditVal('activePriceFactoryPriceNoTax'), getEditVal('cleanMt'))
      )
      // 煤焦比
      set('cokeIndexMjb', CalcUtils.calcCokeToCoalRatio(getEditVal('cleanVdaf'), getEditVal('cleanAd')))
      set('cokeIndexAd', CalcUtils.calculateAshContent(getEditVal('cleanAd'), getEditVal('cokeIndexMjb')))
    },
    getDate,
    afterFetch(data) {
      // eslint-disable-next-line no-unused-expressions
      data?.records?.forEach((item) => {
        item.loading = false
        item.isEdit = false
        this.getEditList.forEach((value) => {
          item[`${value.prop}${this.editKey}`] = item[value.prop]
        })
      })
      return data
    },
    afterFetchV(data) {
      // eslint-disable-next-line no-unused-expressions
      data?.records?.forEach((item) => {
        item.loading = false
        item.isEdit = false
        this.getEditList.forEach((value) => {
          item[`${value.prop}${this.editKey}`] = item[value.prop]
        })
      })
      return data
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {

  .el-dialog__body {
    padding: 10px 20px !important;
  }

  .el-tabs__nav-scroll {
    margin-left: 8px;
    display: flex;
  }

  .el-tabs__nav-wrap::after {
    display: none;
  }

  .search-form-wrapper,
  .common--page-content {
    padding: 0 !important;
  }
}
</style>
