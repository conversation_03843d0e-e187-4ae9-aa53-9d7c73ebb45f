<template>
  <div class="container">
    <SnModal v-model="_value" :showFooter="false" :fullscreen.sync="fullscreen" :ok="ok" :title="title" class="modal"
             v-bind="$attrs" width="70%">
      <!-- <SnProTable :ref="pageRef" :actions="actions" :beforeFetch="beforeFetch" :model="model">
      </SnProTable> -->
      <el-table :data="historytableData" stripe style="width: 100%" height="500px" :header-cell-style="getHeaderCellStyle">
        <el-table-column prop="alldata" label="对比记录" align="center">
        </el-table-column>
        <el-table-column prop="createDate" label="操作日期" align="center">
        </el-table-column>
      </el-table>

    </SnModal>
  </div>
</template>
<script>
import model from './Historym/model'
import projectStyles from '@/styles/project.scss'
import { formatToDateTime, getDate } from '@/utils/dateUtils'
export default {
  name: 'Historym',
  //   inheritAttrs: false,
  components: {},
  data() {
    return {
      pageRef: 'page',
      model,
      fullscreen: false
    }
  },
  computed: {
    actions() {
      return []
    },
    _value: {
      set(val) {
        this.$emit('input', val)
      },
      get() {
        return this.value
      }
    },
    title() {
      return '历史记录'
    }
  },
  props: {
    optName: {
      type: String,
      require: true
    },
    historytableData: {
      type: Array,
      default: []
    },
    value: {
      type: Boolean,
      require: true
    },
    record: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    record: {
      async handler(value) {
        await this.$nextTick()
      },
      immediate: true
    }
  },
  methods: {
    beforeFetch(params) {
      return {
        ...params,
        beginDate: formatToDateTime(params._dateRange, 'YYYY-MM-DD 00:00:00'),
        endDate: formatToDateTime(params._dateRange, 'YYYY-MM-DD 23:59:59'),
        coalCategoryName: this.currentCoalCategory[this.coalCategoryProps.label],
        type: 'outside'
      }
    },
    getHeaderCellStyle() {
      return {
        height: '42px',
        background: projectStyles.projectTheme,
        color: '#fff',
        padding: '0',
        'font-size:': '0.875rem'
      }
    },
    async cancel() {
      return false
    },
    async ok() {
      return this.cancel()
    }
  }
}
</script>
