// eslint-disable-next-line
import BaseModel, { TableConfig, FormConfig } from '@/components/Common/SnProTable/model'
import { getDate } from '@/utils/dateUtils'

// export const name = `/a/coalSourceInside`

/**
 * 表单配置
 * @returns {FormConfig}
 */
const createFormConfig = () => {
  return {
    fieldMapToTime: [['_dateRange', ['beginDate', 'endDate'], 'YYYY-MM-DD HH:mm:ss']],
    filters: [
      // {
      //   label: '品名简称',
      //   prop: 'coalShortname'
      // }
    ]
  }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
  return {
    showOpt: false, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: false, // 显示选择列
    mountedQuery: false, // 自动查询,
    optConfig: {
      width: 140,
      label: '操作',
      prop: 'opt',
      fixed: 'right'
    },
    columns: [
      {
        prop: 'date',
        label: '操作日期',
        slot: 'date',
        width: 100
      },
      {
        label: '对比记录',
        prop: 'insideCoalCategory',
        width: 100,
        showOverflowTooltip: true
      },

      {
        label: '煤炭指标',
        children: [
          {
            label: '灰分Ad%',
            prop: 'cleanAd',
            slot: 'cleanAd',
            width: 60
          },
          {
            label: '硫分St,d%',
            prop: 'cleanStd',
            slot: 'cleanStd',
            width: 65
          },
          {
            label: '挥发分Vdaf%',
            prop: 'cleanVdaf',
            slot: 'cleanVdaf',
            width: 80
          },
          {
            label: '粘结G',
            prop: 'procG',
            slot: 'procG',
            width: 50
          },
          {
            label: 'Y/mm',
            prop: 'procY',
            slot: 'procY',
            width: 55
          }
        ]
      }
    ]
  }
}

class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }

  // page(query) {
  //   return super.request({
  //     url: `${name}/page-by-coal-category`,
  //     method: 'get',
  //     params: query
  //   })
  // }

  page(query) {
    return super.request({
      url: '/a/coalNamePriceCompare/compare-history',
      method: 'get',
      params: query
    })
  }
}

export default new Model()
