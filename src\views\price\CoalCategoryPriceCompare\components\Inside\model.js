// eslint-disable-next-line
import BaseModel, { TableConfig, FormConfig } from '@/components/Common/SnProTable/model'
import { getDate } from '@/utils/dateUtils'

export const name = `/a/coalSourceInside`

/**
 * 表单配置
 * @returns {FormConfig}
 */
const createFormConfig = () => {
  return {
    fieldMapToTime: [['_dateRange', ['beginDate', 'endDate'], 'YYYY-MM-DD HH:mm:ss']],
    filters: [
      // {
      //   label: 'date',
      //   prop: '_dateRange',
      //   component: 'SnDateRanger',
      //   defaultValue: [],
      //   componentProp: {
      //     startPlaceholder: '更新开始',
      //     endPlaceholder: '更新结束'
      //   },
      //   style: {
      //     width: '230px'
      //   }
      // },
      // {
      //   label: '煤种名称',
      //   prop: 'coalCategoryName'
      // },
      {
        label: '品名简称',
        prop: 'coalShortname'
      }
    ]
  }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
  return {
    showOpt: false, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: true, // 显示选择列
    mountedQuery: true, // 自动查询,
    optConfig: {
      width: 140,
      label: '操作',
      prop: 'opt',
      fixed: 'right'
    },
    columns: [
      {
        prop: 'date',
        label: '更新日期',
        slot: 'date',
        width: 100
      },
      {
        label: '煤种名称',
        prop: 'insideCoalCategory',
        width: 100,
        showOverflowTooltip: true
      },
      {
        label: '品名简称',
        prop: 'coalShortname',
        width: 90
      },
      {
        label: '库存量',
        prop: 'stockNum',
        width: 70
      },
      {
        label: '煤炭指标',
        children: [
          {
            label: '灰分Ad%',
            prop: 'cleanAd',
            slot: 'cleanAd',
            width: 60
          },
          {
            label: '硫分St,d%',
            prop: 'cleanStd',
            slot: 'cleanStd',
            width: 65
          },
          {
            label: '挥发分Vdaf%',
            prop: 'cleanVdaf',
            slot: 'cleanVdaf',
            width: 80
          },
          {
            label: '粘结G',
            prop: 'procG',
            slot: 'procG',
            width: 50
          },
          {
            label: 'Y/mm',
            prop: 'procY',
            slot: 'procY',
            width: 55
          }
        ]
      },
      {
        label: '现行价',
        children: [
          {
            label: '含税煤价',
            prop: 'activePriceCoalPriceWithTax',
            slot: 'activePriceCoalPriceWithTax',
            width: 65
          },
          {
            label: '不含税煤价',
            prop: 'activePriceCoalPriceNoTax',
            width: 65,
            slot: 'activePriceCoalPriceNoTax'
          },
          {
            label: '不含税运费',
            prop: 'activePriceTransportPriceNoTax',
            slot: 'activePriceTransportPriceNoTax',
            width: 65
          },
          {
            label: '路耗1%',
            prop: 'activePriceTransportCostP1',
            slot: 'activePriceTransportCostP1',
            width: 55
          },
          {
            label: '不含税进厂价',
            prop: 'activePriceFactoryPriceNoTax',
            slot: 'activePriceFactoryPriceNoTax',
            width: 80
          },
          {
            label: '水分%',
            prop: 'cleanMt',
            slot: 'cleanMt',
            width: 50
          },
          {
            label: '进厂干基成本',
            prop: 'activePriceFactorDryBasisCost',
            slot: 'activePriceFactorDryBasisCost',
            width: 80
          },

          {
            label: '供应情况',
            prop: 'activePriceSupplyInfo',
            slot: 'activePriceSupplyInfo',
            width: 75
          }
        ]
      },
      {
        label: '库存价',
        prop: 'stockPrice',
        width: 60
      },

      {
        label: '煤炭指标',
        children: [
          {
            label: 'MF/ddpm',
            prop: 'coalIndexMfddpm',
            slot: 'coalIndexMfddpm',
            width: 75
          },
          {
            label: '奥亚膨胀b%',
            prop: 'coalIndexAypz',
            slot: 'coalIndexAypz',
            width: 75
          },
          {
            label: '奥亚收缩a%',
            prop: 'coalIndexAyss',
            slot: 'coalIndexAyss',
            width: 75
          },

          {
            label: '反射率R0Max',
            prop: 'coalIndexFslRMax',
            slot: 'coalIndexFslRMax',
            width: 85
          },

          {
            label: '方差',
            prop: 'coalIndexFc',
            slot: 'coalIndexFc',
            width: 50
          },

          {
            label: 'MCI%',
            prop: 'coalIndexMci',
            slot: 'coalIndexMci',
            width: 55
          }
        ]
      },

      {
        label: '焦炭指标',
        children: [
          {
            label: '煤焦比',
            prop: 'cokeIndexMjb',
            slot: 'cokeIndexMjb',
            width: 50
          },
          {
            label: '灰分',
            prop: 'cokeIndexAd',
            slot: 'cokeIndexAd',
            width: 50
          },
          {
            label: '硫分',
            prop: 'cokeIndexStd',
            slot: 'cokeIndexStd',
            width: 50
          },
          {
            label: 'CRI%',
            prop: 'cokeIndexCri',
            slot: 'cokeIndexCri',
            width: 50
          },
          {
            label: 'CSR%',
            prop: 'cokeIndexCsr',
            slot: 'cokeIndexCsr',
            width: 50
          },
          {
            label: 'M40',
            prop: 'cokeIndexM40',
            slot: 'cokeIndexM40',
            width: 50
          },
          {
            label: 'M10',
            prop: 'cokeIndexM10',
            slot: 'cokeIndexM10',
            width: 50
          }
        ]
      },

      {
        label: '备注',
        prop: 'remarks',
        slot: 'remarks',
        minWidth: 100
      },
      {
        label: '分析煤种名称',
        prop: 'coalCategoryName',
        minWidth: 120
      }
    ]
  }
}

class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }

  page(query) {
    return super.request({
      url: `${name}/page-by-coal-category`,
      method: 'get',
      params: query
    })
  }

  // save(data, optName = 'create') {
  //   return super.request({
  //     url: `${name}/batch-save`,
  //     method: 'post',
  //     data,
  //     headers: {
  //       'Content-Type': 'application/json;charset=UTF-8'
  //     }
  //   })
  // }

  // sortTop({ id, sort }) {
  //   return super.request({
  //     // sort如果大于0则取消置顶
  //     url: `${name}/${sort ? 'un-sort-top' : 'sort-top'}`,
  //     method: 'get',
  //     params: { id }
  //   })
  // }

  // get(id) {
  //   return super.request({
  //     url: `${name}/get`,
  //     method: 'get',
  //     params: { id }
  //   })
  // }
}

export default new Model()
