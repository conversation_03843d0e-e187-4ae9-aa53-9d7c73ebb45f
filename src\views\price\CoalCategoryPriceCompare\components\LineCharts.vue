<template>
  <BarMulti :chartData="getChartData" :option="option" type="line" v-bind="$attrs"/>
</template>

<script>
import BarMulti from '@/components/Common/Charts/BarMulti'
import {applyOpacityToColor} from '@/utils/func'

const indexNameMap = new Map()
indexNameMap.set('ad', {
  color: ['#FF726B', '#1BB681']
})
indexNameMap.set('std', {
  color: ['#FF726B', '#0559A8']
})
indexNameMap.set('g', {
  color: ['#FF9639', '#1BB681']
})
indexNameMap.set('csr', {
  color: ['#FF9639', '#0559A8']
})

export default {
  name: 'LineCharts',
  components: {
    BarMulti
  },
  data() {
    return {}
  },
  props: {
    indexName: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    chartData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    getCurrentColor() {
      return indexNameMap.get(this.indexName).color || []
    },
    getChartData() {
      let newArr = this.chartData.reverse()
      return newArr.map((item) => {
        return {
          ...item,
          // color,
          itemStyle: {
            borderWidth: 2 // 设置拐点边框大小
            // shadowBlur: 0, // 设置阴影模糊度
            // shadowColor: applyOpacityToColor(item.color, 0.2),
            // borderColor: applyOpacityToColor(item.color, 0.2)
          }
        }
      })
    },
    option() {
      return {
        seriesFunc: (obj, opt) => {
          const color = obj.data.find((v) => v.color)?.color
          return {
            type: 'line',
            symbol: 'circle',
            emphasis: {
              focus: 'series' // 高亮显示
            },
            symbolSize: 7,
            smooth: false // 曲线
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line'
          },
          textStyle: {
            color: '#fafafa'
          },
          borderColor: 'transparent',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          extraCssText: 'backdrop-filter: blur(6px);'
        },
        title: {
          text: this.title, // 标题文本
          left: 'center', // 标题水平居中
          textStyle: {
            color: '#3D3D3D', // 标题文字颜色
            fontSize: 14, // 标题文字大小
            fontWeight: '400' // 标题文字粗细
          }
        },
        grid: {
          left: 40,
          right: 40,
          bottom: 40,
          top: 50,
          containLabel: true
        },
        color: this.getCurrentColor,
        xAxis: {
          boundaryGap: false // 设置坐标轴两边是否留白
        },
        yAxis: {
          min: (val) => {
            return val.min - 100
          },
          max: (val) => {
            return val.max + 100
          },
          interval: 100,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid',
              color: '#E3F0FF'
            }
          }
        }
      }
    }
  },
  methods: {}
}
</script>
<style lang="scss" scoped>

</style>
