<template>
  <div class="app-container">
    <div class="section">
      <div class="card">
        <div class="card-header">
          <div class="title">对比煤种</div>
          <div class="action">
            <div class="flex-align-items">
              <SnDateRanger
                :end.sync="query.endDate"
                :start.sync="query.beginDate"
                :useValueField="false"
                style="width: 230px;margin:0 1.25rem;"
                value-format="yyyy-MM-dd"
              />
              <el-button :loading="searchLoading" class="search--button" type="search"
                         @click="handleSearch" v-text="'查询'"/>
            </div>
          </div>
          <div>
            <div class="flex-align-items" style="justify-content:flex-end">
              <el-button style="background:#ff8b40;border:solid 1px #ff8b40;color:#fff"
                         @click="lookHistory">历史记录
              </el-button>
            </div>
          </div>
        </div>
        <div class="vs-card" style="height:8rem">
          <div class="vs-card-item">
            <img class="vs" src="./img/orangeVs.png">
            <img class="arrow" src="./img/orangeArrow.png">
            <div class="round-tip round-tip-orange">灰</div>
            <div class="ctx-wrap">
              <div class="title">{{ cleanAdCoalName1 }}</div>
              <div class="toggle-btn" @click="handleToggleCoalCategory('cleanAdCoalCode1')">
                <span>切换煤源</span>
                <i class="el-icon-arrow-down"></i>
              </div>
            </div>
            <div class="ctx-wrap">
              <div class="title">{{ cleanAdCoalName2 }}</div>
              <div class="toggle-btn" @click="handleToggleCoalCategory('cleanAdCoalCode2')">
                <span>切换煤源</span>
                <i class="el-icon-arrow-down"></i>
              </div>
            </div>
          </div>

          <div class="vs-card-item">
            <img class="vs" src="./img/blueVs.png">
            <img class="arrow" src="./img/blurArrow.png">
            <div class="round-tip round-tip-blue">硫</div>
            <div class="ctx-wrap">
              <div class="title">{{ cleanStdCoalName1 }}</div>
              <div class="toggle-btn" @click="handleToggleCoalCategory('cleanStdCoalCode1')">
                <span>切换煤源</span>
                <!-- <span v-else>{{cleanStdCoalName1}}</span> -->
                <i class="el-icon-arrow-down"></i>
              </div>
            </div>
            <div class="ctx-wrap">
              <div class="title">{{ cleanStdCoalName2 }}</div>
              <div class="toggle-btn" @click="handleToggleCoalCategory('cleanStdCoalCode2')">
                <span>切换煤源</span>
                <!-- <span v-else>{{cleanStdCoalName2}}</span> -->
                <i class="el-icon-arrow-down"></i>
              </div>
            </div>
          </div>

          <div class="vs-card-item">
            <img class="vs" src="./img/orangeVs.png">
            <img class="arrow" src="./img/orangeArrow.png">
            <div class="round-tip round-tip-orange">粘结</div>
            <div class="ctx-wrap">
              <div class="title">{{ procGCoalName1 }}</div>
              <div class="toggle-btn" @click="handleToggleCoalCategory('procGCoalCode1')">
                <span>切换煤源</span>
                <!-- <span v-else>{{procGCoalName1}}</span> -->
                <i class="el-icon-arrow-down"></i>
              </div>
            </div>
            <div class="ctx-wrap">
              <div class="title">{{ procGCoalName2 }}</div>
              <div class="toggle-btn" @click="handleToggleCoalCategory('procGCoalCode2')">
                <span>切换煤源</span>
                <!-- <span v-else>{{procGCoalName2}}</span> -->
                <i class="el-icon-arrow-down"></i>
              </div>
            </div>
          </div>

          <div class="vs-card-item">
            <img class="vs" src="./img/blueVs.png">
            <img class="arrow" src="./img/blurArrow.png">
            <div class="round-tip round-tip-blue">CSR</div>
            <div class="ctx-wrap">
              <div class="title">{{ csrCoalName1 }}</div>
              <div class="toggle-btn" @click="handleToggleCoalCategory('csrCoalCode1')">
                <span>切换煤源</span>
                <i class="el-icon-arrow-down"></i>
              </div>
            </div>
            <div class="ctx-wrap">
              <div class="title">{{ csrCoalName2 }}</div>
              <div class="toggle-btn" @click="handleToggleCoalCategory('csrCoalCode2')">
                <span>切换煤源</span>
                <i class="el-icon-arrow-down"></i>
              </div>
            </div>
          </div>

        </div>
        <div/>
        <div class="card">
          <div class="card-header">
            <div class="title">价格对比</div>
          </div>
          <div class="vs-table">
            <el-table
              :cell-style="changeCellStyle" :data="tableData"
              :header-cell-style="tableRowStyleName"
              :row-style="{height:'2rem'}"
              height="27rem"
              stripe
              style="width: 100%;font-size:1rem">
              <el-table-column :label="dateLabel" align="center" fixed="left" prop="date"
                               width="150"/>
              <el-table-column align="center" prop="cleanAdPrice1">
                <template #header>
                  <div class="title">{{ labels.cleanAdCoalShortname1 }}</div>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="cleanAdPrice2">
                <template #header>
                  <div class="title">{{ labels.cleanAdCoalShortname2 }}</div>
                </template>
              </el-table-column>
              <el-table-column :label="differenceLabel" align="center" prop="cleanAdPriceDiff"
                               width="80">
              </el-table-column>
              <el-table-column align="center" prop="cleanStdPrice1">
                <template #header>
                  <div class="title">{{ labels.cleanStdCoalShortname1 }}</div>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="cleanStdPrice2">
                <template #header>
                  <div class="title">{{ labels.cleanStdCoalShortname2 }}</div>
                </template>
              </el-table-column>
              <el-table-column :label="differenceLabel" align="center" prop="cleanStdPriceDiff"
                               width="80">
              </el-table-column>
              <el-table-column align="center" prop="procGPrice1">
                <template #header>
                  <div class="title">{{ labels.procGCoalShortname1 }}</div>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="procGPrice2">
                <template #header>
                  <div class="title">{{ labels.procGCoalShortname2 }}</div>
                </template>
              </el-table-column>
              <el-table-column :label="differenceLabel" align="center" prop="procGPriceDiff"
                               width="80">
              </el-table-column>
              <el-table-column align="center" prop="csrPrice1">
                <template #header>
                  <div class="title">{{ labels.csrCoalShortname1 }}</div>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="csrPrice2">
                <template #header>
                  <div class="title">{{ labels.csrCoalShortname2 }}</div>
                </template>
              </el-table-column>
              <el-table-column :label="differenceLabel" align="center" prop="csrPriceDiff"
                               width="80">
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <div class="card">
        <div class="card-header">
          <div class="title">价格趋势</div>
        </div>
        <div class="vs-charts-wrap">
          <div class="vs-chart-item">
            <LineCharts :chartData="getChartsAdList" indexName="ad"
                        title="灰分差值价格参考图(元/吨)"/>
          </div>
          <div class="vs-chart-item">
            <LineCharts :chartData="getChartsStdList" indexName="std"
                        title="硫分差值价格参考图(元/吨)"/>
          </div>
          <div class="vs-chart-item">
            <LineCharts :chartData="getChartsGList" indexName="g"
                        title="粘结差值价格参考图(元/吨)"/>
          </div>
          <div class="vs-chart-item">
            <LineCharts :chartData="getChartsCsrList" indexName="csr"
                        title="热强度差值价格参考图(元/吨)"/>
          </div>
        </div>
      </div>
    </div>
    <ChooseCoalCategoryModal
      v-if="chooseCoalCategoryInfo.visitable"
      v-model="chooseCoalCategoryInfo.visitable"
      :activekey="chooseCoalCategoryInfo.activekey"
      :record="chooseCoalCategoryInfo.record"
      @setsavedata="getValue"/>

    <HistoryModal
      v-if="historyModal.visitable"
      v-model="historyModal.visitable"
      :historytableData="historytableData">
    </HistoryModal>
  </div>
</template>

<script>
import * as Api from './api'
import {FetchData} from '@/utils'
import LineCharts from './components/LineCharts.vue'
import ChooseCoalCategoryModal from './components/ChooseCoalCategoryModal.vue'
import FormModal from '@/views/price/MineralAdjustMarketPrice/components/FormModal.vue'
import HistoryModal from './components/HistoryModal.vue'

export default {
  name: 'CoalCategoryPriceCompare',
  components: {FormModal, LineCharts, ChooseCoalCategoryModal, HistoryModal},
  data() {
    return {
      pageRef: 'page',
      permissions: {
        save: '*',
        remove: '*'
      },
      cleanAdCoalCode1: '',
      cleanAdCoalCode2: '',
      cleanStdCoalCode1: '',
      cleanStdCoalCode2: '',
      procGCoalCode1: '',
      procGCoalCode2: '',
      csrCoalCode1: '',
      csrCoalCode2: '',

      cleanAdCoalName1: '西坡 ( 外 )',
      cleanAdCoalName2: '邓家庄 ( 外 )',
      cleanStdCoalName1: '朱家店 ( 外 )',
      cleanStdCoalName2: '华泰 ( 外 )',
      procGCoalName1: '沁新 ( 外 )',
      procGCoalName2: '玉华 ( 外 )',
      csrCoalName1: '玉华 ( 外 )',
      csrCoalName2: 'K4 ( 外 )',

      cleanAdCoalType1: '外',
      cleanAdCoalType2: '外',
      cleanStdCoalType1: '外',
      cleanStdCoalType2: '外',
      procGCoalType1: '外',
      procGCoalType2: '外',
      csrCoalType1: '外',
      csrCoalType2: '外',

      chooseCoalCategoryInfo: {
        optName: 'create',
        visitable: false,
        activekey: '',
        record: {}
      },
      query: {
        beginDate: '',
        endDate: '',
        cleanAdCoalCode1: 'donghuixipo-standard',
        cleanAdCoalCode2: 'dengjiazhuang-standard',
        cleanStdCoalCode1: 'zhujiadian-standard',
        cleanStdCoalCode2: 'huatai-standard',
        procGCoalCode1: 'qinxin-standard',
        procGCoalCode2: 'yuhua-standard',
        csrCoalCode1: 'yuhua-standard',
        csrCoalCode2: 'K4-standard',

        cleanAdCoalName1: '西坡 ( 外 )',
        cleanAdCoalName2: '邓家庄 ( 外 )',
        cleanStdCoalName1: '朱家店 ( 外 )',
        cleanStdCoalName2: '华泰 ( 外 )',
        procGCoalName1: '沁新 ( 外 )',
        procGCoalName2: '玉华 ( 外 )',
        csrCoalName1: '玉华 ( 外 )',
        csrCoalName2: 'K4 ( 外 )'
      },
      searchLoading: false,
      tableData: [],
      differenceLabel: '差值',
      dateLabel: '日期',
      labels: {
        cleanAdCoalShortname1: '西坡 灰1',
        cleanAdCoalShortname2: '邓家庄 灰2',
        cleanStdCoalShortname1: '朱家店 硫1',
        cleanStdCoalShortname2: '华泰 硫2',
        procGCoalShortname1: '沁新 粘结1',
        procGCoalShortname2: '玉华 粘结2',
        csrCoalShortname1: '玉华 CSR1',
        csrCoalShortname2: 'K4 CSR2'
      },
      historyModal: {
        optName: 'create',
        visitable: false,
        record: {}
      },
      historytableData: []
    }
  },
  computed: {
    // 这里使用tableData的数据
    getChartsAdList() {
      return [
        ...this.tableData.map((item) => {
          return {
            index: 0,
            type: this.labels.cleanAdCoalShortname1,
            value: item.cleanAdPrice1,
            name: item.date,
            color: '#FF726B',
            minPrice: item.mincleanAdPrice1
          }
        }),
        ...this.tableData.map((item) => {
          return {
            index: 1,
            type: this.labels.cleanAdCoalShortname2,
            value: item.cleanAdPrice2,
            name: item.date,
            color: '#1BB681',
            minPrice: item.mincleanAdPrice2
          }
        })
      ]
    },
    getChartsStdList() {
      return [
        ...this.tableData.map((item) => {
          return {
            index: 1,
            type: this.labels.cleanStdCoalShortname1,
            value: item.cleanStdPrice1,
            name: item.date,
            color: '#FF726B',
            minPrice: item.mincleanStdPrice1
          }
        }),
        ...this.tableData.map((item) => {
          return {
            index: 1,
            type: this.labels.cleanStdCoalShortname2,
            value: item.cleanStdPrice2,
            name: item.date,
            color: '#0559A8',
            minPrice: item.mincleanStdPrice2
          }
        })
      ]
    },
    getChartsGList() {
      return [
        ...this.tableData.map((item) => {
          return {
            index: 1,
            type: this.labels.procGCoalShortname1,
            value: item.procGPrice1,
            name: item.date,
            color: '#FF9639',
            minPrice: item.minprocGPrice1
          }
        }),
        ...this.tableData.map((item) => {
          return {
            index: 1,
            type: this.labels.procGCoalShortname2,
            value: item.procGPrice2,
            name: item.date,
            color: '#1BB681',
            minPrice: item.minprocGPrice2
          }
        })
      ]
    },
    getChartsCsrList() {
      return [
        ...this.tableData.map((item) => {
          return {
            index: 1,
            type: this.labels.csrCoalShortname1,
            value: item.csrPrice1,
            name: item.date,
            color: '#FF9639',
            minPrice: item.mincsrPrice1
          }
        }),
        ...this.tableData.map((item) => {
          return {
            index: 1,
            type: this.labels.csrCoalShortname2,
            value: item.csrPrice2,
            name: item.date,
            color: '#0559A8',
            minPrice: item.mincsrPrice2
          }
        })
      ]
    }
  },
  mounted() {
    // 默认一个月
    let time = this.getInitializeDate()
    this.query.beginDate = time[0]
    this.query.endDate = time[1]
    this.getHistiryList()
  },
  watch: {},
  methods: {
    async getHistiryList() {
      const list = await FetchData(Api.compareHistory, {}, [])
      let newArry = []
      for (var i = 0; i < list.length; i++) {
        let obj = {
          createDate: list[i].createDate,
          alldata:
            '[灰]' +
            list[i].cleanAdCoalName1 +
            ' VS ' +
            list[i].cleanAdCoalName2 +
            '，[硫]' +
            list[i].cleanStdCoalName1 +
            ' VS ' +
            list[i].cleanStdCoalName2 +
            '，[粘结]' +
            list[i].procGCoalName1 +
            ' VS ' +
            list[i].procGCoalName2 +
            '，[CSR]' +
            list[i].csrCoalName1 +
            ' VS ' +
            list[i].csrCoalName2
        }
        newArry.push(obj)
      }
      this.historytableData = newArry
      if (list.length > 1) {
        this.query = {
          ...this.query,
          cleanAdCoalCode1: list[0].cleanAdCoalCode1,
          cleanAdCoalCode2: list[0].cleanAdCoalCode2,
          cleanAdCoalName1: list[0].cleanAdCoalName1,
          cleanAdCoalName2: list[0].cleanAdCoalName2,
          cleanStdCoalCode1: list[0].cleanStdCoalCode1,
          cleanStdCoalCode2: list[0].cleanStdCoalCode2,
          cleanStdCoalName1: list[0].cleanStdCoalName1,
          cleanStdCoalName2: list[0].cleanStdCoalName2,
          csrCoalCode1: list[0].csrCoalCode1,
          csrCoalCode2: list[0].csrCoalCode2,
          csrCoalName1: list[0].csrCoalName1,
          csrCoalName2: list[0].csrCoalName2,
          procGCoalCode1: list[0].procGCoalCode1,
          procGCoalCode2: list[0].procGCoalCode2,
          procGCoalName1: list[0].procGCoalName1,
          procGCoalName2: list[0].procGCoalName2
        }

        this.labels = {
          ...this.labels,
          cleanAdCoalShortname1: list[0].cleanAdCoalName1 + '灰1',
          cleanAdCoalShortname2: list[0].cleanAdCoalName2 + '灰2',
          cleanStdCoalShortname1: list[0].cleanStdCoalName1 + '硫1',
          cleanStdCoalShortname2: list[0].cleanStdCoalName2 + '硫2',
          procGCoalShortname1: list[0].procGCoalName1 + '粘结1',
          procGCoalShortname2: list[0].procGCoalName2 + '粘结2',
          csrCoalShortname1: list[0].csrCoalName1 + 'CSR11',
          csrCoalShortname2: list[0].csrCoalName2 + 'CSR12'
        }

        this.cleanAdCoalName1 = list[0].cleanAdCoalName1
        this.cleanAdCoalName2 = list[0].cleanAdCoalName2
        this.cleanStdCoalName1 = list[0].cleanStdCoalName1
        this.cleanStdCoalName2 = list[0].cleanStdCoalName2
        this.csrCoalName1 = list[0].csrCoalName1
        this.csrCoalName2 = list[0].csrCoalName2
        this.procGCoalName1 = list[0].procGCoalName1
        this.procGCoalName2 = list[0].procGCoalName2

        this.$nextTick(() => {
          this.handleSearch()
        })
      } else {
        console.log('写死默认值')
        this.handleSearch()
      }
    },
    lookHistory() {
      this.historyModal.visitable = true
    },
    getInitializeDate() {
      let nowTimes = Date.now()
      let oldTimes = nowTimes - 86400000 * 30
      let oldDate = this.getDate(oldTimes) // 一个月前的时间
      let nowDate = this.getDate(nowTimes) // 今天的时间
      return [oldDate, nowDate]
    },
    // 格式化时间 返回年月日
    getDate(time) {
      if (time) {
        let val = new Date(time)
        let Y = val.getFullYear()
        let M = val.getMonth() + 1 < 10 ? '0' + (val.getMonth() + 1) : val.getMonth() + 1
        let D = val.getDate() < 10 ? '0' + val.getDate() : val.getDate()
        let h = val.getHours() < 10 ? '0' + val.getHours() : val.getHours()
        let m = val.getMinutes() < 10 ? '0' + val.getMinutes() : val.getMinutes()
        return Y + '-' + M + '-' + D
      }
      return ''
    },

    getValue(data) {
      let Things = data.slectlist
      if (this.chooseCoalCategoryInfo.activekey === 'cleanAdCoalCode1') {
        this.cleanAdCoalCode1 = Things[0].coalCode
        if (data.selectCoalType === 'first') {
          this.cleanAdCoalType1 = '外'
        } else if (data.selectCoalType === 'second') {
          this.cleanAdCoalType1 = '内'
        }
        let name = Things[0].coalShortname + ' (' + this.cleanAdCoalType1 + ')'
        this.cleanAdCoalName1 = name
        this.labels = {...this.labels, cleanAdCoalShortname1: name + '灰1'}
        this.query = {...this.query, cleanAdCoalCode1: Things[0].coalCode, cleanAdCoalName1: name}
      }
      if (this.chooseCoalCategoryInfo.activekey === 'cleanAdCoalCode2') {
        this.cleanAdCoalCode2 = Things[0].coalCode
        if (data.selectCoalType === 'first') {
          this.cleanAdCoalType2 = '外'
        } else if (data.selectCoalType === 'second') {
          this.cleanAdCoalType2 = '内'
        }
        let name = Things[0].coalShortname + ' (' + this.cleanAdCoalType2 + ')'
        this.cleanAdCoalName2 = name
        this.labels = {...this.labels, cleanAdCoalShortname2: name + '灰2'}
        this.query = {...this.query, cleanAdCoalCode2: Things[0].coalCode, cleanAdCoalName2: name}
      }
      if (this.chooseCoalCategoryInfo.activekey === 'cleanStdCoalCode1') {
        this.cleanStdCoalCode1 = Things[0].coalCode

        if (data.selectCoalType === 'first') {
          this.cleanStdCoalType1 = '外'
        } else if (data.selectCoalType === 'second') {
          this.cleanStdCoalType1 = '内'
        }
        let name = Things[0].coalShortname + ' (' + this.cleanStdCoalType1 + ')'
        this.cleanStdCoalName1 = name
        this.labels = {...this.labels, cleanStdCoalShortname1: name + '硫1'}
        this.query = {...this.query, cleanStdCoalCode1: Things[0].coalCode, cleanStdCoalName1: name}
      }
      if (this.chooseCoalCategoryInfo.activekey === 'cleanStdCoalCode2') {
        this.cleanStdCoalCode2 = Things[0].coalCode

        if (data.selectCoalType === 'first') {
          this.cleanStdCoalType2 = '外'
        } else if (data.selectCoalType === 'second') {
          this.cleanStdCoalType2 = '内'
        }
        let name = Things[0].coalShortname + ' (' + this.cleanStdCoalType2 + ')'
        this.cleanStdCoalName2 = name
        this.labels = {...this.labels, cleanStdCoalShortname2: name + '硫2'}
        this.query = {...this.query, cleanStdCoalCode2: Things[0].coalCode, cleanStdCoalName2: name}
      }
      if (this.chooseCoalCategoryInfo.activekey === 'procGCoalCode1') {
        this.procGCoalCode1 = Things[0].coalCode

        if (data.selectCoalType === 'first') {
          this.procGCoalType1 = '外'
        } else if (data.selectCoalType === 'second') {
          this.procGCoalType1 = '内'
        }
        let name = Things[0].coalShortname + ' (' + this.procGCoalType1 + ')'
        this.procGCoalName1 = name
        this.labels = {...this.labels, procGCoalShortname1: name + '粘结1'}
        this.query = {...this.query, procGCoalCode1: Things[0].coalCode, procGCoalName1: name}
      }
      if (this.chooseCoalCategoryInfo.activekey === 'procGCoalCode2') {
        this.procGCoalCode2 = Things[0].coalCode

        if (data.selectCoalType === 'first') {
          this.procGCoalType2 = '外'
        } else if (data.selectCoalType === 'second') {
          this.procGCoalType2 = '内'
        }
        let name = Things[0].coalShortname + ' (' + this.procGCoalType2 + ')'
        this.procGCoalName2 = name
        this.labels = {...this.labels, procGCoalShortname2: name + '粘结2'}
        this.query = {...this.query, procGCoalCode2: Things[0].coalCode, procGCoalName2: name}
      }

      if (this.chooseCoalCategoryInfo.activekey === 'csrCoalCode1') {
        this.csrCoalCode1 = Things[0].coalCode

        if (data.selectCoalType === 'first') {
          this.csrCoalType1 = '外'
        } else if (data.selectCoalType === 'second') {
          this.csrCoalType1 = '内'
        }
        let name = Things[0].coalShortname + ' (' + this.csrCoalType1 + ')'
        this.csrCoalName1 = name
        this.labels = {...this.labels, csrCoalShortname1: name + 'CSR1'}
        this.query = {...this.query, csrCoalCode1: Things[0].coalCode, csrCoalName1: name}
      }
      if (this.chooseCoalCategoryInfo.activekey === 'csrCoalCode2') {
        this.csrCoalCode2 = Things[0].coalCode

        if (data.selectCoalType === 'first') {
          this.csrCoalType2 = '外'
        } else if (data.selectCoalType === 'second') {
          this.csrCoalType2 = '内'
        }
        let name = Things[0].coalShortname + ' (' + this.csrCoalType2 + ')'
        this.csrCoalName2 = name
        this.labels = {...this.labels, csrCoalShortname2: name + 'CSR2'}
        this.query = {...this.query, csrCoalCode2: Things[0].coalCode, csrCoalName2: name}
      }
      this.handleSearch()
    },
    // 切换煤种
    handleToggleCoalCategory(key) {
      this.chooseCoalCategoryInfo.activekey = key
      this.chooseCoalCategoryInfo.visitable = true
    },
    changeCellStyle({row, column, rowIndex, columnIndex}) {
      if (column.label === this.differenceLabel) {
        return 'background-color:rgba(128, 136, 171, 0.1);'
      }
    },
    async handleSearch() {
      const list = await FetchData(Api.coalCategoryPriceCompare, this.query, [])
      this.tableData = list
    },
    tableRowStyleName({row, column, rowIndex, columnIndex}) {
      const styles = {
        background: '#000D30',
        color: '#fff'
      }
      if (column.label === this.dateLabel) {
        styles.background = '#1E4B8F'
      }
      if (column.label === this.differenceLabel) {
        styles.background = '#8088AB'
      }
      return styles
    },
    beforeFetch(params) {
      return {
        ...params
      }
    }
  }
}
</script>
<style lang="scss">
.el-table {
  background-color: rgba(247, 248, 250, 0.5);
}
</style>

<style lang="scss" scoped>
::v-deep {
  .el-table td.el-table__cell {
    border-bottom: 1px solid transparent !important;
  }
}

::v-deep .el-table th.el-table__cell > .cell {
  overflow: hidden; /* 溢出隐藏 */
  white-space: nowrap; /* 强制不换行 */
  position: relative; /* 子绝父相 */
}

.vs-charts-wrap {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(2, 1fr);

  .vs-chart-item {
    height: 28.875rem;
  }
}

.card {
  display: flex;
  flex-flow: column;
  margin-bottom: 15px;

  &:last-of-type {
    margin-bottom: 0;
  }

  .card-header {
    font-size: 1rem;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    width: 100%;
    justify-content: space-between;

    .title {
      position: relative;
      color: #272727;
      font-size: 1.2rem;
      display: flex;
      align-items: center;
      margin-left: 10px;

      &::before {
        position: absolute;
        content: '';
        width: 2.5px;
        left: -10px;
        border-radius: 3px;
        height: 1.1rem;
        top: 50%;
        transform: translateY(-50%);
        background-color: #0559a8;
      }
    }

    .action {
      margin-left: 70px;
      flex: 1;
      display: flex;
    }
  }
}

.flex-align-items {
  display: flex;
  align-items: center;
}

.app-container {
  .section {
    background-color: #fff;
    margin-bottom: 1rem;
    padding: 1.375rem;
  }

  .vs-card {
    display: grid;
    margin-bottom: 40px;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.25rem;
    border-radius: 0.25rem;
    padding-left: 8.125rem;

    .vs-card-item {
      // 偶数
      &:nth-child(2n) {
        background: #f3f9ff;
      }

      // 奇数
      &:nth-child(2n + 1) {
        background: #fffaf5;
      }

      border: 1px dashed #c9c9c9;
      border-radius: 0.625rem;
      height: 9rem;
      flex: 1;
      display: flex;
      position: relative;
      align-items: center;
      padding: 1.25rem;

      .ctx-wrap {
        flex: 1;
        display: flex;
        flex-flow: column;
        //justify-content: space-between;
        align-items: center;

        .title {
          font-size: 1.325rem;
          color: #2c2c2c;
          // 字体间距
          letter-spacing: 0.1em;
          //background-color: rgba(128, 136, 171, 0.3);
        }

        .toggle-btn {
          margin-top: 0.625rem;
          height: 1.5rem;
          border: 1px solid #d3d3d3;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          font-size: 0.875rem;
          color: #525252;
          padding: 4px 8px;

          i {
            margin-left: 3px;
          }
        }
      }

      .vs {
        width: 2rem;
        height: 2rem;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }

      .arrow {
        position: absolute;
        left: 50%;
        bottom: -30%;
        transform: translate(-50%, 0%);
      }

      .round-tip-blue {
        background: #004e9a;
      }

      .round-tip-orange {
        background: #ff8b40;
      }

      .round-tip {
        position: absolute;
        left: 50%;
        bottom: -8%;
        transform: translate(-50%, 0%);
        width: 4.375rem;
        height: 1.3125rem;
        border-radius: 11px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        color: #ffff;
      }
    }
  }
}
</style>
