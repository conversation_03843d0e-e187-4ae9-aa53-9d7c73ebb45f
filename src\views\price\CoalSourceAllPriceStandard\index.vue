<template>
  <div class="app-container">
    <SnProTable :ref="pageRef" :actions="actions" :afterFetch="afterFetch"
                :beforeFetch="beforeFetch"
                :model="model" :summary-method="getSummary"
                show-summary
                @row-class-name="rowClassName"
    >
      <template #searchExtra>
        <SnSimpleSelect v-model="coalCategoryId" v-bind="{
            list: coalCategoryList,
            type: 'radiobutton',
            props: coalCategoryProps
          }"/>
      </template>

      <template #date="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row,$index}">
            <div style="position: relative;overflow: hidden;">
              <span>{{ row[col.prop].substring(0, 10) }}</span>
              <div v-if="$index === 0 && row.base === 1" class="tag-left-right">
                <span class="triangle-text">基准</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </template>
    </SnProTable>
  </div>
</template>

<script>
import model from './model'
import {FetchData} from '@/utils'
import {treeOutside} from '@/views/base/CoalCategory/api'
import {createWorkbook} from '@/utils/excelUtils'
import {formatToDateTime, getDate} from '@/utils/dateUtils'
import CalcUtils from '@/utils/calcUtils'
import {isDef} from '@/utils/is'
import SnProTable from '@/components/Common/SnProTable/index.vue'
import SnSimpleSelect from '@/components/Common/SnSimpleSelect/index.vue'

export default {
  name: 'InsidePrice',
  components: {SnSimpleSelect, SnProTable},
  data() {
    return {
      model,
      pageRef: 'page',
      permissions: {
        save: '*',
        remove: '*'
      },
      addInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      },
      coalCategoryId: '',
      coalCategoryList: [],
      coalCategoryProps: {
        label: 'analysisCategoryName',
        value: 'id'
      }
    }
  },
  computed: {
    actions() {
      return [
        {
          type: 'add',
          text: '导出', // 当前页
          hasPermission: ['*'],
          onClick: async (item) => {
            try {
              const {data, columns} = await this.$refs[this.pageRef].handleExport()
              createWorkbook({
                data,
                columns,
                fileName: this.$route.meta.title,
                defaultRowHeight: 10
              })
            } catch (e) {
              console.warn(e)
            }
          }
        }
      ]
    },
    currentCoalCategory() {
      if (this.coalCategoryList && this.coalCategoryProps) {
        return this.coalCategoryList.find((item) => item[this.coalCategoryProps.value] === this.coalCategoryId) || {}
      } else {
        return {}
      }
    }
  },
  created() {
    this.getCoalCategoryList()
  },
  watch: {
    async coalCategoryId() {
      this.getList()
    }
  },
  methods: {
    afterFetch(data) {
      return {list: data}
    },
    getSummary(params) {
      const {columns, data} = params
      const result = [
        {key: 'cleanAd', decimalPlaces: 2},
        {key: 'cleanVdaf', decimalPlaces: 2},
        {key: 'cleanStd', decimalPlaces: 2},
        {key: 'procG', decimalPlaces: 0},
        {key: 'procY', decimalPlaces: 1},
        {key: 'procX', decimalPlaces: 2},
        {key: 'cokeIndexMjb', decimalPlaces: 2},
        {key: 'cokeIndexAd', decimalPlaces: 2},
        {key: 'cokeIndexCsr', decimalPlaces: 2},
        {key: 'performanceCalcPrice', decimalPlaces: 2},
        {key: 'performanceBaseDiff', decimalPlaces: 2},
        {key: 'performancePriceCokeCost', decimalPlaces: 2},
        {key: 'cleanMt', decimalPlaces: 2},
        {key: 'activePriceTransportPriceNoTax', decimalPlaces: 2},
        {key: 'performancePriceCokeCost', decimalPlaces: 2},
        {key: 'performancePriceFactorDryBasisCost', decimalPlaces: 2}
      ].reduce((pre, tar) => {
        return {
          ...pre,
          [tar.key]: CalcUtils.getShowValue(CalcUtils.calculateAverageDecimal(
            data.map(item => item[tar.key]).filter(v => isDef(v)).map(v => Number(v))
          ), tar.decimalPlaces, 'ROUND_HALF_UP')
        }
      }, {})
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <div class="cell">{<div>合计</div>}</div>
        } else {
          sums[index] =
            <div class="sampleName_cell">{<div>{result[column.property] || '/'}</div>}</div>
        }
      })
      return sums
    },

    getDate,
    rowClassName({row, rowIndex, columnIndex, column}) {
      if (rowIndex === 0 && row.base === 1) {
        return 'mark-base-row'
      }
    },
    async getCoalCategoryList() {
      const list = await FetchData(treeOutside, undefined, [])
      this.coalCategoryList = list
      if (list.length) {
        this.coalCategoryId = list[0][this.coalCategoryProps.value]
      }
    },

    beforeFetch(params) {
      return {
        ...params,
        beginDate: params._dateRange ? formatToDateTime(params._dateRange, 'YYYY-MM-DD 00:00:00') : '',
        endDate: params._dateRange ? formatToDateTime(params._dateRange, 'YYYY-MM-DD 23:59:59') : '',
        coalCategoryName: this.currentCoalCategory[this.coalCategoryProps.label],
        coalSource: 'wb'
      }
    },

    getList() {
      this.$refs[this.pageRef].getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.el-menu--horizontal > .el-menu-item {
  height: 50px;
  line-height: 50px;
}

::v-deep {
  .el-table__footer-wrapper tbody td.el-table__cell,
  .el-table__fixed-footer-wrapper tbody td.el-table__cell {
    background-color: #fff8f8;
  }

  // 选中 .el-table__footer-wrapper 第一个.cell
  .el-table__footer-wrapper .cell:first-child {
    //height: 35px;
  }

  .el-table .el-table__footer-wrapper .cell,
  .el-table .el-table__fixed-footer-wrapper .cell {
    padding: 0;
    height: 35px;

    .sampleName_cell {
      height: 100%;
      display: flex;
      justify-content: center;
      text-align: center;
      align-content: center;
      flex-direction: row;
      align-items: center;
    }

    .cell {
      flex: 1;
      display: flex;
      flex-direction: column;
      line-height: normal;
      justify-content: space-between;

      div {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: #ebeef5 1px solid;
        box-sizing: content-box;

        &:last-of-type {
          border-bottom: none;
        }
      }
    }
  }

  .el-table__footer-wrapper tbody td,
  .el-table__fixed-footer-wrapper tbody td {
    padding: 0;
  }

  .is-active .el-submenu__title {
    color: #2878ff;
  }
}

.tag-left-right {
  font-size: 10px;
  position: absolute;
  right: -35px;
  text-align: center;
  top: -8px;
  width: 76px;
  line-height: 42px;
  height: 28px;
  z-index: 999;
  color: #fff;
  background: #ff9639;
  transform: rotate(50deg);
}

::v-deep {
  .el-table .cell {
    min-height: 33px;
    line-height: 33px;
    padding-right: 0;
    padding-left: 0;
  }
}
</style>
