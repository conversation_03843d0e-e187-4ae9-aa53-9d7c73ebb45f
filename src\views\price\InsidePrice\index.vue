<template>
  <div class="app-container">
    <SnProTable :ref="pageRef" :actions="actions" :beforeFetch="beforeFetch" :model="model">
      <template #searchExtra>
        <SnSimpleSelect
          v-model="coalCategoryId"
          v-bind="{
            list: coalCategoryList,
            type: 'radiobutton',
            props: coalCategoryProps
          }"
        />
      </template>
    </SnProTable>
  </div>
</template>

<script>
import model from './model'
import { FetchData } from '@/utils'
import { treeInside } from '@/views/base/CoalCategory/api'
import SnProTable from '@/components/Common/SnProTable/index.vue'
import SnSimpleSelect from '@/components/Common/SnSimpleSelect/index.vue'

export default {
  name: 'InsidePrice',
  components: {SnSimpleSelect, SnProTable},
  data() {
    return {
      model,
      pageRef: 'page',
      permissions: {
        save: '*',
        remove: '*'
      },
      addInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      },
      coalCategoryId: '',
      coalCategoryList: [],
      coalCategoryProps: {
        label: 'label',
        value: 'id'
      }
    }
  },
  computed: {
    actions() {
      return []
    },
    currentCoalCategory() {
      return this.coalCategoryList.find((item) => item[this.coalCategoryProps.value] === this.coalCategoryId) || {}
    }
  },
  created() {
    this.getCoalCategoryList()
  },
  watch: {
    async coalCategoryId() {
      this.getList()
    }
  },
  methods: {
    async getCoalCategoryList() {
      const list = await FetchData(treeInside, undefined, [])
      this.coalCategoryList = list
      if (list.length) {
        this.coalCategoryId = list[0][this.coalCategoryProps.value]
      }
    },

    beforeFetch(params) {
      return {
        ...params,
        filter_LIKES_coalCategoryName: this.currentCoalCategory[this.coalCategoryProps.label]
        // coalCategoryId: this.currentCoalCategory[this.coalCategoryProps.value],
        // coalCategoryName: this.currentCoalCategory[this.coalCategoryProps.label]
      }
    },

    getList() {
      this.$refs[this.pageRef].getList()
    }
  }
}
</script>

<style lang="scss"></style>
