// eslint-disable-next-line

import BaseModel, { TableConfig, FormConfig } from '@/components/Common/SnProTable/model'
import { getDate } from '@/utils/dateUtils'

export const name = `/a/coalSourceInside`

const createFormConfig = () => {
  return {
    fieldMapToTime: [['_dateRange', ['filter_GES_date', 'filter_LES_date'], 'YYYY-MM-DD']],
    filters: [
      {
        label: '日期',
        prop: '_dateRange',
        component: 'SnDateRanger',
        defaultValue: [],
        componentProp: {
          valueFormat: 'yyyy-MM-dd'
        },
        style: {
          width: '230px'
        }
      },
      {
        label: '品名简称',
        prop: 'filter_LIKES_coalShortname',
        labelWidth: 40
      },
      {
        hide: true,
        prop: 'orderBy',
        defaultValue: 'date'
      },
      {
        hide: true,
        prop: 'orderDir',
        defaultValue: 'desc'
      }
    ]
  }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
  return {
    mountedQuery: false,
    showOpt: false, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: false, // 显示选择列
    columns: [
      {
        prop: 'date',
        label: '日期',
        format: ({ value }) => {
          return getDate(value)
        },
        width: 160,
        fixed: true
      },

      {
        label: '品名简称',
        prop: 'coalShortname',
        width: 150,
        fixed: true
      },
      {
        label: '不含税煤价',
        prop: 'activePriceCoalPriceNoTax',
        width: 100
      },
      {
        label: '不含税运费',
        prop: 'activePriceTransportPriceNoTax',
        width: 100
      },
      {
        label: '路耗1%',
        prop: 'activePriceTransportCostP1',
        width: 100
      },
      {
        label: '不含税进厂价',
        prop: 'activePriceFactoryPriceNoTax',
        width: 100
      },
      {
        label: '水分%',
        prop: 'cleanMt',
        width: 80
      },
      {
        label: '进厂干基成本',
        prop: 'activePriceFactorDryBasisCost',
        width: 100
      },
      {
        label: '库存价',
        prop: 'stockPrice',
        width: 100
      },
      {
        label: '供应情况',
        prop: 'activePriceSupplyInfo'
      },
      {
        label: '数据来源',
        prop: 'activePriceSource'
      }
    ]
  }
}

class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }

  // page(query) {
  //   return super.request({
  //     url: `${name}/page-by-coal-category`,
  //     method: 'get',
  //     params: query
  //   })
  // }
}

export default new Model()
