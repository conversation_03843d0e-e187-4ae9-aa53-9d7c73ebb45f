// eslint-disable-next-line

import BaseModel, {TableConfig, FormConfig} from '@/components/Common/SnProTable/model'
import {getDate} from '@/utils/dateUtils'

export const name = `/cwe/a/mineralAdjustExternalPrice`

const createFormConfig = () => {
  return {
    fieldMapToTime: [['_dateRange', ['filter_GES_date', 'filter_LES_date'], 'YYYY-MM-DD']], // 这里直接格式化
    fieldMapToNumber: [
      ['ad', ['filter_GES_ad', 'filter_LES_ad']],
      ['std', ['filter_GES_std', 'filter_LES_std']],
      ['vdaf', ['filter_GES_vdaf', 'filter_LES_vdaf']],
      ['g', ['filter_GES_g', 'filter_LES_g']]
    ],

    filters: [
      {
        label: '日期',
        prop: '_dateRange',
        component: 'SnDateRanger',
        defaultValue: [],
        componentProp: {
          valueFormat: 'yyyy-MM-dd'
        },
        style: {
          width: '230px'
        }
      },
      {
        label: '区域',
        labelWidth: '40px',
        prop: 'filter_LIKES_area'
      },
      {
        label: '煤矿',
        labelWidth: '40px',
        prop: 'filter_LIKES_colliery'
      },
      {
        label: '煤种',
        labelWidth: '40px',
        prop: 'filter_LIKES_coalType'
      },
      {
        // 独占一行
        slot: 'empty',
        itemStyle: {
          width: '100%',
          height: 0,
          margin: 0
        }
      },
      {
        label: '灰分',
        prop: 'ad',
        component: 'SnSimpleInputRanger',
        defaultValue: [],
        style: {
          width: '200px'
        },
        componentProp: {
          useProFormItem: false,
          startPlaceholder: '最小灰分',
          endPlaceholder: '最大灰分'
        }
      },
      {
        label: '硫分',
        prop: 'std',
        component: 'SnSimpleInputRanger',
        defaultValue: [],
        style: {
          width: '200px'
        },
        componentProp: {
          useProFormItem: false,
          startPlaceholder: '最小硫分',
          endPlaceholder: '最大硫分'
        }
      },

      {
        label: '挥发',
        prop: 'vdaf',
        component: 'SnSimpleInputRanger',
        defaultValue: [],
        style: {
          width: '200px'
        },
        componentProp: {
          useProFormItem: false,
          startPlaceholder: '最小挥发',
          endPlaceholder: '最大挥发'
        }
      },

      {
        label: '粘结',
        prop: 'g',
        component: 'SnSimpleInputRanger',
        defaultValue: [],
        style: {
          width: '200px'
        },
        componentProp: {
          useProFormItem: false,
          startPlaceholder: '最小粘结',
          endPlaceholder: '最大粘结'
        }
      },

      {
        hide: true,
        prop: 'orderBy',
        defaultValue: 'date'
      },
      {
        hide: true,
        prop: 'orderDir',
        defaultValue: 'desc'
      }
    ]
  }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
  return {
    showOpt: false, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: false, // 显示选择列
    columns: [
      {
        label: '日期',
        prop: 'date',
        minWidth: 120,
        format: ({value}) => {
          return value.substring(0, 10)
        }
      },
      {label: '区域', prop: 'area', minWidth: 80},
      {label: '煤矿', prop: 'colliery', minWidth: 100},
      {label: '煤种', prop: 'coalType', minWidth: 100},
      {label: 'Ad', prop: 'ad', minWidth: 50},
      {label: 'St,d', prop: 'std', minWidth: 50},
      {label: 'Vdaf', prop: 'vdaf', minWidth: 50},
      {label: 'G', prop: 'g', minWidth: 50},
      {label: '回收率', prop: 'recycleRate', minWidth: 80},
      {label: '成交量', prop: 'turnover', minWidth: 80},
      {label: '成交价', prop: 'transactionPrice', minWidth: 80},
      {label: '备注', prop: 'remarks', minWidth: 100}
    ]
  }
}

class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }
}

export default new Model()
