<template>
  <SnProTable :ref="pageRef" :actions="actions" :model="model">
    <template #maxRingComparision="{ col }">
      <el-table-column v-bind="col">
        <template #default="{row}">
          <div :style="{ color: row[col.prop] * 1 === 0 ? 'normal' : row[col.prop] < 0 ? '#33cad9' : '#ff726b' }">
            {{ row[col.prop] }}
          </div>
        </template>
      </el-table-column>
    </template>

    <template #minRingComparision="{ col }">
      <el-table-column v-bind="col">
        <template #default="{row}">
          <div :style="{ color: row[col.prop] * 1 === 0 ? 'normal' : row[col.prop] < 0 ? '#33cad9' : '#ff726b' }">
            {{ row[col.prop] }}
          </div>
        </template>
      </el-table-column>
    </template>
  </SnProTable>
</template>

<script>
import model from './model'
import SnProTable from '@/components/Common/SnProTable/index.vue'

export default {
  name: 'MineralAdjustPrice',
  components: {SnProTable},
  data() {
    return {
      model,
      pageRef: 'page',
      permissions: {
        save: '*',
        remove: '*'
      },
      addInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      }
    }
  },
  computed: {
    actions() {
      return []
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped></style>
