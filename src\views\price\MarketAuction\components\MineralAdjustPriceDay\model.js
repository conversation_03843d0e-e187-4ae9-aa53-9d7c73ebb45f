// eslint-disable-next-line

import BaseModel, { TableConfig, FormConfig } from '@/components/Common/SnProTable/model'
import { getTablePagination } from '@/const'

export const name = `/cwe/a/mineralAdjustPrice`

const createFormConfig = () => {
  return {
    fieldMapToTime: [['_dateRange', ['beginDate', 'endDate'], 'YYYY-MM-DD']], // 这里直接格式化
    filters: [
      {
        label: '日期',
        prop: '_dateRange',
        component: 'SnDateRanger',
        defaultValue: [],
        componentProp: {
          valueFormat: 'yyyy-MM-dd'
        },
        style: {
          width: '230px'
        }
      },

      {
        hide: true,
        prop: 'orderBy',
        defaultValue: 'date'
      },
      {
        hide: true,
        prop: 'orderDir',
        defaultValue: 'desc'
      }
    ]
  }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
  return {
    columns: [
      { label: '日期', prop: 'date', minWidth: 120 },
      {
        label: '挂牌量(万)',
        prop: 'hangQuantity',
        minWidth: 80,
        format: ({ value }) => {
          return value / 10000
        }
      },
      {
        label: '成交量(万)',
        prop: 'turnover',
        minWidth: 80,
        format: ({ value }) => {
          return value / 10000
        }
      },
      {
        label: '流拍(万)',
        prop: 'abortive',
        minWidth: 80,
        format: ({ value }) => {
          return value / 10000
        }
      },

      { label: '流拍率', prop: 'abortiveRate', minWidth: 80 },
      { label: '成交率', prop: 'turnoverRate', minWidth: 80 },
      {
        label: '涨跌幅最大值',
        prop: 'maxRingComparision',
        slot: 'maxRingComparision',
        minWidth: 120
      },
      {
        label: '涨跌幅最小值',
        prop: 'minRingComparision',
        slot: 'minRingComparision',
        minWidth: 120
      }
    ], // 显示操作列
    pagination: {
      ...getTablePagination(),
      pageSize: 1000
    }, // 显示序号列
    showIndex: false, // 显示选择列
    showOpt: false,
    showSelection: false
  }
}

class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }

  page(query) {
    return super.request({
      url: `${name}/day/page`,
      method: 'get',
      params: query
    })
  }
}

export default new Model()
