<template>
  <SnTabs v-model="tabActive" :tabs="tabs">
    <component :is="tabActive"/>
  </SnTabs>
</template>

<script>
import MineralAdjustPrice from './components/MineralAdjustPrice'
import MineralAdjustPriceDay from './components/MineralAdjustPriceDay'
import MineralAdjustExternalPrice from './components/MineralAdjustExternalPrice'
import SnTabs from '@/components/Common/SnTabs/index.vue'

export default {
  name: 'MarketAuction',
  components: {
    SnTabs,
    MineralAdjustPrice,
    MineralAdjustPriceDay,
    MineralAdjustExternalPrice
  },
  data() {
    return {
      tabs: [
        {
          label: '国内竞拍价格',
          value: 'MineralAdjustPrice'
        },
        {
          label: '国内日汇总成交/流拍率',
          value: 'MineralAdjustPriceDay'
        },
        {
          label: '国外成交',
          value: 'MineralAdjustExternalPrice'
        }
      ],
      tabActive: 'MineralAdjustPrice'
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
::v-deep .container-tab {
  font-size: 12px !important;
  color: #909399 !important;
  height: 40px !important;
}

::v-deep .search-tabs {
  font-size: 12px !important;
  color: #909399 !important;
  height: 40px !important;
}
</style>
