<template>
  <div class="container">
    <SnModal
      v-model="_value"
      :cancel="cancel"
      :fullscreen.sync="fullscreen"
      :ok="ok"
      :showFooter="!isViewStatus"
      :title="title"
      class="modal"
      v-bind="$attrs"
      width="70%"
    >
      <el-form ref="form" :disabled="isViewStatus" :model="form" :rules="rules" labelPosition="top" labelWidth="100px" style="height: 100%">
        <SnFormCard title="基本信息">
          <VhTable ref="excelTable" :beforePaste="beforePaste" :list="form.data" class="table-content" @init="setTableSetting">
            <VHColumn data="date" dateFormat="YYYY-MM-DD" formRequire title="日期" type="date" width="120" />
            <VHColumn data="coalType" formRequire title="煤种" type="text" width="130" />
            <VHColumn data="coalName" formRequire title="品名" type="text" width="100" />
            <VHColumn :numericFormat="{ pattern: '0.00' }" data="coalPriceWithTax" title="含税煤价" type="numeric" width="100" />
            <VHColumn :numericFormat="{ pattern: '0.00' }" data="coalPriceNoTax" title="不含税煤价" type="numeric" width="130" />
            <VHColumn :numericFormat="{ pattern: '0.00' }" data="transportPriceNoTax" title="不含税运费" type="numeric" width="130" />
            <VHColumn :numericFormat="{ pattern: '0.00' }" data="transportCostP1" title="路耗1%" type="numeric" width="130" />
            <VHColumn :numericFormat="{ pattern: '0.00' }" data="factoryPriceNoTax" title="不含税进厂价" type="numeric" width="130" />
            <VHColumn data="mt" title="水份%" type="numeric" width="80">
              <template #editor="{ value, onChange }">
                <el-input-number v-model="value" :max="100" :min="0" :step="0.1" />
              </template>
            </VHColumn>
            <VHColumn :numericFormat="{ pattern: '0.00' }" data="factorDryBasisCost" title="进厂干基成本" type="numeric" width="130" />
          </VhTable>
        </SnFormCard>
      </el-form>
    </SnModal>
  </div>
</template>

<script>
import TipModal from '@/utils/modal'
import { deepClone, FetchData } from '@/utils'
import { VhTable, VHColumn } from '@/components/ExcelTable'
import { getDate } from '@/utils/dateUtils'
import { INDEX_NUM_FORMAT } from '@/const'

export default {
  inheritAttrs: false,
  components: {
    VhTable,
    VHColumn
  },
  data() {
    return {
      form: {},
      rules: {},
      fullscreen: false
    }
  },
  computed: {
    INDEX_NUM_FORMAT() {
      return INDEX_NUM_FORMAT
    },
    getOptMap() {
      return {
        create: '新增',
        update: '修改',
        view: '查看'
      }
    },
    _value: {
      set(val) {
        this.$emit('input', val)
      },
      get() {
        return this.value
      }
    },
    title() {
      return this.getOptMap[this.optName]
    },
    isViewStatus() {
      return this.optName === 'view'
    },
    isUpdateStatus() {
      return this.optName === 'update'
    },
    isCreateStatus() {
      return this.optName === 'create'
    }
  },
  props: {
    useApi: {
      type: Boolean,
      default: false
    },
    getApi: {
      type: Function
    },
    optName: {
      type: String,
      require: true
    },
    value: {
      type: Boolean,
      require: true
    },
    record: {
      type: Object,
      default() {
        return {}
      }
    },
    model: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    record: {
      async handler(value) {
        await this.$nextTick()
        if (this.isCreateStatus) {
          this.form = {
            ...value,
            data: Array(15)
              .fill()
              .map(() => ({}))
          }
          return true
        }
        if (this.useApi) {
          if (!value.id) throw new Error('使用useApi时record必须包含id字段')
          const impFunc = this.getApi ? this.getApi : this.model.get
          const form = await FetchData(impFunc, value.id, {})
          this.form = this.getFormData(form)
          return true
        }
        this.form = this.getFormData(value)
      },
      immediate: true
    }
  },
  created() {
    this.getSelectList()
  },
  methods: {
    beforePaste(data, coords) {
      data.forEach((row) => {
        // 处理mt
        const currentIndex = row.length - 2
        row[currentIndex] = parseFloat(row[currentIndex] || 0)
      })

      function fillData(targetData, start = 0) {
        const result = []
        let current = null
        for (const row of targetData) {
          if (row[start] !== null) {
            current = row[start]
          }
          if (current !== null) {
            const newRow = [...row.slice()]
            newRow[start] = current
            result.push(newRow)
          }
        }
        result.forEach((v, index) => {
          targetData[index] = v
        })
      }

      const startCol = coords[0]?.startCol

      if (startCol === 0) {
        fillData(data, 0)
        fillData(data, 1)
      }
      if (startCol === 1) {
        fillData(data, 0)
      }
    },
    async setTableSetting() {
      const settingConfig = () => {
        if (!this.isCreateStatus) return {}
        return {
          // fixedColumnsLeft: 2,
          contextMenu: {
            items: {
              row_above: { name: '向上插入一行' },
              row_below: { name: '向下插入一行' },
              remove_row: { name: '删除行' },
              clear_custom: {
                name: '清空所有单元格数据',
                callback() {
                  this.clear()
                }
              }
            }
          }
        }
      }

      this.$refs.excelTable.getHotInstance().updateSettings({
        ...settingConfig(),

        cells(row, col) {
          const cellProperties = {
            className: 't-ellipsis'
          }
          return cellProperties
        }
      })
    },

    async getSelectList(code, target) {},

    getFormData(form) {
      const { ...rest } = form
      return {
        ...rest,
        data: rest.data.map((v) => {
          return {
            ...v,
            assayDate: getDate(v.assayDate) // 日期格式化
          }
        })
      }
    },

    getSubmitForm() {
      const submitForm = deepClone(this.form)
      const { ...rest } = submitForm
      const { getData, getColumns, getFormatEditData } = this.$refs.excelTable
      return {
        ...rest,
        data: getFormatEditData(getData(), getColumns())
      }
    },

    async cancel() {
      this.form = {}
      this.$refs['form'].resetFields()
      return false
    },

    async ok() {
      await this.$refs['form'].validate()
      await this.model.save(this.getSubmitForm(), this.optName)
      TipModal.msgSuccess(`${this.title}成功`)
      this.$emit('ok')
      return this.cancel()
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  width: 100%;
  height: 100%;
}
</style>
