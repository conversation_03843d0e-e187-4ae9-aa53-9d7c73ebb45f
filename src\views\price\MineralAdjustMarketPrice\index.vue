<template>
  <div class="app-container">
    <SnProTable :ref="pageRef" :actions="actions" :afterFetch="afterFetch" :model="model"
                :spanMethod="spanMethod">
      <template #opt="{ row }">
        <!--        @click="handleUpdate(row)"-->
        <!--        <el-button type="text">对比</el-button>-->
      </template>
    </SnProTable>

    <FormModal
      v-if="addInfo.visitable"
      v-model="addInfo.visitable"
      :model="model"
      :optName="addInfo.optName"
      :record="addInfo.record"
      @ok="getList"
    />
  </div>
</template>

<script>
import model from './model'
import FormModal from './components/FormModal.vue'
import TipModal from '@/utils/modal'
import _ from 'lodash'
import {createPermissionMap} from '@/utils'
import SnProTable from '@/components/Common/SnProTable/index.vue'

export default {
  name: 'MineralAdjustMarketPrice',
  components: {
    SnProTable,
    FormModal
  },
  data() {
    return {
      model,
      pageRef: 'page',
      permissions: createPermissionMap('mineralAdjustMarketPrice'),
      addInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      }
    }
  },
  computed: {
    actions() {
      return [
        // {
        //   type: 'add',
        //   text: '新增',
        //   hasPermission: [this.permissions.save],
        //   onClick: async (item) => {
        //     this.setModal('addInfo')
        //   }
        // }
      ]
    }
  },
  methods: {
    afterFetch(data = {}) {
      const list = (data.records = data.records || [])
      const createIterateeFunc = (keys = []) => {
        return (item) => {
          return keys.map((key) => item[key]).join('-')
        }
      }

      // 根据日期和煤种分组
      const dateFunc = createIterateeFunc(['date'])
      const groupByDateMap = _.groupBy(list, dateFunc)

      const dateCoalTypeFunc = createIterateeFunc(['date', 'insideCoalCategory'])
      const groupByDateCoalTypeMap = _.groupBy(list, dateCoalTypeFunc)

      let result = []
      Object.values(groupByDateCoalTypeMap).forEach((item) => {
        result.push(...item)
      })

      console.log(result, 'result')

      result.forEach((item) => {
        const dateRowspanKey = dateFunc(item)
        item['dateRowspan'] = groupByDateMap[dateRowspanKey].length
        groupByDateMap[dateRowspanKey] = []
        const dateCoalTypeRowspanKey = dateCoalTypeFunc(item)
        item['dateCoalTypeRowspan'] = groupByDateCoalTypeMap[dateCoalTypeRowspanKey].length
        groupByDateCoalTypeMap[dateCoalTypeRowspanKey] = []
      })

      return {
        ...data,
        records: result
      }
    },

    spanMethod({row, column}) {
      // if (['date'].includes(column.property)) {
      //   return {
      //     rowspan: row.dateRowspan || 0,
      //     colspan: 1
      //   }
      // }
      if (['insideCoalCategory', 'date'].includes(column.property)) {
        return {
          rowspan: row.dateCoalTypeRowspan || 0,
          colspan: 1
        }
      }
    },

    // 新增时record置为空
    setModal(target, optName = 'create', record = {}) {
      this[target].visitable = true
      this[target].optName = optName
      if (optName) this[target].record = {...record}
    },

    async handleRemove({id}) {
      try {
        await this.model.remove({id, useConfirm: true})
        TipModal.msgSuccess(`删除成功`)
        this.getList()
      } catch (error) {
      }
    },
    async handleUpdate(row) {
      try {
        this.setModal('addInfo', 'update', row)
      } catch (error) {
        console.log(error, 'error')
      }
    },
    async handleView(row) {
      try {
        const {data} = await this.model.get(row.id)
        this.setModal('addInfo', 'view', data)
      } catch (error) {
        console.log(error, 'error')
      }
    },
    getList() {
      this.$refs[this.pageRef].getList()
    }
  }
}
</script>

<style lang="scss"></style>
