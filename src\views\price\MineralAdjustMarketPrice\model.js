// eslint-disable-next-line

import BaseModel, {TableConfig, FormConfig} from '@/components/Common/SnProTable/model'

export const name = `/a/coalSourceInside`

const createFormConfig = () => {
  return {
    fieldMapToTime: [['_dateRange', ['beginDate', 'endDate'], 'YYYY-MM-DD']], // 这里直接格式化
    filters: [
      {
        label: '日期',
        prop: '_dateRange',
        component: 'SnDateRanger',
        defaultValue: [],
        componentProp: {
          valueFormat: 'yyyy-MM-dd'
        },
        style: {
          width: '230px'
        }
      },
      {
        label: '品名',
        labelWidth: '40px',
        prop: 'coalName'
      }
    ]
  }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
  return {
    showOpt: false, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: false, // 显示选择列
    columns: [
      {label: '日期', prop: 'date', minWidth: 80, format: 'date|YYYY-MM-DD'},
      {label: '煤种', prop: 'insideCoalCategory', minWidth: 80},
      {label: '品名', prop: 'coalShortname', minWidth: 100},
      {label: '含税煤价', prop: 'activePriceCoalPriceWithTax', minWidth: 100},
      {label: '不含税煤价', prop: 'activePriceCoalPriceNoTax', minWidth: 100},
      {label: '不含税运费', prop: 'activePriceTransportPriceNoTax', minWidth: 100},
      {label: '路耗1%', prop: 'activePriceTransportCostP1', minWidth: 100},
      {label: '不含税进厂价', prop: 'activePriceFactoryPriceNoTax', minWidth: 100},
      {label: '进厂干基成本', prop: 'activePriceFactorDryBasisCost', minWidth: 100}
    ]
  }
}

class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }

  page(query) {
    return super.request({
      url: `${name}/page-by-coal-category`,
      method: 'get',
      params: query
    })
  }

  // page(query) {
  //   return super.request({
  //     url: `${name}/group-page`,
  //     method: 'get',
  //     params: query
  //   })
  // }

  save(data, optName = 'create') {
    return super.request({
      url: `${name}/batch-save`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    })
  }
}

export default new Model()
