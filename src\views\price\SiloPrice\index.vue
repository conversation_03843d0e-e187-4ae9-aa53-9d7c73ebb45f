<template>
  <div class="app-container">
    <SnProTable :ref="pageRef" :actions="actions" :model="model">
      <template #ringComparison="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <div :style="{ color: row.ringComparison * 1 === 0 ? 'normal' : row.ringComparison < 0 ? '#33cad9' : '#ff726b' }">
              {{ row.ringComparison }}
            </div>
          </template>
        </el-table-column>
      </template>
    </SnProTable>
  </div>
</template>

<script>
import model from './model'
import SnProTable from '@/components/Common/SnProTable/index.vue'

export default {
  name: 'SiloPrice',
  components: {SnProTable},
  data() {
    return {
      model,
      pageRef: 'page',
      permissions: {
        save: '*',
        remove: '*'
      },
      addInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      }
    }
  },
  computed: {
    actions() {
      return []
    }
  },
  methods: {
    getList() {
      this.$refs[this.pageRef].getList()
    }
  }
}
</script>

<style lang="scss"></style>
