<template>
  <div class="app-container">
    <SnProTable :ref="pageRef" :actions="actions" :afterFetch="afterFetch" :model="model" :spanMethod="spanMethod"></SnProTable>
  </div>
</template>

<script>
import model from './model'
import _ from 'lodash'
import { createPermissionMap } from '@/utils'
import SnProTable from '@/components/Common/SnProTable/index.vue'

export default {
  name: 'StockPrice',
  components: {SnProTable},
  data() {
    return {
      model,
      pageRef: 'page',
      permissions: createPermissionMap('stockPrice'),
      addInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      }
    }
  },
  computed: {
    actions() {
      return []
    }
  },
  methods: {
    afterFetch(data = {}) {
      const list = (data.records = data.records || [])
      const createIterateeFunc = (keys = []) => {
        return (item) => {
          return keys.map((key) => item[key]).join('-')
        }
      }

      // 根据日期和煤种分组
      const dateFunc = createIterateeFunc(['date'])
      const groupByDateMap = _.groupBy(list, dateFunc)

      const dateCoalTypeFunc = createIterateeFunc(['date', 'coalType'])
      const groupByDateCoalTypeMap = _.groupBy(list, dateCoalTypeFunc)

      list.forEach((item) => {
        const dateRowspanKey = dateFunc(item)
        item['dateRowspan'] = groupByDateMap[dateRowspanKey].length
        groupByDateMap[dateRowspanKey] = []
        const dateCoalTypeRowspanKey = dateCoalTypeFunc(item)
        item['dateCoalTypeRowspan'] = groupByDateCoalTypeMap[dateCoalTypeRowspanKey].length
        groupByDateCoalTypeMap[dateCoalTypeRowspanKey] = []
      })

      return data
    },

    spanMethod({ row, column }) {
      // if (['date'].includes(column.property)) {
      //   return {
      //     rowspan: row.dateRowspan || 0,
      //     colspan: 1
      //   }
      // }
      if (['coalType', 'date'].includes(column.property)) {
        return {
          rowspan: row.dateCoalTypeRowspan || 0,
          colspan: 1
        }
      }
    },

    getList() {
      this.$refs[this.pageRef].getList()
    }
  }
}
</script>

<style lang="scss"></style>
