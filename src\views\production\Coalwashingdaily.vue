<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <!-- <panel title="品名" type="location" :active="locationActive" :list='locationList' @locationChange="handleToLocation" /> -->

    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" otherHeight="180">

      <!-- <el-table-column label="原煤id" slot="productIdRaw" prop="productIdRaw" align="center">
        <template slot-scope="scope">
          <span class="name">{{scope.row.productIdRaw}}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="原煤矿井名" slot="mineNameRaw" prop="mineNameRaw" align="center">
        <template slot-scope="scope">
          <span class="name">{{scope.row.mineNameRaw}}</span>
        </template>
      </el-table-column>
      <el-table-column label="原煤品名" slot="productNameRaw" prop="productNameRaw" align="center">
        <template slot-scope="scope">
          <span class="name">{{scope.row.productNameRaw}}</span>
        </template>
      </el-table-column>

      <el-table-column class-name="index" label="原煤过磅" slot="chargingTest" align="center">
        <el-table-column label="车数" prop="truckCountRaw" width="100" align="center" />

        <el-table-column label="吨数" prop="weightRaw" width="100" align="center" />
      </el-table-column>

      <el-table-column class-name="index" label="原煤皮带称" slot="chargingTest" align="center">
        <el-table-column label="吨数" prop="beltWeightRaw" width="100" align="center" />
      </el-table-column>

      <el-table-column class-name="index" label="原煤铲车" slot="chargingTest" align="center">
        <el-table-column label="铲数" prop="shovelCountRaw" width="100" align="center" />
        <el-table-column label="吨数" prop="weightCalcRaw" width="100" align="center" />
      </el-table-column>

      <!-- <el-table-column label="精煤id" slot="productId" prop="productId" align="center">
        <template slot-scope="scope">
          <span class="name">{{scope.row.productId}}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="精煤品名" slot="productName" prop="productName" align="center">
        <template slot-scope="scope">
          <span class="name">{{scope.row.productName}}</span>
        </template>
      </el-table-column>
      <el-table-column label="精煤矿井名" slot="mineName" prop="mineName" align="center">
        <template slot-scope="scope">
          <span class="name"><span class="name">{{scope.row.mineName}}</span></span>
        </template>
      </el-table-column>

      <el-table-column class-name="index" label="精煤过磅" slot="chargingTest" align="center">
        <el-table-column label="车数" prop="truckCount" width="100" align="center" />
        <el-table-column label="吨数" prop="weight" width="100" align="center" />
      </el-table-column>

      <el-table-column class-name="index" label="精煤皮带称" slot="chargingTest" align="center">
        <el-table-column label="吨数" prop="beltWeight" width="100" align="center" />
      </el-table-column>

      <el-table-column class-name="index" label="精煤铲车" slot="chargingTest" align="center">
        <el-table-column label="铲数" prop="shovelCount" width="100" align="center" />
        <el-table-column label="吨数" prop="weightCalc" width="100" align="center" />
      </el-table-column>

      <el-table-column label="操作" slot="opt" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)"
                  v-if="perms[`${curd}:update`]||false">编辑</el-tag>
          <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除</el-tag>
        </template>
      </el-table-column>

    </s-curd>

    <!-- <div class="table-container">
      <el-table :data="currentTableData" border class="table" :header-cell-style="headClass">
        <el-table-column prop="productionDate" label="日期" align="center" width="150" />

        <el-table-column prop="productIdRaw" label="原煤id" align="center" width="250" />
        <el-table-column prop="productNameRaw" label="原煤品名" align="center" />
        <el-table-column label="原煤过磅" align="center" width="250">
          <el-table-column label="车数" prop="truckCountRaw" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.truckCountRaw}}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="吨数" prop="weightRaw" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.weightRaw}}</span>
              </template>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="原煤皮带称" align="center" width="250">
          <el-table-column label="吨数" prop="beltWeightRaw" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.beltWeightRaw}}</span>
              </template>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="原煤铲车" align="center" width="250">
          <el-table-column label="铲数" prop="shovelCountRaw" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.shovelCountRaw}}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="吨数" prop="weightCalcRaw" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.weightCalcRaw}}</span>
              </template>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column prop="productId" label="精煤id" align="center" />
        <el-table-column prop="productName" label="精煤品名" align="center" />

        <el-table-column label="精煤过磅" align="center" width="250">
          <el-table-column label="车数" prop="truckCount" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.truckCount}}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="吨数" prop="weight" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.weight}}</span>
              </template>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="皮带称" align="center" width="250">
          <el-table-column label="吨数" prop="beltWeight" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.beltWeight}}</span>
              </template>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="精煤铲车" align="center" width="250">
          <el-table-column label="铲数" prop="shovelCount" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.shovelCount}}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="吨数" prop="weightCalc" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.weightCalc}}</span>
              </template>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row,'update')">编辑</el-tag>
            <el-tag class="opt-btn" color="#33CAD9" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div> -->

    <el-dialog :fullscreen="screen.full" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本类型</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="productionDate">
                    <date-select v-model="entityForm.productionDate" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="原煤品名" prop="productIdRaw">
                    <select-down :value.sync="nameEntityV.active" :list="nameEntityV.options" @eventChange="handleNameEntityV" />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-titleV">原煤过磅</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="车数" prop="truckCountRaw">
                    <el-input v-model="entityForm.truckCountRaw" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="吨数" prop="weightRaw">
                    <el-input v-model="entityForm.weightRaw" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-titleV">原煤皮带称</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="吨数" prop="beltWeightRaw">
                    <el-input v-model="entityForm.beltWeightRaw" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-titleV">原煤铲车</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="铲数" prop="shovelCountRaw">
                    <el-input v-model="entityForm.shovelCountRaw" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="吨数" prop="weightCalcRaw">
                    <el-input v-model="entityForm.weightCalcRaw" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">洗煤生产数据</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="精煤品名" prop="productId">
                    <!-- :disabled="dialogStatus==='update'" -->
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-titleV">精煤过磅</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="车数" prop="truckCount">
                    <el-input v-model="entityForm.truckCount" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="吨数" prop="weight">
                    <el-input v-model="entityForm.weight" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-titleV">精煤皮带称</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="吨数" prop="beltWeight">
                    <el-input v-model="entityForm.beltWeight" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-titleV">精煤铲车</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="铲数" prop="shovelCount">
                    <el-input v-model="entityForm.shovelCount" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="吨数" prop="weightCalc">
                    <el-input v-model="entityForm.weightCalc" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col />
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button class="dialog-footer-all" @click="handleFullScreen">{{screen.full?'取消全屏':'全屏'}}</el-button>
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import { findByKeyword } from '@/api/stock'
import { listQuery } from '@/const'
import Model from '@/model/production/Coalwashingdaily'
import productModel from '@/model/product/productList'
import { getCustomerContract } from '@/api/quality'
import Cache from '@/utils/cache'
const form = {
  _id: '1'
}
export default {
  name: 'coalProductionList',
  mixins: [Mixins],
  data() {
    return {
      curd: 'coalProductionList',
      model: Model,
      form: { ...form },
      listV: [{ ...form }],
      indicators: {
        weightCalcRaw: Infinity,
        shovelCountRaw: Infinity,
        beltWeightRaw: Infinity,
        weightRaw: Infinity,
        truckCountRaw: Infinity,
        truckCount: Infinity,
        weight: Infinity,
        beltWeight: Infinity,
        shovelCount: Infinity,
        weightCalc: Infinity
      },
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'productionDate',
            component: 'date-select',
            defaultValue: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
            resetClearDefault: true,
            filter: 'filter_EQS_productionDate',
            props: {
              placeholder: '请选择日期',
              'picker-options': {
                cellClassName: (time) => {
                  const flag = this.dateList.includes(time.Format('yyyy-MM-dd'))
                  if (flag) return 'background'
                }
              }
            }
          },
          {
            prop: 'productName',
            filter: 'filter_LIKES_productName',
            component: 'select-name',
            props: { placeholder: '请选择品名', clearable: true, selecttype: 'productName' }
          }
        ]
      },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      entityForm: { ...Model.model },
      rules: {
        productionDate: { required: true, message: '请选择日期', trigger: 'blur' },
        productId: { required: true, message: '请选择精煤品名', trigger: 'change' },
        actualProductionTime: { required: true, message: '请输入实际生产时间', trigger: 'blur' },
        productIdRaw: { required: true, message: '请选择原煤品名', trigger: 'blur' }
      },
      nameEntity: { options: [], active: '' },

      nameEntityV: { options: [], active: '' },

      memoryEntity: { fields: {}, triggered: false },
      actions: [],
      tableData: [],
      nameItemEntity: [],
      // filters: { productionDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') },
      filters: {},
      timeValue: [],
      timeValue1: [],
      locationList: [],
      locationActive: 0,
      currentTableData: [],
      screen: { full: false },
      //   draft: { open: false, entityForm: { ...Model.model }, list: [{ ...form }] },
      productionDraft: { entityForm: { ...Model.model }, list: [{ ...form }] },
      dateList: []
    }
  },
  created() {
    this.getName()
    this.getContract()
    this.getFindByKeyword()
    this.getDateList()
    // this.getList()
  },
  methods: {
    clearContract() {
      this.listQuery = { ...listQuery }
      this.indicators = {
        weightCalcRaw: Infinity,
        shovelCountRaw: Infinity,
        beltWeightRaw: Infinity,
        weightRaw: Infinity,
        truckCountRaw: Infinity,
        truckCount: Infinity,
        weight: Infinity,
        beltWeight: Infinity,
        shovelCount: Infinity,
        weightCalc: Infinity
      }
      return false
    },
    // @重写方法-添加了合同搜索字段
    handleFilter(filters) {
      this.filters = { ...filters }
      this.$refs[this.curd].searchChange({ ...this.listQuery, ...filters })
    },
    // @重写方法-添加了clearContract方法
    handleFilterReset() {
      this.clearContract()
      this.$refs[this.curd].searchChange()
    },
    async getDateList(params = {}) {
      try {
        const res = await this.model.getDateList(params)
        if (res.data && res.data.length) {
          this.dateList = res.data
        } else {
          this.dateList = []
        }
      } catch (error) {
        this.dateList = []
      }
    },
    handleFullScreen() {
      this.screen.full = !this.screen.full
      const el = this.$refs.dialogStatus.$el
      const el_body = el.querySelector('.el-dialog__body')
      el_body.style.height = this.screen.full ? '85vh' : '70vh'
    },

    // handleFilter(filters) {
    //   this.filters = filters
    //   this.getList()
    // },
    // handleFilterReset() {
    //   this.filters = {}
    //   this.getList()
    // },

    async getFindByKeyword() {
      const { data } = await findByKeyword()
      this.nameItemEntity = data
    },

    // async handleUpdate(id) {
    //   this.dialogStatus = 'update'
    //   const { data } = await Model.detail(id)
    //   this.entityForm = { ...data }
    //   //   this.listV = data.mixCoalItemList
    //   console.log('111111111')
    //   //   console.log(this.listV)
    //   //   delete this.entityForm.mixCoalItemList
    //   this.dialogFormVisible = true
    // },
    async handleUpdate(item, type) {
      this.entityForm = { ...item }
      if (type == 'review') {
        this.isshow = true
        this.dialogStatus = 'review'
      } else {
        this.isshow = false
        this.dialogStatus = 'update'
      }
      this.dialogFormVisible = true
      //   // 上传所需要的配置
      //   this.uploadData.refId = item.id
      //   const { data } = await this.model.getUploadList(item.id)
      //   this.entityForm = { ...data }
      //   this.supplierEntity.value = this.entityForm.supplierId
      //   this.reviewLogList = data.reviewLogList
      //   this.uploadList = data.attachmentList
      //   let newarry = []
      //   if (data.reviewLogList.length > 0) {
      //     data.reviewLogList.forEach((item, index) => {
      //       newarry.push(item.reviewMessage)
      //     })
      //     this.entityForm.reviewMessage = newarry.toString()
      //   }
    },

    //  async getList() {
    //   this.tableData = []
    //   this.currentTableData = []
    //   this.locationList = []
    //   this.locationActive = 0
    //   const { data } = await Model.page(this.filters)
    //   console.log(data)
    //   if (data.length > 0) {
    //     this.tableData = data
    //     this.currentTableData = this.tableData[this.locationActive].mixCoalVoList
    //     data.forEach((item) => {
    //       this.locationList.push({ label: item.productName })
    //     })
    //   }
    // },

    // async getList() {
    //   const { data } = await Model.page(this.filters)
    //   console.log(data.records)
    //   this.currentTableData = data.records

    //   // this.$refs[this.curd].$emit('refresh')
    // },

    // checkParams(
    //   list,
    //   rule = [
    //     'shovelCount',
    //     'shovelWeight',
    //     'weight',
    //     'productName',
    //     'productId',
    //     'productCode',
    //     'productIdRaw',
    //     'productNameRaw',
    //     'productCodeRaw',
    //     'truckCountRaw',
    //     'shovelCountRaw',
    //     'weightCalcRaw',
    //     'weightRaw',
    //   ]
    // ) {
    //   console.log(list)
    //   for (const item of list) {
    //     for (const [key, val] of Object.entries(item)) {
    //       if (rule.includes(key)) continue
    //       if (val === '') {
    //         this.$message({ message: `请检查参数${key}`, type: 'warning' })
    //         return false
    //       }
    //     }
    //   }
    //   return true
    // },
    async saveEntityForm(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        this.entityFormLoading = true
        const res = await Model.save({ ...this.entityForm })
        this.entityFormLoading = false

        this.resetVariant()
        this.getList()
        // this.clearDraft()
        // this.draft.open = false
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
      }
    },
    async handleDel(row) {
      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await Model.deleteId(row.id)
          if (res) {
            this.$message({ type: 'success', message: '删除成功!' })
            this.getList()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },

    async getContract() {
      const { data } = await getCustomerContract()
      // 供应商名称返回id用来查询
      this.contractEntity.options = this.formatContract({ data, key: { customerName: 'displayName', customerId: 'id' } })
    },
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
        })
        this.nameEntityV.options = this.nameEntity.options
      } catch (error) {}
    },

    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.nameEntityV.active = ''
        this.contractEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },

    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    handleNameEntityV(val) {
      this.entityForm.productNameRaw = val
    },
    headClass() {
      // border:none;
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;'
    },

    clearDraft() {
      this.productionDraft = { entityForm: { ...Model.model }, list: [{ ...form }] }
      Cache.remove('productionDraft')
    },
    /**
     * @status {boolean} 真是保存记录，假是展示
     */
    createDraft(status) {
      if (status || this.dialogStatus === 'create') {
        return {
          entityForm: { ...this.entityForm }
        }
      } else {
        this.entityForm = { ...this.productionDraft.entityForm }
      }
    },
    // async handleCreate(type = true) {
    //   if (type) {
    //     this.dialogStatus = 'create'
    //     this.dialogFormVisible = true
    //     this.entityForm = { ...this.entityForm }
    //     const productionDraft = Cache.get('productionDraft')
    //     if (productionDraft) {
    //       this.productionDraft = productionDraft
    //       this.createDraft(false)
    //     } else {
    //       this.productionDraft = this.createDraft(true)
    //       Cache.set('productionDraft', this.productionDraft)
    //     }
    //   } else {
    //     Cache.remove('productionDraft')
    //     this.productionDraft = this.createDraft(true)
    //     Cache.set('productionDraft', this.productionDraft)
    //   }
    // },
    contrastField(a, b) {
      // if (a === null) return true
      a = JSON.parse(JSON.stringify(a))
      b = JSON.parse(JSON.stringify(b))
      a.list.forEach((item) => {
        for (const [k] of Object.entries(item)) {
          if (k.includes('_')) {
            item[k] = ''
          }
        }
      })
      b.list.forEach((item) => {
        for (const [k] of Object.entries(item)) {
          if (k.includes('_')) {
            item[k] = ''
          }
        }
      })
      const strA = JSON.stringify(a)
      const strB = JSON.stringify(b)
      return strA === strB
    },
    handleClose() {
      this.resetVariant()
      // const productionDraft = Cache.get('productionDraft')
      // if (this.dialogStatus === 'create') {
      //   const new_productionDraft = this.createDraft(true)
      //   const status = this.contrastField(productionDraft, new_productionDraft)
      //   if (productionDraft === null || !status) {
      //     this.$confirm('当前未保存, 是否保存草稿?', '提示', {
      //       confirmButtonText: '不保存',
      //       cancelButtonText: '保存草稿',
      //       type: 'info',
      //       cancelButtonClass: 'btn-custom-save',
      //       confirmButtonClass: 'btn-custom-cancel',
      //     })
      //       .then(() => {
      //         this.resetVariant()
      //       })
      //       .catch(() => {
      //         Cache.set('productionDraft', new_productionDraft)
      //         this.resetVariant()
      //       })
      //   } else {
      //     this.resetVariant()
      //   }
      // } else {
      //   this.resetVariant()
      // }
    }
  },
  watch: {
    // 'entityForm.startTime'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.startTime1'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.startTime2'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.stopTime'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.stopTime1'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.stopTime2'(v) {
    //   this.handlePickerChange(false)
    // },
    'entityForm.contractId'(id) {
      const value = this.filterContractItem(id, 'contractEntity')
      this.contractEntity.active = value
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      if (this.dialogStatus === 'create') this.entityForm.productName = form.productName || ''
      this.entityForm.contractCode = form.customerName || ''
      this.entityForm.contractId = form.customerId || ''
      this.entityForm.contractName = form.name || ''
    },

    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code || ''
        this.entityForm.productId = item.id || ''
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
      }
    },
    'entityForm.productNameRaw'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntityV.active = item.name
        this.entityForm.productCodeRaw = item.code || ''
        this.entityForm.productIdRaw = item.id || ''
      } else {
        this.nameEntityV.active = ''
        this.entityForm.productCodeRaw = ''
        this.entityForm.productIdRaw = ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
.table-container {
  height: 80vh;
  overflow-y: auto !important;
  background: #fff;
  box-sizing: border-box;
  padding-bottom: 30px;
  // <!-- height:84rem;: auto;background:#fff; -->
}
::v-deep .el-icon-date {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-icon-time {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}
// @media screen and (max-width:480px;) {
//   height: 80vh !important;
// }

::v-deep .el-input__prefix {
  right: 5px;
  left: auto;
}

::v-deep .el-input--prefix .el-input__inner {
  padding-left: 10px;
}
::v-deep .el-table__fixed-right::before {
  width: 0;
}

.dialog {
  width: 980px;
  height: 740px;
}
.dialog-footer {
  margin-right: 44px;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px #f1f1f2;
  padding: 10px;
  &-all {
    font-size: 14px;
    margin-left: 34px;
    // margin-right: auto;
    flex: 0 0 58px;
    color: #ff9639;
    background: #fff;
    border: 1px solid #ff9639;
    &:hover {
      background: transparent;
    }
    &:nth-of-type(1) {
      // margin-left: 15px;
      margin-right: auto;
    }
  }
  &-btns {
    width: 85px;
    height: 35px;
    border: 1px solid #2f79e8;
    background: #fff;
    color: #2f79e8;
    margin-left: 20px;
    font-size: 14px;
    &:last-of-type {
      border: 1px solid #d5d5d5;
      color: #747474;
    }
  }
}

.form-layout {
  padding: 0 20px;
}

.form-title {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 17px;
    background: #2f79e8;
    height: 16px;
  }
}

.form-titleV {
  font-size: 14px;
  position: relative;
  padding: 10px;
  font-weight: 900;
}

::v-deep .el-input--mini .el-input__inner {
  height: 29.5px;
  line-height: 29.5px;
}

.opt-btn {
  cursor: pointer;
  margin: 0 3px;
  color: #fff;
  border: none;
}
::v-deep .el-form--label-top .el-form-item__label {
  padding: 0;
  font-weight: 400;
  font-size: 13px;
  color: #646464;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 13px;
}
.name {
  // color: blue;
  text-decoration: underline;
  cursor: pointer;
  transition: 0.5s all;
  &:hover,
  :focus {
    color: #3234d3;
  }
}

.excessive {
  width: 100%;
  color: red;
}

::v-deep .percent-column {
  .cell {
    top: 10px;
    height: 42px;
  }
}
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}
.column {
  display: inline-block;
  width: 100%;
  height: 20px;
}
.app-container {
  height: 100vh;
}
.table {
  width: 100%;
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
}
.el-button--export-all:focus,
.el-button--export-all:hover {
  background: #ff9639 !important;
  border-color: #fff !important;
  color: #fff !important;
}

.el-button--export-all {
  color: #ff9639;
  background-color: #fff;
  border-color: #ff9639;
  padding: 8px 13px;
  margin-top: -6px;
}
::v-deep // 合计行样式
.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  height: 50px;
  line-height: 50px;
  color: #ff9639;
  font-size: 13px;
  background: rgb(245, 246, 251);
}
::v-deep .panel-list {
  margin-left: -25px;
}

::v-deep .info {
  display: flex;
  justify-content: space-evenly;
  flex-flow: wrap;
  span {
    padding: 3px;
  }
  // justify-content: start;
  .title {
    span {
      margin-left: 5px;
      color: #f8a20f;
      font-weight: bold;
    }
  }
}
::v-deep .orange-row {
  color: #f8a20f;
  font-size: 14px;
  font-weight: bold;
  height: 60px;
}
::v-deep .row-layout {
  font-size: 14px;
  height: 60px;
}
.panel-list-item {
  height: 40px;
  padding: 5px;
}
</style>
