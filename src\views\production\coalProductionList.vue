<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <!-- <panel title="品名" type="location" :active="locationActive" :list='locationList' @locationChange="handleToLocation" /> -->

    <div class="table-container">
      <!-- :row-class-name="tableRowClassName" -->
      <el-table :data="currentTableData" element-loading-text="加载中..." v-loading="loading" :span-method="spanMethod" border
                class="table" :header-cell-style="headClass">
        <el-table-column prop="productionDate" label="日期" align="center" />
        <el-table-column prop="productName" label="品名（配煤）" align="center" />
        <el-table-column prop="contractName" label="合同" align="center" />

        <el-table-column prop="itemProductName" label="品名（入煤）" align="center" />
        <el-table-column prop="percent" label="配比" align="center" />
        <!-- <el-table-column prop="shovelCount" label="铲数" align="center" /> -->
        <el-table-column prop="itemWeight" label="吨数" align="center" />
        <!-- <el-table-column prop="pileCount" label="堆数" align="center" /> -->
        <el-table-column prop="weight" label="吨数" align="center" />

        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row.id)"
                    v-if="perms[`${curd}:update`]||false">编辑</el-tag>
            <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- <el-dialog :fullscreen="screen.full" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本类型</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="productionDate">
                    <date-select v-model="entityForm.productionDate" clearable :disabled="dialogStatus==='update'"
                                 :isset="true" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                 :disabled="dialogStatus==='update'" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col style="margin-bottom: 20px;">
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="堆数" prop="pileCount">
                    <el-input v-model="entityForm.pileCount" @input="handleinput(entityForm)" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="吨数" prop="weight">
                    <el-input v-if="entityForm.weight==null || isNaN(entityForm.weight)" value="0" clearable disabled />
                    <el-input v-else v-model="entityForm.weight" clearable disabled />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col style="margin-bottom: 20px;">
            <div class="form-titlV form-warp">
              <span style="position: relative;top:-3px">生产数据</span>
              <el-button type="export-all" @click="handleAdd">新增配入煤</el-button>
            </div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-table :data="listV" style="width: 100%" stripe :header-cell-style="headClass">
                    <el-table-column label="品名" align="center">
                      <template slot-scope="scope">
                        <select-down :value.sync="scope.row.productName" :code.sync="scope.row.productCode"
                                     @eventChange="handleNameEntityV(scope.row)" :stock.sync="scope.row._stock"
                                     :id.sync="scope.row.productId" :list="nameItemEntity" />
                      </template>
                    </el-table-column>
                    <el-table-column label="铲数" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.shovelCount" clearable @input="handleBlur" />
                      </template>
                    </el-table-column>
                    <el-table-column label="吨数/铲" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.shovelWeight" clearable @input="handleBlur" />
                      </template>
                    </el-table-column>
                    <el-table-column label="吨数" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.weight" clearable @input="handleBlurVV" disabled />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template slot-scope="scope">
                        <el-tag class="opt-btn" color="#2f79e8" @click="handleRemove(scope.row)">删除</el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col />
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button class="dialog-footer-all" style="  color: #adadad;
  border: 1px solid #adadad;" @click="handleFullScreen">{{screen.full?'取消全屏':'全屏'}}</el-button>
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog> -->

    <el-dialog :fullscreen="screen.full" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本类型</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="productionDate">
                    <date-select v-model="entityForm.productionDate" clearable :isset="true" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同" prop="contractId">
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 filterable clearable placeholder="请选择合同" style="width:100%" />
                  </el-form-item>
                </el-col>

              </el-row>
            </div>
          </el-col>
          <el-col style="margin-bottom: 20px;">
            <!-- <div class="form-title">人员信息</div> -->
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="吨数" prop="weight">
                    <el-input v-model="entityForm.weight" clearable @blur="handleBlur" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col style="margin-bottom: 20px;">
            <div class="form-titlV form-warp">
              <span style="position: relative;top:-3px">生产数据</span>
              <el-button type="export-all" @click="handleAdd">新增配入煤</el-button>
            </div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-table :data="listV" style="width: 100%" stripe :header-cell-style="headClass">
                    <el-table-column label="品名" align="center">
                      <template slot-scope="scope">
                        <select-down :value.sync="scope.row.productName" :code.sync="scope.row.productCode"
                                     @eventChange="handleNameEntityV(scope.row)" :stock.sync="scope.row._stock"
                                     :id.sync="scope.row.productId" :list="nameItemEntity" />
                      </template>
                    </el-table-column>
                    <el-table-column label="配比" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.percent" clearable @blur="handleBlur" />
                      </template>
                    </el-table-column>
                    <el-table-column label="吨数" align="center">
                      <template slot-scope="scope">
                        <el-input v-if="scope.row.weight==null || isNaN(scope.row.weight)" value="0" clearable />
                        <el-input v-else v-model="scope.row.weight" clearable disabled />
                        <!-- <el-input v-model="scope.row.weight" clearable @input="handleBlurVV" disabled /> -->
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template slot-scope="scope">
                        <el-tag class="opt-btn" color="#2f79e8" @click="handleRemove(scope.row)">删除</el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col />
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button class="dialog-footer-all" style="  color: #adadad;
  border: 1px solid #adadad;" @click="handleFullScreen">{{screen.full?'取消全屏':'全屏'}}</el-button>
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import { findByKeyword } from '@/api/stock'
import Model from '@/model/production/productionList'
import productModel from '@/model/product/productList'
import { getCustomerContract } from '@/api/quality'
import Cache from '@/utils/cache'
const form = {
  id: '1',
  percent: '',
  weight: '',
  productName: '',
  productId: '',
  productCode: ''
}
export default {
  name: 'coalProductionList',
  mixins: [Mixins],
  data() {
    return {
      curd: 'coalProductionList',
      model: Model,
      form: { ...form },
      listV: [{ ...form }],
      filterOption: {
        showMore: true,
        columns: [
          // {
          //   prop: 'productionDate',
          //   component: 'date-select',
          //   // defaultValue: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
          //   defaultValue: new Date().Format('yyyy-MM-dd'),
          //   resetClearDefault: true,
          //   filter: 'productionDate',
          //   props: {
          //     placeholder: '请选择日期',
          //     'picker-options': {
          //       cellClassName: (time) => {
          //         const flag = this.dateList.includes(time.Format('yyyy-MM-dd'))
          //         if (flag) return 'background'
          //       },
          //     },
          //   },
          // },

          {
            prop: 'beginDate',
            component: 'date-select',
            defaultValue: new Date().Format('yyyy-MM-dd'),
            resetClearDefault: true,
            filter: 'beginDate',
            props: {
              placeholder: '开始日期',
              'picker-options': {
                cellClassName: (time) => {
                  const flag = this.dateList.includes(time.Format('yyyy-MM-dd'))
                  if (flag) return 'background'
                }
              }
            }
          },
          {
            prop: 'endDate',
            component: 'date-select',
            defaultValue: new Date().Format('yyyy-MM-dd'),
            resetClearDefault: true,
            filter: 'endDate',
            props: {
              placeholder: '结束日期',
              'picker-options': {
                cellClassName: (time) => {
                  const flag = this.dateList.includes(time.Format('yyyy-MM-dd'))
                  if (flag) return 'background'
                }
              }
            }
          },

          {
            prop: 'productName',
            component: 'select-ProductName',
            resetClearDefault: true,
            filter: 'keyword',
            props: { placeholder: '请选择品名', clearable: true, istype: 'pei', iconisshow: false }
          }
        ]
      },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      entityForm: { ...Model.model },
      rules: {
        productionDate: { required: true, message: '请选择日期', trigger: 'blur' },
        // productName: { required: true, message: '请选择品名', trigger: 'change' },
        // pileCount: { required: true, message: '请输入堆数', trigger: 'blur' },
        weight: { required: true, message: '请输入吨数', trigger: 'blur' }
      },
      nameEntity: { options: [], active: '' },
      memoryEntity: { fields: {}, triggered: false },
      actions: [],
      tableData: [],
      nameItemEntity: [],
      filters: { productionDate: '', keyword: '' },

      timeValue: [],
      timeValue1: [],
      locationList: [],
      locationActive: 0,
      currentTableData: [],
      screen: { full: false },
      draft: { open: false, entityForm: { ...Model.model }, list: [{ ...form }] },
      productionDraft: { entityForm: { ...Model.model }, list: [{ ...form }] },
      dateList: [],
      loading: false
    }
  },
  created() {
    this.getName()
    this.getContract()
    this.getList()
    // this.getFindByKeyword()
    this.getDateList()
  },
  watch: {
    // 'entityForm.startTime'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.startTime1'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.startTime2'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.stopTime'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.stopTime1'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.stopTime2'(v) {
    //   this.handlePickerChange(false)
    // },
    'entityForm.contractId'(id) {
      const value = this.filterContractItem(id, 'contractEntity')
      this.contractEntity.active = value
      console.log(value)
    },
    'contractEntity.active'(value) {
      console.log(value)
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      console.log(form)
      this.entityForm = { ...this.entityForm, productName: form.productName, productId: form.productId }
      this.nameEntity.active = form.productName || ''
      this.entityForm.contractId = form.customerId || ''
      this.entityForm.contractCode = form.customerName || ''
      this.entityForm.contractName = form.name || ''
      // this.entityForm.supplierName = form.secondParty
    },

    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code || ''
        this.entityForm.productId = item.id || ''
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
      }
    }
  },
  methods: {
    async getDateList(params = {}) {
      try {
        const res = await this.model.getDateList(params)
        if (res.data && res.data.length) {
          this.dateList = res.data
          // console.log(this.dateList)
        } else {
          this.dateList = []
        }
      } catch (error) {
        this.dateList = []
      }
    },
    handleFullScreen() {
      this.screen.full = !this.screen.full
      const el = this.$refs.dialogStatus.$el
      const el_body = el.querySelector('.el-dialog__body')
      el_body.style.height = this.screen.full ? '85vh' : '70vh'
    },

    // handleToLocation(index) {
    //   this.locationActive = index
    //   this.currentTableData = this.tableData[this.locationActive].mixCoalVoList
    //   // this.setTime()
    // },

    handleFilter(filters) {
      this.filters = filters
      this.getList()
    },
    handleFilterReset() {
      this.filters = {}
      this.getList()
    },

    async handleUpdate(id) {
      this.dialogStatus = 'update'
      const { data } = await Model.detail(id)
      this.entityForm = { ...data }
      this.listV = data.mixCoalItemList
      delete this.entityForm.mixCoalItemList
      this.dialogFormVisible = true
    },

    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2 || columnIndex === 6 || columnIndex === 7) {
        let spanArr = this.getSpanArr(this.currentTableData)
        // 页面列表上 表格合并行 -> 第几列(从0开始)
        // 需要合并多个单元格时 依次增加判断条件即可
        // 数组存储的数据 取出
        const _row = spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      } else {
        //不可以return {rowspan：0， colspan: 0} 会造成数据不渲染， 也可以不写else，eslint过不了的话就返回false
        return false
      }
    },
    // 处理合并行的数据
    getSpanArr: function (data) {
      let spanArr = []
      let pos = ''
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          spanArr.push(1)
          pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].id === data[i - 1].id) {
            spanArr[pos] += 1
            spanArr.push(0)
          } else {
            spanArr.push(1)
            pos = i
          }
        }
      }
      return spanArr
    },

    // spanMethod({ row, column, rowIndex, columnIndex }) {
    //   console.log(column)
    //   console.log(row.id)
    //   // // 日期
    //   if ([0].includes(columnIndex)) {
    //     if (rowIndex === 0) {
    //       return {
    //         rowspan: this.currentTableData.length - 1,
    //         colspan: 1,
    //       }
    //     } else {
    //       return {
    //         rowspan: 0,
    //         colspan: 0,
    //       }
    //     }
    //   } else if ([1].includes(columnIndex)) {
    //     if (rowIndex === 0) {
    //       return {
    //         rowspan: this.currentTableData.length - 1,
    //         colspan: 1,
    //       }
    //     } else {
    //       return {
    //         rowspan: 0,
    //         colspan: 0,
    //       }
    //     }
    //   } else if ([2].includes(columnIndex)) {
    //     const index = []
    //     this.currentTableData.forEach((item, i) => {
    //       if (item.itemProductName === row.itemProductName) {
    //         index.push(i)
    //       }
    //     })
    //     if (index.length <= 1) {
    //       return [rowIndex === this.currentTableData.length - 1 ? 0 : 1, 1]
    //     }
    //     if (index[0] === rowIndex) {
    //       return [index.length, 0]
    //     } else {
    //       return [0, 0]
    //     }
    //   } else if ([5, 6, 7].includes(columnIndex)) {
    //     if (rowIndex === 0) {
    //       return {
    //         rowspan: this.currentTableData.length - 1,
    //         colspan: 1,
    //       }
    //     } else {
    //       return {
    //         // 如果不是最后一行则合并
    //         rowspan: rowIndex === this.currentTableData.length - 1 ? 1 : 0,
    //         colspan: 0,
    //       }
    //     }
    //   } else if (rowIndex === this.currentTableData.length - 1) {
    //     return [0, 0]
    //   }
    // },

    async getList() {
      this.tableData = []
      this.currentTableData = []
      this.locationList = []
      this.locationActive = 0
      this.loading = true
      const { data } = await Model.page(this.filters)
      if (data.length > 0) {
        this.tableData = data
        // console.log(data)
        for (var i = 0; i < data.length; i++) {
          // console.log(data[i])
          for (var j = 0; j < data[i].mixCoalVoList.length; j++) {
            let obj = {}
            obj = data[i].mixCoalVoList[j]
            this.currentTableData.push(obj)
            console.log(this.currentTableData)
            this.loading = false
          }
        }

        // console.log(this.currentTableData)

        // this.currentTableData = this.tableData[this.locationActive].mixCoalVoList
        // console.log(this.currentTableData)
        // data.forEach((item) => {
        //   this.locationList.push({ label: item.productName })
        // })
      } else {
        this.loading = false
      }
    },

    async saveEntityForm(entityForm) {
      if (isNaN(this.entityForm.weight)) {
        this.entityForm = { ...this.entityForm, weight: 0 }
        return
      }
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.listV.length) {
          // let sum = 0
          const list = []
          this.listV.forEach((item) => {
            let obj = {}
            obj.percent = item.percent
            obj.weight = item.weight
            obj.productName = item.productName
            obj.productId = item.productId
            obj.productCode = item.productCode
            obj.id = item.id
            list.push({ ...obj })
          })
          if (!this.checkParams(list)) return false
          this.entityFormLoading = true
          const res = await Model.save({ ...this.entityForm, mixCoalItemList: list })
          this.entityFormLoading = false
          if (res) {
            this.resetVariant()
            this.getList()
            this.clearDraft()
            this.draft.open = false
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else {
          this.$message({ message: '生产数据不能为空', type: 'warning' })
        }
      }
    },

    checkParams(list, rule = ['percent', 'weight', 'productName', 'productId', 'productCode', 'id']) {
      for (const item of list) {
        for (const [key, val] of Object.entries(item)) {
          // if (rule.includes(key)) continue
          // console.log(val)
          if (val === '' || val === undefined || val === null) {
            this.$message({ message: `请检查参数${key}`, type: 'warning' })
            return false
          }
        }
      }
      return true
    },
    async getContract() {
      const { data } = await getCustomerContract()
      // 供应商名称返回id用来查询
      this.contractEntity.options = this.formatContract({ data, key: { customerName: 'displayName', customerId: 'id' } })
    },

    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameItemEntity = data.records
        // console.log(data.records)
        this.nameEntity.options = data.records.map((item) => {
          return {
            name: item.name,
            id: item.id,
            code: item.code,
            coalCategory: item.coalCategory,
            shovelWeight: item.shovelWeight
          }
        })
      } catch (error) {}
    },

    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.contractEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.listV = []
      this.handleAdd()
    },

    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    handleNameEntity(val) {
      this.entityForm = { ...this.entityForm, productName: val }
    },
    handleNameEntityV(row) {
      let data = this.listV
      setTimeout(() => {
        for (var i = 0; i < data.length; i++) {
          let ff = data[i]
          if (row.id == ff.id) {
            const itemf = this.nameItemEntity.find((item) => item.name == row.productName)
            // console.log(itemf)
            ff.productId = itemf.id
            ff.productCode = itemf.code
            // ff.shovelWeight = itemf.shovelWeight //品名里面的单铲重量
            this.$set(data, i, ff)
          }
        }
      }, 300)
    },

    handleBlur() {
      let sum = 0
      // 第一步，计算总数
      this.listV.forEach((item, index) => {
        if (item.percent && this.entityForm.weight) {
          console.log(item.percent)
          sum += Number(item.percent)
          console.log(sum)
        } else {
          item.weight = ''
        }
      })
      // 第二步，计算weight
      this.listV.forEach((item, index) => {
        if (item.percent && this.entityForm.weight) {
          item.weight = ((item.percent / sum) * this.entityForm.weight).toFixed(2)
        } else {
          item.weight = ''
        }
        this.$set(this.listV, index, item)
      })
      this.handleBlurVV()
    },
    
    handleBlurVV() {
      if (this.entityForm.weight) {
        let total = 0
        this.listV.forEach((item, index) => {
          if (item.weight) {
            total += parseFloat(item.weight)
          }
        })
        // if (total != '' || total != null) {
        if (total) {
          this.entityForm.weight = total.toFixed(2)
        }
      }
    },

    // 小数相加计算（建议小数精确值不超过16位）
    addNum(num1, num2) {
      let sq1, sq2, multiple
      sq1 = getLength(num1)
      sq2 = getLength(num2)

      multiple = Math.pow(10, Math.max(sq1, sq2) + 1)

      return (num1 * multiple + num2 * multiple) / multiple
    },

    // 获取小数位数
    getLength(num) {
      let sq
      try {
        const numString = num.toString()
        if (numString.search('e') !== -1) {
          const length = parseInt(numString.split('e')[1])
          sq = Math.abs(length)
        } else {
          sq = num.toString().split('.')[1].length
        }
      } catch {
        sq = 0
      }
      return sq
    },

    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },
    handleAdd() {
      const form = { ...this.form }
      form.id = Math.random().toFixed(6).slice(-6)
      this.listV.push(form)
    },
    handleRemove({ id }) {
      const index = this.listV.findIndex((item) => item.id === id)
      if (index === -1) return false
      this.listV.splice(index, 1)
      this.handleBlurVV()
    },
    clearDraft() {
      this.productionDraft = { entityForm: { ...Model.model }, list: [{ ...form }] }
      Cache.remove('productionDraft')
    },
    /**
     * @status {boolean} 真是保存记录，假是展示
     */
    createDraft(status) {
      if (status || this.dialogStatus === 'create') {
        const list = this.listV.map((item) => item)
        return {
          entityForm: { ...this.entityForm },
          list
        }
      } else {
        this.entityForm = { ...this.productionDraft.entityForm }
        this.listV = this.productionDraft.list.map((item) => item)
      }
    },
    async handleCreate(type = true) {
      if (type) {
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
        this.entityForm = { ...this.entityForm }
        const productionDraft = Cache.get('productionDraft')
        if (productionDraft) {
          this.productionDraft = productionDraft
          this.createDraft(false)
        } else {
          this.productionDraft = this.createDraft(true)
          Cache.set('productionDraft', this.productionDraft)
        }
      } else {
        Cache.remove('productionDraft')
        this.productionDraft = this.createDraft(true)
        Cache.set('productionDraft', this.productionDraft)
      }
    },
    contrastField(a, b) {
      // if (a === null) return true
      a = JSON.parse(JSON.stringify(a))
      b = JSON.parse(JSON.stringify(b))
      a.list.forEach((item) => {
        for (const [k] of Object.entries(item)) {
          if (k.includes('_')) {
            item[k] = ''
          }
        }
      })
      b.list.forEach((item) => {
        for (const [k] of Object.entries(item)) {
          if (k.includes('_')) {
            item[k] = ''
          }
        }
      })
      const strA = JSON.stringify(a)
      const strB = JSON.stringify(b)
      return strA === strB
    },
    handleClose() {
      this.resetVariant()
      // const productionDraft = Cache.get('productionDraft')
      // if (this.dialogStatus === 'create') {
      //   const new_productionDraft = this.createDraft(true)
      //   const status = this.contrastField(productionDraft, new_productionDraft)
      //   if (productionDraft === null || !status) {
      //     this.$confirm('当前未保存, 是否保存草稿?', '提示', {
      //       confirmButtonText: '不保存',
      //       cancelButtonText: '保存草稿',
      //       type: 'info',
      //       cancelButtonClass: 'btn-custom-save',
      //       confirmButtonClass: 'btn-custom-cancel',
      //     })
      //       .then(() => {
      //         this.resetVariant()
      //       })
      //       .catch(() => {
      //         Cache.set('productionDraft', new_productionDraft)
      //         this.resetVariant()
      //       })
      //   } else {
      //     this.resetVariant()
      //   }
      // } else {
      //   this.resetVariant()
      // }
    }
  }
}
</script>
<style lang="scss" scoped>
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }

.table-container {
  height: 90vh;
  padding: 20px 20px 0 20px;
  overflow-y: auto !important;
  background: #fff;
  box-sizing: border-box;
  padding-bottom: 30px;
  // <!-- height:84rem;: auto;background:#fff; -->
}
::v-deep .el-icon-date {
  transform: scale(1.3);
  margin-top: 1px;
}
::v-deep.el-input.is-disabled .el-input__inner {
  color: #606266;
}
::v-deep .el-icon-time {
  transform: scale(1.3);
  margin-top: 1px;
}
::v-deep .dialog-footer-all {
  color: #adadad;
  border: 1px solid #adadad;
}
::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}
// @media screen and (max-width:480px;) {
//   height: 80vh !important;
// }

::v-deep .el-input__prefix {
  right: 5px;
  left: auto;
}

::v-deep .el-input--prefix .el-input__inner {
  padding-left: 10px;
}
::v-deep .el-table__fixed-right::before {
  width: 0;
}
::v-deep .el-dialog__footer {
  padding: 0px 0px 0px;
}
.dialog {
  width: 980px;
  height: 740px;
}
.dialog-footer {
  // margin-right: 44px;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px #f1f1f2;
  // padding: 10px;
  padding: 10px 44px 10px 10px;
  &-all {
    font-size: 14px;
    margin-left: 34px;
    // margin-right: auto;
    flex: 0 0 58px;
    color: #ff9639;
    background: #fff;
    border: 1px solid #ff9639;
    &:hover {
      background: transparent;
    }
    &:nth-of-type(1) {
      // margin-left: 15px;
      margin-right: auto;
    }
  }
  &-btns {
    width: 85px;
    height: 35px;
    border: 1px solid #2f79e8;
    background: #fff;
    color: #2f79e8;
    margin-left: 20px;
    font-size: 14px;
    &:last-of-type {
      border: 1px solid #d5d5d5;
      color: #747474;
    }
  }
}

.form-layout {
  padding: 0 20px;
}

.form-title {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 17px;
    background: #2f79e8;
    height: 16px;
  }
}

.form-titlV {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 11px;
    background: #2f79e8;
    height: 16px;
  }
}

::v-deep .el-input--mini .el-input__inner {
  height: 29.5px;
  line-height: 29.5px;
}

.opt-btn {
  cursor: pointer;
  margin: 0 3px;
  color: #fff;
  border: none;
}
::v-deep .el-form--label-top .el-form-item__label {
  padding: 0;
  font-weight: 400;
  font-size: 13px;
  color: #646464;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 13px;
}
.name {
  // color: blue;
  text-decoration: underline;
  cursor: pointer;
  transition: 0.5s all;
  &:hover,
  :focus {
    color: #3234d3;
  }
}

.excessive {
  width: 100%;
  color: red;
}

::v-deep .percent-column {
  .cell {
    top: 10px;
    height: 42px;
  }
}
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}
.column {
  display: inline-block;
  width: 100%;
  height: 20px;
}
.app-container {
  height: 100vh;
}
.table {
  width: 100%;
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.el-button--export-all:focus,
.el-button--export-all:hover {
  background: #ff9639 !important;
  border-color: #fff !important;
  color: #fff !important;
}

.el-button--export-all {
  color: #ff9639;
  background-color: #fff;
  border-color: #ff9639;
  padding: 5px 8px;
  margin-top: -6px;
}
::v-deep // 合计行样式
.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  height: 50px;
  line-height: 50px;
  color: #ff9639;
  font-size: 13px;
  background: rgb(245, 246, 251);
}
::v-deep .panel-list {
  margin-left: -25px;
}

::v-deep .info {
  display: flex;
  justify-content: space-evenly;
  flex-flow: wrap;
  span {
    padding: 3px;
  }
  // justify-content: start;
  .title {
    span {
      margin-left: 5px;
      color: #f8a20f;
      font-weight: bold;
    }
  }
}
::v-deep .orange-row {
  color: #f8a20f;
  font-size: 14px;
  font-weight: bold;
  height: 60px;
}
::v-deep .row-layout {
  font-size: 14px;
  height: 60px;
}
.panel-list-item {
  height: 40px;
  padding: 5px;
}
</style>
