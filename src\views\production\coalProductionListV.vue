<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <panel title="品名" type="location" :active="locationActive" :list='locationList' @locationChange="handleToLocation" />

    <div class="table-container">
      <el-table :data="currentTableData" :span-method="spanMethod" border class="table" :row-class-name="tableRowClassName"
                :header-cell-style="headClass">
        <el-table-column prop="productionDate" label="日期" align="center" width="100" />
        <el-table-column prop="productName" label="品名" align="center" width="100" />
        <el-table-column prop="contractName" label="合同名称" align="center" width="100" />
        <el-table-column prop="measuringScale" label="给料机" align="center" />
        <el-table-column prop="itemProductName" label="品名" align="center" width="120" />
        <el-table-column prop="innerCode" label="编号" align="center" width="110" />
        <el-table-column prop="measureWeight" label="计量t" align="center" />
        <el-table-column prop="measurePercent" label="计量%" align="center" />
        <el-table-column prop="mt" label="水分%" align="center" />
        <el-table-column prop="startAccumulated" label="开机累计" align="center" />
        <el-table-column prop="stopAccumulated" label="停机累计" align="center" />
        <el-table-column width="120" align="center" class-name="percent-column">
          <template #header>
            <span>设定配比/量</span>
            <div>{{headerTime[0]}}</div>
          </template>
          <el-table-column prop="setPercent1" align="center" style="display:none">
          </el-table-column>
          <el-table-column prop="setWeight1" align="center" style="display:none">
          </el-table-column>
        </el-table-column>
        <el-table-column width="120" align="center" class-name="percent-column">
          <template #header>
            <span>设定配比/量</span>
            <div>{{headerTime[1]}}</div>
          </template>
          <el-table-column prop="setPercent2" align="center">
          </el-table-column>
          <el-table-column prop="setWeight2" align="center">
          </el-table-column>
        </el-table-column>
        <el-table-column width="120" align="center" class-name="percent-column">
          <template #header>
            <span>设定配比/量</span>
            <div>{{headerTime[2]}}</div>
          </template>
          <el-table-column prop="setPercent3" align="center">
          </el-table-column>
          <el-table-column prop="setWeight3" align="center">
          </el-table-column>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row.id)"
                    v-if="perms[`${curd}:update`]||false">编辑</el-tag>
            <el-tag class="opt-btn" color="#33CAD9" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog :fullscreen="screen.full" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本类型</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="productionDate">
                    <date-select v-model="entityForm.productionDate" clearable :disabled="dialogStatus==='update'" />
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="品名" prop="productId">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                 :disabled="dialogStatus==='update'" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同名称" prop="contractName">
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 filterable style="width:100%" :disabled="dialogStatus==='update'" clearable
                                 placeholder="请选择合同" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">生产信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="开机时间" required>
                    <el-time-picker v-model="entityForm.startTime" :picker-options="{selectableRange: '00:00:00 - 23:59:59'}"
                                    :clearable="false" @change="handlePickerChange" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="停机时间" required>
                    <el-time-picker v-model="entityForm.stopTime" :picker-options="{selectableRange: '00:00:00 - 23:59:59'}"
                                    :clearable="false" @change="handlePickerChange" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="开机时间" required>
                    <el-time-picker v-model="entityForm.startTime1" :picker-options="{selectableRange: '00:00:00 - 23:59:59'}"
                                    :clearable="false" @change="handlePickerChange" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="停机时间" required>
                    <el-time-picker v-model="entityForm.stopTime1" :picker-options="{selectableRange: '00:00:00 - 23:59:59'}"
                                    :clearable="false" @change="handlePickerChange" />
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="开机时间" required>
                    <el-time-picker v-model="entityForm.startTime2" :picker-options="{selectableRange: '00:00:00 - 23:59:59'}"
                                    :clearable="false" @change="handlePickerChange" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="停机时间" required>
                    <el-time-picker v-model="entityForm.stopTime2" :picker-options="{selectableRange: '00:00:00 - 23:59:59'}"
                                    :clearable="false" @change="handlePickerChange" />
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="实际生产时间/h" prop="actualProductionTime">
                    <el-input v-model="entityForm.actualProductionTime" disabled>
                      <template slot="append">h</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col />
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable :rows="1" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">人员信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="现场指挥" prop="fieldCommand">
                    <el-input v-model="entityForm.fieldCommand" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="填表人" prop="makeupMan">
                    <el-input v-model="entityForm.makeupMan" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="审核人" prop="auditMan">
                    <el-input v-model="entityForm.auditMan" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title form-warp">
              <span>生产数据</span>
              <el-button type="export-all" @click="handleAdd">新增配入煤</el-button>
            </div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-table :data="list" style="width: 100%" stripe :header-cell-style="headClass" show-summary
                            :summary-method="getSummaries">
                    <el-table-column label="班次" width="120" align="center">
                      <template slot-scope="scope">
                        <dict-select v-model="scope.row.shift" type="b_blending_production_shift" />
                      </template>
                    </el-table-column>
                    <el-table-column label="计量秤" width="120" align="center">
                      <template slot-scope="scope">
                        <dict-select v-model="scope.row.measuringScale" type="b_b_blending_production_measuring_scale" />
                      </template>
                    </el-table-column>
                    <el-table-column label="品名" width="120" align="center">
                      <template slot-scope="scope">
                        <select-down :value.sync="scope.row.productName" :code.sync="scope.row.productCode"
                                     :stock.sync="scope.row._stock" :id.sync="scope.row.productId" :list="nameItemEntity" />
                      </template>
                    </el-table-column>
                    <el-table-column label="编号" width="130" align="center">
                      <template slot-scope="scope">
                        <code-select :value.sync="scope.row.innerCode"></code-select>
                      </template>
                    </el-table-column>
                    <el-table-column label="计量t" width="80" align="center" prop="measureWeight">
                      <template slot-scope="scope">
                        <span v-if="scope.row._measureWeight" class="column"
                              @click="handleColumn(scope.row,'measureWeight')">{{scope.row.measureWeight}}</span>
                        <el-input v-else v-model="scope.row.measureWeight" oninput="value=value.replace(/[^0-9.]/g,'')"
                                  @blur="handleBlur(scope.row,'measureWeight')" />
                      </template>
                    </el-table-column>
                    <el-table-column label="计量%" width="80" align="center" prop="measurePercent">
                      <template slot-scope="scope">
                        <span
                              class="column">{{scope.row.measurePercent===''?scope.row.measurePercent:`${scope.row.measurePercent}%`}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="水分%" width="80" align="center">
                      <template slot-scope="scope">
                        <span v-if="scope.row._mt" @click="handleColumn(scope.row,'mt')" class="column">{{scope.row.mt}}</span>
                        <el-input v-else v-model="scope.row.mt" @blur="handleBlur(scope.row,'mt')"
                                  oninput="value=value.replace(/[^0-9.]/g,'')" />
                      </template>
                    </el-table-column>
                    <el-table-column label="开机累计h" width="80" align="center" prop="startAccumulated">
                      <template slot-scope="scope">
                        <span v-if="scope.row._startAccumulated" class="column"
                              @click="handleColumn(scope.row,'startAccumulated')">{{scope.row.startAccumulated}}</span>
                        <el-input v-else v-model="scope.row.startAccumulated" oninput="value=value.replace(/[^0-9.]/g,'')"
                                  @blur="handleBlur(scope.row,'startAccumulated')">
                        </el-input>
                      </template>
                    </el-table-column>
                    <el-table-column label="停机累计h" width="80" align="center" prop="stopAccumulated">
                      <template slot-scope="scope">
                        <!-- <span v-if="scope.row._stopAccumulated" class="column"
                              @click="handleColumn(scope.row,'stopAccumulated')">{{scope.row.stopAccumulated}}</span>
                        <el-input v-else v-model="scope.row.stopAccumulated" oninput="value=value.replace(/[^0-9.]/g,'')"
                                  @blur="handleBlur(scope.row,'stopAccumulated')" /> -->
                        <span class="column">{{scope.row.stopAccumulated}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column width="120" align="center">
                      <template #header>
                        <category-select :value.sync="config.setUnit1" type="SET_UNIT" placeholder="设定产量/h" />
                        <!-- <select-production :value.sync="config.setUnit1" /> -->
                        <!-- <el-select style="width:100%" v-model="config.setUnit1" placeholder="设定产量/h" clearable filterable>
                          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                          </el-option>
                        </el-select> -->
                      </template>
                      <el-table-column>
                        <template #header>
                          <el-time-picker v-model="config.setTime1" :picker-options="{selectableRange: '00:00:00 - 23:59:59'}"
                                          placeholder="请选择时间点" />
                        </template>
                        <el-table-column label="配比/%" align="center" prop="setPercent1">
                          <template slot-scope="scope">
                            <span v-if="scope.row._setPercent1"
                                  @click="handleColumn(scope.row,'setPercent1')">{{scope.row.setPercent1}}%</span>
                            <el-input v-else v-model="scope.row.setPercent1" @blur="handleBlur(scope.row,'setPercent1')"
                                      oninput="value=value.replace(/[^0-9.]/g,'')">
                            </el-input>
                          </template>
                        </el-table-column>
                        <el-table-column label="数量/t" prop="setWeight1" align="center">
                          <template slot-scope="scope">
                            <span>{{scope.row.setWeight1}}</span>
                          </template>
                        </el-table-column>
                      </el-table-column>
                    </el-table-column>

                    <el-table-column width="120" align="center">
                      <template #header>
                        <category-select :value.sync="config.setUnit2" type="SET_UNIT" placeholder="设定产量/h" />
                        <!-- <el-select style="width:100%" v-model="config.setUnit2" placeholder="设定产量/h" clearable filterable>
                          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select> -->
                      </template>
                      <el-table-column>
                        <template #header>
                          <el-time-picker v-model="config.setTime2" :picker-options="{selectableRange: '00:00:00 - 23:59:59'}"
                                          placeholder="请选择时间点" />
                        </template>
                        <el-table-column prop="setPercent2" label="配比/%" align="center">
                          <template slot-scope="scope">
                            <span v-if="scope.row._setPercent2"
                                  @click="handleColumn(scope.row,'setPercent2')">{{scope.row.setPercent2}}%</span>
                            <el-input v-else v-model="scope.row.setPercent2" @blur="handleBlur(scope.row,'setPercent2')"
                                      oninput="value=value.replace(/[^0-9.]/g,'')" />
                          </template>
                        </el-table-column>
                        <el-table-column prop="setWeight2" label="数量/t" align="center">
                          <template slot-scope="scope">
                            <span>{{scope.row.setWeight2}}</span>
                          </template>
                        </el-table-column>
                      </el-table-column>
                    </el-table-column>

                    <el-table-column width="120">
                      <template #header>
                        <category-select :value.sync="config.setUnit3" type="SET_UNIT" placeholder="设定产量/h" />
                        <!-- <select-production :value.sync="config.setUnit3" /> -->
                        <!-- <el-select style="width:100%" v-model="config.setUnit3" placeholder="设定产量/h" clearable filterable>
                          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                          </el-option>
                        </el-select> -->
                      </template>
                      <el-table-column>
                        <template #header>
                          <el-time-picker v-model="config.setTime3" :picker-options="{selectableRange: '00:00:00 - 23:59:59'}"
                                          placeholder="请选择时间点" />
                        </template>
                        <el-table-column prop="setPercent3" label="配比/%" align="center">
                          <template slot-scope="scope">
                            <span v-if="scope.row._setPercent3"
                                  @click="handleColumn(scope.row,'setPercent3')">{{scope.row.setPercent3}}%</span>
                            <el-input v-else v-model="scope.row.setPercent3" @blur="handleBlur(scope.row,'setPercent3')"
                                      oninput="value=value.replace(/[^0-9.]/g,'')" />
                          </template>
                        </el-table-column>
                        <el-table-column prop="setWeight3" label="数量/t" align="center">
                          <template slot-scope="scope">
                            <span>{{scope.row.setWeight3}}</span>
                          </template>
                        </el-table-column>
                      </el-table-column>
                    </el-table-column>

                    <el-table-column label="操作" width="80" align="center">
                      <template slot-scope="scope">
                        <el-tag class="opt-btn" color="#2f79e8" @click="handleRemove(scope.row)">删除</el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col />
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button class="dialog-footer-all" @click="handleFullScreen">{{screen.full?'取消全屏':'全屏'}}</el-button>
        <!-- <el-button class="dialog-footer-all" @click="handleUseDraft" :style="{'visibility':dialogStatus==='create'?'':'hidden'}">
          {{draft.open?'已保存':'保存草稿'}}
        </el-button> -->
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import { findByKeyword, findByInnerCode } from '@/api/stock'
import Model from '@/model/production/productionListV'
import productModel from '@/model/product/productList'
import { getCustomerContract } from '@/api/quality'
import { formatTimeSecond, parseTime } from '@/utils/index'
import Cache from '@/utils/cache'
const config = {
  setUnit1: '', // 设定单位1
  setTime1: '', // 设定时间1
  setUnit2: '', // 设定单位2
  setTime2: '', // 设定时间2
  setUnit3: '', // 设定单位3
  setTime3: '' // 设定时间3
}
const form = {
  _id: '1'
  // // blendingProductionId: '', // 配煤生产ID
  // shift: '白班', // '班次（白班、晚班）',
  // measuringScale: '', // 计量称
  // productId: '', // 产品ID
  // productName: '', // 产品名称
  // productCode: '', // 产品编码
  // measureWeight: '', // 计量（吨）
  // measurePercent: '', // '计量百分比',
  // mt: '', // 水分
  // innerCode: '',
  // startAccumulated: 0, // 开机累计
  // stopAccumulated: '', // 停机累计
  // setUnit1: '', // 设定单位1
  // setTime1: '', // 设定时间1
  // setPercent1: '', // 设定配比1
  // setWeight1: '', // 设定量1
  // setUnit2: '', // 设定单位2
  // setTime2: '', // 设定时间2
  // setPercent2: '', // 设定配比2
  // setWeight2: '', // 设定量2
  // setUnit3: '', // 设定单位3
  // setTime3: '', // 设定时间3
  // setPercent3: '', // 设定配比3
  // setWeight3: '', // 设定量3
  // _measureWeight: false,
  // _innerCode: false,
  // _mt: false,
  // _startAccumulated: false,
  // _stopAccumulated: false,
  // _setPercent1: false,
  // _setPercent2: false,
  // _setPercent3: false,
  // _stock: 0,
}
export default {
  name: 'coalProductionList',
  mixins: [Mixins],
  data() {
    return {
      curd: 'coalProductionList',
      model: Model,
      form: { ...form },
      config: { ...config },
      list: [{ ...form }],
      options: [
        {
          value: 350,
          label: '350t/时'
        },
        {
          value: 400,
          label: '400t/时'
        },
        {
          value: 420,
          label: '420t/时'
        },
        {
          value: 450,
          label: '450t/时'
        },
        {
          value: 500,
          label: '500t/时'
        },
        {
          value: 550,
          label: '550t/时'
        },
        {
          value: 600,
          label: '600t/时'
        }
      ],
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'productionDate',
            component: 'date-select',
            defaultValue: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
            resetClearDefault: true,
            filter: 'productionDate',
            props: {
              placeholder: '请选择日期',
              'picker-options': {
                cellClassName: (time) => {
                  const flag = this.dateList.includes(time.Format('yyyy-MM-dd'))
                  if (flag) return 'background'
                }
              }
            }
          }
        ]
      },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      entityForm: { ...Model.model },
      rules: {
        productionDate: { required: true, message: '请选择日期', trigger: 'blur' },
        productId: { required: true, message: '请选择品名', trigger: 'change' },
        actualProductionTime: { required: true, message: '请输入实际生产时间', trigger: 'blur' }
      },
      nameEntity: { options: [], active: '' },
      memoryEntity: { fields: {}, triggered: false },
      actions: [],
      tableData: [],
      headerTime: [],
      nameItemEntity: [],
      filters: { productionDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') },
      timeValue: [],
      timeValue1: [],
      locationList: [],
      locationActive: 0,
      currentTableData: [],
      innerCodeList: [],
      screen: { full: false },
      draft: { open: false, config: { ...config }, entityForm: { ...Model.model }, list: [{ ...form }] },
      productionDraft: { config: { ...config }, entityForm: { ...Model.model }, list: [{ ...form }] },
      dateList: []
    }
  },
  created() {
    this.getName()
    this.getContract()
    this.getList()
    this.getFindByKeyword()
    this.getFindByInnerCode()
    this.getDateList()
  },

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (vm.$route.params.isOpenAdd) {
        vm.handleCreate(false)
      }
    })
  },
  methods: {
    async getDateList(params = {}) {
      try {
        const res = await this.model.getDateList(params)
        if (res.data && res.data.length) {
          this.dateList = res.data
        } else {
          this.dateList = []
        }
      } catch (error) {
        this.dateList = []
      }
    },
    handleFullScreen() {
      this.screen.full = !this.screen.full
      const el = this.$refs.dialogStatus.$el
      const el_body = el.querySelector('.el-dialog__body')
      el_body.style.height = this.screen.full ? '85vh' : '70vh'
    },
    handleUseDraft() {
      this.draft.open = true
    },
    async getFindByInnerCode() {
      const res = await findByInnerCode()
      // const codes = res.data.map(val => val.code)
      this.innerCodeList = res.data
    },

    handlePickerChange(date) {
      const { startTime, stopTime, startTime1, stopTime1, startTime2, stopTime2 } = this.entityForm
      const list = { startTime, stopTime, startTime1, stopTime1, startTime2, stopTime2 }
      for (const [key, val] of Object.entries(list)) {
        if (val !== null) {
          const date = (new Date(val) * 1) / 1000 / 3600
          if (typeof date === 'number') list[key] = date
        } else {
          list[key] = null
        }
      }
      if (date) {
        if (list['stopTime'] < list['startTime']) {
          // this.$message({ message: `开始时间不能小于结束时间`, type: 'warning' })
          // list['stopTime'] = null
        }
        if (list['stopTime1'] < list['startTime1']) {
          // this.$message({ message: `开始时间不能小于结束时间`, type: 'warning' })
          // list['stopTime1'] = null
        }
      }

      let actualTime = (
        list['stopTime'] -
        list['startTime'] +
        (list['stopTime1'] - list['startTime1']) +
        (list['stopTime2'] - list['startTime2'])
      ).toFixed(2)
      // this.entityForm.actualProductionTime = actualTime > 0 ? actualTime : 0
      this.entityForm.actualProductionTime = actualTime
    },
    computeActualProductionTime() {
      let start = this.entityForm.startTime
      let stop = this.entityForm.stopTime
      let start1 = this.entityForm.startTime1
      let stop1 = this.entityForm.stopTime1
      this.entityForm.actualProductionTime = ((stop - start) / 1000 / 3600 + (stop1 - start1) / 1000 / 3600).toFixed(2)
    },
    handleToLocation(index) {
      this.locationActive = index
      this.currentTableData = this.tableData[index].blendingProductionVoList
      this.setTime()
    },
    initDateTime() {
      const date = new Date()
      this.timeValue = [
        new Date(date.getFullYear(), date.getMonth(), date.getDay(), 0, 0, 0),
        new Date(date.getFullYear(), date.getMonth(), date.getDay(), 23, 59, 59)
      ]
      this.timeValue1 = [
        new Date(date.getFullYear(), date.getMonth(), date.getDay(), 0, 0, 0),
        new Date(date.getFullYear(), date.getMonth(), date.getDay(), 23, 59, 59)
      ]
    },
    handleFilter(filters) {
      this.filters = filters
      this.getList()
    },
    handleFilterReset() {
      this.filters = {}
      this.getList()
    },
    async getFindByKeyword() {
      const { data } = await findByKeyword()
      this.nameItemEntity = data
    },

    async handleUpdate(id) {
      this.dialogStatus = 'update'
      const { data } = await Model.detail(id)
      this.entityForm = { ...data }
      this.list = data.blendingProductionItemList
      const { setUnit1, setTime1, setUnit2, setTime2, setUnit3, setTime3 } = data.blendingProductionItemList[0]
      this.config = { setUnit1, setTime1, setUnit2, setTime2, setUnit3, setTime3 }
      delete this.entityForm.blendingProductionItemList
      this.dialogFormVisible = true
    },

    tableRowClassName({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === this.currentTableData.length - 2) {
        return 'orange-row'
      } else {
        return 'row-layout'
      }
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // 日期
      if ([0].includes(columnIndex)) {
        if (rowIndex === 0) {
          return {
            rowspan: this.currentTableData.length,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      } else if ([1, 2].includes(columnIndex)) {
        if (rowIndex === 0) {
          return {
            rowspan: this.currentTableData.length - 1,
            colspan: 1
          }
        } else {
          return {
            // 如果不是最后一行则合并
            rowspan: rowIndex === this.currentTableData.length - 1 ? 1 : 0,
            colspan: 0
          }
        }
      } else if ([3].includes(columnIndex)) {
        // 给料机合并
        const index = []
        this.currentTableData.forEach((item, i) => {
          if (item.measuringScale === row.measuringScale) {
            index.push(i)
          }
        })
        if (index.length <= 1) {
          return [rowIndex === this.currentTableData.length - 1 ? 0 : 1, 1]
        }
        if (index[0] === rowIndex) {
          return [index.length, 1]
        } else {
          return [0, 1]
        }
      } else if ([9].includes(columnIndex)) {
        // 累计列
        // return this.mergeColumnFactory(row, rowIndex, 'startAccumulated')
      } else if ([10].includes(columnIndex)) {
        // 累计列
        return this.mergeColumnFactory(row, rowIndex, 'stopAccumulated')
      } else if ([17].includes(columnIndex)) {
        // 操作列合并
        if (rowIndex === 0) {
          return {
            rowspan: this.currentTableData.length - 1,
            colspan: 1
          }
        } else {
          return {
            // 如果不是最后一行则合并
            rowspan: 1,
            colspan: 0
          }
        }
      } else if (rowIndex === this.currentTableData.length - 1) {
        if (columnIndex === 4 && row.id === 'lastRow') {
          setTimeout(() => {
            const v = this.currentTableData[0]
            const el = document.querySelectorAll(`.${column.id}`)
            const row = el[el.length - 1].querySelector('.cell')
            const hasInfo = document.querySelectorAll(`.info`)
            if (hasInfo.length) {
              hasInfo.forEach((item) => {
                item.parentNode.removeChild(item)
              })
            }

            row.innerHTML = `<div class="info">
                <span class="title">开机时间-关机时间<span>${formatTimeSecond(new Date(v.startTime).getTime()) || ''}-${
              formatTimeSecond(new Date(v.stopTime).getTime()) || ''
            }</span></span>
                <span class="title">开机时间-关机时间<span>${formatTimeSecond(new Date(v.startTime1).getTime()) || ''}-${
              formatTimeSecond(new Date(v.stopTime1).getTime()) || ''
            }</span></span>
                 <span class="title">开机时间-关机时间<span>${formatTimeSecond(new Date(v.startTime2).getTime()) || ''}-${
              formatTimeSecond(new Date(v.stopTime2).getTime()) || ''
            }</span></span>
                <span class="title">实际生产时间/h<span>${v.actualProductionTime || ''}</span></span>
                <span class="title">现场指挥<span>${v.fieldCommand || ''}</span></span>
                <span class="title">填表人<span>${v.makeupMan || ''}</span></span>
                <span class="title">审核人<span>${v.auditMan || ''}
                </span></span><span class="title">备注<span>${v.remarks || ''}</span></span>
              </div>`
          }, 0)
          return [1, 16]
        } else {
          return [0, 0]
        }
      }
    },

    mergeColumnFactory(row, rowIndex, key) {
      const index = []
      let sum = 0
      this.currentTableData.forEach((item, i) => {
        if (item.measuringScale === row.measuringScale) {
          index.push(i)
          sum += item[key] * 1
        }
      })
      if (index.length <= 1) return [1, 1]
      if (index[0] === rowIndex) {
        row[key] = sum.toFixed(2)
        return [index.length, 1]
      } else {
        row[key] = 0
        return [0, 1]
      }
    },
    async getList() {
      this.tableData = []
      this.currentTableData = []
      this.locationList = []
      this.headerTime = ['', '', '']
      this.locationActive = 0
      const { data } = await Model.page(this.filters)
      if (data.length) {
        this.tableData = data
        this.currentTableData = this.tableData[this.locationActive].blendingProductionVoList
        data.forEach((item) => {
          this.locationList.push({ label: item.productName })
        })
        this.setTime()
      }
    },
    setTime() {
      let { setTime1, setTime2, setTime3 } = this.currentTableData[0]
      setTime1 = setTime1 === '' ? '' : parseTime(setTime1, '{h}:{i}:{s}')
      setTime2 = setTime2 === '' ? '' : parseTime(setTime2, '{h}:{i}:{s}')
      setTime3 = setTime3 === '' ? '' : parseTime(setTime3, '{h}:{i}:{s}')
      this.headerTime = [setTime1, setTime2, setTime3]
    },
    checkParams(
      list,
      rule = [
        'mt',
        'stopAccumulated',
        'setPercent1',
        'setWeight1',
        'setPercent2',
        'setWeight2',
        'setPercent3',
        'setWeight3',
        'setUnit1',
        'setTime1',
        'setUnit2',
        'setTime2',
        'setUnit3',
        'setTime3',
        'ext',
        'remarks',
        'updateBy',
        'updateDate',
        'innerCode'
      ]
    ) {
      for (const item of list) {
        for (const [key, val] of Object.entries(item)) {
          if (rule.includes(key)) continue
          if (val === '') {
            this.$message({ message: `请检查参数${key}`, type: 'warning' })
            return false
          }
        }
      }
      return true
    },
    async saveEntityForm(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.list.length) {
          let sum = 0
          const list = []
          this.list.forEach((item) => {
            sum += item.measureWeight * 1
            list.push({ ...item, ...this.config })
          })
          if (!this.checkParams(list)) return false
          this.entityForm.outputWeight = sum
          this.entityFormLoading = true
          const res = await Model.save({ ...this.entityForm, blendingProductionItemList: list })
          this.entityFormLoading = false
          if (res) {
            // this.saveMemoryField()
            this.resetVariant()
            this.getList()
            this.getFindByInnerCode()
            this.clearDraft()
            this.draft.open = false
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else {
          this.$message({ message: '生产数据不能为空', type: 'warning' })
        }
      }
    },
    async getContract() {
      const { data } = await getCustomerContract()
      // 供应商名称返回id用来查询
      this.contractEntity.options = this.formatContract({ data, key: { customerName: 'displayName', customerId: 'id' } })
    },
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
        })
      } catch (error) {}
    },

    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.contractEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.list = []
      this.handleAdd()
      this.config = { setUnit1: '', setTime1: '', setUnit2: '', setTime2: '', setUnit3: '', setTime3: '' }
    },

    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    getSummaries(param) {
      const summaries = [
        { prop: 'measureWeight', suffix: '', fixed: 2 },
        { prop: 'measurePercent', suffix: '%', fixed: 0 },
        { prop: 'startAccumulated', suffix: '', fixed: 2 },
        { prop: 'stopAccumulated', suffix: '', fixed: 2 },
        { prop: 'setPercent1', suffix: '%', fixed: 0 },
        { prop: 'setWeight1', suffix: '', fixed: 2 },
        { prop: 'setPercent2', suffix: '%', fixed: 0 },
        { prop: 'setWeight2', suffix: '', fixed: 2 },
        { prop: 'setPercent3', suffix: '%', fixed: 0 },
        { prop: 'setWeight3', suffix: '', fixed: 2 }
      ]
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index <= 3) {
          sums[3] = '合计'
        } else {
          const values = data.map((item) => Number(item[column.property]))
          if (!values.every((value) => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            const idx = summaries.findIndex((val) => {
              if (val.prop === column.property) {
                sums[index] = sums[index].toFixed(val.fixed) + val.suffix
                return true
              }
            })
            idx === -1 && (sums[index] = '')
          } else {
            sums[index] = ''
          }
        }
      })
      return sums
    },
    handleColumn(row, name) {
      row['_' + name] = !row['_' + name]
    },
    handleBlur(row, name) {
      if (row[name] === '') return
      row['_' + name] = !row['_' + name]
      if (['innerCode'].includes(name)) return
      row[name] = (row[name] * 1).toFixed(2)
      switch (name) {
        case 'measureWeight':
          // console.log(row)
          row.stopAccumulated = row.measureWeight
          // console.log(row._stock, 11)
          // if (row.measureWeight > row._stock) {
          //   row.measureWeight = 0
          //   row.__measureWeight = false
          //   this.$message({ message: '计量超过库存', type: 'warning' })
          //   return
          // }
          const sum = this.list.reduce((t, item) => t + item.measureWeight * 1, 0)
          if (sum === 0) return
          this.list.forEach((item) => {
            item.measurePercent = ((item.measureWeight / sum) * 100).toFixed(2)
          })
          break
        case 'setPercent1':
          // row['setPercent1'] = (row['setPercent1'] / 100).toFixed(2)
          this.computerWeight({ row, num: 1 })
          break
        case 'setPercent2':
          // row['setPercent2'] = (row['setPercent2'] / 100).toFixed(2)
          this.computerWeight({ row, num: 2 })
          break
        case 'setPercent3':
          // row['setPercent3'] = (row['setPercent3'] / 100).toFixed(2)
          this.computerWeight({ row, num: 3 })
          break
      }
    },
    computerWeight({ row, num, isWatch = false }) {
      const setUnit = this.config['setUnit' + num]
      if (setUnit === '') {
        this.list.forEach((item) => {
          item['setWeight' + num] = ''
        })
        return
      }
      if (isWatch) {
        this.list.forEach((item) => {
          if (item['setPercent' + num] !== '') {
            item['setWeight' + num] = ((item['setPercent' + num] / 100) * setUnit).toFixed(2)
          }
        })
      } else {
        row['setWeight' + num] = ((row['setPercent' + num] / 100) * setUnit).toFixed(2)
      }
    },
    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },
    handleAdd() {
      const form = { ...this.form }
      form._id = Math.random().toFixed(6).slice(-6)
      this.list.push(form)
    },
    handleRemove({ _id }) {
      const index = this.list.findIndex((item) => item._id === _id)
      if (index === -1) return false
      this.list.splice(index, 1)
    },
    clearDraft() {
      this.productionDraft = { config: { ...config }, entityForm: { ...Model.model }, list: [{ ...form }] }
      Cache.remove('productionDraft')
    },
    /**
     * @status {boolean} 真是保存记录，假是展示
     */
    createDraft(status) {
      if (status && this.dialogStatus === 'create') {
        const list = this.list.map((item) => item)
        return {
          config: { ...this.config },
          entityForm: { ...this.entityForm },
          list
        }
      } else {
        this.config = { ...this.productionDraft.config }
        this.entityForm = { ...this.productionDraft.entityForm }
        this.list = this.productionDraft.list.map((item) => item)
      }
    },
    async handleCreate(type = true) {
      if (type) {
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
        this.entityForm = { ...this.entityForm }
        const productionDraft = Cache.get('productionDraft')
        if (productionDraft) {
          this.productionDraft = productionDraft
          this.createDraft(false)
        } else {
          this.productionDraft = this.createDraft(true)
          Cache.set('productionDraft', this.productionDraft)
        }
      } else {
        Cache.remove('productionDraft')
        this.productionDraft = this.createDraft(true)
        Cache.set('productionDraft', this.productionDraft)
      }
    },
    contrastField(a, b) {
      // if (a === null) return true
      a = JSON.parse(JSON.stringify(a))
      b = JSON.parse(JSON.stringify(b))
      a.list.forEach((item) => {
        for (const [k] of Object.entries(item)) {
          if (k.includes('_')) {
            item[k] = ''
          }
        }
      })
      b.list.forEach((item) => {
        for (const [k] of Object.entries(item)) {
          if (k.includes('_')) {
            item[k] = ''
          }
        }
      })
      const strA = JSON.stringify(a)
      const strB = JSON.stringify(b)
      return strA === strB
    },
    handleClose() {
      const productionDraft = Cache.get('productionDraft')
      if (this.dialogStatus === 'create') {
        const new_productionDraft = this.createDraft(true)
        const status = this.contrastField(productionDraft, new_productionDraft)
        if (productionDraft === null || !status) {
          this.$confirm('当前未保存, 是否保存草稿?', '提示', {
            confirmButtonText: '不保存',
            cancelButtonText: '保存草稿',
            type: 'info',
            cancelButtonClass: 'btn-custom-save',
            confirmButtonClass: 'btn-custom-cancel'
          })
            .then(() => {
              this.resetVariant()
            })
            .catch(() => {
              Cache.set('productionDraft', new_productionDraft)
              this.resetVariant()
            })
        } else {
          this.resetVariant()
        }
      } else {
        this.resetVariant()
      }
    }
  },
  watch: {
    'entityForm.startTime'(v) {
      this.handlePickerChange(false)
    },
    'entityForm.startTime1'(v) {
      this.handlePickerChange(false)
    },
    'entityForm.startTime2'(v) {
      this.handlePickerChange(false)
    },
    'entityForm.stopTime'(v) {
      this.handlePickerChange(false)
    },
    'entityForm.stopTime1'(v) {
      this.handlePickerChange(false)
    },
    'entityForm.stopTime2'(v) {
      this.handlePickerChange(false)
    },
    'entityForm.contractId'(id) {
      const value = this.filterContractItem(id, 'contractEntity')
      this.contractEntity.active = value
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      if (this.dialogStatus === 'create') this.entityForm.productName = form.productName || ''
      this.entityForm.contractCode = form.customerName || ''
      this.entityForm.contractId = form.customerId || ''
      this.entityForm.contractName = form.name || ''
    },
    'config.setUnit1'(v) {
      this.computerWeight({ num: 1, isWatch: true })
    },
    'config.setUnit2'(v) {
      this.computerWeight({ num: 2, isWatch: true })
    },
    'config.setUnit3'(v) {
      this.computerWeight({ num: 3, isWatch: true })
    },

    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code || ''
        this.entityForm.productId = item.id || ''
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.table-container {
  height: 80vh;
  overflow-y: auto !important;
  background: #fff;
  box-sizing: border-box;
  padding-bottom: 30px;
  // <!-- height:84rem;: auto;background:#fff; -->
}
::v-deep .el-icon-date {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-icon-time {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}
// @media screen and (max-width:480px;) {
//   height: 80vh !important;
// }

::v-deep .el-input__prefix {
  right: 5px;
  left: auto;
}

::v-deep .el-input--prefix .el-input__inner {
  padding-left: 10px;
}
::v-deep .el-table__fixed-right::before {
  width: 0;
}

.dialog {
  width: 980px;
  height: 740px;
}
.dialog-footer {
  margin-right: 44px;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px #f1f1f2;
  padding: 10px;
  &-all {
    font-size: 14px;
    margin-left: 34px;
    // margin-right: auto;
    flex: 0 0 58px;
    color: #ff9639;
    background: #fff;
    border: 1px solid #ff9639;
    &:hover {
      background: transparent;
    }
    &:nth-of-type(1) {
      // margin-left: 15px;
      margin-right: auto;
    }
  }
  &-btns {
    width: 85px;
    height: 35px;
    border: 1px solid #2f79e8;
    background: #fff;
    color: #2f79e8;
    margin-left: 20px;
    font-size: 14px;
    &:last-of-type {
      border: 1px solid #d5d5d5;
      color: #747474;
    }
  }
}

.form-layout {
  padding: 0 20px;
}

.form-title {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 17px;
    background: #2f79e8;
    height: 16px;
  }
}
::v-deep .el-input--mini .el-input__inner {
  height: 29.5px;
  line-height: 29.5px;
}

.opt-btn {
  cursor: pointer;
  margin: 0 3px;
  color: #fff;
  border: none;
}
::v-deep .el-form--label-top .el-form-item__label {
  padding: 0;
  font-weight: 400;
  font-size: 13px;
  color: #646464;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 13px;
}
.name {
  // color: blue;
  text-decoration: underline;
  cursor: pointer;
  transition: 0.5s all;
  &:hover,
  :focus {
    color: #3234d3;
  }
}

.excessive {
  width: 100%;
  color: red;
}

::v-deep .percent-column {
  .cell {
    top: 10px;
    height: 42px;
  }
}
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}
.column {
  display: inline-block;
  width: 100%;
  height: 20px;
}
.app-container {
  height: 100vh;
}
.table {
  width: 100%;
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
}
.el-button--export-all:focus,
.el-button--export-all:hover {
  background: #ff9639 !important;
  border-color: #fff !important;
  color: #fff !important;
}

.el-button--export-all {
  color: #ff9639;
  background-color: #fff;
  border-color: #ff9639;
  padding: 8px 13px;
  margin-top: -6px;
}
::v-deep // 合计行样式
.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  height: 50px;
  line-height: 50px;
  color: #ff9639;
  font-size: 13px;
  background: rgb(245, 246, 251);
}
::v-deep .panel-list {
  margin-left: -25px;
}

::v-deep .info {
  display: flex;
  justify-content: space-evenly;
  flex-flow: wrap;
  span {
    padding: 3px;
  }
  // justify-content: start;
  .title {
    span {
      margin-left: 5px;
      color: #f8a20f;
      font-weight: bold;
    }
  }
}
::v-deep .orange-row {
  color: #f8a20f;
  font-size: 14px;
  font-weight: bold;
  height: 60px;
}
::v-deep .row-layout {
  font-size: 14px;
  height: 60px;
}
.panel-list-item {
  height: 40px;
  padding: 5px;
}
</style>
