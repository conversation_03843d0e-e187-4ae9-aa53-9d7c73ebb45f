<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset"
            @import="handleImpiort"      :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <div class="table-container">
      <el-table v-if="tableData.length" :data="tableData" :border="false" class="table" :header-cell-style="headClass"
                show-summary>
        <el-table-column prop="productName" label="品名" width="130">
        </el-table-column>
        <el-table-column prop="totalActualProductionTime" label="生产时间(h)" width="130">
        </el-table-column>
        <el-table-column prop="totalOutputWeight" label="生产总量(T)" width="130">
        </el-table-column>
        <el-table-column label="生产用煤(T)" align="center" minWidth="300">
          <el-table-column v-for="item in category" :key="item.productId" :label="item.productName" :prop="item.productId" />
        </el-table-column>
      </el-table>
      <div class="empty" v-else>
        <el-empty description="暂无数据" :image="empty" :image-size="300"></el-empty>
      </div>
    </div>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import { getYearMonth } from '@/utils'
import { getMonthSum } from '@/model/product/productionMonth'
import empty from '@/assets/empty.png'
const listQuery = {
  month: getYearMonth(),
}

export default {
  name: 'productionMonth',
  mixins: [Mixins],
  data() {
    return {
      empty,
      curd: 'productionMonth',
      listQuery: { ...listQuery },
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'month',
            component: 'date-select',
            filter: 'month',
            defaultValue: getYearMonth(),
            props: {
              placeholder: '请选择月份',
              clearable: false,
              type: 'month',
              format: 'yyyy-MM',
              formats: 'yyyy-MM',
            },
          },
          {
            prop: 'productName',
            filter: 'productName',
            component: 'select-name',
            props: { placeholder: '请选择品名', clearable: true, changeFilter: false },
          },
        ],
      },
      tableData: [],
      category: [],
    }
  },
  created() {
    this.getMonthDate()
  },
  methods: {
    async getMonthDate() {
      try {
        const { data } = await getMonthSum({ ...this.listQuery })
        if (data.length) {
          this.tableData = data
          this.formatData()
        } else {
          this.tableData = []
          this.category = []
        }
      } catch (error) {
        this.tableData = []
        this.category = []
      }
    },

    handleFilterReset() {
      this.listQuery = { ...listQuery }
      this.getMonthDate()
    },
    handleFilter(filters) {
      this.listQuery = { ...filters }
      this.getMonthDate()
    },

    headClass() {
      return 'background:#2f79e8;color:#fff;font-weight:400;'
    },
    formatData() {
      this.category = this.tableData[0].blendingProductionItemMonthSumVoList
      this.tableData.forEach((item) => {
        item.blendingProductionItemMonthSumVoList.forEach((val) => {
          item[val.productId] = val.totalMeasureWeight
        })
      })
    },

    resetData() {},
  },
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.table-container {
  height: 85vh;
  // overflow-y: auto !important;
  background: #fff;
  box-sizing: border-box;
  overflow-y: auto;
  position: relative;
  .empty {
    position: absolute;
    left: 50%;
    top: 20%;
    transform: translate(-50%);
  }
}
.table {
  height: 100%;
}
</style>
