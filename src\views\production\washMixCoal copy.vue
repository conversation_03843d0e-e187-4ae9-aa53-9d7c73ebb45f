<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <!-- <panel title="品名" type="location" :active="locationActive" :list='locationList' @locationChange="handleToLocation" /> -->

    <!-- <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" otherHeight="180">
      <el-table-column label="原煤矿井名" slot="mineNameRaw" prop="mineNameRaw" align="center">
        <template slot-scope="scope">
          <span class="name">{{scope.row.mineNameRaw}}</span>
        </template>
      </el-table-column>
      <el-table-column label="原煤品名" slot="productNameRaw" prop="productNameRaw" align="center">
        <template slot-scope="scope">
          <span class="name">{{scope.row.productNameRaw}}</span>
        </template>
      </el-table-column>
      <el-table-column class-name="index" label="原煤过磅" slot="chargingTest" align="center">
        <el-table-column label="车数" prop="truckCountRaw" width="100" align="center" />
        <el-table-column label="吨数" prop="weightRaw" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="原煤皮带称" slot="chargingTest" align="center">
        <el-table-column label="吨数" prop="beltWeightRaw" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="原煤铲车" slot="chargingTest" align="center">
        <el-table-column label="铲数" prop="shovelCountRaw" width="100" align="center" />
        <el-table-column label="吨数" prop="weightCalcRaw" width="100" align="center" />
      </el-table-column>
      <el-table-column label="精煤品名" slot="productName" prop="productName" align="center">
        <template slot-scope="scope">
          <span class="name">{{scope.row.productName}}</span>
        </template>
      </el-table-column>
      <el-table-column label="精煤矿井名" slot="mineName" prop="mineName" align="center">
        <template slot-scope="scope">
          <span class="name"><span class="name">{{scope.row.mineName}}</span></span>
        </template>
      </el-table-column>
      <el-table-column class-name="index" label="精煤过磅" slot="chargingTest" align="center">
        <el-table-column label="车数" prop="truckCount" width="100" align="center" />
        <el-table-column label="吨数" prop="weight" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="精煤皮带称" slot="chargingTest" align="center">
        <el-table-column label="吨数" prop="beltWeight" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="精煤铲车" slot="chargingTest" align="center">
        <el-table-column label="铲数" prop="shovelCount" width="100" align="center" />
        <el-table-column label="吨数" prop="weightCalc" width="100" align="center" />
      </el-table-column>
      <el-table-column label="操作" slot="opt" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)">编辑</el-tag>
          <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除</el-tag>
        </template>
      </el-table-column>
    </s-curd> -->

    <div class="table-container">
      <!-- <el-table :data="currentTableData" border class="table" :header-cell-style="headClass"> -->
      <el-table :data="currentTableData" element-loading-text="加载中..." v-loading="loading" :span-method="spanMethod" border
                class="table" :header-cell-style="headClass">
        <el-table-column prop="productionDate" label="日期" align="center" width="150" />

        <!-- <el-table-column prop="productId" label="精煤id" align="center" /> -->
        <el-table-column prop="productName" label="精煤品名" align="center" />
        <el-table-column prop="mineName" label="精煤矿井名" align="center" />

        <el-table-column label="精煤过磅" align="center" width="250">
          <el-table-column label="车数" prop="truckCount" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.truckCount}}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="吨数" prop="weight" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.weight}}</span>
              </template>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="皮带称" align="center" width="250">
          <el-table-column label="吨数" prop="beltWeight" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.beltWeight}}</span>
              </template>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="精煤铲车" align="center" width="250">
          <el-table-column label="铲数" prop="shovelCount" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.shovelCount}}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="吨数" prop="weightCalc" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.weightCalc}}</span>
              </template>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- <el-table-column prop="productIdRaw" label="原煤id" align="center" width="250" /> -->
        <el-table-column prop="productNameRaw" label="原煤品名" align="center" />
        <el-table-column prop="mineNameRaw" label="原煤矿井名" align="center" />

        <el-table-column label="原煤过磅" align="center" width="250">
          <el-table-column label="车数" prop="truckCountRaw" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.truckCountRaw}}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="吨数" prop="weightRaw" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.weightRaw}}</span>
              </template>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="原煤皮带称" align="center" width="250">
          <el-table-column label="吨数" prop="beltWeightRaw" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.beltWeightRaw}}</span>
              </template>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="原煤铲车" align="center" width="250">
          <el-table-column label="铲数" prop="shovelCountRaw" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.shovelCountRaw}}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="吨数" prop="weightCalcRaw" min-min-width="80" align="center">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.weightCalcRaw}}</span>
              </template>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="操作" align="center" width="150">
          <template slot-scope="scope">
            <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row.id)"
                    v-if="perms[`${curd}:update`]||false">编辑</el-tag>
            <!-- <el-tag class="opt-btn" color="#33CAD9" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除
            </el-tag> -->
            <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除
            </el-tag>

          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog :fullscreen="screen.full" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col style="margin-bottom: 20px;">
            <div class="form-title">基本类型</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="productionDate">
                    <date-select v-model="entityForm.productionDate" clearable :isset="true" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <!-- <el-form-item label="原煤品名" prop="productIdRaw">
                    <select-down :value.sync="nameEntityV.active" :list="nameEntityV.options" @eventChange="handleNameEntityV" />
                  </el-form-item> -->
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col style="margin-bottom: 20px;">
            <div class="form-titl2 form-warp">
              <span style="position: relative;top:-3px">原煤数据</span>
              <el-button type="export-all" @click="handleAdd">新增</el-button>
            </div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-table :data="listV" style="width: 100%" stripe :header-cell-style="headClass">
                    <el-table-column label="原煤名称" align="center">
                      <template slot-scope="scope">
                        <select-down :value.sync="scope.row.productNameRaw" :code.sync="scope.row.productCodeRaw"
                                     @eventChange="handleNameEntityV(scope.row)" :stock.sync="scope.row._stock"
                                     :id.sync="scope.row.productIdRaw" :list="nameItemEntity" />
                      </template>
                    </el-table-column>
                    <el-table-column label="原煤过磅车数" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.truckCountRaw" clearable />
                      </template>
                    </el-table-column>
                    <el-table-column label="原煤过磅吨数" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.weightRaw" clearable />
                      </template>
                    </el-table-column>

                    <el-table-column label="原煤皮带称吨数" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.beltWeightRaw" clearable />
                      </template>
                    </el-table-column>

                    <el-table-column label="原煤铲车铲数" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.shovelCountRaw" clearable />
                      </template>
                    </el-table-column>
                    <el-table-column label="原煤铲车吨数" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.weightCalcRaw" clearable />
                      </template>
                    </el-table-column>

                    <el-table-column label="操作" width="80" align="center">
                      <template slot-scope="scope">
                        <el-tag class="opt-btn" color="#2f79e8" @click="handleRemove(scope.row)">删除</el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col style="margin-bottom: 20px;">
            <div class="form-title">洗煤生产数据</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="精煤品名" prop="productId">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-titleV">精煤过磅</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="车数" prop="truckCount">
                    <el-input v-model="entityForm.truckCount" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="吨数" prop="weight">
                    <el-input v-model="entityForm.weight" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-titleV">精煤皮带称</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="吨数" prop="beltWeight">
                    <el-input v-model="entityForm.beltWeight" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-titleV">精煤铲车</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="铲数" prop="shovelCount">
                    <el-input v-model="entityForm.shovelCount" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="吨数" prop="weightCalc">
                    <el-input v-model="entityForm.weightCalc" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col />
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button class="dialog-footer-all" style="  color: #adadad;
  border: 1px solid #adadad;" @click="handleFullScreen">{{screen.full?'取消全屏':'全屏'}}</el-button>
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import { findByKeyword } from '@/api/stock'
import { listQuery } from '@/const'
import Model from '@/model/production/washMixCoal'
import productModel from '@/model/product/productList'
import { getCustomerContract } from '@/api/quality'
import Cache from '@/utils/cache'
const form = {
  id: '1',
  productCodeRaw: '',
  productNameRaw: '',
  productIdRaw: '',
  truckCountRaw: '',
  weightRaw: '',
  beltWeightRaw: '',
  shovelCountRaw: '',
  weightCalcRaw: ''
}
export default {
  name: 'washMixCoal',
  mixins: [Mixins],
  data() {
    return {
      curd: 'washMixCoal',
      model: Model,
      form: { ...form },
      listV: [{ ...form }],
      indicators: {
        weightCalcRaw: Infinity,
        shovelCountRaw: Infinity,
        beltWeightRaw: Infinity,
        weightRaw: Infinity,
        truckCountRaw: Infinity,
        truckCount: Infinity,
        weight: Infinity,
        beltWeight: Infinity,
        shovelCount: Infinity,
        weightCalc: Infinity
      },
      filterOption: {
        showMore: true,
        columns: [
          // {
          //   prop: 'productionDate',
          //   component: 'date-select',
          //   defaultValue: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
          //   resetClearDefault: true,
          //   filter: 'filter_EQS_productionDate',
          //   props: {
          //     placeholder: '请选择日期',
          //     'picker-options': {
          //       cellClassName: (time) => {
          //         const flag = this.dateList.includes(time.Format('yyyy-MM-dd'))
          //         if (flag) return 'background'
          //       },
          //     },
          //   },
          // },

          {
            prop: 'productionDate',
            component: 'date-select',
            defaultValue: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
            resetClearDefault: true,
            filter: 'productionDate',
            props: {
              placeholder: '请选择日期',
              'picker-options': {
                cellClassName: (time) => {
                  const flag = this.dateList.includes(time.Format('yyyy-MM-dd'))
                  if (flag) return 'background'
                }
              }
            }
          },
          {
            prop: 'productName',
            filter: 'keyword',
            component: 'select-ProductName',
            props: { placeholder: '请选择品名', clearable: true, istype: 'xi', iconisshow: false }
          }
          // {
          //   prop: 'productName',
          //   filter: 'filter_LIKES_productName',
          //   component: 'select-name',
          //   props: { placeholder: '请选择品名', clearable: true },
          // },
        ]
      },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      entityForm: { ...Model.model },
      rules: {
        productionDate: { required: true, message: '请选择日期', trigger: 'blur' },
        productId: { required: true, message: '请选择精煤品名', trigger: 'blur' }
      },
      nameEntity: { options: [], active: '' },

      nameEntityV: { options: [], active: '' },

      memoryEntity: { fields: {}, triggered: false },
      actions: [],
      tableData: [],
      nameItemEntity: [],
      // filters: { productionDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') },
      // filters: { productionDate: new Date().Format('yyyy-MM-dd') },
      filters: { productionDate: '', keyword: '' },
      timeValue: [],
      timeValue1: [],
      locationList: [],
      locationActive: 0,
      currentTableData: [],
      screen: { full: false },
      // draft: { open: false, entityForm: { ...Model.model }, list: [{ ...form }] },
      productionDraft: { entityForm: { ...Model.model }, list: [{ ...form }] },
      dateList: [],
      loading: false
    }
  },
  created() {
    this.getName()
    this.getContract()
    // this.getFindByKeyword()
    this.getDateList()
    this.getList()
  },
  methods: {
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (
        columnIndex === 0 ||
        columnIndex === 1 ||
        columnIndex === 2 ||
        columnIndex === 3 ||
        columnIndex === 4 ||
        columnIndex === 5 ||
        columnIndex === 6 ||
        columnIndex === 7 ||
        columnIndex === 15
      ) {
        let spanArr = this.getSpanArr(this.currentTableData)
        // 页面列表上 表格合并行 -> 第几列(从0开始)
        // 需要合并多个单元格时 依次增加判断条件即可
        // 数组存储的数据 取出
        const _row = spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      } else {
        //不可以return {rowspan：0， colspan: 0} 会造成数据不渲染， 也可以不写else，eslint过不了的话就返回false
        return false
      }
    },
    // 处理合并行的数据
    getSpanArr: function (data) {
      let spanArr = []
      let pos = ''
      for (let i = 0; i < data.length; i++) {
        // console.log(data[i].id)

        if (i === 0) {
          spanArr.push(1)
          pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].id === data[i - 1].id) {
            spanArr[pos] += 1
            spanArr.push(0)
          } else {
            spanArr.push(1)
            pos = i
          }
        }
      }
      return spanArr
    },

    //设置单个单元格样式   行下标：rowIndex    列下标：columnIndex
    // spanMethod({ row, column, rowIndex, columnIndex }) {
    //   if ([0, 1, 2, 3, 4, 5, 6, 7, 15].includes(columnIndex)) {
    //     // console.log(rowIndex)
    //     if (rowIndex === 0) {
    //       return {
    //         rowspan: this.currentTableData.length - 1,
    //         colspan: 1,
    //       }
    //     } else {
    //       return {
    //         rowspan: 0,
    //         colspan: 0,
    //       }
    //     }
    //   } else if ([17].includes(columnIndex)) {
    //     // 操作列合并
    //     if (rowIndex === 0) {
    //       return {
    //         rowspan: this.currentTableData.length - 1,
    //         colspan: 1,
    //       }
    //     } else {
    //       return {
    //         // 如果不是最后一行则合并
    //         rowspan: 1,
    //         colspan: 0,
    //       }
    //     }
    //   } else if (rowIndex === this.currentTableData.length - 1) {
    //     return [0, 0]
    //   }
    // },

    // spanMethod({ row, column, rowIndex, columnIndex }) {
    //   if ([0, 1, 2, 3, 4, 5, 6, 7].includes(columnIndex)) {
    //     if (rowIndex === 0) {
    //       console.log(this.currentTableData.length - 1)
    //       return {
    //         rowspan: this.currentTableData.length - 1,
    //         colspan: 1,
    //       }
    //     } else {
    //       console.log('33')
    //       return {
    //         rowspan: 0,
    //         colspan: 0,
    //       }
    //     }
    //   } else if ([15].includes(columnIndex)) {
    //     // 操作列合并
    //     if (rowIndex === 0) {
    //       return {
    //         rowspan: this.currentTableData.length - 1,
    //         colspan: 1,
    //       }
    //     } else {
    //       return {
    //         // 如果不是最后一行则合并
    //         rowspan: 1,
    //         colspan: 0,
    //       }
    //     }
    //   } else if (rowIndex === this.currentTableData.length - 1) {
    //     return [0, 0]
    //   }
    // },

    handleAdd() {
      const form = { ...this.form }
      form.id = Math.random().toFixed(6).slice(-6)
      this.listV.push(form)
    },
    handleRemove({ id }) {
      const index = this.listV.findIndex((item) => item.id === id)
      if (index === -1) return false
      this.listV.splice(index, 1)
    },
    handleToLocation(index) {
      this.locationActive = index
      this.currentTableData = this.tableData[this.locationActive].washMixCoalVoList
      // this.setTime()
    },
    clearContract() {
      this.listQuery = { ...listQuery }
      this.indicators = {
        weightCalcRaw: Infinity,
        shovelCountRaw: Infinity,
        beltWeightRaw: Infinity,
        weightRaw: Infinity,
        truckCountRaw: Infinity,
        truckCount: Infinity,
        weight: Infinity,
        beltWeight: Infinity,
        shovelCount: Infinity,
        weightCalc: Infinity
      }
      return false
    },
    // @重写方法-添加了合同搜索字段
    handleFilter(filters) {
      this.filters = { ...filters }
      this.$refs[this.curd].searchChange({ ...this.listQuery, ...filters })
    },
    // @重写方法-添加了clearContract方法
    handleFilterReset() {
      this.clearContract()
      this.$refs[this.curd].searchChange()
    },
    async getDateList(params = {}) {
      try {
        const res = await this.model.getDateList(params)
        if (res.data && res.data.length) {
          this.dateList = res.data
        } else {
          this.dateList = []
        }
      } catch (error) {
        this.dateList = []
      }
    },
    handleFullScreen() {
      this.screen.full = !this.screen.full
      const el = this.$refs.dialogStatus.$el
      const el_body = el.querySelector('.el-dialog__body')
      el_body.style.height = this.screen.full ? '85vh' : '70vh'
    },

    handleFilter(filters) {
      this.filters = filters
      this.getList()
    },
    handleFilterReset() {
      this.filters = {}
      this.getList()
    },

    async handleUpdate(id) {
      this.dialogStatus = 'update'
      const { data } = await Model.detail(id)
      this.entityForm = { ...data }
      this.listV = data.washMixCoalItemList
      delete this.entityForm.washMixCoalItemList
      this.dialogFormVisible = true
    },
    // async handleUpdate(item, type) {
    //   this.entityForm = { ...item }
    //   if (type == 'review') {
    //     this.isshow = true
    //     this.dialogStatus = 'review'
    //   } else {
    //     this.isshow = false
    //     this.dialogStatus = 'update'
    //   }
    //   this.dialogFormVisible = true
    // },

    async getList() {
      this.tableData = []
      this.currentTableData = []
      this.locationList = []
      this.locationActive = 0
      this.loading = true
      const { data } = await Model.page(this.filters)
      if (data.length > 0) {
        this.tableData = data
        // this.currentTableData = this.tableData[this.locationActive].washMixCoalVoList
        // data.forEach((item) => {
        //   this.locationList.push({ label: item.productName })
        // })
        for (var i = 0; i < data.length; i++) {
          for (var j = 0; j < data[i].washMixCoalVoList.length; j++) {
            let obj = {}
            obj = data[i].washMixCoalVoList[j]
            this.currentTableData.push(obj)
            this.loading = false
          }
        }
      } else {
        this.loading = false
      }
    },

    // async getList() {
    //   const { data } = await Model.page(this.filters)
    //   console.log(data.records)
    //   this.currentTableData = data.records
    //   // this.$refs[this.curd].$emit('refresh')
    // },

    // checkParams(
    //   list,
    //   rule = [
    //     'shovelCount',
    //     'shovelWeight',
    //     'weight',
    //     'productName',
    //     'productId',
    //     'productCode',
    //     'productIdRaw',
    //     'productNameRaw',
    //     'productCodeRaw',
    //     'truckCountRaw',
    //     'shovelCountRaw',
    //     'weightCalcRaw',
    //     'weightRaw',
    //   ]
    // ) {
    //   console.log(list)
    //   for (const item of list) {
    //     for (const [key, val] of Object.entries(item)) {
    //       if (rule.includes(key)) continue
    //       if (val === '') {
    //         this.$message({ message: `请检查参数${key}`, type: 'warning' })
    //         return false
    //       }
    //     }
    //   }
    //   return true
    // },
    async saveEntityForm(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.listV.length) {
          const list = []
          this.listV.forEach((item) => {
            let obj = {}
            obj.productIdRaw = item.productIdRaw
            obj.productNameRaw = item.productNameRaw
            obj.productCodeRaw = item.productCodeRaw
            obj.truckCountRaw = item.truckCountRaw
            obj.weightRaw = item.weightRaw
            obj.beltWeightRaw = item.beltWeightRaw
            obj.shovelCountRaw = item.shovelCountRaw
            obj.weightCalcRaw = item.weightCalcRaw
            obj.id = item.id
            list.push({ ...obj })
          })
          // if (!this.checkParams(list)) return false
          this.entityFormLoading = true
          const res = await Model.save({ ...this.entityForm, washMixCoalItemList: list })
          this.entityFormLoading = false
          if (res) {
            this.resetVariant()
            this.getList()
            this.clearDraft()
            // this.draft.open = false
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else {
          this.$message({ message: '生产数据不能为空', type: 'warning' })
        }

        // this.entityFormLoading = true
        // const res = await Model.save({ ...this.entityForm })
        // this.entityFormLoading = false
        // this.resetVariant()
        // this.getList()
        // // this.clearDraft()
        // // this.draft.open = false
        // this.$message({ showClose: true, message: '操作成功', type: 'success' })
      }
    },
    // checkParams(list, rule = ['truckCountRaw', 'weightRaw', 'weight', 'productName', 'productId', 'productCode', 'id']) {
    //       for (const item of list) {
    //         for (const [key, val] of Object.entries(item)) {
    //           // if (rule.includes(key)) continue
    //           // console.log(val)
    //           if (val === '' || val === undefined || val === null) {
    //             this.$message({ message: `请检查参数${key}`, type: 'warning' })
    //             return false
    //           }
    //         }
    //       }
    //       return true
    //     },

    async handleDel(row) {
      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await Model.deleteId(row.id)
          if (res) {
            this.$message({ type: 'success', message: '删除成功!' })
            this.getList()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },

    async getContract() {
      const { data } = await getCustomerContract()
      // 供应商名称返回id用来查询
      this.contractEntity.options = this.formatContract({ data, key: { customerName: 'displayName', customerId: 'id' } })
    },
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        // console.log(data.records)
        this.nameItemEntity = data.records
        this.nameEntity.options = data.records.map((item) => {
          return {
            name: item.name,
            id: item.id,
            code: item.code,
            coalCategory: item.coalCategory
          }
        })
      } catch (error) {}
    },
    // async getFindByKeyword() {
    //   const { data } = await findByKeyword()
    //   this.nameItemEntity = data
    // },

    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.nameEntityV.active = ''
        this.contractEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.listV = []
      this.handleAdd()
    },

    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    handleNameEntity(val) {
      // this.entityForm.productName = val
      this.entityForm = { ...this.entityForm, productName: val }
    },

    // handleNameEntityV(row) {
    //   console.log(row)
    //   let data = this.listV
    //   setTimeout(() => {
    //     for (var i = 0; i < data.length; i++) {
    //       let ff = data[i]
    //       if (row.id == ff.id) {
    //         console.log(this.nameItemEntity)
    //         const item = this.nameItemEntity.find((item) => item.productId == row.productIdRaw)
    //         console.log(item)
    //         ff.productIdRaw = item.productId
    //         ff.productCodeRaw = item.code
    //         console.log(ff)
    //         this.$set(data, i, ff)
    //       }
    //     }
    //   }, 300)
    // },

    handleNameEntityV(row) {
      // console.log(row)
      let data = this.listV
      setTimeout(() => {
        for (var i = 0; i < data.length; i++) {
          let ff = data[i]
          if (row.id == ff.id) {
            // console.log(this.nameItemEntity)
            const itemf = this.nameItemEntity.find((item) => item.name == row.productNameRaw)
            // console.log(itemf)
            ff.productIdRaw = itemf.id
            ff.productCodeRaw = itemf.code
            // console.log(ff)
            this.$set(data, i, ff)
          }
        }
      }, 300)
    },

    headClass() {
      // border:none;
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;'
    },

    clearDraft() {
      this.productionDraft = { entityForm: { ...Model.model }, list: [{ ...form }] }
      Cache.remove('productionDraft')
    },
    /**
     * @status {boolean} 真是保存记录，假是展示
     */
    createDraft(status) {
      if (status || this.dialogStatus === 'create') {
        return {
          entityForm: { ...this.entityForm }
        }
      } else {
        this.entityForm = { ...this.productionDraft.entityForm }
      }
    },
    // async handleCreate(type = true) {
    //   if (type) {
    //     this.dialogStatus = 'create'
    //     this.dialogFormVisible = true
    //     this.entityForm = { ...this.entityForm }
    //     const productionDraft = Cache.get('productionDraft')
    //     if (productionDraft) {
    //       this.productionDraft = productionDraft
    //       this.createDraft(false)
    //     } else {
    //       this.productionDraft = this.createDraft(true)
    //       Cache.set('productionDraft', this.productionDraft)
    //     }
    //   } else {
    //     Cache.remove('productionDraft')
    //     this.productionDraft = this.createDraft(true)
    //     Cache.set('productionDraft', this.productionDraft)
    //   }
    // },
    contrastField(a, b) {
      // if (a === null) return true
      a = JSON.parse(JSON.stringify(a))
      b = JSON.parse(JSON.stringify(b))
      a.list.forEach((item) => {
        for (const [k] of Object.entries(item)) {
          if (k.includes('_')) {
            item[k] = ''
          }
        }
      })
      b.list.forEach((item) => {
        for (const [k] of Object.entries(item)) {
          if (k.includes('_')) {
            item[k] = ''
          }
        }
      })
      const strA = JSON.stringify(a)
      const strB = JSON.stringify(b)
      return strA === strB
    },
    handleClose() {
      this.resetVariant()
      // const productionDraft = Cache.get('productionDraft')
      // if (this.dialogStatus === 'create') {
      //   const new_productionDraft = this.createDraft(true)
      //   const status = this.contrastField(productionDraft, new_productionDraft)
      //   if (productionDraft === null || !status) {
      //     this.$confirm('当前未保存, 是否保存草稿?', '提示', {
      //       confirmButtonText: '不保存',
      //       cancelButtonText: '保存草稿',
      //       type: 'info',
      //       cancelButtonClass: 'btn-custom-save',
      //       confirmButtonClass: 'btn-custom-cancel',
      //     })
      //       .then(() => {
      //         this.resetVariant()
      //       })
      //       .catch(() => {
      //         Cache.set('productionDraft', new_productionDraft)
      //         this.resetVariant()
      //       })
      //   } else {
      //     this.resetVariant()
      //   }
      // } else {
      //   this.resetVariant()
      // }
    }
  },
  watch: {
    // 'entityForm.startTime'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.startTime1'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.startTime2'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.stopTime'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.stopTime1'(v) {
    //   this.handlePickerChange(false)
    // },
    // 'entityForm.stopTime2'(v) {
    //   this.handlePickerChange(false)
    // },
    'entityForm.contractId'(id) {
      const value = this.filterContractItem(id, 'contractEntity')
      this.contractEntity.active = value
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      if (this.dialogStatus === 'create') this.entityForm.productName = form.productName || ''
      this.entityForm.contractCode = form.customerName || ''
      this.entityForm.contractId = form.customerId || ''
      this.entityForm.contractName = form.name || ''
    },

    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntity.active = item.name
        // this.entityForm.productCode = item.code || ''
        // this.entityForm.productId = item.id || ''

        this.entityForm = { ...this.entityForm, productId: item.id || '', productCode: item.code || '' }
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
      }
    },
    'entityForm.productNameRaw'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntityV.active = item.name
        this.entityForm.productCodeRaw = item.code || ''
        this.entityForm.productIdRaw = item.id || ''
      } else {
        this.nameEntityV.active = ''
        this.entityForm.productCodeRaw = ''
        this.entityForm.productIdRaw = ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
.table-container {
  height: 90vh;
  padding: 20px 20px 0 20px;
  overflow-y: auto !important;
  background: #fff;
  box-sizing: border-box;
  padding-bottom: 30px;
  // <!-- height:84rem;: auto;background:#fff; -->
}
::v-deep .el-icon-date {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-icon-time {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}
// @media screen and (max-width:480px;) {
//   height: 80vh !important;
// }

::v-deep .el-input__prefix {
  right: 5px;
  left: auto;
}

::v-deep .el-input--prefix .el-input__inner {
  padding-left: 10px;
}
::v-deep .el-table__fixed-right::before {
  width: 0;
}

.dialog {
  width: 980px;
  height: 740px;
}
// ::v-deep .el-dialog__footer {
//   width: 100%;
//   padding: 0px 0px 0px;
// }

::v-deep .el-dialog__footer {
  padding: 0px 0px 0px;
}
.dialog-footer {
  // margin-right: 44px;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px #f1f1f2;
  padding: 10px 44px 10px 10px;
  &-all {
    font-size: 14px;
    margin-left: 34px;
    // margin-right: auto;
    flex: 0 0 58px;
    color: #ff9639;
    background: #fff;
    border: 1px solid #ff9639;
    &:hover {
      background: transparent;
    }
    &:nth-of-type(1) {
      // margin-left: 15px;
      margin-right: auto;
    }
  }
  &-btns {
    width: 85px;
    height: 35px;
    border: 1px solid #2f79e8;
    background: #fff;
    color: #2f79e8;
    margin-left: 20px;
    font-size: 14px;
    &:last-of-type {
      border: 1px solid #d5d5d5;
      color: #747474;
    }
  }
}

.form-layout {
  padding: 0 20px;
}

.form-title {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 17px;
    background: #2f79e8;
    height: 16px;
  }
}

.form-titl2 {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 11px;
    background: #2f79e8;
    height: 16px;
  }
}

.form-titleV {
  font-size: 14px;
  position: relative;
  padding: 10px;
  font-weight: 900;
}

::v-deep .el-input--mini .el-input__inner {
  height: 29.5px;
  line-height: 29.5px;
}

.opt-btn {
  cursor: pointer;
  margin: 0 3px;
  color: #fff;
  border: none;
}
::v-deep .el-form--label-top .el-form-item__label {
  padding: 0;
  font-weight: 400;
  font-size: 13px;
  color: #646464;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 13px;
}
.name {
  // color: blue;
  text-decoration: underline;
  cursor: pointer;
  transition: 0.5s all;
  &:hover,
  :focus {
    color: #3234d3;
  }
}

.excessive {
  width: 100%;
  color: red;
}

::v-deep .percent-column {
  .cell {
    top: 10px;
    height: 42px;
  }
}
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}
.column {
  display: inline-block;
  width: 100%;
  height: 20px;
}
.app-container {
  height: 100vh;
}
.table {
  width: 100%;
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.el-button--export-all:focus,
.el-button--export-all:hover {
  background: #ff9639 !important;
  border-color: #fff !important;
  color: #fff !important;
}

.el-button--export-all {
  color: #ff9639;
  background-color: #fff;
  border-color: #ff9639;
  padding: 5px 8px;
  margin-top: -6px;
  min-width: 50px;
}
::v-deep // 合计行样式
.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  height: 50px;
  line-height: 50px;
  color: #ff9639;
  font-size: 13px;
  background: rgb(245, 246, 251);
}
::v-deep .panel-list {
  margin-left: -25px;
}

::v-deep .info {
  display: flex;
  justify-content: space-evenly;
  flex-flow: wrap;
  span {
    padding: 3px;
  }
  // justify-content: start;
  .title {
    span {
      margin-left: 5px;
      color: #f8a20f;
      font-weight: bold;
    }
  }
}
::v-deep .orange-row {
  color: #f8a20f;
  font-size: 14px;
  font-weight: bold;
  height: 60px;
}
::v-deep .row-layout {
  font-size: 14px;
  height: 60px;
}
.panel-list-item {
  height: 40px;
  padding: 5px;
}
</style>
