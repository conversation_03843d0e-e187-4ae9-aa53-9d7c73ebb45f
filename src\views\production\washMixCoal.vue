<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <div class="table-container">
      <el-table :data="currentTableData" element-loading-text="加载中..." v-loading="loading" :span-method="spanMethod" border
                class="table" :header-cell-style="headClass">
        <el-table-column prop="productionDate" label="日期" align="center" width="150" />
        <el-table-column prop="contractName" label="合同" align="center" />
        <el-table-column label="精煤" align="center" width="250">
          <el-table-column label="品名" prop="productName" min-width="170" align="center" />
          <el-table-column label="吨数" prop="actualWeight" min-width="80" align="center" />
        </el-table-column>
        <el-table-column label="中煤" align="center" width="250">
          <el-table-column label="品名" prop="productNameMidCoal" min-width="170" align="center" />
          <el-table-column label="吨数" prop="actualWeightMidCoal" min-width="80" align="center" />
        </el-table-column>
        <el-table-column label="矸石" align="center" width="250">
          <el-table-column label="品名" prop="productNameGangue" min-width="170" align="center" />
          <el-table-column label="吨数" prop="actualWeightGangue" min-width="80" align="center" />
        </el-table-column>
        <el-table-column label="煤泥" align="center" width="250">
          <el-table-column label="品名" prop="productNameCoalMud" min-width="170" align="center" />
          <el-table-column label="吨数" prop="actualWeightCoalMud" min-width="80" align="center" />
        </el-table-column>

        <el-table-column label="煤粉" align="center" width="250">
          <el-table-column label="品名" prop="productNameCoalPulverized" min-width="170" align="center" />
          <el-table-column label="吨数" prop="actualWeightCoalPulverized" min-width="80" align="center" />
        </el-table-column>

        <el-table-column prop="productNameRaw" label="原煤品名" min-width="170" align="center" />
        <el-table-column prop="usedWeight" label="吨数" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.usedWeight||'&nbsp;' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template slot-scope="scope">
            <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row.id)"
                    v-if="perms[`${curd}:update`]||false">编辑</el-tag>
            <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">
              删除
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog :fullscreen="screen.full" top="5vh" ref="dialogStatus" :title="`${textMap[dialogStatus]}(${title})`"
               :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false"
               :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col style="margin-bottom: 20px;">
            <div class="form-title">基本类型</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="productionDate">
                    <date-select v-model="entityForm.productionDate" clearable :isset="true" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同" prop="contractId">
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 filterable clearable placeholder="请选择合同" style="width:100%" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>
          <el-col style="margin-bottom: 20px;">
            <div class="form-titl2 form-warp">
              <span style="position: relative;top:-3px">原煤数据</span>
              <el-button type="export-all" @click="handleAdd">新增</el-button>
            </div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-table :data="listV" style="width: 100%" stripe :header-cell-style="headClass">
                    <el-table-column label="原煤名称" align="center">
                      <template slot-scope="scope">
                        <select-down :value.sync="scope.row.productNameRaw" :code.sync="scope.row.productCodeRaw"
                                     @eventChange="handleNameEntityV(scope.row)" :stock.sync="scope.row._stock"
                                     :id.sync="scope.row.productIdRaw" :list="nameItemEntity" />
                      </template>
                    </el-table-column>
                    <el-table-column label="吨数" align="center">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.usedWeight" clearable />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template slot-scope="scope">
                        <el-tag class="opt-btn" color="#2f79e8" @click="handleRemove(scope.row)">删除</el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col style="margin-bottom: 20px;">
            <div class="form-title">洗煤生产数据</div>
            <div class="form-titleV">精煤数据</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col :span="8">
                  <el-form-item label="精煤品名" prop="productId">
                    <el-select v-model="entityForm.productId" @change="handleNameEntity" filterable>
                      <el-option v-for="item in cleanCoalList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="吨数" prop="actualWeight">
                    <el-input v-model="entityForm.actualWeight" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-titleV">中煤数据</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col :span="8">
                  <el-form-item label="中煤品名" prop="productIdMidCoal">
                    <el-select v-model="entityForm.productIdMidCoal" @change="handleMidNameEntity" filterable>
                      <el-option v-for="item in middlingCoalList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="吨数" prop="actualWeightMidCoal">
                    <el-input v-model="entityForm.actualWeightMidCoal" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-titleV">矸石数据</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col :span="8">
                  <el-form-item label="矸石品名" prop="productIdGangue">
                    <el-select v-model="entityForm.productIdGangue" @change="handleGangueNameEntity" filterable>
                      <el-option v-for="item in middlingCoalList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="吨数" prop="actualWeightGangue">
                    <el-input v-model="entityForm.actualWeightGangue" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-titleV">煤泥数据</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col :span="8">
                  <el-form-item label="煤泥" prop="productIdCoalMud">
                    <el-select v-model="entityForm.productIdCoalMud" @change="handleMudNameEntity" filterable>
                      <el-option v-for="item in middlingCoalList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="吨数" prop="actualWeightCoalMud">
                    <el-input v-model="entityForm.actualWeightCoalMud" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-titleV">煤粉数据</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col :span="8">
                  <el-form-item label="煤粉" prop="productIdCoalPulverized">
                    <el-select v-model="entityForm.productIdCoalPulverized" @change="handleCoalPulverizedEntity" filterable>
                      <el-option v-for="item in middlingCoalList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="吨数" prop="actualWeightCoalPulverized">
                    <el-input v-model="entityForm.actualWeightCoalPulverized" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col />
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button class="dialog-footer-all" style="  color: #adadad;
  border: 1px solid #adadad;" @click="handleFullScreen">{{ screen.full ? '取消全屏' : '全屏' }}
        </el-button>
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Mixins from '@/utils/mixins'
import { findByKeyword } from '@/api/stock'
import { listQuery } from '@/const'
import Model from '@/model/production/washMixCoal'
import productModel from '@/model/product/productList'
import { getCustomerContract } from '@/api/quality'
import Cache from '@/utils/cache'
import selectDown from '@/components/SelectDown/index.vue'
import DictSelect from '@/components/DictSelect/index.vue'

const form = {
  id: '1',
  productCodeRaw: '',
  productNameRaw: '',
  productIdRaw: '',
  truckCountRaw: '',
  weightRaw: '',
  beltWeightRaw: '',
  shovelCountRaw: '',
  weightCalcRaw: ''
}
export default {
  name: 'washMixCoal',
  mixins: [Mixins],
  components: { DictSelect, selectDown },
  data() {
    return {
      curd: 'washMixCoal',
      model: Model,
      form: { ...form },
      listV: [{ ...form }],
      indicators: {
        weightCalcRaw: Infinity,
        shovelCountRaw: Infinity,
        beltWeightRaw: Infinity,
        weightRaw: Infinity,
        truckCountRaw: Infinity,
        truckCount: Infinity,
        weight: Infinity,
        beltWeight: Infinity,
        shovelCount: Infinity,
        weightCalc: Infinity
      },
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'productionDate',
            component: 'date-select',
            defaultValue: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
            resetClearDefault: true,
            filter: 'productionDate',
            props: {
              placeholder: '请选择日期',
              'picker-options': {
                cellClassName: (time) => {
                  const flag = this.dateList.includes(time.Format('yyyy-MM-dd'))
                  if (flag) return 'background'
                }
              }
            }
          },
          {
            prop: 'productName',
            filter: 'keyword',
            component: 'select-ProductName',
            props: { placeholder: '请选择品名', clearable: true, istype: 'xi', iconisshow: false }
          }
        ]
      },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      entityForm: {
        // 精煤
        productId: '', //精煤id
        productName: '', //精煤名称
        productCode: '', //精煤编码
        actualWeight: '', //精煤吨数
        // 中煤
        productIdMidCoal: '', //中煤id
        productNameMidCoal: '', //中煤名称
        productCodeMidCoal: '', //中煤编码
        actualWeightMidCoal: '', //吨数

        // 矸石
        productIdGangue: '', //矸石id
        productNameGangue: '', //矸石名称
        productCodeGangue: '', //矸石编码
        actualWeightGangue: '', //矸石吨数

        // 煤泥
        productIdCoalMud: '', //煤泥id
        productNameCoalMud: '', //煤泥名称
        productCodeCoalMud: '', //煤泥编码
        actualWeightCoalMud: '', //煤泥吨数

        // 煤粉
        productIdCoalPulverized: '', //煤粉id
        productNameCoalPulverized: '', //煤粉名称
        productCodeCoalPulverized: '', //煤粉编码
        actualWeightCoalPulverized: '' //煤粉吨数
      },
      rules: {
        productionDate: { required: true, message: '请选择日期', trigger: 'blur' },
        productId: { required: true, message: '请选择精煤品名', trigger: 'blur' }
      },
      nameEntity: { options: [], active: '' },

      nameEntityV: { options: [], active: '' },

      memoryEntity: { fields: {}, triggered: false },
      actions: [],
      tableData: [],
      rawCoalList: [],
      cleanCoalList: [],
      middlingCoalList: [],
      nameItemEntity: [],
      filters: { productionDate: '', keyword: '' },
      timeValue: [],
      timeValue1: [],
      locationList: [],
      locationActive: 0,
      currentTableData: [],
      screen: { full: false },
      productionDraft: { entityForm: { ...Model.model }, list: [{ ...form }] },
      dateList: [],
      loading: false
    }
  },
  created() {
    this.getName()
    this.getproductName()
    this.getContract()
    this.getDateList()
    this.getList()
  },
  methods: {
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (
        columnIndex === 0 ||
        columnIndex === 1 ||
        columnIndex === 2 ||
        columnIndex === 3 ||
        columnIndex === 4 ||
        columnIndex === 5 ||
        columnIndex === 6 ||
        columnIndex === 7 ||
        columnIndex === 8 ||
        columnIndex === 9 ||
        columnIndex === 10 ||
        columnIndex === 11 ||
        columnIndex === 14
      ) {
        let spanArr = this.getSpanArr(this.currentTableData)
        // 页面列表上 表格合并行 -> 第几列(从0开始)
        // 需要合并多个单元格时 依次增加判断条件即可
        // 数组存储的数据 取出
        const _row = spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      } else {
        //不可以return {rowspan：0， colspan: 0} 会造成数据不渲染， 也可以不写else，eslint过不了的话就返回false
        return false
      }
    },
    // 处理合并行的数据
    getSpanArr: function (data) {
      let spanArr = []
      let pos = ''
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          spanArr.push(1)
          pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].id === data[i - 1].id) {
            spanArr[pos] += 1
            spanArr.push(0)
          } else {
            spanArr.push(1)
            pos = i
          }
        }
      }
      return spanArr
    },
    handleAdd() {
      const form = { ...this.form }
      form.id = Math.random().toFixed(6).slice(-6)
      this.listV.push(form)
    },
    handleRemove({ id }) {
      const index = this.listV.findIndex((item) => item.id === id)
      if (index === -1) return false
      this.listV.splice(index, 1)
    },
    handleToLocation(index) {
      this.locationActive = index
      this.currentTableData = this.tableData[this.locationActive].washMixCoalVoList
      // this.setTime()
    },
    clearContract() {
      this.listQuery = { ...listQuery }
      this.indicators = {
        weightCalcRaw: Infinity,
        shovelCountRaw: Infinity,
        beltWeightRaw: Infinity,
        weightRaw: Infinity,
        truckCountRaw: Infinity,
        truckCount: Infinity,
        weight: Infinity,
        beltWeight: Infinity,
        shovelCount: Infinity,
        weightCalc: Infinity
      }
      return false
    },
    async getDateList(params = {}) {
      try {
        const res = await this.model.getDateList(params)
        if (res.data && res.data.length) {
          this.dateList = res.data
        } else {
          this.dateList = []
        }
      } catch (error) {
        this.dateList = []
      }
    },
    handleFullScreen() {
      this.screen.full = !this.screen.full
      const el = this.$refs.dialogStatus.$el
      const el_body = el.querySelector('.el-dialog__body')
      el_body.style.height = this.screen.full ? '85vh' : '70vh'
    },
    handleFilter(filters) {
      this.filters = filters
      this.getList()
    },
    handleFilterReset() {
      this.filters = {}
      this.getList()
    },
    async handleUpdate(id) {
      this.dialogStatus = 'update'
      const { data } = await Model.detail(id)
      this.entityForm = { ...data }
      this.listV = data.washMixCoalItemList
      delete this.entityForm.washMixCoalItemList
      this.dialogFormVisible = true
    },
    async getList() {
      this.tableData = []
      this.currentTableData = []
      this.locationList = []
      this.locationActive = 0
      this.loading = true
      const { data } = await Model.page(this.filters)
      if (data.length > 0) {
        this.tableData = data
        // this.currentTableData = this.tableData[this.locationActive].washMixCoalVoList
        // data.forEach((item) => {
        //   this.locationList.push({ label: item.productName })
        // })
        for (var i = 0; i < data.length; i++) {
          for (var j = 0; j < data[i].washMixCoalVoList.length; j++) {
            let obj = {}
            obj = data[i].washMixCoalVoList[j]
            this.currentTableData.push(obj)
            this.loading = false
          }
        }
      } else {
        this.loading = false
      }
    },
    async saveEntityForm(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        if (this.listV.length) {
          const list = []
          this.listV.forEach((item) => {
            let obj = {}
            obj.productIdRaw = item.productIdRaw
            obj.usedWeight = item.usedWeight
            obj.productNameRaw = item.productNameRaw
            obj.productCodeRaw = item.productCodeRaw
            obj.truckCountRaw = item.truckCountRaw
            obj.weightRaw = item.weightRaw
            obj.beltWeightRaw = item.beltWeightRaw
            obj.shovelCountRaw = item.shovelCountRaw
            obj.weightCalcRaw = item.weightCalcRaw
            obj.id = item.id
            list.push({ ...obj })
          })
          // if (!this.checkParams(list)) return false
          this.entityFormLoading = true
          const res = await Model.save({ ...this.entityForm, washMixCoalItemList: list })
          this.entityFormLoading = false
          if (res) {
            this.resetVariant()
            this.getList()
            this.clearDraft()
            // this.draft.open = false
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else {
          this.$message({ message: '生产数据不能为空', type: 'warning' })
        }
      }
    },
    async handleDel(row) {
      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await Model.deleteId(row.id)
          if (res) {
            this.$message({ type: 'success', message: '删除成功!' })
            this.getList()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },
    async getContract() {
      const { data } = await getCustomerContract()
      // 供应商名称返回id用来查询
      this.contractEntity.options = this.formatContract({ data, key: { customerName: 'displayName', customerId: 'id' } })
    },

    async getproductName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        // this.nameItemEntity = data.records
        this.nameEntity.options = data.records.map((item) => {
          return {
            name: item.name,
            id: item.id,
            code: item.code,
            coalCategory: item.coalCategory,
            shovelWeight: item.shovelWeight
          }
        })
      } catch (error) {}
    },
    async getName() {
      try {
        productModel.page({ ...this.listQuery, filter_EQS_type: 'YM', size: 999 }).then((res) => {
          // this.rawCoalList = res.data.records
          this.nameItemEntity = res.data.records
        })
        productModel.page({ ...this.listQuery, size: 999 }).then((res) => {
          this.cleanCoalList = res.data.records
        })
        productModel.page({ ...this.listQuery, filter_EQS_type: 'ZM', size: 999 }).then((res) => {
          this.middlingCoalList = res.data.records
        })
      } catch (error) {}
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.nameEntityV.active = ''
        this.contractEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.listV = []
      this.handleAdd()
    },
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    handleNameEntity(val) {
      const v = this.cleanCoalList.find((item) => item.id === val)
      this.entityForm.productId = v.id
      this.entityForm.productName = v.name
      this.entityForm.productCode = v.code
    },
    handleCoalPulverizedEntity(val) {
      const v = this.middlingCoalList.find((item) => item.id === val)
      this.entityForm.productIdCoalPulverized = v.id
      this.entityForm.productNameCoalPulverized = v.name
      this.entityForm.productCodeCoalPulverized = v.code
    },
    handleMudNameEntity(val) {
      const v = this.middlingCoalList.find((item) => item.id === val)
      this.entityForm.productIdCoalMud = v.id
      this.entityForm.productNameCoalMud = v.name
      this.entityForm.productCodeCoalMud = v.code
    },
    handleMidNameEntity(val) {
      const v = this.middlingCoalList.find((item) => item.id === val)
      this.entityForm.productIdMidCoal = v.id
      this.entityForm.productNameMidCoal = v.name
      this.entityForm.productCodeMidCoal = v.code
    },
    handleGangueNameEntity(val) {
      const v = this.middlingCoalList.find((item) => item.id === val)
      this.entityForm.productIdGangue = v.id
      this.entityForm.productNameGangue = v.name
      this.entityForm.productCodeGangue = v.code
    },
    handleNameEntityV(row) {
      console.log(row)
      let data = this.listV
      setTimeout(() => {
        for (var i = 0; i < data.length; i++) {
          let ff = data[i]
          if (row.id == ff.id) {
            const itemf = this.nameItemEntity.find((item) => item.name == row.productNameRaw)
            ff.productIdRaw = itemf.id
            ff.productCodeRaw = itemf.code
            this.$set(data, i, ff)
          }
        }
      }, 300)
    },
    headClass() {
      // border:none;
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;'
    },
    clearDraft() {
      this.productionDraft = { entityForm: { ...Model.model }, list: [{ ...form }] }
      Cache.remove('productionDraft')
    },
    /**
     * @status {boolean} 真是保存记录，假是展示
     */
    createDraft(status) {
      if (status || this.dialogStatus === 'create') {
        return {
          entityForm: { ...this.entityForm }
        }
      } else {
        this.entityForm = { ...this.productionDraft.entityForm }
      }
    },
    contrastField(a, b) {
      // if (a === null) return true
      a = JSON.parse(JSON.stringify(a))
      b = JSON.parse(JSON.stringify(b))
      a.list.forEach((item) => {
        for (const [k] of Object.entries(item)) {
          if (k.includes('_')) {
            item[k] = ''
          }
        }
      })
      b.list.forEach((item) => {
        for (const [k] of Object.entries(item)) {
          if (k.includes('_')) {
            item[k] = ''
          }
        }
      })
      const strA = JSON.stringify(a)
      const strB = JSON.stringify(b)
      return strA === strB
    },
    handleClose() {
      this.resetVariant()
      // const productionDraft = Cache.get('productionDraft')
      // if (this.dialogStatus === 'create') {
      //   const new_productionDraft = this.createDraft(true)
      //   const status = this.contrastField(productionDraft, new_productionDraft)
      //   if (productionDraft === null || !status) {
      //     this.$confirm('当前未保存, 是否保存草稿?', '提示', {
      //       confirmButtonText: '不保存',
      //       cancelButtonText: '保存草稿',
      //       type: 'info',
      //       cancelButtonClass: 'btn-custom-save',
      //       confirmButtonClass: 'btn-custom-cancel',
      //     })
      //       .then(() => {
      //         this.resetVariant()
      //       })
      //       .catch(() => {
      //         Cache.set('productionDraft', new_productionDraft)
      //         this.resetVariant()
      //       })
      //   } else {
      //     this.resetVariant()
      //   }
      // } else {
      //   this.resetVariant()
      // }
    }
  },
  watch: {
    'entityForm.contractId'(id) {
      const value = this.filterContractItem(id, 'contractEntity')
      this.contractEntity.active = value
    },
    // 'contractEntity.active'(value) {
    //   if (!value || !Array.isArray(value)) return
    //   const form = this.filterContractItem(value, 'contractEntity')
    //   if (this.dialogStatus === 'create') this.entityForm.productName = form.productName || ''
    //   this.entityForm.contractCode = form.customerName || ''
    //   this.entityForm.contractId = form.customerId || ''
    //   this.entityForm.contractName = form.name || ''
    // },

    'contractEntity.active'(value) {
      console.log(value)
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      console.log(form)
      this.entityForm = { ...this.entityForm, productName: form.productName, productId: form.productId }
      this.nameEntity.active = form.productName || ''
      this.entityForm.contractId = form.customerId || ''
      // this.entityForm.customerName = form.customerName
      this.entityForm.contractCode = form.customerName || ''
      this.entityForm.contractName = form.name || ''
      // this.entityForm.supplierName = form.secondParty
    },

    // 'entityForm.productName'(name) {
    //   const item = this.nameEntity.options.find((item) => item.name === name)
    //   if (item) {
    //     this.nameEntity.active = item.name
    //     this.entityForm = { ...this.entityForm, productId: item.id || '', productCode: item.code || '' }
    //   } else {
    //     this.nameEntity.active = ''
    //     this.entityForm.productCode = ''
    //     this.entityForm.productId = ''
    //   }
    // },
    'entityForm.productNameRaw'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntityV.active = item.name
        this.entityForm.productCodeRaw = item.code || ''
        this.entityForm.productIdRaw = item.id || ''
      } else {
        this.nameEntityV.active = ''
        this.entityForm.productCodeRaw = ''
        this.entityForm.productIdRaw = ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
.table-container {
  height: 90vh;
  padding: 20px 20px 0 20px;
  overflow-y: auto !important;
  background: #fff;
  box-sizing: border-box;
  padding-bottom: 30px;
  // <!-- height:84rem;: auto;background:#fff; -->
}

::v-deep .el-icon-date {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-icon-time {
  transform: scale(1.3);
  margin-top: 1px;
}

::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 70vh;
}

// @media screen and (max-width:480px;) {
//   height: 80vh !important;
// }

::v-deep .el-input__prefix {
  right: 5px;
  left: auto;
}

::v-deep .el-input--prefix .el-input__inner {
  padding-left: 10px;
}

::v-deep .el-table__fixed-right::before {
  width: 0;
}

.dialog {
  width: 980px;
  height: 740px;
}

// ::v-deep .el-dialog__footer {
//   width: 100%;
//   padding: 0px 0px 0px;
// }

::v-deep .el-dialog__footer {
  padding: 0px 0px 0px;
}

.dialog-footer {
  // margin-right: 44px;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px #f1f1f2;
  padding: 10px 44px 10px 10px;

  &-all {
    font-size: 14px;
    margin-left: 34px;
    // margin-right: auto;
    flex: 0 0 58px;
    color: #ff9639;
    background: #fff;
    border: 1px solid #ff9639;

    &:hover {
      background: transparent;
    }

    &:nth-of-type(1) {
      // margin-left: 15px;
      margin-right: auto;
    }
  }

  &-btns {
    width: 85px;
    height: 35px;
    border: 1px solid #2f79e8;
    background: #fff;
    color: #2f79e8;
    margin-left: 20px;
    font-size: 14px;

    &:last-of-type {
      border: 1px solid #d5d5d5;
      color: #747474;
    }
  }
}

.form-layout {
  padding: 0 20px;
}

.form-title {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 17px;
    background: #2f79e8;
    height: 16px;
  }
}

.form-titl2 {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 11px;
    background: #2f79e8;
    height: 16px;
  }
}

.form-titleV {
  font-size: 14px;
  position: relative;
  padding: 10px;
  font-weight: 900;
}

::v-deep .el-input--mini .el-input__inner {
  height: 29.5px;
  line-height: 29.5px;
}

.opt-btn {
  cursor: pointer;
  margin: 0 3px;
  color: #fff;
  border: none;
}

::v-deep .el-form--label-top .el-form-item__label {
  padding: 0;
  font-weight: 400;
  font-size: 13px;
  color: #646464;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 13px;
}

.name {
  // color: blue;
  text-decoration: underline;
  cursor: pointer;
  transition: 0.5s all;

  &:hover,
  :focus {
    color: #3234d3;
  }
}

.excessive {
  width: 100%;
  color: red;
}

::v-deep .percent-column {
  .cell {
    top: 10px;
    height: 42px;
  }
}

.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);

  &-contract {
    width: 140px;
  }

  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;

    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }

    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;

      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

.column {
  display: inline-block;
  width: 100%;
  height: 20px;
}

.app-container {
  height: 100vh;
}

.table {
  width: 100%;
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.el-button--export-all:focus,
.el-button--export-all:hover {
  background: #ff9639 !important;
  border-color: #fff !important;
  color: #fff !important;
}

.el-button--export-all {
  color: #ff9639;
  background-color: #fff;
  border-color: #ff9639;
  padding: 5px 8px;
  margin-top: -6px;
  min-width: 50px;
}

::v-deep // 合计行样式
.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  height: 50px;
  line-height: 50px;
  color: #ff9639;
  font-size: 13px;
  background: rgb(245, 246, 251);
}

::v-deep .panel-list {
  margin-left: -25px;
}

::v-deep .info {
  display: flex;
  justify-content: space-evenly;
  flex-flow: wrap;

  span {
    padding: 3px;
  }

  // justify-content: start;
  .title {
    span {
      margin-left: 5px;
      color: #f8a20f;
      font-weight: bold;
    }
  }
}

::v-deep .orange-row {
  color: #f8a20f;
  font-size: 14px;
  font-weight: bold;
  height: 60px;
}

::v-deep .row-layout {
  font-size: 14px;
  height: 60px;
}

.panel-list-item {
  height: 40px;
  padding: 5px;
}
</style>
