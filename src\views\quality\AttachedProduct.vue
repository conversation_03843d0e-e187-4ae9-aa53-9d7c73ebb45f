<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimportV`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" :actions="actions" :changeactions="changeactions" />
    <s-curd :ref="curd" :name="curd" :model="model" otherHeight="130" :actions="actions" :list-query="listQuery"
            :changeactions="changeactions" :showSummary='true'>
      <el-table-column label="日期" slot="date" prop="date" width="100" fixed="left" align='center'>
        <template slot-scope="scope"><span class="name">{{scope.row.date}}</span></template>
      </el-table-column>

      <el-table-column label="类型" slot="type" prop="type" width="100" fixed="left" align='center'>
        <template slot-scope="scope"><span class="name">{{scope.row.type}}</span></template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" min-width="100" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)">编辑</el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
    </s-curd>
    <!-- <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="closeDialog" :close-on-click-modal="false" :close-on-press-escape="false">
      <laboratoryImport ref="excelref" optName='create' v-if="dialogFormVisible" type="b_contract_party_type"
                        :exportExcelDialogVisible.sync="dialogFormVisible" @setSubmittext='setSubmittext'
                        :traitSettings="tableOption" :tableOption="model.tableOption" :entityForm="entityForm" Buttontext="save">
      </laboratoryImport>
      <div class="form-title">附件信息</div>
      <div class="form-layout">
        <el-collapse v-model="collapse">
          <el-collapse-item title="上传附件" name="1">
            <div class="upload">
              <div class="upload-list">
                <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                  <i class="el-icon-close icon" @click="handleRemoveUpload(index)"></i>
                  <el-link class="up-load-warp-link" :underline=" false" :href="item.uri">{{item.display}}</el-link>
                </div>
                <upload-attachment ref="upload" class="upload-attachment" :uploadData="uploadData" source="coalSample"
                                   :size="size" :limitNum="limitNum" accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg"
                                   @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete" listType="text" />
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div style="float:right;margin-top:20px">
        <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" @click="okbtnV()">上传322
        </el-button>
        <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;"
                   @click="closeExportExcelDialogV()">取消222
        </el-button>
      </div>
    </el-dialog> -->

    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="entityForm.date" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="名称" prop="name">
                    <el-input v-model="entityForm.name" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="类型" prop="type">
                    <dict-select style="width:100%" v-model="entityForm.type" type="b_qual_attach_product_type"></dict-select>
                  </el-form-item>
                </el-col>
                <el-col> </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">化验指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="硫St,d" prop="std">
                    <el-input v-model="entityForm.std" :oninput="field.decimal" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="灰Ad" prop="ad">
                    <el-input v-model="entityForm.ad" clearable :oninput="field.decimal" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="高位发热量" prop="qgr">
                    <!-- :oninput="field.int" -->
                    <el-input v-model="entityForm.qgr" :oninput="field.decimal" clearable> </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="低位发热量" prop="qnet">
                    <el-input v-model="entityForm.qnet" :oninput="field.decimal" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||perms[`${curd}:save`]|| false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisibleV"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisibleV">
        <laboratoryImport ref="excelref" optName='create' v-if="dialogFormVisibleV" type="b_qual_attach_product_type"
                          :exportExcelDialogVisible.sync="dialogFormVisibleV" @setSubmittext='setSubmittext'
                          :traitSettings="tableOption" :tableOption="model.tableOption" :entityForm="entityForm"
                          Buttontext="save">
        </laboratoryImport>
      </el-form>
      <div style="float:right;margin-top:20px">
        <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" @click="okbtnV()">上传
        </el-button>
        <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" @click="closeExportExcelDialogV()">取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCustomerContract } from '@/api/quality'
import CustomerAndSupplierModel from '@/model/user/supplierAndCustomer'
import contractSellModel from '@/model/user/contractSell'
import contractBuyModel from '@/model/user/contractBuy'

import Mixins from '@/utils/mixins'
import { AttachedProductOption } from '@/const'
import Model from '@/model/coalQuality/AttachedProduct'
import { createMemoryField } from '@/utils/index'
import { numberplateValidatorCallBack } from '@/utils/validate'
export default {
  name: 'AttachedProduct',
  data() {
    return {
      curd: 'AttachedProduct',
      entityForm: { ...Model.model },
      model: Model,
      filterOption: { ...AttachedProductOption },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        customerName: { required: true, message: '请选择客户', trigger: 'blur' }
      },
      contractActive: [],
      contractEntityFormActive: [],
      contractEntityForm: {
        options: []
      },
      contractParams: { query: '' },
      contract: {
        props: {
          label: 'customerName',
          value: 'customerId',
          children: 'contractSellList'
        },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      indicators: { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity },
      nameEntity: { options: [], active: '' },
      memoryEntity: { fields: {}, triggered: false },
      filters: {},
      fromDashboard: false,
      customerEntity: { options: [], active: [] },
      val: '',

      // excel配置
      // excelConfig: { excel: Model.getImportConfig(), dialog: false },
      tableOption: {
        showPage: true,
        columns: [
          {
            label: '日期',
            title: '日期',
            prop: 'date',
            type: 'date',
            width: 120,
            validator: /[\s\S]/,
            correctFormat: true,
            dateFormat: 'YYYY-MM-DD'
          },
          {
            label: '名称',
            prop: 'name',
            title: '名称',
            width: 100,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            title: '类型',
            width: 100,
            prop: 'type',
            type: 'autocomplete',
            visibleRows: 10,
            allowInvalid: true
          },
          {
            label: '灰分Ad',
            prop: 'ad',
            title: '灰分Ad',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '硫分St,d',
            prop: 'std',
            title: '硫分St,d',
            type: 'text',
            validator: /[\s\S]/
          },

          {
            label: '高位发热量',
            prop: 'qgr',
            title: '高位发热量',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '低位发热量',
            prop: 'qnet',
            title: '低位发热量',
            type: 'text',
            validator: /[\s\S]/
          }
        ]
      },

      optName: 'create',
      uploadList: [], // 用于展示
      limitNum: 1000,
      collapse: '1',
      uploadData: { refId: '', refType: 'CoalSample' },

      dialogFormVisibleV: false
    }
  },
  computed: {
    otherHeight() {
      return this.perms[`${this.curd}:export`] ? '227' : '180'
    },
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    }
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_LIKES_date: to.params.date }
        vm.fromDashboard = true
      })
    } else {
      next()
    }
  },
  mixins: [Mixins],
  created() {
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'time',
        'customerName',
        'contractName',
        'contractCode',
        'contractId',
        'receiverName',
        'productName',
        'productCode',
        'productId',
        'name'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getContract()
    this.getalllist() //获取供应商和客户列表
    this.permsAction(this.curd)

    this.permschange(this.curd)
  },
  watch: {
    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.entityForm.productName = name
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.productName = ''
      }
    },
    'entityForm.customerName'(name) {
      if (!name) return
      const item = this.customerEntity.options.find((item) => item.label == name)
      if (item) {
        this.entityForm.customerId = item.value
        this.customerEntity.active = item.label
      } else {
        this.customerEntity.active = []
      }
    }
  },
  // 可写原方法覆盖mixins方法
  methods: {
    async addimportExcel(unknown, type) {
      this.dialogFormVisibleV = true
    },

    async saveEntityForm(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        const res = await Model.save({ ...this.entityForm })
        this.entityFormLoading = false
        if (res) {
          this.getList()
          this.baseVariant && this.baseVariant()
          this.saveMemoryField && this.saveMemoryField()
          this.resetVariant()
          this.dialogFormVisible = false
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        }
      }
    },
    okbtnV() {
      this.$refs.excelref.okbtn()
    },
    closeExportExcelDialogV() {
      this.$refs.excelref.closeExportExcelDialog()
    },

    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },
    closeDialog() {
      this.$refs.excelref.closeExportExcelDialog()
    },
    async setSubmittext(fList) {
      fList.forEach((e, index) => {
        if (e.sampleDate) {
          var str = e.sampleDate
          str = str.replace(/\//g, '-')
          e.sampleDate = str
        }
      })
      const text = JSON.stringify(fList)
      const res = await Model.importQualAttachProduct({ text })
      if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      this.$message({ showClose: true, message: '导入成功', type: 'success' })
      this.dialogFormVisibleV = false
      this.getList()
    },
    //批量删除,
    async BatchDelete(selectList) {
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      let newarry = []
      for (var i = 0; i < selectList.length; i++) {
        let obj = {}
        obj = selectList[i].id
        newarry.push(obj)
      }
      let parm = {
        idList: newarry
      }
      // console.log(parm)

      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // console.log('确认')
          this.deletefn(parm)
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },
    async deletefn(parm) {
      let { data } = await Model.savebatchChange({ ...parm })
      if (data) {
        this.$message({ type: 'success', message: '删除成功!' })
        this.getList()
      }
    },
    handleInput(val) {
      this.entityForm.customerName = val
    },
    // 合同改变同步entityForm
    handleCustomer({ value, label, type }) {
      this.entityForm.customerId = value
      this.entityForm.customerName = label
      this.entityForm.productName = ''
      this.getProductListfnc(value, type)
    },

    async getalllist() {
      //获取客户和供应商列表
      try {
        let options = []
        const { data } = await CustomerAndSupplierModel.findByKeyword({})
        if (data.length) {
          data.forEach((item) => {
            const { name: label, id: value, type: type } = item
            options.push({ label, value, type })
          })
        }
        this.customerEntity.options = options
      } catch (e) {}
    },

    async getProductListfnc(id, type) {
      if (type == 'S') {
        this.getSupplierProductName(id)
      }
      if (type == 'C') {
        this.getCustomerProductName(id)
      }
    },
    async getCustomerProductName(id) {
      try {
        let options = []
        const { data } = await contractSellModel.getProductListfn({ customerId: id })

        this.nameEntity.options = data.map((item) => {
          return { name: item.name, id: item.id, code: item.code }
        })
        if (this.nameEntity.options.length == 1) {
          this.entityForm.productName = this.nameEntity.options[0].name
        }
      } catch (e) {}
    },
    async getSupplierProductName(id) {
      try {
        const { data } = await contractBuyModel.getProductListfn({ supplierId: id })

        this.nameEntity.options = data.map((item) => {
          return { name: item.name, id: item.id, code: item.code }
        })
        if (this.nameEntity.options.length == 1) {
          this.entityForm.productName = this.nameEntity.options[0].name
        }
      } catch (e) {}
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogFormVisibleV = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      this.uploadList = [] // 清空下载列表
      this.fileList = []
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.contractEntityFormActive = []
        this.customerEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },

    async handleUpdate(item) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      // 上传所需要的配置
      this.uploadData.refId = item.id
      const { data } = await this.model.getUploadList(item.id)
      this.entityForm = { ...data }
      this.uploadList = data.attachmentList
    },
    async handleRemoveUpload(index) {
      const { id } = this.uploadList[index]
      this.$refs.upload.attachmentDelete({ id, index })
    },
    handleUploadSuccess(res) {
      this.uploadList = [...this.uploadList, res]
      // 上传完毕后更新展示的列表和把新增的附件id塞入到
      this.entityForm.attachmentList.push({ id: res.id })
    },
    handleUploadDelete({ status, index }) {
      this.uploadList.splice(index, 1)
    },

    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    /**
     * 初始化数据选择下拉数据
     * contract用于指标合同下拉选取label显示name,value为id
     * contractEntityForm用于新增编辑下拉label显示code,value为id
     */
    async getContract() {
      const { data } = await getCustomerContract()
      this.contract.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { customerName: 'name', customerId: 'id' }
      })
      this.contractEntityForm.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { customerName: 'code', customerId: 'id' }
      })
    },
    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;
    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      padding: 6px 10px;
      margin-right: 5px;
      font-size: 12px;
      position: relative;
      &-link {
        font-size: 12px;
        opacity: 0.8;
      }
      .icon {
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 9;
        top: 0;
        flex: 0;
      }
    }
  }
}

// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
// .opt-btnV {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
::v-deep .el-table__footer-wrapper tbody td {
  border: none !important;
}
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}
::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}
::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  color: #fff;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 4) {
  border-left: none;
  border-right: none;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 3) {
  border-left: none;
  border-right: none;
}
</style>
