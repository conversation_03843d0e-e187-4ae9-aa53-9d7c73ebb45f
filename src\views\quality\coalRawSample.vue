<template>
  <div class="app-container">
    <filter-table :options="filterOption" :showAdd="perms[`${curd}:save`] || false"
                  :showImport="perms[`${curd}:addimportV`]||false" :showRefresh="perms[`${curd}:Refresh`] || false"
                  @add="handleCreate" @filter="handleFilter" @import="handleImpiort" @reset="handleFilterReset" />
    <s-curd :ref="curd" :actions="actions" :changeactions="changeactions" :list-query="listQuery" :model="model" :name="curd"
            :showSummary="true" otherHeight="135">
      <el-table-column slot="sampleDate" align="center" fixed="left" label="化验日期" prop="name" width="100">
        <template slot-scope="scope">
          <span class="name" @click="handleUpdate(scope.row,'watch')">{{ scope.row.sampleDate }}</span>
        </template>
      </el-table-column>
      <el-table-column slot="attachment" align="center" label="查看附件" prop="attachment" width="100">
        <template slot-scope="scope">
          <div class="attachmentV" style="margin: 0 auto;">
            <span style="cursor: pointer;color: blue;" @click="$refs.attachment.open(scope.row)">查看附件</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column slot="opt" align="center" fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-tag v-if="perms[`${curd}:update`]||false" class="opt-btn" color="#FF9639"
                  @click="handleUpdate(scope.row,'update')">编辑
          </el-tag>
          <el-tag v-if="perms[`${curd}:delete`] || false" class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)">
            删除
          </el-tag>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false"
               :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible" top="5vh">
      <el-form v-if="dialogFormVisible" ref="entityForm" :disabled="dialogStatus === 'watch'" :model="entityForm" :rules="rules"
               :show-message="true" :status-icon="true" label-position="top" label-width="90px">
        <div class="form-title">基本信息</div>
        <el-row>
          <el-col :span="8">
            <el-form-item label="日期" prop="sampleDate">
              <date-select v-model="entityForm.sampleDate" :clearable="false" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top:20px">
          <hot-table ref="hot" :data="entityForm.coalRawSampleItemList" :settings="settings">
            <hot-column data="name" title="小样名称" />
            <hot-column :numericFormat="{pattern: '0.00'}" type="numeric" data="haRaw" title="含精" />
            <hot-column :numericFormat="{pattern: '0.00'}" type="numeric" data="haMid" title="含中" />
            <hot-column :numericFormat="{pattern: '0.00'}" type="numeric" data="haRock" title="含矸" />
            <hot-column :numericFormat="{pattern: '0.00'}" data="ad" title="灰分" type="numeric" />
            <hot-column :numericFormat="{pattern: '0.00'}" data="std" title="硫" type="numeric" />
            <hot-column :numericFormat="{pattern: '0.00'}" data="vdaf" title="挥发份" type="numeric" />
            <hot-column :numericFormat="{pattern: '0'}" data="g" title="粘结指数" type="numeric" />
            <hot-column :numericFormat="{pattern: '0'}" data="crc" title="焦渣" type="numeric" />
            <hot-column :numericFormat="{pattern: '0.000'}" data="macR0" title="平均反射率" type="numeric" />
            <hot-column :numericFormat="{pattern: '0.000'}" data="macS" title="标准偏差" type="numeric" />
            <hot-column :numericFormat="{pattern: '0.00'}" data="mt" title="全水" type="numeric" />
            <hot-column :numericFormat="{pattern: '0.0'}" data="y" title="手动Y值" type="numeric" />
            <hot-column :numericFormat="{pattern: '0.0'}" data="smY" title="自动Y值" type="numeric" />
          </hot-table>
        </el-row>
        <div class="form-title">附件信息</div>
        <el-collapse :value="['1','2']">
          <el-collapse-item name="1" title="煤岩报告上传">
            <file-upload :list.sync="entityForm.attachmentList" />
          </el-collapse-item>
          <el-collapse-item name="2" title="CSR报告上传">
            <file-upload :list.sync="entityForm.attachmentCsrList" />
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <div v-if="dialogStatus !== 'watch'" slot="footer" class="dialog-footer">
        <el-button v-if="perms[`${curd}:update`]||perms[`${curd}:save`]|| false" :loading="entityFormLoading"
                   class="dialog-footer-btns" type="primary" @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button class="dialog-footer-btns" @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
    <view-attachment ref="attachment" :request-method="model.getUploadList" />
  </div>
</template>
<script>
import Mixins from '@/utils/mixins'
import productModel from '@/model/product/productList'
import { coalSampleOption } from '@/const'
import Model from '@/model/coalQuality/coalRawSample'
import { createMemoryField } from '@/utils'
import Cache from '@/utils/cache'
import { HotColumn, HotTable } from '@handsontable/vue'
import 'handsontable/dist/handsontable.full.css'
import Handsontable from 'handsontable'
import filterTable from '@/components/FilterTable/index.vue'
import EditTable from '@/components/EditTable/index.vue'
import DictSelect from '@/components/DictSelect/index.vue'
import SelectDown from '@/components/SelectDown/index.vue'
import DateSelect from '@/components/DateSelect/index.vue'
import { mapGetters } from 'vuex'
import { zhCN } from 'handsontable/i18n'
import HotSelect from '@/components/HotSelect/index.vue'
import HotDateSelect from '@/components/HotDateSelect/index.vue'
import FileUpload from '@/components/FileUpload/index.vue'
import ViewAttachment from '@/components/ViewAttachment/index.vue'

class CoalRawSampleItem {
  constructor() {
    this.sampleDate = ''
    // 小样名称
    this.name = ''
    // 含精
    this.haRaw = ''
    //  含中
    this.haMid = ''
    // 含矸
    this.haRock = ''
    // 灰分
    this.ad = ''
    // 硫
    this.std = ''
    // 挥发分
    this.vdaf = ''
    // 粘结指数
    this.g = ''
    //  焦渣
    this.crc = ''
    // 平均反射率
    this.macR0 = ''
    //  标准偏差
    this.macS = ''
    // 全水
    this.mt = ''
    //  手动Y值
    this.y = ''
    // 自动Y值
    this.smY = ''
  }
}

class Form {
  constructor() {
    this.sampleDate = ''
    this.attachmentList = []
    this.attachmentCsrList = []
    this.coalRawSampleItemList = Array(10).fill(null).map(item => new CoalRawSampleItem())
  }
}

const form = {
  id: '1',
  typeName: '',
  cleanVdaf: '',
  cleanAd: '',
  cleanStd: '',
  cleanMt: '',
  procG: '',
  oneDotFour: '',
  procCrc: '',
  midCoal: '',
  recovery: ''
}
export default {
  name: 'coalRawSample',
  mixins: [Mixins],
  components: {
    ViewAttachment,
    FileUpload,
    HotDateSelect,
    HotSelect,
    DateSelect,
    SelectDown,
    DictSelect,
    EditTable,
    HotTable,
    HotColumn,
    filterTable
  },
  data() {
    return {
      curd: 'coalRawSample',
      listQuery: {
        orderBy: 'sampleDate',
        orderDir: 'desc'
      },
      model: Model,
      filterOption: { ...coalSampleOption },
      entityForm: new Form(),
      contract: {
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      locationList: [{ label: '化验指标' }, { label: '中煤指标' }, { label: '末煤指标' }],
      locationActive: -1,
      memoryEntity: { fields: {}, triggered: false },
      userpermissionsList: { options: [], active: '' },
      responsiblelist: [],
      emptyImgPath: require(`@/assets/empty.jpg`),
      listV: [{ ...form }],
      rules: {
        sampleDate: { required: true, message: '请选择日期', trigger: 'blur' },
        name: { required: true, message: '请输入小样名称', trigger: 'blur' }
      },
      tableOption: {
        showPage: true,
        columns: [
          {
            label: '化验日期',
            prop: 'sampleDate',
            title: '日期',
            type: 'date',
            width: 100,
            validator: /[\s\S]/,
            correctFormat: true,
            dateFormat: 'YYYY-MM-DD'
          },
          {
            label: '小样名称',
            prop: 'name',
            title: '小样名称',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            title: '煤种',
            width: 100,
            prop: 'type',
            type: 'autocomplete',
            visibleRows: 10,
            allowInvalid: true
          },
          {
            label: '负责人',
            prop: 'responsibleUserName',
            width: 120,
            title: '负责人',
            type: 'autocomplete',
            visibleRows: 10,
            allowInvalid: true
          },
          {
            label: '类型',
            prop: 'typeName',
            title: '类型',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '灰Ad',
            prop: 'cleanAd',
            title: '灰Ad',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '硫St,d',
            prop: 'cleanStd',
            title: '硫St,d',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '挥发分Vdaf',
            prop: 'cleanVdaf',
            title: '挥发分Vdaf',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '焦渣CRC',
            prop: 'procCrc',
            title: '焦渣CRC',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '回收',
            prop: 'recovery',
            title: '回收',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '中煤含量',
            prop: 'midCoal',
            title: '中煤',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'G值',
            prop: 'procG',
            title: 'G值',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '全水Mt',
            prop: 'cleanMt',
            title: '全水Mt',
            type: 'numeric',
            numericFormat: {
              pattern: '0.00'
            },
            validator: /[\s\S]/
          },
          {
            label: '1.4含量',
            prop: 'oneDotFour',
            title: '1.4含量',
            type: 'text',
            validator: /[\s\S]/
          }
        ]
      },
      editData: [{}, {}, {}, {}, { typeName: '中煤' }, { typeName: '末煤' }],
      type: 'b_raw_coal_sample_density',
      hotData: Array(10).fill(null).map(item => new CoalRawSampleItem()),
      settings: {
        readOnly: this.disabled,
        className: 'custom-classname',
        language: zhCN.languageCode,
        width: 'auto',
        height: 'auto',
        manualColumnResize: true,
        licenseKey: 'non-commercial-and-evaluation',
        trimWhitespace: false,
        stretchH: 'all',
        contextMenu: {
          items: {
            row_above: { name: '向上插入一行' },
            row_below: { name: '向下插入一行' },
            remove_row: { name: '删除行' }
          }
        }

      }
    }
  },
  created() {
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'time',
        'customerName',
        'contractCode',
        'contractId',
        'receiverName',
        'productCode',
        'productId',
        'name',
        'type'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getName()
    this.gethead()
    this.permschange(this.curd)
  },
  watch: {
    'userpermissionsList.active'(name) {
      if (name) {
        const item = this.userpermissionsList.options.find((item) => item.name === name)
        if (item) {
          this.entityForm.responsibleUserId = item.id
        }
      }
    }
  },
  computed: {
    ...mapGetters(['perms']),
    getColumns() {
      const dialogStatus = this.dialogStatus
      const len = this.editData.length
      const watchArr =
        dialogStatus === 'watch'
          ? [
            {
              data: null,
              title: '',
              width: 30,
              //只读
              readOnly: true,
              renderer: function (instance, td, row, col, prop, value, cellProperties) {
                Handsontable.renderers.TextRenderer.apply(this, arguments)
                const index = row + 1
                td.style.textAlign = 'center'
                td.innerHTML = index
                // td.innerHTML = index == len ? '平均' : index
              }
            }
          ]
          : []
      return [
        ...watchArr,
        {
          title: '密度级',
          width: 60,
          data: 'typeName',
          formRequire: true, // 自定义字段
          visibleRows: 15,
          type: 'dropdown',
          source: (window.dict['b_raw_coal_sample_density'] || []).map((item) => item.code)
        },
        {
          title: '灰Ad%',
          width: 60,
          data: 'cleanAd',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '硫St,d',
          width: 60,
          data: 'cleanStd',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '挥发分Vdaf',
          width: 60,
          data: 'cleanVdaf',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '焦渣CRC',
          width: 60,
          data: 'procCrc',
          type: 'numeric'
        },
        {
          title: 'G值',
          width: 60,
          data: 'procG',
          type: 'numeric'
        },
        {
          title: '全水分',
          width: 60,
          data: 'cleanMt',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '回收',
          width: 60,
          data: 'recovery',
          type: 'numeric',
          numericFormat: {
            pattern: '0'
          }
        },
        {
          title: '1.4含量',
          width: 60,
          data: 'oneDotFour',
          type: 'numeric',
          numericFormat: {
            pattern: '0'
          }
        },
        {
          title: '中煤',
          width: 60,
          data: 'midCoal',
          type: 'numeric',
          numericFormat: {
            pattern: '0'
          }
        }
      ]
    },
    listType() {
      return window.dict[this.type] || []
    }
  },
  methods: {
    async handleEditTableInit(instance) {
      let source = []
      if (this.listType) {
        let Array = []
        this.listType.forEach((item, index) => {
          Array.push(item.name)
        })
        source = Array
      }
      const getSetting = () => {
        if (this.dialogStatus === 'watch') {
          return {
            readOnly: true,
            contextMenu: undefined,
            data: this.editData,
            rowHeaders: false
          }
        }
        return {
          contextMenu: {
            items: {
              // rowHeaders: false,
              // row_above: { name: '向上插入一行' },
              row_below: { name: '向下插入一行' },
              remove_row: { name: '删除行' },
              clear_custom: {
                name: '清空所有单元格数据',
                callback() {
                  this.clear()
                }
              }
            }
          },
          data: this.editData
        }
      }
      const typeColIndex = this.getColumns.findIndex((v) => v.data === 'typeName')
      instance.updateSettings({
        ...getSetting(),
        async cells(row, col) {
          if (col == typeColIndex) {
            this.source = source
          }
        }
      })
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1 || columnIndex === 0 || column.label === '操作') {
        if (row.coalRawSampleItemList === undefined) {
          return { rowspan: 0, colspan: 1 }
        }
        const len = row.coalRawSampleItemList.length
        if (len) {
          return { rowspan: len, colspan: 1 }
        } else {
          return { rowspan: 1, colspan: 1 }
        }
      }
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      let userdata = Cache.get('user')
      this.entityForm = { ...this.entityForm, responsibleUserName: userdata.userName, responsibleUserId: userdata.userId }
      this.userpermissionsList.active = userdata.userName
    },
    handleClose() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.entityForm = new Form()
    },
    // 获取负责人接口
    async gethead() {
      const { data } = await this.model.getuserList()
      this.userpermissionsList.options = data.map((item) => {
        return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
      })
      this.responsiblelist = data.map((item, index) => {
        return item.name
      })
    },
    //批量删除
    async BatchDelete(selectList) {
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      let newarry = []
      for (var i = 0; i < selectList.length; i++) {
        let obj = {}
        obj = selectList[i].id
        newarry.push(obj)
      }
      let parm = {
        idList: newarry
      }
      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          this.deletefn(parm)
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },
    async deletefn(parm) {
      let { data } = await Model.savebatchChange({ ...parm })
      if (data) {
        this.$message({ type: 'success', message: '删除成功!' })
        this.getList()
      }
    },
    closeExportExcelDialogV() {
      this.$refs.excelref.closeExportExcelDialog()
    },
    handleAdd() {
      const form = { ...this.form }
      form.id = Math.random().toFixed(6).slice(-6)
      this.listV.push(form)
    },
    handleRemove({ id }) {
      const index = this.listV.findIndex((item) => item.id === id)
      if (index === -1) return false
      this.listV.splice(index, 1)
    },
    async getName() {
      try {
        let { data } = await productModel.page({ size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
        })
      } catch (error) {
      }
    },
    async saveEntityForm(entityForm) {
      let valid = await this.$refs.entityForm.validate().catch(e => console.log(e))
      if (valid) {
        this.entityFormLoading = true
        /** @type {import('handsontable').default} */
        let hotInstance = this.$refs.hot.hotInstance
        for (let i = this.entityForm.coalRawSampleItemList.length - 1; i >= 0; i--) {
          if (hotInstance.isEmptyRow(i)) hotInstance.alter('remove_row', i, 1)
        }
        const status = await new Promise((resolve, reject) => {
          hotInstance.validateCells((result) => {
            if (!result) this.$message.error('表单有中有异常数据，请检查！')
            resolve(result)
          })
        })
        if (!status) return
        if (this.dialogStatus === 'create') {
          this.entityForm.coalRawSampleItemList.forEach((item) => {
            item.sampleDate = this.entityForm.sampleDate
          })
          const res = await this.model.save({ text: JSON.stringify(this.entityForm.coalRawSampleItemList) })
          this.entityFormLoading = false
          if (res) {
            this.$message({ message: '保存成功', type: 'success' })
            this.getList()
            this.entityForm = new Form()
            this.resetVariant()
          }
        } else if (this.dialogStatus === 'update') {
          this.entityForm.haRaw = this.entityForm.coalRawSampleItemList[0].haRaw
          this.entityForm.haMid = this.entityForm.coalRawSampleItemList[0].haMid
          this.entityForm.haRock = this.entityForm.coalRawSampleItemList[0].haRock
          this.entityForm.ad = this.entityForm.coalRawSampleItemList[0].ad
          this.entityForm.std = this.entityForm.coalRawSampleItemList[0].std
          this.entityForm.vdaf = this.entityForm.coalRawSampleItemList[0].vdaf
          this.entityForm.g = this.entityForm.coalRawSampleItemList[0].g
          this.entityForm.crc = this.entityForm.coalRawSampleItemList[0].crc
          this.entityForm.macR0 = this.entityForm.coalRawSampleItemList[0].macR0
          this.entityForm.macS = this.entityForm.coalRawSampleItemList[0].macS
          this.entityForm.mt = this.entityForm.coalRawSampleItemList[0].mt
          this.entityForm.y = this.entityForm.coalRawSampleItemList[0].y
          this.entityForm.smY = this.entityForm.coalRawSampleItemList[0].smY
          const res = await this.model.saveCoalRawSampleItem({ ...this.entityForm })
          this.entityFormLoading = false
          if (res) {
            this.$message({ message: '保存成功', type: 'success' })
            this.getList()
            this.entityForm = new Form()
            this.resetVariant()
          }
        }
      }
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.entityForm = new Form()
      this.editData = [{}, {}, {}, {}, { typeName: '中煤' }, { typeName: '末煤' }]
      this.uploadList = []
      this.fileList = []
      this.userpermissionsList.active = ''
    },
    handleToLocation(index) {
      this.locationActive = index === this.locationActive ? -1 : index
      this.$refs[this.curd].toLocation(this.locationActive)
    },
    async handleUpdate(item, status) {
      try {
        await this.$nextTick()
        const { data } = await this.model.getUploadList(item.id)
        this.userpermissionsList.active = data.responsibleUserName
        this.entityForm = data
        this.entityForm.coalRawSampleItemList = [{ ...data }]
        // this.entityForm.sampleDate = data.sampleDate
        // this.uploadList = data.attachmentList
        // if (this.editData.length < 1) {
        //   this.editData = [{}, {}, {}, {}, {typeName: '中煤'}, {typeName: '末煤'}]
        // } else {
        //   this.editData = [...data.coalRawSampleItemList]
        // }
      } catch (e) {
        console.log(e, 'e')
      }
      this.dialogStatus = status
      this.dialogFormVisible = true
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
//::v-deep .el-table td.el-table__cell,
//.el-table th.el-table__cell.is-leaf {
//  border-left: 1px solid #ebeef5;
//}
//
//.form-warp {
//  padding: 10px 20px 6px 18px;
//  display: flex;
//  justify-content: space-between;
//}
//
//.el-button--export-all:focus,
//.el-button--export-all:hover {
//  background: #ff9639 !important;
//  border-color: #fff !important;
//  color: #fff !important;
//}
//
//::v-deep .el-drawer__header > :first-child {
//  font-size: 14px;
//}
//
//.images-warp {
//  height: 100%;
//  display: flex;
//  justify-content: flex-start;
//  align-content: flex-start;
//  flex-flow: wrap;
//
//  .links {
//    padding-left: 20px;
//    display: flex;
//    width: 100%;
//    justify-content: flex-start;
//    flex-flow: wrap;
//    margin-bottom: 10px;
//    opacity: 0.95;
//
//    .link-title {
//      // color: ;
//      color: #000;
//      opacity: 0.5;
//    }
//  }
//}
//
//::v-deep .el-collapse-item__arrow {
//  display: none;
//}
//
//.upload {
//  display: flex;
//  align-content: center;
//  justify-content: space-around;
//
//  .upload-list {
//    flex: 1;
//    display: flex;
//    align-content: center;
//    justify-content: flex-start;
//
//    .up-load-warp {
//      display: flex;
//      flex-flow: column;
//      align-content: center;
//      justify-content: center;
//      padding: 6px 10px;
//      margin-right: 5px;
//      font-size: 12px;
//      position: relative;
//
//      &-link {
//        font-size: 12px;
//        opacity: 0.8;
//      }
//
//      .icon {
//        cursor: pointer;
//        position: absolute;
//        right: 0;
//        z-index: 9;
//        top: 0;
//        flex: 0;
//      }
//    }
//  }
//}
//
//::v-deep .el-table .cell {
//  display: flex;
//  justify-content: center;
//  height: 23px;
//}
//
//::v-deep .el-table__footer-wrapper tbody td,
//.el-table__header-wrapper tbody td {
//  background: transparent;
//  border: 0.5px solid #eef0ff;
//}
//
//::v-deep .el-table__fixed-footer-wrapper tbody td {
//  border-top: none;
//  background-color: transparent;
//  color: #fff;
//}
//
//::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 5) {
//  // border-left: none;
//  // border-right: none;
//  border: none;
//}
//
//::v-deep .el-table__fixed::before {
//  background: transparent;
//}
//
//::v-deep .el-table__fixed-right::before {
//  background: transparent;
//}
</style>
