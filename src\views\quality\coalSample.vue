<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimportV`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <panel title="定位查看" type="location" :active="locationActive" :list='locationList' @locationChange="handleToLocation" />
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" otherHeight="200"
            :changeactions="changeactions" :showSummary='true'>
      <el-table-column label="小样名称" slot="name" prop="name" width="100" fixed="left" align='center'>
        <template slot-scope="scope"><span class="name"
                @click="handleUpdate(scope.row,'watch')">{{scope.row.name}}</span></template>
      </el-table-column>
      <el-table-column class-name="index" label="入炉样测定" slot="chargingTest" align="center">
        <el-table-column label="灰Ad%" prop="rawAd" width="100" align="center" />
        <el-table-column label="硫St,d" prop="rawStd" width="100" align="center" />
        <el-table-column label="挥发分Vdaf%" prop="rawVdaf" width="100" align="center" />
        <el-table-column label="特征Crc" prop="procCrc" width="100" align="center" />
        <el-table-column label="粘结G" prop="procG" width="100" align="center" />
        <el-table-column label="Ymm" prop="procY" width="100" align="center" />
        <el-table-column label="Xmm" prop="procX" width="100" align="center" />
        <el-table-column label="全水Mt" prop="rawMt" width="100" align="center" />
        <el-table-column label="回收" prop="recover" width="100" align="center" />
        <el-table-column label="中煤" prop="midCoal" width="100" align="center" />
        <el-table-column label="矸石" prop="wasteRock" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="入炉煤细度" slot="chargingFineness" align="center">
        <el-table-column label=">3mm" prop="less3mm" width="100" align="center" />
        <el-table-column label="<3mm" prop="greater3mm" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="焦炭" slot="coke" align="center">
        <el-table-column label="Ad" prop="qualAd" width="100" align="center" />
        <el-table-column label="St,d" prop="qualStd" width="100" align="center" />
        <el-table-column label="Vdaf" prop="qualVdaf" width="100" align="center" />
        <el-table-column label="M40" prop="qualM40" width="100" align="center" />
        <el-table-column label="M25" prop="qualM25" width="100" align="center" />
        <el-table-column label="M10" prop="qualM10" width="100" align="center" />
        <el-table-column label="CRI" prop="qualCri" width="100" align="center" />
        <el-table-column label="CSR" prop="qualCsr" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="灰成份" slot="ashComposition" align="center">
        <el-table-column label="SiO2" prop="comSiO2" width="100" align="center" />
        <el-table-column label="Ai2O3" prop="comAl2O3" width="100" align="center" />
        <el-table-column label="Fe2O3" prop="comFe2O3" width="100" align="center" />
        <el-table-column label="CaO" prop="comCaO" width="100" align="center" />
        <el-table-column label="MgO" prop="comMgO" width="100" align="center" />
        <el-table-column label="Na2O" prop="comNa2O" width="100" align="center" />
        <el-table-column label="K2O" prop="comK2O" width="100" align="center" />
        <el-table-column label="TiO2" prop="comTiO2" width="100" align="center" />
        <el-table-column label="P" prop="comP2O5" width="100" align="center" />
        <el-table-column label="SO3" prop="comSO3" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="岩相" slot="lithofacies" align="center">
        <el-table-column label="Rran平均值" prop="macRran" width="100" align="center" />
        <el-table-column label="Rmax平均值" prop="macRmax" width="100" align="center" />
        <el-table-column label="标准偏差S" prop="macS" width="100" align="center" />
        <el-table-column label="活性物" prop="procV" width="100" align="center" />
        <el-table-column label="活惰比" prop="procVi" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="奥亚" slot="aoya" align="center">
        <el-table-column label="软化温度" prop="procT1" width="100" align="center" />
        <el-table-column label="开始膨胀温度" prop="procT2" width="100" align="center" />
        <el-table-column label="固化温度" prop="procT3" width="100" align="center" />
        <el-table-column label="最大膨胀度" prop="procB" width="100" align="center" />
        <el-table-column label="最大收缩度" prop="procA" width="100" align="center" />
      </el-table-column>

      <el-table-column label="附件" slot="attachment" prop="attachment" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handlePreviewAttachment(scope.row)">查看附件</el-button>
          <!-- <div class="attachment">
            <img class="attachment-img" src="@/assets/attachment.png" >
          </div> -->
        </template>
      </el-table-column>
      <el-table-column label="操作" slot="opt" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row,'update')"
                  v-if="perms[`${curd}:update`]||false">编辑</el-tag>
          <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除</el-tag>
        </template>
      </el-table-column>
    </s-curd>
    <!-- <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible" :rules="rules">
        <el-row>
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="日期" prop="sampleDate">
                    <date-select v-model="entityForm.sampleDate" :clearable="false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="小样名称" prop="name">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤种" prop="type">
                    <dict-select style="width:100%" v-model="entityForm.type" type="coal_type"></dict-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="负责人" prop="responsibleUserId">
                    <select-down :value.sync="userpermissionsList.active" :list="userpermissionsList.options"
                                 @eventChange="handleUserpermissionsList" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">基本指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="灰Ad" prop="rawAd">
                    <el-input v-model="entityForm.rawAd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="硫St,d" prop="rawStd">
                    <el-input v-model="entityForm.rawStd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="挥发分Vdaf" prop="rawVdaf">
                    <el-input v-model="entityForm.rawVdaf" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="特征Crc" prop="procCrc">
                    <el-input v-model="entityForm.procCrc" :oninput="field.int" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="粘结G" prop="procG">
                    <el-input v-model="entityForm.procG" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Ymm" prop="procY">
                    <el-input v-model="entityForm.procY" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Xmm" prop="procX">
                    <el-input v-model="entityForm.procX" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="全水Mt" prop="rawMt">
                    <el-input v-model="entityForm.rawMt" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="回收" prop="recover">
                    <el-input v-model="entityForm.recover" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="中煤" prop="midCoal">
                    <el-input v-model="entityForm.midCoal" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="矸石" prop="wasteRock">
                    <el-input v-model="entityForm.wasteRock" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col />
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">细度</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label=">3mm" prop="less3mm">
                    <el-input v-model="entityForm.less3mm" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="<3mm" prop="greater3mm">
                    <el-input v-model="entityForm.greater3mm" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col />
                <el-col />
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">焦炭</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="Ad" prop="qualAd">
                    <el-input v-model="entityForm.qualAd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="St,d" prop="qualStd">
                    <el-input v-model="entityForm.qualStd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Vdaf" prop="qualVdaf">
                    <el-input v-model="entityForm.qualVdaf" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="M40" prop="qualM40">
                    <el-input v-model="entityForm.qualM40" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="M25" prop="qualM25">
                    <el-input v-model="entityForm.qualM25" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="M10" prop="qualM10">
                    <el-input v-model="entityForm.qualM10" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="CRI" prop="qualCri">
                    <el-input v-model="entityForm.qualCri" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="CSR" prop="qualCsr">
                    <el-input v-model="entityForm.qualCsr" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">灰成分</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="SIO2" prop="comSiO2">
                    <el-input v-model="entityForm.comSiO2" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="AI2O3" prop="comAl2O3">
                    <el-input v-model="entityForm.comAl2O3" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Fe2O3" prop="comFe2O3">
                    <el-input v-model="entityForm.comFe2O3" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="CaO" prop="comCaO">
                    <el-input v-model="entityForm.comCaO" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="MgO" prop="comMgO">
                    <el-input v-model="entityForm.comMgO" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Na2O" prop="comNa2O">
                    <el-input v-model="entityForm.comNa2O" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="K2O" prop="comK2O">
                    <el-input v-model="entityForm.comK2O" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="TiO2" prop="comTiO2">
                    <el-input v-model="entityForm.comTiO2" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="P" prop="comP2O5">
                    <el-input v-model="entityForm.comP2O5" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="SO3" prop="comSO3">
                    <el-input v-model="entityForm.comSO3" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col />
                <el-col />
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">岩相</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="Rran平均值" prop="macRran">
                    <el-input v-model="entityForm.macRran" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Rmax平均值" prop="macRmax">
                    <el-input v-model="entityForm.macRmax" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="标准偏差S" prop="macS">
                    <el-input v-model="entityForm.macS" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="活性物" prop="procV">
                    <el-input v-model="entityForm.procV" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="活惰比" prop="procVi">
                    <el-input v-model="entityForm.procVi" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col> </el-col>
                <el-col> </el-col>
                <el-col> </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">奥亚</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="软化温度T1" prop="procT1">
                    <el-input v-model="entityForm.procT1" :oninput="field.decimal" clearable>
                      <template slot="append">°C</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="开始膨胀温度T2" prop="procT2">
                    <el-input v-model="entityForm.procT2" :oninput="field.decimal" clearable>
                      <template slot="append">°C</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="固化温度T3" prop="procT3">
                    <el-input v-model="entityForm.procT3" :oninput="field.decimal" clearable>
                      <template slot="append">°C</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="最大膨胀度b" prop="procB">
                    <el-input v-model="entityForm.procB" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="最大收缩度a" prop="procA">
                    <el-input v-model="entityForm.procA" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                </el-col>
                <el-col />
                <el-col />
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">附件信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="">
                    <el-collapse v-model="collapse">
                      <el-collapse-item title="上传附件" name="1">
                        <div class="upload">
                          <div class="upload-list">
                            <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                              <i class="el-icon-close icon" @click="handleRemoveUpload(index)"></i>
                              <el-link class="up-load-warp-link" :underline=" false" :href="item.uri">{{item.display}}</el-link>
                            </div>
                            <upload-attachment ref="upload" class="upload-attachment" :uploadData="uploadData" source="coalSample"
                                               :size="size" :limitNum="limitNum"
                                               accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg"
                                               @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                               listType="text" />
                          </div>
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog> -->

    <el-dialog ref="dialogStatus" top="0vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               class="tsel-dialog" :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false"
               width="100%">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <hot-table ref="hotV" class="hot-table" height="250" width="100%" :settings="settingsV"></hot-table>
          </el-col>

          <el-col style="margin-top:20px">
            <hot-table ref="hot" class="hot-table" height="250" width="100%" :settings="settings"></hot-table>
            <div style="margin-top:10px">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input v-model="entityForm.remarks" type="textarea" :rows="6" placeholder="请填写备注信息~">
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">附件信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="">
                    <el-collapse v-model="collapse">
                      <el-collapse-item title="上传附件" name="1">
                        <div class="upload">
                          <div class="upload-list">
                            <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                              <i class="el-icon-close icon" @click="handleRemoveUpload(index)"></i>
                              <el-link class="up-load-warp-link" :underline=" false" :href="item.uri">{{item.display}}</el-link>
                            </div>
                            <upload-attachment ref="upload" class="upload-attachment" :uploadData="uploadData" source="coalSample"
                                               :size="size" :limitNum="limitNum"
                                               accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg"
                                               @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                               listType="text" />
                          </div>
                        </div>

                      </el-collapse-item>
                    </el-collapse>

                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||perms[`${curd}:save`]|| false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisibleV"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisibleV">
        <el-row>
          <el-col>
            <laboratoryImport ref="excelref" optName='create' v-if="dialogFormVisibleV" type="coal_type"
                              :responsiblelist="responsiblelist" :exportExcelDialogVisible.sync="dialogFormVisibleV"
                              @setSubmittext='setSubmittext' :traitSettings="tableOption" :tableOption="model.tableOption"
                              :entityForm="entityForm" Buttontext="save">
            </laboratoryImport>
          </el-col>
          <!-- <el-col>
            <div class="form-title">附件信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="">
                    <el-collapse v-model="collapse">
                      <el-collapse-item title="上传附件" name="1">
                        <div class="upload">
                          <div class="upload-list">
                            <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                              <i class="el-icon-close icon" @click="handleRemoveUpload(index)"></i>
                              <el-link class="up-load-warp-link" :underline=" false" :href="item.uri">{{item.display}}</el-link>
                            </div>
                            <upload-attachment ref="upload" class="upload-attachment" :uploadData="uploadData" source="coalSample"
                                               :size="size" :limitNum="limitNum"
                                               accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg"
                                               @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                               listType="text" />
                          </div>
                        </div>

                      </el-collapse-item>
                    </el-collapse>

                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col> -->
        </el-row>
      </el-form>
      <div style="float:right;margin-top:20px">
        <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" @click="okbtnV()">上传
        </el-button>
        <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" @click="closeExportExcelDialogV()">取消
        </el-button>
      </div>
    </el-dialog>

    <el-drawer title="附件列表" :visible.sync="drawer.visible" direction="rtl" :before-close="handleCloseDrawer"
               custom-class="drawer">
      <div class="images-warp">
        <template v-if="drawer.list.length">
          <div class="links" v-for="(item,index) in drawer.list" :key="index">
            <el-link class="up-load-warp-link" @click.native="handleDownload(item.uri)" :underline="false" href="javascript:;">
              {{item.display}}
              <el-divider direction="vertical" />
            </el-link>
          </div>
        </template>
        <template v-else>
          <el-empty :image="emptyImgPath" :image-size="200" description="暂无附件" style="display:flex;width:100%" />
        </template>
        <!-- <div class="links" v-for="(item,index) in Object.values(drawer.dateList)" :key="index">
          <h5 class="link-title">上传时间:{{item.date}}</h5>
          <template v-for="(link,i) in item.list">
            <el-link class="up-load-warp-link" :underline="false" :href="link.uri" :key="i">
              {{link.display}}
              <el-divider direction="vertical" />
            </el-link>
          </template>
        </div> -->
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import productModel from '@/model/product/productList'
import { coalSampleOption } from '@/const'
import Model from '@/model/coalQuality/coalSample'
import { createMemoryField, deepClone } from '@/utils/index'
import { HotTable, HotColumn } from '@handsontable/vue'
import { registerLanguageDictionary, zhCN } from 'handsontable/i18n'
import { registerAllModules } from 'handsontable/registry'
import 'handsontable/dist/handsontable.full.css'
registerAllModules()
registerLanguageDictionary(zhCN)
export default {
  name: 'coalSample',
  mixins: [Mixins],
  components: {
    HotTable,
    HotColumn
  },
  data() {
    return {
      curd: 'coalSample',
      model: Model,
      filterOption: { ...coalSampleOption },
      currentContract: [],
      entityForm: { ...Model.model },
      contract: {
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      rules: {
        sampleDate: { required: true, message: '请选择日期', trigger: 'blur' },
        // customerName: { required: true, message: '请选择客户', trigger: 'blur' },
        name: { required: true, message: '请输入小样名称', trigger: 'blur' },
        type: { required: true, message: '请选择煤种', trigger: 'blur' }
      },
      locationList: [
        { label: '入炉样测定' },
        { label: '入炉煤细度' },
        { label: '焦炭' },
        { label: '灰成份' },
        { label: '岩相' },
        { label: '奥亚' }
      ],
      locationActive: -1,
      memoryEntity: { fields: {}, triggered: false },
      nameEntity: { options: [], active: '' },
      userpermissionsList: { options: [], active: '' },
      responsiblelist: [],
      // upload
      fileList: [],
      limitNum: 1000,
      uploadList: [], // 用于展示
      uploadData: { refId: '', refType: 'CoalSample' },
      collapse: '1',
      drawer: {
        visible: false,
        list: [],
        preview: [],
        dateList: []
      },
      emptyImgPath: require(`@/assets/empty.jpg`),

      dialogFormVisibleV: false,
      tableOption: {
        showPage: true,
        columns: [
          {
            label: '化验日期',
            prop: 'sampleDate',
            title: '日期',
            type: 'date',
            width: 100,
            validator: /[\s\S]/,
            correctFormat: true,
            dateFormat: 'YYYY-MM-DD'
          },
          {
            label: '小样名称',
            prop: 'name',
            title: '小样名称',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '煤种',
            title: '煤种',
            width: 100,
            prop: 'type',
            type: 'autocomplete',
            visibleRows: 10,
            allowInvalid: true
            // validator: numberplateValidatorCallBack,
          },
          {
            label: '负责人',
            prop: 'responsibleUserName',
            width: 120,
            title: '负责人',
            type: 'autocomplete',
            visibleRows: 10,
            allowInvalid: true
          },
          {
            label: '灰Ad%',
            prop: 'rawAd',
            title: '灰Ad%',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '硫St,d',
            prop: 'rawStd',
            title: '硫St,d',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '挥发分Vdaf%',
            prop: 'rawVdaf',
            title: '挥发分Vdaf%',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '特征Crc%',
            prop: 'procCrc',
            title: '特征Crc%',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '粘结G',
            prop: 'procG',
            title: '粘结G',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'Ymm',
            prop: 'procY',
            title: 'procY',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'Xmm',
            prop: 'procX',
            title: 'procX',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '全水Mt',
            prop: 'rawMt',
            title: '全水Mt',
            type: 'numeric',
            numericFormat: {
              pattern: '0.00'
            },
            validator: /[\s\S]/
          },
          {
            label: '回收',
            prop: 'recover',
            title: '回收',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '中煤',
            prop: 'midCoal',
            title: '中煤',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '矸石',
            prop: 'wasteRock',
            title: '矸石',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '>3mm',
            prop: 'less3mm',
            title: '>3mm',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '<3mm',
            prop: 'greater3mm',
            title: '<3mm',
            type: 'text',
            validator: /[\s\S]/
          },
          // 焦炭
          {
            label: 'Ad',
            prop: 'qualAd',
            title: 'Ad',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'St,d',
            prop: 'qualStd',
            title: 'St,d',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'Vdaf',
            prop: 'qualVdaf',
            title: 'Vdaf',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'M40',
            prop: 'qualM40',
            title: 'M40',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'M25',
            prop: 'qualM25',
            title: 'M25',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'M10',
            prop: 'qualM10',
            title: 'M10',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'CRI',
            prop: 'qualCri',
            title: 'CRI',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'CSR',
            prop: 'qualCsr',
            title: 'CSR',
            type: 'text',
            validator: /[\s\S]/
          },
          // 灰成份
          {
            label: 'SiO2',
            prop: 'comSiO2',
            title: 'SiO2',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'Ai2O3',
            prop: 'comAl2O3',
            title: 'Ai2O3',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'Fe2O3',
            prop: 'comFe2O3',
            title: 'Fe2O3',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'CaO',
            prop: 'comCaO',
            title: 'CaO',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'MgO',
            prop: 'comMgO',
            title: 'MgO',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'Na2O',
            prop: 'comNa2O',
            title: 'Na2O',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'K2O',
            prop: 'comK2O',
            title: 'K2O',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'TiO2',
            prop: 'comTiO2',
            title: 'TiO2',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'P',
            prop: 'comP2O5',
            title: 'P',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'SO3',
            prop: 'comSO3',
            title: 'SO3',
            type: 'text',
            validator: /[\s\S]/
          },
          // 岩相
          {
            label: 'Rran平均值',
            prop: 'macRran',
            title: 'Rran平均值',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'Rmax平均值',
            prop: 'macRmax',
            title: 'Rmax平均值',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '标准偏差S',
            prop: 'macS',
            title: '标准偏差S',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '活性物',
            prop: 'procV',
            title: '活性物',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '活惰比',
            prop: 'procVi',
            title: '活惰比',
            type: 'text',
            validator: /[\s\S]/
          },
          // 奥亚
          {
            label: '软化温度',
            prop: 'procT1',
            title: '软化温度',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '开始膨胀温度',
            prop: 'procT2',
            title: '开始膨胀温度',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '固化温度',
            prop: 'procT3',
            title: '固化温度',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '最大膨胀度',
            prop: 'procB',
            title: '最大膨胀度',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '最大收缩度',
            prop: 'procA',
            title: '最大收缩度',
            type: 'text',
            validator: /[\s\S]/
          }
        ]
      },

      settingsV: {
        startRows: 1,
        data: [{}],
        columns: [
          {
            title: '化验日期',
            data: 'sampleDate',
            type: 'date',
            trimWhitespace: true,
            width: 120,
            validator: /[\s\S]/,
            correctFormat: true,
            dateFormat: 'YYYY-MM-DD'
          },
          { title: '小样名称', data: 'name', type: 'text', trimWhitespace: true, width: 140 },
          {
            title: '煤种',
            data: 'type',
            type: 'autocomplete',
            source: (window.dict['coal_type'] || []).map((item) => item.code),
            allowInvalid: true,
            visibleRows: 10,
            allowInvalid: true,
            trimWhitespace: true,
            width: 100
          },
          {
            title: '负责人',
            data: 'responsibleUserName',
            type: 'autocomplete',
            source: [],
            allowInvalid: true,
            visibleRows: 10,
            allowInvalid: true,
            trimWhitespace: true,
            width: 100
          },
          // 入炉样测定
          {
            title: '灰Ad%',
            data: 'rawAd',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '硫St,d',
            data: 'rawStd',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '挥发分Vdaf%',
            data: 'rawVdaf',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.00'
            }
          },
          { title: '特征Crc%', data: 'procCrc', type: 'numeric', trimWhitespace: true },
          { title: '粘结G', data: 'procG', type: 'numeric', trimWhitespace: true },
          {
            title: 'Ymm',
            data: 'procY',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.0'
            }
          },
          {
            title: 'Xmm',
            data: 'procX',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.0'
            }
          },
          {
            title: '全水Mt',
            data: 'rawMt',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.00'
            }
          },
          { title: '回收', data: 'recover', type: 'numeric', trimWhitespace: true },
          { title: '中煤', data: 'midCoal', type: 'numeric', trimWhitespace: true },
          { title: '矸石', data: 'wasteRock', type: 'numeric', trimWhitespace: true },
          // 入炉煤细度

          { title: '>3mm', data: 'less3mm', type: 'numeric', trimWhitespace: true },
          { title: '<3mm', data: 'greater3mm', type: 'numeric', trimWhitespace: true },

          //   焦炭
          {
            title: 'Ad',
            data: 'qualAd',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: 'St,d',
            data: 'qualStd',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: 'Vdaf',
            data: 'qualVdaf',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: 'M40',
            data: 'qualM40',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.0'
            }
          },
          {
            title: 'M25',
            data: 'qualM25',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: 'M10',
            data: 'qualM10',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: 'CRI',
            data: 'qualCri',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: 'CSR',
            data: 'qualCsr',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.00'
            }
          },
          //   灰成份
          { title: 'SiO2', data: 'comSiO2', type: 'numeric', trimWhitespace: true },
          { title: 'Ai2O3', data: 'comAl2O3', type: 'numeric', trimWhitespace: true },
          { title: 'Fe2O3', data: 'comFe2O3', type: 'numeric', trimWhitespace: true },
          { title: 'CaO', data: 'comCaO', type: 'numeric', trimWhitespace: true },
          { title: 'MgO', data: 'comMgO', type: 'numeric', trimWhitespace: true },
          { title: 'Na2O', data: 'comNa2O', type: 'numeric', trimWhitespace: true },
          { title: 'K2O', data: 'comK2O', type: 'numeric', trimWhitespace: true },
          { title: 'TiO2', data: 'comTiO2', type: 'numeric', trimWhitespace: true },
          { title: 'P', data: 'comP2O5', type: 'numeric', trimWhitespace: true },
          { title: 'SO3', data: 'comSO3', type: 'numeric', trimWhitespace: true }
        ],
        nestedHeaders: [
          [
            '化验日期',
            '小样名称',
            '煤种',
            '负责人',
            {
              label: '入炉样测定',
              colspan: 11
            },
            {
              label: '入炉煤细度',
              colspan: 2
            },
            {
              label: '焦炭',
              colspan: 8
            },
            {
              label: '灰成分',
              colspan: 10
            }
          ],
          [
            '',
            '',
            '',
            '',
            '灰Ad%',
            '硫St,d',
            '挥发分Vdaf%',
            '特征Crc%',
            '粘结G',
            'Ymm',
            'Xmm',
            '全水Mt',
            '回收',
            '中煤',
            '矸石',
            '>3mm',
            '<3mm',
            'Ad',
            'St,d',
            'Vdaf',
            'M40',
            'M25',
            'M10',
            'CRI',
            'CSR',
            'SiO2',
            'Ai2O3',
            'Fe2O3',
            'CaO',
            'MgO',
            'Na2O',
            'K2O',
            'TiO2',
            'P',
            'SO3'
          ]
        ],

        readOnly: this.disabled,
        className: 'custom-classname',
        language: zhCN.languageCode,
        copyPaste: true,
        width: 'auto',
        height: 'auto',
        stretchH: 'all',
        manualColumnResize: true,
        manualRowResize: true,
        licenseKey: 'non-commercial-and-evaluation',
        formulas: {
          sheetName: 'Sheet1'
        },
        rowHeaders: false,
        contextMenu: {
          items: {
            // row_above: { name: '向上插入一行' },
            // row_below: { name: '向下插入一行' },
            // remove_row: { name: '删除行' },
          }
        }
      },
      settings: {
        startRows: 1,
        data: [{}],
        columns: [
          {
            title: 'Rran平均值',
            data: 'macRran',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.000'
            }
          },
          {
            title: 'Rmax平均值',
            data: 'macRmax',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.000'
            }
          },
          {
            title: '标准偏差S',
            data: 'macS',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.000'
            }
          },
          { title: '活性物', data: 'procV', type: 'numeric', trimWhitespace: true },
          { title: '活惰比', data: 'procVi', type: 'numeric', trimWhitespace: true },
          // 中煤

          { title: '软化温度', data: 'procT1', type: 'numeric', trimWhitespace: true },
          { title: '开始膨胀温度', data: 'procT2', type: 'numeric', trimWhitespace: true },
          { title: '固化温度', data: 'procT3', type: 'numeric', trimWhitespace: true },
          {
            title: '最大膨胀度',
            data: 'procB',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.0'
            }
          },
          {
            title: '最大收缩度',
            data: 'procA',
            type: 'numeric',
            trimWhitespace: true,
            numericFormat: {
              pattern: '0.0'
            }
          }
        ],
        nestedHeaders: [
          [
            {
              label: '岩相',
              colspan: 5
            },
            {
              label: '奥亚',
              colspan: 5
            }
          ],
          [
            'Rran平均值',
            'Rmax平均值',
            '标准偏差S',
            '活性物',
            '活惰比',

            '软化温度',
            '开始膨胀温度',
            '固化温度',
            '最大膨胀度',
            '最大收缩度'
          ]
        ],

        readOnly: this.disabled,
        className: 'custom-classname',
        language: zhCN.languageCode,
        copyPaste: true,
        width: 'auto',
        height: 'auto',
        stretchH: 'all',
        manualColumnResize: true,
        manualRowResize: true,
        licenseKey: 'non-commercial-and-evaluation',
        formulas: {
          sheetName: 'Sheet1'
        },
        rowHeaders: false,
        contextMenu: {
          items: {
            // row_above: { name: '向上插入一行' },
            // row_below: { name: '向下插入一行' },
            // remove_row: { name: '删除行' },
          }
        }
      }
    }
  },
  created() {
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'time',
        'customerName',
        'contractCode',
        'contractId',
        'receiverName',
        'productCode',
        'productId',
        'name',
        'type'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getName()
    this.permschange(this.curd)
    this.gethead()
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_EQS_id: to.params.id }
      })
    } else {
      next()
    }
  },
  watch: {
    'entityForm.name'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item == undefined) {
        this.entityForm.productName = name
        this.nameEntity.active = name
      }
      if (item) {
        this.nameEntity.active = item.name
        this.entityForm.productName = name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        // this.entityForm.type = item.coalCategory
      }
    },
    'userpermissionsList.active'(name) {
      if (name) {
        const item = this.userpermissionsList.options.find((item) => item.name === name)
        if (item == undefined) {
          this.entityForm.responsibleUserId = item.id
        }
        if (item) {
          this.entityForm.responsibleUserId = item.id
        }
      }
    }
  },
  computed: {
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    }
  },
  methods: {
    // 获取负责人接口
    async gethead() {
      const { data } = await this.model.getuserList()
      this.userpermissionsList.options = data.map((item) => {
        return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
      })
      this.responsiblelist = data.map((item, index) => {
        return item.name
      })
    },

    async saveEntityForm(entityForm) {
      let valid
      try {
        this.entityFormLoading = true
        // valid = await this.$refs[entityForm].validate()
        let validateResV = await new Promise((resolve, reject) => {
          this.$refs.hotV.hotInstance.validateCells((res) => {
            resolve(res)
          })
        })
        if (!validateResV) {
          this.$message({ showClose: true, message: '数据格式不正确，请检查！', type: 'warning' })
          return false
        }
        let Things = this.$refs.hotV.hotInstance.getData()
        var newArr = []
        let keyData = [
          'sampleDate',
          'name',
          'type',
          'responsibleUserName',
          'rawAd',
          'rawStd',
          'rawVdaf',
          'procCrc',
          'procG',
          'procY',
          'procX',
          'rawMt',
          'recover',
          'midCoal',
          'wasteRock',
          'less3mm',
          'greater3mm',
          'qualAd',
          'qualStd',
          'qualVdaf',
          'qualM40',
          'qualM25',
          'qualM10',
          'qualCri',
          'qualCsr',
          'comSiO2',
          'comAl2O3',
          'comFe2O3',
          'comCaO',
          'comMgO',
          'comNa2O',
          'comK2O',
          'comTiO2',
          'comP2O5',
          'comSO3'
        ]
        if (Things) {
          Things.forEach((item, index) => {
            if (item[0] != null) {
              let obj = {}
              item.forEach((s, i) => {
                obj[keyData[i]] = s
              })
              newArr.push(obj)
            }
          })
          this.entityForm.sampleDate = newArr[0].sampleDate
          this.entityForm.name = newArr[0].name
          this.entityForm.type = newArr[0].type
          this.entityForm.responsibleUserName = newArr[0].responsibleUserName
          this.entityForm.rawAd = newArr[0].rawAd
          this.entityForm.rawStd = newArr[0].rawStd
          this.entityForm.rawVdaf = newArr[0].rawVdaf
          this.entityForm.procCrc = newArr[0].procCrc
          this.entityForm.procG = newArr[0].procG
          this.entityForm.procY = newArr[0].procY
          this.entityForm.procX = newArr[0].procX
          this.entityForm.rawMt = newArr[0].rawMt
          this.entityForm.recover = newArr[0].recover
          this.entityForm.midCoal = newArr[0].midCoal
          this.entityForm.wasteRock = newArr[0].wasteRock
          this.entityForm.less3mm = newArr[0].less3mm
          this.entityForm.greater3mm = newArr[0].greater3mm
          this.entityForm.qualAd = newArr[0].qualAd
          this.entityForm.qualStd = newArr[0].qualStd
          this.entityForm.qualVdaf = newArr[0].qualVdaf
          this.entityForm.qualM40 = newArr[0].qualM40
          this.entityForm.qualM25 = newArr[0].qualM25
          this.entityForm.qualM10 = newArr[0].qualM10
          this.entityForm.qualCri = newArr[0].qualCri
          this.entityForm.qualCsr = newArr[0].qualCsr
          this.entityForm.comSiO2 = newArr[0].comSiO2
          this.entityForm.comAl2O3 = newArr[0].comAl2O3
          this.entityForm.comFe2O3 = newArr[0].comFe2O3
          this.entityForm.comCaO = newArr[0].comCaO
          this.entityForm.comMgO = newArr[0].comMgO
          this.entityForm.comNa2O = newArr[0].comNa2O
          this.entityForm.comK2O = newArr[0].comK2O
          this.entityForm.comTiO2 = newArr[0].comTiO2
          this.entityForm.comP2O5 = newArr[0].comP2O5
          this.entityForm.comSO3 = newArr[0].comSO3
        }
        // let validateRes = await new Promise((resolve, reject) => {
        //   this.$refs.hot.hotInstance.validateCells((res) => {
        //     resolve(res)
        //   })
        // })
        // if (!validateRes) {
        //   this.$message({ showClose: true, message: '数据格式不正确，请检查！', type: 'warning' })
        //   return false
        // }
        let Things1 = this.$refs.hot.hotInstance.getData()
        var newArr1 = []
        let keyData1 = ['macRran', 'macRmax', 'macS', 'procV', 'procVi', 'procT1', 'procT2', 'procT3', 'procB', 'procA']
        if (Things1) {
          Things1.forEach((item, index) => {
            if (item[0] != null) {
              let obj = {}
              item.forEach((s, i) => {
                obj[keyData1[i]] = s
              })
              newArr1.push(obj)
            }
          })
          if (newArr1.length > 0) {
            this.entityForm.macRran = newArr1[0].macRran
            this.entityForm.macRmax = newArr1[0].macRmax
            this.entityForm.macS = newArr1[0].macS
            this.entityForm.procV = newArr1[0].procV
            this.entityForm.procVi = newArr1[0].procVi

            this.entityForm.procT1 = newArr1[0].procT1
            this.entityForm.procT2 = newArr1[0].procT2
            this.entityForm.procT3 = newArr1[0].procT3
            this.entityForm.procB = newArr1[0].procB
            this.entityForm.procA = newArr1[0].procA
          }
        }
        const form = {
          id: this.dialogStatus === 'create' ? undefined : this.entityForm.id,
          ...this.entityForm
        }
        // await this.model.save(form)
        const res = await this.model.save(form)
        if (res) {
          this.$message({ message: '保存成功', type: 'success' })
          this.getList()
          this.resetVariant()
        }
      } catch (e) {
        // console.log(e, 'e')
        this.$message({ message: '请输入化验时间', type: 'warning' })
      } finally {
        this.entityFormLoading = false
      }
    },
    getFormatEditData(target = [], columns, require = true) {
      const getExcludeDirtyList = (target = []) => {
        const list = deepClone(target)
        // console.log(list)
        list.forEach((v) => {
          for (const key in v) {
            if ([null, ''].includes(v[key])) {
              delete v[key]
            }
          }
        })
        return list
      }
      const list = getExcludeDirtyList(target)
      const data = []
      // 获取需要校验的字段
      const validateList = columns
        .filter((col) => col.formRequire)
        .map((v) => {
          return { key: v.data, msg: v.title }
        })
      list.forEach((item, index) => {
        // 过滤掉空数据 但在编辑的时候会保留id所以不能被删除只能修改当前这条数据
        if (item && Object.keys(item).length) {
          validateList.forEach((v) => {
            if (!item[v.key]) {
              this.$message(`表格第${index + 1}行${v.msg}不能为空`)
              throw new Error('数据不能为空')
            }
          })
          data.push(item)
        }
      })
      if (!data.length && require) {
        this.$message({ message: '表格至少添加一条数据', type: 'warning' })
        throw new Error('数据不能为空')
      }
      return data
    },

    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      // let userdata = Cache.get('user')
      // console.log(userdata)
      // this.entityForm = { ...this.entityForm, responsibleUserName: userdata.userName, responsibleUserId: userdata.userId }
      // this.userpermissionsList.active = userdata.userName
      this.$nextTick(() => {
        let hotInstance = this.$refs.hotV.hotInstance
        console.log(hotInstance)
        hotInstance.updateSettings({
          columns: [
            {
              title: '化验日期',
              data: 'sampleDate',
              type: 'date',
              trimWhitespace: true,
              width: 120,
              validator: /[\s\S]/,
              correctFormat: true,
              dateFormat: 'YYYY-MM-DD'
            },
            { title: '小样名称', data: 'name', type: 'text', trimWhitespace: true, width: 140 },
            {
              title: '煤种',
              data: 'type',
              type: 'autocomplete',
              source: (window.dict['coal_type'] || []).map((item) => item.code),
              allowInvalid: true,
              visibleRows: 10,
              allowInvalid: true,
              trimWhitespace: true,
              width: 100
            },
            {
              title: '负责人',
              data: 'responsibleUserName',
              type: 'autocomplete',
              source: this.userpermissionsList.options.map((item) => item.name),
              allowInvalid: true,
              visibleRows: 10,
              allowInvalid: true,
              trimWhitespace: true,
              width: 100
            },
            // 入炉样测定
            { title: '灰Ad%', data: 'rawAd', type: 'numeric', trimWhitespace: true },
            { title: '硫St,d', data: 'rawStd', type: 'numeric', trimWhitespace: true },
            { title: '挥发分Vdaf%', data: 'rawVdaf', type: 'numeric', trimWhitespace: true },
            { title: '特征Crc%', data: 'procCrc', type: 'numeric', trimWhitespace: true },
            { title: '粘结G', data: 'procG', type: 'numeric', trimWhitespace: true },
            { title: 'Ymm', data: 'procY', type: 'numeric', trimWhitespace: true },
            { title: 'Xmm', data: 'procX', type: 'numeric', trimWhitespace: true },
            {
              title: '全水Mt',
              data: 'rawMt',
              type: 'numeric',
              trimWhitespace: true,
              numericFormat: {
                pattern: '0.00'
              }
            },
            { title: '回收', data: 'recover', type: 'numeric', trimWhitespace: true },
            { title: '中煤', data: 'midCoal', type: 'numeric', trimWhitespace: true },
            { title: '矸石', data: 'wasteRock', type: 'numeric', trimWhitespace: true },
            // 入炉煤细度

            { title: '>3mm', data: 'less3mm', type: 'numeric', trimWhitespace: true },
            { title: '<3mm', data: 'greater3mm', type: 'numeric', trimWhitespace: true },

            //   焦炭
            { title: 'Ad', data: 'qualAd', type: 'numeric', trimWhitespace: true },
            { title: 'St,d', data: 'qualStd', type: 'numeric', trimWhitespace: true },
            { title: 'Vdaf', data: 'qualVdaf', type: 'numeric', trimWhitespace: true },
            { title: 'M40', data: 'qualM40', type: 'numeric', trimWhitespace: true },
            { title: 'M25', data: 'qualM25', type: 'numeric', trimWhitespace: true },
            { title: 'M10', data: 'qualM10', type: 'numeric', trimWhitespace: true },
            { title: 'CRI', data: 'qualCri', type: 'numeric', trimWhitespace: true },
            { title: 'CSR', data: 'qualCsr', type: 'numeric', trimWhitespace: true },
            //   灰成份
            { title: 'SiO2', data: 'comSiO2', type: 'numeric', trimWhitespace: true },
            { title: 'Ai2O3', data: 'comAl2O3', type: 'numeric', trimWhitespace: true },
            { title: 'Fe2O3', data: 'comFe2O3', type: 'numeric', trimWhitespace: true },
            { title: 'CaO', data: 'comCaO', type: 'numeric', trimWhitespace: true },
            { title: 'MgO', data: 'comMgO', type: 'numeric', trimWhitespace: true },
            { title: 'Na2O', data: 'comNa2O', type: 'numeric', trimWhitespace: true },
            { title: 'K2O', data: 'comK2O', type: 'numeric', trimWhitespace: true },
            { title: 'TiO2', data: 'comTiO2', type: 'numeric', trimWhitespace: true },
            { title: 'P', data: 'comP2O5', type: 'numeric', trimWhitespace: true },
            { title: 'SO3', data: 'comSO3', type: 'numeric', trimWhitespace: true }
          ],
          afterChange: async (changes, source) => {
            if (changes) {
              changes.forEach((change) => {
                if (change[1] === 'responsibleUserName') {
                  console.log(change[3])
                  let coalPurchaseConfigRelationId = this.userpermissionsList.options.find((item) => item.name === change[3]).id
                  console.log(coalPurchaseConfigRelationId)
                  this.entityForm = { ...this.entityForm, responsibleUserId: coalPurchaseConfigRelationId }
                  console.log(this.$refs.hotV.hotInstance.getData())

                  // const fromData = { coalPurchaseConfigRelationId: coalPurchaseConfigRelationId }
                  // getCoalIndexAvgAndArrivePrice(fromData).then((res) => {
                  // if (res.data) {
                  // let Things = this.$refs.hot.hotInstance.getData()
                  // let ThingsV = this.$refs.hotV.hotInstance.getData()
                  // let objV = {}
                  // this.$refs.hotV.hotInstance.loadData([objV])
                  // }
                  // })
                }
              })
            }
          }
        })
      })
    },
    async getCreateData(obj = {}) {
      try {
        let data = []
        const result = []

        if (obj.list) {
          data = obj.list
        } else {
          const resp = await this.model.findRangeList()
          data = resp.data
        }

        function chunkArray(arr, chunkSize) {
          const chunkedArray = []
          for (let i = 0; i < arr.length; i += chunkSize) {
            chunkedArray.push(arr.slice(i, i + chunkSize))
          }
          return chunkedArray
        }

        // chunkArray(data, 5).forEach((list) => {
        //   const obj = {}
        //   let i = 1
        //   list.forEach((v) => {
        //     obj[i] = v.rangeName
        //     obj[i + 1] = v.proportion
        //     i += 2
        //   })
        //   result.push(obj)
        // })
        // this.editData1 = [...result]
      } catch (e) {
        // console.log(e, 'e')
      }
    },

    resetVariant() {
      // console.log('关闭')
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogFormVisibleV = false
      this.uploadList = [] // 清空下载列表
      this.fileList = []
      this.userpermissionsList.active = ''
      this.entityForm.attachmentList = []
      this.entityForm = { ...this.model.model }
      this.nameEntity.active = ''
      this.entityForm = { ...this.entityForm, attachmentList: [] }
      this.settingsV.data = [{}]
      this.settings.data = [{}]
      // if (!this.memoryEntity.triggered) {
      //   this.nameEntity.active = ''
      //   this.entityForm = { ...this.model.model }
      // } else {
      //   this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      // }
    },

    handleDownload(url) {
      window.open(url)
    },
    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      this.drawer.list = res.data.attachmentList
      let list = {}
      res.data.attachmentList.forEach((item) => {
        let { createDate, display, uri } = item
        createDate = createDate.match(/.+?(?=\s)/)[0]

        if (list[createDate]) {
          list[createDate].list.push({ display, uri })
        } else {
          list[createDate] = { date: createDate, list: [{ display, uri }] }
        }
      })
      this.drawer.dateList = list

      this.drawer.preview = res.data.attachmentList.map((item) => {
        let { createDate, display, uri } = item
        createDate = createDate.match(/.+?(?=\s)/)
        return { createDate, display, uri }
      })
      this.drawer.visible = true
    },

    async getName() {
      try {
        let { data } = await productModel.page({ size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
        })
      } catch (error) {}
    },
    handleNameEntity(val) {
      this.entityForm.name = val
    },
    handleToLocation(index) {
      this.locationActive = index === this.locationActive ? -1 : index
      this.$refs[this.curd].toLocation(this.locationActive)
    },
    async handleRemoveUpload(index) {
      const { id } = this.uploadList[index]
      this.$refs.upload.attachmentDelete({ id, index })
    },

    handleUploadSuccess(res) {
      this.uploadList = [...this.uploadList, res]
      // 上传完毕后更新展示的列表和把新增的附件id塞入到
      this.entityForm.attachmentList.push({ id: res.id })
      // this.entityForm.attachmentList.push(res)
    },
    handleUploadDelete({ status, index }) {
      this.uploadList.splice(index, 1)
    },
    async setSubmittext(fList) {
      // console.log(this.entityForm.attachmentList)
      // console.log(fList)
      fList.forEach((e, index) => {
        if (e.sampleDate) {
          var str = e.sampleDate
          str = str.replace(/\//g, '-')
          // console.log(str)
          e.sampleDate = str
        }
      })
      const text = JSON.stringify(fList)
      const res = await Model.importCoalSample({ text })
      if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      this.$message({ showClose: true, message: '导入成功', type: 'success' })
      this.dialogFormVisibleV = false
      this.getList()
    },
    okbtnV() {
      this.$refs.excelref.okbtn()
    },
    // async handleWatchForm(item) {
    //   item.type = this.entityForm = { ...item }
    //   this.dialogStatus = 'watch'
    //   this.dialogFormVisible = true
    //   // 上传所需要的配置
    //   this.uploadData.refId = item.id
    //   const { data } = await this.model.getUploadList(item.id)
    //   this.uploadList = data.attachmentList
    // },
    // async handleUpdate(item) {
    //   item.type = this.entityForm = { ...item }
    //   this.dialogStatus = 'update'
    //   this.dialogFormVisible = true
    //   // 上传所需要的配置
    //   this.uploadData.refId = item.id
    //   const { data } = await this.model.getUploadList(item.id)
    //   this.uploadList = data.attachmentList
    // },
    async handleUpdate(item, status) {
      try {
        await this.$nextTick()
        const { data } = await this.model.getUploadList(item.id)
        this.entityForm = {
          ...data
        }
        // await this.getCreateData({
        //   // rRan: data.rRan,
        //   // macR0: data.macR0,
        //   // macS: data.macS,
        //   list: data.rangeDtoList
        // })

        this.uploadList = data.attachmentList
        // this.uploadList1 = data.attachmentCsrList
        // this.editData = [...data.coalCleanQualBuyInItemList]

        // status == 'watch' &&
        //   this.editData.push({
        //     ad: data.ad,
        //     std: data.std,
        //     vdaf: data.vdaf,
        //     crc: data.crc,
        //     recovery: data.recovery,
        //     midCoal: data.midCoal,
        //     gangue: data.gangue,
        //     g: data.g,
        //     mt: data.mt,
        //   })
        let list = []
        let obj = {
          sampleDate: data.sampleDate,
          name: data.name,
          type: data.type,
          responsibleUserName: data.responsibleUserName,
          responsibleUserId: data.responsibleUserId,
          rawAd: data.rawAd,
          rawStd: data.rawStd,
          rawVdaf: data.rawVdaf,
          procCrc: data.procCrc,
          procG: data.procG,
          procY: data.procY,
          procX: data.procX,
          rawMt: data.rawMt,
          recover: data.recover,
          midCoal: data.midCoal,
          wasteRock: data.wasteRock,

          less3mm: data.less3mm,
          greater3mm: data.greater3mm,

          qualAd: data.qualAd,
          qualStd: data.qualStd,
          qualVdaf: data.qualVdaf,
          qualM40: data.qualM40,
          qualM25: data.qualM25,
          qualM10: data.qualM10,
          qualCri: data.qualCri,
          qualCsr: data.qualCsr,

          comSiO2: data.comSiO2,
          comAl2O3: data.comAl2O3,
          comFe2O3: data.comFe2O3,
          comCaO: data.comCaO,
          comMgO: data.comMgO,
          comNa2O: data.comNa2O,
          comK2O: data.comNa2O,
          comTiO2: data.comTiO2,
          comP2O5: data.comP2O5,
          comSO3: data.comSO3
        }
        list.push(obj)
        this.settingsV.data = list

        let list1 = []
        let obj1 = {
          macRran: data.macRran,
          macRmax: data.macRmax,
          macS: data.macS,
          procV: data.procV,
          procVi: data.procVi,

          procT1: data.procT1,
          procT2: data.procT2,
          procT3: data.procT3,
          procB: data.procB,
          procA: data.procA
        }
        list1.push(obj1)
        this.settings.data = list1
        this.uploadList = data.attachmentList
        this.entityForm = { ...data }
      } catch (e) {}
      this.dialogStatus = status
      this.dialogFormVisible = true
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
::v-deep .handsontable.listbox .ht_master table {
  position: absolute;
  z-index: 999999;
}

::v-deep .hot-table .handsontable table,
.handsontable tbody,
.handsontable thead,
.handsontable td,
.handsontable th,
.handsontable input,
.handsontable textarea,
.handsontable div {
  position: relative;
}

::v-deep .el-drawer__header > :first-child {
  font-size: 14px;
}
.images-warp {
  height: 100%;
  display: flex;
  justify-content: flex-start;
  align-content: flex-start;
  flex-flow: wrap;
  .links {
    padding-left: 20px;
    display: flex;
    width: 100%;
    justify-content: flex-start;
    flex-flow: wrap;
    margin-bottom: 10px;
    opacity: 0.95;
    .link-title {
      // color: ;
      color: #000;
      opacity: 0.5;
    }
  }
}
::v-deep .el-collapse-item__arrow {
  display: none;
}

.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;
    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      padding: 6px 10px;
      margin-right: 5px;
      font-size: 12px;
      position: relative;
      &-link {
        font-size: 12px;
        opacity: 0.8;
      }
      .icon {
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 9;
        top: 0;
        flex: 0;
      }
    }
  }
}

::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}

::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}
::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  color: #fff;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 5) {
  // border-left: none;
  // border-right: none;
  border: none;
}
::v-deep .el-table__fixed::before {
  background: transparent;
}
::v-deep .el-table__fixed-right::before {
  background: transparent;
}
</style>
<style scoped>
.tsel-dialog >>> .el-dialog {
  height: 100% !important;
}
.tsel-dialog >>> .dialog-footer {
  margin-top: 80px !important;
}
.tsel-dialog >>> .el-dialog__body {
  height: 72vh;
}

.hot-table >>> .autocompleteEditor {
  height: 120px !important;
  position: fixed !important;
  z-index: 999999 !important;
  overflow: auto;
}
</style>