<template>
  <div class="app-container">
    <filter-table :options="filterOption" :showAdd="perms[`${curd}:save`]||false" :showImport="perms[`${curd}:addimport`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" @add="handleCreate" @filter="handleFilter"
                  @import="handleImpiort" @reset="handleFilterReset" />
    <s-curd :ref="curd" :actions="actions" :changeactions="changeactions" :list-query="listQuery" :model="model" :name="curd"
            :showSummary="true" otherHeight="130">
      <el-table-column slot="attachment" align="center" label="查看附件" prop="attachment" width="100">
        <template slot-scope="scope">
          <div class="attachmentV" style="margin: 0 auto;">
            <span style="cursor: pointer;color: blue;" @click="$refs.attachment.open(scope.row)">查看附件</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column slot="opt" align="center" fixed="right" label="操作" min-width="100" width="150">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <el-tag class="opt-btn" color="#fa3131" @click="handleUpdate(scope.row,'watch')">查看</el-tag>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row,'update')">编辑</el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false"
               :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible" top="5vh">
      <el-form v-if="dialogFormVisible" ref="entityForm" :disabled="dialogStatus==='watch'" :model="entityForm" :rules="rules"
               :show-message="true" :status-icon="true" label-position="top" label-width="90px">
        <el-row>
          <div class="form-title">基本信息</div>
          <div class="form-layout">
            <el-row :gutter="80" type="flex">
              <el-col>
                <el-form-item label="日期" prop="date">
                  <date-select v-model="entityForm.date" />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="品名" prop="productName">
                  <select-down :list="nameEntity.options" :value.sync="nameEntity.active" @eventChange="handleNameEntity" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="form-title">化验指标</div>
          <div class="form-layout">
            <el-row>
              <el-col>
                <EditTable ref="editTable" v-model="editData" :columns="getColumns" @inited="handleEditTableInit"></EditTable>
              </el-col>
            </el-row>
          </div>
          <div class="form-title">附件信息</div>
          <div class="form-layout">
            <el-collapse :value="['1','2']">
              <el-collapse-item name="1" title="煤岩报告上传">
                <file-upload :list.sync="entityForm.attachmentList" />
              </el-collapse-item>
              <el-collapse-item name="2" title="CSR报告上传">
                <file-upload :list.sync="entityForm.attachmentCsrList" />
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-row>
      </el-form>
      <div v-if="dialogStatus!=='watch'" slot="footer" class="dialog-footer">
        <el-button v-if="perms[`${curd}:update`]||perms[`${curd}:save`]|| false" :loading="entityFormLoading"
                   class="dialog-footer-btns" type="primary" @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button class="dialog-footer-btns" @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
    <view-attachment ref="attachment" :request-method="model.getUploadList" />
  </div>
</template>
<script>
import Mixins from '@/utils/mixins'
import { coalblendingproduceOption, listQuery } from '@/const'
import Model from '@/model/coalQuality/coalblendingproduce'
import productModel from '@/model/product/productList'
import { createMemoryField, deepClone } from '@/utils/index'
import FilterTable from '@/components/FilterTable/index.vue'
import Handsontable from 'handsontable'
import UploadAttachment from '@/components/UploadAttachment/index.vue'
import FileUpload from '@/components/FileUpload/index.vue'
import ViewAttachment from '@/components/ViewAttachment/index.vue'

class QualProductionItem {
  constructor() {
    this.time = ''
    // 灰分
    this.ad = ''
    // 硫
    this.std = ''
    // 挥发分
    this.vdaf = ''
    // 粘结指数
    this.g = ''
    // 焦渣
    this.crc = ''
    // 全水
    this.mt = ''
    // 平均反射率
    this.macR0 = ''
    // 标准偏差
    this.macS = ''
    // 自动Y值
    this.smY = ''
    // 手动Y值
    this.y = ''
  }

}

export default {
  name: 'coalblendingproduce',
  data() {
    return {
      curd: 'coalblendingproduce',
      entityForm: { ...Model.model },
      model: Model,
      filterOption: { ...coalblendingproduceOption },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        productName: { required: true, message: '请选择品名', trigger: 'blur' },
        // productAliasName: { required: true, message: '请输入别名', trigger: 'blur' },
        time: { required: true, message: '请输入采样时间', trigger: 'blur' }
      },
      contractActive: [],
      // contractEntityFormActive: [],
      contractEntityForm: {
        options: []
      },
      contractParams: { query: '' },
      contract: {
        props: {
          label: 'customerName',
          value: 'customerId',
          children: 'contractSellList'
        },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      indicators: { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity },
      nameEntity: { options: [], active: '' },
      memoryEntity: { fields: {}, triggered: false },
      filters: {},
      fromDashboard: false,
      customerEntity: { options: [], active: [] },
      val: '',
      // excel配置
      excelConfig: { excel: Model.getImportConfig(), dialog: false },
      tableOption: {
        showPage: true,
        columns: [
          {
            label: '日期',
            title: '日期',
            prop: 'date',
            type: 'date',
            width: 100,
            validator: /[\s\S]/,
            correctFormat: true,
            dateFormat: 'YYYY-MM-DD'
          },
          {
            label: '采样时间',
            prop: 'time',
            title: '采样时间',
            type: 'time',
            validator: /[\s\S]/,
            dateFormat: 'HH:mm:ss'
          },
          {
            title: '品名',
            width: 100,
            prop: 'productName',
            type: 'autocomplete',
            visibleRows: 10,
            allowInvalid: true
            // validator: numberplateValidatorCallBack,
          },
          {
            label: '别名',
            title: '别名',
            type: 'text',
            validator: /[\s\S]/,
            prop: 'productAliasName'
          },
          {
            label: '硫分St,d',
            prop: 'std',
            title: '硫分St,d',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '灰分Ad',
            prop: 'ad',
            title: '灰分Ad',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '挥发分Vdaf',
            prop: 'vdaf',
            title: '挥发分Vdaf',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '特征CRC',
            prop: 'crc',
            title: '特征CRC',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'G值',
            prop: 'g',
            title: 'G值',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '水分',
            prop: 'mt',
            title: '水分',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'Y值',
            prop: 'y',
            title: 'Y值',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'CRI',
            prop: 'cri',
            title: 'CRI',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'CSR',
            prop: 'csr',
            title: 'CSR',
            type: 'text',
            validator: /[\s\S]/
          }
        ]
      },
      optName: 'create',
      editData: [
        ...Array(10)
          .fill(null)
          .map(() => new QualProductionItem())
      ]
    }
  },
  computed: {
    otherHeight() {
      return this.perms[`${this.curd}:export`] ? '227' : '180'
    },
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    },
    getColumns() {
      const dialogStatus = this.dialogStatus
      const len = this.editData.length
      const watchArr =
        dialogStatus === 'watch'
          ? [
            {
              data: null,
              title: '序号',
              width: 30,
              //只读
              readOnly: true,
              renderer: function (instance, td, row, col, prop, value, cellProperties) {
                Handsontable.renderers.TextRenderer.apply(this, arguments)
                const index = row + 1
                td.style.textAlign = 'center'
                td.innerHTML = index == len ? '平均值' : index
              }
            }
          ]
          : []

      return [
        ...watchArr,
        {
          title: '时间',
          width: 60,
          data: 'time'
        },
        {
          title: '灰分',
          width: 60,
          data: 'ad',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '硫分',
          width: 60,
          data: 'std',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '挥发分',
          width: 60,
          data: 'vdaf',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '粘结指数',
          width: 60,
          data: 'g',
          type: 'numeric'
        },
        {
          title: '焦渣',
          width: 60,
          data: 'crc',
          type: 'numeric'
        },
        {
          title: '全水',
          width: 60,
          data: 'mt',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '平均反射率',
          width: 60,
          data: 'macR0',
          type: 'numeric',
          numericFormat: {
            pattern: '0.000'
          }
        },
        {
          title: '标准偏差',
          width: 60,
          data: 'macS',
          type: 'numeric',
          numericFormat: {
            pattern: '0.000'
          }
        },
        {
          title: '自动Y值',
          width: 60,
          data: 'smY',
          type: 'numeric',
          numericFormat: {
            pattern: '0.0'
          }
        },
        {
          title: 'Y值',
          width: 60,
          data: 'y',
          type: 'numeric',
          numericFormat: {
            pattern: '0.0'
          }
        }
      ]
    }
  },
  components: { ViewAttachment, FileUpload, UploadAttachment, FilterTable },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_LIKES_date: to.params.date }
        vm.fromDashboard = true
      })
    } else {
      next()
    }
  },
  mixins: [Mixins],
  created() {
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'time',
        'customerName',
        'contractName',
        'contractCode',
        'contractId',
        'receiverName',
        'productName',
        'productCode',
        'productId',
        'name'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    // this.getContract()
    // this.getalllist() //获取供应商和客户列表
    this.permsAction(this.curd)
    this.getName()
    this.permschange(this.curd)
  },
  watch: {
    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item == undefined) {
        this.entityForm.productName = name
        this.nameEntity.active = name
      }
      if (item) {
        this.entityForm.productName = name
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
      }
    }
  },
  // 可写原方法覆盖mixins方法
  methods: {
    async saveEntityForm(entityForm) {
      let valid
      try {
        this.entityFormLoading = true
        valid = await this.$refs[entityForm].validate()
        const status = await this.$refs['editTable'].validate()
        if (!status) return
        const qualProductionItemList = this.getFormatEditData(this.editData, this.getColumns, true)
        const form = {
          id: this.dialogStatus === 'create' ? undefined : this.entityForm.id,
          ...this.entityForm,
          qualProductionItemList
        }
        // await this.model.save(form)
        const res = await this.model.save(form)
        if (res) {
          this.$message({ message: '保存成功', type: 'success' })
          this.dialogFormVisible = false
          this.getList()
          this.resetVariant()
        }
      } catch (e) {
        console.log(e, 'e')
      } finally {
        this.entityFormLoading = false
      }
    },
    /**
     * 过滤掉空数据，校验数据
     * @param target 提供的车牌列表
     * @param isSubmit 是否提交 主要用于控制是否校验车牌号 true时抛出Error
     */
    getFormatEditData(target = [], columns, require = true) {
      const getExcludeDirtyList = (target = []) => {
        const list = deepClone(target)
        list.forEach((v) => {
          for (const key in v) {
            if ([null, ''].includes(v[key])) {
              delete v[key]
            }
          }
        })
        return list
      }
      const list = getExcludeDirtyList(target)
      const data = []
      // 获取需要校验的字段
      const validateList = columns
        .filter((col) => col.formRequire)
        .map((v) => {
          return { key: v.data, msg: v.title }
        })

      list.forEach((item, index) => {
        // 过滤掉空数据 但在编辑的时候会保留id所以不能被删除只能修改当前这条数据

        if (Object.keys(item).length) {
          validateList.forEach((v) => {
            if (!item[v.key]) {
              this.$message(`表格第${index + 1}行${v.msg}不能为空`)
              throw new Error('数据不能为空')
            }
          })
          data.push(item)
        }
      })
      if (!data.length && require) {
        this.$message({ message: '表格至少添加一条数据', type: 'warning' })
        throw new Error('数据不能为空')
      }
      return data
    },
    async handleEditTableInit(instance) {
      console.log(this.dialogStatus)
      const getSetting = () => {
        if (this.dialogStatus === 'watch') {
          return {
            readOnly: true,
            // height: '350',
            contextMenu: undefined,
            data: this.editData,
            rowHeaders: false
          }
        }
        return {
          contextMenu: {
            items: {
              row_above: { name: '向上插入一行' },
              row_below: { name: '向下插入一行' },
              remove_row: { name: '删除行' },
              clear_custom: {
                name: '清空所有单元格数据',
                callback() {
                  this.clear()
                }
              }
            }
          },
          data: this.editData
        }
      }
      instance.updateSettings({
        columns: this.getColumns,
        ...getSetting(),
        async cells(row, col) {
        }
      })
    },
    //批量删除,
    async BatchDelete(selectList) {
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      let newarry = []
      for (var i = 0; i < selectList.length; i++) {
        let obj = {}
        obj = selectList[i].id
        newarry.push(obj)
      }
      let parm = {
        idList: newarry
      }
      // console.log(parm)

      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // console.log('确认')
          this.deletefn(parm)
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },
    async deletefn(parm) {
      let { data } = await Model.savebatchChange({ ...parm })
      if (data) {
        this.$message({ type: 'success', message: '删除成功!' })
        this.getList()
      }
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) {
      }
    },
    okbtnV() {
      this.$refs.excelref.okbtn()
    },
    closeExportExcelDialogV() {
      this.$refs.excelref.closeExportExcelDialog()
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },
    closeDialog() {
      this.$refs.excelref.closeExportExcelDialog()
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      this.uploadList = [] // 清空下载列表
      this.fileList = []
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        // this.contractEntityFormActive = []
        // this.customerEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
      this.editData = [
        ...Array(10)
          .fill(null)
          .map(() => new QualProductionItem())
      ]
    },
    async handleUpdate(item, status) {
      try {
        await this.$nextTick()
        const { data } = await this.model.getUploadList(item.id)
        this.entityForm = { ...data }
        this.editData = [...data.qualProductionItemList]
        if (status === 'watch') {
          this.editData.push({
            time: data.time,
            std: data.std,
            ad: data.ad,
            vdaf: data.vdaf,
            crc: data.crc,
            g: data.g,
            mt: data.mt,
            y: data.y,
            macR0: data.macR0,
            macS: data.macS,
            smY: data.smY,
            cri: data.cri,
            csr: data.csr
          })
        }
      } catch (e) {
      }
      this.dialogStatus = status
      this.dialogFormVisible = true
    },
    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    /**
     * 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;

  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      padding: 6px 10px;
      margin-right: 5px;
      font-size: 12px;
      position: relative;

      &-link {
        font-size: 12px;
        opacity: 0.8;
      }

      .icon {
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 9;
        top: 0;
        flex: 0;
      }
    }
  }
}

// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
// .opt-btnV {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
::v-deep .el-table__footer-wrapper tbody td {
  border: none !important;
}

.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);

  &-contract {
    width: 140px;
  }

  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;

    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }

    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;

      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}

::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}

::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  //color: #fff;
}

::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 4) {
  border-left: none;
  border-right: none;
}

::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 3) {
  border-left: none;
  border-right: none;
}
</style>
