<template>
  <div class="app-container">
    <filter-table :options="filterOption" :showAdd="perms[`${curd}:save`]||false" :showImport="perms[`${curd}:addimport`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" @add="handleCreate" @filter="handleFilter"
                  @import="handleImpiort" @reset="handleFilterReset" />
    <s-curd :ref="curd" :actions="actions" :changeactions="changeactions" :list-query="listQuery" :model="model" :name="curd"
            :showSummary="true" otherHeight="130">
      <el-table-column slot="productName" align="center" label="品名" prop="productName" minWidth="130">
        <template slot-scope="scope"><span class="name" @click="handleUpdate(scope.row,'watch')">{{
            scope.row.productName
          }}</span>
        </template>
      </el-table-column>
      <el-table-column slot="indicators" align="center" class-name="index" label="大堆精">
        <el-table-column align="center" label="灰分" prop="blendingAd" min-width="100" />
        <el-table-column align="center" label="硫" prop="blendingStd" min-width="100" />
        <el-table-column align="center" label="挥发分" prop="blendingVdaf" min-width="100" />
        <el-table-column align="center" label="粘结指数" prop="blendingG" min-width="100" />
        <el-table-column align="center" label="焦渣" prop="blendingCrc" min-width="100" />
        <el-table-column align="center" label="全水" prop="blendingMt" min-width="100" />
      </el-table-column>
      <el-table-column slot="attachment" align="center" label="查看附件" prop="attachment" width="100">
        <template slot-scope="scope">
          <div class="attachmentV" style="margin: 0 auto;">
            <span style="cursor: pointer;color: blue;" @click="$refs.attachment.open(scope.row)">查看附件</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column slot="opt" align="center" fixed="right" label="操作" min-width="100" width="150">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <el-tag class="opt-btn" color="#fa3131" @click="handleUpdate(scope.row,'watch')">查看</el-tag>
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row,'update')">编辑</el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag v-if="perms[`${curd}:delete`]||false" class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false"
               :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible" top="5vh">
      <el-form v-if="dialogFormVisible" ref="entityForm" :disabled="dialogStatus==='watch'" :model="entityForm" :rules="rules"
               :show-message="true" :status-icon="true" label-position="top">
        <div class="form-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="日期" prop="date">
              <date-select v-model="entityForm.date" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="品名" prop="productName">
              <el-select v-model="entityForm.productId" style="width: 100%" @change="handleNameEntity">
                <el-option v-for="item in nameEntity.options" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-tabs active-name="大堆精">
          <el-tab-pane label="脱介精" name="脱介精">
            <!--<div class="hot-table_heard" id="脱介精">脱介精</div>-->
            <hot-table ref="sculpingCleanList" :data="entityForm.sculpingCleanList" :settings="settings">
              <hot-column data="time" title="时间" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="haRaw" title="含精" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="haMid" title="含中" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="haRock" title="含矸" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="ad" title="灰分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="std" title="硫" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="insideMt" title="内水" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="highOrder" title="高位" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="infraversion" title="低位" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="mt" title="全水" type="numeric" />
            </hot-table>
          </el-tab-pane>
          <el-tab-pane label="脱介中" name="脱介中">
            <!--<div class="hot-table_heard" id="脱介中">脱介中</div>-->
            <hot-table ref="diapauseCoalList" :data="entityForm.diapauseCoalList" :settings="settings">
              <hot-column data="time" title="时间" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="diapauseHaRaw" title="含精" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="diapauseHaMid" title="含中" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="diapauseHaRock" title="含矸" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="diapauseAd" title="灰分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="diapauseStd" title="硫" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="diapauseInsideMt" title="内水" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="diapauseHighOrder" title="高位" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="diapauseInfraversion" title="低位" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="diapauseMt" title="全水" type="numeric" />
            </hot-table>
          </el-tab-pane>
          <el-tab-pane label="尾矿水" name="尾矿水">
            <!--<div class="hot-table_heard" id="尾矿水">尾矿水</div>-->
            <hot-table ref="tailingsCoalList" :data="entityForm.tailingsCoalList" :settings="settings">
              <hot-column data="time" title="时间" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="tailingCoalAd" title="灰分" type="numeric" />
            </hot-table>
          </el-tab-pane>
          <el-tab-pane label="煤泥" name="煤泥">
            <!--<div class="hot-table_heard" id="煤泥">煤泥</div>-->
            <hot-table ref="slurryCoalList" :data="entityForm.slurryCoalList" :settings="settings">
              <hot-column data="time" title="时间" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="coalSlurryAd" title="灰分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="coalSlurryStd" title="硫" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="coalSlurryMt" title="全水" type="numeric" />
            </hot-table>
          </el-tab-pane>
          <el-tab-pane label="大堆精" name="大堆精">
            <!--<div class="hot-table_heard" id="大堆精">大堆精</div>-->
            <hot-table ref="blendingCoalList" :data="entityForm.blendingCoalList" :settings="settings">
              <hot-column data="time" title="时间" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingAd" title="灰分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingStd" title="硫" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingVdaf" title="挥发分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0'}" data="blendingG" title="粘结指数" type="numeric" />
              <hot-column :numericFormat="{pattern: '0'}" data="blendingCrc" title="焦渣" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingMt" title="全水" type="numeric" />
            </hot-table>
          </el-tab-pane>
          <el-tab-pane label="脱介矸" name="脱介矸">
            <!--<div class="hot-table_heard" id="脱介矸">脱介矸</div>-->
            <hot-table ref="rockCoalList" :data="entityForm.rockCoalList" :settings="settings">
              <hot-column data="time" title="时间" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rockHaRaw" title="含精中" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rockHaRock" title="含矸" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rockAd" title="灰分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rockStd" title="硫" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rockInsideMt" title="内水" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rockHighOrder" title="高位" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rockInfraversion" title="低位" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rockMt" title="全水" type="numeric" />
            </hot-table>
          </el-tab-pane>
          <el-tab-pane label="浮精" name="浮精">
            <!--<div class="hot-table_heard" id="浮精">浮精</div>-->
            <hot-table ref="floatRawCoalList" :data="entityForm.floatRawCoalList" :settings="settings">
              <hot-column data="time" title="时间" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="floatRawAd" title="灰分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="floatRawStd" title="硫" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="floatRawVdaf" title="挥发分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0'}" data="floatRawG" title="粘结指数" type="numeric" />
              <hot-column :numericFormat="{pattern: '0'}" data="floatRawCrc" title="焦渣" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="floatRawMt" title="全水" type="numeric" />
            </hot-table>
          </el-tab-pane>
          <el-tab-pane label="沫精" name="沫精">
            <!--<div class="hot-table_heard" id="沫精">沫精</div>-->
            <hot-table ref="mrawCoalList" :data="entityForm.mrawCoalList" :settings="settings">
              <hot-column data="time" title="时间" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="mRawAd" title="灰分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="mRawStd" title="硫" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="mRawVdaf" title="挥发分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0'}" data="mRawG" title="粘结指数" type="numeric" />
              <hot-column :numericFormat="{pattern: '0'}" data="mRawCrc" title="焦渣" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="mRawMt" title="全水" type="numeric" />
            </hot-table>
          </el-tab-pane>
          <el-tab-pane label="车间精" name="车间精">
            <!--<div class="hot-table_heard" id="车间精">车间精</div>-->
            <hot-table ref="workshopCoalList" :data="entityForm.workshopCoalList" :settings="settings">
              <hot-column data="time" title="时间" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="workshopCoalAd" title="灰分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="workshopCoalStd" title="硫" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="workshopCoalVdaf" title="挥发分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0'}" data="workshopCoalG" title="粘结指数" type="numeric" />
              <hot-column :numericFormat="{pattern: '0'}" data="workshopCoalCrc" title="焦渣" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="workshopCoalMt" title="全水" type="numeric" />
            </hot-table>
          </el-tab-pane>
          <el-tab-pane label="入洗原煤" name="入洗原煤">
            <!--<div class="hot-table_heard" id="入洗原煤">入洗原煤</div>-->
            <hot-table ref="rawCoalList" :data="entityForm.rawCoalList" :settings="settings">
              <hot-column data="time" title="时间" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rawCoalRecycle" title="回收" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rawCoalHaMid" title="含中" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rawCoalHaRock" title="含矸" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rawCoalAd" title="灰分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rawCoalStd" title="硫" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rawCoalVdaf" title="挥发分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0'}" data="rawCoalG" title="粘结指数" type="numeric" />
              <hot-column :numericFormat="{pattern: '0'}" data="rawCoalCrc" title="焦渣" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="rawCoalMt" title="全水" type="numeric" />
            </hot-table>
          </el-tab-pane>
          <el-tab-pane label="大堆中" name="大堆中">
            <!--<div class="hot-table_heard" id="大堆中">大堆中</div>-->
            <hot-table ref="blendingRawCoalList" :data="entityForm.blendingRawCoalList" :settings="settings">
              <hot-column data="time" title="时间" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingRawRecycle" title="回收" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingRawHaMid" title="含中" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingRawHaRock" title="含矸" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingRawAd" title="灰分" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingRawStd" title="硫" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingRawInsideMt" title="内水" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingRawHighOrder" title="高位" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingRawInfraversion" title="低位" type="numeric" />
              <hot-column :numericFormat="{pattern: '0.00'}" data="blendingRawMt" title="全水" type="numeric" />
            </hot-table>
          </el-tab-pane>
        </el-tabs>
        <div class="form-title">附件信息</div>
        <div class="form-layout">
          <el-collapse :value="['1','2']">
            <el-collapse-item name="1" title="煤岩报告上传">
              <file-upload :list.sync="entityForm.attachmentList" />
            </el-collapse-item>
            <el-collapse-item name="2" title="CSR报告上传">
              <file-upload :list.sync="entityForm.attachmentCsrList" />
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-form>
      <div v-if="dialogStatus!=='watch'" slot="footer" class="dialog-footer">
        <el-button v-if="perms[`${curd}:update`]||perms[`${curd}:save`]|| false" :loading="entityFormLoading"
                   class="dialog-footer-btns" type="primary" @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button class="dialog-footer-btns" @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog ref="dialogStatus" :before-close="closeDialog" :close-on-click-modal="false" :close-on-press-escape="false"
               :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisibleV" top="5vh">

      <laboratoryImport v-if="dialogFormVisibleV" ref="excelref" :entityForm="entityForm"
                        :exportExcelDialogVisible.sync="dialogFormVisibleV" :tableOption="model.tableOption"
                        :traitSettings="tableOption" Buttontext="save" optName="create" type="productName"
                        @setSubmittext="setSubmittext">
      </laboratoryImport>
      <!--
      <div class="form-title">附件信息</div>
      <div class="form-layout">
        <el-collapse v-model="collapse">
          <el-collapse-item title="上传附件" name="1">
            <div class="upload">
              <div class="upload-list">
                <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                  <i class="el-icon-close icon" @click="handleRemoveUpload(index)"></i>
                  <el-link class="up-load-warp-link" :underline=" false" :href="item.uri">{{item.display}}</el-link>
                </div>
                <upload-attachment ref="upload" class="upload-attachment" :uploadData="uploadData" source="coalSample"
                                   :size="size" :limitNum="limitNum" accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg"
                                   @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete" listType="text" />
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div> -->

      <div style="float:right;margin-top:20px">
        <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" @click="okbtnV()">上传
        </el-button>
        <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" @click="closeExportExcelDialogV()">取消
        </el-button>
      </div>
    </el-dialog>
    <view-attachment ref="attachment" :request-method="model.getUploadList" />
  </div>
</template>
<script>
import { contractList } from '@/api/quality'
import CustomerAndSupplierModel from '@/model/user/supplierAndCustomer'
import contractBuyModel from '@/model/user/contractBuy'
import Mixins from '@/utils/mixins'
import { coalblendingproduceOption } from '@/const'
import Model from '@/model/coalQuality/coalwashing'
import productModel from '@/model/product/productList'
import { createMemoryField, deepClone } from '@/utils/index'
import Handsontable from 'handsontable'
import { HotColumn, HotTable } from '@handsontable/vue'
import FilterTable from '@/components/FilterTable/index.vue'
import DateSelect from '@/components/DateSelect/index.vue'
import SelectDown from '@/components/SelectDown/index.vue'
import EditTable from '@/components/EditTable/index.vue'
import { zhCN } from 'handsontable/i18n'
import HotDateSelect from '@/components/HotDateSelect/index.vue'
import UploadAttachment from '@/components/UploadAttachment/index.vue'
import FileUpload from '@/components/FileUpload/index.vue'
import ViewAttachment from '@/components/ViewAttachment/index.vue'

// 脱介精
class SculpingCleanItem {
  constructor() {
    this.ad = ''
    this.createBy = ''
    this.createDate = ''
    this.date = ''
    this.ext = ''
    this.extMap = ''
    this.haMid = ''
    this.haRaw = ''
    this.haRock = ''
    this.highOrder = ''
    this.id = ''
    this.infraversion = ''
    this.insideMt = ''
    this.mt = ''
    this.productAliasName = ''
    this.productCode = ''
    this.productId = ''
    this.productName = ''
    this.qualWashCoalId = ''
    this.remarks = ''
    this.std = ''
    this.time = ''
    this.updateBy = ''
    this.updateDate = ''
    this.vdaf = ''
  }
}

// 脱介中
class DiapauseCoalItem {
  constructor() {
    this.id = ''
    this.date = ''
    this.diapauseAd = ''
    this.diapauseHaMid = ''
    this.diapauseHaRaw = ''
    this.diapauseHaRock = ''
    this.diapauseHighOrder = ''
    this.diapauseInfraversion = ''
    this.diapauseInsideMt = ''
    this.diapauseMt = ''
    this.diapauseStd = ''
    this.ext = ''
    this.extMap = ''
    this.createBy = ''
    this.createDate = ''
  }
}

// 尾矿水
class TailingsCoalItem {
  constructor() {
    this.id = ''
    this.time = ''
    this.tailingCoalAd = ''
    this.createDate = ''
    this.productAliasName = ''
    this.productCode = ''
    this.productId = ''
    this.productName = ''
    this.qualWashCoalId = ''
    this.remarks = ''
    this.date = ''
    this.ext = ''
    this.extMap = ''
    this.createBy = ''
    this.updateBy = ''
    this.updateDate = ''
  }
}

// 煤泥
class SlurryCoalItem {
  constructor() {
    this.id = ''
    this.time = ''
    this.date = ''
    this.coalSlurryAd = ''
    this.coalSlurryMt = ''
    this.coalSlurryStd = ''
    this.productAliasName = ''
    this.productCode = ''
    this.productId = ''
    this.productName = ''
    this.qualWashCoalId = ''
    this.remarks = ''
    this.createBy = ''
    this.createDate = ''
    this.ext = ''
    this.extMap = ''
    this.updateBy = ''
    this.updateDate = ''
  }
}

// 大堆精
class BlendingCoalItem {
  constructor() {
    this.blendingAd = ''
    this.blendingCrc = ''
    this.blendingG = ''
    this.blendingMt = ''
    this.blendingStd = ''
    this.blendingVdaf = ''
    this.createBy = ''
    this.createDate = ''
    this.date = ''
    this.ext = ''
    this.extMap = ''
    this.id = ''
    this.productAliasName = ''
    this.productCode = ''
    this.productId = ''
    this.productName = ''
    this.qualWashCoalId = ''
    this.remarks = ''
    this.time = ''
    this.updateBy = ''
    this.updateDate = ''
  }
}

// 脱介矸
class RockCoalItem {
  constructor() {
    this.createBy = ''
    this.createDate = ''
    this.date = ''
    this.ext = ''
    this.extMap = ''
    this.id = ''
    this.productAliasName = ''
    this.productCode = ''
    this.productId = ''
    this.productName = ''
    this.qualWashCoalId = ''
    this.remarks = ''
    this.rockAd = ''
    this.rockHaRaw = ''
    this.rockHaRock = ''
    this.rockHighOrder = ''
    this.rockInfraversion = ''
    this.rockInsideMt = ''
    this.rockMt = ''
    this.rockStd = ''
    this.time = ''
    this.updateBy = ''
    this.updateDate = ''
  }
}

// 浮精
class FloatRawCoalItem {
  constructor() {
    this.createBy = ''
    this.createDate = ''
    this.date = ''
    this.ext = ''
    this.extMap = ''
    this.floatRawAd = ''
    this.floatRawCrc = ''
    this.floatRawG = ''
    this.floatRawMt = ''
    this.floatRawStd = ''
    this.floatRawVdaf = ''
    this.id = ''
    this.productAliasName = ''
    this.productCode = ''
    this.productId = ''
    this.productName = ''
    this.qualWashCoalId = ''
    this.remarks = ''
    this.time = ''
    this.updateBy = ''
    this.updateDate = ''
  }
}

// 沫精
class MrawCoalItem {
  constructor() {
    this.createBy = ''
    this.createDate = ''
    this.date = ''
    this.ext = ''
    this.extMap = ''
    this.id = ''
    this.mRawAd = ''
    this.mRawCrc = ''
    this.mRawG = ''
    this.mRawMt = ''
    this.mRawStd = ''
    this.mRawVdaf = ''
    this.productAliasName = ''
    this.productCode = ''
    this.productId = ''
    this.productName = ''
    this.qualWashCoalId = ''
    this.remarks = ''
    this.time = ''
    this.updateBy = ''
    this.updateDate = ''
  }
}

// 车间精
class WorkshopCoalItem {
  constructor() {
    this.createBy = ''
    this.createDate = ''
    this.date = ''
    this.ext = ''
    this.extMap = ''
    this.id = ''
    this.productAliasName = ''
    this.productCode = ''
    this.productId = ''
    this.productName = ''
    this.qualWashCoalId = ''
    this.remarks = ''
    this.time = ''
    this.updateBy = ''
    this.updateDate = ''
    this.workshopCoalAd = ''
    this.workshopCoalCrc = ''
    this.workshopCoalG = ''
    this.workshopCoalMt = ''
    this.workshopCoalStd = ''
    this.workshopCoalVdaf = ''
  }
}

// 入洗原煤
class RawCoalItem {
  constructor() {
    this.createBy = ''
    this.createDate = ''
    this.date = ''
    this.ext = ''
    this.extMap = ''
    this.id = ''
    this.productAliasName = ''
    this.productCode = ''
    this.productId = ''
    this.productName = ''
    this.qualWashCoalId = ''
    this.rawCoalAd = ''
    this.rawCoalCrc = ''
    this.rawCoalG = ''
    this.rawCoalHaMid = ''
    this.rawCoalHaRock = ''
    this.rawCoalMt = ''
    this.rawCoalRecycle = ''
    this.rawCoalStd = ''
    this.rawCoalVdaf = ''
    this.remarks = ''
    this.time = ''
    this.updateBy = ''
    this.updateDate = ''
  }
}

// 大堆中
class BlendingRawCoalItem {
  constructor() {
    this.blendingRawAd = ''
    this.blendingRawHaMid = ''
    this.blendingRawHaRock = ''
    this.blendingRawHighOrder = ''
    this.blendingRawInfraversion = ''
    this.blendingRawInsideMt = ''
    this.blendingRawMt = ''
    this.blendingRawRecycle = ''
    this.blendingRawStd = ''
    this.createBy = ''
    this.createDate = ''
    this.date = ''
    this.ext = ''
    this.extMap = ''
    this.id = ''
    this.productAliasName = ''
    this.productCode = ''
    this.productId = ''
    this.productName = ''
    this.qualWashCoalId = ''
    this.remarks = ''
    this.time = ''
    this.updateBy = ''
    this.updateDate = ''
  }
}

class Form {
  constructor() {
    this.date = ''
    this.productName = ''
    this.productId = ''
    this.sculpingCleanList = Array(5).fill(null).map(item => new SculpingCleanItem())
    this.diapauseCoalList = Array(5).fill(null).map(item => new DiapauseCoalItem())
    this.tailingsCoalList = Array(5).fill(null).map(item => new TailingsCoalItem())
    this.slurryCoalList = Array(5).fill(null).map(item => new SlurryCoalItem())
    this.blendingCoalList = Array(5).fill(null).map(item => new BlendingCoalItem())
    this.rockCoalList = Array(5).fill(null).map(item => new RockCoalItem())
    this.floatRawCoalList = Array(5).fill(null).map(item => new FloatRawCoalItem())
    this.mrawCoalList = Array(5).fill(null).map(item => new MrawCoalItem())
    this.workshopCoalList = Array(5).fill(null).map(item => new WorkshopCoalItem())
    this.rawCoalList = Array(5).fill(null).map(item => new RawCoalItem())
    this.blendingRawCoalList = Array(5).fill(null).map(item => new BlendingRawCoalItem())
  }
}

const form = {
  ext: '1',
  supplierName: '',
  supplierId: '',
  productName: '',
  productId: ''
}
export default {
  name: 'CoalWashing',
  data() {
    return {
      curd: 'coalwashing',
      entityForm: new Form(),
      model: Model,
      filterOption: { ...coalblendingproduceOption },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        // customerName: { required: true, message: '请选择客户', trigger: 'blur' },
        productName: { required: true, message: '请选择品名', trigger: 'blur' },
        time: { required: true, message: '请输入取样时间', trigger: 'blur' }
      },
      contractActive: [],
      // contractEntityFormActive: [],
      contractEntityForm: {
        options: []
      },
      contractParams: { query: '' },
      contract: {
        props: {
          label: 'customerName',
          value: 'customerId',
          children: 'contractSellList'
        },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      indicators: { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity },
      nameEntity: { options: [], active: '' },
      memoryEntity: { fields: {}, triggered: false },
      filters: {},
      fromDashboard: false,
      // customerEntity: { options: [], active: [] },
      supplierEntity: { value: '', options: [] },
      val: '',
      dialogFormVisibleV: false,
      // excel配置
      tableOption: {
        showPage: true,
        columns: [
          {
            label: '日期',
            title: '日期',
            prop: 'date',
            type: 'date',
            width: 100,
            validator: /[\s\S]/,
            correctFormat: true,
            dateFormat: 'YYYY-MM-DD'
          },
          {
            label: '送样时间',
            prop: 'time',
            title: '送样时间',
            type: 'time',
            validator: /[\s\S]/,
            dateFormat: 'HH:mm:ss'
          },
          // {
          //   label: '品名',
          //   prop: 'productName',
          //   title: '品名',
          //   type: 'text',
          //   validator: /[\s\S]/,
          // },
          {
            title: '品名',
            width: 100,
            prop: 'productName',
            type: 'autocomplete',
            visibleRows: 10,
            allowInvalid: true
            // validator: numberplateValidatorCallBack,
          },
          {
            label: '别名',
            title: '别名',
            type: 'text',
            validator: /[\s\S]/,
            prop: 'productAliasName'
          },
          {
            label: '硫分St,d',
            prop: 'std',
            title: '硫分St,d',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '灰分Ad',
            prop: 'ad',
            title: '灰分Ad',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '挥发分Vdaf',
            prop: 'vdaf',
            title: '挥发分Vdaf',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '特征CRC',
            prop: 'crc',
            title: '特征CRC',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'G值',
            prop: 'g',
            title: 'G值',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '水分Mt',
            prop: 'mt',
            title: '水分Mt',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '筛精硫分',
            prop: 'screenRawStd',
            title: '筛精硫分',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '筛精灰分',
            prop: 'screenRawAd',
            title: '筛精灰分',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '浮精硫分',
            prop: 'floatRawStd',
            title: '浮精硫分',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '尾煤灰分',
            prop: 'tailingCoalAd',
            title: '尾煤灰分',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '旋筛硫分',
            prop: 'rotaryScreenStd',
            title: '旋筛硫分',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '旋筛灰分',
            prop: 'rotaryScreenAd',
            title: '旋筛灰分',
            type: 'text',
            validator: /[\s\S]/
          }
        ]
      },
      editData: [
        ...Array(10)
          .fill(null)
          .map(() => ({}))
      ],
      listV: [{ ...form }],
      settings: {
        readOnly: this.disabled,
        className: 'custom-classname',
        language: zhCN.languageCode,
        width: 'auto',
        height: 'auto',
        manualColumnResize: false,
        licenseKey: 'non-commercial-and-evaluation',
        trimWhitespace: false,
        stretchH: 'all',
        contextMenu: {
          items: {
            row_above: { name: '向上插入一行' },
            row_below: { name: '向下插入一行' },
            remove_row: { name: '删除行' }
          }
        },
        startRow: 5,
        minRows: 5

      }
    }
  },
  computed: {
    otherHeight() {
      return this.perms[`${this.curd}:export`] ? '227' : '180'
    },
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    },
    getColumns() {
      const dialogStatus = this.dialogStatus
      const len = this.editData.length
      const watchArr =
        dialogStatus === 'watch'
          ? [
            {
              data: null,
              title: '序号',
              width: 30,
              //只读
              readOnly: true,
              renderer: function (instance, td, row, col, prop, value, cellProperties) {
                Handsontable.renderers.TextRenderer.apply(this, arguments)
                const index = row + 1
                td.style.textAlign = 'center'
                td.innerHTML = index == len ? '平均' : index
              }
            }
          ]
          : []

      return [
        ...watchArr,
        {
          title: '采样时间',
          width: 100,
          // type: 'date',
          type: 'text',
          data: 'time'
          // dateFormat: 'YYYY-MM-DD HH:mm:ss'
        },
        {
          title: '硫分St,d',
          width: 60,
          data: 'std',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '灰分Ad',
          width: 60,
          data: 'ad',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '挥发分Vdaf',
          width: 60,
          data: 'vdaf',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '特征CRC',
          width: 60,
          data: 'crc',
          type: 'numeric'
        },
        {
          title: 'G值',
          width: 60,
          data: 'g',
          type: 'numeric'
        },
        {
          title: '水分',
          width: 60,
          data: 'mt',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '筛精硫分',
          width: 60,
          data: 'screenRawStd',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '筛精灰分',
          width: 60,
          data: 'screenRawAd',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '浮精灰分',
          width: 60,
          data: 'floatRawAd',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '浮精硫分',
          width: 60,
          data: 'floatRawStd',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '尾煤灰分',
          width: 60,
          data: 'tailingCoalAd',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '旋筛硫分',
          width: 60,
          data: 'rotaryScreenStd',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '旋筛灰分',
          width: 60,
          data: 'rotaryScreenAd',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        }
      ]
    }
  },
  components: {
    ViewAttachment,
    FileUpload, UploadAttachment, HotDateSelect, EditTable, SelectDown, DateSelect, FilterTable, HotTable, HotColumn
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_LIKES_date: to.params.date }
        vm.fromDashboard = true
      })
    } else {
      next()
    }
  },
  mixins: [Mixins],
  created() {
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'time',
        'customerName',
        'contractName',
        'contractCode',
        'contractId',
        'receiverName',
        'productName',
        'productCode',
        'productId',
        'name'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getalllist() //获取供应商和客户列表
    this.permsAction(this.curd)
    this.permschange(this.curd)
    this.getName()
    this.getSupplier()
  },
  // 可写原方法覆盖mixins方法
  methods: {
    //供应商列表
    async getSupplier() {
      const { data } = await contractList()
      if (data.length) this.supplierEntity.options = data
      // try {
      //   let options = []
      //   const { data } = await contractList()
      //   if (data.records.length) {
      //     data.records.forEach((item) => {
      //       const { name: label, id: value } = item
      //       options.push({ label, value })
      //     })
      //   }
      //   this.supplierEntity.options = options
      // } catch (e) {}
    },
    handleClose() {
      this.entityForm = new Form()
      this.settings.minRows = 5
      this.resetVariant()
    },
    // 合同改变同步entityForm
    handleSupplier(value, row) {
      console.log(value)
      console.log(row)
      console.log(this.supplierEntity.options)
      let data = this.listV
      // setTimeout(() => {
      for (var i = 0; i < data.length; i++) {
        console.log(i)
        let ff = data[i]
        console.log(ff)
        if (row.ext == ff.ext) {
          const itemf = this.supplierEntity.options.find((item) => item.name == value)
          console.log(itemf)
          ff.supplierId = itemf.id
          ff.supplierName = itemf.name
          this.$set(data, i, ff)
        }
      }
      // }, 300)
      // this.getProductListfnc(value)
    },
    handleNameEntityV(row) {
      let data = this.listV
      setTimeout(() => {
        for (var i = 0; i < data.length; i++) {
          let ff = data[i]
          if (row.ext == ff.ext) {
            const itemf = this.nameEntity.options.find((item) => item.name == row.productName)
            console.log(itemf)
            ff.productId = itemf.id
            ff.productName = itemf.name
            this.$set(data, i, ff)
          }
        }
      }, 300)
    },
    headClass() {
      return 'text-align:center;background:#24c1ab;color:#fff;font-weight:400;border:none;'
    },
    handleAdd() {
      const form = { ...form }
      form.ext = Math.random().toFixed(6).slice(-6)
      this.listV.push(form)
    },
    handleRemove({ ext }) {
      console.log(ext)
      const index = this.listV.findIndex((item) => item.ext === ext)
      if (index === -1) return false
      this.listV.splice(index, 1)
    },
    async saveEntityForm() {
      let valid = await this.$refs.entityForm.validate().catch(e => console.log(e))
      this.settings.minRows = 0
      const validTable = async (hotInstanceName) => {
        return await new Promise((resolve, reject) => {
          /** @type {import('handsontable').default} */
          let hotInstance = this.$refs[hotInstanceName].hotInstance
          hotInstance.validateCells((result) => {
            if (!result) this.$message.error('表单有中有异常数据，请检查！')
            resolve(result)
          })
        })
      }
      const filterTable = (hotInstanceName) => {
        /** @type {import('handsontable').default} */
        let hotInstance = this.$refs[hotInstanceName].hotInstance
        for (let i = this.entityForm[hotInstanceName].length - 1; i >= 0; i--) {
          if (hotInstance.isEmptyRow(i)) hotInstance.alter('remove_row', i, 1)
        }
      }
      if (valid) {
        if (!await validTable('sculpingCleanList')) return
        if (!await validTable('diapauseCoalList')) return
        if (!await validTable('tailingsCoalList')) return
        if (!await validTable('slurryCoalList')) return
        if (!await validTable('blendingCoalList')) return
        if (!await validTable('rockCoalList')) return
        if (!await validTable('floatRawCoalList')) return
        if (!await validTable('mrawCoalList')) return
        if (!await validTable('workshopCoalList')) return
        if (!await validTable('rawCoalList')) return
        if (!await validTable('blendingRawCoalList')) return
        filterTable('sculpingCleanList')
        filterTable('diapauseCoalList')
        filterTable('tailingsCoalList')
        filterTable('slurryCoalList')
        filterTable('blendingCoalList')
        filterTable('rockCoalList')
        filterTable('floatRawCoalList')
        filterTable('mrawCoalList')
        filterTable('workshopCoalList')
        filterTable('rawCoalList')
        filterTable('blendingRawCoalList')
        this.entityFormLoading = true
        const res = await this.model.save(this.entityForm)
        this.entityFormLoading = false
        if (res) {
          this.$message({ message: '保存成功', type: 'success' })
          this.dialogFormVisible = false
          this.getList()
          this.resetVariant()
        }
      }
    },
    async handleEditTableInit(instance) {
      console.log(this.dialogStatus)
      const getSetting = () => {
        if (this.dialogStatus === 'watch') {
          return {
            readOnly: true,
            // height: '350',
            contextMenu: undefined,
            data: this.editData,
            rowHeaders: false
          }
        }
        return {
          contextMenu: {
            items: {
              row_above: { name: '向上插入一行' },
              row_below: { name: '向下插入一行' },
              remove_row: { name: '删除行' },
              clear_custom: {
                name: '清空所有单元格数据',
                callback() {
                  this.clear()
                }
              }
            }
          },
          data: this.editData
        }
      }
      instance.updateSettings({
        columns: this.getColumns,
        ...getSetting(),
        async cells(row, col) {
        }
      })
    },
    /**
     * 过滤掉空数据，校验数据
     * @param target 提供的车牌列表
     */
    getFormatEditData(target = [], columns, require = true) {
      const getExcludeDirtyList = (target = []) => {
        const list = deepClone(target)
        list.forEach((v) => {
          for (const key in v) {
            if ([null, ''].includes(v[key])) {
              delete v[key]
            }
          }
        })
        return list
      }
      const list = getExcludeDirtyList(target)
      const data = []
      // 获取需要校验的字段
      const validateList = columns
        .filter((col) => col.formRequire)
        .map((v) => {
          return { key: v.data, msg: v.title }
        })

      list.forEach((item, index) => {
        // 过滤掉空数据 但在编辑的时候会保留id所以不能被删除只能修改当前这条数据

        if (Object.keys(item).length) {
          validateList.forEach((v) => {
            if (!item[v.key]) {
              this.$message(`表格第${index + 1}行${v.msg}不能为空`)
              throw new Error('数据不能为空')
            }
          })
          data.push(item)
        }
      })
      if (!data.length && require) {
        this.$message({ message: '表格至少添加一条数据', type: 'warning' })
        throw new Error('数据不能为空')
      }
      return data
    },
    //批量删除,
    async BatchDelete(selectList) {
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      let newarry = []
      for (var i = 0; i < selectList.length; i++) {
        let obj = {}
        obj = selectList[i].id
        newarry.push(obj)
      }
      let parm = {
        idList: newarry
      }
      // console.log(parm)

      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // console.log('确认')
          this.deletefn(parm)
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },
    async deletefn(parm) {
      let { data } = await Model.savebatchChange({ ...parm })
      if (data) {
        this.$message({ type: 'success', message: '删除成功!' })
        this.getList()
      }
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
        console.log(this.nameEntity.options)
      } catch (error) {
      }
    },
    okbtnV() {
      this.$refs.excelref.okbtn()
    },
    closeExportExcelDialogV() {
      this.$refs.excelref.closeExportExcelDialog()
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },
    closeDialog() {
      this.$refs.excelref.closeExportExcelDialog()
    },
    async setSubmittext(fList) {
      // console.log(fList)
      fList.forEach((e, index) => {
        if (e.date) {
          var str = e.date
          str = str.replace(/\//g, '-')
          // console.log(str)
          e.date = str
        }
      })
      const text = JSON.stringify(fList)
      const res = await Model.importQualWashCoal({ text })
      if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      this.$message({ showClose: true, message: '导入成功', type: 'success' })
      this.dialogFormVisibleV = false
      this.getList()
    },
    async getalllist() {
      //获取客户和供应商列表
      try {
        let options = []
        const { data } = await CustomerAndSupplierModel.findByKeyword({})
        if (data.length) {
          data.forEach((item) => {
            const { name: label, id: value, type: type } = item
            options.push({ label, value, type })
          })
        }
        this.customerEntity.options = options
      } catch (e) {
      }
    },
    async getSupplierProductName(id) {
      try {
        const { data } = await contractBuyModel.getProductListfn({ supplierId: id })

        this.nameEntity.options = data.map((item) => {
          return { name: item.name, id: item.id, code: item.code }
        })
        if (this.nameEntity.options.length == 1) {
          this.entityForm.productName = this.nameEntity.options[0].name
        }
      } catch (e) {
      }
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      this.uploadList = [] // 清空下载列表
      this.fileList = []
      this.listV = [{ ...form }]
      this.editData = [
        ...Array(10)
          .fill(null)
          .map(() => ({}))
      ]

      if (!this.memoryEntity.triggered) {
        this.entityForm = new Form()
      } else {
        this.entityForm = new Form()
      }
      this.settings.minRows = 5
    },
    async handleUpdate(item, status) {
      try {
        await this.$nextTick()
        const { data } = await this.model.getUploadList(item.id)
        this.entityForm = { ...data }
        this.editData = [...data.qualWashCoalItemList]
        this.listV = [...data.qualWashCoalProductList]
        if (status === 'watch') {
          this.editData.push({
            time: data.time,
            std: data.std,
            ad: data.ad,
            vdaf: data.vdaf,
            crc: data.crc,
            g: data.g,
            mt: data.mt,
            Y: data.y,
            cri: data.cri,
            csr: data.csr,
            screenRawStd: data.screenRawStd,
            screenRawAd: data.screenRawAd,
            floatRawStd: data.floatRawStd,
            tailingCoalAd: data.tailingCoalAd,
            rotaryScreenStd: data.rotaryScreenStd,
            rotaryScreenAd: data.rotaryScreenAd,
            floatRawAd: data.floatRawAd
          })
        }
      } catch (e) {
      }
      this.dialogStatus = status
      this.dialogFormVisible = true
    },
    handleNameEntity(val) {
      this.entityForm.productName = this.nameEntity.options.find(item => item.id === val)?.name
    },
    /**
     * 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.hot-table_heard {
  background-color: #24c1ab;
  width: 100%;
  color: white;
  text-align: center;
  line-height: 36px;
  font-size: 13px;
  //margin-top: 20px;
}

.link {
  margin-right: 10px;
}

:deep(.el-tabs__content) {
  padding: 0;
}

.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;

  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      padding: 6px 10px;
      margin-right: 5px;
      font-size: 12px;
      position: relative;

      &-link {
        font-size: 12px;
        opacity: 0.8;
      }

      .icon {
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 9;
        top: 0;
        flex: 0;
      }
    }
  }
}

.form-warp {
  padding: 0px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.form-titlV {
  font-size: 16px;
  position: relative;
  // padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 11px;
    background: #2f79e8;
    height: 16px;
  }
}

::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}

::v-deep .el-input.is-disabled .el-input__inner {
  color: #606266;
}

.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);

  &-contract {
    width: 140px;
  }

  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;

    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }

    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;

      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}

::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  color: #fff;
}

::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 5) {
  border-left: none;
  border-right: none;
}
</style>
