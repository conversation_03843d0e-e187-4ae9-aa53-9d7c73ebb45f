<template>
  <div class="app-container">
    <panel-bar type="panel" title="数据看板">
      <el-date-picker e-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate"
                      placeholder="选择日期" :clearable="false" />
    </panel-bar>
    <card height="inherit" style="height:43vh">
      <card-item :title="name" sub="（灰分Ad）">
        <div class="item-echarts">
          <echarts width="inherit" height="inherit" v-if="isAdOption" :option="adOption" />
        </div>
      </card-item>
      <card-item :title="name" sub="（硫分St,d）">
        <div class="item-echarts">
          <echarts width="inherit" height="inherit" v-if="isStdOption" :option="stdOption" />
        </div>
      </card-item>
    </card>
    <panel-bar type="pounds" title="化验录入" subtitle="(支持excel表导入、支持模板下载、支持数据导出至excel)" style="margin:10px 0" />
    <section class="test">
      <div class="left">
        <span class="left-title">生产煤质监测</span>
        <div class="left-section">
          <div class="left-section-content">
            <div class="img" style="background:#2f79e8;" @click="handleToPage('coalSample')">
              <img src="@/assets/console/quality_xy.png" />
            </div>
            <div class="desc" @click="handleToPage('coalSample')">
              <span>小样录入</span>
              <span>SAMPLE ENTRY</span>
            </div>
          </div>
        </div>
        <div class="bottom-section">
          <div class="entry" @click="handleToPage('qualBelt')">
            <span>皮带样录入</span>
            <span>BELT SAMPLE</span>
          </div>
          <div class="entry" @click="handleToPage('qualPile')">
            <span>大堆样录入</span>
            <span>BULK SAMPLE</span>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="right-section">
          <span class="right-title">新增试验报表</span>
          <div class="right-areas">
            <div class="area">
              <div class="img" style="background:#33CAD9;" @click="handleToPage('qualBuyIn')">
                <img src="@/assets/console/purchase_enter.png" />
              </div>
              <div class="desc" @click="handleToPage('qualBuyIn')">
                <span>购进化验录入</span>
                <span>PURCHASE ENTRY</span>
              </div>
            </div>
            <div class="area" @click="handleToPage('qualSellOut')">
              <div class="img" style="background:#FF726B;">
                <img src="@/assets/console/sales_test_enter.png" />
              </div>
              <div class="desc" @click="handleToPage('qualSellOut')">
                <span>销售化验录入</span>
                <span>SALES TEST ENTRY</span>
              </div>
            </div>
          </div>
        </div>
        <div class="right-section">
          <span class="right-title">客户指标</span>
          <div class="right-areas">

            <div class="area">
              <div class="img" style="background:#2f79e8;" @click="handleToPage('customerFeedback')">
                <img src="@/assets/console/quality_yh.png" />
              </div>
              <div class="desc" @click="handleToPage('customerFeedback')">
                <span>客户反馈录入</span>
                <span>CUSTOMER FEEDBACK</span>
              </div>
            </div>

            <div class="area" style="visibility:hidden;">
              <div class="img" style="background:#2f79e8;" @click="handleToPage('customerFeedback')">
                <img src="@/assets/console/quality_yh.png" />
              </div>
              <div class="desc" @click="handleToPage('customerFeedback')">
                <span>客户反馈录入</span>
                <span>CUSTOMER FEEDBACK</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { chartData } from '@/api/quality'
import { PanelBar, Card, CardItem } from '@/components/Console'
import { listQuery } from '@/const'
const option = {
  animation: false,
  color: ['#1BB681', '#FF726B', '#FCBE38'],
  tooltip: {
    trigger: 'axis',
    point: [-30, -30],
    backgroundColor: '#EDEEFF',
    padding: [5, 20, 5, 20],
    textStyle: {
      color: '#9f9ea5',
    },
  },
  grid: {
    top: '50px',
    left: '50px',
    right: '20px',
    bottom: '50px',
  },
  legend: {
    right: 20,
    itemGap: 20,
    padding: [10, 0, 10, 10],
    align: 'left',
    data: ['皮带样', '大堆样', { name: '要求指标', icon: 'line' }],
  },
  xAxis: {
    boundaryGap: false, // 不留白，从原点开始
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF', // 坐标轴线的颜色修改--文字也同步修改
      },
    },
    axisTick: {
      // 坐标轴刻度相关设置
      // show:true, // 是否显示坐标轴刻度
      alignWithLabel: false, // 可以保证刻度线和标签对齐
    },
    axisLabel: {
      color: '#9f9ea5', // 刻度标签文字的颜色
    },
    data: [],
  },
  yAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF',
        type: 'solid',
      },
    },
    axisLabel: {
      // 坐标轴刻度标签的相关设置
      color: '#9f9ea5',
    },
    splitLine: {
      // 坐标轴在 grid 区域中的分隔线。
      show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示。
      lineStyle: {
        color: '#E3F0FF', // 分隔线颜色，可以设置成单个颜色
      },
    },
  },
  series: [
    {
      name: '皮带样',
      type: 'line',
      symbolSize: 8,
      symbol: 'circle',
      // areaStyle: {
      //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //     { offset: 0, color: '#1BB681' },
      //     { offset: 1, color: '#fff' }
      //   ])
      // },
      data: [],
    },
    {
      name: '大堆样',
      type: 'line',
      symbolSize: 8,
      symbol: 'circle',
      // areaStyle: {
      //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //     { offset: 0, color: '#FF726B' },
      //     { offset: 1, color: '#fff' }
      //   ])
      // },
      data: [],
    },
    {
      name: '要求指标',
      type: 'line',
      symbol: 'none',
      data: [],
    },
  ],
}
const stdOption = {
  animation: false,
  color: ['#FF726B', '#5C61CA', '#33CAD9'],
  tooltip: {
    trigger: 'axis',
    point: [-30, -30],
    backgroundColor: '#EDEEFF',
    padding: [5, 20, 5, 20],
    textStyle: {
      color: '#9f9ea5',
    },
  },
  grid: {
    top: '50px',
    left: '50px',
    right: '20px',
    bottom: '50px',
  },
  legend: {
    right: 20,
    itemGap: 20,
    padding: [10, 0, 10, 10],
    align: 'left',
    data: ['皮带样', '大堆样', { name: '要求指标', icon: 'line' }],
  },
  xAxis: {
    boundaryGap: false, // 不留白，从原点开始
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF', // 坐标轴线的颜色修改--文字也同步修改
      },
    },
    axisTick: {
      // 坐标轴刻度相关设置
      // show:true, // 是否显示坐标轴刻度
      alignWithLabel: false, // 可以保证刻度线和标签对齐
    },
    axisLabel: {
      color: '#9f9ea5', // 刻度标签文字的颜色
    },
    data: [],
  },
  yAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF',
        type: 'solid',
      },
    },
    axisLabel: {
      // 坐标轴刻度标签的相关设置
      color: '#9f9ea5',
    },
    splitLine: {
      // 坐标轴在 grid 区域中的分隔线。
      show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示。
      lineStyle: {
        color: '#E3F0FF', // 分隔线颜色，可以设置成单个颜色
      },
    },
    // data: ['10:1', '10.2', '10:3', '10:4', '10.5']
  },
  series: [
    {
      name: '皮带样',
      type: 'line',
      symbolSize: 10,
      symbol: 'circle',
      // areaStyle: {
      //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //     { offset: 0, color: '#FF9639' },
      //     { offset: 1, color: '#fff' }
      //   ])
      // },
      data: [],
    },
    {
      name: '大堆样',
      type: 'line',
      symbolSize: 10,
      symbol: 'circle',
      // areaStyle: {
      //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //     { offset: 0, color: '#5C61CA' },
      //     { offset: 1, color: '#fff' }
      //   ])
      // },
      data: [],
    },
    {
      name: '要求指标',
      type: 'line',
      symbol: 'none',
      data: [],
    },
  ],
}
export default {
  name: 'coalQualityConsole',
  components: { PanelBar, Card, CardItem },
  data() {
    return {
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      listQuery: { ...listQuery },
      loading: false,
      contract: [],
      adOption: { ...option },
      stdOption: { ...stdOption },
      ad: {
        list: [],
        indicators: null,
      },
      std: {
        list: [],
        indicators: null,
      },
      name: '',
    }
  },
  created() {
    this.getData()
  },
  computed: {
    isAdOption() {
      if (Object.keys(this.adOption).length) return true
      return false
    },
    isStdOption() {
      if (Object.keys(this.stdOption).length) return true
      return false
    },
  },
  methods: {
    async getData(filter_EQS_date = '') {
      if (this.loading) return
      this.loading = true
      try {
        const { data: qualPileBeltAdStdVoList } = await chartData(filter_EQS_date)
        this.name = qualPileBeltAdStdVoList.productName
        this.ad.indicators = qualPileBeltAdStdVoList.adLimit
        this.std.indicators = qualPileBeltAdStdVoList.stdLimit
        let timeList = []
        let adPileList = []
        let adBeltList = []
        let stdBeltList = []
        let stdPileList = []
        qualPileBeltAdStdVoList.qualPileBeltAdStdVoList.forEach((item) => {
          const { adBelt, adPile, stdBelt, stdPile, time } = item
          timeList.push(time)
          adPileList.push(adPile)
          adBeltList.push(adBelt)
          stdBeltList.push(stdBelt)
          stdPileList.push(stdPile)
        })
        this.adOption.xAxis.data = timeList
        this.adOption.series[0].data = adBeltList
        this.adOption.series[1].data = adPileList
        this.adOption.series[2].data = new Array(timeList.length).fill(this.ad.indicators)
        this.stdOption.xAxis.data = timeList
        this.stdOption.series[0].data = stdBeltList
        this.stdOption.series[1].data = stdPileList
        this.stdOption.series[2].data = new Array(timeList.length).fill(this.std.indicators)
        this.loading = false
      } catch (error) {
        this.loading = false
      }
    },
    findContract(contractId) {
      for (const val of this.contract) {
        for (const obj of val.contractSellList) {
          if (obj.id === contractId) return obj
        }
      }
    },

    handleToPage(pageName) {
      this.$store.dispatch('changeChildRoute', { parent: pageName })
      this.$router.push({ name: pageName, params: { isOpenAdd: true } })
    },
  },
  watch: {
    currentDate: {
      handler(v) {
        this.getData(v)
      },
      immediate: true,
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .card-item {
  padding: 5px;
}
::v-deep .item-echarts {
  width: 98.5%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  height: inherit;
}
.test {
  display: flex;
  justify-content: space-evenly;
  width: 100%;
  height: 31rem;
  .left {
    background: #fff;
    padding: 1.5rem;
    font-size: 1.6rem;
    flex: 1;
    margin-right: 10px;
    display: flex;
    flex-flow: column;
    &-section {
      padding: 6rem 0 4rem;
      display: flex;
      justify-content: center;
      &-content {
        display: flex;
        .img {
          position: relative;
          width: 6rem;
          height: 6rem;
          border-radius: 50%;
          margin-right: 2rem;
          cursor: pointer;
          img {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 50%;
            height: 50%;
          }
        }
        .desc {
          display: flex;
          justify-content: center;
          flex-flow: column nowrap;
          cursor: pointer;

          span:first-of-type {
            font-size: 1.8rem;
            color: #424242;
            margin-bottom: 10px;
          }

          span:last-of-type {
            font-size: 1.3rem;
            color: #a8a8a8;
          }
        }
      }
    }
    .bottom-section {
      padding: 2rem 0;
      width: 100%;
      display: flex;
      justify-content: space-around;
      position: relative;
      &::after {
        position: absolute;
        top: 1rem;
        right: 50%;
        content: '';
        transform: translate(-50%, 1rem);
        height: 3rem;
        width: 2px;
        background: #efefef;
      }
      .entry {
        display: flex;
        flex-flow: column;

        span:first-of-type {
          font-size: 1.6rem;
          color: #424242;
          margin-bottom: 10px;
        }

        span:last-of-type {
          font-size: 1.4rem;
          color: #a8a8a8;
        }
      }
    }
  }
  .right {
    font-size: 1.6rem;
    flex: 2;
    height: 100%;
    // justify-content: space-between;
    display: flex;
    flex-flow: column;
    &-section {
      height: 50%;
      padding: 0rem 1.6rem 0rem;
      // padding: 0.5rem 2.6rem 1rem;

      display: flex;
      flex-flow: column;
      background: #fff;
      justify-content: space-evenly;
      &:first-of-type {
        margin-bottom: 10px;
      }
      .right-title {
        margin-top: 1.4rem;
      }
      .right-areas {
        flex: 1;
        display: flex;
        align-items: center;
        // padding: 0rem 0 1rem;
        .area {
          flex: 1;
          display: flex;
          justify-content: center;
          .img {
            position: relative;
            width: 6rem;
            height: 6rem;
            border-radius: 50%;
            margin-right: 2rem;
            cursor: pointer;
            img {
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              width: 50%;
              height: 50%;
            }
          }
          .desc {
            display: flex;
            flex: 0 1 170px;
            justify-content: center;
            flex-flow: column nowrap;
            cursor: pointer;

            span:first-of-type {
              font-size: 1.8rem;
              color: #424242;
              margin-bottom: 10px;
            }

            span:last-of-type {
              font-size: 1.3rem;
              color: #a8a8a8;
            }
          }
        }
      }
    }
  }
}
.sections {
  display: flex;
  .section {
    // padding: 0px 10px;
    height: 100%;
    &-title {
      color: #3d3d3d;
      font-size: 15px;
      padding: 15px 3px 10px 20px;
      display: inline-block;
    }
    &-areas {
      display: flex;
      flex-flow: row;
      width: 100%;
      justify-content: space-evenly;
      align-items: center;
      height: 96px;
      .area {
        display: flex;
        justify-content: center;
        margin: 0 auto;
        padding: 10px auto 30px;
        .img {
          position: relative;
          width: 60px;
          height: 60px;
          border-radius: 50%;
          margin-right: 20px;
          cursor: pointer;
          img {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 50%;
            height: 50%;
          }
        }
        .desc {
          display: flex;
          justify-content: center;
          flex-flow: column nowrap;
          cursor: pointer;
          width: 180px;

          span:first-of-type {
            font-size: 18px;
            color: #424242;
            margin-bottom: 10px;
          }

          span:last-of-type {
            font-size: 13px;
            color: #a8a8a8;
          }
        }
      }
    }
    .sections {
      display: flex;
      align-items: center;
      flex-flow: column;
      &-item {
        width: 100%;
        margin-top: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        .img {
          position: relative;
          width: 60px;
          height: 60px;
          border-radius: 50%;
          margin-right: 20px;
          cursor: pointer;
          img {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 50%;
            height: 50%;
          }
        }
        .desc {
          display: flex;
          justify-content: center;
          flex-flow: column nowrap;
          cursor: pointer;
          width: 180px;

          span:first-of-type {
            font-size: 18px;
            color: #424242;
            margin-bottom: 10px;
          }

          span:last-of-type {
            font-size: 13px;
            color: #a8a8a8;
          }
        }
        .entrys {
          width: 100%;
          display: flex;
          justify-content: space-around;
          position: relative;
          &::after {
            position: absolute;
            top: 10px;
            right: 50%;
            content: '';
            transform: translate(-50%, 10px);
            height: 30px;
            width: 2px;
            background: #efefef;
          }
          .entry {
            display: flex;
            flex-flow: column;

            span:first-of-type {
              font-size: 20px;
              color: #424242;
              margin-bottom: 10px;
            }

            span:last-of-type {
              font-size: 15px;
              color: #a8a8a8;
            }
          }
        }
      }
    }

    &:first-of-type {
      flex: 1;
      margin-right: 10px;
    }
    &:last-of-type {
      flex: 1.5;
    }
  }
}
</style>
