<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" otherHeight="120"
            :showSummary='true'>
      <el-table-column label="品名" slot="name" prop="name" width="100" align="center">
        <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{scope.row.name}}</span></template>
      </el-table-column>
      <el-table-column label="途耗" slot="wayCost" prop="wayCost" align="center">
        <template slot-scope="scope">{{scope.row.wayCost}}%</template>
      </el-table-column>
      <el-table-column label="化验指标" slot="indicators" align="center">
        <el-table-column label="灰Ad" prop="ad" width="80" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.ad >= indicators.ad ? 'excessive':'']">{{ scope.row.ad}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.ad >= scope.row.limitAd ? 'excessive':'']">{{ scope.row.ad}}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="硫St,d" prop="std" width="80" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.std >= indicators.std ? 'excessive':'']">{{ scope.row.std}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.std >= scope.row.limitStd ? 'excessive':'']">{{ scope.row.std}}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="挥发Vdaf" prop="vadf" width="80" align="center">
          <template slot-scope="scope">
            <span v-show="scope.row.vadf > indicators.vadf" style="color: red">{{ scope.row.vadf}}</span>
            <span v-show="scope.row.vadf <= indicators.vadf">{{ scope.row.vadf}}</span>
          </template>
        </el-table-column>
        <el-table-column label="特征" prop="crc" width="80" align="center" />
        <el-table-column label="粘结" prop="g" width="80" align="center">
          <template slot-scope="scope">
            <span v-show="scope.row.g > indicators.g" style="color: red">{{ scope.row.g}}</span>
            <span v-show="scope.row.g <= indicators.g">{{ scope.row.g}}</span>
          </template>
        </el-table-column>
        <el-table-column label="Y值" prop="y" width="80" align="center" />
        <el-table-column label="X值" prop="x" width="80" align="center" />
        <el-table-column label="水分" prop="mt" width="80" align="center">
          <template slot-scope="scope">
            <span v-show="scope.row.mt > indicators.mt" style="color: red">{{ scope.row.mt}}</span>
            <span v-show="scope.row.mt <= indicators.mt">{{ scope.row.mt}}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="操作" slot="opt" min-width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)"
                  v-if="perms[`${curd}:update`]||false">编辑</el-tag>
          <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除</el-tag>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false" width="980px">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">

        <el-row>
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="合同" prop="contractId">
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 filterable style="width:100%" :disabled="dialogStatus==='update'" clearable
                                 placeholder="请选择合同" />
                  </el-form-item>
                </el-col>
                <el-col />
                <el-col />
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="entityForm.date" :clearable="false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="发货单位" prop="senderName">
                    <el-input v-model="entityForm.senderName" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="收货单位" prop="receiverName">
                    <el-input v-model="entityForm.receiverName" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="品名" prop="name">
                    <el-input v-model="entityForm.name" clearable disabled />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤种" prop="coalCategory">
                    <category-select :value.sync="entityForm.coalCategory" type="COAL_TYPE" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="本次化验车数" prop="truckCount">
                    <el-input v-model="entityForm.truckCount" :oninput="field.int" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="原发吨数" prop="sendWeight">
                    <el-input v-model="entityForm.sendWeight" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="实收吨数" prop="receiveWeight">
                    <el-input v-model="entityForm.receiveWeight" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="途耗" prop="wayCost">
                    <el-input v-model="entityForm.wayCost" disabled>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">化验指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="灰分Ad" prop="ad">
                    <el-input v-model="entityForm.ad" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="硫St,d" prop="std">
                    <el-input v-model="entityForm.std" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="挥发Vdaf" prop="vadf">
                    <el-input v-model="entityForm.vadf" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="特征" prop="crc">
                    <el-input v-model="entityForm.crc" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="粘结" prop="g">
                    <el-input v-model="entityForm.g" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="Y值" prop="y">
                    <el-input v-model="entityForm.y" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="X值" prop="x">
                    <el-input v-model="entityForm.x" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="水分" prop="mt">
                    <el-input v-model="entityForm.mt" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCustomerContract } from '@/api/quality'
import Mixins from '@/utils/mixins'
import { qualityFilterOption, listQuery } from '@/const'
import Model from '@/model/coalQuality/customerFeedback'
import { createMemoryField } from '@/utils/index'
export default {
  name: 'customerFeedback',
  data() {
    return {
      curd: 'customerFeedback',
      model: Model,
      filterOption: { ...qualityFilterOption },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        name: { required: true, message: '请输入品名', trigger: 'blur' },
        senderName: { required: true, message: '请输入发货单位', trigger: 'blur' },
        receiverName: { required: true, message: '请输入收货单位', trigger: 'blur' },
        coalCategory: { required: true, message: '请选择煤种', trigger: 'change' },
        truckCount: { required: true, message: '请输入车数', trigger: 'blur' },
        contractId: { required: true, message: '请选择合同', trigger: 'blur' }
      },
      entityForm: { ...Model.model },
      actions: [],
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      contractActive: [],
      contractParams: { query: '' },
      contract: {
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      indicators: { mt: Infinity, ad: Infinity, std: Infinity, vadf: Infinity, g: Infinity },
      memoryEntity: { fields: {}, triggered: false },
      filters: {},
      fromDashboard: false
    }
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_LIKES_date: to.params.date }
        vm.fromDashboard = true
      })
    } else {
      next()
    }
  },
  created() {
    this.getContract()
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'contractId',
        'contractCode',
        'date',
        'senderName',
        'receiverName',
        'name',
        'coalCategory',
        'truckCount',
        'sendWeight',
        'receiveWeight',
        'wayCost'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
  },
  mixins: [Mixins],
  watch: {
    'entityForm.date'(date) {
      if (this.dialogStatus === 'create' && this.entityForm.contractId) {
        this.getBookkeeping(this.entityForm.contractId, date)
      }
    },
    // 在打开更新时watch监听然后通过过滤筛选触发contractEntityFormActive灌入显示数据
    'entityForm.contractId'(id) {
      if (this.dialogStatus === 'create' && this.entityForm.date) {
        this.getBookkeeping(id, this.entityForm.date)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.name = form.productName
      this.entityForm.contractCode = form.customerName
      this.entityForm.contractId = form.customerId
      this.entityForm.senderName = form.firstParty
      this.entityForm.receiverName = form.secondParty
      this.entityForm.coalCategory = form.coalType
    },
    'entityForm.sendWeight'(v) {
      this.computeWayCostAcc()
    },
    'entityForm.receiveWeight'(v) {
      this.computeWayCostAcc()
    }
  },
  // 可写原方法覆盖mixins方法
  methods: {
    // 计算涂耗累计Acc
    computeWayCostAcc() {
      let { receiveWeight, sendWeight } = this.entityForm
      receiveWeight = receiveWeight * 1
      sendWeight = sendWeight * 1
      let wayCost = (((sendWeight - receiveWeight) / sendWeight) * 100).toFixed(2)
      if (!Number.isFinite(wayCost * 1) || Number.isNaN(wayCost * 1)) {
        this.entityForm.wayCost = 0
      } else {
        this.entityForm.wayCost = wayCost
      }
    },
    async getBookkeeping(id, date) {
      if (!id) {
        this.entityForm.sendWeight = 0
        this.entityForm.receiveWeight = 0
        return
      }
      const { sendWeight, receiveWeight } = await this.model.bookkeeping(id, date)
      this.entityForm.sendWeight = sendWeight
      this.entityForm.receiveWeight = receiveWeight
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.contractEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    // @重写方法-添加了合同搜索字段
    handleFilter(filters) {
      this.filters = { ...filters }
      this.$refs[this.curd].searchChange({ ...this.listQuery, ...filters, filter_EQS_contractId: this.contractParams.query })
    },
    // @重写方法-添加了clearContract方法
    handleFilterReset() {
      this.clearContract()
      this.$refs[this.curd].searchChange()
    },
    /**
     * 初始化数据选择下拉数据
     * contract用于指标合同下拉选取label显示name,value为id
     * contractEntityForm用于新增编辑下拉label显示code,value为id
     */
    async getContract() {
      const { data } = await getCustomerContract()
      this.contract.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { customerName: 'name', customerId: 'id' }
      })
      this.contractEntity.options = this.formatContract({ data, key: { customerName: 'displayName', customerId: 'id' } })
    },
    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },

    clearContract() {
      this.listQuery = { ...listQuery }
      this.fromDashboard = false
      this.filters = {}
      this.contractActive = []
      this.contractParams.query = ''
      this.indicators = { mt: Infinity, ad: Infinity, std: Infinity, vadf: Infinity, g: Infinity }
      return false
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     * @target {contract|contractEntityForm}<String>
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    /**
     * @切换时更行指标并设置搜索关键字query
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    handleContractChange(value) {
      if (!value.length) {
        this.clearContract()
        this.$refs[this.curd].searchChange()
        return
      }
      const item = this.filterContractItem(value, 'contract')
      const { cleanMt, cleanAd, cleanStd, cleanVdaf, procG } = item
      this.indicators = { mt: cleanMt, ad: cleanAd, std: cleanStd, vadf: cleanVdaf, g: procG }
      this.contractParams.query = item.id
      this.$refs[this.curd].searchChange({
        ...this.listQuery,
        filters: this.filters,
        filter_EQS_contractId: this.contractParams.query
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
// .opt-btnV {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}
::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}
::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}
::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  color: #fff;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 4) {
  border-left: none;
  border-right: none;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 3) {
  border-left: none;
  border-right: none;
}
</style>
