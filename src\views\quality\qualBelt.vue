<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <section class="panel">
      <div class="panel-contract">
        <el-cascader v-model="contractActive" v-bind="contract" @change="handleContractChange" filterable />
      </div>
      <div class="panel-indicators">
        <div class="panel-title">合同指标</div>
        <div class="panel-desc" v-show="indicators.mt!==Infinity">
          <span>Mt≤{{indicators.mt}}%,</span>
          <span>Adt≤{{indicators.ad}}%,</span>
          <span>St,d≤{{indicators.std}}%,</span>
          <span>Vdaf:24{{indicators.vdaf}}%</span>
          <span>g≤{{indicators.g}}</span>
        </div>
      </div>
    </section>
    <s-curd :ref="curd" :name="curd" :model="model" :otherHeight="otherHeight" :actions="actions" :list-query="listQuery"
            :showSummary='true'>
      <el-table-column label="品名" slot="name" prop="name" width="100" fixed="left" align='center'>
        <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{scope.row.name}}</span></template>
      </el-table-column>
      <el-table-column label="水分%" slot="moisture" align="center">
        <el-table-column prop="mt" label="Mt" align="center">
          <template slot-scope="scope">
            <span v-show="scope.row.mt > indicators.mt" style="color: red">{{ scope.row.mt}}</span>
            <span v-show="scope.row.mt <= indicators.mt">{{ scope.row.mt}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="mad" label="Mad" align="center" />
      </el-table-column>
      <el-table-column label="灰分%" slot="ash" align="center">
        <el-table-column prop="aad" label="Aad" align="center" />
        <el-table-column prop="ad" label="Ad" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.ad >= indicators.ad ? 'excessive':'']">{{ scope.row.ad}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.ad >= scope.row.limitAd ? 'excessive':'']">{{ scope.row.ad}}</span>
            </template>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="硫分%" slot="sulfur" align="center">
        <el-table-column prop="stad" label="St.ad" align="center" />
        <el-table-column prop="std" label="St.d" align="center">
          <template slot-scope="scope">
            <span v-show="scope.row.std > indicators.std" style="color: red">{{ scope.row.std}}</span>
            <span v-show="scope.row.std <= indicators.std">{{ scope.row.std}}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="挥发分%" slot="volatilization" align="center">
        <el-table-column prop="vad" label="Vad" align="center" />
        <el-table-column prop="vdaf" label="Vdaf" align="center">
          <template slot-scope="scope">
            <span v-show="scope.row.vdaf > indicators.vdaf" style="color: red">{{ scope.row.vdaf}}</span>
            <span v-show="scope.row.vdaf <= indicators.vdaf">{{ scope.row.vdaf}}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="特征" slot="crcSlot" align="center">
        <el-table-column prop="crc" label="CRC" width="80" align="center" />
      </el-table-column>
      <el-table-column label="粘结指数" slot="gSlot" align="center">
        <el-table-column prop="g" label="G" width="80" align="center">
          <template slot-scope="scope">
            <span v-show="scope.row.g > indicators.g" style="color: red">{{ scope.row.g}}</span>
            <span v-show="scope.row.g <= indicators.g">{{ scope.row.g}}</span>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="胶质层" slot="xySlot" align="center">
        <el-table-column prop="x" label="X" align="center" />
        <el-table-column prop="y" label="Y" align="center" />
      </el-table-column>

      <el-table-column label="操作" slot="opt" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)"
                  v-if="perms[`${curd}:update`]||false">编辑</el-tag>
          <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除</el-tag>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false" width="980px">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基础信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="entityForm.date" :clearable="false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="品名" prop="name">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="采样时间" prop="time">
                    <el-time-select :picker-options="{ start: '00:00',step: '00:10', end: '23:30' }" v-model="entityForm.time"
                                    value-format="HH:mm:ss" :clearable="false" placeholder="选择时间点" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同编号" prop="contractId">
                    <el-cascader style="width: 100%;" v-model=" contractEntityFormActive" :options="contractEntityForm.options"
                                 filterable :props="contract.props" clearable placeholder="请选择合同" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div class="form-title">化验指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="Mt" prop="mt">
                    <el-input v-model="entityForm.mt" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="Mad" prop="mad">
                    <el-input v-model="entityForm.mad" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="Aad" prop="aad">
                    <el-input v-model="entityForm.aad" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Ad" prop="ad">
                    <el-input v-model="entityForm.ad" :oninput="field.decimal" clearable disabled>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="St.ad" prop="stad">
                    <el-input v-model="entityForm.stad" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="St.d" prop="std">
                    <el-input v-model="entityForm.std" :oninput="field.decimal" clearable disabled>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="Vad" prop="vad">
                    <el-input v-model="entityForm.vad" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="Vdaf" prop="vdaf">
                    <el-input v-model="entityForm.vdaf" :oninput="field.decimal" clearable disabled>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="CRC" prop="crc">
                    <el-input v-model="entityForm.crc" clearable :oninput="field.int" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="G" prop="g">
                    <el-input v-model="entityForm.g" clearable :oninput="field.decimal" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="X" prop="x">
                    <el-input v-model="entityForm.x" clearable :oninput="field.decimal">
                      <template slot="append">mm</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Y" prop="y">
                    <el-input v-model="entityForm.y" clearable :oninput="field.decimal">
                      <template slot="append">mm</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCustomerContract } from '@/api/quality'
import Mixins from '@/utils/mixins'
import { qualityFilterOption, listQuery } from '@/const'
import Model from '@/model/coalQuality/qualBelt'
import productModel from '@/model/product/productList'
import { createMemoryField } from '@/utils/index'
export default {
  name: 'qualBelt',
  data() {
    return {
      curd: 'qualBelt',
      entityForm: { ...Model.model },
      model: Model,
      filterOption: { ...qualityFilterOption },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        name: { required: true, message: '请选择品名', trigger: 'blur' }
      },
      contractActive: [],
      contractEntityFormActive: [],
      contractEntityForm: {
        options: []
      },
      contractParams: { query: '' },
      contract: {
        props: {
          label: 'customerName',
          value: 'customerId',
          children: 'contractSellList'
        },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      indicators: { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity },
      nameEntity: { options: [], active: '' },
      memoryEntity: { fields: {}, triggered: false },
      filters: {},
      fromDashboard: false
    }
  },
  computed: {
    otherHeight() {
      return this.perms[`${this.curd}:export`] ? '227' : '180'
    }
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_LIKES_date: to.params.date }
        vm.fromDashboard = true
      })
    } else {
      next()
    }
  },
  mixins: [Mixins],
  created() {
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'time',
        'customerName',
        'contractName',
        'contractCode',
        'contractId',
        'receiverName',
        'productName',
        'productCode',
        'productId',
        'name'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getContract()
    this.getName()
    this.permsAction(this.curd)
  },
  watch: {
    'entityForm.aad'(v) {
      this.computerIndicators('ad')
      this.computerIndicators('vdaf')
    },
    'entityForm.mad'(v) {
      this.computerIndicators('ad')
      this.computerIndicators('std')
      this.computerIndicators('vdaf')
    },
    'entityForm.stad'(v) {
      this.computerIndicators('std')
    },
    'entityForm.vad'(v) {
      this.computerIndicators('vdaf')
    },
    'entityForm.name'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.entityForm.productName = name
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
      }
    },
    // 在打开更新时watch监听然后通过过滤筛选触发contractEntityFormActive灌入显示数据
    'entityForm.contractId'(id) {
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntityForm')
        this.contractEntityFormActive = value
      }
    },
    contractEntityFormActive(value) {
      if (!value || !Array.isArray(value)) return
      const { customerName: contractCode, customerId, name: customerName } = this.filterContractItem(value, 'contractEntityForm')
      this.entityForm.contractCode = contractCode
      this.entityForm.contractId = customerId
      this.entityForm.customerName = customerName
    }
  },
  // 可写原方法覆盖mixins方法
  methods: {
    computerIndicators(name) {
      let { mad, aad, stad, vad } = this.entityForm
      switch (name) {
        case 'ad':
          if (!mad || !aad) {
            this.entityForm.ad = ''
          } else {
            this.entityForm.ad = (aad / (1 - mad / 100)).toFixed(2)
          }
          break
        case 'std':
          if (!mad || !stad) {
            this.entityForm.std = ''
          } else {
            this.entityForm.std = (stad / (1 - mad / 100)).toFixed(2)
          }
          break
        case 'vdaf':
          if (!mad || !vad || !aad) {
            this.entityForm.vdaf = ''
          } else {
            this.entityForm.vdaf = ((vad / (100 - aad - mad)) * 100).toFixed(2)
          }
          break
      }
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.contractEntityFormActive = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },

    handleNameEntity(val) {
      this.entityForm.name = val
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
        })
      } catch (error) {}
    },

    // @重写方法-添加了合同搜索字段
    handleFilter(filters) {
      this.filters = { ...filters }
      this.$refs[this.curd].searchChange({ ...this.listQuery, ...this.filters, filter_EQS_contractId: this.contractParams.query })
    },
    // @重写方法-添加了clearContract方法
    handleFilterReset() {
      this.clearContract()
      this.$refs[this.curd].searchChange()
    },

    /**
     * 初始化数据选择下拉数据
     * contract用于指标合同下拉选取label显示name,value为id
     * contractEntityForm用于新增编辑下拉label显示code,value为id
     */
    async getContract() {
      const { data } = await getCustomerContract()
      this.contract.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { customerName: 'name', customerId: 'id' }
      })
      this.contractEntityForm.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { customerName: 'code', customerId: 'id' }
      })
    },
    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },

    clearContract() {
      this.listQuery = { ...listQuery }
      this.fromDashboard = false
      this.filters = {}
      this.contractActive = []
      this.contractParams.query = ''
      this.indicators = { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity }
      return false
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     * @target {contract|contractEntityForm}<String>
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        // console.log(this[target].options, target)
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    /**
     * @切换时更行指标并设置搜索关键字query
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    handleContractChange(value) {
      if (!value.length) {
        this.clearContract()
        this.$refs[this.curd].searchChange()
        return
      }
      const item = this.filterContractItem(value, 'contract')
      const { cleanMt, cleanAd, cleanStd, cleanVdaf, procG } = item
      this.indicators = { mt: cleanMt, ad: cleanAd, std: cleanStd, vdaf: cleanVdaf, g: procG }
      this.contractParams.query = item.id
      this.$refs[this.curd].searchChange({ ...this.listQuery, ...this.filters, filter_EQS_contractId: this.contractParams.query })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}
::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}
::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  color: #fff;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 4) {
  border-left: none;
  border-right: none;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 3) {
  border-left: none;
  border-right: none;
}
</style>
