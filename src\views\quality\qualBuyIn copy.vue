<template>
  <div class="app-container">
    <!-- :showImport="perms[`${curd}:addimport`]||false" -->
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <!-- <section class="panel">
      <div class="panel-contract">
        <el-cascader v-model="contractActive" v-bind="contract" @change="handleContractChange" />
      </div>
      <div class="panel-indicators">
        <div class="panel-title">合同指标</div>
        <div class="panel-desc" v-show="indicators.mt!==Infinity">
          <span>Mt≤{{indicators.mt}}%,</span>
          <span>Adt≤{{indicators.ad}}%,</span>
          <span>St,d≤{{indicators.std}}%,</span>
          <span>Vdaf:24{{indicators.vdaf}}%</span>
          <span>g≤{{indicators.g}}</span>
        </div>
      </div>
    </section> -->
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" otherHeight="160"
            :changeactions="changeactions" :showSummary='true' @selectItem="selectItem">
      <el-table-column label="品名" slot="productName" prop="productName" width="130" align="center">
        <!-- @click="handleWatchForm(scope.row)" -->
        <template slot-scope="scope"><span class="name"
                @click="handleUpdate(scope.row,'watch')">{{scope.row.productName}}</span></template>
      </el-table-column>

      <el-table-column label="煤岩报告附件" slot="attachment" prop="attachment" width="100" align="center">
        <template slot-scope="scope">
          <div class="attachmentV" style="margin: 0 auto;">
            <span @click="handlePreviewAttachment(scope.row)" style="cursor: pointer;">查看附件</span>
            <!-- <img class="attachment-img" src="@/assets/attachment.png" @click="handlePreviewAttachment(scope.row)"> -->
          </div>
        </template>
      </el-table-column>

      <el-table-column label="CSR报告附件" slot="attachmentCsrList" prop="attachmentCsrList" width="100" align="center">
        <template slot-scope="scope">
          <div class="attachmentV" style="margin: 0 auto;">
            <span @click="handlePreviewAttachmentV(scope.row)" style="cursor: pointer;">查看附件</span>
            <!-- <img class="attachment-img" src="@/assets/attachment.png" @click="handlePreviewAttachmentV(scope.row)"> -->
          </div>
        </template>
      </el-table-column>

      <!-- <el-table-column label="精煤指标" slot="indicators" align="center">
        <el-table-column label="硫St,d" prop="std" min-min-width="80" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.std >= indicators.std ? 'excessive':'']">{{ scope.row.std}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.std >= scope.row.limitStd ? 'excessive':'']">{{ scope.row.std}}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="灰Ad" prop="ad" min-min-width="80" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.ad >= indicators.ad ? 'excessive':'']">{{ scope.row.ad}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.ad >= scope.row.limitAd ? 'excessive':'']">{{ scope.row.ad}}</span>
            </template>
          </template>
        </el-table-column>

        <el-table-column label="挥发Vdaf" prop="vdaf" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.vdaf > indicators.vdaf ? 'excessive':'']">{{ scope.row.vdaf}}</span>
          </template>
        </el-table-column>
        <el-table-column label="特征CRC" prop="crc" min-width="80" align="center" />
        <el-table-column label="回收" prop="recovery" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.recovery > indicators.recovery ? 'excessive':'']">{{ scope.row.recovery}}</span>
          </template>
        </el-table-column>

        <el-table-column label="含中" prop="midCoal" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.midCoal > indicators.midCoal ? 'excessive':'']">{{ scope.row.midCoal}}</span>
          </template>
        </el-table-column>

        <el-table-column label="水分" prop="mt" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.mt > indicators.mt ? 'excessive':'']">{{ scope.row.mt}}</span>
          </template>
        </el-table-column>
        <el-table-column label="G值" prop="g" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.g > indicators.g ? 'excessive':'']">{{ scope.row.g}}</span>
          </template>
        </el-table-column>
        <el-table-column label="Y值" prop="y" min-width="80" align="center" />
      </el-table-column> -->

      <!-- 原煤指标灰Ad -->
      <!-- <el-table-column label="原煤指标" slot="indicators" align="center">
        <el-table-column label="灰Ad" prop="rawAd" min-min-width="80" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.rawAd >= indicators.rawAd ? 'excessive':'']">{{ scope.row.rawAd}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.rawAd >= scope.row.limitAd ? 'excessive':'']">{{ scope.row.rawAd}}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="硫St,d" prop="rawStd" min-min-width="80" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.rawStd >= indicators.rawStd ? 'excessive':'']">{{ scope.row.rawStd}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.rawStd >= scope.row.limitStd ? 'excessive':'']">{{ scope.row.rawStd}}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="水分" prop="rawMt" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.rawMt > indicators.rawMt ? 'excessive':'']">{{ scope.row.rawMt}}</span>
          </template>
        </el-table-column>
      </el-table-column> -->

      <!-- <el-table-column label="中煤指标" slot="indicators" align="center">
        <el-table-column label="灰Ad" prop="midAd" min-min-width="80" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.midAd >= indicators.midAd ? 'excessive':'']">{{ scope.row.midAd}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.midAd >= scope.row.limitAd ? 'excessive':'']">{{ scope.row.midAd}}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="硫St,d" prop="midStd" min-min-width="80" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.midStd >= indicators.midStd ? 'excessive':'']">{{ scope.row.midStd}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.midStd >= scope.row.limitStd ? 'excessive':'']">{{ scope.row.midStd}}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="挥发Vdaf" prop="midVdaf" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.midVdaf > indicators.midVdaf ? 'excessive':'']">{{ scope.row.midVdaf}}</span>
          </template>
        </el-table-column>
        <el-table-column label="CRC" prop="midCrc" min-width="80" align="center" />
        <el-table-column label="粘结" prop="midG" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.midG > indicators.midG ? 'excessive':'']">{{ scope.row.midG}}</span>
          </template>
        </el-table-column>
        <el-table-column label="水分" prop="midMt" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.midMt > indicators.midMt ? 'excessive':'']">{{ scope.row.midMt}}</span>
          </template>
        </el-table-column>
        <el-table-column label="回收" prop="midRecovery" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.midRecovery > indicators.midRecovery ? 'excessive':'']">{{ scope.row.midRecovery}}</span>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="末煤指标" slot="indicators" align="center">
        <el-table-column label="灰Ad" prop="foamAd" min-min-width="80" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.foamAd >= indicators.foamAd ? 'excessive':'']">{{ scope.row.foamAd}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.foamAd >= scope.row.limitAd ? 'excessive':'']">{{ scope.row.foamAd}}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="硫St,d" prop="foamStd" min-min-width="80" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.foamStd >= indicators.foamStd ? 'excessive':'']">{{ scope.row.foamStd}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.foamStd >= scope.row.limitStd ? 'excessive':'']">{{ scope.row.foamStd}}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="挥发Vdaf" prop="foamVdaf" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.foamVdaf > indicators.foamVdaf ? 'excessive':'']">{{ scope.row.foamVdaf}}</span>
          </template>
        </el-table-column>
        <el-table-column label="CRC" prop="foamCrc" min-width="80" align="center" />
        <el-table-column label="粘结" prop="foamG" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.foamG > indicators.foamG ? 'excessive':'']">{{ scope.row.foamG}}</span>
          </template>
        </el-table-column>
        <el-table-column label="水分" prop="foamMt" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.foamMt > indicators.foamMt ? 'excessive':'']">{{ scope.row.foamMt}}</span>
          </template>
        </el-table-column>
        <el-table-column label="回收" prop="foamRecovery" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.foamRecovery > indicators.foamRecovery ? 'excessive':'']">{{ scope.row.foamRecovery}}</span>
          </template>
        </el-table-column>
      </el-table-column> -->

      <!-- <el-table-column label="矸石指标" slot="indicators" align="center">
        <el-table-column label="灰Ad" prop="ganAd" min-min-width="80" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.ganAd >= indicators.ganAd ? 'excessive':'']">{{ scope.row.ganAd}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.ganAd >= scope.row.limitAd ? 'excessive':'']">{{ scope.row.ganAd}}</span>
            </template>
          </template>
        </el-table-column> -->

      <!-- <el-table-column label="硫St,d" prop="ganStd" min-min-width="80" align="center">
          <template slot-scope="scope">
            <template v-if="!fromDashboard">
              <span :class="[scope.row.ganStd >= indicators.ganStd ? 'excessive':'']">{{ scope.row.ganStd}}</span>
            </template>
            <template v-else>
              <span :class="[scope.row.ganStd >= scope.row.limitStd ? 'excessive':'']">{{ scope.row.ganStd}}</span>
            </template>
          </template>
        </el-table-column> -->

      <!-- <el-table-column label="挥发Vdaf" prop="ganVdaf" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.ganVdaf > indicators.ganVdaf ? 'excessive':'']">{{ scope.row.ganVdaf}}</span>
          </template>
        </el-table-column>
        <el-table-column label="CRC" prop="ganCrc" min-width="80" align="center" />
        <el-table-column label="粘结" prop="ganG" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.ganG > indicators.ganG ? 'excessive':'']">{{ scope.row.ganG}}</span>
          </template>
        </el-table-column> -->
      <!--矸石指标粘结 -->
      <!-- <el-table-column label="水分" prop="ganMt" min-width="80" align="center">
          <template slot-scope="scope">
            <span :class="[scope.row.ganMt > indicators.ganMt ? 'excessive':'']">{{ scope.row.ganMt}}</span>
          </template>
        </el-table-column> -->
      <!-- <el-table-column label="回收" prop="ganRecovery" min-width="80" align="center">
        <template slot-scope="scope">
          <span :class="[scope.row.ganRecovery > indicators.ganRecovery ? 'excessive':'']">{{ scope.row.ganRecovery}}</span>
        </template>
      </el-table-column> -->

      <el-table-column label="操作" slot="opt" width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#fa3131" @click="handleUpdate(scope.row,'watch')">查看</el-tag>
          <el-tag class="opt-btn" color="#FF9639" v-if="perms[`${curd}:update`]||false"
                  @click="handleUpdate(scope.row,'update')">编辑</el-tag>
          <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除</el-tag>
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="entityForm.date" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="合同" prop="contractId">
                    <el-cascader v-model="contractEntity.active" :options="contractEntity.options" :props="contractEntity.props"
                                 filterable style="width:100%" clearable placeholder="请选择合同" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity"
                                 disabled="disabled" />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col v-if="entityForm.ContractIndicators">
                  <el-form-item label="合同指标" prop="">
                    <el-input v-model="entityForm.ContractIndicators" clearable />
                  </el-form-item>
                </el-col>
                <el-col v-else></el-col>
                <el-col></el-col>
              </el-row>

              <!-- :oninput="field.int" -->
              <!-- <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="车牌号" prop="plateNumber">
                    <el-input v-model="entityForm.plateNumber" clearable />
                  </el-form-item>
                </el-col>
                <el-col> </el-col>
                <el-col> </el-col>
                <el-col> </el-col>
              </el-row> -->
            </div>
          </el-col>

          <el-col>
            <!-- <div class="form-title">指标</div> -->
            <div class="form-titlV form-warp">
              <span style="position: relative;top:10px">指标</span>
              <el-button type="export-all" @click="handleAdd">插入一行</el-button>
            </div>
            <div class="form-layout" style="margin-bottom:20px">
              <el-row type="flex">
                <el-col>
                  <!-- <el-form-item> -->
                  <!-- <div style="width:100%;height:250px;overflow-y:auto;"> -->
                  <hot-table ref="hotV" class="hot-table" height="250" width="100%" :settings="settingsV"></hot-table>
                  <!-- </div> -->
                  <!-- </el-form-item> -->
                </el-col>
              </el-row>
            </div>
          </el-col>

          <!-- <el-col>
            <div class="form-title">精煤指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="硫St,d" prop="std">
                    <el-input v-model="entityForm.std" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="灰Ad" prop="ad">
                    <el-input v-model="entityForm.ad" oninput="value=value.replace(/[^\d.]/g,'')" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="挥发Vdaf" prop="vdaf">
                    <el-input v-model="entityForm.vdaf" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="特征CRC" prop="crc">
                    <el-input v-model="entityForm.crc" :oninput="field.int" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="回收" prop="recovery">
                    <el-input v-model="entityForm.recovery" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="含中" prop="midCoal">
                    <el-input v-model="entityForm.midCoal" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="水分Mt" prop="mt">
                    <el-input v-model="entityForm.mt" :oninput="field.decimal" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="G值" prop="g">
                    <el-input v-model="entityForm.g" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="Y值" prop="y">
                    <el-input v-model="entityForm.y" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col> </el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">原煤指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="全灰Ad" prop="rawAd">
                    <el-input v-model="entityForm.rawAd" oninput="value=value.replace(/[^\d.]/g,'')" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="全硫St,d" prop="rawStd">
                    <el-input v-model="entityForm.rawStd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="全水Mt" prop="rawMt">
                    <el-input v-model="entityForm.rawMt" :oninput="field.decimal" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">中煤指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="硫St,d" prop="midStd">
                    <el-input v-model="entityForm.midStd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="灰Ad" prop="midAd">
                    <el-input v-model="entityForm.midAd" oninput="value=value.replace(/[^\d.]/g,'')" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="挥发Vdaf" prop="midVdaf">
                    <el-input v-model="entityForm.midVdaf" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="特征CRC" prop="midCrc">
                    <el-input v-model="entityForm.midCrc" :oninput="field.int" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="G值" prop="midG">
                    <el-input v-model="entityForm.midG" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="水分Mt" prop="midMt">
                    <el-input v-model="entityForm.midMt" :oninput="field.decimal" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="回收" prop="midRecovery">
                    <el-input v-model="entityForm.midRecovery" :oninput="field.decimal" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">末煤指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="硫St,d" prop="foamStd">
                    <el-input v-model="entityForm.foamStd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="灰Ad" prop="foamAd">
                    <el-input v-model="entityForm.foamAd" oninput="value=value.replace(/[^\d.]/g,'')" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="挥发Vdaf" prop="foamVdaf">
                    <el-input v-model="entityForm.foamVdaf" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="特征CRC" prop="foamCrc">
                    <el-input v-model="entityForm.foamCrc" :oninput="field.int" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="G值" prop="foamG">
                    <el-input v-model="entityForm.foamG" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="水分Mt" prop="foamMt">
                    <el-input v-model="entityForm.foamMt" :oninput="field.decimal" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="回收" prop="foamRecovery">
                    <el-input v-model="entityForm.foamRecovery" :oninput="field.decimal" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">矸石指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="灰Ad" prop="ganAd">
                    <el-input v-model="entityForm.ganAd" oninput="value=value.replace(/[^\d.]/g,'')" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="回收" prop="ganRecovery">
                    <el-input v-model="entityForm.ganRecovery" :oninput="field.decimal" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col> -->

          <el-col>
            <div class="form-title">附件信息</div>
            <div class="form-layout">
              <el-collapse v-model="collapse">
                <el-collapse-item title="煤岩报告上传" name="1">
                  <div class="upload">
                    <div class="upload-list">
                      <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                        <i class="el-icon-close icon" @click="handleRemoveUpload(index)"></i>
                        <el-link class="up-load-warp-link" :underline=" false" :href="item.uri">{{item.display}}</el-link>
                      </div>
                      <upload-attachment ref="upload" class="upload-attachment" :uploadData="uploadData" source="coalSample"
                                         :size="size" :limitNum="limitNum" accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg"
                                         @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                         listType="text" />
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">附件信息</div>
            <div class="form-layout">
              <el-collapse v-model="collapse">
                <el-collapse-item title="CSR报告上传" name="1">
                  <div class="upload">
                    <div class="upload-list">
                      <div class="up-load-warp" v-for="(item,index) in uploadListV" :key="index">
                        <i class="el-icon-close icon" @click="handleRemoveUploadV(index)"></i>
                        <el-link class="up-load-warp-link" :underline=" false" :href="item.uri">{{item.display}}</el-link>
                      </div>
                      <upload-attachment ref="uploadV" class="upload-attachment" :uploadData="uploadDataV" source="coalSample"
                                         :size="sizeV" :limitNum="limitNumV" accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg"
                                         @successNotify="handleUploadSuccessV" @deleteNotify="handleUploadDeleteV"
                                         listType="text" />
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </el-col>

        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisibleV"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisibleV">
        <!-- type="b_contract_party_type"  type='productName'-->
        <laboratoryImport ref="excelref" optName='create' v-if="dialogFormVisibleV" type="productName"
                          :contractlist="contractlistV" :exportExcelDialogVisible.sync="dialogFormVisibleV"
                          @setSubmittext='setSubmittext' :nestedHeaders="nestedHeaders" :traitSettings="tableOption"
                          :tableOption="model.tableOption" :entityForm="entityForm" Buttontext="save">
        </laboratoryImport>
      </el-form>

      <div style="float:right;margin-top:20px">
        <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" @click="okbtnV()">上传
        </el-button>
        <el-button style="border: 1px solid #2f79e8; color: #2f79e8;margin:0 30px 30px 0;" @click="closeExportExcelDialogV()">取消
        </el-button>
      </div>
      <!-- <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div> -->
    </el-dialog>

    <el-drawer title="煤岩报告附件列表" :visible.sync="drawer.visible" direction="rtl" :before-close="handleCloseDrawer">
      <template v-if="drawer.list.length">
        <div class="images-warp">
          <div class="img" v-for="(item,index) in drawer.list" :key="index">
            <el-image class="img" :title="item.display" :src="item.uri" fit="fill" :preview-src-list="drawer.preview" />
            <span class="img-title">{{item.display}}</span>
          </div>
        </div>
      </template>
      <template v-else>
        <el-empty :image="emptyImgPath" :image-size="200" description="暂无图片" />
      </template>
    </el-drawer>

    <el-drawer title="CSR报告附件列表" :visible.sync="drawerV.visible" direction="rtl" :before-close="handleCloseDrawer">
      <template v-if="drawerV.list.length">
        <div class="images-warp">
          <div class="img" v-for="(item,index) in drawerV.list" :key="index">
            <el-image class="img" :title="item.display" :src="item.uri" fit="fill" :preview-src-list="drawerV.preview" />
            <span class="img-title">{{item.display}}</span>
          </div>
        </div>
      </template>
      <template v-else>
        <el-empty :image="emptyImgPath" :image-size="200" description="暂无图片" />
      </template>
    </el-drawer>

  </div>
</template>

<script>
import productModel from '@/model/product/productList'
import { chooseContract, contractList, getContractBuy } from '@/api/quality'
import Mixins from '@/utils/mixins'
import { qualInOrOutFilterOption, listQuery } from '@/const'
import Model from '@/model/coalQuality/qualBuyIn'
import { createMemoryField } from '@/utils/index'
import contractBuyModel from '@/model/user/contractBuy'

import { HotTable, HotColumn } from '@handsontable/vue'
import { registerLanguageDictionary, zhCN } from 'handsontable/i18n'
import { registerAllModules } from 'handsontable/registry'
import 'handsontable/dist/handsontable.full.css'
registerAllModules()
registerLanguageDictionary(zhCN)

export default {
  name: 'qualBuyIn',
  mixins: [Mixins],
  data() {
    return {
      curd: 'qualBuyIn',
      model: Model,
      filterOption: { ...qualInOrOutFilterOption },
      dialogFormVisibleV: false,
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        // time: { required: true, message: '请选择采样时间', trigger: 'blur' },
        productName: { required: true, message: '请输入品名', trigger: 'blur' }
        // plateNumber: { required: true, message: '请输入车牌号', trigger: 'blur' },
      },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      contractParams: { query: '' },
      contractActive: [],
      entityForm: { ...Model.model },
      contract: {
        props: {
          label: 'supplierName',
          value: 'supplierId',
          children: 'contractBuyList'
        },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      indicators: { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity },
      memoryEntity: { fields: {}, triggered: false },
      filters: {},
      fromDashboard: false,
      supplierEntity: { value: '', options: [] },
      nameEntity: { options: [], active: '' },

      nestedHeaders: [
        [
          '日期',
          '采样时间',
          '车牌',
          '品名',
          '合同编号',
          {
            label: '精煤',
            colspan: 9
          },
          {
            label: '原煤',
            colspan: 3
          },
          {
            label: '中煤',
            colspan: 7
          },
          {
            label: '末煤',
            colspan: 7
          },
          {
            label: '矸石',
            colspan: 2
          }
        ],
        [
          '',
          '',
          '',
          '',
          '',
          '硫分St,d',
          '灰分Ad',
          '挥发份Vdaf',
          '特征CRC',
          '回收',
          '含中',
          '水分Mt',
          'G值',
          'Y值',
          '灰分Ad',
          '硫分St,d',
          '水分Mt',
          '硫分St,d',
          '灰分Ad',
          '挥发份Vdaf',
          '特征CRC',
          'G值',
          '水分Mt',
          '回收',
          '硫分St,d',
          '灰分Ad',
          '挥发份Vdaf',
          '特征CRC',
          'G值',
          '水分Mt',
          '回收',
          '灰分',
          '回收'
        ]
      ],
      contractlistV: [],
      tableOption: {
        showPage: true,
        columns: [
          {
            label: '日期',
            title: '日期',
            prop: 'date',
            type: 'date',
            width: 100,
            validator: /[\s\S]/,
            correctFormat: true,
            dateFormat: 'YYYY-MM-DD'
          },
          {
            label: '送样时间',
            prop: 'time',
            title: '送样时间',
            type: 'time',
            validator: /[\s\S]/,
            dateFormat: 'HH:mm:ss'
          },
          {
            label: '车牌',
            title: '车牌',
            prop: 'plateNumber',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            title: '品名',
            width: 100,
            prop: 'productName',
            type: 'autocomplete',
            visibleRows: 10,
            allowInvalid: true
          },
          {
            label: '合同编号',
            prop: 'contractCode',
            width: 200,
            title: '合同编号',
            type: 'dropdown',
            visibleRows: 10,
            allowInvalid: true
          },
          {
            label: '精煤指标硫St,d',
            title: '硫St,d',
            prop: 'std',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '精煤指标灰Ad',
            title: '灰Ad',
            prop: 'ad',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },

          {
            label: '精煤指标挥发Vdaf',
            title: '挥发Vdaf',
            prop: 'vdaf',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '精煤指标CRC',
            title: 'CRC',
            prop: 'crc',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '精煤指标回收',
            title: '回收',
            prop: 'recovery',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '精煤指标含中',
            title: '含中',
            prop: 'midCoal',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '精煤指标水分',
            title: '水分',
            prop: 'mt',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '精煤指标G值',
            title: 'G值',
            prop: 'g',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '精煤指标Y值',
            title: 'Y值',
            prop: 'y',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },

          {
            label: '原煤指标灰Ad',
            title: '灰Ad',
            prop: 'rawAd',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '原煤指标硫St,d',
            title: '硫St,d',
            prop: 'rawStd',
            align: 'center',
            isShow: true
          },
          {
            label: '原煤指标水分',
            title: '水分',
            prop: 'rawMt',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },

          {
            label: '中煤指标灰Ad',
            title: '灰Ad',
            prop: 'midAd',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '中煤指标硫St,d',
            title: '硫St,d',
            prop: 'midStd',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '中煤指标挥发Vdaf',
            title: '挥发Vdaf',
            prop: 'midVdaf',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '中煤指标CRC',
            title: 'CRC',
            prop: 'midCrc',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '中煤指标粘结',
            title: '粘结',
            prop: 'midG',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '中煤指标水分',
            title: '水分',
            prop: 'midMt',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '中煤指标回收',
            title: '回收',
            prop: 'midRecovery',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },

          {
            label: '末煤指标灰Ad',
            title: '灰Ad',
            prop: 'foamAd',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '末煤指标硫St,d',
            title: '硫St,d',
            prop: 'foamStd',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },

          {
            label: '末煤指标挥发Vdaf',
            title: '挥发Vdaf',
            prop: 'foamVdaf',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '末煤指标CRC',
            title: 'CRC',
            prop: 'foamCrc',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '末煤指标粘结',
            title: '粘结',
            prop: 'foamG',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '末煤指标水分',
            title: '水分',
            prop: 'foamMt',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '末煤指标回收',
            title: '回收',
            prop: 'foamRecovery',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '矸石指标灰Ad',
            title: '灰Ad',
            prop: 'ganAd',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '矸石指标回收',
            title: '回收',
            prop: 'ganRecovery',
            align: 'center',
            isShow: true,
            type: 'text',
            validator: /[\s\S]/
          }
        ]
      },
      uploadList: [], // 用于展示
      limitNum: 1000,
      collapse: '1',
      uploadData: { refId: '', refType: 'CoalSample' },
      selectList: [],
      optName: 'create',

      settingsV: {
        startRows: 1,
        data: [{}, {}, {}, {}, {}],
        columns: [
          { title: '采样时间', data: 'time', type: 'text', trimWhitespace: true, width: 65 },
          // {
          //   title: '采样时间',
          //   data: 'time',
          //   type: 'time',
          //   timeFormat: 'h:mm:ss',
          //   trimWhitespace: true,
          //   width: 65,
          //   correctFormat: true,
          // },
          {
            title: '硫分St,d',
            data: 'std',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '灰分Ad',
            data: 'ad',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '挥发分Vdaf',
            data: 'vdaf',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '特征CRC',
            data: 'crc',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0'
            }
          },
          {
            title: 'G值',
            data: 'g',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0'
            }
          },
          {
            title: '水分',
            data: 'mt',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.0'
            }
          },
          {
            title: 'Y值',
            data: 'y',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.0'
            }
          },
          {
            title: 'CRI',
            data: 'cri',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.0'
            }
          },
          {
            title: 'CSR',
            data: 'csr',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.0'
            }
          },
          {
            title: '反射率',
            data: 'macR0',
            type: 'numeric',
            trimWhitespace: true,
            width: 65
          },
          {
            title: '标准差',
            data: 'macS',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.000'
            }
          },
          { title: '车牌', data: 'plateNumber', type: 'text', trimWhitespace: true, width: 65 }
        ],
        readOnly: this.disabled,
        className: 'custom-classname',
        language: zhCN.languageCode,
        copyPaste: true,
        rowHeaders: false,
        width: 'auto',
        height: 'auto',
        stretchH: 'all',
        manualColumnResize: true,
        manualRowResize: true,
        licenseKey: 'non-commercial-and-evaluation',
        formulas: {
          sheetName: 'Sheet1'
        },
        contextMenu: {
          items: {
            row_above: { name: '向上插入一行' },
            row_below: { name: '向下插入一行' },
            remove_row: { name: '删除行' }
          }
        }
      },
      drawer: {
        visible: false,
        list: [],
        preview: []
      },
      drawerV: {
        visible: false,
        list: [],
        preview: []
      },
      emptyImgPath: require(`@/assets/empty_img.jpg`),
      uploadListV: [],
      limitNumV: 1000,
      uploadDataV: { refId: '', refType: 'CoalSample' }
    }
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_LIKES_date: to.params.date }
        vm.fromDashboard = true
      })
    } else {
      next()
    }
  },
  components: {
    HotTable,
    HotColumn
  },
  computed: {
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    },
    sizeV() {
      return this.uploadListV.length
    }
  },
  async created() {
    this.getContract()
    // this.getContractList()
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'contractId',
        'contractCode',
        'date',
        'senderName',
        'receiverName',
        'name',
        'productName',
        'coalCategory',
        'truckCount',
        'sendWeight',
        'supplierName',
        'supplierId',
        'receiveWeight',
        'wayCost'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getName()
    this.getContractDR()
    this.permschange(this.curd)
  },
  watch: {
    'entityForm.date'(date) {
      if (this.dialogStatus === 'create' && this.entityForm.contractId) {
        this.getBookkeeping(this.entityForm.contractId, date)
      }
    },
    'entityForm.contractId'(id) {
      if (this.dialogStatus === 'create' && this.entityForm.date) {
        this.getBookkeeping(id, this.entityForm.date)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.name = form.productName
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.supplierId
      this.nameEntity.active = form.productName
      this.entityForm.senderName = form.firstParty
      this.entityForm.receiverName = form.secondParty
      this.entityForm.coalCategory = form.coalType

      if (this.dialogStatus == 'watch') {
        let cleanMt = form.cleanMt ? form.cleanMt : 0
        let cleanAd = form.cleanAd ? form.cleanAd : 0
        let cleanStd = form.cleanAd ? form.cleanStd : 0
        let cleanVdaf = form.cleanVdaf ? form.cleanVdaf : 0
        let crc = form.crc ? form.crc : 0
        let procG = form.procG ? form.procG : 0
        let procY = form.procY ? form.procY : 0
        let procX = form.procX ? form.procX : 0
        let recovery = form.recovery ? form.recovery : 0
        let qualCsr = form.qualCsr ? form.qualCsr : 0
        let macR0 = form.macR0 ? form.macR0 : 0
        let macS = form.macS ? form.macS : 0

        this.entityForm.ContractIndicators =
          'Mt: ' +
          cleanMt +
          '，Ad: ' +
          cleanAd +
          '，Std: ' +
          cleanStd +
          '，Vdaf: ' +
          cleanVdaf +
          '，特征(crc): ' +
          crc +
          '，粘结(G): ' +
          procG +
          '，胶质层Y: ' +
          procY +
          '，X指标: ' +
          procX +
          '，回收率 ' +
          recovery +
          '，热强度(CSR): ' +
          qualCsr +
          '，岩相(R0): ' +
          macR0 +
          '，标准差S: ' +
          macS
      } else {
        this.entityForm.ContractIndicators = ''
      }

      // this.entityForm.productName = form.productName
      this.entityForm = { ...this.entityForm, productName: form.productName }
    },

    'entityForm.sendWeight'(v) {
      this.computeWayCostAcc()
    },
    'entityForm.receiveWeight'(v) {
      this.computeWayCostAcc()
    },
    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.entityForm.productName = name
        this.entityForm.name = name
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
      }
    }
    // 'supplierEntity.value'(id) {
    //   if (!id) return
    //   const item = this.supplierEntity.options.find((item) => item.id === id)
    //   if (item) {
    //     this.supplierEntity.active = item
    //     this.getProductListfnc(id)
    //   } else {
    //     this.supplierEntity.active = []
    //   }
    //   this.entityForm.supplierId = id
    //   this.entityForm.supplierName = item.name
    // },
  },
  methods: {
    handleAdd() {
      const form = {}
      this.settingsV.data.push(form)
    },
    async handleDel(row) {
      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await this.model.deleteId(row.id)
          if (res) {
            this.$message({ type: 'success', message: '删除成功!' })
            this.getList()
            this.getnumber()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },

    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []

      this.drawerV.visible = false
      this.drawerV.list = []
      this.drawerV.preview = []
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      this.drawer.list = res.data.attachmentList
      this.drawer.preview = res.data.attachmentList.map((item) => item.uri)
      this.drawer.visible = true
    },
    async handlePreviewAttachmentV(row) {
      const res = await this.model.getUploadList(row.id)
      this.drawerV.list = res.data.attachmentCsrList
      this.drawerV.preview = res.data.attachmentCsrList.map((item) => item.uri)
      this.drawerV.visible = true
    },

    async saveEntityForm(formName) {
      console.log(this.$refs.hotV.hotInstance.getData())
      let validateResV = await new Promise((resolve, reject) => {
        this.$refs.hotV.hotInstance.validateCells((res) => {
          resolve(res)
        })
      })
      if (!validateResV) {
        this.$message({ showClose: true, message: '数据格式不正确，请检查！', type: 'warning' })
        return false
      }
      let Things = this.$refs.hotV.hotInstance.getData()
      var newArr = []
      let keyData = ['time', 'std', 'ad', 'vdaf', 'crc', 'g', 'mt', 'y', 'cri', 'csr', 'macR0', 'macS', 'plateNumber']
      Things.forEach((item, index) => {
        if (item[0] != null) {
          let obj = {}
          item.forEach((s, i) => {
            obj[keyData[i]] = s
          })
          newArr.push(obj)
        }
      })
      this.entityForm = { ...this.entityForm, qualBuyInItemList: newArr }
      const valid = await this.$refs[formName].validate()
      if (valid) {
        this.entityFormLoading = true
        const res = await Model.save({ ...this.entityForm })
        this.entityFormLoading = false
        if (res) {
          this.getList()
          this.resetVariant()
          this.dialogFormVisible = false
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        }
      }
    },

    // 导入时获取的采购合同列表
    async getContractDR() {
      const { data } = await getContractBuy()
      let newarry = []
      for (var i = 0; i < data.records.length; i++) {
        newarry.push(data.records[i].code)
        // newarry.push(data.records[i].supplierName + '/' + data.records[i].code)
      }
      this.contractlistV = newarry
    },
    //批量删除,
    async BatchDelete(selectList) {
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      let newarry = []
      for (var i = 0; i < selectList.length; i++) {
        let obj = {}
        obj = selectList[i].id
        newarry.push(obj)
      }
      let parm = {
        idList: newarry
      }
      // console.log(parm)

      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // console.log('确认')
          this.deletefn(parm)
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },
    async deletefn(parm) {
      let { data } = await Model.savebatchChange({ ...parm })
      if (data) {
        this.$message({ type: 'success', message: '删除成功!' })
        this.getList()
      }
    },

    okbtnV() {
      this.$refs.excelref.okbtn()
    },
    closeExportExcelDialogV() {
      this.$refs.excelref.closeExportExcelDialog()
    },
    selectItem(list) {
      //批量选择的数据
      this.selectList = list
    },
    async handleUpdate(item, type) {
      // item.type = this.entityForm = { ...item }
      if (type == 'update') {
        this.dialogStatus = 'update'
      } else {
        this.dialogStatus = 'watch'
      }
      // 上传所需要的配置
      this.uploadData.refId = item.id
      // console.log(this.uploadData)
      this.settingsV.data = [{}]
      const { data } = await this.model.getUploadList(item.id)
      this.uploadList = data.attachmentList
      this.uploadListV = data.attachmentCsrList
      if (type == 'watch') {
        let obj = {
          time: '平均值',
          ad: data.ad,
          crc: data.crc,
          cri: data.cri,
          csr: data.csr,
          g: data.g,
          macR0: data.macR0,
          macS: data.macS,
          mt: data.mt,
          std: data.std,
          vdaf: data.vdaf,
          y: data.y
        }
        data.qualBuyInItemList.push(obj)
      }
      this.entityForm = { ...data }

      // console.log(data)
      // let obj2 = {
      //   time: data.time,
      //   std: data.std,
      //   ad: data.ad,
      //   vdaf: data.vdaf,
      //   crc: data.crc,
      //   g: data.g,
      //   mt: data.mt,
      //   y: data.y,
      //   cri: data.cri,
      //   csr: data.csr,
      //   macR0: data.macR0,
      //   macS: data.macS,
      //   plateNumber: data.plateNumber,
      // }
      // var arr = []
      // arr.push(obj2)
      // this.settingsV.data = arr
      this.settingsV.data = data.qualBuyInItemList
      // console.log(this.settingsV.data)
      this.entityForm = { ...data }
      this.dialogFormVisible = true
    },

    //煤岩报告上传
    async handleRemoveUpload(index) {
      const { id } = this.uploadList[index]
      this.$refs.upload.attachmentDelete({ id, index })
    },
    handleUploadSuccess(res) {
      this.uploadList = [...this.uploadList, res]
      // 上传完毕后更新展示的列表和把新增的附件id塞入到
      this.entityForm.attachmentList.push({ id: res.id })
    },
    handleUploadDelete({ status, index }) {
      this.uploadList.splice(index, 1)
    },

    //CSR报告上传
    async handleRemoveUploadV(index) {
      const { id } = this.uploadListV[index]
      this.$refs.uploadV.attachmentDelete({ id, index })
    },
    handleUploadSuccessV(res) {
      this.uploadListV = [...this.uploadListV, res]
      // 上传完毕后更新展示的列表和把新增的附件id塞入到
      this.entityForm.attachmentCsrList.push({ id: res.id })
    },
    handleUploadDeleteV({ status, index }) {
      this.uploadListV.splice(index, 1)
    },

    async setSubmittext(fList) {
      fList.forEach((e, index) => {
        if (e.date) {
          var str = e.date
          str = str.replace(/\//g, '-')
          // console.log(str)
          e.date = str
        }
      })
      const text = JSON.stringify(fList)
      const res = await Model.importQualBuyIn({ text })
      if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      this.$message({ showClose: true, message: '导入成功', type: 'success' })
      this.dialogFormVisibleV = false
      this.getList()
    },
    handleWatchForm(item) {
      // this.supplierEntity.value = item.supplierId
      setTimeout(() => {
        this.entityForm = { ...item }
      }, 1000)
      this.dialogStatus = 'watch'
      this.dialogFormVisible = true
    },

    // querySearch(queryString, cb) {
    //   console.log(queryString)
    //   this.supplierEntity.options.forEach((element) => {
    //     element.label = element.name
    //     element.value = element.id
    //   })
    //   let list = this.supplierEntity.options
    //   console.log(list)
    //   let results = queryString ? list.filter(this.createFilter(queryString)) : list
    //   cb(results)
    // },
    // createFilter(queryString) {
    //   console.log(queryString)
    //   return (restaurant) => restaurant.label.toString().includes(queryString.toString())
    // },
    // handleInput(val) {
    // this.$emit('update:value', val)
    //   this.entityForm.supplierName = val
    //   console.log(val)
    // },

    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    // async getContractList() {
    //   const { data } = await contractList()
    //   if (data.length) this.supplierEntity.options = data
    // },
    handleSupplier({ value, label }) {
      this.entityForm.productName = ''
      this.nameEntity.active = ''
      // this.entityForm.supplierId = value
      // this.entityForm.supplierName = label
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label }
    },
    // async getProductListfnc(id) {
    //   try {
    //     const { data } = await contractBuyModel.getProductListfn({ supplierId: id })
    //     this.nameEntity.options = data.map((item) => {
    //       return { name: item.name, id: item.id, code: item.code }
    //     })
    //     if (this.nameEntity.options.length == 1) {
    //       this.entityForm.productName = this.nameEntity.options[0].name
    //       this.nameEntity.active = this.nameEntity.options[0].name
    //     }
    //   } catch (e) {}
    // },

    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) {}
    },

    // 计算涂耗累计Acc
    computeWayCostAcc() {
      let { receiveWeight, sendWeight } = this.entityForm
      receiveWeight = receiveWeight * 1
      sendWeight = sendWeight * 1
      let wayCost = (((sendWeight - receiveWeight) / sendWeight) * 100).toFixed(2)
      if (!Number.isFinite(wayCost * 1) || Number.isNaN(wayCost * 1)) {
        this.entityForm.wayCost = 0
      } else {
        this.entityForm.wayCost = wayCost
      }
    },
    async getBookkeeping(id, date) {
      if (!id) {
        this.entityForm.sendWeight = 0
        this.entityForm.receiveWeight = 0
        return
      }
      const { sendWeight, receiveWeight } = await this.model.bookkeeping(id, date)
      this.entityForm.sendWeight = sendWeight
      this.entityForm.receiveWeight = receiveWeight
    },

    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogFormVisibleV = false
      this.batchChangeType = ''
      this.uploadList = [] // 清空下载列表
      this.uploadListV = []
      this.settingsV.data = [{}, {}, {}, {}, {}]
      this.nameEntity.active = ''
      this.contractEntity.active = []
      this.entityForm = { ...this.model.model }
      this.entityForm.attachmentList = []
      this.entityForm.attachmentCsrList = []
      // if (!this.memoryEntity.triggered) {
      //   this.nameEntity.active = ''
      //   // this.supplierEntity.value = ''
      //   this.contractEntity.active = []
      //   this.entityForm = { ...this.model.model }
      // } else {
      //   this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      // }
    },
    // @重写方法-添加了合同搜索字段
    handleFilter(filters) {
      this.filters = { ...filters }
      this.$refs[this.curd].searchChange({ ...this.listQuery, ...filters, filter_EQS_contractId: this.contractParams.query })
    },
    // @重写方法-添加了clearContract方法
    handleFilterReset() {
      this.clearContract()
      this.$refs[this.curd].searchChange()
    },
    async getContract() {
      const { data } = await chooseContract()
      // 供应商名称返回id用来查询
      this.contract.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { supplierName: 'name', supplierId: 'id' }
      })
      // this.contractEntityForm.options = this.formatContract({ data: JSON.parse(JSON.stringify(data)), key: { supplierName: 'code', supplierId: 'id' } })
      this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'displayName', supplierId: 'id' } })
      // console.log(this.contractEntity.options)
    },
    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },

    clearContract() {
      this.listQuery = { ...listQuery }
      this.fromDashboard = false
      this.contractActive = []
      this.contractParams.query = ''
      this.indicators = { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity }
      return false
    },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    /**
     * @切换时更行指标并设置搜索关键字query
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    handleContractChange(value) {
      if (!value.length) {
        this.clearContract()
        this.$refs[this.curd].searchChange()
        return
      }
      const item = this.filterContractItem(value, 'contract')
      const { cleanMt, cleanAd, cleanStd, cleanVdaf, procG } = item
      this.indicators = { mt: cleanMt, ad: cleanAd, std: cleanStd, vdaf: cleanVdaf, g: procG }
      this.contractParams.query = item.id
      this.$refs[this.curd].searchChange({ ...this.listQuery, ...this.filters, filter_EQS_contractId: this.contractParams.query })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// .attachment {
//   width: 50px;
//   height: 50px;
//   cursor: pointer;
//   border: 1px solid #ccc;
//   border-radius: 3px;
//   box-shadow: 0 2px 4px #adabab1f, 0 0 6px #aaa6a60a;
//   &-img {
//     width: 100%;
//   }
// }

.form-titlV {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 11px;
    background: #2f79e8;
    height: 16px;
  }
}
.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.attachmentV {
  & > span {
    font-size: 12px;
    color: blue;
  }
}
.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;
    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      padding: 6px 10px;
      margin-right: 5px;
      font-size: 12px;
      position: relative;
      &-link {
        font-size: 12px;
        opacity: 0.8;
      }
      .icon {
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 9;
        top: 0;
        flex: 0;
      }
    }
  }
}
::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}
::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  color: #fff;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 5) {
  border-left: none;
  border-right: none;
}
.excessive {
  width: 100%;
  color: red;
}
</style>
