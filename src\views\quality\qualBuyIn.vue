<template>
  <div class="app-container">
    <filter-table :options="filterOption" :showAdd="perms[`${curd}:save`]||false" :showImport="false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" @add="handleCreate" @filter="handleFilter"
                  @import="handleImpiort" @reset="handleFilterReset" />
    <s-curd :ref="curd" :actions="actions" :changeactions="changeactions" :list-query="listQuery" :model="model" :name="curd"
            :showSummary="true" otherHeight="130" @selectItem="selectItem">
      <el-table-column slot="productName" align="center" label="品名" prop="productName" width="120">
        <template slot-scope="scope"><span class="name"
                @click="handleUpdate(scope.row,'watch')">{{ scope.row.productName }}</span>
        </template>
      </el-table-column>
      <el-table-column slot="attachment" align="center" label="查看附件" prop="attachment" width="100">
        <template slot-scope="scope">
          <div class="attachmentV" style="margin: 0 auto;">
            <span style="cursor: pointer;color: blue;" @click="$refs.attachment.open(scope.row)">查看附件</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column slot="opt" align="center" fixed="right" label="操作" width="150">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#fa3131" @click="handleUpdate(scope.row,'watch')">查看</el-tag>
          <el-tag v-if="perms[`${curd}:update`]||false" class="opt-btn" color="#FF9639"
                  @click="handleUpdate(scope.row,'update')">编辑
          </el-tag>
          <el-tag v-if="perms[`${curd}:delete`]||false" class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)">
            删除
          </el-tag>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false"
               :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible" top="5vh">
      <el-form v-if="dialogFormVisible" ref="entityForm" :disabled="dialogStatus==='watch'" :model="entityForm" :rules="rules"
               :show-message="true" :status-icon="true" label-position="top" label-width="90px">
        <el-row>
          <div class="form-title">基本信息</div>
          <div class="form-layout">
            <el-row :gutter="80" type="flex">
              <el-col>
                <el-form-item label="日期" prop="date">
                  <date-select v-model="entityForm.date" />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="品名" prop="productName">
                  <select-down :list="nameEntity.options" :value.sync="nameEntity.active" @eventChange="handleNameEntity" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="80" type="flex">
              <el-col v-if="entityForm.ContractIndicators">
                <el-form-item label="合同指标" prop="">
                  <el-input v-model="entityForm.ContractIndicators" clearable />
                </el-form-item>
              </el-col>
              <el-col v-else></el-col>
              <el-col></el-col>
            </el-row>
          </div>
          <div class="form-titlV form-warp">
            <span style="position: relative;top:10px">指标</span>
            <el-button type="export-all" @click="handleAdd">插入一行</el-button>
          </div>
          <div class="form-layout" style="margin-bottom:20px">
            <el-row type="flex">
              <el-col>
                <hot-table ref="hotV" :settings="settingsV" class="hot-table" height="250" width="100%"></hot-table>
              </el-col>
            </el-row>
          </div>
          <div class="form-title">备注信息</div>
          <div class="form-layout" style="margin-bottom:20px">
            <el-row :gutter="80" type="flex">
              <el-col>
                <el-input class="font-col" v-model="entityForm.remarks" type="textarea" :rows="2" placeholder="请输入备注信息" clearable
                          maxlength="500" show-word-limit />
              </el-col>
            </el-row>
          </div>
          <div class="form-title">附件信息</div>
          <el-collapse :value="['1','2']">
            <el-collapse-item name="1" title="煤岩报告上传">
              <file-upload @download="handleFileDownload" :list.sync="entityForm.attachmentList" />
            </el-collapse-item>
            <el-collapse-item name="2" title="CSR报告上传">
              <file-upload @download="handleFileDownload" :list.sync="entityForm.attachmentCsrList" />
            </el-collapse-item>
          </el-collapse>
        </el-row>
      </el-form>
      <div v-if="dialogStatus!=='watch'" slot="footer" class="dialog-footer">
        <el-button v-if="perms[`${curd}:update`]||perms[`${curd}:save`]|| false" :loading="entityFormLoading"
                   class="dialog-footer-btns" type="primary" @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button class="dialog-footer-btns" @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
    <view-attachment ref="attachment" :request-method="model.getUploadList" />
  </div>
</template>
<script>
import productModel from '@/model/product/productList'
import FilterTable from '@/components/FilterTable/index.vue'
import { chooseContract, contractList, getContractBuy } from '@/api/quality'
import Mixins from '@/utils/mixins'
import { qualBuyInFilterOption, listQuery } from '@/const'
import Model from '@/model/coalQuality/qualBuyIn'
import { createMemoryField } from '@/utils/index'
import { HotTable, HotColumn } from '@handsontable/vue'
import { registerLanguageDictionary, zhCN } from 'handsontable/i18n'
import { registerAllModules } from 'handsontable/registry'
import 'handsontable/dist/handsontable.full.css'
import UploadAttachment from '@/components/UploadAttachment/index.vue'
import FileUpload from '@/components/FileUpload/index.vue'
import ViewAttachment from '@/components/ViewAttachment/index.vue'

registerAllModules()
registerLanguageDictionary(zhCN)

class QualBuyInItem {
  constructor() {
    // 采样时间
    this.time = ''
    // 灰分
    this.ad = ''
    // 硫
    this.std = ''
    // 挥发分
    this.vdaf = ''
    // 粘结指数
    this.g = ''
    // 焦渣
    this.crc = ''
    // 全水
    this.mt = ''
    // 平均反射率
    this.macR0 = ''
    // 标准偏差
    this.macS = ''
    // 手动Y值
    this.y = ''
    // 自动Y值
    this.smY = ''
  }
}

export default {
  name: 'qualBuyIn',
  mixins: [Mixins],
  data() {
    return {
      curd: 'qualBuyIn',
      model: Model,
      filterOption: { ...qualBuyInFilterOption },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        productName: { required: true, message: '请输入品名', trigger: 'blur' },
        remarks: { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
      },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      contractParams: { query: '' },
      contractActive: [],
      entityForm: { ...Model.model },
      contract: {
        props: {
          label: 'supplierName',
          value: 'supplierId',
          children: 'contractBuyList'
        },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      indicators: { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity },
      memoryEntity: { fields: {}, triggered: false },
      filters: {},
      fromDashboard: false,
      supplierEntity: { value: '', options: [] },
      nameEntity: { options: [], active: '' },
      nestedHeaders: [
        [
          '日期',
          '采样时间',
          '车牌',
          '品名',
          '合同编号',
          {
            label: '精煤',
            colspan: 9
          },
          {
            label: '原煤',
            colspan: 3
          },
          {
            label: '中煤',
            colspan: 7
          },
          {
            label: '末煤',
            colspan: 7
          },
          {
            label: '矸石',
            colspan: 2
          }
        ],
        [
          '',
          '',
          '',
          '',
          '',
          '硫分St,d',
          '灰分Ad',
          '挥发份Vdaf',
          '特征CRC',
          '回收',
          '含中',
          '水分Mt',
          'G值',
          'Y值',
          '灰分Ad',
          '硫分St,d',
          '水分Mt',
          '硫分St,d',
          '灰分Ad',
          '挥发份Vdaf',
          '特征CRC',
          'G值',
          '水分Mt',
          '回收',
          '硫分St,d',
          '灰分Ad',
          '挥发份Vdaf',
          '特征CRC',
          'G值',
          '水分Mt',
          '回收',
          '灰分',
          '回收'
        ]
      ],
      contractlistV: [],
      selectList: [],
      optName: 'create',
      settingsV: {
        startRows: 1,
        data: Array(10)
          .fill(null)
          .map(item => new QualBuyInItem()),
        columns: [
          { title: '时间', data: 'time', type: 'text', trimWhitespace: true, width: 65 },
          {
            title: '灰分',
            data: 'ad',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '硫',
            data: 'std',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '挥发分',
            data: 'vdaf',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '粘结指数',
            data: 'g',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0'
            }
          },
          {
            title: '焦渣',
            data: 'crc',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0'
            }
          },
          {
            title: '全水',
            data: 'mt',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '平均反射率',
            data: 'macR0',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.000'
            }
          },
          {
            title: '标准偏差',
            data: 'macS',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.000'
            }
          },
          {
            title: '手动Y值',
            data: 'y',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.0'
            }
          },
          {
            title: '自动Y值',
            data: 'smY',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.0'
            }
          }
        ],
        readOnly: this.disabled,
        className: 'custom-classname',
        language: zhCN.languageCode,
        copyPaste: true,
        rowHeaders: false,
        width: 'auto',
        height: 'auto',
        stretchH: 'all',
        manualColumnResize: true,
        manualRowResize: true,
        licenseKey: 'non-commercial-and-evaluation',
        formulas: {
          sheetName: 'Sheet1'
        },
        contextMenu: {
          items: {
            row_above: { name: '向上插入一行' },
            row_below: { name: '向下插入一行' },
            remove_row: { name: '删除行' }
          }
        }
      },
      drawer: {
        visible: false,
        list: [],
        preview: []
      },
      drawerV: {
        visible: false,
        list: [],
        preview: []
      },
      emptyImgPath: require(`@/assets/empty_img.jpg`)
    }
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_LIKES_date: to.params.date }
        vm.fromDashboard = true
      })
    } else {
      next()
    }
  },
  components: {
    ViewAttachment,
    FileUpload,
    UploadAttachment,
    HotTable,
    HotColumn,
    FilterTable
  },
  computed: {
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    },
    sizeV() {
      return this.uploadListV.length
    }
  },
  async created() {
    this.getContract()
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'contractId',
        'contractCode',
        'date',
        'senderName',
        'receiverName',
        'name',
        'productName',
        'coalCategory',
        'truckCount',
        'sendWeight',
        'supplierName',
        'supplierId',
        'receiveWeight',
        'wayCost'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getName()
    this.getContractDR()
    this.permschange(this.curd)
  },
  watch: {
    'entityForm.date'(date) {
      if (this.dialogStatus === 'create' && this.entityForm.contractId) {
        this.getBookkeeping(this.entityForm.contractId, date)
      }
    },
    'entityForm.contractId'(id) {
      if (this.dialogStatus === 'create' && this.entityForm.date) {
        this.getBookkeeping(id, this.entityForm.date)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      // this.entityForm.name = form.productName
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.supplierId
      // this.nameEntity.active = form.productName
      this.entityForm.senderName = form.firstParty
      this.entityForm.receiverName = form.secondParty
      this.entityForm.coalCategory = form.coalType

      if (this.dialogStatus == 'watch') {
        let cleanMt = form.cleanMt ? form.cleanMt : 0
        let cleanAd = form.cleanAd ? form.cleanAd : 0
        let cleanStd = form.cleanAd ? form.cleanStd : 0
        let cleanVdaf = form.cleanVdaf ? form.cleanVdaf : 0
        let crc = form.crc ? form.crc : 0
        let procG = form.procG ? form.procG : 0
        let procY = form.procY ? form.procY : 0
        let procX = form.procX ? form.procX : 0
        let recovery = form.recovery ? form.recovery : 0
        let qualCsr = form.qualCsr ? form.qualCsr : 0
        let macR0 = form.macR0 ? form.macR0 : 0
        let macS = form.macS ? form.macS : 0

        this.entityForm.ContractIndicators =
          'Mt: ' +
          cleanMt +
          '，Ad: ' +
          cleanAd +
          '，Std: ' +
          cleanStd +
          '，Vdaf: ' +
          cleanVdaf +
          '，特征(crc): ' +
          crc +
          '，粘结(G): ' +
          procG +
          '，胶质层Y: ' +
          procY +
          '，X指标: ' +
          procX +
          '，回收率 ' +
          recovery +
          '，热强度(CSR): ' +
          qualCsr +
          '，岩相(R0): ' +
          macR0 +
          '，标准差S: ' +
          macS
      } else {
        this.entityForm.ContractIndicators = ''
      }

      // this.entityForm = { ...this.entityForm, productName: form.productName }
    },

    'entityForm.sendWeight'(v) {
      this.computeWayCostAcc()
    },
    'entityForm.receiveWeight'(v) {
      this.computeWayCostAcc()
    },
    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.entityForm.productName = name
        this.entityForm.name = name
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.getActualWeightSumfn(item.id)
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
      }
    }
  },
  methods: {
    handleAdd() {
      this.settingsV.data.push(new QualBuyInItem())
    },
    handleFileDownload(file) {
      console.log('下载文件:', file)
    },
    async handleDel(row) {
      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await this.model.deleteId(row.id)
          if (res) {
            this.$message({ type: 'success', message: '删除成功!' })
            this.getList()
            this.getnumber()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },
    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []

      this.drawerV.visible = false
      this.drawerV.list = []
      this.drawerV.preview = []
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      this.drawer.list = res.data.attachmentList
      this.drawer.preview = res.data.attachmentList.map((item) => item.uri)
      this.drawer.visible = true
    },
    async handlePreviewAttachmentV(row) {
      const res = await this.model.getUploadList(row.id)
      this.drawerV.list = res.data.attachmentCsrList
      this.drawerV.preview = res.data.attachmentCsrList.map((item) => item.uri)
      this.drawerV.visible = true
    },
    async saveEntityForm(formName) {
      /** @type {  import('handsontable/core').default} */
      let hotInstance = this.$refs.hotV.hotInstance
      let validateResV = await new Promise((resolve, reject) => {
        hotInstance.validateCells((res) => resolve(res))
      })
      if (!validateResV) {
        this.$message({ showClose: true, message: '数据格式不正确，请检查！', type: 'warning' })
        return false
      }
      let qualBuyInItemList = hotInstance.getSourceData()
        .filter((item, index) => !hotInstance.isEmptyRow(index))
      if (qualBuyInItemList.length <= 0) {
        this.$message({ showClose: true, message: '表格不能为空', type: 'warning' })
        return
      }
      this.entityForm = { ...this.entityForm, qualBuyInItemList }
      const valid = await this.$refs[formName].validate()
      if (valid) {
        this.entityFormLoading = true
        const res = await Model.save({ ...this.entityForm })
        this.entityFormLoading = false
        if (res) {
          this.getList()
          this.resetVariant()
          this.dialogFormVisible = false
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        }
      }
    },
    // 导入时获取的采购合同列表
    async getContractDR() {
      const { data } = await getContractBuy()
      let newarry = []
      for (var i = 0; i < data.records.length; i++) {
        newarry.push(data.records[i].code)
        // newarry.push(data.records[i].supplierName + '/' + data.records[i].code)
      }
      this.contractlistV = newarry
    },
    //批量删除,
    async BatchDelete(selectList) {
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      let newarry = []
      for (var i = 0; i < selectList.length; i++) {
        let obj = {}
        obj = selectList[i].id
        newarry.push(obj)
      }
      let parm = {
        idList: newarry
      }
      // console.log(parm)

      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // console.log('确认')
          this.deletefn(parm)
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },
    async deletefn(parm) {
      let { data } = await Model.savebatchChange({ ...parm })
      if (data) {
        this.$message({ type: 'success', message: '删除成功!' })
        this.getList()
      }
    },
    okbtnV() {
      this.$refs.excelref.okbtn()
    },
    closeExportExcelDialogV() {
      this.$refs.excelref.closeExportExcelDialog()
    },
    selectItem(list) {
      //批量选择的数据
      this.selectList = list
    },
    async handleUpdate(item, type) {
      if (type === 'update') {
        this.dialogStatus = 'update'
      } else {
        this.dialogStatus = 'watch'
      }
      // 上传所需要的配置
      this.settingsV.data = [{}]
      const { data } = await this.model.getUploadList(item.id)
      if (type === 'watch') {
        let obj = {
          time: '平均值',
          ad: data.ad,
          crc: data.crc,
          cri: data.cri,
          csr: data.csr,
          g: data.g,
          macR0: data.macR0,
          macS: data.macS,
          mt: data.mt,
          std: data.std,
          vdaf: data.vdaf,
          y: data.y,
          smY: data.smY
        }
        data.qualBuyInItemList.push(obj)
      }
      this.entityForm = { ...data }
      this.settingsV.data = data.qualBuyInItemList
      this.entityForm = { ...data }
      this.dialogFormVisible = true
    },
    handleWatchForm(item) {
      // this.supplierEntity.value = item.supplierId
      setTimeout(() => {
        this.entityForm = { ...item }
      }, 1000)
      this.dialogStatus = 'watch'
      this.dialogFormVisible = true
    },
    handleNameEntity(val) {
      this.entityForm = { ...this.entityForm, productName: val }
    },
    async getActualWeightSumfn(productId) {
      let { data } = await this.model.getActualWeightSum(this.entityForm.date, productId)
      this.entityForm = { ...this.entityForm, actualWeight: data }
    },
    handleSupplier({ value, label }) {
      this.entityForm.productName = ''
      this.nameEntity.active = ''
      // this.entityForm.supplierId = value
      // this.entityForm.supplierName = label
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label }
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) {
      }
    },
    // 计算涂耗累计Acc
    computeWayCostAcc() {
      let { receiveWeight, sendWeight } = this.entityForm
      receiveWeight = receiveWeight * 1
      sendWeight = sendWeight * 1
      let wayCost = (((sendWeight - receiveWeight) / sendWeight) * 100).toFixed(2)
      if (!Number.isFinite(wayCost * 1) || Number.isNaN(wayCost * 1)) {
        this.entityForm.wayCost = 0
      } else {
        this.entityForm.wayCost = wayCost
      }
    },
    async getBookkeeping(id, date) {
      if (!id) {
        this.entityForm.sendWeight = 0
        this.entityForm.receiveWeight = 0
        return
      }
      const { sendWeight, receiveWeight } = await this.model.bookkeeping(id, date)
      this.entityForm.sendWeight = sendWeight
      this.entityForm.receiveWeight = receiveWeight
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.batchChangeType = ''
      this.uploadList = [] // 清空下载列表
      this.uploadListV = []
      this.settingsV.data = Array(10).fill(null).map(item => new QualBuyInItem())
      this.nameEntity.active = ''
      this.contractEntity.active = []
      this.entityForm = { ...this.model.model }
      this.entityForm.attachmentList = []
      this.entityForm.attachmentCsrList = []
    },
    // @重写方法-添加了合同搜索字段
    handleFilter(filters) {
      this.filters = { ...filters }
      this.$refs[this.curd].searchChange({
        ...this.listQuery, ...filters,
        filter_EQS_contractId: this.contractParams.query
      })
    },
    // @重写方法-添加了clearContract方法
    handleFilterReset() {
      this.clearContract()
      this.$refs[this.curd].searchChange()
    },
    async getContract() {
      const { data } = await chooseContract()
      // 供应商名称返回id用来查询
      this.contract.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { supplierName: 'name', supplierId: 'id' }
      })
      // this.contractEntityForm.options = this.formatContract({ data: JSON.parse(JSON.stringify(data)), key: { supplierName: 'code', supplierId: 'id' } })
      this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'displayName', supplierId: 'id' } })
      // console.log(this.contractEntity.options)
    },
    /**
     * 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    clearContract() {
      this.listQuery = { ...listQuery }
      this.fromDashboard = false
      this.contractActive = []
      this.contractParams.query = ''
      this.indicators = { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity }
      return false
    },
    /**
     *找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    /**
     * 切换时更行指标并设置搜索关键字query
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    handleContractChange(value) {
      if (!value.length) {
        this.clearContract()
        this.$refs[this.curd].searchChange()
        return
      }
      const item = this.filterContractItem(value, 'contract')
      const { cleanMt, cleanAd, cleanStd, cleanVdaf, procG } = item
      this.indicators = { mt: cleanMt, ad: cleanAd, std: cleanStd, vdaf: cleanVdaf, g: procG }
      this.contractParams.query = item.id
      this.$refs[this.curd].searchChange({
        ...this.listQuery, ...this.filters,
        filter_EQS_contractId: this.contractParams.query
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.form-titlV {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 11px;
    background: #2f79e8;
    height: 16px;
  }
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.attachmentV {
  & > span {
    font-size: 12px;
    color: blue;
  }
}

.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;

  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      padding: 6px 10px;
      margin-right: 5px;
      font-size: 12px;
      position: relative;

      &-link {
        font-size: 12px;
        opacity: 0.8;
      }

      .icon {
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 9;
        top: 0;
        flex: 0;
      }
    }
  }
}

.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);

  &-contract {
    width: 140px;
  }

  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;

    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }

    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;

      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}

::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  color: #fff;
}

::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 5) {
  border-left: none;
  border-right: none;
}

.excessive {
  width: 100%;
  color: red;
}
.font-col {
  ::v-deep .el-textarea__inner {
    color: #000;
  }
}
</style>
