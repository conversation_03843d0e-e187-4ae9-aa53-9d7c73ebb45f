<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <panel title="定位查看" type="location" :active="locationActive" :list='locationList' @locationChange="handleToLocation" />
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" otherHeight="180"
            :showSummary='true'>
      <el-table-column label="小样名称" slot="name" prop="name" width="100" fixed="left" align='center'>
        <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{scope.row.name}}</span></template>
      </el-table-column>
      <el-table-column class-name="index" label="基本指标" slot="chargingTest" align="center">
        <el-table-column label="灰Ad%" prop="rawAd" width="100" align="center" />
        <el-table-column label="硫St,d" prop="rawStd" width="100" align="center" />
        <el-table-column label="挥发分Vdaf%" prop="rawVdaf" width="100" align="center" />
        <el-table-column label="特征Crc" prop="procCrc" width="100" align="center" />
        <el-table-column label="粘结G" prop="procG" width="100" align="center" />
        <el-table-column label="Ymm" prop="procY" width="100" align="center" />
        <el-table-column label="Xmm" prop="procX" width="100" align="center" />
        <el-table-column label="全水Mt" prop="rawMt" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="细度" slot="chargingFineness" align="center">
        <el-table-column label=">3mm" prop="less3mm" width="100" align="center" />
        <el-table-column label="<3mm" prop="greater3mm" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="焦炭" slot="coke" align="center">
        <el-table-column label="Ad" prop="qualAd" width="100" align="center" />
        <el-table-column label="St,d" prop="qualStd" width="100" align="center" />
        <el-table-column label="Vdaf" prop="qualVdaf" width="100" align="center" />
        <el-table-column label="M40" prop="qualM40" width="100" align="center" />
        <el-table-column label="M25" prop="qualM25" width="100" align="center" />
        <el-table-column label="M10" prop="qualM10" width="100" align="center" />
        <el-table-column label="CRI" prop="qualCri" width="100" align="center" />
        <el-table-column label="CSR" prop="qualCsr" width="100" align="center" />
        <el-table-column label="固定碳" prop="fcad" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="灰成份" slot="ashComposition" align="center">
        <el-table-column label="SiO2" prop="comSiO2" width="100" align="center" />
        <el-table-column label="Ai2O3" prop="comAl2O3" width="100" align="center" />
        <el-table-column label="Fe2O3" prop="comFe2O3" width="100" align="center" />
        <el-table-column label="CaO" prop="comCaO" width="100" align="center" />
        <el-table-column label="MgO" prop="comMgO" width="100" align="center" />
        <el-table-column label="Na2O" prop="comNa2O" width="100" align="center" />
        <el-table-column label="K2O" prop="comK2O" width="100" align="center" />
        <el-table-column label="TiO2" prop="comTiO2" width="100" align="center" />
        <el-table-column label="P" prop="comP2O5" width="100" align="center" />
        <el-table-column label="SO3" prop="comSO3" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="岩相" slot="lithofacies" align="center">
        <el-table-column label="Rran平均值" prop="macRavg" width="100" align="center" />
        <el-table-column label="Rmax平均值" prop="macRmax" width="100" align="center" />
        <el-table-column label="Rmax平均值" prop="macRmax" width="100" align="center" />

        <el-table-column label="标准偏差S" prop="macS" width="100" align="center" />
        <el-table-column label="活性物" prop="procV" width="100" align="center" />
        <el-table-column label="活惰比" prop="procVi" width="100" align="center" />
      </el-table-column>
      <el-table-column class-name="index" label="奥亚" slot="aoya" align="center">
        <el-table-column label="软化温度" prop="procT1" width="100" align="center" />
        <el-table-column label="开始膨胀温度" prop="procT2" width="100" align="center" />
        <el-table-column label="固化温度" prop="procT3" width="100" align="center" />
        <el-table-column label="最大膨胀度" prop="procB" width="100" align="center" />
        <el-table-column label="最大收缩度" prop="procA" width="100" align="center" />
      </el-table-column>

      <el-table-column label="附件" slot="attachment" prop="attachment" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handlePreviewAttachment(scope.row)">查看附件</el-button>
          <!-- <div class="attachment">
            <img class="attachment-img" src="@/assets/attachment.png" >
          </div> -->
        </template>
      </el-table-column>
      <el-table-column label="操作" slot="opt" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)"
                  v-if="perms[`${curd}:update`]||false">编辑</el-tag>
          <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除</el-tag>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false" width="980px">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="日期" prop="sampleDate">
                    <date-select v-model="entityForm.sampleDate" :clearable="false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="小样名称" prop="name">
                    <el-input v-model="entityForm.name" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="煤种" prop="type">
                    <category-select :value.sync="entityForm.type" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="检测单位" prop="customerName">
                    <el-input v-model="entityForm.customerName" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">基本指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="灰Ad" prop="rawAd">
                    <el-input v-model="entityForm.rawAd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="硫St,d" prop="rawStd">
                    <el-input v-model="entityForm.rawStd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="挥发分Vdaf" prop="rawVdaf">
                    <el-input v-model="entityForm.rawVdaf" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="特征Cr" prop="procCrc">
                    <el-input v-model="entityForm.procCrc" :oninput="field.decimal" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="粘结G" prop="procG">
                    <el-input v-model="entityForm.procG" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Ymm" prop="procY">
                    <el-input v-model="entityForm.procY" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Xmm" prop="procX">
                    <el-input v-model="entityForm.procX" :oninput="field.decimal" clearable>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="全水Mt" prop="rawMt">
                    <el-input v-model="entityForm.rawMt" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">细度</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label=">3mm" prop="less3mm">
                    <el-input v-model="entityForm.less3mm" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="<3mm" prop="greater3mm">
                    <el-input v-model="entityForm.greater3mm" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col />
                <el-col />
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">焦炭</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="Ad" prop="qualAd">
                    <el-input v-model="entityForm.qualAd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="St,d" prop="qualStd">
                    <el-input v-model="entityForm.qualStd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Vdaf" prop="qualVdaf">
                    <el-input v-model="entityForm.qualVdaf" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="M40" prop="qualM40">
                    <el-input v-model="entityForm.qualM40" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="M25" prop="qualM25">
                    <el-input v-model="entityForm.qualM25" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="M10" prop="qualM10">
                    <el-input v-model="entityForm.qualM10" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="CRI" prop="qualCri">
                    <el-input v-model="entityForm.qualCri" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="CSR" prop="qualCsr">
                    <el-input v-model="entityForm.qualCsr" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="固定碳" prop="fcad">
                    <el-input v-model="entityForm.fcad" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>
          <el-col>
            <div class="form-title">灰成分</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="SIO2" prop="comSiO2">
                    <el-input v-model="entityForm.comSiO2" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="AI2O3" prop="comAl2O3">
                    <el-input v-model="entityForm.comAl2O3" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Fe2O3" prop="comFe2O3">
                    <el-input v-model="entityForm.comFe2O3" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="CaO" prop="comCaO">
                    <el-input v-model="entityForm.comCaO" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="MgO" prop="comMgO">
                    <el-input v-model="entityForm.comMgO" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Na2O" prop="comNa2O">
                    <el-input v-model="entityForm.comNa2O" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="K2O" prop="comK2O">
                    <el-input v-model="entityForm.comK2O" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="TiO2" prop="comTiO2">
                    <el-input v-model="entityForm.comTiO2" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="P" prop="comP2O5">
                    <el-input v-model="entityForm.comP2O5" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="SO3" prop="comSO3">
                    <el-input v-model="entityForm.comSO3" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col />
                <el-col />
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">岩相</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="Rran平均值" prop="macRavg">
                    <el-input v-model="entityForm.macRavg" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="Rmax平均值" prop="macRmax">
                    <el-input v-model="entityForm.macRmax" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="标准偏差S" prop="macS">
                    <el-input v-model="entityForm.macS" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <!-- 不确定 -->
                  <el-form-item label="活性物" prop="procV">
                    <el-input v-model="entityForm.procV" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="活惰比" prop="procVi">
                    <el-input v-model="entityForm.procVi" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="镜质组" prop="macV">
                    <el-input v-model="entityForm.macV" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="丝质组" prop="macI">
                    <el-input v-model="entityForm.macI" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="稳定组" prop="macE">
                    <el-input v-model="entityForm.macE" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">奥亚</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="软化温度T1" prop="procT1">
                    <el-input v-model="entityForm.procT1" :oninput="field.decimal" clearable>
                      <template slot="append">°C</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="开始膨胀温度T2" prop="procT2">
                    <el-input v-model="entityForm.procT2" :oninput="field.decimal" clearable>
                      <template slot="append">°C</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="固化温度T3" prop="procT3">
                    <el-input v-model="entityForm.procT3" :oninput="field.decimal" clearable>
                      <template slot="append">°C</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="最大膨胀度b" prop="procB">
                    <el-input v-model="entityForm.procB" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="最大收缩度a" prop="procA">
                    <el-input v-model="entityForm.procA" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                </el-col>
                <el-col />
                <el-col />
              </el-row>
            </div>
          </el-col>

          <el-col>
            <div class="form-title">附件信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="80">
                <el-col>
                  <el-form-item label="">
                    <el-collapse v-model="collapse">
                      <el-collapse-item title="上传附件" name="1">
                        <div class="upload">
                          <div class="upload-list">
                            <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                              <i class="el-icon-close icon" @click="handleRemoveUpload(index)"></i>
                              <el-link class="up-load-warp-link" :underline=" false" :href="item.uri">{{item.display}}</el-link>
                            </div>
                            <upload-attachment ref="upload" class="upload-attachment" :uploadData="uploadData" source="coalSample"
                                               :size="size" :limitNum="limitNum"
                                               accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg"
                                               @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete"
                                               listType="text" />
                          </div>
                        </div>

                      </el-collapse-item>
                    </el-collapse>

                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

    <el-drawer title="附件列表" :visible.sync="drawer.visible" direction="rtl" :before-close="handleCloseDrawer"
               custom-class="drawer">
      <div class="images-warp">
        <template v-if="drawer.list.length">
          <div class="links" v-for="(item,index) in drawer.list" :key="index">
            <el-link class="up-load-warp-link" @click.native="handleDownload(item.uri)" :underline="false" href="javascript:;">
              {{item.display}}
              <el-divider direction="vertical" />
            </el-link>
          </div>
        </template>
        <template v-else>
          <el-empty :image="emptyImgPath" :image-size="200" description="暂无附件" style="display:flex;width:100%" />
        </template>
        <!-- <div class="links" v-for="(item,index) in Object.values(drawer.dateList)" :key="index">
          <h5 class="link-title">上传时间:{{item.date}}</h5>
          <template v-for="(link,i) in item.list">
            <el-link class="up-load-warp-link" :underline="false" :href="link.uri" :key="i">
              {{link.display}}
              <el-divider direction="vertical" />
            </el-link>
          </template>
        </div> -->
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import productModel from '@/model/product/productList'
import { qualOuterOption } from '@/const'
import Model from '@/model/coalQuality/qualOuter'
import { createMemoryField } from '@/utils/index'
export default {
  name: 'qualOuter',
  mixins: [Mixins],
  data() {
    return {
      curd: 'qualOuter',
      model: Model,
      filterOption: { ...qualOuterOption },
      currentContract: [],
      entityForm: { ...Model.model },
      contract: {
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      locationList: [
        { label: '基本指标' },
        { label: '细度' },
        { label: '焦炭' },
        { label: '灰成份' },
        { label: '岩相' },
        { label: '奥亚' }
      ],
      locationActive: -1,
      memoryEntity: { fields: {}, triggered: false },
      nameEntity: { options: [], active: '' },
      // upload
      fileList: [],
      limitNum: 1000,
      uploadList: [], // 用于展示
      uploadData: { refId: '', refType: 'CoalSample' },
      collapse: '1',
      drawer: {
        visible: false,
        list: [],
        preview: [],
        dateList: []
      },
      emptyImgPath: require(`@/assets/empty.jpg`)
    }
  },
  created() {
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'time',
        'customerName',
        'contractCode',
        'contractId',
        'receiverName',
        'productCode',
        'productId',
        'name',
        'type'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getName()
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_EQS_id: to.params.id }
      })
    } else {
      next()
    }
  },
  watch: {
    'entityForm.name'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.nameEntity.active = item.name
        this.entityForm.productName = name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.entityForm.type = item.coalCategory
      } else {
        this.entityForm.type = ''
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.productName = ''
      }
    }
  },
  computed: {
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    }
  },
  methods: {
    handleDownload(url) {
      window.open(url)
    },
    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      this.drawer.list = res.data.attachmentList
      let list = {}
      res.data.attachmentList.forEach((item) => {
        let { createDate, display, uri } = item
        createDate = createDate.match(/.+?(?=\s)/)[0]

        if (list[createDate]) {
          list[createDate].list.push({ display, uri })
        } else {
          list[createDate] = { date: createDate, list: [{ display, uri }] }
        }
      })
      this.drawer.dateList = list

      this.drawer.preview = res.data.attachmentList.map((item) => {
        let { createDate, display, uri } = item
        createDate = createDate.match(/.+?(?=\s)/)
        return { createDate, display, uri }
      })
      this.drawer.visible = true
    },

    async getName() {
      try {
        let { data } = await productModel.page({ size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
        })
      } catch (error) {}
    },
    handleNameEntity(val) {
      this.entityForm.name = val
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.uploadList = [] // 清空下载列表
      this.fileList = []
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },
    handleToLocation(index) {
      this.locationActive = index === this.locationActive ? -1 : index
      this.$refs[this.curd].toLocation(this.locationActive)
    },
    async handleRemoveUpload(index) {
      const { id } = this.uploadList[index]
      this.$refs.upload.attachmentDelete({ id, index })
    },

    handleUploadSuccess(res) {
      this.uploadList = [...this.uploadList, res]
      // 上传完毕后更新展示的列表和把新增的附件id塞入到
      this.entityForm.attachmentList.push({ id: res.id })

      // this.entityForm.attachmentList.push(res)
    },
    handleUploadDelete({ status, index }) {
      this.uploadList.splice(index, 1)
    },
    async handleUpdate(item) {
      this.entityForm = { ...item }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      // 上传所需要的配置
      this.uploadData.refId = item.id
      const { data } = await this.model.getUploadList(item.id)
      this.uploadList = data.attachmentList
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
// .opt-btnV {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
::v-deep .el-drawer__header > :first-child {
  font-size: 14px;
}
.images-warp {
  height: 100%;
  display: flex;
  justify-content: flex-start;
  align-content: flex-start;
  flex-flow: wrap;
  .links {
    padding-left: 20px;
    display: flex;
    width: 100%;
    justify-content: flex-start;
    flex-flow: wrap;
    margin-bottom: 10px;
    opacity: 0.95;
    .link-title {
      // color: ;
      color: #000;
      opacity: 0.5;
    }
  }
}
::v-deep .el-collapse-item__arrow {
  display: none;
}

.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;
  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;
    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      padding: 6px 10px;
      margin-right: 5px;
      font-size: 12px;
      position: relative;
      &-link {
        font-size: 12px;
        opacity: 0.8;
      }
      .icon {
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 9;
        top: 0;
        flex: 0;
      }
    }
  }
}

::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}

::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}
::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  color: #fff;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 5) {
  // border-left: none;
  // border-right: none;
  border: none;
}
::v-deep .el-table__fixed::before {
  background: transparent;
}
::v-deep .el-table__fixed-right::before {
  background: transparent;
}
</style>
