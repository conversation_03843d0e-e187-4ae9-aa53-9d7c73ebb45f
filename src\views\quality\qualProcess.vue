<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <!-- :showSummary='true' -->
    <s-curd :ref="curd" :name="curd" :model="model" otherHeight="120" :actions="actions" :list-query="listQuery">

      <!-- <el-table-column label="一矸4" slot="oneGanAd" align="center">
        <template slot-scope="scope" v-if="scope.row.oneGanAd">
          {{ analysisfn(scope.row.oneGanAd) }}
        </template>
      </el-table-column> -->

      <el-table-column label="操作" slot="opt" min-width="100" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)"
                      v-if="perms[`${curd}:update`]||false">编辑</el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
    </s-curd>

    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false" width="980px">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基础信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="entityForm.date" :clearable="false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="客户" prop="customerName">
                    <el-select v-model="customerEntity.active" filterable placeholder="请选择客户资料" clearable @change="handleCustomer"
                               @input="handleInput" style="width:100%">
                      <el-option v-for="item in customerEntity.options" :key="item.value" :label="item.label" :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div class="form-title">化验指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col class="tescol">
                  <el-form-item label="筛精" prop="sifterAd">
                    <el-tag :key="tag" v-for="tag in dynamicTags" size="medium" closable :disable-transitions="false"
                            @close="handleClose0(tag)">
                      {{tag}}
                    </el-tag>
                    <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput" size="small"
                              @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm">
                    </el-input>
                    <el-button v-else class="button-new-tag" size="small" @click="showInput">+</el-button>
                  </el-form-item>
                </el-col>

                <el-col class="tescol">
                  <el-form-item label="纯精" prop="pureAd">
                    <!-- <el-input v-model="entityForm.pureAd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input> -->
                    <el-tag :key="tag1" v-for="tag1 in dynamicTags1" size="medium" closable :disable-transitions="false"
                            @close="handleClose1(tag1)">
                      {{tag1}}
                    </el-tag>
                    <el-input class="input-new-tag" v-if="inputVisible1" v-model="inputValue1" ref="saveTagInput1" size="small"
                              @keyup.enter.native="handleInputConfirm1" @blur="handleInputConfirm1">
                    </el-input>
                    <el-button v-else class="button-new-tag" size="small" @click="showInput1">+</el-button>
                  </el-form-item>
                </el-col>

              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col class="tescol">
                  <el-form-item label="精矿" prop="pureMineAd">
                    <!-- <el-input v-model="entityForm.pureMineAd" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input> -->
                    <el-tag :key="tag2" v-for="tag2 in dynamicTags2" size="medium" closable :disable-transitions="false"
                            @close="handleClose2(tag2)">
                      {{tag2}}
                    </el-tag>
                    <el-input class="input-new-tag" v-if="inputVisible2" v-model="inputValue2" ref="saveTagInput2" size="small"
                              @keyup.enter.native="handleInputConfirm2" @blur="handleInputConfirm2">
                    </el-input>
                    <el-button v-else class="button-new-tag" size="small" @click="showInput2">+</el-button>
                  </el-form-item>
                </el-col>

                <el-col class="tescol">
                  <el-form-item label="南筛" prop="southeAd">
                    <el-tag :key="tag7" v-for="tag7 in dynamicTags7" size="medium" closable :disable-transitions="false"
                            @close="handleClose7(tag7)">
                      {{tag7}}
                    </el-tag>
                    <el-input class="input-new-tag" v-if="inputVisible7" v-model="inputValue7" ref="saveTagInput7" size="small"
                              @keyup.enter.native="handleInputConfirm7" @blur="handleInputConfirm7">
                    </el-input>
                    <el-button v-else class="button-new-tag" size="small" @click="showInput7">+</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row type="flex" :gutter="50">
                <el-col class="tescol">
                  <el-form-item label="尾矿" prop="tailMineAd">
                    <el-tag :key="tag3" v-for="tag3 in dynamicTags3" size="medium" closable :disable-transitions="false"
                            @close="handleClose3(tag3)">
                      {{tag3}}
                    </el-tag>
                    <el-input class="input-new-tag" v-if="inputVisible3" v-model="inputValue3" ref="saveTagInput3" size="small"
                              @keyup.enter.native="handleInputConfirm3" @blur="handleInputConfirm3">
                    </el-input>
                    <el-button v-else class="button-new-tag" size="small" @click="showInput3">+</el-button>
                  </el-form-item>
                </el-col>
                <el-col class="tescol">
                  <el-form-item label="一矸" prop="oneGanAd">
                    <!-- <el-input v-model="entityForm.oneGanAd" :oninput="field.decimal" clearable>
                    </el-input> -->
                    <el-tag :key="tag4" v-for="tag4 in dynamicTags4" size="medium" closable :disable-transitions="false"
                            @close="handleClose4(tag4)">
                      {{tag4}}
                    </el-tag>
                    <el-input class="input-new-tag" v-if="inputVisible4" v-model="inputValue4" ref="saveTagInput4" size="small"
                              @keyup.enter.native="handleInputConfirm4" @blur="handleInputConfirm4">
                    </el-input>
                    <el-button v-else class="button-new-tag" size="small" @click="showInput4">+</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col class="tescol">
                  <el-form-item label="二矸" prop="twoGanAd">
                    <el-tag :key="tag5" v-for="tag5 in dynamicTags5" size="medium" closable :disable-transitions="false"
                            @close="handleClose5(tag5)">
                      {{tag5}}
                    </el-tag>
                    <el-input class="input-new-tag" v-if="inputVisible5" v-model="inputValue5" ref="saveTagInput5" size="small"
                              @keyup.enter.native="handleInputConfirm5" @blur="handleInputConfirm5">
                    </el-input>
                    <el-button v-else class="button-new-tag" size="small" @click="showInput5">+</el-button>
                  </el-form-item>
                </el-col>
                <el-col class="tescol">
                  <el-form-item label="三矸" prop="threeGanAd">
                    <el-tag :key="tag6" v-for="tag6 in dynamicTags6" size="medium" closable :disable-transitions="false"
                            @close="handleClose6(tag6)">
                      {{tag6}}
                    </el-tag>
                    <el-input class="input-new-tag" v-if="inputVisible6" v-model="inputValue6" ref="saveTagInput6" size="small"
                              @keyup.enter.native="handleInputConfirm6" @blur="handleInputConfirm6">
                    </el-input>
                    <el-button v-else class="button-new-tag" size="small" @click="showInput6">+</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCustomerContract } from '@/api/quality'
// import CustomerModel from '@/model/user/customer'
import CustomerAndSupplierModel from '@/model/user/supplierAndCustomer'
import contractSellModel from '@/model/user/contractSell'
import contractBuyModel from '@/model/user/contractBuy'
import Mixins from '@/utils/mixins'
import { qualProcessOption, listQuery } from '@/const'
import Model from '@/model/coalQuality/qualProcess'
import { createMemoryField } from '@/utils/index'
export default {
  name: 'qualProcess',
  data() {
    return {
      curd: 'qualProcess',
      entityForm: { ...Model.model },
      model: Model,
      filterOption: { ...qualProcessOption },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        customerName: { required: true, message: '请选择客户', trigger: 'blur' }
      },
      contractActive: [],
      contractEntityFormActive: [],
      contractEntityForm: {
        options: []
      },
      contractParams: { query: '' },
      contract: {
        props: {
          label: 'customerName',
          value: 'customerId',
          children: 'contractSellList'
        },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      indicators: { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity },
      nameEntity: { options: [], active: '' },
      memoryEntity: { fields: {}, triggered: false },
      filters: {},
      fromDashboard: false,
      customerEntity: { options: [], active: [] },
      val: '',

      dynamicTags: [],
      inputVisible: false,
      inputValue: '',

      dynamicTags1: [],
      inputVisible1: false,
      inputValue1: '',

      dynamicTags2: [],
      inputVisible2: false,
      inputValue2: '',

      dynamicTags3: [],
      inputVisible3: false,
      inputValue3: '',

      dynamicTags4: [],
      inputVisible4: false,
      inputValue4: '',

      dynamicTags5: [],
      inputVisible5: false,
      inputValue5: '',

      dynamicTags6: [],
      inputVisible6: false,
      inputValue6: '',

      dynamicTags7: [],
      inputVisible7: false,
      inputValue7: '',

      istrue: false
    }
  },
  computed: {
    otherHeight() {
      return this.perms[`${this.curd}:export`] ? '227' : '180'
    }
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_LIKES_date: to.params.date }
        vm.fromDashboard = true
      })
    } else {
      next()
    }
  },
  mixins: [Mixins],
  created() {
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        // 'time',
        'customerName',
        'contractName',
        'contractCode',
        'contractId',
        // 'receiverName',
        'productName',
        'productCode',
        'productId'
        // 'name',
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getContract()
    // this.getCustomer()
    this.getalllist() //获取供应商和客户列表
    this.permsAction(this.curd)
  },
  watch: {
    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.entityForm.productName = name
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
      } else {
        // this.nameEntity.active = ''
        // this.entityForm.productCode = ''
        // this.entityForm.productId = ''
        // this.entityForm.productName = ''
      }
    },
    'entityForm.customerName'(name) {
      if (!name) return
      const item = this.customerEntity.options.find((item) => item.label == name)
      if (item) {
        this.entityForm.customerId = item.value
        this.customerEntity.active = item.label
        this.getProductListfnc(item.value, item.type)
      } else {
        this.customerEntity.active = []
      }
    }
  },
  // 可写原方法覆盖mixins方法
  methods: {
    async getalllist() {
      //获取客户和供应商列表
      try {
        let options = []
        const { data } = await CustomerAndSupplierModel.findByKeyword({})
        if (data.length) {
          data.forEach((item) => {
            const { name: label, id: value, type: type } = item
            options.push({ label, value, type })
          })
        }
        this.customerEntity.options = options
      } catch (e) {}
    },

    // analysisfn(oneGanAd) {
    //   let newString = ''
    //   if (oneGanAd) {
    //     var array = oneGanAd.split(',')
    //     array.forEach((itemv, index) => {
    //       // if (index % 2 !== 0) {
    //       //   console.log(' 奇数')
    //       // } else {
    //       //   console.log('偶数')
    //       // }
    //       newString += itemv + (index % 2 ? ';' : ',')
    //     })
    //     return newString
    //   }
    // },

    handleClose() {
      this.resetVariant()
    },
    handleUpdate(item) {
      this.istrue = TextTrackCue
      this.entityForm = { ...item }
      this.getProductListfnc(this.entityForm.customerId, this.entityForm.type, this.entityForm.productName)
      if (item.sifterAd != '') {
        var array = item.sifterAd.split(',')
        this.dynamicTags = array
        this.entityForm.sifterAd = item.sifterAd
      }

      if (item.pureAd != '') {
        var array1 = item.pureAd.split(',')
        this.dynamicTags1 = array1
        this.entityForm.pureAd = item.pureAd
      }

      if (item.pureMineAd != '') {
        var array2 = item.pureMineAd.split(',')
        this.dynamicTags2 = array2
        this.entityForm.pureMineAd = item.pureMineAd
      }

      if (item.tailMineAd != '') {
        var array3 = item.tailMineAd.split(',')
        this.dynamicTags3 = array3
        this.entityForm.tailMineAd = item.tailMineAd
      }

      if (item.oneGanAd != '') {
        var array4 = item.oneGanAd.split(',')
        this.dynamicTags4 = array4
        this.entityForm.oneGanAd = item.oneGanAd
      }

      if (item.twoGanAd != '') {
        var array5 = item.twoGanAd.split(',')
        this.dynamicTags5 = array5
        this.entityForm.twoGanAd = item.twoGanAd
      }
      if (item.threeGanAd != '') {
        var array6 = item.threeGanAd.split(',')
        this.dynamicTags6 = array6
        this.entityForm.threeGanAd = item.threeGanAd
      }
      if (item.southeAd != '') {
        var array7 = item.southeAd.split(',')
        this.dynamicTags7 = array7
        this.entityForm.southeAd = item.southeAd
      }

      this.dialogStatus = 'update'
      this.dialogFormVisible = true
    },
    handleClose0(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1)
      let result = this.dynamicTags.join(',')
      this.entityForm.sifterAd = result
    },
    showInput() {
      this.inputVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    handleInputConfirm() {
      let inputValue = this.inputValue
      if (inputValue) {
        this.dynamicTags.push(inputValue)
        let result = this.dynamicTags.join(',')
        this.entityForm.sifterAd = result
      }
      this.inputVisible = false
      this.inputValue = ''
    },

    handleClose1(tag) {
      this.dynamicTags1.splice(this.dynamicTags1.indexOf(tag), 1)
      let result = this.dynamicTags1.join(',')
      this.entityForm.pureAd = result
    },
    showInput1() {
      this.inputVisible1 = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput1.$refs.input.focus()
      })
    },
    handleInputConfirm1() {
      let inputValue = this.inputValue1
      if (inputValue) {
        this.dynamicTags1.push(inputValue)
        let result = this.dynamicTags1.join(',')
        this.entityForm.pureAd = result
      }
      this.inputVisible1 = false
      this.inputValue1 = ''
    },

    handleClose2(tag) {
      this.dynamicTags2.splice(this.dynamicTags2.indexOf(tag), 1)
      let result = this.dynamicTags2.join(',')
      this.entityForm.pureMineAd = result
    },
    showInput2() {
      this.inputVisible2 = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput2.$refs.input.focus()
      })
    },
    handleInputConfirm2() {
      let inputValue = this.inputValue2
      if (inputValue) {
        this.dynamicTags2.push(inputValue)
        let result = this.dynamicTags2.join(',')
        this.entityForm.pureMineAd = result
      }
      this.inputVisible2 = false
      this.inputValue2 = ''
    },

    handleClose3(tag) {
      this.dynamicTags3.splice(this.dynamicTags3.indexOf(tag), 1)
      let result = this.dynamicTags3.join(',')
      this.entityForm.tailMineAd = result
    },
    showInput3() {
      this.inputVisible3 = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput3.$refs.input.focus()
      })
    },
    handleInputConfirm3() {
      let inputValue = this.inputValue3
      if (inputValue) {
        this.dynamicTags3.push(inputValue)
        let result = this.dynamicTags3.join(',')
        this.entityForm.tailMineAd = result
      }
      this.inputVisible3 = false
      this.inputValue3 = ''
    },

    handleClose4(tag) {
      this.dynamicTags4.splice(this.dynamicTags4.indexOf(tag), 1)
      let result = this.dynamicTags4.join(',')
      this.entityForm.oneGanAd = result
    },
    showInput4() {
      this.inputVisible4 = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput4.$refs.input.focus()
      })
    },
    handleInputConfirm4() {
      let inputValue = this.inputValue4
      if (inputValue) {
        this.dynamicTags4.push(inputValue)
        let result = this.dynamicTags4.join(',')
        this.entityForm.oneGanAd = result
      }
      this.inputVisible4 = false
      this.inputValue4 = ''
    },

    handleClose7(tag) {
      this.dynamicTags7.splice(this.dynamicTags7.indexOf(tag), 1)
      let result = this.dynamicTags7.join(',')
      this.entityForm.southeAd = result
    },
    showInput7() {
      this.inputVisible7 = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput7.$refs.input.focus()
      })
    },
    handleInputConfirm7() {
      let inputValue = this.inputValue7
      if (inputValue) {
        this.dynamicTags7.push(inputValue)
        let result = this.dynamicTags7.join(',')
        this.entityForm.southeAd = result
      }
      this.inputVisible7 = false
      this.inputValue7 = ''
    },

    handleClose5(tag) {
      this.dynamicTags5.splice(this.dynamicTags5.indexOf(tag), 1)
      let result = this.dynamicTags5.join(',')
      this.entityForm.twoGanAd = result
    },
    showInput5() {
      this.inputVisible5 = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput5.$refs.input.focus()
      })
    },
    handleInputConfirm5() {
      let inputValue = this.inputValue5
      if (inputValue) {
        this.dynamicTags5.push(inputValue)
        let result = this.dynamicTags5.join(',')
        this.entityForm.twoGanAd = result
      }
      this.inputVisible5 = false
      this.inputValue5 = ''
    },

    handleClose6(tag) {
      this.dynamicTags6.splice(this.dynamicTags6.indexOf(tag), 1)
      let result = this.dynamicTags6.join(',')
      this.entityForm.threeGanAd = result
    },
    showInput6() {
      this.inputVisible6 = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput6.$refs.input.focus()
      })
    },
    handleInputConfirm6() {
      let inputValue = this.inputValue6
      if (inputValue) {
        this.dynamicTags6.push(inputValue)
        let result = this.dynamicTags6.join(',')
        this.entityForm.threeGanAd = result
      }
      this.inputVisible6 = false
      this.inputValue6 = ''
    },

    // dd？

    handleInput(val) {
      // this.$emit('update:value', val)
      this.entityForm.customerName = val
    },
    // 合同改变同步entityForm
    handleCustomer({ value, label, type }) {
      this.entityForm.customerId = value
      this.entityForm.customerName = label
      this.entityForm.productName = ''
      this.getProductListfnc(value, type)
    },
    // async getCustomer() {
    //   try {
    //     let options = []
    //     const { data } = await CustomerModel.page({ size: 1000 })
    //     if (data.records.length) {
    //       data.records.forEach((item) => {
    //         const { name: label, id: value } = item
    //         options.push({ label, value })
    //       })
    //     }
    //     this.customerEntity.options = options
    //   } catch (e) {}
    // },

    async getProductListfnc(id, type) {
      if (type == 'S') {
        //contractBuy
        this.getSupplierProductName(id)
      }
      if (type == 'C') {
        this.getCustomerProductName(id)
      }
    },
    async getCustomerProductName(id) {
      try {
        let options = []
        const { data } = await contractSellModel.getProductListfn({ customerId: id })

        this.nameEntity.options = data.map((item) => {
          return { name: item.name, id: item.id, code: item.code }
        })
        if (this.nameEntity.options.length == 1) {
          this.entityForm.productName = this.nameEntity.options[0].name
        }
        if (this.istrue) {
          const itemww = this.nameEntity.options.find((itemv) => itemv.name === productName)
          if (itemww) {
            this.entityForm.productName = productName
            this.nameEntity.active = itemww.name
            this.entityForm.productCode = itemww.code
            this.entityForm.productId = itemww.id
          }
        }
      } catch (e) {}
    },
    async getSupplierProductName(id) {
      try {
        const { data } = await contractBuyModel.getProductListfn({ supplierId: id })

        this.nameEntity.options = data.map((item) => {
          return { name: item.name, id: item.id, code: item.code }
        })
        if (this.nameEntity.options.length == 1) {
          this.entityForm.productName = this.nameEntity.options[0].name
        }
        if (this.istrue) {
          const itemww = this.nameEntity.options.find((itemv) => itemv.name === this.entityForm.productName)

          if (itemww) {
            this.entityForm.productName = this.entityForm.productName
            this.nameEntity.active = itemww.name
            this.entityForm.productCode = itemww.code
            this.entityForm.productId = itemww.id
          }
        }
      } catch (e) {}
    },

    // async getProductListfnc(id, productName) {
    //   try {
    //     const { data } = await contractSellModel.getProductListfn({ customerId: id })
    //     console.log(data)
    //     this.nameEntity.options = data.map((item) => {
    //       return { name: item.name, id: item.id, code: item.code }
    //     })
    //     if (this.nameEntity.options.length == 1) {
    //       this.entityForm.productName = this.nameEntity.options[0].name
    //     }
    //     if (this.istrue) {
    //       const itemww = this.nameEntity.options.find((itemv) => itemv.name === productName)
    //       if (itemww) {
    //         this.entityForm.productName = productName
    //         this.nameEntity.active = itemww.name
    //         this.entityForm.productCode = itemww.code
    //         this.entityForm.productId = itemww.id
    //       }
    //     }
    //   } catch (e) {}
    // },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false

      this.nameEntity.active = ''
      this.contractEntityFormActive = []
      this.customerEntity.active = []
      this.dynamicTags = []
      this.dynamicTags1 = []
      this.dynamicTags2 = []
      this.dynamicTags3 = []
      this.dynamicTags4 = []
      this.dynamicTags5 = []
      this.dynamicTags6 = []
      this.dynamicTags7 = []

      this.entityForm = { ...this.model.model }

      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.contractEntityFormActive = []
        this.customerEntity.active = []
        this.dynamicTags = []
        this.dynamicTags1 = []
        this.dynamicTags2 = []
        this.dynamicTags3 = []
        this.dynamicTags4 = []
        this.dynamicTags5 = []
        this.dynamicTags6 = []
        this.dynamicTags7 = []

        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },

    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    /**
     * 初始化数据选择下拉数据
     * contract用于指标合同下拉选取label显示name,value为id
     * contractEntityForm用于新增编辑下拉label显示code,value为id
     */
    async getContract() {
      const { data } = await getCustomerContract()
      this.contract.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { customerName: 'name', customerId: 'id' }
      })
      this.contractEntityForm.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { customerName: 'code', customerId: 'id' }
      })
    },
    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
// .opt-btnV {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}
::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}
::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  color: #fff;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 4) {
  border-left: none;
  border-right: none;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 3) {
  border-left: none;
  border-right: none;
}
</style>

<style>
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
