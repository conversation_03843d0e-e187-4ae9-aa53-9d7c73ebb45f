<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <!-- <section class="panel">
      <div class="panel-contract">
        <el-cascader v-model="contractActive" v-bind="contract" @change="handleContractChange" filterable />
      </div>
      <div class="panel-indicators">
        <div class="panel-title">合同指标</div>
        <div class="panel-desc" v-show="indicators.mt!==Infinity">
          <span>Mt≤{{indicators.mt}}%,</span>
          <span>Adt≤{{indicators.ad}}%,</span>
          <span>St,d≤{{indicators.std}}%,</span>
          <span>Vdaf:24{{indicators.vdaf}}%</span>
          <span>g≤{{indicators.g}}</span>
        </div>
      </div>
    </section> -->
    <s-curd :ref="curd" :name="curd" :model="model" otherHeight="120" :actions="actions" :list-query="listQuery"
            :showSummary='true'>
      <el-table-column label="操作" slot="opt" min-width="100" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <div style=" display: flex; flex-direction: row; align-items: center;justify-content: center;">
            <div v-if="perms[`${curd}:update`] || false">
              <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)"
                      v-if="perms[`${curd}:update`]||false">编辑</el-tag>
            </div>
            <div v-if="perms[`${curd}:delete`] || false">
              <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
    </s-curd>
    <!-- 
    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false" width="980px">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">基础信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="entityForm.date" :clearable="false" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="客户" prop="customerName">
                    <el-select v-model="customerEntity.active" filterable placeholder="请选择" clearable @change="handleCustomer"
                               @input="handleInput" style="width:100%">
                      <el-option v-for="item in customerEntity.options" :key="item.value" :label="item.label" :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="品名" prop="productName">
                    <select-down :value.sync="nameEntity.active" :list="nameEntity.options" @eventChange="handleNameEntity" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="采样时间" prop="time">
                    <el-time-select v-model="entityForm.time" :picker-options="{
                                start: '00:00',
                                step: '00:15',
                                end: '23:59'
                              }" placeholder="选择时间">
                    </el-time-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div class="form-title">化验指标</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="挥发分" prop="vad">
                    <el-input v-model="entityForm.vad" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="干燥基灰分" prop="ad">
                    <el-input v-model="entityForm.ad" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="Vdaf" prop="vdaf">
                    <el-input v-model="entityForm.vdaf" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="硫分" prop="std">
                    <el-input v-model="entityForm.std" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="粘结" prop="g">
                    <el-input v-model="entityForm.g" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="特征" prop="crc">
                    <el-input v-model="entityForm.crc" :oninput="field.decimal" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="水分" prop="mt">
                    <el-input v-model="entityForm.mt" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item label="回收" prop="recover">
                    <el-input v-model="entityForm.recover" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="矸石" prop="wasteRock">
                    <el-input v-model="entityForm.wasteRock" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="中煤" prop="midCoal">
                    <el-input v-model="entityForm.midCoal" :oninput="field.decimal" clearable>
                      <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:update`]||false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog> -->
    <el-dialog ref="dialogStatus" top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="closeDialog" :close-on-click-modal="false" :close-on-press-escape="false" width="980px">
      <laboratoryImport ref="excelref" optName='create' v-if="dialogFormVisible"
                        :exportExcelDialogVisible.sync="dialogFormVisible" @setSubmittext='setSubmittext'
                        :traitSettings="tableOption" :tableOption="model.tableOption" :entityForm="entityForm" Buttontext="save">
      </laboratoryImport>
    </el-dialog>

  </div>
</template>

<script>
import { getCustomerContract } from '@/api/quality'
import CustomerAndSupplierModel from '@/model/user/supplierAndCustomer'
import contractSellModel from '@/model/user/contractSell'
import contractBuyModel from '@/model/user/contractBuy'

import Mixins from '@/utils/mixins'
import { qualProductionOption, listQuery } from '@/const'
import Model from '@/model/coalQuality/qualProduction'
import productModel from '@/model/product/productList'
import { createMemoryField } from '@/utils/index'
export default {
  name: 'qualProduction',
  data() {
    return {
      curd: 'qualProduction',
      entityForm: { ...Model.model },
      model: Model,
      filterOption: { ...qualProductionOption },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        customerName: { required: true, message: '请选择客户', trigger: 'blur' }
      },
      contractActive: [],
      contractEntityFormActive: [],
      contractEntityForm: {
        options: []
      },
      contractParams: { query: '' },
      contract: {
        props: {
          label: 'customerName',
          value: 'customerId',
          children: 'contractSellList'
        },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      indicators: { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity },
      nameEntity: { options: [], active: '' },
      memoryEntity: { fields: {}, triggered: false },
      filters: {},
      fromDashboard: false,
      customerEntity: { options: [], active: [] },
      val: '',

      // excel配置
      excelConfig: { excel: Model.getImportConfig(), dialog: false },
      tableOption: {
        showPage: true,
        columns: [
          {
            label: '日期',
            title: '日期',
            prop: 'date',
            type: 'date',
            width: 120,
            validator: /[\s\S]/,
            correctFormat: true,
            dateFormat: 'YYYY-MM-DD'
          },
          {
            label: '采样时间',
            prop: 'time',
            title: '采样时间',
            type: 'time',
            validator: /[\s\S]/,
            dateFormat: 'HH:mm:ss'
          },
          {
            label: '客户',
            title: '客户',
            type: 'text',
            validator: /[\s\S]/,
            prop: 'customerName'
          },
          {
            label: '品名',
            prop: 'productName',
            title: '品名',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '挥发分',
            prop: 'vad',
            title: '挥发分',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '干燥基灰分',
            prop: 'ad',
            title: '干燥基灰分',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: 'Vdaf',
            prop: 'vdaf',
            title: 'Vdaf',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '硫分',
            prop: 'std',
            title: '硫分',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '粘结',
            prop: 'g',
            title: '粘结',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '特征',
            prop: 'crc',
            title: '特征',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '水分',
            prop: 'mt',
            title: '水分',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '回收',
            prop: 'recover',
            title: '回收',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '中煤',
            prop: 'midCoal',
            title: '中煤',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            label: '矸石',
            prop: 'wasteRock',
            title: '矸石',
            type: 'text',
            validator: /[\s\S]/
          },
          {
            noExport: true,
            label: '操作',
            prop: 'opt',
            slot: 'opt',
            isShow: true
          }
        ]
      },
      optName: 'create'
    }
  },
  computed: {
    otherHeight() {
      return this.perms[`${this.curd}:export`] ? '227' : '180'
    }
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_LIKES_date: to.params.date }
        vm.fromDashboard = true
      })
    } else {
      next()
    }
  },
  mixins: [Mixins],
  created() {
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'date',
        'time',
        'customerName',
        'contractName',
        'contractCode',
        'contractId',
        'receiverName',
        'productName',
        'productCode',
        'productId',
        'name'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getContract()
    this.getalllist() //获取供应商和客户列表
    // this.getName()
    // this.getCustomer()
    this.permsAction(this.curd)
  },
  watch: {
    // 'entityForm.aad'(v) {
    //   this.computerIndicators('ad')
    //   this.computerIndicators('vdaf')
    // },
    // 'entityForm.mad'(v) {
    //   this.computerIndicators('ad')
    //   this.computerIndicators('std')
    //   this.computerIndicators('vdaf')
    // },
    // 'entityForm.stad'(v) {
    //   this.computerIndicators('std')
    // },
    // 'entityForm.vad'(v) {
    //   this.computerIndicators('vdaf')
    // },
    // 'entityForm.productName'(name) {
    //   const item = this.nameEntity.options.find((item) => item.name === name)
    //   if (item) {
    //     this.entityForm.productName = name
    //     this.nameEntity.active = item.name
    //     this.entityForm.productCode = item.code
    //     // this.entityForm.productName = item.name
    //     this.entityForm.productId = item.id
    //   } else {
    //     this.nameEntity.active = ''
    //     this.entityForm.productCode = ''
    //     this.entityForm.productId = ''
    //     this.entityForm.productName = ''
    //   }
    // },
    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.entityForm.productName = name
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
        this.entityForm.productName = ''
      }
    },
    // 'entityForm.customerId'(id) {
    //   if (!id) return
    //   const item = this.customerEntity.options.find((item) => item.value == id)
    //   if (item) {
    //     this.customerEntity.active = item.label
    //   } else {
    //     this.customerEntity.active = []
    //   }
    // },
    'entityForm.customerName'(name) {
      if (!name) return
      const item = this.customerEntity.options.find((item) => item.label == name)
      if (item) {
        this.entityForm.customerId = item.value
        this.customerEntity.active = item.label
      } else {
        this.customerEntity.active = []
      }
    }

    // 在打开更新时watch监听然后通过过滤筛选触发contractEntityFormActive灌入显示数据
    // 'entityForm.contractId'(id) {
    //   if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
    //     const value = this.filterContractItem(id, 'contractEntityForm')
    //     this.contractEntityFormActive = value
    //   }
    // },
    // contractEntityFormActive(value) {
    //   if (!value || !Array.isArray(value)) return
    //   const { customerName: contractCode, customerId, name: customerName } = this.filterContractItem(value, 'contractEntityForm')
    //   this.entityForm.contractCode = contractCode
    //   this.entityForm.contractId = customerId
    //   this.entityForm.customerName = customerName
    // },
  },
  // 可写原方法覆盖mixins方法
  methods: {
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },
    closeDialog() {
      this.$refs.excelref.closeExportExcelDialog()
    },
    async setSubmittext(fList) {
      // fList.forEach((e, index) => {
      //   if (e.firstWeightDate) {
      //     var str = e.firstWeightDate
      //     str = str.replace(/\//g, '-')
      //     console.log(str)
      //     if (str.indexOf(' ') == -1) {
      //       console.log(str)
      //       var timearr = str.replace('', ':').replace(/\:/g, '-').split('-')
      //       console.log(timearr)
      //       var nian = timearr[1]
      //       var yue = timearr[2]
      //       var ri = timearr[3].substring(0, 2)
      //       var shi = timearr[3].slice(2)
      //       var fen = timearr[4]
      //       var miao = timearr[5]
      //       if (miao != undefined) {
      //         let date = nian + '-' + yue + '-' + ri
      //         let time = shi + ':' + fen + ':' + miao
      //         let datetime = date + ' ' + time
      //         console.log(datetime)
      //         e.firstWeightDate = datetime
      //       } else {
      //         let date = nian + '-' + yue + '-' + ri
      //         let time = shi + ':' + fen + ':00'
      //         let datetime = date + ' ' + time
      //         console.log(datetime)
      //         e.firstWeightDate = datetime
      //       }
      //     }
      //   }
      //   if (e.secondWeightDate) {
      //     var str = e.secondWeightDate
      //     str = str.replace(/\//g, '-')
      //     console.log(str)
      //     if (str.indexOf(' ') == -1) {
      //       console.log(str)
      //       var timearr = str.replace('', ':').replace(/\:/g, '-').split('-')
      //       // var timearr = e.secondWeightDate.replace('', ':').replace(/\:/g, '/').split('/')
      //       console.log(timearr)
      //       var nian = timearr[1]
      //       var yue = timearr[2]
      //       var ri = timearr[3].substring(0, 2)
      //       var shi = timearr[3].slice(2)
      //       var fen = timearr[4]
      //       var miao = timearr[5]
      //       if (miao != undefined) {
      //         let date = nian + '-' + yue + '-' + ri
      //         let time = shi + ':' + fen + ':' + miao
      //         let datetime = date + ' ' + time
      //         console.log(datetime)
      //         e.secondWeightDate = datetime
      //       } else {
      //         let date = nian + '-' + yue + '-' + ri
      //         let time = shi + ':' + fen + ':00'
      //         let datetime = date + ' ' + time
      //         console.log(datetime)
      //         e.secondWeightDate = datetime
      //       }
      //     }
      //   }
      // })

      const text = JSON.stringify(fList)
      // console.log(text)
      // const res = await Model.saveList({ text })
      //  if (!res) return this.$message({ showClose: true, message: '导入失败', type: 'error' })
      //   this.$message({ showClose: true, message: '导入成功', type: 'success' })
      //   this.excelConfig.dialog = false
      //   this.getList()
    },

    // querySearch(queryString, cb) {
    //   let list = this.customerEntity.options
    //   console.log(list)
    //   let results = queryString ? list.filter(this.createFilter(queryString)) : list
    //   cb(results)
    // },
    // createFilter(queryString) {
    //   return (restaurant) => restaurant.label.toString().includes(queryString.toString())
    // },
    handleInput(val) {
      // this.$emit('update:value', val)
      this.entityForm.customerName = val
    },
    // 合同改变同步entityForm
    handleCustomer({ value, label, type }) {
      this.entityForm.customerId = value
      this.entityForm.customerName = label
      this.entityForm.productName = ''
      this.getProductListfnc(value, type)
    },

    async getalllist() {
      //获取客户和供应商列表
      try {
        let options = []
        const { data } = await CustomerAndSupplierModel.findByKeyword({})
        if (data.length) {
          data.forEach((item) => {
            const { name: label, id: value, type: type } = item
            options.push({ label, value, type })
          })
        }
        this.customerEntity.options = options
      } catch (e) {}
    },

    async getProductListfnc(id, type) {
      if (type == 'S') {
        //contractBuy
        this.getSupplierProductName(id)
      }
      if (type == 'C') {
        this.getCustomerProductName(id)
      }
      // try {
      //   let options = []
      //   const { data } = await contractSellModel.getProductListfn({ customerId: id })
      //   console.log(data)
      //   this.nameEntity.options = data.map((item) => {
      //     return { name: item.name, id: item.id, code: item.code }
      //   })
      //   if (this.nameEntity.options.length == 1) {
      //     this.entityForm.productName = this.nameEntity.options[0].name
      //   }
      //   console.log(this.nameEntity.options.length)
      //   console.log(this.nameEntity.options)
      // } catch (e) {}
    },
    async getCustomerProductName(id) {
      try {
        let options = []
        const { data } = await contractSellModel.getProductListfn({ customerId: id })

        this.nameEntity.options = data.map((item) => {
          return { name: item.name, id: item.id, code: item.code }
        })
        if (this.nameEntity.options.length == 1) {
          this.entityForm.productName = this.nameEntity.options[0].name
        }
      } catch (e) {}
    },
    async getSupplierProductName(id) {
      try {
        const { data } = await contractBuyModel.getProductListfn({ supplierId: id })

        this.nameEntity.options = data.map((item) => {
          return { name: item.name, id: item.id, code: item.code }
        })
        if (this.nameEntity.options.length == 1) {
          this.entityForm.productName = this.nameEntity.options[0].name
        }
      } catch (e) {}
    },
    // async getCustomer() {
    //   try {
    //     let options = []
    //     const { data } = await CustomerModel.page({ size: 1000 })
    //     if (data.records.length) {
    //       data.records.forEach((item) => {
    //         const { name: label, id: value } = item
    //         options.push({ label, value })
    //       })
    //     }
    //     this.customerEntity.options = options
    //     console.log(options)
    //   } catch (e) {}
    // },

    // computerIndicators(name) {
    //   let { mad, aad, stad, vad } = this.entityForm
    //   switch (name) {
    //     case 'ad':
    //       if (!mad || !aad) {
    //         this.entityForm.ad = ''
    //       } else {
    //         this.entityForm.ad = (aad / (1 - mad / 100)).toFixed(2)
    //       }
    //       break
    //     case 'std':
    //       if (!mad || !stad) {
    //         this.entityForm.std = ''
    //       } else {
    //         this.entityForm.std = (stad / (1 - mad / 100)).toFixed(2)
    //       }
    //       break
    //     case 'vdaf':
    //       if (!mad || !vad || !aad) {
    //         this.entityForm.vdaf = ''
    //       } else {
    //         this.entityForm.vdaf = ((vad / (100 - aad - mad)) * 100).toFixed(2)
    //       }
    //       break
    //   }
    // },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      if (!this.memoryEntity.triggered) {
        this.nameEntity.active = ''
        this.contractEntityFormActive = []
        this.customerEntity.active = []
        this.entityForm = { ...this.model.model }
      } else {
        this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      }
    },

    handleNameEntity(val) {
      this.entityForm.productName = val
    },
    // 获取品名接口
    // async getName() {
    //   try {
    //     let { data } = await productModel.page({ ...this.listQuery, size: 999 })
    //     this.nameEntity.options = data.records.map((item) => {
    //       return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory }
    //     })
    //   } catch (error) {}
    // },

    // @重写方法-添加了合同搜索字段
    // handleFilter(filters) {
    //   this.filters = { ...filters }
    //   this.$refs[this.curd].searchChange({ ...this.listQuery, ...this.filters, filter_EQS_contractId: this.contractParams.query })
    // },
    // @重写方法-添加了clearContract方法
    // handleFilterReset() {
    //   this.clearContract()
    //   this.$refs[this.curd].searchChange()
    // },

    /**
     * 初始化数据选择下拉数据
     * contract用于指标合同下拉选取label显示name,value为id
     * contractEntityForm用于新增编辑下拉label显示code,value为id
     */
    async getContract() {
      const { data } = await getCustomerContract()
      this.contract.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { customerName: 'name', customerId: 'id' }
      })
      this.contractEntityForm.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { customerName: 'code', customerId: 'id' }
      })
    },
    /**
     * @用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    }

    // clearContract() {
    //   this.listQuery = { ...listQuery }
    //   this.fromDashboard = false
    //   this.filters = {}
    //   this.contractActive = []
    //   this.contractParams.query = ''
    //   this.indicators = { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity }
    //   return false
    // },
    /**
     * @找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     * @target {contract|contractEntityForm}<String>
     */
    // filterContractItem(value, target) {
    //   if (Array.isArray(value) && value.length) {
    //     // console.log(this[target].options, target)
    //     return this[target].options
    //       .find((item) => item.customerId === value[0])
    //       .contractSellList.find((item) => item.customerId === value[1])
    //   } else if (typeof value === 'string') {
    //     const val = []
    //     for (const list of this[target].options) {
    //       for (const item of list.contractSellList) {
    //         if (item.customerId === value) {
    //           val.push(list.customerId, value)
    //           return val
    //         }
    //       }
    //     }
    //   } else {
    //     return []
    //   }
    // },
    /**
     * @切换时更行指标并设置搜索关键字query
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    // handleContractChange(value) {
    //   if (!value.length) {
    //     this.clearContract()
    //     this.$refs[this.curd].searchChange()
    //     return
    //   }
    //   const item = this.filterContractItem(value, 'contract')
    //   const { cleanMt, cleanAd, cleanStd, cleanVdaf, procG } = item
    //   this.indicators = { mt: cleanMt, ad: cleanAd, std: cleanStd, vdaf: cleanVdaf, g: procG }
    //   this.contractParams.query = item.id
    //   this.$refs[this.curd].searchChange({ ...this.listQuery, ...this.filters, filter_EQS_contractId: this.contractParams.query })
    // },
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
// .opt-btnV {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
::v-deep .el-table__footer-wrapper tbody td {
  border: none !important;
}
.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  &-contract {
    width: 140px;
  }
  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;
    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }
    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;
      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}
::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}
::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  color: #fff;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 4) {
  border-left: none;
  border-right: none;
}
::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 3) {
  border-left: none;
  border-right: none;
}
</style>
