<template>
  <div class="app-container">
    <filter-table :options="filterOption" :showAdd="perms[`${curd}:save`]||false" :showImport="perms[`${curd}:addimport`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" @add="handleCreate" @filter="handleFilter"
                  @import="handleImpiort" @reset="handleFilterReset" />
    <s-curd :ref="curd" :actions="actions" :changeactions="changeactions" :list-query="listQuery" :model="model" :name="curd"
            :showSummary="true" otherHeight="130">
      <el-table-column slot="productName" align="center" label="品名" prop="productName" width="130">
        <!-- @click="handleWatchForm(scope.row)" -->
        <template slot-scope="scope"><span class="name"
                @click="handleUpdate(scope.row,'watch')">{{ scope.row.productName }}</span>
        </template>
      </el-table-column>
      <el-table-column slot="attachment" align="center" label="查看附件" prop="attachment" width="100">
        <template slot-scope="scope">
          <div class="attachmentV" style="margin: 0 auto;">
            <span style="cursor: pointer;color: blue;" @click="$refs.attachment.open(scope.row)">查看附件</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column slot="opt" align="center" fixed="right" label="操作" width="150">
        <template slot-scope="scope">
          <el-tag v-if="perms[`${curd}:view`]||false" class="opt-btn" color="#fa3131" @click="handleUpdate(scope.row,'watch')">查看
          </el-tag>
          <el-tag v-if="perms[`${curd}:update`]||false" class="opt-btn" color="#FF9639"
                  @click="handleUpdate(scope.row,'update')">编辑
          </el-tag>
          <el-tag v-if="perms[`${curd}:delete`]||false" class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)">
            删除
          </el-tag>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false"
               :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible" top="5vh">
      <el-form v-if="dialogFormVisible" ref="entityForm" :disabled="dialogStatus==='watch'" :model="entityForm" :rules="rules"
               :show-message="true" :status-icon="true" label-position="top" label-width="90px">
        <el-row>
          <div class="form-title">基本信息</div>
          <div class="form-layout">
            <el-row :gutter="80" type="flex">
              <el-col>
                <el-form-item label="日期" prop="date">
                  <date-select v-model="entityForm.date" />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="品名" prop="productName">
                  <select-down :list="nameEntity.options" :value.sync="nameEntity.active" @eventChange="handleNameEntity" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="form-title">化验指标</div>
          <div class="form-layout">
            <el-row>
              <el-col>
                <EditTable ref="editTable" v-model="editData" :columns="getColumns" @inited="handleEditTableInit"></EditTable>
              </el-col>
            </el-row>
          </div>
          <div class="form-title">附件信息</div>
          <div class="form-layout">
            <el-collapse :value="['1','2']">
              <el-collapse-item name="1" title="煤岩报告上传">
                <file-upload :list.sync="entityForm.attachmentList" />
              </el-collapse-item>
              <el-collapse-item name="2" title="CSR报告上传">
                <file-upload :list.sync="entityForm.attachmentCsrList" />
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-row>
      </el-form>
      <div v-if="dialogStatus!=='watch'" slot="footer" class="dialog-footer">
        <el-button v-if="perms[`${curd}:update`]||perms[`${curd}:save`]|| false" :loading="entityFormLoading"
                   class="dialog-footer-btns" type="primary" @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button class="dialog-footer-btns" @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
    <view-attachment ref="attachment" :request-method="model.getUploadList" />
  </div>
</template>
<script>
import productModel from '@/model/product/productList'
import FilterTable from '@/components/FilterTable/index.vue'
import { getCustomerContract, getContractSell } from '@/api/quality'
import Mixins from '@/utils/mixins'
import { qualSellOutOption, listQuery } from '@/const'
import Model from '@/model/coalQuality/qualSellOut'
import { createMemoryField, deepClone } from '@/utils/index'
import { HotTable, HotColumn } from '@handsontable/vue'
import { registerLanguageDictionary, zhCN } from 'handsontable/i18n'
import { registerAllModules } from 'handsontable/registry'
import 'handsontable/dist/handsontable.full.css'
import Handsontable from 'handsontable'
import FileUpload from '@/components/FileUpload/index.vue'
import ViewAttachment from '@/components/ViewAttachment/index.vue'

registerAllModules()
registerLanguageDictionary(zhCN)

class QualSellOutItem {
  constructor() {
    // 灰分
    this.ad = ''
    // 硫
    this.std = ''
    // 挥发分
    this.vdaf = ''
    // 粘结指数
    this.g = ''
    // 焦渣
    this.crc = ''
    // 全水
    this.mt = ''
    // 平均反射率
    this.macR0 = ''
    // 标准偏差
    this.macS = ''
    // 自动Y值
    this.smY = ''
    // 手动Y值
    this.y = ''
  }

}

export default {
  name: 'qualSellOut',
  data() {
    return {
      curd: 'qualSellOut',
      entityForm: { ...Model.model },
      model: Model,
      filterOption: { ...qualSellOutOption },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        productName: { required: true, message: '请输入品名', trigger: 'blur' }
        // contractId: { required: true, message: '请选择合同', trigger: 'blur' }
      },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'customerName', value: 'customerId', children: 'contractSellList' }
      },
      contractActive: [],
      contractParams: {
        query: ''
      },
      contract: {
        props: {
          label: 'customerName',
          value: 'customerId',
          children: 'contractSellList'
        },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      indicators: { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity },
      memoryEntity: { fields: {}, triggered: false },
      filters: {},
      fromDashboard: false,
      nameEntity: { options: [], active: '' },
      customerEntity: { options: [], active: [] },
      contractlist: [],
      selectList: [],
      optName: 'create',
      settingsV: {
        startRows: 1,
        data: [{}],
        columns: [
          { title: '硫分St,d', data: 'std', type: 'numeric', trimWhitespace: true, width: 65 },
          { title: '灰分Ad', data: 'ad', type: 'numeric', trimWhitespace: true, width: 65 },
          { title: '挥发分Vdaf', data: 'vdaf', type: 'numeric', trimWhitespace: true, width: 65 },
          { title: '水分Mt', data: 'mt', type: 'numeric', trimWhitespace: true, width: 65 },
          { title: 'G值', data: 'g', type: 'numeric', trimWhitespace: true, width: 65 },
          { title: 'Y值', data: 'y', type: 'numeric', trimWhitespace: true, width: 65 },
          { title: '特征CRC', data: 'crc', type: 'numeric', trimWhitespace: true, width: 65 },
          { title: 'X值', data: 'x', type: 'numeric', trimWhitespace: true, width: 65 }
        ],
        readOnly: this.disabled,
        className: 'custom-classname',
        language: zhCN.languageCode,
        copyPaste: true,
        rowHeaders: false,
        width: 'auto',
        height: 'auto',
        stretchH: 'all',
        manualColumnResize: true,
        manualRowResize: true,
        licenseKey: 'non-commercial-and-evaluation',
        formulas: {
          sheetName: 'Sheet1'
        },
        contextMenu: {
          items: {
            row_above: { name: '向上插入一行' },
            row_below: { name: '向下插入一行' },
            remove_row: { name: '删除行' }
          }
        }
      },
      editData: [
        ...Array(10)
          .fill(null)
          .map(() => new QualSellOutItem())
      ]
    }
  },
  mixins: [Mixins],
  created() {
    this.getContract()
    //获取客户列表
    // this.getCustomer()
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'contractId',
        'contractCode',
        'date',
        'senderName',
        'receiverName',
        'name',
        'coalCategory',
        'truckCount',
        'sendWeight',
        'receiveWeight',
        'wayCost'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getName()
    this.getContractDR()
    this.permschange(this.curd)
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_LIKES_date: to.params.date }
        vm.fromDashboard = true
      })
    } else {
      next()
    }
  },
  watch: {
    'entityForm.date'(date) {
      if (this.dialogStatus === 'create' && this.entityForm.contractId) {
        this.getBookkeeping(this.entityForm.contractId, date)
      }
    },
    'entityForm.contractId'(id) {
      if (this.dialogStatus === 'create' && this.entityForm.date) {
        this.getBookkeeping(id, this.entityForm.date)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      // this.entityForm.name = form.productName
      this.entityForm.contractCode = form.customerName
      this.entityForm.contractId = form.customerId
      this.entityForm.senderName = form.secondParty
      this.entityForm.receiverName = form.firstParty
      this.entityForm.coalCategory = form.coalType
      // this.nameEntity.active = form.productName
      // this.entityForm = { ...this.entityForm, productName: form.productName }
    },
    'entityForm.sendWeight'(v) {
      this.computeWayCostAcc()
    },
    'entityForm.receiveWeight'(v) {
      this.computeWayCostAcc()
    },
    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.entityForm.name = name
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
      }
    }
  },
  computed: {
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    },
    getColumns() {
      const dialogStatus = this.dialogStatus
      const len = this.editData.length
      const watchArr =
        dialogStatus === 'watch'
          ? [
            {
              data: null,
              title: '序号',
              width: 30,
              //只读
              readOnly: true,
              renderer: function (instance, td, row, col, prop, value, cellProperties) {
                Handsontable.renderers.TextRenderer.apply(this, arguments)
                const index = row + 1
                td.style.textAlign = 'center'
                td.innerHTML = index == len ? '平均' : index
              }
            }
          ]
          : []
      return [
        ...watchArr,
        {
          title: '灰分',
          width: 60,
          data: 'ad',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '硫分',
          width: 60,
          data: 'std',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '挥发分',
          width: 60,
          data: 'vdaf',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '粘结指数',
          width: 60,
          data: 'g',
          type: 'numeric'
        },
        {
          title: '焦渣',
          width: 60,
          data: 'crc',
          type: 'numeric'
        },
        {
          title: '全水',
          width: 60,
          data: 'mt',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '平均反射率',
          width: 60,
          data: 'macR0',
          type: 'numeric',
          numericFormat: {
            pattern: '0.000'
          }
        },
        {
          title: '标准偏差',
          width: 60,
          data: 'macS',
          type: 'numeric',
          numericFormat: {
            pattern: '0.000'
          }
        },
        {
          title: '自动Y值',
          width: 60,
          data: 'smY',
          type: 'numeric',
          numericFormat: {
            pattern: '0.0'
          }
        },
        {
          title: '手动Y值',
          width: 60,
          data: 'y',
          type: 'numeric',
          numericFormat: {
            pattern: '0.0'
          }
        }
      ]
    }
  },
  components: {
    ViewAttachment,
    FileUpload,
    HotTable,
    HotColumn,
    FilterTable
  },
  // 可写原方法覆盖mixins方法
  methods: {
    async handleEditTableInit(instance) {
      console.log(this.dialogStatus)
      const getSetting = () => {
        if (this.dialogStatus === 'watch') {
          return {
            readOnly: true,
            // height: '350',
            contextMenu: undefined,
            data: this.editData,
            rowHeaders: false
          }
        }
        return {
          contextMenu: {
            items: {
              row_above: { name: '向上插入一行' },
              row_below: { name: '向下插入一行' },
              remove_row: { name: '删除行' },
              clear_custom: {
                name: '清空所有单元格数据',
                callback() {
                  this.clear()
                }
              }
            }
          },
          data: this.editData
        }
      }
      instance.updateSettings({
        columns: this.getColumns,
        ...getSetting(),
        async cells(row, col) {
        }
      })
    },
    async saveEntityForm(entityForm) {
      let valid
      try {
        this.entityFormLoading = true
        valid = await this.$refs[entityForm].validate()
        console.log(valid, 'val', this.entityForm)
        const status = await this.$refs['editTable'].validate()
        if (!status) return
        const qualSellOutItemList = this.getFormatEditData(this.editData, this.getColumns, true)
        const form = {
          id: this.dialogStatus === 'create' ? undefined : this.entityForm.id,
          ...this.entityForm,
          qualSellOutItemList
        }
        // await Model.save({ ...form })
        const res = await this.model.save(form)
        if (res) {
          this.$message({ message: '保存成功', type: 'success' })
          this.dialogFormVisible = false
          this.getList()
          this.resetVariant()
        }
      } catch (e) {
        console.log(e, 'e')
      } finally {
        this.entityFormLoading = false
      }
    },
    /**
     * 过滤掉空数据，校验数据
     * @param target 提供的车牌列表
     * @param isSubmit 是否提交 主要用于控制是否校验车牌号 true时抛出Error
     */
    getFormatEditData(target = [], columns, require = true) {
      const getExcludeDirtyList = (target = []) => {
        const list = deepClone(target)
        list.forEach((v) => {
          for (const key in v) {
            if ([null, ''].includes(v[key])) {
              delete v[key]
            }
          }
        })
        return list
      }
      const list = getExcludeDirtyList(target)
      const data = []
      // 获取需要校验的字段
      const validateList = columns
        .filter((col) => col.formRequire)
        .map((v) => {
          return { key: v.data, msg: v.title }
        })

      list.forEach((item, index) => {
        // 过滤掉空数据 但在编辑的时候会保留id所以不能被删除只能修改当前这条数据

        if (Object.keys(item).length) {
          validateList.forEach((v) => {
            if (!item[v.key]) {
              this.$message(`表格第${index + 1}行${v.msg}不能为空`)
              throw new Error('数据不能为空')
            }
          })
          // if (Object.keys(item).length == 1 && item.type) {
          //     this.$message(`表格第${index + 1}行不能只填写类型`)
          //     throw new Error('数据不能为空1')
          // }

          data.push(item)
        }
      })

      if (!data.length && require) {
        this.$message({ message: '表格至少添加一条数据', type: 'warning' })
        throw new Error('数据不能为空')
      }

      return data
    },
    //批量删除,
    async BatchDelete(selectList) {
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      let newarry = []
      for (var i = 0; i < selectList.length; i++) {
        let obj = {}
        obj = selectList[i].id
        newarry.push(obj)
      }
      let parm = {
        idList: newarry
      }

      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // console.log('确认')
          this.deletefn(parm)
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },
    async deletefn(parm) {
      let { data } = await Model.savebatchChange({ ...parm })
      if (data) {
        this.$message({ type: 'success', message: '删除成功!' })
        this.getList()
      }
    },
    async handleUpdate(item, status) {
      try {
        await this.$nextTick()
        const { data } = await this.model.getUploadList(item.id)
        this.entityForm = { ...data }
        this.editData = [...data.qualSellOutItemList]
        if (status == 'watch') {
          this.editData.push({
            std: data.std,
            ad: data.ad,
            vdaf: data.vdaf,
            mt: data.mt,
            g: data.g,
            y: data.y,
            crc: data.crc,
            x: data.x,
            cri: data.cri,
            csr: data.csr,
            macR0: data.macR0,
            macS: data.macS,
            smY: data.smY
          })
        }
      } catch (e) {
      }
      this.dialogStatus = status
      this.dialogFormVisible = true
    },
    okbtnV() {
      this.$refs.excelref.okbtn()
    },
    closeExportExcelDialogV() {
      this.$refs.excelref.closeExportExcelDialog()
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) {
      }
    },
    handleWatchForm(item) {
      this.customerEntity.active = item.customerName
      // this.getProductListfnc(item.customerId)
      setTimeout(() => {
        this.entityForm = { ...item }
      }, 1000)
      this.dialogStatus = 'watch'
      this.dialogFormVisible = true
    },
    handleNameEntity(val) {
      this.entityForm = { ...this.entityForm, productName: val }
    },
    // 计算涂耗累计Acc
    computeWayCostAcc() {
      let { receiveWeight, sendWeight } = this.entityForm
      receiveWeight = receiveWeight * 1
      sendWeight = sendWeight * 1
      let wayCost = (((sendWeight - receiveWeight) / sendWeight) * 100).toFixed(2)
      if (!Number.isFinite(wayCost * 1) || Number.isNaN(wayCost * 1)) {
        this.entityForm.wayCost = 0
      } else {
        this.entityForm.wayCost = wayCost
      }
    },
    async getBookkeeping(id, date) {
      if (!id) {
        this.entityForm.sendWeight = 0
        this.entityForm.receiveWeight = 0
        return
      }
      const { sendWeight, receiveWeight } = await this.model.bookkeeping(id, date)
      this.entityForm.sendWeight = sendWeight
      this.entityForm.receiveWeight = receiveWeight
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.uploadList = [] // 清空下载列表
      this.customerEntity.active = []
      this.nameEntity.active = []
      this.contractEntity.active = []
      this.entityForm = { ...this.model.model }
      this.entityForm.attachmentList = []
      this.settingsV.data = [{}]
      this.editData = [
        ...Array(10)
          .fill(null)
          .map(() => new QualSellOutItem())
      ]
      // 判断当前是否没有保存取消 如果取消则初始化选择框 否则表单基础功能被记忆新增时会记忆
      // if (!this.memoryEntity.triggered) {
      //   this.contractEntity.active = []
      //   this.nameEntity.active = []
      //   this.entityForm = { ...this.model.model }
      // } else {
      //   this.entityForm = { ...this.model.model, ...this.memoryEntity.fields }
      // }
    },
    // @重写方法-添加了合同搜索字段
    handleFilter(filters) {
      this.filters = { ...filters }
      this.$refs[this.curd].searchChange({
        ...this.listQuery, ...filters,
        filter_EQS_contractId: this.contractParams.query
      })
    },
    // @重写方法-添加了clearContract方法
    handleFilterReset() {
      this.clearContract()
      this.$refs[this.curd].searchChange()
    },
    /**
     * 初始化数据选择下拉数据
     * contract用于指标合同下拉选取label显示name,value为id
     * contractEntityForm用于新增编辑下拉label显示code,value为id
     */
    async getContract() {
      const { data } = await getCustomerContract()
      this.contract.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { customerName: 'name', customerId: 'id' }
      })
      this.contractEntity.options = this.formatContract({ data, key: { customerName: 'displayName', customerId: 'id' } })
    },
    // 导入时获取的合同列表
    async getContractDR() {
      const { data } = await getContractSell()
      let newarry = []
      for (var i = 0; i < data.records.length; i++) {
        newarry.push(data.records[i].code)
        // newarry.push(data.records[i].customerName + '/' + data.records[i].code)
      }
      this.contractlist = newarry
    },
    /**
     * 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractSellList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },

    clearContract() {
      this.listQuery = { ...listQuery }
      this.fromDashboard = false
      this.filters = {}
      this.contractActive = []
      this.contractParams.query = ''
      this.indicators = { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity }
      return false
    },
    /**
     * 找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     * @target {contract|contractEntityForm}<String>
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.customerId === value[0])
          .contractSellList.find((item) => item.customerId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractSellList) {
            if (item.customerId === value) {
              val.push(list.customerId, value)
              // console.log(val, 1111)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    /**
     * 切换时更行指标并设置搜索关键字query
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    handleContractChange(value) {
      if (!value.length) {
        this.clearContract()
        this.$refs[this.curd].searchChange()
        return
      }
      const item = this.filterContractItem(value, 'contract')
      const { cleanMt, cleanAd, cleanStd, cleanVdaf, procG } = item
      this.indicators = { mt: cleanMt, ad: cleanAd, std: cleanStd, vdaf: cleanVdaf, g: procG }
      this.contractParams.query = item.id
      this.$refs[this.curd].searchChange({
        ...this.listQuery, ...this.filters,
        filter_EQS_contractId: this.contractParams.query
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;

  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      padding: 6px 10px;
      margin-right: 5px;
      font-size: 12px;
      position: relative;

      &-link {
        font-size: 12px;
        opacity: 0.8;
      }

      .icon {
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 9;
        top: 0;
        flex: 0;
      }
    }
  }
}

// .opt-btn {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
// .opt-btnV {
//   margin: 5px 10px 0 0;
//   height: 26px;
//   line-height: 26px;
// }
::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}

::v-deep .el-input.is-disabled .el-input__inner {
  color: #606266;
}

.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);

  &-contract {
    width: 140px;
  }

  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;

    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }

    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;

      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}

::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  //color: #fff;
}

// ::v-deep .el-table__fixed-footer-wrapper tbody td {
//   border-left: none;
//   border-right: none;
// }

::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 5) {
  border-left: none;
  border-right: none;
}
</style>
