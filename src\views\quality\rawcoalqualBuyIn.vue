<template>
  <div class="app-container">
    <filter-table :options="filterOption" :showAdd="perms[`${curd}:save`]||false" :showImport="false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" @add="handleCreate" @filter="handleFilter"
                  @import="handleImpiort" @reset="handleFilterReset" />
    <s-curd :ref="curd" :actions="actions" :changeactions="changeactions" :list-query="listQuery" :model="model" :name="curd"
            :showSummary="true" otherHeight="130" @selectItem="selectItem">
      <el-table-column slot="productName" align="center" label="品名" prop="productName" width="130">
        <template slot-scope="scope"><span class="name"
                @click="handleUpdate(scope.row,'watch')">{{ scope.row.productName }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="!perms[`${curd}:test`]" slot="contractCode" align="center" label="合同编号" min-width="130"
                       prop="contractCode">
        <template slot-scope="scope">{{ scope.row.contractCode }}</template>
      </el-table-column>
      <el-table-column slot="indicators" align="center" class-name="index" label="1.4">
        <el-table-column align="center" label="灰分" prop="ad" width="100" />
        <el-table-column align="center" label="硫分" prop="std" width="100" />
        <el-table-column align="center" label="挥发分" prop="vdaf" width="100" />
        <el-table-column align="center" label="粘结指数" prop="g" width="100" />
        <el-table-column align="center" label="焦渣" prop="crc" width="100" />
        <el-table-column align="center" label="水分" prop="macS" width="100" />
        <el-table-column align="center" label="Y值" prop="y" width="100" />
      </el-table-column>
      <el-table-column slot="mincol" align="center" class-name="index" label="1.55">
        <el-table-column align="center" label="灰分" prop="rawAd" width="100" />
        <el-table-column align="center" label="硫分" prop="rawStd" width="100" />
        <el-table-column align="center" label="挥发分" prop="rawVdaf" width="100" />
        <el-table-column align="center" label="粘结指数" prop="rawG" width="100" />
        <el-table-column align="center" label="焦渣" prop="rawCrc" width="100" />
        <el-table-column align="center" label="水分" prop="rawMacS" width="100" />
        <el-table-column align="center" label="Y值" prop="rawY" width="100" />
      </el-table-column>
      <el-table-column slot="attachment" align="center" label="煤岩报告附件" prop="attachment" width="100">
        <template slot-scope="scope">
          <div class="attachmentV" style="margin: 0 auto;">
            <span style="cursor: pointer;color: blue;" @click="$refs.attachment.open(scope.row)">查看附件</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column slot="opt" align="center" fixed="right" label="操作" width="150">
        <template slot-scope="scope">
          <el-tag v-if="perms[`${curd}:view`]||false" class="opt-btn" color="#fa3131" @click="handleUpdate(scope.row,'watch')">查看
          </el-tag>
          <el-tag v-if="perms[`${curd}:update`]||false" class="opt-btn" color="#FF9639"
                  @click="handleUpdate(scope.row,'update')">编辑
          </el-tag>
          <el-tag v-if="perms[`${curd}:delete`]||false" class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)">
            删除
          </el-tag>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false"
               :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible" top="5vh">
      <el-form v-if="dialogFormVisible" ref="entityForm" :disabled="dialogStatus==='watch'" :model="entityForm" :rules="rules"
               :show-message="true" :status-icon="true" label-position="top" label-width="90px">
        <el-row>
          <div class="form-title">基本信息</div>
          <div class="form-layout">
            <el-row :gutter="80" type="flex">
              <el-col>
                <el-form-item label="日期" prop="date">
                  <date-select v-model="entityForm.date" />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="品名" prop="productName">
                  <select-down :list="nameEntity.options" :value.sync="nameEntity.active" @eventChange="handleNameEntity" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="form-titlV form-warp">
            <span style="position: relative;top:10px">指标</span>
            <el-button type="export-all" @click="handleAdd">插入一行</el-button>
          </div>
          <div class="form-layout" style="margin-bottom:20px">
            <el-row type="flex">
              <el-col>
                <hot-table ref="hotV" :settings="settingsV" class="hot-table" height="250" width="100%"></hot-table>
              </el-col>
            </el-row>
          </div>
          <div class="form-title">附件信息</div>
          <div class="form-layout">
            <el-collapse :value="['1','2']">
              <el-collapse-item name="1" title="煤岩报告上传">
                <file-upload :list.sync="entityForm.attachmentList" />
              </el-collapse-item>
              <el-collapse-item name="2" title="CSR报告上传">
                <file-upload :list.sync="entityForm.attachmentCsrList" />
              </el-collapse-item>
            </el-collapse>
          </div>

        </el-row>
      </el-form>
      <div v-if="dialogStatus!=='watch'" slot="footer" class="dialog-footer">
        <el-button v-if="perms[`${curd}:update`]||perms[`${curd}:save`]|| false" :loading="entityFormLoading"
                   class="dialog-footer-btns" type="primary" @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button class="dialog-footer-btns" @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
    <view-attachment ref="attachment" :request-method="model.getUploadList" />
  </div>
</template>
<script>
import productModel from '@/model/product/productList'
import FilterTable from '@/components/FilterTable/index.vue'
import { chooseContract, contractList, getContractBuy } from '@/api/quality'
import Mixins from '@/utils/mixins'
import { listQuery, rawcoalqualBuyInFilterOption } from '@/const'
import Model from '@/model/coalQuality/rawcoalqualBuyIn'
import { createMemoryField } from '@/utils/index'
import { HotColumn, HotTable } from '@handsontable/vue'
import { registerLanguageDictionary, zhCN } from 'handsontable/i18n'
import { registerAllModules } from 'handsontable/registry'
import 'handsontable/dist/handsontable.full.css'
import FileUpload from '@/components/FileUpload/index.vue'
import ViewAttachment from '@/components/ViewAttachment/index.vue'

registerAllModules()
registerLanguageDictionary(zhCN)

class CoalRawQualBuyInItem {
  constructor() {
    this.time = ''
    this.haRaw = ''
    this.haMid = ''
    this.haRock = ''
    this.ad = ''
    this.std = ''
    this.vdaf = ''
    this.g = ''
    this.crc = ''
    this.macS = ''
    this.y = ''
    this.rawAd = ''
    this.rawStd = ''
    this.rawVdaf = ''
    this.rawG = ''
    this.rawCrc = ''
    this.rawMacS = ''
    this.rawY = ''
  }

}

export default {
  name: 'rawcoalqualBuyIn',
  mixins: [Mixins],
  components: { ViewAttachment, FileUpload, HotTable, HotColumn, FilterTable },
  data() {
    return {
      curd: 'rawcoalqualBuyIn',
      model: Model,
      filterOption: { ...rawcoalqualBuyInFilterOption },
      rules: {
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        time: { required: true, message: '请选择采样时间', trigger: 'blur' },
        productName: { required: true, message: '请输入品名', trigger: 'blur' },
        plateNumber: { required: true, message: '请输入车牌号', trigger: 'blur' }
      },
      contractEntity: {
        active: [],
        options: [],
        props: { label: 'supplierName', value: 'supplierId', children: 'contractBuyList' }
      },
      contractParams: { query: '' },
      contractActive: [],
      entityForm: { ...Model.model },
      contract: {
        props: {
          label: 'supplierName',
          value: 'supplierId',
          children: 'contractBuyList'
        },
        options: [],
        placeholder: '请选择合同',
        clearable: true
      },
      indicators: { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity },
      memoryEntity: { fields: {}, triggered: false },
      filters: {},
      fromDashboard: false,
      supplierEntity: { value: '', options: [] },
      nameEntity: { options: [], active: '' },
      contractlistV: [],
      uploadData: { refId: '', refType: 'CoalSample' },
      selectList: [],
      optName: 'create',
      settingsV: {
        startRows: 1,
        data: Array(10).fill(null).map(item => new CoalRawQualBuyInItem()),
        columns: [
          { title: '时间', data: 'time', type: 'text', trimWhitespace: true, width: 65 },
          { title: '含精', data: 'haRaw', type: 'numeric', trimWhitespace: true, width: 65 },
          {
            title: '含中',
            data: 'haMid',
            type: 'numeric',
            numericFormat: {
              pattern: '0.00'
            },
            trimWhitespace: true,
            width: 65
          },
          {
            title: '含矸',
            data: 'haRock',
            type: 'numeric',
            numericFormat: {
              pattern: '0.00'
            },
            trimWhitespace: true,
            width: 65
          },
          {
            title: '灰分',
            data: 'ad',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '硫分',
            data: 'std',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '挥发分',
            data: 'vdaf',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '粘结指数',
            data: 'g',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0'
            }
          },
          {
            title: '焦渣',
            data: 'crc',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0'
            }
          },
          {
            title: '水分',
            data: 'macS',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0'
            }
          },
          {
            title: 'Y值',
            data: 'y',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.0'
            }
          },
          {
            title: '灰分',
            data: 'rawAd',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '硫分',
            data: 'rawStd',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '挥发分',
            data: 'rawVdaf',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.00'
            }
          },
          {
            title: '粘结指数',
            data: 'rawG',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0'
            }
          },
          {
            title: '焦渣',
            data: 'rawCrc',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0'
            }
          },
          {
            title: '水分',
            data: 'rawMacS',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0'
            }
          },
          {
            title: 'Y值',
            data: 'rawY',
            type: 'numeric',
            trimWhitespace: true,
            width: 65,
            numericFormat: {
              pattern: '0.0'
            }
          }
        ],
        nestedHeaders: [
          [
            '',
            '',
            '',
            '',
            {
              label: '1.4',
              colspan: 7
            },
            {
              label: '1.4-1.8',
              colspan: 7
            }
          ],
          [
            '时间',
            '含精',
            '含中',
            '含矸',
            '灰分',
            '硫分',
            '挥发分',
            '粘结指数',
            '焦渣',
            '水分',
            'Y值',
            '灰分',
            '硫分',
            '挥发分',
            '粘结指数',
            '焦渣',
            '水分',
            'Y值'
          ]
        ],
        readOnly: this.disabled,
        className: 'custom-classname',
        language: zhCN.languageCode,
        copyPaste: true,
        width: 'auto',
        height: 'auto',
        stretchH: 'all',
        manualColumnResize: true,
        manualRowResize: true,
        licenseKey: 'non-commercial-and-evaluation',
        formulas: {
          sheetName: 'Sheet1'
        },
        rowHeaders: false,
        contextMenu: {
          items: {
            row_above: { name: '向上插入一行' },
            row_below: { name: '向下插入一行' },
            remove_row: { name: '删除行' }
          }
        }

      },
      drawer: {
        visible: false,
        list: [],
        preview: []
      },
      drawerV: {
        visible: false,
        list: [],
        preview: []
      },
      emptyImgPath: require(`@/assets/empty_img.jpg`),
      uploadListV: [],
      limitNumV: 1000,
      uploadDataV: { refId: '', refType: 'CoalSample' }
    }
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.params).length && from.name === 'Dashboard') {
      next((vm) => {
        vm.listQuery = { ...vm.listQuery, filter_LIKES_date: to.params.date }
        vm.fromDashboard = true
      })
    } else {
      next()
    }
  },
  computed: {
    srcList() {
      return this.uploadList.map((item) => {
        return item.uri
      })
    },
    size() {
      return this.uploadList.length
    },
    sizeV() {
      return this.uploadListV.length
    }
  },
  async created() {
    this.getContract()
    this.getContractList()
    this.memoryEntity.fields = createMemoryField({
      fields: [
        'contractId',
        'contractCode',
        'date',
        'senderName',
        'receiverName',
        'name',
        'productName',
        'coalCategory',
        'truckCount',
        'sendWeight',
        'supplierName',
        'supplierId',
        'receiveWeight',
        'wayCost'
      ],
      target: this.entityForm,
      init: { date: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd') }
    })
    this.getName()
    this.getContractDR()
    this.permschange(this.curd)
  },
  watch: {
    'entityForm.date'(date) {
      if (this.dialogStatus === 'create' && this.entityForm.contractId) {
        this.getBookkeeping(this.entityForm.contractId, date)
      }
    },
    'entityForm.contractId'(id) {
      if (this.dialogStatus === 'create' && this.entityForm.date) {
        this.getBookkeeping(id, this.entityForm.date)
      }
      if (this.dialogStatus === 'update' || this.dialogStatus === 'watch') {
        const value = this.filterContractItem(id, 'contractEntity')
        this.contractEntity.active = value
      }
    },
    'contractEntity.active'(value) {
      if (!value || !Array.isArray(value)) return
      const form = this.filterContractItem(value, 'contractEntity')
      this.entityForm.contractCode = form.supplierName
      this.entityForm.contractId = form.supplierId
      this.entityForm.senderName = form.firstParty
      this.entityForm.receiverName = form.secondParty
      this.entityForm.coalCategory = form.coalType
      if (this.dialogStatus == 'watch') {
        let cleanMt = form.cleanMt ? form.cleanMt : 0
        let cleanAd = form.cleanAd ? form.cleanAd : 0
        let cleanStd = form.cleanAd ? form.cleanStd : 0
        let cleanVdaf = form.cleanVdaf ? form.cleanVdaf : 0
        let crc = form.crc ? form.crc : 0
        let procG = form.procG ? form.procG : 0
        let procY = form.procY ? form.procY : 0
        let procX = form.procX ? form.procX : 0
        let recovery = form.recovery ? form.recovery : 0
        let qualCsr = form.qualCsr ? form.qualCsr : 0
        let macR0 = form.macR0 ? form.macR0 : 0
        let macS = form.macS ? form.macS : 0

        this.entityForm.ContractIndicators =
          'Mt: ' +
          cleanMt +
          '，Ad: ' +
          cleanAd +
          '，Std: ' +
          cleanStd +
          '，Vdaf: ' +
          cleanVdaf +
          '，特征(crc): ' +
          crc +
          '，粘结(G): ' +
          procG +
          '，胶质层Y: ' +
          procY +
          '，X指标: ' +
          procX +
          '，回收率 ' +
          recovery +
          '，热强度(CSR): ' +
          qualCsr +
          '，岩相(R0): ' +
          macR0 +
          '，标准差S: ' +
          macS
      } else {
        this.entityForm.ContractIndicators = ''
      }
    },
    'entityForm.productName'(name) {
      const item = this.nameEntity.options.find((item) => item.name === name)
      if (item) {
        this.entityForm.productName = name
        this.entityForm.name = name
        this.nameEntity.active = item.name
        this.entityForm.productCode = item.code
        this.entityForm.productId = item.id
        this.getActualWeightSumfn(item.id)
      } else {
        this.nameEntity.active = ''
        this.entityForm.productCode = ''
        this.entityForm.productId = ''
      }
    }
  },
  methods: {
    async getActualWeightSumfn(productId) {
      let { data } = await this.model.getActualWeightSum(this.entityForm.date, productId)
      this.entityForm = { ...this.entityForm, actualWeight: data }
    },
    async saveEntityForm(formName) {
      /** @type {  import('handsontable/core').default} */
      let hotInstance = this.$refs.hotV.hotInstance
      let validateResV = await new Promise((resolve, reject) => {
        this.$refs.hotV.hotInstance.validateCells((res) => {
          resolve(res)
        })
      })
      if (!validateResV) {
        this.$message({ showClose: true, message: '数据格式不正确，请检查！', type: 'warning' })
        return false
      }
      let coalRawQualBuyInItemList = hotInstance.getSourceData()
        .filter((item, index) => !hotInstance.isEmptyRow(index))
      if (coalRawQualBuyInItemList.length <= 0) {
        this.$message({ showClose: true, message: '表格不能为空', type: 'warning' })
        return
      }
      this.entityForm = { ...this.entityForm, coalRawQualBuyInItemList }
      const valid = await this.$refs[formName].validate()
      if (valid) {
        this.entityFormLoading = true
        const res = await Model.save({ ...this.entityForm })
        this.entityFormLoading = false
        if (res) {
          this.getList()
          this.resetVariant()
          this.dialogFormVisible = false
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        }
      }
    },
    async handlePreviewAttachment(row) {
      const res = await this.model.getUploadList(row.id)
      this.drawer.list = res.data.attachmentList
      this.drawer.preview = res.data.attachmentList.map((item) => item.uri)
      this.drawer.visible = true
    },
    async handlePreviewAttachmentV(row) {
      const res = await this.model.getUploadList(row.id)
      this.drawerV.list = res.data.attachmentCsrList
      this.drawerV.preview = res.data.attachmentCsrList.map((item) => item.uri)
      this.drawerV.visible = true
    },
    //CSR报告上传
    async handleRemoveUploadV(index) {
      const { id } = this.uploadListV[index]
      this.$refs.uploadV.attachmentDelete({ id, index })
    },
    handleUploadSuccessV(res) {
      this.uploadListV = [...this.uploadListV, res]
      // 上传完毕后更新展示的列表和把新增的附件id塞入到
      this.entityForm.attachmentCsrList.push({ id: res.id })
    },
    handleUploadDeleteV({ status, index }) {
      this.uploadListV.splice(index, 1)
    },
    handleCloseDrawer() {
      this.drawer.visible = false
      this.drawer.list = []
      this.drawer.preview = []

      this.drawerV.visible = false
      this.drawerV.list = []
      this.drawerV.preview = []
    },
    handleAdd() {
      this.settingsV.data.push(new CoalRawQualBuyInItem())
    },
    // 导入时获取的采购合同列表
    async getContractDR() {
      const { data } = await getContractBuy()
      let newarry = []
      for (var i = 0; i < data.records.length; i++) {
        newarry.push(data.records[i].code)
      }
      this.contractlistV = newarry
    },
    //批量删除,
    async BatchDelete(selectList) {
      if (selectList.length === 0) {
        this.$notify.error('请选择一条记录')
        return false
      }
      let newarry = []
      for (var i = 0; i < selectList.length; i++) {
        let obj = {}
        obj = selectList[i].id
        newarry.push(obj)
      }
      let parm = {
        idList: newarry
      }
      this.$confirm('此操作将永久删除该条信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          this.deletefn(parm)
        })
        .catch(() => this.$message({ type: 'info', message: '已取消删除' }))
    },
    async deletefn(parm) {
      let { data } = await Model.savebatchChange({ ...parm })
      if (data) {
        this.$message({ type: 'success', message: '删除成功!' })
        this.getList()
      }
    },
    okbtnV() {
      this.$refs.excelref.okbtn()
    },
    closeExportExcelDialogV() {
      this.$refs.excelref.closeExportExcelDialog()
    },
    selectItem(list) {
      //批量选择的数据
      this.selectList = list
    },
    async handleUpdate(item, type) {
      if (type == 'update') {
        this.dialogStatus = 'update'
      } else {
        this.dialogStatus = 'watch'
      }
      // 上传所需要的配置
      this.uploadData.refId = item.id
      this.settingsV.data = [{}]
      const { data } = await this.model.getUploadList(item.id)
      this.uploadList = data.attachmentList
      this.uploadListV = data.attachmentCsrList
      if (type == 'watch') {
        let obj = {
          time: '平均值',
          haRaw: data.haRaw,
          haMid: data.haMid,
          haRock: data.haRock,
          ad: data.ad,
          std: data.std,
          vdaf: data.vdaf,
          g: data.g,
          crc: data.crc,
          macS: data.macS,
          y: data.y,
          rawAd: data.rawAd,
          rawStd: data.rawStd,
          rawVdaf: data.rawVdaf,
          rawG: data.rawG,
          rawCrc: data.rawCrc,
          rawMacS: data.rawMacS,
          rawY: data.rawY
        }
        data.coalRawQualBuyInItemList.push(obj)
      }
      this.entityForm = { ...data }
      this.settingsV.data = data.coalRawQualBuyInItemList
      this.entityForm = { ...data }
      this.dialogFormVisible = true
    },
    handleNameEntity(val) {
      this.entityForm = { ...this.entityForm, productName: val }
    },
    async getContractList() {
      const { data } = await contractList()
      if (data.length) this.supplierEntity.options = data
    },
    handleSupplier({ value, label }) {
      this.entityForm.productName = ''
      this.nameEntity.active = ''
      this.entityForm = { ...this.entityForm, supplierId: value, supplierName: label }
    },
    // 获取品名接口
    async getName() {
      try {
        let { data } = await productModel.page({ ...this.listQuery, size: 999 })
        this.nameEntity.options = data.records.map((item) => {
          return { name: item.name, id: item.id, code: item.code, coalCategory: item.coalCategory, type: item.type }
        })
      } catch (error) {
      }
    },
    // 计算涂耗累计Acc
    computeWayCostAcc() {
      let { receiveWeight, sendWeight } = this.entityForm
      receiveWeight = receiveWeight * 1
      sendWeight = sendWeight * 1
      let wayCost = (((sendWeight - receiveWeight) / sendWeight) * 100).toFixed(2)
      if (!Number.isFinite(wayCost * 1) || Number.isNaN(wayCost * 1)) {
        this.entityForm.wayCost = 0
      } else {
        this.entityForm.wayCost = wayCost
      }
    },
    async getBookkeeping(id, date) {
      if (!id) {
        this.entityForm.sendWeight = 0
        this.entityForm.receiveWeight = 0
        return
      }
      const { sendWeight, receiveWeight } = await this.model.bookkeeping(id, date)
      this.entityForm.sendWeight = sendWeight
      this.entityForm.receiveWeight = receiveWeight
    },
    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.batchChangeType = ''
      this.uploadList = [] // 清空下载列表
      this.uploadList = [] // 清空下载列表
      this.uploadListV = []
      this.settingsV.data = Array(10).fill(null).map(item => new CoalRawQualBuyInItem())
      this.nameEntity.active = ''
      this.contractEntity.active = []
      this.entityForm = { ...this.model.model }
      this.entityForm.attachmentList = []
      this.entityForm.attachmentCsrList = []
    },
    // @重写方法-添加了合同搜索字段
    handleFilter(filters) {
      this.filters = { ...filters }
      this.$refs[this.curd].searchChange({
        ...this.listQuery, ...filters,
        filter_EQS_contractId: this.contractParams.query
      })
    },
    // @重写方法-添加了clearContract方法
    handleFilterReset() {
      this.clearContract()
      this.$refs[this.curd].searchChange()
    },
    async getContract() {
      const { data } = await chooseContract()
      // 供应商名称返回id用来查询
      this.contract.options = this.formatContract({
        data: JSON.parse(JSON.stringify(data)),
        key: { supplierName: 'name', supplierId: 'id' }
      })
      this.contractEntity.options = this.formatContract({ data, key: { supplierName: 'displayName', supplierId: 'id' } })
    },
    /**
     * 用于格式化合约数据因为合约的子supplierId是不符合要求的
     * @Object {data,key:{string:string|number}}
     */
    formatContract({ data, key }) {
      if (Array.isArray(data) && data.length) {
        return data.map((list) => {
          list.contractBuyList.forEach((item) => {
            for (const [k, v] of Object.entries(key)) {
              item[k] = item[v]
            }
            return item
          })
          return list
        })
      }
      return []
    },
    clearContract() {
      this.listQuery = { ...listQuery }
      this.fromDashboard = false
      this.contractActive = []
      this.contractParams.query = ''
      this.indicators = { mt: Infinity, ad: Infinity, std: Infinity, vdaf: Infinity, g: Infinity }
      return false
    },
    /**
     * 找到选中的合约项
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    filterContractItem(value, target) {
      if (Array.isArray(value) && value.length) {
        return this[target].options
          .find((item) => item.supplierId === value[0])
          .contractBuyList.find((item) => item.supplierId === value[1])
      } else if (typeof value === 'string') {
        const val = []
        for (const list of this[target].options) {
          for (const item of list.contractBuyList) {
            if (item.supplierId === value) {
              val.push(list.supplierId, value)
              return val
            }
          }
        }
      } else {
        return []
      }
    },
    /**
     * 切换时更行指标并设置搜索关键字query
     * @value {[supplierId,supplierId]} 第一个父级Id 第二个是子集Id
     */
    handleContractChange(value) {
      if (!value.length) {
        this.clearContract()
        this.$refs[this.curd].searchChange()
        return
      }
      const item = this.filterContractItem(value, 'contract')
      const { cleanMt, cleanAd, cleanStd, cleanVdaf, procG } = item
      this.indicators = { mt: cleanMt, ad: cleanAd, std: cleanStd, vdaf: cleanVdaf, g: procG }
      this.contractParams.query = item.id
      this.$refs[this.curd].searchChange({
        ...this.listQuery, ...this.filters,
        filter_EQS_contractId: this.contractParams.query
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.custom-classname {
  width: 100%;

  textarea {
    line-height: 31px;
  }

  th {
    background-color: transparent;
  }

  thead {
    tr {
      height: 30px;

      div {
        height: 100%;
        line-height: 30px;
      }
    }

    th {
      background-color: #24c1ab !important;
      color: #fff !important;

      div {
        background-color: #24c1ab !important;
      }
    }
  }
}

// .handsontable tr:first-child th, .handsontable tr:first-child td {
//     /* border-top: 1px solid #ccc; */
// }

.handsontable tr:first-child th,
.handsontable tr:first-child td {
  background-color: #2f79e8;
}

.form-titlV {
  font-size: 16px;
  position: relative;
  padding: 15px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    bottom: 11px;
    background: #2f79e8;
    height: 16px;
  }
}

.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.attachmentV {
  & > span {
    font-size: 12px;
    color: blue;
  }
}

.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;

  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      padding: 6px 10px;
      margin-right: 5px;
      font-size: 12px;
      position: relative;

      &-link {
        font-size: 12px;
        opacity: 0.8;
      }

      .icon {
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 9;
        top: 0;
        flex: 0;
      }
    }
  }
}

::v-deep .el-table .cell {
  display: flex;
  justify-content: center;
}

.panel {
  position: relative;
  display: flex;
  height: 50px;
  align-items: center;
  padding-left: 15px;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);

  &-contract {
    width: 140px;
  }

  &-indicators {
    flex: 1;
    margin: 0 15px;
    display: flex;
    justify-items: center;
    border-radius: 4px;
    border: 1px solid #f1f1f2;

    .panel-title {
      background: #f1f1f2;
      color: #7f7f7f;
      padding: 8px;
    }

    .panel-desc {
      margin-left: 10px;
      padding: 8px;
      background: #ffffff;

      span {
        font-size: 13px;
        display: inline-block;
        padding-left: 3px;
        margin-right: 6px;
      }
    }
  }
}

::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background: transparent;
  border: 0.5px solid #eef0ff;
}

::v-deep .el-table__fixed-footer-wrapper tbody td {
  border-top: none;
  background-color: transparent;
  color: #fff;
}

::v-deep .el-table__footer-wrapper tbody td:nth-of-type(-n + 5) {
  border-left: none;
  border-right: none;
}

.excessive {
  width: 100%;
  color: red;
}
</style>
