<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate" :showAdd="false"
                  :showImport="perms[`${curd}:addimport`]||false" :actions="actions"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <s-curd :ref="curd" :name="curd" :model="model" :list-query="listQuery" :showSummary='true' @selectBatch="handleSelectBatch"
            otherHeight="170">
      <el-table-column label="品名" slot="name" prop="name">
        <template slot-scope="scope">
          {{scope.row.name}}
        </template>
        <!-- <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{scope.row.name}}</span></template> -->
      </el-table-column>
      <el-table-column label="操作" slot="opt" width="360" align="center" fixed="right">
        <template slot-scope="scope">
          <!-- <div style="display:flex;justify-content: center;"> -->
          <div class="buttonbox">

            <el-button style="color: #b42020;margin-right: 10px;" type="opt_stock" @click="handleSetTop(scope.row)">
              {{scope.row.setTop==='Y'?'取消置顶':'置顶'}}</el-button>

            <el-button v-if="perms[`${curd}:calculateinventory`] || false" style="color: #b42020;  margin-right: 10px;"
                       type="opt_stock" @click="handleUpdate(scope.row)">盘库
            </el-button>

            <el-button style="color: #b42020;  margin-right: 10px;" type="opt_stock" @click="handleViewLog(scope.row)">日志
            </el-button>

            <!-- <div>
              <span @click="handleSetTop(scope.row)">{{scope.row.setTop==='Y'?'取消置顶':'置顶'}}</span>
            </div>
            <div>
              <span @click="handleUpdate(scope.row)">盘库</span>
            </div>
            <div>
              <span @click="handleViewLog(scope.row)">日志</span>
            </div> -->

            <div>
              <el-tag class="opt-btn" color="#FF9639" @click="handleRefreshstock(scope.row)">
                <i class="el-icon-refresh"></i>库存
              </el-tag>
            </div>
            <div>
              <el-tag class="opt-btn" color="#33CAD9" @click="handleRefreshindex(scope.row)">
                <i class="el-icon-refresh"></i>指标
              </el-tag>
            </div>
            <div>
              <el-tag class="opt-btn" color="#FF726B" @click="handleRefreshPrice(scope.row)">
                <i class="el-icon-refresh"></i>库存价
              </el-tag>
            </div>

            <!-- <el-button type="opt_stock" @click="handleRefreshstock(scope.row)">刷新库存</el-button> -->

            <!-- <el-button type="opt_stock" @click="handleRefreshindex(scope.row)">刷新指标</el-button> -->

            <!-- <el-button type="opt_stock" @click="handleRefreshPrice(scope.row)">刷新库存价</el-button> -->

          </div>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" top="5vh" title="盘库" :visible.sync="dialogFormVisible" :before-close="handleClose"
               :close-on-click-modal="false" :close-on-press-escape="false" width="900px">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               :disabled="dialogStatus==='watch'" label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <div class="form-title">库存信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="name" label-width="140px">
                    <el-input v-model="entityForm.name" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="entityForm.date" clearable @input="handleChangeDate($event,entityForm,'single')" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="现库存" prop="editStock">
                    <el-input v-model="entityForm.editStock" clearable type="number"
                              @input="handleInput(entityForm.editStock,'item')">
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="原库存" prop="stock">
                    <el-input v-model="entityForm.stock" oninput="value=value.replace(/[^\-\d.]/g,'')" disabled>
                      <template slot="append">吨</template>
                    </el-input>
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" clearable :rows="2" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms[`${curd}:calculateinventory`] || false">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog top="5vh" title="盘库" :visible.sync="batch.visible" width="900px">
      <div class="form-title">库存信息</div>
      <template v-for="item in batch.list">
        <el-row :key="item.id">
          <el-form :rules="rules" :show-message="true" :status-icon="true" :ref="item.id" :model="item"
                   :disabled="dialogStatus==='watch'" :before-close="handleCloseList" label-width="90px" label-position="top">
            <el-row>
              <el-col>
                <div class="form-layout">
                  <el-row type="flex" :gutter="50">
                    <el-col>
                      <el-form-item label="品名" prop="name" label-width="140px">
                        <el-input v-model="item.name" disabled clearable />
                      </el-form-item>
                    </el-col>
                    <el-col>
                      <el-form-item label="日期" prop="date">
                        <date-select v-model="item.date" clearable @input="handleChangeDate($event,item,'more')" />
                      </el-form-item>
                    </el-col>
                    <el-col>
                      <el-form-item label="现库存" prop="editStock">
                        <!--  oninput="value=value.replace(/[^\d.]/g,'')" -->
                        <el-input v-model="item.editStock" clearable type="number" @input="handleInput(item,'row')">
                          <template slot="append">吨</template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col>
                      <el-form-item label="原库存" prop="stock">
                        <el-input v-model="item.stock" disabled>
                          <template slot="append">吨</template>
                        </el-input>
                      </el-form-item>
                    </el-col>

                  </el-row>
                  <el-row type="flex" :gutter="50">
                    <el-col>
                      <el-form-item label="备注" prop="remarks">
                        <el-input type="textarea" v-model="item.remarks" clearable :rows="2" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </el-row>
      </template>
      <div slot="footer" class="dialog-footer" v-if="dialogStatus!=='watch'">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormBatchLoading" @click="saveEntityFormList()"
                   v-if="perms[`${curd}:calculateinventory`] || false">保存
        </el-button>
        <el-button @click="handleCloseList" class="dialog-footer-btns">取消</el-button>
      </div>

    </el-dialog>

    <el-dialog width="900px" top="5vh" title="历史日志" :visible.sync="log.visible" :destroy-on-close="false" class="historyLog">
      <filter-table :options="filterOptionV" @filter="handleFilterV" @reset="handleFilterResetV" :showAdd="false"
                    @import="handleImpiort" :showRefresh="false" :showImport="false" />
      <s-curd class="curdLog" ref="stockLog" v-if="log.visible" name="stockLog" :model="modelLog" :actions="[]" height="55rem"
              :list-query="listQueryLog" />
    </el-dialog>

    <el-dialog top="5vh" title="刷新" :visible.sync="refresh.visible" :before-close="handleCloseRefresh"
               :close-on-click-modal="false" :close-on-press-escape="false" width="900px">
      <el-form :show-message="true" :status-icon="true" :model="refresh.form" label-width="90px" :rules="rules" ref="refreshForm"
               label-position="top" v-if="refresh.visible">
        <el-row>
          <el-col>
            <div class="form-title">库存信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="品名" prop="name" label-width="140px">
                    <el-input v-model="refresh.form.name" clearable disabled />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="日期" prop="date">
                    <date-select v-model="refresh.form.date" :clearable="false" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="dialog-footer-btns" v-if="perms[`${curd}:Refresh`] || false" :loading="refresh.loading"
                   @click="handleRefreshSave()">刷新
        </el-button>
        <el-button @click="handleCloseRefresh" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import { listQuery } from '@/const'
import Model from '@/model/stock/coalStock'
import StockLog from '@/model/stockLog/index'
import { getStockByDate } from '@/api/stock'
const refresh = {
  visible: false,
  loading: false,
  form: {
    name: '',
    date: ''
  }
}
export default {
  name: 'coalStock',
  mixins: [Mixins],
  data() {
    return {
      curd: 'coalStock',
      curdv: 'stockLog',
      model: Model,
      modelLog: StockLog,
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'type',
            component: 'dict-select',
            filter: 'filter_EQS_type',
            props: {
              type: 'stock_type',
              placeholder: '请选择类型',
              clearable: false
            }
          },
          {
            prop: 'name',
            filter: 'filter_LIKES_name',
            component: 'select-name',
            props: {
              placeholder: '请选择品名',
              clearable: true
            }
          }
        ]
      },

      filterOptionV: {
        showMore: true,
        columns: [
          {
            prop: 'startDate',
            component: 'date-select',
            filter: 'filter_GES_date',
            props: {
              placeholder: '开始日期',
              clearable: false
            }
          },
          {
            prop: 'endDate',
            component: 'date-select',
            filter: 'filter_LES_date',
            props: {
              placeholder: '结束日期',
              clearable: false
            }
          }
        ]
      },

      entityForm: { ...Model.model },
      listQuery: { ...listQuery },
      listQueryLog: { ...listQuery },
      rules: {
        name: { required: true, message: '请输入品名', trigger: 'blur' },
        stock: { required: true, message: '请输入库存', trigger: 'blur' },
        date: { required: true, message: '请选择日期', trigger: 'blur' },
        editStock: { required: true, message: '请输入修改的库存', trigger: 'blur' }
      },
      actions: [],
      batch: {
        visible: false,
        list: []
      },
      entityFormBatchLoading: false,
      log: { visible: false, loading: null },
      refresh: { ...refresh },
      filtersSeachV: {}
    }
  },
  created() {
    this.permsAction(this.curd)
  },
  methods: {
    // start 历史日志搜索
    handleFilterV(filters) {
      this.filtersSeachV = filters
      this.$refs[this.curdv].searchChange({ ...this.listQuery, ...filters })
    },
    handleFilterResetV() {
      this.$refs[this.curdv].searchChange()
    },
    // end历史日志搜索
    async handleRefreshindex(row) {
      //刷新指标
      this.$confirm('是否确认刷新指标', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await Model.refreshIndicators({ id: row.id })
          if (res) {
            this.$message({ type: 'success', message: '刷新成功!' })
            this.getList()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '刷新取消' }))
    },
    async handleRefreshPrice(row) {
      //刷新库存价
      this.$confirm('是否确认刷新库存价', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await Model.refreshPrice({ id: row.id })
          if (res) {
            this.$message({ type: 'success', message: '刷新成功!' })
            this.getList()
          }
        })
        .catch(() => this.$message({ type: 'info', message: '刷新取消' }))
    },
    handleRefreshstock(row) {
      this.refresh.visible = true
      this.refresh.form.name = row.name
      this.refresh.form.date = row.date
    },
    handleCloseRefresh() {
      this.refresh = {
        visible: false,
        form: {
          name: '',
          date: ''
        }
      }
    },
    async handleRefreshSave() {
      let valid
      try {
        valid = await this.$refs.refreshForm.validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        this.refresh.loading = true
        const res = await Model.refreshByName({ ...this.refresh.form })
        this.refresh.loading = false
        if (res) {
          this.getList()
          this.handleCloseRefresh()
          this.$message({ showClose: true, message: '刷新成功', type: 'success' })
        }
      }
    },
    async handleSetTop(row) {
      let { id, setTop } = row
      setTop = setTop === 'Y' ? 'N' : 'Y'
      try {
        await this.model.setTop({ id, setTop })
        this.getList()
      } catch (error) {}
    },
    async handleChangeDate(date, item, type) {
      if (date) {
        const res = await getStockByDate({ date, productId: item.productId })
        if (res.data) {
          item.stock = res.data.stock
          item.stockBak = res.data.stock
          item._stock = item.editStock - item.stockBak
        } else {
          item.stock = 0
          item.stockBak = 0
          item._stock = item.editStock - item.stockBak
        }
      }
    },
    handleInput(v, who) {
      if (who === 'item') {
        if (!isNaN(v * 1)) {
          this.entityForm._stock = v - this.entityForm.stockBak
        }
      } else {
        if (!isNaN(v.editStock * 1)) {
          v._stock = v.editStock - v.stockBak
        }
      }
    },
    async saveEntityForm(entityForm) {
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        const { id, _stock, remarks, date } = this.entityForm
        this.entityFormLoading = true
        const res = await Model.inventory({ id, stock: _stock, remarks, date })
        this.entityFormLoading = false
        if (res) {
          this.getList()
          this.resetVariant()
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        }
      }
    },
    async checkEntityFormList() {
      const ids = this.batch.list.map((item) => item.id)
      for (const id of ids) {
        try {
          await this.$refs[id][0].validate()
        } catch (e) {
          return false
        }
      }
      return true
    },
    async saveEntityFormList() {
      if (!(await this.checkEntityFormList())) return this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      const list = this.batch.list.map((item) => {
        return { id: item.id, stock: item._stock, remarks: item.remarks, date: item.date }
      })
      try {
        this.entityFormBatchLoading = true
        await Model.batchInventory({ inventoryParamList: list })
        this.entityFormBatchLoading = false
        this.getList()
        this.resetBatchVisible()
        this.$message({ showClose: true, message: '操作成功', type: 'success' })
      } catch (e) {
        // console.log(e)
      }
    },
    resetBatchVisible() {
      this.batch.visible = false
      this.batch.list = []
      this.$refs[this.curd].clearSelect()
    },
    handleCloseList() {
      this.resetBatchVisible()
    },
    handleSelectBatch(list) {
      const newList = list.map((item) => {
        return { ...item }
      })
      this.batch.visible = true
      this.batch.list = newList
    },
    async handleViewLog(row) {
      this.log.visible = true
      await this.$nextTick()
      this.listQueryLog = { ...this.listQueryLog, filter_EQS_productId: row.productId }
      this.$refs['stockLog'].searchChange()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/router-page.scss';
// ::v-deep .el-table .cell,
// .el-table .cell .el-input__inner {
//   height: 65px;
// }
.buttonbox {
  width: 100%;
  height: 100%;
  display: inline-block;

  // display: flex;
  // flex-wrap: wrap;

  & > div {
    display: inline-block;
    // width: calc(100% / 3);
    // text-align: center;
    margin-right: 10px;
    color: #b42020;
    cursor: pointer;
  }
}
::v-deep .el-tag--mini {
  padding: 2px 5px;
}
.el-button--opt_stock:focus,
.el-button--opt_stock:hover {
  opacity: 1;
}
.el-button--opt_stock {
  flex: 0 1 50px;
  transition: 0.5s all;
  color: #b42020;
  opacity: 0.85;
  padding: 0;
  background-color: transparent;
  border-color: transparent;
}
::v-deep .el-input.is-disabled .el-input__inner {
  color: #606266;
}
.historyLog {
  width: 100%;
  height: 800px !important;
  text-align: center;
  color: black;
  background: none;
  ::v-deep .el-dialog__body {
    padding: 0 !important;
  }
  .curdLog {
    min-height: 500px;
    max-height: 500px;
    height: 500px;
  }
}
</style>