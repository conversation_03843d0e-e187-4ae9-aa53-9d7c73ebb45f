<template>
  <div class="app-container">
    <panel-bar type="panel" title="数据看板">
      <el-date-picker style="width: 154px;" type="date" value-format="yyyy-MM-dd" v-model="currentDate" placeholder="选择日期"
                      :clearable="false">
      </el-date-picker>
    </panel-bar>
    <card height="inherit" style="height:43vh">
      <card-item title="半成品精煤进配存">
        <div class="item-echarts">
          <echarts v-if="isFirstOption" :option="firstOption" />
        </div>
      </card-item>
      <card-item title="成品精煤产销存">
        <div class="item-echarts">
          <echarts v-if="isSecondOption" :option="secondOption" />
        </div>
      </card-item>
    </card>
    <card height="inherit" style="height: 40.5rem;background:#fff;">
      <!-- <card-item title="新增试验报表">
        <section class="reports">
          <div class="reports-item">
            <div class="reports-item-section">
              <div class="reports-item-img" style="background:#33CAD9;" @click="handleToPage('rawCoalStock')">
                <img src="@/assets/console/stock_ym.png" />
              </div>
              <div class="reports-item-desc" @click="handleToPage('rawCoalStock')">
                <span>原煤库存录入</span>
                <span>RAW COAL INVENTORY</span>
              </div>
            </div>
            <div class="reports-item-section">
              <div class="reports-item-img" style="background:#FF9639;" @click="handleToPage('semiFinishedCoalStock')">
                <img src="@/assets/console/stock_bcp.png" />
              </div>
              <div class="reports-item-desc" @click="handleToPage('semiFinishedCoalStock')">
                <span>半成品精煤库存录入</span>
                <span>SEMI FINISHED COAL</span>
              </div>
            </div>
          </div>
          <div class="reports-item">
            <div class="reports-item-section">
              <div class="reports-item-img" style="background:#FF726B;" @click="handleToPage('chinaCoalStock')">
                <img src="@/assets/console/stock_zm.png" />
              </div>
              <div class="reports-item-desc" @click="handleToPage('chinaCoalStock')">
                <span>中煤库存录入</span>
                <span>CHINA COAL </span>
              </div>
            </div>
            <div class="reports-item-section">
              <div class="reports-item-img" style="background:#2f79e8;" @click="handleToPage('finishedCoalStock')">
                <img src="@/assets/console/stock_jp.png" />
              </div>
              <div class="reports-item-desc" @click="handleToPage('finishedCoalStock')">
                <span>成品精煤库存录入</span>
                <span>FINE COAL INVETORY</span>
              </div>
            </div>
          </div>
        </section>
      </card-item> -->
    </card>
  </div>
</template>

<script>
import { PanelBar, Card, CardItem } from '@/components/Console'
import { listQuery } from '@/const'
import first from '@/model/stock/finishedCoalStock'
import second from '@/model/stock/semiFinishedCoalStock'

const option = {
  animation: false,
  color: [],
  tooltip: {
    trigger: 'axis',
    point: [-30, -30],
    backgroundColor: '#EDEEFF',
    padding: [5, 20, 5, 20],
    textStyle: {
      color: '#9f9ea5',
    },
  },
  grid: {
    top: '50px',
    left: '70px',
    right: '20px',
    bottom: '50px',
  },
  legend: {
    right: 20,
    itemGap: 20,
    padding: [10, 0, 10, 10],
    align: 'left',
    data: [],
  },
  xAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      // show:false, // 是否显示坐标轴轴线
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF', // 坐标轴线的颜色修改--文字也同步修改
        type: 'dashed',
      },
    },
    axisTick: {
      // 坐标轴刻度相关设置
      // show:true, // 是否显示坐标轴刻度
      alignWithLabel: true, // 可以保证刻度线和标签对齐
    },
    axisLabel: {
      color: '#9f9ea5', // 刻度标签文字的颜色
    },
    data: [],
  },
  yAxis: {
    axisLine: {
      // 坐标轴轴线相关设置
      lineStyle: {
        // 坐标轴线线的颜色
        color: '#E3F0FF',
        type: 'solid',
      },
    },
    axisLabel: {
      // 坐标轴刻度标签的相关设置
      color: '#9f9ea5',
    },
    splitLine: {
      // 坐标轴在 grid 区域中的分隔线。
      show: true, // 是否显示分隔线。默认数值轴显示，类目轴不显示。
      lineStyle: {
        color: '#E3F0FF', // 分隔线颜色，可以设置成单个颜色
      },
    },
  },
  series: [
    {
      name: '',
      type: 'bar',
      barWidth: 12,
      barGap: '30%',
      itemStyle: {
        normal: {
          barBorderRadius: [30, 30, 0, 0], // 配置样式
          color: () => '',
        },
      },
      data: [],
    },
    {
      name: '',
      type: 'bar',
      barGap: '30%',
      barWidth: 12,
      itemStyle: {
        normal: {
          barBorderRadius: [30, 30, 0, 0], // 配置样式
          color: () => '',
        },
      },
      data: [],
    },
    {
      name: '',
      type: 'bar',
      barGap: '30%',
      barWidth: 12,
      itemStyle: {
        normal: {
          barBorderRadius: [30, 30, 0, 0], // 配置样式
          color: () => '',
        },
      },
      data: [],
    },
  ],
}
Object.freeze(option)
export default {
  name: 'stockConsole',
  components: { PanelBar, Card, CardItem },
  data() {
    return {
      currentDate: new Date(new Date().getTime() - 24 * 60 * 60 * 1000).Format('yyyy-MM-dd'),
      listQuery: { ...listQuery },
      loading: false,
      firstOption: {},
      secondOption: {},
      firstLegendData: ['本日购产', '本日配销', '本日库存'],
      secondLegendData: ['本日产出', '本日销售', '本日库存'],
      firstColor: ['#FF9639', '#2f79e8', '#FF726B'],
      secondColor: ['#33CAD9', '#456FDF', '#FF9639'],
    }
  },
  created() {
    this.getData()
  },
  computed: {
    isFirstOption() {
      if (Object.keys(this.firstOption).length) return true
      return false
    },
    isSecondOption() {
      if (Object.keys(this.secondOption).length) return true
      return false
    },
  },
  methods: {
    /**
     * 初始化请求数据用于渲染`echarts`
     * @param filter_EQS_date:String 检索当前日期的date 默认为``则查询所有
     * @return void
     */
    async getData(filter_EQS_date = '') {
      if (this.loading) return
      this.loading = true
      try {
        const firstListQuery = { ...this.listQuery, filter_EQS_date, filter_EQS_type: 'BCPJM' }
        const secondListQuery = { ...this.listQuery, filter_EQS_date, filter_EQS_type: 'CPJM' }
        const res = await Promise.all([first.pages(firstListQuery), second.pages(secondListQuery)])
        if (res.length) {
          this.firstOption = this.formatOption({
            data: res[0].data.records,
            option,
            legendData: this.firstLegendData,
            color: this.firstColor,
          })
          this.secondOption = this.formatOption({
            data: res[1].data.records,
            option,
            legendData: this.secondLegendData,
            color: this.secondColor,
          })
        }
        this.loading = false
      } catch (e) {
        this.loading = false
      }
    },

    /**
     * 自定义格式化 echarts.option数据
     * @param optinos:{data:[],option:option,color:[]}
     * @return newOption
     */
    formatOption({ data, option, color, legendData }) {
      // 深拷贝
      const newOption = JSON.parse(JSON.stringify(option))
      const nameList = []
      const todayInList = []
      const todayOutList = []
      const stockList = []
      data.forEach((item) => {
        // 不存在则push 存在根据索引去累加
        const nameIndex = nameList.findIndex((name) => name === item.name)
        if (nameIndex <= -1) {
          nameList.push(item.name)
          todayInList.push(item.todayIn * 1)
          todayOutList.push(item.todayOut * 1)
          stockList.push(item.stock * 1)
        } else {
          todayInList[nameIndex] += item.todayIn * 1
          todayOutList[nameIndex] += item.todayOut * 1
          stockList[nameIndex] += item.stock * 1
        }
      })
      newOption.color = color
      newOption.legend.data = legendData
      newOption.xAxis.data = nameList
      newOption.series[0].data = todayInList
      newOption.series[0].name = legendData[0]
      newOption.series[1].data = todayOutList
      newOption.series[1].name = legendData[1]
      newOption.series[2].data = stockList
      newOption.series[2].name = legendData[2]
      return newOption
    },
    handleToPage(pageName) {
      this.$store.dispatch('changeChildRoute', pageName)
      this.$router.push({ name: pageName, params: { isOpenAdd: true } })
    },
  },
  watch: {
    currentDate: {
      handler(v) {
        this.getData(v)
      },
      immediate: true,
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .card-item {
  padding: 5px;
}
::v-deep .item-echarts {
  width: 98.5%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  height: inherit;
}
.reports {
  width: inherit;
  height: inherit;
  display: flex;

  &-item {
    width: inherit;
    height: inherit;
    display: flex;
    flex-flow: column;
    justify-content: space-evenly;
    position: relative;

    &-section {
      display: flex;
      justify-content: center;
      opacity: 0.9;
      transition: all 0.5s;

      .reports-item-img {
        position: relative;
        width: 6rem;
        height: 6rem;
        border-radius: 50%;
        margin-right: 2rem;
        cursor: pointer;

        img {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 50%;
          height: 50%;
        }
      }

      .reports-item-desc {
        display: flex;
        justify-content: center;
        flex-flow: column nowrap;
        cursor: pointer;
        width: 200px;

        span:first-of-type {
          font-size: 2rem;
          color: #424242;
          margin-bottom: 10px;
        }

        span:last-of-type {
          font-size: 1.5rem;
          color: #a8a8a8;
        }
      }
    }

    &-section:hover {
      opacity: 1;
    }
  }

  &-item:first-of-type:after {
    content: '';
    position: absolute;
    opacity: 1;
    right: 0;
    height: 50px;
    width: 2px;
    background: #efefef;
  }
}
</style>
