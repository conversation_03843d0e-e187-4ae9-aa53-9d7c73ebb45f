<template>
    <div class="app-container">
        <s-curd @selectItem="selectItem" ref="actualTest" name="actualTest" :model="model" :list-query="listQuery"
                :actions="actions"
                title="实际化验项">
            <el-table-column label="类别" slot="categoryName" prop="categoryName" width="300px">
                <template slot-scope="scope">
                    <el-button type="text" @click="handleEdit(scope.row)">{{scope.row.categoryName}}</el-button>
                </template>
            </el-table-column>
            <el-table-column label="操作" slot="opt" prop="opt" width="120">
                <template slot-scope="scope">
                    <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </s-curd>
        <el-dialog ref="dialogStatus" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
                   :before-close="handleClose"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   width="60%"
        >
            <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm"
                     label-width="110px"
                     v-if="dialogFormVisible">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="名称" prop="name" :rules="[{required:true, message:'请输入名称'}]">
                            <el-input v-model="entityForm.name" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="类型" prop="type" :rules="[{required:true, message:'请选择类型'}]">
                            <dict-select v-model="entityForm.type" clearable type="s_assay_type"></dict-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="排序" prop="sort" :rules="[{required:true, message:'请输入排序字段'}]">
                            <el-input v-model="entityForm.sort"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="化验项目字段" prop="assayMetaFieldName"
                                      :rules="[{required:true, message:'请输入化验项目'}]">
                            <el-input v-model="entityForm.assayMetaFieldName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="类别" prop="categoryName" :rules="[{required:true, message:'请选择类别'}]">
                            <category-select v-model="entityForm.categoryName" clearable :type="entityForm.type"
                                             @info="getCategory"></category-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="符号" prop="mark">
                            <el-input v-model="entityForm.mark"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="单位" prop="unit">
                            <el-input v-model="entityForm.unit"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="界面类型" prop="uiType" :rules="[{required:true, message:'请选择界面类型'}]">
                            <dict-select v-model="entityForm.uiType" type="s_input_type"></dict-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="选项" prop="options">
                            <el-input v-model="entityForm.options"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否必填" prop="required">
                            <dict-select type="s_yes_or_no" v-model="entityForm.required"></dict-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="参考标准" prop="reference">
                            <el-input v-model="entityForm.reference" type="textarea" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" :loading="entityFormLoading" @click="handleSave('entityForm')">保存
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {mapGetters} from 'vuex'
    import Model from '@/model/sys/actualTest'
    import DictSelect from '@/components/DictSelect/index'
    import {getToken} from '@/utils/auth'

    const token = getToken()
    export default {
        name: 'actualTest',
        components: {DictSelect},
        data() {
            return {
                headers: {
                    'Authorization': token
                },
                dialogVisible: false,
                productImgs: [],
                imgLimit: 5,
                editForm: {},
                hideUploadEdit: false,
                fileList: [],
                dialogFormVisible: false,
                entityForm: {
                    required: 'N',
                    attachment: ''
                },
                textMap: {
                    update: '编辑',
                    create: '创建'
                },
                dialogStatus: '',
                entityFormLoading: false,
                actions: [
                    {
                        config: {
                            type: 'primary',
                            plain: true,
                            icon: 'el-icon-plus'
                        },
                        click: this.handleCreate,
                        show: 'actualTest:save',
                        label: '新增'
                    }
                ],
                model: Model,
                listQuery: {
                    orderBy: 'createDate',
                    orderDir: 'desc'
                }
            }
        },
        computed: {
            ...mapGetters([
                'perms'
            ])
        },
        methods: {
            handleEditChange(file, fileList) {
                let vm = this
                vm.hideUploadEdit = fileList.length >= this.imgLimit
            },
            handleRemove(file, fileList) {
                this.productImgs = fileList
                this.fileList = fileList
            },
            handleExceed(files, fileList) {
                this.$message.error('上传文件不能超5个!')
            },
            handleAvatarSuccess(res, file) {
                // 图片上传成功
                this.fileList.push(res)
            },
            selectItem(list) {
                this.selectList = list
            },
            // 删除信息
            async handleDelete(row) {
                try {
                    await this.$confirm('确认删除?')
                    let id = row.id
                    const res = await this.model.delete(id)
                    if (res) {
                        this.$notify.success('删除成功')
                        this.getList()
                    }
                } catch (e) {
                }
            },
            getCategory(info) {
                this.entityForm.categoryName = info.name
                this.entityForm.categoryId = info.id
            },
            // 刷新页面
            getList() {
                this.$refs.actualTest.$emit('refresh')
            },
            // 新建
            handleCreate() {
                this.dialogStatus = 'create'
                this.dialogFormVisible = true
            },
            // 保存
            async handleSave() {
                const valid = await this.$refs.entityForm.validate()
                if (valid) {
                    this.entityFormLoading = true
                    let res = await Model.save(this.entityForm)
                    this.entityFormLoading = false
                    if (res.data) {
                        this.$message.success('保存信息成功')
                        this.getList()
                        this.handleClose()
                    }
                }
            },
            // 修改
            handleEdit(row) {
                this.entityForm = {...row}
                if (row.attachment) {
                    if (row.attachment.indexOf(',') > -1) {
                        let productImgs = row.attachment.split(',')
                        productImgs.map(item => {
                            let obj = {
                                name: item,
                                url: item
                            }
                            this.productImgs.push(obj)
                            this.fileList.push(obj)
                        })
                    } else {
                        let obj = {
                            name: row.attachment,
                            url: row.attachment
                        }
                        this.productImgs.push(obj)
                        this.fileList.push(obj)
                    }
                }
                this.dialogFormVisible = true
                this.dialogStatus = 'update'
            },
            // 关闭弹窗
            handleClose() {
                this.fileList = []
                this.productImgs = []
                this.entityForm = {required: 'N'}
                this.dialogFormVisible = false
            }
        }
    }
</script>
