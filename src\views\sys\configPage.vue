<template>
  <div class="app-container">
    <s-curd ref="config" name="config" :model="model" :list-query="listQuery" otherHeight="140" :actions="actions"
            title="公司配置">
      <!-- <el-table-column label="名称" slot="name" prop="name">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdit(scope.row)">{{scope.row.name}}</el-button>
        </template>
      </el-table-column> -->

      <el-table-column label="编码" slot="code" prop="code">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdit(scope.row,'update')" style="cursor: pointer;">{{ scope.row.code }}
          </el-button>
        </template>
      </el-table-column>
      <!-- width="980px"  -->
    </s-curd>
    <el-dialog ref="dialogStatus" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
               :before-close="handleClose"
               top="30vh" :close-on-click-modal="false" :close-on-press-escape="false" style="height:740px">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" label-width="90px" :model="entityForm"
               label-position="top" v-if="dialogFormVisible">
        <el-row type="flex" :gutter="50" style="margin-top:20px;padding: 10px">
          <!-- <el-col :span="8">
            <el-form-item label="名称" prop="name" :rules="[{required:true, message:'请输入名称'}]">
              <el-input v-model="entityForm.name" clearable></el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="24">
            <el-form-item label="编码" prop="code" :rules="[{required:true, message:'请输入编码'}]">
              <el-input v-model="entityForm.code" :disabled="dialogStatus==='update'"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="值" prop="value">
              <el-input type="textarea" :rows="2" v-model="entityForm.value"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input type="textarea" :rows="2" v-model="entityForm.remarks"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="entityFormLoading" @click="handleSave('entityForm')">保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import Model from '@/model/config/configPage'

export default {
  name: 'actualTest',
  data() {
    return {
      dialogVisible: false,
      entityForm: {},
      textMap: {
        update: '编辑',
        create: '创建'
      },
      dialogStatus: '',
      entityFormLoading: false,
      actions: [
        {
          config: {
            type: 'primary',
            plain: true,
            icon: 'el-icon-plus'
          },
          click: this.handleCreate,
          show: 'actualTest:save',
          label: '新增'
        }
      ],
      dialogFormVisible: false,
      model: Model,
      listQuery: {
        orderBy: 'createDate',
        orderDir: 'desc'
      }
    }
  },
  computed: {
    ...mapGetters(['perms'])
  },
  methods: {
    // 刷新页面
    getList() {
      this.$refs.config.$emit('refresh')
    },
    // 新建
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },
    // 保存
    async handleSave() {
      this.entityFormLoading = true
      let res = await Model.save(this.entityForm)
      this.entityFormLoading = false
      if (res.data) {
        this.$message.success('保存信息成功')
        this.getList()
        this.handleClose()
      }
    },
    // 修改
    handleEdit(row) {
      this.entityForm = {...row}
      this.dialogFormVisible = true
      this.dialogStatus = 'update'
    },
    // 关闭弹窗
    handleClose() {
      this.fileList = []
      this.productImgs = []
      this.entityForm = {required: 'N'}
      this.dialogFormVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .top-filter {
  margin-top: 10px;
}

.form {
  display: flex;
  justify-content: stretch;
}
</style>
