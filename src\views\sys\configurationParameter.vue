<template>
  <div class="app-container">
    <!-- <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
           @import="handleImpiort"  :showImport="perms[`${curd}:addimport`]||false"     :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" /> -->

    <!-- <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">
      <el-table-column label="名称" slot="name" prop="name" width="150" align="center">
        <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{ scope.row.name }}</span></template>
      </el-table-column>

      <el-table-column label="类型" slot="type" prop="type" align="center">
        <template slot-scope="scope" v-if="scope.row.type">
          <el-tag :type="(scope.row.type=='SELL' ? 'danger':(scope.row.type == 'BUY' ? 'warning' : (scope.row.type == 'ALL' ? 'success' : '')))"
                  size="mini">
            {{scope.row.type == 'SELL' ? '销售' : (scope.row.type == 'BUY' ? '采购' : (scope.row.type == 'ALL' ? '采购销售' :'')) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" slot="opt" max-width="100" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" v-if="perms[`${curd}:update`] || false" color="#FF9639"
                  @click="handleUpdate(scope.row)">编辑</el-tag>
        </template>
      </el-table-column>
    </s-curd> -->

    <div class="s-curd">
      <el-tabs class="tabsclass" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="邮件通知" name="first"></el-tab-pane>
        <el-tab-pane label="短信模板" name="second"></el-tab-pane>
        <!-- <el-tab-pane label="打印模板" name="third"></el-tab-pane> -->
        <!-- <el-tab-pane label="定时任务补偿" name="fourth">定时任务补偿</el-tab-pane> -->
      </el-tabs>
    </div>
    <div class="tabsclass" v-if="activeName=='first'">
      <el-form :show-message="true" :status-icon="true" ref="sbForm" :model="sbForm" label-width="90px" :rules="sbFormrules"
               label-position="right">
        <el-row type="flex" :gutter="20">
          <el-col>
            <el-form-item label="SMTP服务器" prop="host">
              <el-input v-model="sbForm.host" autocomplete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col />
        </el-row>
        <el-row type="flex" :gutter="20">
          <el-col>
            <el-form-item label="SMTP端口" prop="port">
              <el-input v-model="sbForm.port" autocomplete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col />
        </el-row>
        <el-row type="flex" :gutter="20">
          <el-col>
            <el-form-item label="邮箱账号" prop="from">
              <el-input v-model="sbForm.from" autocomplete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col />
        </el-row>
        <el-row type="flex" :gutter="20">
          <el-col>
            <el-form-item label="邮箱账号前缀" prop="user">
              <el-input v-model="sbForm.user" autocomplete="off" clearable style="width:72%;margin-right:10px" />
              <span style="font-size:12px;color:red;display: inline;">@符号前面的邮箱号</span>
            </el-form-item>
          </el-col>
          <el-col />
        </el-row>
        <el-row type="flex" :gutter="20">
          <el-col>
            <el-form-item label="邮箱密码" prop="pass">
              <el-input v-model="sbForm.pass" autocomplete="off" clearable show-password />
            </el-form-item>
          </el-col>
          <el-col />
        </el-row>

        <el-row type="flex" :gutter="20">
          <el-col>
            <el-form-item label="磅房通知发送邮件" prop="isWeightHouseNotice">
              <el-radio-group v-model="sbForm.isWeightHouseNotice">
                <el-radio label="Y">启用</el-radio>
                <el-radio label="N">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col />
        </el-row>
        <el-row type="flex" :gutter="20">
          <el-col>
            <el-form-item label="审批通知发送邮件" prop="isReviewNotice">
              <el-radio-group v-model="sbForm.isReviewNotice">
                <el-radio label="Y">启用</el-radio>
                <el-radio label="N">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col />
        </el-row>

      </el-form>
      <div slot="footer" class="fromfooter">
        <el-button type="primary" class="fromfooter-btns" @click="sumitForm('sbForm')">立即提交
        </el-button>
        <el-button @click="sedtest" class="fromfooter-btns">发送测试邮箱</el-button>
      </div>
    </div>

    <div class="tabsclass" v-if="activeName=='second'">
      <el-row>
        <el-col :span="6" v-for="(item,index) in smsTemplateList" :key="index" :offset="index > 0 ? 1: 0">
          <el-card :body-style="{ padding: '0px' }">
            <!-- <img src="https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png" class="image"> -->
            <div style="padding: 14px;">
              <div class="linblack">
                <div>姓名:</div>
                <div>{{item.name}}</div>
              </div>
              <div class="linblack">
                <div>是否启用：</div>
                <div v-if="!item.isedit">
                  <span v-if="item.isOpen=='Y'">启用</span>
                  <span v-if="item.isOpen=='N'">禁用</span>
                </div>
                <div v-else>
                  <el-radio-group v-model="item.isOpen">
                    <el-radio label="Y">启用</el-radio>
                    <el-radio label="N">禁用</el-radio>
                  </el-radio-group>
                </div>
              </div>
              <div class="linblack" style="">
                <div style="width:60px">模板内容:</div>
                <div v-if="!item.isedit">{{item.content}}</div>
                <div v-else>
                  <el-input type="textarea" v-model="item.content" placeholder="请输入" />
                </div>
              </div>
              <div class="bottom clearfix">
                <el-button type="text" class="button" v-if="!item.isedit" @click="smsTemplateeditFn(item)">编辑</el-button>
                <div v-else>
                  <el-button type="text" class="button" style="margin-left:10px" @click="smsTemplatesaveFn(item)">保存</el-button>
                  <el-button type="text" class="button" @click="smsTemplatecancelFn(item)">取消</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

    </div>

    <div class="tabsclass" v-if="activeName=='third'">
      <el-row>
        <el-col :span="6" v-for="(item,index) in printlist" :key="index" :offset="1">
          <el-card :body-style="{ padding: '0px' }">
            <div style="padding: 14px;">
              <div class="linblack">
                <div>模板名称:</div>
                <div>{{item.name}}</div>
              </div>
              <div class="bottom clearfix">
                <el-button type="text" class="button" style="margin-left:10px" @click="uploadFn(item)">上传模板</el-button>
                <el-button type="text" class="button" style="margin-left:10px" @click="downloadFn(item)">下载</el-button>

              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <el-dialog width="300px" top="5vh" title="邮箱账号" :visible.sync="dialogFormVisible" :before-close="handleClose"
               :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row>
          <el-col>
            <el-row type="flex" :gutter="20">
              <el-col>
                <el-form-item label="邮箱账号" prop="email">
                  <el-input v-model="entityForm.email" clearable />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')">发送
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog width="450px" top="5vh" title="上传模版" :visible.sync="dialoguploadVisible" :before-close="handleuploadClose"
               :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="uploadForm" :model="uploadForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialoguploadVisible">
        <el-row>
          <el-col>
            <el-row type="flex" :gutter="50">
              <el-col>
                <div class="upload">
                  <div class="upload-list">
                    <div class="up-load-warp" v-for="(item,index) in uploadList" :key="index">
                      <i class="el-icon-close icon" @click="handleRemoveUpload(index)"></i>
                      <el-link class="up-load-warp-link" :underline=" false" :href="item.uri">{{item.display}}</el-link>
                    </div>
                    <upload-attachment ref="upload" class="upload-attachment" :uploadData="uploadData" source="coalSample"
                                       url="/cwe/a/printTemplate/upload" :size="size" :limitNum="limitNum" accept=".xls"
                                       @successNotify="handleUploadSuccess" @deleteNotify="handleUploadDelete" listType="text" />
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="dialog-footer-btns" :loading="uploadFormLoading" @click="saveuploadForm('uploadForm')">上传
        </el-button>
        <el-button @click="handleuploadClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/sys/configurationParameter'
import { createMemoryField } from '@/utils/index'
export default {
  name: 'configurationParameter',
  mixins: [Mixins],
  data() {
    // 自定义邮箱规则
    var checkEmail = (rule, value, callback) => {
      const mailReg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/
      if (!value) {
        return callback(new Error('邮箱不能为空'))
      }
      setTimeout(() => {
        if (mailReg.test(value)) {
          callback()
        } else {
          callback(new Error('请输入正确的邮箱格式'))
        }
      }, 100)
    }
    return {
      curd: 'configurationParameter',
      model: Model,
      activeName: 'first',
      sbForm: {},
      sbFormrules: {
        host: { required: true, message: '请输入SMTP服务器', trigger: 'blur' },
        port: { required: true, message: '请输入SMTP端口', trigger: 'blur' },
        user: { required: true, message: '请输入邮箱前缀', trigger: 'blur' },
        pass: { required: true, message: '请输入邮箱密码', trigger: 'blur' },
        // from: [
        //   { required: true, message: '请输入邮箱', trigger: 'blur' },
        //   { validator: checkEmail, trigger: 'blur' },
        // ],
        // from: { required: true, message: '请输入邮箱', trigger: 'blur' },
        isWeightHouseNotice: { required: true, message: '请输选择', trigger: 'change' },
        isReviewNotice: { required: true, message: '请输选择', trigger: 'change' }
      },
      entityForm: { ...Model.model },
      uploadForm: {},
      rules: {
        email: { required: true, message: '请输入邮箱', trigger: 'blur' }
      },
      actions: [],
      sourceOptions: {
        show: true,
        list: [{ label: '自产' }, { label: '采购煤' }]
      },
      memoryEntity: { fields: {}, triggered: false },
      smsTemplateList: [],
      printlist: [],
      uploadList: [], // 用于展示
      uploadData: { refId: '', refType: 'CoalSample' },
      limitNum: 1,
      dialoguploadVisible: false,
      uploadFormLoading: false,
      uploadid: '',
      uploadurl: ''
    }
  },
  created() {
    this.memoryEntity.fields = createMemoryField({ fields: ['name', 'coalCategory', 'source'], target: this.entityForm })
    this.getEmailConfig()
    this.getsmsTemplateFn()
    this.getprintTemplateFn()
  },
  watch: {},
  computed: {
    size() {
      return this.uploadList.length
    }
  },
  methods: {
    handleuploadClose() {
      this.uploadList = []
      this.uploadid = ''
      this.uploadurl = ''
      this.dialoguploadVisible = false
    },
    async getprintTemplateFn() {
      const res = await Model.getprintTemplate({})
      this.printlist = res.data
    },
    async downloadFn(item) {
      window.location.href = item.content
    },
    async uploadFn(item) {
      this.dialoguploadVisible = true
      this.uploadid = item.id
    },
    async saveuploadForm() {
      // id：记录的id
      // content：上传模板后返回的全地址
      if (this.uploadid && this.uploadurl) {
        let dada = {
          id: this.uploadid,
          content: this.uploadurl
        }
        const res = await this.model.printuploadsave({ ...dada })
        if (res) {
          this.getprintTemplateFn()
          this.handleuploadClose()
          this.$message({ showClose: true, message: '保存成功', type: 'success' })
        }
      } else {
        this.$message({ showClose: true, message: '请选择文件', type: 'warning' })
      }
    },
    async handleRemoveUpload(index) {
      const { id } = this.uploadList[index]
      this.$refs.upload.attachmentDelete({ id, index })
    },
    handleUploadSuccess(res) {
      this.uploadurl = res.uri
      this.uploadList = [...this.uploadList, res]
    },
    handleUploadDelete({ status, index }) {
      this.uploadList.splice(index, 1)
    },

    smsTemplatecancelFn(item) {
      let Things = this.smsTemplateList
      for (var i = 0; i < Things.length; i++) {
        if (Things[i].id == item.id) {
          Things[i].isedit = false
        }
      }
      this.smsTemplateList = Things
    },
    //保存模板的接口
    async smsTemplatesaveFn(item) {
      const res = await this.model.smsTemplatesave(item)
      this.getsmsTemplateFn()
    },
    smsTemplateeditFn(item) {
      let Things = this.smsTemplateList
      for (var i = 0; i < Things.length; i++) {
        if (Things[i].id == item.id) {
          Things[i].isedit = true
        }
      }
      this.smsTemplateList = Things
    },

    async getsmsTemplateFn() {
      const res = await Model.getsmsTemplate({})
      // isedit
      let Things = res.data
      for (var i = 0; i < Things.length; i++) {
        Things[i].isedit = false
      }
      this.smsTemplateList = Things
    },

    async getEmailConfig() {
      const res = await Model.getEmailConfig({})
      this.sbForm = res.data
    },
    handleClick(tab, event) {
      // console.log(tab, event)
      this.getsmsTemplateFn()
    },
    sedtest() {
      this.dialogFormVisible = true
    },

    async sumitForm(sbForm) {
      const mailReg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/
      if (mailReg.test(this.sbForm.from)) {
        // console.log('合法的邮箱')
      } else {
        this.$message({ message: '请输入合法邮箱!!!', type: 'warning' })
        return
      }
      //立即提交
      let valid
      try {
        valid = await this.$refs[sbForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        this.entityFormLoading = true
        const res = await this.model.save({ ...this.sbForm })
        this.entityFormLoading = false
        if (res) {
          this.resetVariant()
          this.getEmailConfig()
          this.$message({ showClose: true, message: '提交成功', type: 'success' })
        }
      }
    },

    async saveEntityForm(entityForm) {
      const mailReg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/
      //   const mailReg = /\w+@[a-z0-9]+\.[a-z]{2,4}/
      if (mailReg.test(this.entityForm.email)) {
        // console.log('合法的邮箱')
      } else {
        this.$message({ message: '请输入合法邮箱!!!', type: 'warning' })
        return
      }
      //发送测试邮箱
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        this.entityFormLoading = true
        const res = await this.model.savetestSendEmail({ ...this.entityForm })
        this.entityFormLoading = false
        if (res) {
          this.$message({ showClose: true, message: '发送测试邮箱成功', type: 'success' })
        }
      }
    },
    resetVariant() {
      this.dialogFormVisible = false
      this.entityForm = { ...this.model.model }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.tabsclass .el-col-offset-1 {
  margin-left: 0px !important;
}
.uploadAttachment {
  text-align: left !important;
}
.tabsclass {
  ::v-deep .el-form-item__label {
    width: 120px !important;
  }
  ::v-deep .el-form-item__content {
    margin-left: 120px !important;
  }
  ::v-deep .el-col-offset-1 {
    margin-left: 2%;
  }
  ::v-deep .el-card {
    margin-bottom: 20px;
  }
}
::v-deep .el-dialog__body {
  height: 20vh;
}

.app-container {
  background-color: #fff;
  height: 100vh;
  .tabsclass {
    padding: 0 10px 20px 10px;
    .fromfooter {
      padding-left: 7.2%;
    }
  }
  .linblack {
    width: 100%;
    display: flex;
    // align-items: center;
    padding-top: 10px;
  }
}
</style>
<style>
.time {
  font-size: 13px;
  color: #999;
}

.bottom {
  margin-top: 13px;
  line-height: 12px;
}

.button {
  padding: 0;
  float: right;
}

.image {
  width: 100%;
  display: block;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: '';
}

.clearfix:after {
  clear: both;
}
</style>