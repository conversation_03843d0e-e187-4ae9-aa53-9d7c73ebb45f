<template>
    <div class="container">
        <Modal v-model="_value" :cancel="cancel" :ok="ok" :showFooter="!isViewStatus" :title="title" class="modal"
               v-bind="$attrs" width="50%">
            <el-form ref="form" :disabled="isViewStatus" :model="form" :rules="rules" labelPosition="top"
                     labelWidth="100px">
                <el-row :gutter="20">
                    <FormCard title="基本信息">
                        <ProFormItem label="上级部门" prop="parentId" required span="24">
                            <template #default="props">
                                <Treeselect style="height: 28px" v-model="form[props.bindField]" :options='menuOptions'
                                            :normalizer='normalizer' :show-count='true'
                                            placeholder='选择上级菜单'
                                />
                            </template>
                        </ProFormItem>

                        <ProFormItem :span="24" label="名称" prop="name" required>
                            <template #default="props">
                                <el-input v-model="form[props.bindField]" v-bind="{ ...props, clearable: true }"/>
                            </template>
                        </ProFormItem>

                        <ProFormItem :span="12" label="排序" prop="sort" required>
                            <template #default="props">
                                <el-input v-model="form[props.bindField]" v-bind="{ ...props, clearable: false }"/>
                            </template>
                        </ProFormItem>


                        <ProFormItem :span="12" label="状态" prop="state">
                            <DictSelect v-model="form.state" type="b_coal_status_type"></DictSelect>
                        </ProFormItem>

                    </FormCard>
                </el-row>
            </el-form>
        </Modal>
    </div>
</template>

<script>
import TipModal from '@/utils/modal'
import Modal from '@/components/Common/SnModal/index.vue'
import FormCard from '@/components/Common/SnFormCard/index.vue'
import ProFormItem from '@/components/Common/SnProFormItem/index.vue'
import {deepClone, FetchData} from '@/utils'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
    inheritAttrs: false,
    components: {
        Modal,
        FormCard,
        ProFormItem,
        Treeselect
    },
    data() {
        return {
            form: {},
            rules: {},
            menuOptions: []
        }
    },
    computed: {
        getOptMap() {
            return {
                create: '新增',
                update: '修改',
                view: '查看'
            }
        },
        _value: {
            set(val) {
                this.$emit('input', val)
            },
            get() {
                return this.value
            }
        },
        title() {
            return this.getOptMap[this.optName]
        },
        isViewStatus() {
            return this.optName === 'view'
        },
        isUpdateStatus() {
            return this.optName === 'update'
        },
        isCreateStatus() {
            return this.optName === 'create'
        }
    },
    props: {
        useApi: {
            type: Boolean,
            default: false
        },
        getApi: {
            type: Function
        },
        optName: {
            type: String,
            require: true
        },
        value: {
            type: Boolean,
            require: true
        },
        record: {
            type: Object,
            default() {
                return {}
            }
        },
        model: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    watch: {
        record: {
            async handler(value) {
                await this.getSelectList()
                await this.$nextTick()

                if (this.isCreateStatus) {
                    this.form = {
                        ...value,
                        parentId: value.parentId ? value.parentId : this.menuOptions.length ? this.menuOptions[0].code : '',
                        sort: value.sort ? value.sort : 1,
                        state: value.state ? value.state : 'ZC'
                    }
                    return true
                } else {
                    this.form = this.getFormData(value)
                }
            },
            immediate: true
        }
    },
    methods: {
        normalizer(node) {
            if (node.children && !node.children.length) {
                delete node.children
            }
            return {
                id: node.code,
                label: node.label,
                children: node.children
            }
        },


        async getSelectList() {
            try {
                const {data} = await this.model.list()
                this.menuOptions = data
            } catch (e) {
            }
        },

        getFormData(form = {}) {
            const {...rest} = form
            console.log(rest, 'rest')
            return {
                ...rest
            }
        },
        getSubmitForm() {
            const submitForm = deepClone(this.form)
            const {...rest} = submitForm
            // 返回给后端的数据
            return {
                ...rest,
            }
        },
        async cancel() {
            this.form = {}
            this.$refs['form'].resetFields()
            return false
        },
        async ok() {
            await this.$refs['form'].validate()
            const resp = await this.model.save({...this.getSubmitForm()}, this.optName)
            if (resp) {
                TipModal.msgSuccess(`${this.title}成功`)
                this.$emit('ok')
                return this.cancel()
            }
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-dialog__body {
    padding: 25px 25px;
    height: 50vh;
    overflow: auto;
  }
}
</style>
