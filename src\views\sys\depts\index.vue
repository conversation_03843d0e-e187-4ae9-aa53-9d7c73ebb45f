<template>
    <div class="app-container">
        <SnProTable :ref="pageRef" row-key="code" :actions="actions"
                    :afterFetch="afterFetch" :beforeFetch="beforeFetch"
                    :model="model"
                    :default-expand-all='true'
        >

            <template #opt="{ row }">
                <el-button type="text" v-if="row.code !== '1'" @click="handleUpdate(row)">修改</el-button>
                <el-button type="text" @click="handleCreate(row)">新增</el-button>
                <el-button type="text" v-if="row.code !== '1'" @click="onOptCommand('remove', row)">删除</el-button>
            </template>


        </SnProTable>

        <FormModal
                v-if="addInfo.visitable"
                v-model="addInfo.visitable"
                :model="model"
                :optName="addInfo.optName"
                :record="addInfo.record"
                @ok="getList"
        />

    </div>
</template>

<script>
import model from './model'
import SnProTable from '@/components/Common/SnProTable/index.vue'
import FormModal from './components/FormModal.vue'

import checkPermission from '@/utils/permission'
import TipModal from '@/utils/modal'

export default {
    components: {SnProTable, FormModal},
    data() {
        return {
            model,
            pageRef: 'page',
            permissions: {
                save: '*',
                remove: '*'
            },
            addInfo: {
                optName: 'create',
                visitable: false,
                record: {}
            },

        }
    },
    computed: {
        actions() {
            return [
                {
                    type: 'add',
                    text: '新增',
                    hasPermission: '*',
                    onClick: async (item) => {
                        this.setModal('addInfo')
                    }
                }
            ]
        },

    },
    methods: {
        beforeFetch(params) {
            return {
                ...params,
            }
        },
        afterFetch(data, listKey) {
            function addHasChildrenField(tree) {
                tree.id = tree.code
                tree.parentId = tree.attributes.deptDto.parentId
                tree.createTime = tree.attributes.deptDto.createTime
                tree.state = tree.attributes.deptDto.state
                tree.sort = tree.attributes.deptDto.sort
                // 如果当前节点有子节点
                if (Array.isArray(tree.children) && tree.children.length > 0) {
                    // 递归调用函数处理每个子节点
                    tree.children.forEach(addHasChildrenField);
                } else {
                }
            }

            data.forEach(tree => {
                addHasChildrenField(tree)
            })

            console.log(data, 'data')

            return {
                list: data
            }
        },
        checkPermission,


        onOptCommand(command, row) {
            if (command === 'remove') {
                this.handleRemove(row)
            }
        },

        // 新增时record置为空
        setModal(target, optName = 'create', record = {}) {
            this[target].visitable = true
            this[target].optName = optName
            if (optName) this[target].record = {...record}
        },


        async handleRemove({id}) {
            try {
                const resp = await this.model.remove({id, useConfirm: true})
                if (resp) {
                    TipModal.msgSuccess(`删除成功`)
                    this.getList()
                }

            } catch (error) {
            }
        },


        async handleCreate(row) {
            try {
                this.setModal('addInfo', 'create', {
                    parentId: row.code
                })
            } catch (error) {
                console.log(error, 'error')
            }
        },

        async handleUpdate(row) {
            try {

                this.setModal('addInfo', 'update', {
                    sort: row.sort,
                    state: row.state,
                    parentId: row.parentId,
                    name: row.label,
                    id: row.id,
                })
            } catch (error) {
                console.log(error, 'error')
            }
        },
        async handleView(row) {
            try {
                const {data} = await this.model.get(row.id)
                this.setModal('addInfo', 'view', data)
            } catch (error) {
                console.log(error, 'error')
            }
        },
        getList() {
            this.$refs[this.pageRef].getList()
        }
    }
}
</script>

<style lang="scss"></style>
