// eslint-disable-next-line

import BaseModel, {TableConfig, FormConfig} from '@/components/Common/SnProTable/model'
import {getDate} from '@/utils/dateUtils'
import fetch from "@/utils/request";

export const name = `/cwe/a/dept`

const createFormConfig = () => {
    return {

        filters: [
            // {
            //     label: '部门名称',
            //     prop: 'label',
            // },
            // {
            //     hide: true,
            //     prop: 'orderBy',
            //     defaultValue: 'date'
            // },
            // {
            //     hide: true,
            //     prop: 'orderDir',
            //     defaultValue: 'desc'
            // }
        ]
    }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
    return {
        mountedQuery: true,
        showOpt: true, // 显示操作列
        showIndex: false, // 显示序号列
        showSelection: false, // 显示选择列
        useApiList: true,
        columns: [
            {
                label: '部门名称',
                prop: 'label',
                align: 'left'
            },
            {
                label: '状态',
                prop: 'state',
                format: "dict|b_coal_status_type"
            },
            // {
            //     label: '创建时间',
            //     prop: 'createTime',
            // },
        ]
    }
}

class Model extends BaseModel {
    constructor() {
        super(name, createFormConfig(), createTableConfig())
    }

    delete(id) {
        return super.request({
            url: `/cwe/a/dept/delete`,
            method: 'post',
            data: {id}
        })
    }
    
    list(query) {
        return super.request({
            url: `/cwe/a/dept/findAllTreeDepts`,
            method: 'get',
            params: query
        })
    }
}

export default new Model()
