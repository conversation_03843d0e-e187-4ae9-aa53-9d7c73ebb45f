<template>
  <div class="app-container">
    <s-curd ref="scurd" name="dictList" :model="model" :actions="actions" :list-query="listQuery" title="字典管理" otherHeight="140">
      <el-table-column label="名称" slot="name" prop="name" width="180px">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdit(scope.row)">{{scope.row.name}}</el-button>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog v-if="dialogFormVisible" ref="entityFormDialog" :title="status" width="980px" :before-close="handleClose" top="5vh"
               :visible.sync="dialogFormVisible">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" :rules="entityFormRules"
               style="margin-top: 10px;">
        <el-form-item label="名称" prop="name" :rules="[{required:true, message:'请输入名称'}]">
          <el-input v-model="entityForm.name"></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type" :rules="[{required:true, message:'请输入类型'}]">
          <el-input v-model="entityForm.type"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 5}" placeholder="请输入备注" v-model="entityForm.remarks"
                    resize="none">
          </el-input>
        </el-form-item>
        <!--数据值字段处理-->
        <el-form-item label="添加数据" prop="value" :rules="[{required:true, message:'请添加数据值'}]">
          <el-button icon="el-icon-plus" @click="addData"></el-button>
        </el-form-item>
      </el-form>
      <div style="">
        <el-table border :data="tableHeader" style="width:1000px">
          <el-table-column label="数据值">
            <template slot-scope="scope">
              <div class="wrap">
                <div class="label">名称</div>
                <el-input class="keyValueInput" placeholedr="输入名称" v-model="scope.row.name"></el-input>
              </div>
              <div class="wrap">
                <div class="label">值</div>
                <el-input class="keyValueInput" placeholedr="输入值" v-model="scope.row.code"></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="60">
            <template slot-scope="scope">
              <el-button type="danger" icon="el-icon-delete" @click="removeData(scope.row)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="perms['dictList:save']">提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import Func from '@/utils/func'
import Model from '@/model/sys/dictList'
import { saveDict } from '@/api/common'

const keyValueData = {
  name: '',
  code: '',
}
export default {
  name: 'dictList',
  data() {
    return {
      dialogFormVisible: false,
      option: Model.tableOption,
      model: Model,
      listQuery: {
        orderBy: 'createDate',
        orderDir: 'desc',
      },
      entityFormLoading: false,
      entityForm: [],
      tableHeader: [],
      entityFormRules: {},
      status: '',
      actions: [
        {
          config: {
            type: 'primary',
            plain: true,
            icon: 'el-icon-plus',
          },
          show: 'dictList:save',
          label: '新增',
          click: this.handleCreate,
        },
        {
          config: {
            type: 'warning',
            plain: true,
            icon: 'el-icon-edit',
          },
          show: 'dictList:update',
          label: '修改',
          click: this.handleEdit,
        },
      ],
    }
  },
  computed: {
    ...mapGetters(['perms']),
  },
  methods: {
    handleCreate() {
      this.status = '创建'
      this.entityForm = {}
      this.tableHeader = []
      this.dialogFormVisible = true
    },
    handleEdit(list) {
      let row = {}
      if (Array.isArray(list)) {
        if (list.length !== 1) {
          this.$message({ type: 'warning', message: '请选择且仅选择一条记录操作' })
          return false
        }
        row = list[0]
      } else {
        row = { ...list }
      }
      this.status = '编辑'
      delete row.ext
      this.entityForm = row
      this.tableHeader = JSON.parse(row.value)
      this.dialogFormVisible = true
    },
    handleClose() {
      this.dialogFormVisible = false
    },
    addData() {
      const singleData = { ...keyValueData }
      this.tableHeader.push(singleData)
    },
    removeData(item) {
      this.tableHeader = this.tableHeader.filter((v, i, array) => {
        return v !== item
      })
    },
    getList() {
      this.$refs['scurd'].$emit('refresh')
    },
    async saveEntityForm(formName) {
      const valid = this.$refs[formName].validate()
      if (valid) {
        this.entityFormLoading = true
        this.entityForm.value = JSON.stringify(this.tableHeader)
        const res = await Func.fetch(saveDict, this.entityForm)
        if (res) {
          this.$message({ showClose: true, message: '提交成功', type: 'success' })
          this.dialogFormVisible = false
          this.getList()
        }
        this.entityFormLoading = false
      }
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 20px 35px;
  overflow: auto;
  height: 68vh;
}
.el-table {
  border-right: none;
}
.wrap {
  display: inline-block;
  vertical-align: top;
  margin-top: 7px;
  margin-right: 10px;
}

.keyValueInput {
  width: 166px;
  margin-left: 7px;
}

.label {
  display: inline-block;
  vertical-align: top;
  margin-top: 7px;
}

::v-deep .top-filter {
  // display: none;
  margin-top: 10px;
}
</style>
