<template>
  <div class="app-container">
    <s-curd ref="scurd" name="roleList" :model="model" :actions="actions" :list-query="listQuery" title="字典管理">
      <el-table-column label="名称" slot="fileName" prop="fileName">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdit(scope.row)">{{scope.row.fileName}}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" slot="opt" prop="opt" width="120">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdit(scope.row)">修改</el-button>
          <el-button type="text" @click="handlePreview(scope.row)">查看</el-button>
          <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog v-if="dialogFormVisible" ref="entityFormDialog" :title="status" width="600px" :before-close="handleClose"
               :visible.sync="dialogFormVisible">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" :rules="entityFormRules"
               label-width="90px" style="margin-top: 10px;">
        <el-form-item label="文件名称" prop="fileName" :rules="[{required:true, message:'请输入名称'}]">
          <el-input v-model="entityForm.fileName"></el-input>
        </el-form-item>
        <el-form-item label="上传文件" prop="fileUrl" :rules="[{required:true, message:'请输入类型'}]">
          <el-upload v-if="dialogFormVisible" ref='upload' action="http://1.116.61.138:9010/lims/a/attachment/upload"
                     list-type="file" :headers="headers" :limit="imgLimit" :file-list="productImgs" :on-remove="handleRemove"
                     :on-success="handleAvatarSuccess" :on-exceed="handleExceed" :on-change="handleEditChange"
                     :class="{hide:hideUploadEdit}">
            <el-button type="primary">上传文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="entityFormLoading" @click="handleSave('entityForm')" v-if="perms['document:save']">提交
        </el-button>
      </div>
    </el-dialog>
    <!--        <el-dialog-->
    <!--                ref="entityFormDialog"-->
    <!--                title="查看"-->
    <!--                width="600px"-->
    <!--                :before-close="handleClose"-->
    <!--                :visible.sync="imgVisible">-->
    <!--            <el-image :src="previewList">-->
    <!--            </el-image>-->
    <!--        </el-dialog>-->
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import Model from '@/model/sys/document'
import { getToken } from '@/utils/auth'

const token = getToken()
export default {
  name: 'document',
  data() {
    return {
      imgVisible: false,
      headers: {
        Authorization: token,
      },
      fileVisible: false,
      dialogFormVisible: false,
      imgLimit: 1,
      productImgs: [],
      option: Model.tableOption,
      hideUploadEdit: false,
      model: Model,
      listQuery: {
        orderBy: 'createDate',
        orderDir: 'desc',
      },
      entityFormLoading: false,
      entityForm: [],
      tableHeader: [],
      entityFormRules: {},
      status: '',
      actions: [
        {
          config: {
            type: 'primary',
            plain: true,
            icon: 'el-icon-plus',
          },
          show: 'dictList:save',
          label: '新增',
          click: this.handleCreate,
        },
      ],
      previewList: '',
      fileList: [],
    }
  },
  computed: {
    ...mapGetters(['perms']),
  },
  methods: {
    // 删除信息
    async handleDelete(row) {
      this.$confirm('确认删除？', '删除', {
        distinguishCancelAndClose: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消',
      })
        .then(async () => {
          const res = await this.model.delete(row.id)
          if (res) {
            this.$notify.success('删除成功')
            this.getList()
          }
        })
        .catch((action) => {
          this.$message({
            type: 'info',
            message: '已取消删除操作',
          })
        })
    },
    async handleSave() {
      this.entityForm.fileUrl = this.fileList[0].url
      this.entityFormLoading = true
      let res = await Model.save({
        ...this.entityForm,
      })
      this.entityFormLoading = false
      if (res.data) {
        this.$message.success('保存信息成功')
        this.getList()
        this.handleClose()
      }
    },
    handleEditChange(file, fileList) {
      let vm = this
      vm.hideUploadEdit = fileList.length >= this.imgLimit
    },
    handleRemove(file, fileList) {
      this.productImgs = fileList
    },
    handleExceed(files, fileList) {
      this.$message.error('上传文件不能超1个!')
    },
    handleAvatarSuccess(res, file) {
      this.entityForm.fileSuffix = res.suffix
      this.fileList.push(res)
    },
    async handleEdit(row) {
      let obj = {
        name: row.fileUrl,
        url: row.fileUrl,
      }
      this.productImgs.push(obj)
      this.status = '编辑'
      this.entityForm = { ...row }
      this.dialogFormVisible = true
    },
    async handlePreview(row) {
      window.open(row.fileUrl)
      // this.imgVisible = true
      // this.previewList = row.fileUrl
    },
    handleCreate() {
      this.status = '创建'
      this.entityForm = {}
      this.dialogFormVisible = true
    },
    handleClose() {
      this.entityForm = {}
      this.productImgs = []
      this.dialogFormVisible = false
      this.imgVisible = false
    },
    getList() {
      this.$refs['scurd'].$emit('refresh')
    },
  },
}
</script>
<style lang="scss" scoped>
.wrap {
  display: inline-block;
  vertical-align: top;
  margin-top: 7px;
  margin-right: 10px;
}

.keyValueInput {
  width: 166px;
  margin-left: 7px;
}

.label {
  display: inline-block;
  vertical-align: top;
  margin-top: 7px;
  background: #efefef;
}
</style>
