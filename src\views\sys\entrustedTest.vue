<template>
    <div class="app-container">
        <el-tabs type="border-card" v-model="paneType" @tab-click="switchPane">
            <!--            <el-tab-pane name="JM">-->
            <!--                <span slot="label"><i class="el-icon-date"></i>精煤</span>-->
            <!--                <s-curd @selectItem="selectItem" ref="testJMIndex" name="testJMIndex" :model="model"-->
            <!--                        :list-query="listQuery" :actions="actions"-->
            <!--                        title="精煤">-->
            <!--                    <el-table-column label="类别名" slot="name" prop="name" width="300px">-->
            <!--                        <template slot-scope="scope">-->
            <!--                            <el-button type="text" @click="handleEdit(scope.row)">{{scope.row.name}}</el-button>-->
            <!--                        </template>-->
            <!--                    </el-table-column>-->
            <!--                    <el-table-column label="操作" slot="opt" prop="opt" width="120px">-->
            <!--                        <template slot-scope="scope">-->
            <!--                            <el-button type="text" @click="handleContact(scope.row)">关联化验项</el-button>-->
            <!--                        </template>-->
            <!--                    </el-table-column>-->
            <!--                </s-curd>-->
            <!--            </el-tab-pane>-->
            <el-tab-pane name="MT">
                <span slot="label"><i class="el-icon-date"></i>煤炭</span>
                <s-curd @selectItem="selectItem" ref="testMTIndex" name="testMTIndex" :model="model"
                        :list-query="listQuery" :actions="actions"
                        title="原煤">
                    <el-table-column label="类别名" slot="name" prop="name" width="300px">
                        <template slot-scope="scope">
                            <el-button type="text" @click="handleEdit(scope.row)">{{scope.row.name}}</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" slot="opt" prop="opt" width="120px">
                        <template slot-scope="scope">
                            <el-button type="text" @click="handleContact(scope.row)">关联化验项</el-button>
                        </template>
                    </el-table-column>
                </s-curd>
            </el-tab-pane>
            <el-tab-pane name="JT">
                <span slot="label"><i class="el-icon-date"></i>焦炭</span>
                <s-curd @selectItem="selectItem" ref="testJTIndex" name="testJTIndex" :model="model"
                        :list-query="listQuery" :actions="actions"
                        title="焦炭">
                    <el-table-column label="类别名" slot="name" prop="name" width="300px">
                        <template slot-scope="scope">
                            <el-button type="text" @click="handleEdit(scope.row)">{{scope.row.name}}</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" slot="opt" prop="opt" width="120px">
                        <template slot-scope="scope">
                            <el-button type="text" @click="handleContact(scope.row)">关联化验项</el-button>
                        </template>
                    </el-table-column>
                </s-curd>
            </el-tab-pane>
        </el-tabs>
        <el-dialog ref="dialogStatus" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
                   :before-close="handleClose"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   width="60%"
                   top="8vh"
        >
            <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm"
                     label-width="90px"
                     v-if="dialogFormVisible">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="名称" prop="name" :rules="[{required:true, message:'请输入名称'}]">
                            <el-input v-model="entityForm.name" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="简称" prop="aliasName">
                            <el-input v-model="entityForm.aliasName" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="编号" prop="code">
                            <el-input v-model="entityForm.code" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="价格" prop="price">
                            <el-input v-model="entityForm.price" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注信息" prop="remarks">
                            <el-input v-model="entityForm.remarks" type="textarea" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item>
                    <el-table ref="testAssay" :data="testAssayList" border height="400"  v-if="dialogFormVisible"
                              @selection-change="handleSelectionChange">
                        <el-table-column
                                type="selection"
                                width="55">
                        </el-table-column>
                        <el-table-column
                                prop="categoryName"
                                label="类别"
                                width="300">
                        </el-table-column>
                        <el-table-column
                                prop="name"
                                label="名称"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="mark"
                                label="符号"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="unit"
                                label="单位"
                        >
                        </el-table-column>
                    </el-table>
                    <!--                    <div>化验指标</div>-->
                    <!--                    <div class="resources-list">-->
                    <!--                        <div>-->
                    <!--                            <div class="c" v-for="c1 in assayList" :key="c1.id">-->
                    <!--                                <div class="name" style="padding-left: 20px" v-if="c1.type === paneType ">-->
                    <!--                                    <el-checkbox-->
                    <!--                                            v-model="c1.select"-->
                    <!--                                            true-label="Y"-->
                    <!--                                            false-label="N"-->
                    <!--                                    >-->
                    <!--                                        <span class="blue-text">类别：</span>{{c1.categoryName}}-->
                    <!--                                        <span class="blue-text">名称：</span>{{c1.name}}-->
                    <!--                                    </el-checkbox>-->
                    <!--                                </div>-->
                    <!--                            </div>-->
                    <!--                        </div>-->
                    <!--                        &lt;!&ndash;                        </div>&ndash;&gt;-->
                    <!--                    </div>-->
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" :loading="entityFormLoading" @click="handleSave('entityForm')">保存
                </el-button>
            </div>
        </el-dialog>
        <el-dialog ref="contactStatus" title="关联指标" :visible.sync="contactVisible"
                   :before-close="handleClose"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   width="60%"
                   top="6vh"
        >
            <div class="resources-list" v-if="contactVisible">
                <!--特性区-->
                <div v-for="(c,i) in assayDict" :key="i">
                    <div class="name" style="font-weight: bold">
                        {{i+1}}.{{c.name}}
                    </div>
                    <div style="width:100%">
                                <span class="row" v-for="c1 in assayList" :key="c1.id">
                                    <span class="name" v-if="c1.type === c.value">
                                        <el-checkbox :label="c.name"
                                                     v-model="c1.select"
                                                     true-label="Y"
                                                     false-label="N"
                                        >
                                            {{c1.name}}({{c1.price}})
                                        </el-checkbox>
                                    </span>
                                </span>
                    </div>
                </div>
            </div>
            <!--            <el-tabs v-if="contactVisible" type="border-Card" v-model="tableType" @tab-click="switchTablePane">-->
            <!--                <el-tab-pane name="TABLTJM">-->
            <!--                    <span slot="label"><i class="el-icon-date"></i>精煤</span>-->
            <!--                    <el-table :data="JMList" border @selection-change="handleTableJMChange">-->
            <!--                        <el-table-column-->
            <!--                                type="selection"-->
            <!--                                width="55">-->
            <!--                        </el-table-column>-->
            <!--                        <el-table-column-->
            <!--                                prop="name"-->
            <!--                                label="类别名"-->
            <!--                                min-width="180">-->
            <!--                        </el-table-column>-->
            <!--                        <el-table-column-->
            <!--                                prop="code"-->
            <!--                                label="编号">-->
            <!--                        </el-table-column>-->
            <!--                        <el-table-column-->
            <!--                                prop="price"-->
            <!--                                label="价格">-->
            <!--                        </el-table-column>-->
            <!--                        <el-table-column-->
            <!--                                prop="remarks"-->
            <!--                                label="备注">-->
            <!--                        </el-table-column>-->
            <!--                    </el-table>-->
            <!--                </el-tab-pane>-->
            <!--                <el-tab-pane name="TABLEYM">-->
            <!--                    <span slot="label"><i class="el-icon-date"></i>原煤</span>-->
            <!--                    <el-table :data="YMList" border>-->
            <!--                        <el-table-column-->
            <!--                                type="selection"-->
            <!--                                width="55">-->
            <!--                        </el-table-column>-->
            <!--                        <el-table-column-->
            <!--                                prop="name"-->
            <!--                                label="类别名"-->
            <!--                                min-width="180">-->
            <!--                        </el-table-column>-->
            <!--                        <el-table-column-->
            <!--                                prop="code"-->
            <!--                                label="编号">-->
            <!--                        </el-table-column>-->
            <!--                        <el-table-column-->
            <!--                                prop="price"-->
            <!--                                label="价格">-->
            <!--                        </el-table-column>-->
            <!--                        <el-table-column-->
            <!--                                prop="remarks"-->
            <!--                                label="备注">-->
            <!--                        </el-table-column>-->
            <!--                    </el-table>-->
            <!--                </el-tab-pane>-->
            <!--                <el-tab-pane name="TABLEJT">-->
            <!--                    <span slot="label"><i class="el-icon-date"></i>焦炭</span>-->
            <!--                    <el-table :data="JTList" border>-->
            <!--                        <el-table-column-->
            <!--                                type="selection"-->
            <!--                                width="55">-->
            <!--                        </el-table-column>-->
            <!--                        <el-table-column-->
            <!--                                prop="name"-->
            <!--                                label="类别名"-->
            <!--                                min-width="180">-->
            <!--                        </el-table-column>-->
            <!--                        <el-table-column-->
            <!--                                prop="code"-->
            <!--                                label="编号">-->
            <!--                        </el-table-column>-->
            <!--                        <el-table-column-->
            <!--                                prop="price"-->
            <!--                                label="价格">-->
            <!--                        </el-table-column>-->
            <!--                        <el-table-column-->
            <!--                                prop="remarks"-->
            <!--                                label="备注">-->
            <!--                        </el-table-column>-->
            <!--                    </el-table>-->
            <!--                </el-tab-pane>-->
            <!--            </el-tabs>-->
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" :loading="entityFormLoading" @click="handleContactSave">关联
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {mapGetters} from 'vuex'
    import Model from '@/model/sys/entrustedTest'
    import ActModel from '@/model/sys/actualTest'
    import Cache from '@/utils/cache'

    export default {
        name: 'roleList',
        data() {
            return {
                paneType: 'MT',
                form: {},
                assayDict: [],
                assayList: [],
                testAssayList: [],
                selectAssayList: [],
                dialogFormVisible: false,
                tableType: 'TABLTJM',
                contactVisible: false,
                entityForm: {
                    state: 'ZC'
                },
                textMap: {
                    update: '编辑',
                    create: '创建'
                },
                dialogStatus: '',
                entityFormLoading: false,
                actions: [
                    {
                        config: {
                            type: 'primary',
                            plain: true,
                            icon: 'el-icon-plus'
                        },
                        click: this.handleCreate,
                        show: 'entrustedTest:save',
                        label: '新增'
                    },
                    {
                        config: {
                            type: 'danger',
                            plain: true,
                            icon: 'el-icon-delete'
                        },
                        click: this.handleDelete,
                        show: 'entrustedTest:delete',
                        label: '删除'
                    }
                ],
                model: Model,
                listQuery: {
                    orderBy: 'createDate',
                    orderDir: 'desc',
                    filter_EQS_type: 'MT'
                }
                // assayDict: [],
                // assayList: [],
            }
        },
        computed: {
            ...mapGetters([
                'perms'
            ])
        },
        watch: {
            testAssayList(val) {

            }
        },
        created() {
            this.loadMenuTree()
        },
        methods: {
            selectItem(list) {
                this.selectList = list
            },
            // 删除信息
            async handleDelete() {
                if (this.selectList.length === 0) {
                    this.$notify.error('请选择一条记录')
                    return false
                }
                try {
                    await this.$confirm('确认删除?')
                    const ids = this.selectList.map(val => val.id)
                    const res = await this.model.delete(ids)
                    if (res) {
                        this.$notify.success('删除成功')
                        this.getList()
                    }
                } catch (e) {
                }
            },
            handleSelectionChange(list) {
                this.selectAssayList = [...list]
            },
            // 获取新增化验指标
            async getItem(id, code) {
                this.assayDict = Cache.get('dict')['s_assay_type']
                let res = await this.model.pageList()
                if (res.data) {
                    if (!code) {
                        const list = res.data
                        const newList = []
                        list.map(item => {
                            item.select = 'N'
                            if (item.id !== id) {
                                newList.push(item)
                            }
                        })
                        this.assayList = newList
                    } else {
                        const codeList = code.split(',')
                        const list = res.data
                        const newList = []
                        list.map(item => {
                            codeList.map(code => {
                                if (Number(code) === Number(item.code)) {
                                    item.select = 'Y'
                                }
                            })
                            if (item.id !== id) {
                                newList.push(item)
                            }
                        })
                        this.assayList = newList
                    }
                }
            },
            async handleContactSave() {
                let assayItemList = []
                this.assayList.map(item => {
                    if (item.select === 'Y') {
                        assayItemList.push(item)
                    }
                })
                let res = await Model.saveRelItemCode({id: this.form.id, assayItemList})
                if (res) {
                    this.getList()
                    this.$message.success('关联成功')
                    this.handleClose()
                }
            },
            switchPane(tab) {
                this.paneType = tab.paneName
                this.listQuery.filter_EQS_type = tab.paneName
            },
            async loadMenuTree() {
                this.assayDict = Cache.get('dict')['s_assay_type']
                let res = await ActModel.pageList()
                if (res.data) {
                    let list = res.data
                    list.map(item => {
                        item.select = 'N'
                        return item
                    })
                    this.testAssayList = list
                }
            },
            // 刷新页面
            getList() {
                this.$refs.testJTIndex.$emit('refresh')
                this.$refs.testMTIndex.$emit('refresh')
            },
            // 新建
            handleCreate() {
                this.loadMenuTree()
                this.dialogStatus = 'create'
                this.dialogFormVisible = true
            },
            // 保存
            async handleSave() {
                this.entityFormLoading = true
                const indexMetaIds = []
                this.selectAssayList.map(item => {
                    indexMetaIds.push(item.id)
                })
                this.entityForm.type = this.paneType
                let res = await Model.saveWithMultiIndexMetas({...this.entityForm, indexMetaIds})
                this.entityFormLoading = false
                if (res.data) {
                    this.$message.success('保存信息成功')
                    this.getList()
                    this.handleClose()
                }
            },
            // 修改
            async handleEdit(row) {
                // this.loadMenuTree()
                let res = await Model.getById({id: row.id})
                if (res) {
                    this.dialogFormVisible = true
                    this.dialogStatus = 'update'
                    this.entityForm = {...row}
                    const list = res.data.indexMetaIds
                    list.forEach(item => {
                        this.testAssayList.map(asc => {
                            if (item === asc.id) {
                                asc.select = 'Y'
                            }
                        })
                    })
                    this.checkboxInit()
                }
            },
            checkboxInit() {
                let that = this
                that.$nextTick(() => {
                    if (that.$refs.testAssay) {
                        that.$refs.testAssay.clearSelection()
                    }
                    that.testAssayList.forEach(row => {
                        if (row.select === 'Y') {
                            that.$refs.testAssay.toggleRowSelection(
                                row,
                                true
                            )
                        }
                    })
                })
            },
            // 关联化验项
            async handleContact(row) {
                this.getItem(row.id, row.relItemCode)
                this.form.id = row.id
                this.contactVisible = true
                // }
            },
            // 重置
            checkNone() {
                this.assayList.map(item => {
                    item.select = 'N'
                    return item
                })
                this.testAssayList.map(item => {
                    item.select = 'N'
                    return item
                })
            },
            // 关闭弹窗
            handleClose() {
                this.checkNone()
                this.entityForm = {}
                this.dialogFormVisible = false
                this.contactVisible = false
            }
        }
    }
</script>
<style lang="scss" scoped>
    .blue-text {
        color: #2d8cf0;
    }

    .name {
        width: 40%;
        line-height: 25px;
        display: flex;
    }
</style>
