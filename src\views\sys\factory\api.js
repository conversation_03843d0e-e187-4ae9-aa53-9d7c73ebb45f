import fetch from "@/utils/request";

const urlPrefix = "/a/user";

export function getDetail(id) {
  return fetch({
    url: urlPrefix + "/getDetail",
    method: "get",
    params: { id: id },
  });
}

export function save(entity) {
  return fetch({
    url: urlPrefix + "/saveDynamic",
    method: "post",
    data: entity,
  });
}

export function deleteById(id) {
  return fetch({
    url: urlPrefix + "/delete",
    method: "post",
    data: { id: id },
  });
}

export function getById(id) {
  return fetch({
    url: urlPrefix + "/get",
    method: "get",
    params: { id: id },
  });
}

export function page(searchForm) {
  return fetch({
    url: urlPrefix + "/page",
    method: "get",
    params: searchForm,
  });
}

/**
 * 查找货主
 */
export function findOwners({ keyword, warehouseId }) {
  return fetch({
    url: urlPrefix + "/findOwners",
    method: "get",
    params: {
      keyword,
      warehouseId,
    },
  });
}

/**
 * 根据获取id获取联系人
 * @param ownerId
 */
export function listByOwnerId(ownerId) {
  return fetch({
    url: "/a/receiver/listByOwnerId",
    method: "get",
    params: {
      ownerId,
    },
  });
}

/*
 * 修改密码
 * */
export function modifyPassword(data) {
  return fetch({
    url: urlPrefix + "/modifyPassword",
    method: "post",
    data: data,
  });
}

export function saveWithRoles(entity) {
  return fetch({
    url: urlPrefix + "/save",
    method: "post",
    data: entity,
  });
}

/**
 * 根据角色ID查询用户
 * @param searchForm
 */
export function findByRoleId(searchForm) {
  return fetch({
    url: urlPrefix + "/findByRoleId",
    method: "get",
    params: searchForm,
  });
}

/**
 * 根据角色ID查询不属于该角色的用户
 * @param searchForm
 */
export function findNotInRoleId(searchForm) {
  return fetch({
    url: urlPrefix + "/findNotInRoleId",
    method: "get",
    params: searchForm,
  });
}

/**
 * 保存角色用户关联关系
 * @param roleId
 * @param userIdList
 */
export function addRoleUser({ roleId, userIdList }) {
  return fetch({
    url: urlPrefix + "/addRoleUser",
    method: "post",
    data: { roleId, userIdList },
  });
}

/**
 * 移除角色用户关联关系
 * @param roleId
 * @param userIdList
 */
export function delRoleUser({ roleId, userIdList }) {
  return fetch({
    url: urlPrefix + "/delRoleUser",
    method: "post",
    data: { roleId, userIdList },
  });
}

/*
 *重置密码
 * */
export function resetPassword(data) {
  return fetch({
    url: urlPrefix + "/resetPassword",
    method: "post",
    data: data,
  });
}

export function findByKeyword(keyword) {
  return fetch({
    url: urlPrefix + "/findByKeyword",
    method: "get",
    params: { keyword },
  });
}

export function pageMenu(filters) {
  return fetch({
    url: "/a/user/findByFactoryCode",
    method: "get",
    params: filters,
  });
}
