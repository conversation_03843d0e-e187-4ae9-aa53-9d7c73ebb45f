<template>
  <div class="container">
    <Modal v-model="_value" :cancel="cancel" :ok="ok" :showFooter="!isViewStatus" :title="title" class="modal" v-bind="$attrs" width="50%">
      <el-form ref="form" :disabled="isViewStatus" :model="form" :rules="rules" labelPosition="top" labelWidth="100px">
        <el-row :gutter="20">
          <FormCard title="基本信息">
            <ProFormItem label="姓名" prop="name" required span="24">
              <template #default="props">
                {{ form[props.bindField] }}
              </template>
            </ProFormItem>

            <ProFormItem label="新密码" prop="password" required span="24">
              <template #default="props">
                <el-input v-model="form[props.bindField]" type="password" v-bind="{ ...props, clearable: true }" />
              </template>
            </ProFormItem>
          </FormCard>
        </el-row>
      </el-form>
    </Modal>
  </div>
</template>

<script>
import TipModal from '@/utils/modal'
import Modal from '@/components/Common/SnModal/index.vue'
import FormCard from '@/components/Common/SnFormCard/index.vue'
import ProFormItem from '@/components/Common/SnProFormItem/index.vue'
import { deepClone, FetchData } from '@/utils'

export default {
  inheritAttrs: false,
  components: {
    Modal,
    FormCard,
    ProFormItem
  },
  data() {
    return {
      form: {},
      rules: {}
    }
  },
  computed: {
    getOptMap() {
      return {
        create: '新增',
        update: '修改密码',
        view: '查看'
      }
    },
    _value: {
      set(val) {
        this.$emit('input', val)
      },
      get() {
        return this.value
      }
    },
    title() {
      return this.getOptMap[this.optName]
    },
    isViewStatus() {
      return this.optName === 'view'
    },
    isUpdateStatus() {
      return this.optName === 'update'
    },
    isCreateStatus() {
      return this.optName === 'create'
    }
  },
  props: {
    useApi: {
      type: Boolean,
      default: false
    },
    getApi: {
      type: Function
    },
    optName: {
      type: String,
      require: true
    },
    value: {
      type: Boolean,
      require: true
    },
    record: {
      type: Object,
      default() {
        return {}
      }
    },
    model: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    record: {
      async handler(value) {
        await this.$nextTick()
        if (this.isCreateStatus) {
          this.form = {
            ...value
          }
          return true
        }
        if (this.useApi) {
          if (!value.id) throw new Error('使用useApi时record必须包含id字段')
          const impFunc = this.getApi ? this.getApi : this.model.get
          const form = await FetchData(impFunc, value.id, {})
          this.form = this.getFormData(form)
          return true
        }
        this.form = this.getFormData(value)
      },
      immediate: true
    }
  },
  methods: {
    onSelect(type, value, info) {
      if (type === '') {
      }
    },
    async getSelectList(code, target) {
      switch (target) {
        default:
      }
    },
    getFormData(form) {
      const { ...rest } = form
      return {
        ...rest,
        password: ''
      }
    },
    getSubmitForm() {
      const submitForm = deepClone(this.form)
      const { ...rest } = submitForm
      // 返回给后端的数据
      return {
        userId: rest.id,
        newPassword: rest.password
      }
    },
    async cancel() {
      this.form = {}
      this.$refs['form'].resetFields()
      return false
    },
    async ok() {
      await this.$refs['form'].validate()
      await this.model.resetPassword({ ...this.getSubmitForm() })
      TipModal.msgSuccess(`${this.title}成功`)
      this.$emit('ok')
      return this.cancel()
    }
  }
}
</script>

<style lang="scss" scoped></style>
