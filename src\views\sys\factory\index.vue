<template>
  <div class="app-container">
    <ProTable :ref="pageRef" :actions="actions" :model="model">
      <template #factory="{ col }">
        <el-table-column v-bind="col">
          <template #default="{ row }"></template>
        </el-table-column>
      </template>
      <template #actionOther>
        <el-button @click="handleCreate">新增</el-button>
      </template>
      <template #isShareCoal="{ col }">
        <el-table-column v-bind="col">
          <template #default="{ row }">
            <!-- 11111 -->
            <dict-select
              :value="row.isShareCoal === 'Y'"
              name="switch"
              type="s_yes_or_no"
              @input="
                (val) => {
                  row.isShareCoal = val ? 'Y' : 'N';
                  onDelFlagChange(row, val);
                }
              "
            />
            <!-- <DictTag v-else :value="row.isShareCoal" type="s_yes_or_no" /> -->
          </template>
        </el-table-column>
      </template>

      <template #opt="{ row }">
        <!-- v-if="checkPermission([permissions.create])" -->
        <el-button type="text" @click="handleUpdate(row)">编辑 </el-button>
      </template>
    </ProTable>

    <FormModal
      v-if="addInfo.visitable"
      v-model="addInfo.visitable"
      :model="model"
      :optName="addInfo.optName"
      :record="addInfo.record"
      @ok="getList"
    />

    <ResetPwdModal
      v-if="resetPwdInfo.visitable"
      v-model="resetPwdInfo.visitable"
      :model="model"
      :optName="resetPwdInfo.optName"
      :record="resetPwdInfo.record"
      useApi
      @ok="getList"
    />
  </div>
</template>

<script>
// import UserExtTag from './components/UserExtTag'
import model from "./model";
import ProTable from "@/components/Common/SnProTable/index.vue";
import DictSelect from "@/components/DictSelect/index.vue";
// import DictTag from "@/components/Common/SnDictTag/index.vue";
import FormModal from "./components/FormModal.vue";
import ResetPwdModal from "./components/ResetPwdModal.vue";

import checkPermission from "@/utils/permission";
import TipModal from "@/utils/modal";

export default {
  name: "User",
  components: {
    ProTable,
    DictSelect,
    // DictTag,
    FormModal,
    ResetPwdModal,
  },
  data() {
    return {
      model,
      pageRef: "page",
      permissions: {
        save: "user:save",
        remove: "user:delete",
        modify: "user:modify",
        list: "user:list",
      },
      addInfo: {
        optName: "create",
        visitable: false,
        record: {},
      },
      resetPwdInfo: {
        optName: "create",
        visitable: false,
        record: {},
      },
    };
  },
  mounted() {
    console.log(
      "权限校验结果：",
      this.checkPermission([this.permissions.save])
    );
  },
  computed: {
    actions() {
      return [
        {
          type: "add",
          text: "新增",
          hasPermission: [this.permissions.save],
          onClick: async (item) => {
            await this.handleCreate();
          },
        },
      ];
    },
  },
  methods: {
    async onDelFlagChange(row, val) {
      console.log(row, val);
      try {
        const isShareCoalValue = val ? "Y" : "N";
        await this.model.save({ ...row, isShareCoal: isShareCoalValue });
        TipModal.msgSuccess(`操作成功`);
        this.getList();
      } catch (e) {
        TipModal.msgError("操作失败");
      }
    },
    onOptCommand(command, row) {
      if (command === "resetPwd") {
        this.setModal("resetPwdInfo", "update", row);
      }
      if (command === "remove") {
        this.handleRemove(row);
      }
    },

    checkPermission,

    // 新增时record置为空
    setModal(target, optName = "create", record = {}) {
      this[target].visitable = true;
      this[target].optName = optName;
      if (optName) this[target].record = { ...record };
    },

    async handleRemove({ id }) {
      try {
        await this.model.remove({ id, useConfirm: true });
        TipModal.msgSuccess(`删除成功`);
        this.getList();
      } catch (error) {}
    },
    async handleCreate(row) {
      try {
        this.setModal("addInfo", "create", { ...row, isShareCoal: "N" });
      } catch (error) {
        console.log(error, "error");
      }
    },
    async handleUpdate(row) {
      try {
        this.setModal("addInfo", "update", row);
      } catch (error) {
        console.log(error, "error");
      }
    },
    async handleView(row) {
      try {
        const { data } = await this.model.get(row.id);
        this.setModal("addInfo", "view", data);
      } catch (error) {
        console.log(error, "error");
      }
    },
    getList() {
      this.$refs[this.pageRef].getList();
    },
  },
};
</script>

<style lang="scss"></style>
