import BaseModel from "@/components/Common/SnProTable/model";

const name = `/cwe/a/factory`;

const createFormConfig = () => {
  return {
    filters: [
      {
        label: "姓名",
        prop: "filter_LIKES_name",
        labelWidth: 40,
      },
      {
        label: "登录名",
        prop: "filter_LIKES_loginName",
        labelWidth: 55,
      },
      {
        label: "设备号",
        prop: "filter_LIKES_deviceCode",
        labelWidth: 55,
      },
      {
        label: "手机",
        prop: "filter_EQS_mobile",
        labelWidth: 40,
      },
      {
        hide: true,
        prop: "orderBy",
        defaultValue: "createDate",
      },
      {
        hide: true,
        prop: "orderDir",
        defaultValue: "desc",
      },
    ],
  };
};

const createTableConfig = () => {
  return {
    // optConfig: { width: 170, label: '操作', prop: 'opt', fixed: 'right' }, // 默认表格操作列配置
    showOpt: true, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: false, // 显示选择列
    columns: [
      {
        label: "工厂名称",
        minWidth: 120,
        prop: "name",
        sortable: true,
      },
      {
        label: "编号",
        prop: "code",
        minWidth: 120,
      },
      {
        label: "是否分享煤源",
        prop: "isShareCoal",
        slot: "isShareCoal",
        minWidth: 120,
      },
      {
        label: "是否禁用",
        prop: "delFlag",
        slot: "delFlag",
        minWidth: 100,
      },
      {
        label: "备注",
        prop: "remarks",
        minWidth: 140,
        showOverflowTooltip: true,
      },
    ],
  };
};

class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig());
  }

  get(id) {
    return super.request({
      url: `${name}/getDetail`,
      method: "get",
      params: { id },
    });
  }
  save(data) {
    return super.request({
      url: `${name}/saveFactory`,
      method: "post",
      data,
    });
  }
  resetPassword(data) {
    return super.request({
      url: `${name}/resetPassword`,
      method: "post",
      data,
    });
  }
}

export default new Model();
