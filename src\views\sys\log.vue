<template>
  <div class="app-container">
    <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                  @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false" :showAdd="perms[`${curd}:save`]||false"
                  :showRefresh="perms[`${curd}:Refresh`]||false" />
    <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="日志" otherHeight="130">
      <el-table-column label="品名" slot="name" prop="name" width="150" align="center">
        <template slot-scope="scope"><span class="name" @click="handleWatchForm(scope.row)">{{scope.row.name}}</span></template>
      </el-table-column>
      <el-table-column label="挥发分Vdaf" slot="cleanVdaf" min-width="100" align="center">
        <template slot-scope="scope">
          <div class="tags">
            <el-tag class="tag" effect="plain" type="info">
              {{scope.row.cleanVdaf}}
            </el-tag>
            <span>-</span>
            <el-tag class="tag" effect="plain" type="warning">
              {{scope.row.cleanVdaf2}}
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="Y" slot="procY" min-width="100" align="center">
        <template slot-scope="scope">
          <div class="tags">
            <el-tag class="tag" effect="plain" type="info">
              {{scope.row.procY}}
            </el-tag>
            <span>-</span>
            <el-tag class="tag" effect="plain" type="warning">
              {{scope.row.procY2}}
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="是否开放接口" slot="isPublic" prop="isPublic" width="100" v-if="perms[`${curd}:interface`]||false">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isPublicBoolean === true ? 'success' : 'warning'">
            {{scope.row.isPublicBoolean === true ?'是':'否'}}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" slot="opt" max-width="100" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tag class="opt-btn" color="#FF9639" @click="handleUpdate(scope.row)"
                  v-if="perms[`${curd}:update`]||false">编辑</el-tag>
          <el-tag class="opt-btn" color="#2f79e8" @click="handleDel(scope.row)" v-if="perms[`${curd}:delete`]||false">删除</el-tag>
        </template>
      </el-table-column>
    </s-curd>
  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/sys/log'
export default {
  name: 'log',
  mixins: [Mixins],
  data() {
    return {
      curd: 'log',
      model: Model,
      filterOption: {
        showMore: true,
        columns: [
          {
            prop: 'startDate',
            component: 'date-time-select',
            filter: 'filter_GES_createDate',
            props: {
              placeholder: '开始时间',
              format: 'yyyy-MM-dd HH:mm:ss',
              defaultTime: '00:00:00',
              clearable: false,
              type: 'datetime'
            }
          },
          {
            prop: 'endDate',
            component: 'date-time-select',
            filter: 'filter_LES_createDate',
            props: {
              placeholder: '结束时间',
              format: 'yyyy-MM-dd HH:mm:ss',
              clearable: false,
              defaultTime: '23:59:59',
              type: 'datetime'
            }
          },
          {
            prop: 'loginName',
            filter: 'filter_LIKES_loginName',
            props: { placeholder: '请输入用户名', clearable: true }
          },
          {
            prop: 'module',
            filter: 'filter_LIKES_module',
            props: {
              placeholder: '请输入模块',
              clearable: true
            }
          },
          {
            prop: 'method',
            filter: 'filter_LIKES_method',
            props: {
              placeholder: '请输入操作',
              clearable: true
            }
          }
        ]
      },
      entityForm: { ...Model.model },
      rules: {},
      actions: [],
      memoryEntity: { fields: {}, triggered: false }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.tags {
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    margin: 0 3px;
  }
  .tag {
    width: 30px;
    height: 25px;
    line-height: 25px;
    text-align: center;
  }
}
::v-deep .form-item {
  width: 160px;
}
</style>
