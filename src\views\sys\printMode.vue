<template>
  <div class="app-container">
    <el-tabs :style="{height:tableHeight}" ref="eltabs" v-model="activeName" @tab-click="handleTabClick" type="border-card">
      <el-tab-pane ref="assay" label="化验报告模板" name="assay">
        <div style="margin: 10px; font-size: 16px">打印模板列表：</div>
        <div class="fixed">
          <el-link style="color: #409eff" @click="handleAssayDowload">下载化验报告模板</el-link>
        </div>
        <div class="listContent">
          <el-card style="width: 250px; height: 80px; margin: 10px; background-color: #ffffff;" v-for="(list, index) in assayList"
                   :key="list.id">
            <div>
              <span style="font-size: 13px">{{list.name}} </span>
              <el-link style="margin-left: 6px;color: #409eff" icon="el-icon-edit" @click="handleEdit(list)">编辑
              </el-link>
            </div>
            <div style="margin-top: 5px">
              <el-link v-show="activeName === 'assay' && list.content" style="margin-left: 6px;color: #409eff"
                       icon="el-icon-download" @click="handleDowl(list)">下载模板
              </el-link>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane ref="contract" label="合同报告模板" name="contract">
        <div style="margin: 10px; font-size: 16px">打印模板列表：</div>
        <div class="fixed">
          <el-link style="color: #409eff" @click="handleContractDowload">下载合同报告模板</el-link>
        </div>
        <div class="listContent">
          <el-card style="width: 250px; height: 80px; margin: 10px; background-color: #ffffff;"
                   v-for="(list, index) in contractList" :key="list.id">
            <div>
              <span style="font-size: 13px">{{list.name}}</span>
              <el-link style="margin-left: 6px;color: #409eff" icon="el-icon-edit" @click="handleEdit(list)">
                编辑
              </el-link>
            </div>
            <div style="margin-top: 5px">
              <el-link v-show="activeName === 'contract' && list.content" style="margin-left: 6px;color: #409eff"
                       icon="el-icon-download" @click="handleDowl(list)">下载模板
              </el-link>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-dialog :title="'修改   (模板名称:' + entityForm.name + ')'" :visible.sync="dialogVisible" width="65%"
               :before-close="handleClose">
      <el-upload v-if="dialogVisible" ref='upload' action="http://1.116.61.138:9010/lims/a/attachment/upload" list-type="file"
                 :headers="headers" :limit="imgLimit" :file-list="productImgs" :on-remove="handleRemove"
                 :on-success="handleAvatarSuccess" :on-exceed="handleExceed" :on-change="handleEditChange"
                 :class="{hide:hideUploadEdit}">
        <el-button type="primary">上传文件</el-button>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getPrint, setData, dowloadPrint1, dowloadPrint2 } from '@/api/print'
import { getToken } from '@/utils/auth'

const token = getToken()
const entityForm = {
  attachment: '',
  name: '',
  content: '',
}
export default {
  name: 'printTemplate',
  data() {
    return {
      headers: {
        Authorization: token,
      },
      hideUploadEdit: false,
      imgLimit: 1,
      activeName: 'assay',
      entityForm: entityForm,
      assayList: [],
      entityFormmini: {},
      contractList: [],
      productImgs: [],
      dialogVisible: false,
      tableHeight: '',
      form: {},
      file: {},
      formData: {},
      rules: {
        name: [{ required: true, message: '请输入模板名称' }],
        content: [{ required: true, message: '请输入内容' }],
      },
    }
  },
  watch: {
    tableHeight(val) {
      if (val) {
      }
    },
  },
  mounted() {
    this.doLayout()
    window.onresize = () => {
      this.doLayout()
    }
    this.getAssayList()
  },
  methods: {
    handleEditChange(file, fileList) {
      let vm = this
      vm.hideUploadEdit = fileList.length >= this.imgLimit
    },
    handleRemove(file, fileList) {
      fileList.map((item) => {
        if (this.activeName === 'assay') {
          item.name = '化验报告' + item.uid
        } else {
          item.name = '合同报告' + item.uid
        }
        return item
      })
      this.productImgs = fileList
      this.fileList = fileList
    },
    handleExceed(files, fileList) {
      this.$message.error('上传文件不能超1个!')
    },
    handleAvatarSuccess(res, file) {
      // 图片上传成功
      this.fileList.push(res)
    },
    // 切换Tab
    handleTabClick(tab) {
      this.activeName = tab.name
      if (tab.name === 'assay') {
        this.getAssayList()
      } else {
        this.getContractList()
      }
    },
    async getAssayList() {
      let res = await getPrint({ filter_EQS_code: 'assayApply' })
      if (res) {
        this.assayList = res.data.records
      }
    },
    async getContractList() {
      let res = await getPrint({ filter_EQS_code: 'assayContract' })
      if (res) {
        this.contractList = res.data.records
      }
    },
    doLayout() {
      const clientHeight = document.documentElement.clientHeight
      // console.log(clientHeight)
      this.tableHeight = clientHeight - 100 + 'px'
    },
    async handleAssayDowload() {
      const res = await dowloadPrint1()
      if (res.data) {
        window.location.href = res.data
      } else {
        this.$message({
          type: 'warning',
          message: '还没有原始模板！',
        })
      }
    },
    async handleContractDowload() {
      const res = await dowloadPrint2()
      if (res.data) {
        window.location.href = res.data
      } else {
        this.$message({
          type: 'warning',
          message: '还没有原始模板！',
        })
      }
    },
    handleDowl(row) {
      window.location.href = row.content
    },
    handleEdit(row) {
      this.entityForm = { ...row }

      let obj = {
        name: row.name,
        url: row.content,
      }
      // this.entityForm.content = row.content
      // this.entityForm.attachment = row.content
      this.productImgs.push(obj)
      this.dialogVisible = true
    },
    handleClose() {
      this.productImgs = []
      this.dialogVisible = false
    },
    async handleSave() {
      this.entityForm.content = this.productImgs[0].url
      this.entityForm.templateName = this.productImgs[0].name
      const res = await setData(this.entityForm)
      if (res) {
        this.$message({
          type: 'success',
          message: '保存信息成功',
        })
        this.handleClose()
        this.getContractList()
      }
    },
    // 刷新页面
    getList() {
      this.getContractList()
    },
  },
}
</script>

<style scoped>
.app-container {
  position: relative;
}

.listContent {
  display: flex;
  flex-wrap: wrap;
  justify-items: center;
}

.fixed {
  /*width: 90px;*/
  /*height: 30px;*/
  position: fixed;
  top: 115px;
  right: 30px;
  /*background-color: dodgerblue;*/
  /*color: #FFFFFF;*/
  /*border-radius: 50%;*/
}
</style>
