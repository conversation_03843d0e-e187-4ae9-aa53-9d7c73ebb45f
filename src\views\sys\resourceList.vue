<template>
  <div class="app-container">
    <div style="display: flex">
      <div class="menu-tree">
        <div class="search-bar">
          <el-input placeholder="输入关键字过滤" v-model="filterText"/>
          <el-button type="primary" icon="el-icon-search" @click="loadMenuTree" plain v-loading="loading"></el-button>
          <!-- icon="el-icon-plus"  -->
          <el-button class="filter-item" type="add" @click="addMenu()">增加
          </el-button>
        </div>
        <el-tree v-if="menus.length > 0" class="margin-top-5" style="height: 95%;overflow: auto" :data="menus"
                 :props="defaultProps" :default-expand-all="false" :filter-node-method="filterNode" node-key="id"
                 :expand-on-click-node="true" ref="menuTree" :render-content="renderContent">
        </el-tree>
        <div class="grid-content bg-purple"></div>
      </div>
      <div class="section">
        <el-form :show-message="true" :status-icon="true" :model="menuForm" :rules="rules" ref="menuForm"
                 label-width="60px">
          <el-row :gutter="10">
            <el-col>
              <el-form-item label="上级菜单">
                <menu-select v-model="menuForm.parentCode" :name="menuForm.parentName"></menu-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="名称" prop="name">
                <el-input v-model="menuForm.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="编码" prop="code" :rules="[{required:true, message:'请输入编码'}]">
                <el-input clearable v-model="menuForm.code"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="排序" prop="sort">
                <el-input-number v-model="menuForm.sort" :min="1" :max="9999"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="备注" prop="remarks">
                <el-input v-model="menuForm.remarks"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" class="button__add">
              <el-form-item>
                <el-button type="add" :loading="loading" @click="saveForm('menuForm')">保存</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div style="width:100%;height:400px">
          <s-curd ref="scurd" name="resource" :model="model" :actions="actions" otherHeight="280">
            <el-table-column :width="100" align="center" fixed="right" label="操作" slot="column"
                             v-if="perms['resource:update']||perms ['resource:delete']">
              <template slot-scope="scope">
                <el-button type="text" @click="updateHandle(scope.row)" v-if="perms['resource:update']">
                  修改
                </el-button>
                <el-button type="text" @click="deleteHandle(scope.row)" v-if="perms ['resource:delete']">删除
                </el-button>
              </template>
            </el-table-column>
          </s-curd>
        </div>

      </div>
    </div>
  </div>
</template>
<script>
import Model from '@/model/sys/resourceList'
import {pagination} from '@/const'
import Func from '../../utils/func'
import {deleteMenuById, pageMenu, saveMenu, deleteById, saveResource} from '@/api/common'
import {mapGetters} from 'vuex'
import MenuSelect from '@/components/MenuSelect/index'

const form = {
  id: '',
  menuId: '',
  code: '', // 编码
  name: '', // 姓名
  urlPattern: '', // 资源路径
  method: '', // 方法
  createBy: '',
  remarks: '',
  sort: '' // 排序
}
export default {
  name: 'resource',
  components: {MenuSelect},
  data() {
    return {
      model: Model,
      tenantCode: '',
      appCode: '',
      resourceFormLoading: false,
      type: 'resource',
      listQuery: {
        current: 1,
        filter_EQS_menuId: '',
        size: pagination.pageSize,
        orderDir: 'desc',
        importance: undefined
      },
      menuCode: '',
      activeName: 'resource',
      loading: false,
      filterText: '',
      menus: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      menuForm: {
        id: null,
        parentCode: 'root',
        parentName: '顶级菜单',
        name: '',
        sort: 100,
        remarks: ''
      },
      resourceForm: Object.assign({}, form),
      rules: {
        name: [{required: true, message: '请输入菜单名称', trigger: 'blur'}],
        type: [{type: 'number', required: true, message: '请选择类型', trigger: 'change'}]
      },
      dialogFormVisible: false,
      dialogStatus: '创建',
      textMap: {
        update: '编辑',
        create: '创建'
      },
      listTableConfig: {
        columns: [
          {
            slot: 'operation',
            isShow: true
          },
          {
            slot: 'code',
            label: '编码',
            isShow: true,
            isFilter: false,
            filter: {
              prop: 'filter_LIKES_code',
              label: '编码'
            }
          },
          {
            prop: 'name',
            label: '名称',
            isShow: true,
            isFilter: false,
            filter: {
              prop: 'filter_LIKES_name',
              label: '名称'
            },
            width: 120
          },
          {
            prop: 'sort',
            label: '排序',
            isShow: true,
            width: 60
          },
          {
            prop: 'remarks',
            label: '备注',
            isShow: true,
            minWidth: 140,
            showOverflowTooltip: true
          }
        ],
        listQuery: {
          menuCode: '',
          filter_LIKES_name: '',
          filter_EQS_menuCode: '',
          orderBy: 'createDate',
          orderDir: 'desc',
          importance: undefined
        }
      },
      actions: [
        {
          config: {
            type: 'add'
            // icon: 'el-icon-plus',
          },
          label: '新增',
          show: 'resource:save',
          click: this.handleCreate
        }
      ]
    }
  },
  watch: {
    filterText(val) {
      this.$refs.menuTree.filter(val)
    },
    menuForm: function () {
      if (this.menuForm.type === 2) {
        this.menuForm.isShow = 0
      }
    }
  },
  computed: {
    ...mapGetters(['perms'])
  },
  created() {
    this.loadMenuTree()
    Model.getMenuCode(this.menuCode)
  },
  methods: {
    deleteHandle(row) {
      this.$refs.scurd.$emit('delRow', row)
    },
    getList() {
      this.$refs['scurd'].$emit('refresh')
    },
    // 修改
    updateHandle(row) {
      this.$refs.scurd.$emit('editRow', row)
    },
    async saveForm(formName) {
      const valid = await this.$refs[formName].validate()
      if (valid) {
        this.loading = true
        delete this.menuForm.ext
        const res = await Func.fetch(saveMenu, {
          ...this.menuForm
        })
        this.loading = false
        if (res) {
          this.$message({
            showClose: true,
            message: '提交成功',
            type: 'success'
          })
          this.loadMenuTree()
        }
      }
    },
    handleCreate() {
      if (!this.menuForm.id) {
        this.$message({
          showClose: true,
          message: '请选择一个菜单',
          type: 'error'
        })
      } else {
        this.resetEntityForm()
        this.listQuery.menuCode = this.menuForm.code
        this.$refs.scurd.$emit('editRow')
      }
    },
    async saveEntityForm(formName) {
      const valid = await this.$refs[formName].validate()
      if (valid) {
        this.resourceFormLoading = true
        const res = await Func.fetch(saveResource, {
          ...this.resourceForm
        })
        this.resourceFormLoading = false
        if (res) {
          this.$message({
            showClose: true,
            message: '操作成功',
            type: 'success'
          })
          this.dialogFormVisible = false
          this.getList()
        }
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    renderContent(h, {node, data}) {
      const menu = data.attributes.menu || data.attributes.resource
      // const child = data.children
      return h(
        'span',
        {
          style: {
            flex: 1,
            display: 'flex',
            'align-items': 'center',
            'justify-content': 'space-between',
            'margin-right': '20px'
          }
        },
        [
          h(
            'span',
            {
              class: {
                'el-tree-node__label': true
              }
            },
            data.label
          ),
          h('span', [
            h(
              'i',
              {
                class: {
                  'el-icon-search': true
                },
                style: {
                  padding: '2px 4px'
                },
                on: {
                  click: (event) => {
                    event.stopPropagation()
                    this.editMenu(node.parent, menu)
                  }
                }
              },
              ''
            ),
            h(
              'i',
              {
                class: {
                  'el-icon-plus': true
                },
                style: {
                  padding: '2px 4px'
                },
                on: {
                  click: (event) => {
                    event.stopPropagation()
                    this.addSubMenu(menu)
                  }
                }
              },
              ''
            ),
            h(
              'i',
              {
                class: {
                  'el-icon-delete': true
                },
                style: {
                  padding: '2px 4px'
                },
                on: {
                  click: (event) => {
                    event.stopPropagation()
                    this.deleteForm(menu.id)
                  }
                }
              },
              ''
            )
          ])
        ]
      )
    },
    async loadMenuTree() {
      this.loading = true
      const res = await Func.fetch(pageMenu, {...this.listQuery})
      this.loading = false
      if (res) {
        this.menus = res.data || []
      }
    },
    resetEntityForm() {
      this.resourceForm = Object.assign({}, form)
    },
    handleDelete(row) {
      Func.deleteEntityById({
        context: this,
        impl: deleteById,
        id: row.id
      })
    },
    async editMenu(parent, menu) {
      this.menuCode = menu.code
      Model.getMenuCode(this.menuCode)
      this.listQuery.filter_EQS_menuCode = menu.code
      this.listQuery.orderBy = 'sort'
      this.listQuery.orderDir = 'asc'
      if (menu.parentCode === 'root') {
        menu.parentName = '顶级菜单'
        this.menuForm = menu
      } else {
        menu.parentName = parent.data.attributes.menu.name
        this.menuForm = menu
        // this.resourceForm.menuId = menu.id
      }
      this.getList()
    },
    menuFormInit() {
      this.menuForm = {
        id: null,
        parentCode: 'root',
        parentName: '顶级菜单',
        name: '',
        sort: 100,
        remarks: ''
      }
    },
    addMenu: function () {
      this.menuFormInit()
      this.menuForm.parentCode = 'root'
    },
    async addSubMenu(menu) {
      this.menuFormInit()
      this.menuForm.parentCode = menu.code
      this.menuForm.parentName = menu.name
    },
    async handleEdit(row) {
      this.dialogFormVisible = true
      this.dialogStatus = 'update'
      this.resourceForm = Object.assign({}, row)
    },
    async deleteForm(id) {
      try {
        await this.$confirm('删除后不可恢复, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        this.loading = true
        const res = await Func.fetch(deleteMenuById, id || this.menuForm.id)
        this.loading = false
        if (res) {
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          })
          this.loadMenuTree()
        }
      } catch (e) {
      }
    },
    handleCloseDialog() {
      this.resetEntityForm()
      this.dialogFormVisible = false
    },
    handleTab(tab) {
      this.type = tab.name
    }
  }
}
</script>
<style lang="scss" scoped>
// ::v-deep .el-table__body {
//   height: 550px;
// }
::v-deep .el-table--scrollable-y .el-table__body-wrapper {
  overflow-y: scroll;
}

.section {
  flex: 3;
}

.app-container {
  display: flex;
  //   width: 100%;
  height: 100%;
  overflow: hidden;
}

::v-deep .top-filter {
  display: none;
}

.menu-tree {
  flex: 1;
  width: 300px;
  height: 100%;
  margin-right: 10px;
  padding-top: 5px;
  background: #ffffff;
}

.parentName {
  font-size: 14px;
  color: red;
}

.el-form {
  padding: 15px;
  background: #ffffff;
  margin-bottom: 10px;
}

.el-tree {
  background: transparent;
}

.search-bar {
  display: flex;
  padding: 10px;

  .el-input {
    flex: 1;
  }

  button {
    margin-left: 10px;
  }
}

.btn-view {
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 10px;
  text-align: right;
}

.el-col-offset-3 {
  // margin-left: 11.7%;
}

.button__add {
  display: flex;
  justify-content: center;
  padding-left: 5.5% !important;
}
</style>
<style scoped>
.tebtb >>> .el-button {
  border: none;
}
</style>
