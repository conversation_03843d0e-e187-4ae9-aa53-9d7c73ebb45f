<template>
  <div class="app-container">
    <div class="s-curd">
      <el-tabs class="tabsclass" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="采购合同" name="BUY"></el-tab-pane>
        <el-tab-pane label="采购合同调价" name="CONTRACT_BUY_PRICE"></el-tab-pane>
        <el-tab-pane label="采购合同运费" name="CONTRACT_BUY_CARRIAGE"></el-tab-pane>
        <el-tab-pane label="付款单" name="APPLY_PAY"></el-tab-pane>
        <el-tab-pane label="付款结算" name="SETTLE_PAY"></el-tab-pane>
        <el-tab-pane label="销售合同" name="CONTRACT_SELL"></el-tab-pane>
        <el-tab-pane label="销售合同调价" name="CONTRACT_SELL_PRICE"></el-tab-pane>
        <el-tab-pane label="销售合同运费" name="CONTRACT_SELL_CARRIAGE"></el-tab-pane>
        <el-tab-pane label="收款结算" name="SETTLE_RECV"></el-tab-pane>
        <el-tab-pane label="销售运费结算" name="SETTLE_RECV_FREIGHT"></el-tab-pane>
      </el-tabs>
    </div>
    <div v-if="activeName=='BUY'">
      <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                    @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                    :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" />
      <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">

        <el-table-column label="类型" slot="belongState" prop="belongState" align="center">
          <template slot-scope="scope" v-if="scope.row.belongState">
            <el-tag :belongState="(scope.row.belongState=='DRAFT' ? 'info':(scope.row.belongState == 'REJECT' ? 'danger' : (scope.row.belongState == 'NEW' ? 'warning' : (scope.row.belongState == 'PASS' ? 'success' : ''))))"
                    size="mini">
              {{scope.row.belongState == 'DRAFT' ? '草稿' : (scope.row.belongState == 'REJECT' ? '拒绝' : (scope.row.belongState == 'NEW' ? '待审核' :(scope.row.belongState == 'PASS' ? '已审核' :''))) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" slot="opt" max-width="200" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" v-if="perms[`${curd}:update`] || false" color="#FF9639"
                    @click="handleUpdate(scope.row)">编辑</el-tag>
            <el-tag class="opt-btn" v-if="perms[`${curd}:pushPermission`] || false" color="#FF9639"
                    @click="pushPermission(scope.row)">推送权限</el-tag>
          </template>
        </el-table-column>
      </s-curd>
    </div>

    <div v-if="activeName=='CONTRACT_BUY_CARRIAGE'">
      <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                    @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                    :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" />
      <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">

        <el-table-column label="状态" slot="belongState" prop="belongState" align="center">
          <template slot-scope="scope" v-if="scope.row.belongState">
            <el-tag :belongState="(scope.row.belongState=='DRAFT' ? 'info':(scope.row.belongState == 'REJECT' ? 'danger' : (scope.row.belongState == 'NEW' ? 'warning' : (scope.row.belongState == 'PASS' ? 'success' : ''))))"
                    size="mini">
              {{scope.row.belongState == 'DRAFT' ? '草稿' : (scope.row.belongState == 'REJECT' ? '拒绝' : (scope.row.belongState == 'NEW' ? '待审核' :(scope.row.belongState == 'PASS' ? '已审核' :''))) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" slot="opt" max-width="200" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" v-if="perms[`${curd}:update`] || false" color="#FF9639"
                    @click="handleUpdate(scope.row)">编辑</el-tag>
            <el-tag class="opt-btn" v-if="perms[`${curd}:pushPermission`] || false" color="#FF9639"
                    @click="pushPermission(scope.row)">推送权限</el-tag>
          </template>
        </el-table-column>
      </s-curd>
    </div>

    <div v-if="activeName=='CONTRACT_BUY_PRICE'">
      <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                    @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                    :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" />
      <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">

        <el-table-column label="类型" slot="belongState" prop="belongState" align="center">
          <template slot-scope="scope" v-if="scope.row.belongState">
            <el-tag :belongState="(scope.row.belongState=='DRAFT' ? 'info':(scope.row.belongState == 'REJECT' ? 'danger' : (scope.row.belongState == 'NEW' ? 'warning' : (scope.row.belongState == 'PASS' ? 'success' : ''))))"
                    size="mini">
              {{scope.row.belongState == 'DRAFT' ? '草稿' : (scope.row.belongState == 'REJECT' ? '拒绝' : (scope.row.belongState == 'NEW' ? '待审核' :(scope.row.belongState == 'PASS' ? '已审核' :''))) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" slot="opt" max-width="200" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" v-if="perms[`${curd}:update`] || false" color="#FF9639"
                    @click="handleUpdate(scope.row)">编辑</el-tag>
            <el-tag class="opt-btn" v-if="perms[`${curd}:pushPermission`] || false" color="#FF9639"
                    @click="pushPermission(scope.row)">推送权限</el-tag>
          </template>
        </el-table-column>
      </s-curd>
    </div>

    <div v-if="activeName=='APPLY_PAY'">
      <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                    @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                    :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" />
      <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">

        <el-table-column label="类型" slot="belongState" prop="belongState" align="center">
          <template slot-scope="scope" v-if="scope.row.belongState">
            <el-tag :belongState="(scope.row.belongState=='DRAFT' ? 'info':(scope.row.belongState == 'REJECT' ? 'danger' : (scope.row.belongState == 'NEW' ? 'warning' : (scope.row.belongState == 'PASS' ? 'success' : ''))))"
                    size="mini">
              {{scope.row.belongState == 'DRAFT' ? '草稿' : (scope.row.belongState == 'REJECT' ? '拒绝' : (scope.row.belongState == 'NEW' ? '待审核' :(scope.row.belongState == 'PASS' ? '已审核' :''))) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" slot="opt" max-width="200" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" v-if="perms[`${curd}:update`] || false" color="#FF9639"
                    @click="handleUpdate(scope.row)">编辑</el-tag>
            <el-tag class="opt-btn" v-if="perms[`${curd}:pushPermission`] || false" color="#FF9639"
                    @click="pushPermission(scope.row)">推送权限</el-tag>
          </template>
        </el-table-column>
      </s-curd>
    </div>

    <div v-if="activeName=='SETTLE_PAY'">
      <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                    @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                    :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" />
      <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">

        <el-table-column label="类型" slot="belongState" prop="belongState" align="center">
          <template slot-scope="scope" v-if="scope.row.belongState">
            <el-tag :belongState="(scope.row.belongState=='DRAFT' ? 'info':(scope.row.belongState == 'REJECT' ? 'danger' : (scope.row.belongState == 'NEW' ? 'warning' : (scope.row.belongState == 'PASS' ? 'success' : ''))))"
                    size="mini">
              {{scope.row.belongState == 'DRAFT' ? '草稿' : (scope.row.belongState == 'REJECT' ? '拒绝' : (scope.row.belongState == 'NEW' ? '待审核' :(scope.row.belongState == 'PASS' ? '已审核' :''))) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" slot="opt" max-width="200" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" v-if="perms[`${curd}:update`] || false" color="#FF9639"
                    @click="handleUpdate(scope.row)">编辑</el-tag>
            <el-tag class="opt-btn" v-if="perms[`${curd}:pushPermission`] || false" color="#FF9639"
                    @click="pushPermission(scope.row)">推送权限</el-tag>
          </template>
        </el-table-column>
      </s-curd>
    </div>

    <div v-if="activeName=='CONTRACT_SELL'">
      <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                    @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                    :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" />
      <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">

        <el-table-column label="类型" slot="belongState" prop="belongState" align="center">
          <template slot-scope="scope" v-if="scope.row.belongState">
            <el-tag :belongState="(scope.row.belongState=='DRAFT' ? 'info':(scope.row.belongState == 'REJECT' ? 'danger' : (scope.row.belongState == 'NEW' ? 'warning' : (scope.row.belongState == 'PASS' ? 'success' : ''))))"
                    size="mini">
              {{scope.row.belongState == 'DRAFT' ? '草稿' : (scope.row.belongState == 'REJECT' ? '拒绝' : (scope.row.belongState == 'NEW' ? '待审核' :(scope.row.belongState == 'PASS' ? '已审核' :''))) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" slot="opt" max-width="200" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" v-if="perms[`${curd}:update`] || false" color="#FF9639"
                    @click="handleUpdate(scope.row)">编辑</el-tag>
            <el-tag class="opt-btn" v-if="perms[`${curd}:pushPermission`] || false" color="#FF9639"
                    @click="pushPermission(scope.row)">推送权限</el-tag>
          </template>
        </el-table-column>
      </s-curd>
    </div>

    <div v-if="activeName=='CONTRACT_SELL_PRICE'">
      <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                    @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                    :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" />
      <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">

        <el-table-column label="类型" slot="belongState" prop="belongState" align="center">
          <template slot-scope="scope" v-if="scope.row.belongState">
            <el-tag :belongState="(scope.row.belongState=='DRAFT' ? 'info':(scope.row.belongState == 'REJECT' ? 'danger' : (scope.row.belongState == 'NEW' ? 'warning' : (scope.row.belongState == 'PASS' ? 'success' : ''))))"
                    size="mini">
              {{scope.row.belongState == 'DRAFT' ? '草稿' : (scope.row.belongState == 'REJECT' ? '拒绝' : (scope.row.belongState == 'NEW' ? '待审核' :(scope.row.belongState == 'PASS' ? '已审核' :''))) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" slot="opt" max-width="200" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" v-if="perms[`${curd}:update`] || false" color="#FF9639"
                    @click="handleUpdate(scope.row)">编辑</el-tag>
            <el-tag class="opt-btn" v-if="perms[`${curd}:pushPermission`] || false" color="#FF9639"
                    @click="pushPermission(scope.row)">推送权限</el-tag>
          </template>
        </el-table-column>
      </s-curd>
    </div>

    <div v-if="activeName=='CONTRACT_SELL_CARRIAGE'">
      <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                    @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                    :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" />
      <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">

        <el-table-column label="类型" slot="belongState" prop="belongState" align="center">
          <template slot-scope="scope" v-if="scope.row.belongState">
            <el-tag :belongState="(scope.row.belongState=='DRAFT' ? 'info':(scope.row.belongState == 'REJECT' ? 'danger' : (scope.row.belongState == 'NEW' ? 'warning' : (scope.row.belongState == 'PASS' ? 'success' : ''))))"
                    size="mini">
              {{scope.row.belongState == 'DRAFT' ? '草稿' : (scope.row.belongState == 'REJECT' ? '拒绝' : (scope.row.belongState == 'NEW' ? '待审核' :(scope.row.belongState == 'PASS' ? '已审核' :''))) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" slot="opt" max-width="200" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" v-if="perms[`${curd}:update`] || false" color="#FF9639"
                    @click="handleUpdate(scope.row)">编辑</el-tag>
            <el-tag class="opt-btn" v-if="perms[`${curd}:pushPermission`] || false" color="#FF9639"
                    @click="pushPermission(scope.row)">推送权限</el-tag>
          </template>
        </el-table-column>
      </s-curd>
    </div>

    <div v-if="activeName=='SETTLE_RECV'">
      <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                    @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                    :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" />
      <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">

        <el-table-column label="类型" slot="belongState" prop="belongState" align="center">
          <template slot-scope="scope" v-if="scope.row.belongState">
            <el-tag :belongState="(scope.row.belongState=='DRAFT' ? 'info':(scope.row.belongState == 'REJECT' ? 'danger' : (scope.row.belongState == 'NEW' ? 'warning' : (scope.row.belongState == 'PASS' ? 'success' : ''))))"
                    size="mini">
              {{scope.row.belongState == 'DRAFT' ? '草稿' : (scope.row.belongState == 'REJECT' ? '拒绝' : (scope.row.belongState == 'NEW' ? '待审核' :(scope.row.belongState == 'PASS' ? '已审核' :''))) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" slot="opt" max-width="200" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" v-if="perms[`${curd}:update`] || false" color="#FF9639"
                    @click="handleUpdate(scope.row)">编辑</el-tag>
            <el-tag class="opt-btn" v-if="perms[`${curd}:pushPermission`] || false" color="#FF9639"
                    @click="pushPermission(scope.row)">推送权限</el-tag>
          </template>
        </el-table-column>
      </s-curd>
    </div>

    <div v-if="activeName=='SETTLE_RECV_FREIGHT'">
      <filter-table :options="filterOption" @filter="handleFilter" @reset="handleFilterReset" @add="handleCreate"
                    @import="handleImpiort" :showImport="perms[`${curd}:addimport`]||false"
                    :showAdd="perms[`${curd}:save`] || false" :showRefresh="perms[`${curd}:Refresh`]||false" />
      <s-curd :ref="curd" :name="curd" :model="model" :actions="actions" :list-query="listQuery" title="品名列表" otherHeight="120">

        <el-table-column label="类型" slot="belongState" prop="belongState" align="center">
          <template slot-scope="scope" v-if="scope.row.belongState">
            <el-tag :belongState="(scope.row.belongState=='DRAFT' ? 'info':(scope.row.belongState == 'REJECT' ? 'danger' : (scope.row.belongState == 'NEW' ? 'warning' : (scope.row.belongState == 'PASS' ? 'success' : ''))))"
                    size="mini">
              {{scope.row.belongState == 'DRAFT' ? '草稿' : (scope.row.belongState == 'REJECT' ? '拒绝' : (scope.row.belongState == 'NEW' ? '待审核' :(scope.row.belongState == 'PASS' ? '已审核' :''))) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" slot="opt" max-width="200" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <el-tag class="opt-btn" v-if="perms[`${curd}:update`] || false" color="#FF9639"
                    @click="handleUpdate(scope.row)">编辑</el-tag>
            <el-tag class="opt-btn" v-if="perms[`${curd}:pushPermission`] || false" color="#FF9639"
                    @click="pushPermission(scope.row)">推送权限</el-tag>
          </template>
        </el-table-column>
      </s-curd>
    </div>

    <el-dialog top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px" :rules="rules"
               label-position="top" v-if="dialogFormVisible">
        <el-row type="flex" :gutter="50">
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="编号" prop="code">
                    <el-input v-model="entityForm.code" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="名称" prop="name">
                    <el-input v-model="entityForm.name" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="别名" prop="aliasName">
                    <el-input v-model="entityForm.aliasName" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="排序" prop="sort">
                    <el-input v-model="entityForm.sort" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="所属状态" prop="belongState" :rules="[{ required: true, message: '请选择一项' }]">
                    <dict-select v-model="entityForm.belongState" type="b_flow_design_belong_state" />
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="权限编码" prop="resourceCode">
                    <el-input v-model="entityForm.resourceCode" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col>
                  <!-- v-if="entityForm.belongState=='NEW'" -->
                  <el-form-item label="上一流程" prop="previousFlowName">
                    <el-select v-model="previousFlowEntity.active" placeholder="请选择" clearable @change="handlepreviousFlow"
                               style="width:100%">
                      <el-option v-for="item in previousFlowEntity.options" :key="item.value" :label="item.label" :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <!-- v-if="entityForm.belongState=='DRAFT' || entityForm.belongState=='REJECT' || entityForm.belongState=='NEW' " -->
                  <el-form-item label="下一流程" prop="nextFlowName">
                    <el-select v-model="nextFlowEntity.active" placeholder="请选择" clearable @change="handlenextFlow"
                               style="width:100%">
                      <el-option v-for="item in nextFlowEntity.options" :key="item.value" :label="item.label" :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="entityForm.remarks" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <div style="margin:10px 0">
                    <span style="margin-right:10px">是否发送消息</span>
                    <el-radio-group v-model="entityForm.isSendMessage" size="small" @change="changeTheme">
                      <el-radio :label="'Y'">是</el-radio>
                      <el-radio :label="'N'">否</el-radio>
                    </el-radio-group>
                  </div>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="消息模板:" prop="messageTemplate">
                    <el-input type="textarea" v-model="entityForm.messageTemplate" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <div style="margin:10px 0">
                    <span style="margin-right:10px">是否发送短信</span>
                    <el-radio-group v-model="entityForm.isSendSms" size="small" @change="changeThemeV">
                      <el-radio :label="'Y'">是</el-radio>
                      <el-radio :label="'N'">否</el-radio>
                    </el-radio-group>
                  </div>
                </el-col>
              </el-row>
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="短信模板:" prop="smsTemplate">
                    <el-input type="textarea" v-model="entityForm.smsTemplate" autocomplete="off" clearable />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading" @click="saveEntityForm('entityForm')">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog top="5vh" :title="`${textMap[dialogStatus]}(${title})`" :visible.sync="dialogFormVisibleV"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :show-message="true" :status-icon="true" ref="entityFormV" :model="entityForm" label-width="90px"
               label-position="top" v-if="dialogFormVisibleV">
        <el-row type="flex" :gutter="50">
          <el-col>
            <div class="form-title">基本信息</div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-form-item label="名称" prop="name">
                    <el-input v-model="entityFormV.name" autocomplete="off" clearable disabled />
                  </el-form-item>
                </el-col>
                <el-col></el-col>
                <el-col></el-col>
                <el-col></el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col style="margin-bottom: 20px;">
            <div class="form-titlV form-warp">
              <span style="position: relative;top:12px">权限列表</span>
              <el-button type="export-all" @click="handleAdd">新增</el-button>
            </div>
            <div class="form-layout">
              <el-row type="flex" :gutter="50">
                <el-col>
                  <el-table :data="listV" stripe :header-cell-style="headClass">
                    <el-table-column label="类型" align="center">
                      <template slot-scope="scope">
                        <dict-select v-model="scope.row.type" type="b_flow_design_permission_type"
                                     @change="selectType($event,scope.$index)" />
                      </template>
                    </el-table-column>
                    <el-table-column label="权限列表" align="center">
                      <template slot-scope="scope">
                        <template v-if="scope.row.type=='ROLE'">
                          <el-select v-model="scope.row.permissionsnew" multiple filterable allow-create default-first-option
                                     size="medium" placeholder="请选择权限列表" @change="selectPermissions($event,scope.$index)"
                                     style="width:100%">
                            <el-option v-for="item in permissionsList" :key="item.id" :label="item.name" :value="item.id">
                            </el-option>
                          </el-select>
                        </template>
                        <template v-else>
                          <el-select v-model="scope.row.permissionsnew" multiple filterable allow-create default-first-option
                                     size="medium" placeholder="请选择权限列表" @change="selectPermissions($event,scope.$index)"
                                     style="width:100%">
                            <el-option v-for="item in userpermissionsList" :key="item.id" :label="item.name" :value="item.id">
                            </el-option>
                          </el-select>
                        </template>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template slot-scope="scope">
                        <el-button type="primary" @click="handleRemove(scope.row)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="dialog-footer-btns" :loading="entityFormLoading"
                   @click="saveEntityFormV('entityFormV')">保存
        </el-button>
        <el-button @click="handleClose" class="dialog-footer-btns">取消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Mixins from '@/utils/mixins'
import Model from '@/model/sys/reviewprocess'
import { createMemoryField } from '@/utils/index'
import { listQuery, formindexOptionbelongState } from '@/const'
const form = {
  type: '',
  permissions: '',
  permissionsnew: ''
  // permissionsList: [],
}
export default {
  name: 'reviewprocess',
  mixins: [Mixins],
  data() {
    return {
      curd: 'reviewprocess',
      model: Model,
      activeName: 'BUY',
      // filterOption: {
      //   showMore: true,
      //   columns: [
      //     {
      //       prop: 'belongState',
      //       filter: 'filter_EQS_belongState',
      //       component: 'dict-select',
      //       props: {
      //         type: 'b_flow_design_belong_state',
      //         placeholder: '请选择类型',
      //         clearable: true,
      //       },
      //     },
      //   ],
      // },
      filterOption: { ...formindexOptionbelongState },
      entityForm: { ...Model.model },
      entityFormV: { ...Model.model },
      memoryEntity: { fields: {}, triggered: false },
      rules: {
        code: { required: true, message: '请输入', trigger: 'blur' },
        name: { required: true, message: '请输入', trigger: 'blur' },
        sort: { required: true, message: '请输入', trigger: 'blur' }
      },
      actions: [],
      dialoguploadVisible: false,
      previousFlowEntity: { options: [], active: [] },
      nextFlowEntity: { options: [], active: [] },
      listQuery: { ...listQuery },

      dialogFormVisibleV: false,
      listV: [{ ...form }],
      permissionsList: { value: '', options: [] },
      userpermissionsList: { value: '', options: [] }
    }
  },
  created() {
    this.memoryEntity.fields = createMemoryField({ fields: ['name'], target: this.entityForm })
    this.getlclistfn()
  },
  watch: {
    'previousFlowEntity.active'(value) {
      // console.log(value)
    }
  },
  computed: {},
  methods: {
    async selectType(e, index) {
      if (e == 'ROLE') {
        //角色
        this.getroleListFN()
        this.listV[index].permissionsnew = []
      } else if (e == 'USER') {
        //用户
        this.listV[index].permissionsnew = []
        this.getuserListFN()
      }
    },
    async getroleListFN() {
      const { data } = await this.model.getroleList()
      this.permissionsList = data
    },
    async getuserListFN() {
      const { data } = await this.model.getuserList()
      this.userpermissionsList = data
    },
    selectPermissions(e, index) {
      if (this.listV[index].type == 'ROLE') {
        //角色
        this.getroleListFN()
      } else if (this.listV[index].type == 'USER') {
        //用户
        this.getuserListFN()
      }
    },
    handleAdd() {
      const form = { ...this.form }
      form.ext = Math.random().toFixed(6).slice(-6)
      this.listV.push(form)
    },
    handleRemove({ ext }) {
      const index = this.listV.findIndex((item) => item.ext === ext)
      if (index === -1) return false
      this.listV.splice(index, 1)
    },
    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },

    handleClose() {
      this.resetVariant()
    },
    changeTheme(val) {
      this.entityForm = { ...this.entityForm, isSendMessage: val }
    },
    changeThemeV(val) {
      this.entityForm = { ...this.entityForm, isSendSms: val }
    },
    async handleUpdate(item) {
      const { data } = await this.model.getUploadList(item.id)
      this.entityForm = { ...data }
      // this.entityForm = { ...item }
      this.dialogStatus = 'update'
      this.nextFlowEntity.active = this.entityForm.nextFlowName
      this.previousFlowEntity.active = this.entityForm.previousFlowName
      this.dialogFormVisible = true
    },
    async pushPermission(item) {
      this.permissionsList = []
      this.userpermissionsList = []
      const { data } = await this.model.getDetailList(item.id)
      this.entityFormV = { ...data }
      var array = data.flowDesignPermissionList
      array.forEach((element) => {
        element.permissionsnew = element.permissions.split(',')
      })
      let that = this
      if (array.length > 0) {
        for (var i = 0; i < array.length; i++) {
          if (array[i].type == 'ROLE') {
            //角色
            that.getroleListFN()
          } else if (array[i].type == 'USER') {
            //用户
            that.getuserListFN()
          }
        }
      }

      this.listV = array

      this.dialogStatus = 'pushPermission'
      this.dialogFormVisibleV = true
    },
    async getlclistfn() {
      try {
        let options = []
        const { data } = await Model.getlclist({ type: this.activeName })
        if (data.length) {
          data.forEach((item) => {
            const { name: label, id: value } = item
            options.push({ label, value })
          })
        }
        this.previousFlowEntity.options = options
        this.nextFlowEntity.options = options
      } catch (e) {}
    },

    handlepreviousFlow({ value, label }) {
      this.entityForm = { ...this.entityForm, previousFlowId: value, previousFlowName: label }
    },
    handlenextFlow({ value, label }) {
      this.entityForm = { ...this.entityForm, nextFlowId: value, nextFlowName: label }
    },
    handleClick(tab, event) {
      if (this.activeName == 'BUY') {
        this.listQuery = { ...this.listQuery, filter_EQS_type: this.activeName }
      } else if (this.activeName == 'CONTRACT_BUY_CARRIAGE') {
        this.listQuery = { ...this.listQuery, filter_EQS_type: this.activeName }
      } else if (this.activeName == 'CONTRACT_BUY_PRICE') {
        this.listQuery = { ...this.listQuery, filter_EQS_type: this.activeName }
      } else if (this.activeName == 'APPLY_PAY') {
        this.listQuery = { ...this.listQuery, filter_EQS_type: this.activeName }
      } else if (this.activeName == 'SETTLE_PAY') {
        this.listQuery = { ...this.listQuery, filter_EQS_type: this.activeName }
      } else if (this.activeName == 'CONTRACT_SELL') {
        this.listQuery = { ...this.listQuery, filter_EQS_type: this.activeName }
      } else if (this.activeName == 'CONTRACT_SELL_PRICE') {
        this.listQuery = { ...this.listQuery, filter_EQS_type: this.activeName }
      } else if (this.activeName == 'CONTRACT_SELL_CARRIAGE') {
        this.listQuery = { ...this.listQuery, filter_EQS_type: this.activeName }
      } else if (this.activeName == 'SETTLE_RECV') {
        this.listQuery = { ...this.listQuery, filter_EQS_type: this.activeName }
      } else if (this.activeName == 'SETTLE_RECV_FREIGHT') {
        this.listQuery = { ...this.listQuery, filter_EQS_type: this.activeName }
      }

      this.getlclistfn()
      this.filterOption = { ...formindexOptionbelongState }
      this.getList()
    },
    async getList() {
      this.$refs[this.curd].searchChange()
    },

    resetVariant() {
      this.entityFormLoading = false
      this.dialogFormVisible = false
      this.dialogFormVisibleV = false
      this.listV = []
      this.entityForm = { ...this.model.model }
    },

    async saveEntityForm(entityForm) {
      this.entityForm.type = this.activeName
      let valid
      try {
        valid = await this.$refs[entityForm].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        this.entityFormLoading = true
        const res = await this.model.save({ ...this.entityForm })
        this.resetVariant()
        this.getList()
        this.dialogFormVisible = false
        if (res) {
          this.$message({ showClose: true, message: '操作成功', type: 'success' })
        }
      }
    },

    async saveEntityFormV(entityFormV) {
      let valid
      try {
        valid = await this.$refs[entityFormV].validate()
      } catch (e) {
        this.$message({ message: '参数有误,请检查!!!', type: 'warning' })
      }
      if (valid) {
        const list = []
        if (this.listV.length) {
          this.listV.forEach((item) => {
            let obj = {}
            obj.type = item.type
            obj.permissions = item.permissions
            obj.permissions = item.permissionsnew.toString()
            if (item.id !== undefined) {
              obj.ext = item.id
              obj.id = item.id
            } else {
              obj.ext = item.ext
            }
            list.push({ ...obj })
          })
          if (!this.checkParams(list)) return false
          this.entityFormLoading = true
          //新增。草稿箱编辑
          const res = await Model.savePermissions({ ...this.entityFormV, flowDesignPermissionList: list })
          this.entityFormLoading = false
          if (res) {
            this.resetVariant()
            this.getList()
            this.$message({ showClose: true, message: '操作成功', type: 'success' })
          }
        } else {
          this.$message({ message: '权限数据不能为空', type: 'warning' })
        }
      }
    },
    checkParams(list, rule = ['type']) {
      for (const item of list) {
        for (const [key, val] of Object.entries(item)) {
          // if (rule.includes(key)) continue
          if (val === '' || val === undefined || val === null) {
            this.$message({ message: `请检查参数${key}`, type: 'warning' })
            return false
          }
        }
      }
      return true
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';
.form-warp {
  padding: 10px 20px 6px 18px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.form-titlV {
  font-size: 16px;
  position: relative;
  padding: 7px 18px;

  &::before {
    content: '';
    left: 7px;
    position: absolute;
    width: 3px;
    // top: 10px;
    bottom: 11px;
    background: #2f79e8;
    height: 16px;
  }
}
.uploadAttachment {
  text-align: left !important;
}
.tabsclass {
  ::v-deep .el-form-item__label {
    width: 120px !important;
  }
  ::v-deep .el-form-item__content {
    margin-left: 120px !important;
  }
  ::v-deep .el-col-offset-1 {
    margin-left: 2%;
  }
  ::v-deep .el-card {
    margin-bottom: 20px;
  }
}
// ::v-deep .el-dialog__body {
//   height: 20vh;
// }

.app-container {
  background-color: #fff;
  .tabsclass {
    padding: 0 10px 20px 10px;
    .fromfooter {
      padding-left: 7.2%;
    }
  }
  .linblack {
    width: 100%;
    display: flex;
    // align-items: center;
    padding-top: 10px;
  }
}
</style>
<style>
.time {
  font-size: 13px;
  color: #999;
}

.bottom {
  margin-top: 13px;
  line-height: 12px;
}

.button {
  padding: 0;
  float: right;
}

.image {
  width: 100%;
  display: block;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: '';
}

.clearfix:after {
  clear: both;
}
</style>