<template>
  <div class="app-container">
    <s-curd ref="roleList" name="roleList" :model="model" :actions="actions" :list-query="listQuery" title="角色管理"
            otherHeight="140">
      <el-table-column label="角色名称" slot="name" prop="name" width="180px">
        <template slot-scope="scope">
          <el-button type="text" @click="updateHandle(scope.row)">{{ scope.row.name }}</el-button>
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog ref="dialogStatus" top="5vh" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">

      <el-tabs type="border-card" v-model="paneType" @tab-click="switchPane">
        <el-tab-pane name="basic">
          <span slot="label"><i class="el-icon-date"></i> 基本信息</span>
          <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="90px"
                   v-if="dialogFormVisible">
            <el-row>
              <el-col :span="24">
                <el-form-item label="角色名称" prop="name" label-width="140px" :rules="[{required:true, message:'请输入角色名称'}]">
                  <el-input v-model="entityForm.name" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注信息" prop="remarks" label-width="140px">
                  <el-input v-model="entityForm.remarks" type="textarea" clearable></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="资源列表">
              <el-row>
                <el-col class="totalHandlerBtn">
                  <el-button type="text" class="margin-r-35" @click="totalHandler">全部选中
                  </el-button>
                  <el-button type="text" class="margin-r-35" @click="cancelHandler">全部取消
                  </el-button>
                </el-col>
              </el-row>
              <el-checkbox-group v-model="entityForm.resourceCodes">
                <div class="resources-list">
                  <!--特性区-->
                  <div v-for="(c,i) in menus" :key="i">
                    <!--第一级-->
                    <div class="name">
                      <span class="label root">{{ c.label }}</span>
                      <div class="resource">
                        <template v-for="r in c.attributes.menu.resources">
                          <el-checkbox :label="r.code">{{ r.name }}</el-checkbox>
                          <!-- <span>全选</span></span> -->
                        </template>
                      </div>
                    </div>
                    <!--第二级-->
                    <div v-if="c.children">
                      <div class="c" style="margin-bottom:30px" v-for="(c1,index) in c.children" :key="c1.label">
                        <div class="name">
                          <span class="label name_label">
                            <!--                                                        ├{{ c1.label }}-->
                            <el-dropdown @command="handleCommand($event, c1)">
                              <span class="el-dropdown-link" style="color:#6f75df ">├ {{ c1.label }}<i
                                   class="el-icon-arrow-down el-icon--right"></i>
                              </span>
                              <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="select">全部选中</el-dropdown-item>
                                <el-dropdown-item command="cancel">全部取消</el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>

                          </span>
                          <div class="resource">
                            <template v-for="(r1) in c1.attributes.menu.resources">
                              <el-checkbox :label="r1.code">{{ r1.name }}</el-checkbox>
                              <!-- <el-checkbox :label="r1.code" @change="handleBatchSelected($event,c1)">{{r1.name}}</el-checkbox> -->
                              <!-- <el-button type="text" icon="el-icon-smoking" @click="handleBatchSelected(c1)">选中当前 -->
                              <!-- </el-button> -->
                            </template>
                          </div>
                        </div>
                        <!--第三级-->
                        <div v-if="c1.children">
                          <div class="c" v-for="c2 in c1.children" :key="c2.label">
                            <div class="name" style="margin-left: 17px;">
                              <span class="label">
                                <el-dropdown @command="handleCommand($event, c2)">
                                  <span class="el-dropdown-link" style="color:#6f75df ">├ {{ c2.label }}<i
                                       class="el-icon-arrow-down el-icon--right"></i>
                                  </span>
                                  <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="select">全部选中</el-dropdown-item>
                                    <el-dropdown-item command="cancel">全部取消</el-dropdown-item>
                                  </el-dropdown-menu>
                                </el-dropdown>

                              </span>
                              <div class="resource">
                                <template v-for="r2 in c2.attributes.menu.resources">
                                  <el-checkbox :label="r2.code">{{ r2.name }}
                                  </el-checkbox>
                                </template>
                              </div>
                            </div>
                            <!--第四级-->
                            <div v-if="c2.children">
                              <div class="c" v-for="c3 in c2.children" :key="c3.label">
                                <div class="name" style="margin-left: 40px;">
                                  <span class="label">

                                    <el-dropdown @command="handleCommand($event, c3)">
                                      <span class="el-dropdown-link" style="color:#6f75df ">├ {{ c3.label }}<i
                                           class="el-icon-arrow-down el-icon--right"></i>
                                      </span>
                                      <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item command="select">全部选中</el-dropdown-item>
                                        <el-dropdown-item command="cancel">全部取消</el-dropdown-item>
                                      </el-dropdown-menu>
                                    </el-dropdown>

                                  </span>
                                  <div class="resource">
                                    <template v-for="r3 in c3.attributes.menu.resources">
                                      <el-checkbox :label="r3.code">{{ r3.name }}
                                      </el-checkbox>
                                    </template>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane name="setUser" v-if="userId && checkPermission(['roleList:user'])">
          <span slot="label"><i class="el-icon-date"></i> 设置用户</span>
          <div class="form-edit-content">
            <div class="left-flex">
              <h3 class="content-title">全部用户列表</h3>
              <div class="content">
                <div class="filter-container">
                  <el-input @keyup.enter.native="getNotRoleUserList" class="filter-item" placeholder="用户名称" style="width: 200px;"
                            v-model="userListQuery.userName">
                    <template slot="append">
                    </template>
                  </el-input>

                  <Treeselect style="width: 200px" v-model="userListQuery.deptId" :options='menuOptions' :normalizer='normalizer'
                              :show-count='true' placeholder='选择上级菜单' />

                  <el-button type="primary" icon="el-icon-search" @click="getNotRoleUserList">查询
                  </el-button>
                </div>
                <el-table :data="userList" height="360px" v-loading="userListLoading" element-loading-text="加载中..." stripe border
                          highlight-current-row @selection-change="handleAddSelectionChange">
                  <el-table-column type="selection" width="40">
                  </el-table-column>
                  <el-table-column label="用户名称" prop="name" width="120"></el-table-column>
                  <el-table-column label="登录名" prop="loginName" width="120"></el-table-column>
                  <el-table-column label="手机号码" prop="mobile"></el-table-column>
                </el-table>
                <div v-show="!userListLoading" class="pagination-container">
                  <el-pagination @size-change="handleNotMyUserSizeChange" @current-change="handleNotMyUserCurrentChange"
                                 :current-page.sync="userListQuery.page" :layout="pagination.layout"
                                 :page-sizes="pagination.pageSizes" :page-size="userListQuery.size" :total="totalUser">
                  </el-pagination>
                </div>
              </div>
            </div>
            <div class="mid-tools">
              <el-button type="primary" icon="el-icon-arrow-right" :disabled="setUserLoading || addUserList.length === 0 "
                         @click="saveUserList('add')"></el-button>
              <br>
              <el-button type="danger" icon="el-icon-arrow-left" :disabled="setUserLoading || delUserList.length === 0 "
                         @click="saveUserList('del')"></el-button>
            </div>

            <div class="right-form">
              <h3 class="content-title">当前用户列表</h3>
              <div class="content">
                <div class="filter-container">
                  <el-input @keyup.enter.native="getRoleUserList" style="width: 200px;" class="filter-item" placeholder="用户名称"
                            v-model="myUserListQuery.userName">
                    <template slot="append">

                    </template>
                  </el-input>

                  <Treeselect style="width: 200px" v-model="myUserListQuery.deptId" :options='menuOptions'
                              :normalizer='normalizer' :show-count='true' placeholder='选择上级菜单' />

                  <el-button type="primary" icon="el-icon-search" @click="getRoleUserList">查询
                  </el-button>
                </div>
                <el-table :data="myUserList" height="360px" v-loading="setUserLoading" element-loading-text="加载中..." stripe border
                          highlight-current-row @selection-change="handleDelSelectionChange">
                  <el-table-column type="selection" width="40">
                  </el-table-column>
                  <el-table-column label="用户名称" prop="name" width="120"></el-table-column>
                  <el-table-column label="登录名" prop="loginName" width="120"></el-table-column>
                  <el-table-column label="手机号码" prop="mobile"></el-table-column>
                </el-table>
                <div v-show="!setUserLoading" class="pagination-container">
                  <el-pagination @size-change="handleMyUserSizeChange" @current-change="handleMyUserCurrentChange"
                                 :current-page.sync="myUserListQuery.page" :layout="pagination.layout"
                                 :page-sizes="pagination.pageSizes" :page-size="myUserListQuery.size" :total="totalMyUser">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="entityFormLoading" @click="saveEntityForm('entityForm')"
                   v-if="paneType == 'basic'&&perms['roleList:save']">保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { pagination } from '@/const'
import Model from '@/model/sys/roleList'
import { pageMenu, saveRole, getById, addRoleUser, delRoleUser, findByRoleCode, findNotInRoleCode } from '@/api/common'
import Func from '@/utils/func'

const userForm = {
  id: '',
  name: '',
  remarks: '',
  resourceCodes: []
}
const myUserListQuery = {
  current: 1,
  size: pagination.pageSize,
  orderBy: 'createDate',
  orderDir: 'desc',
  roleCode: '',
  userName: ''
}
const userListQuery = {
  current: 1,
  size: pagination.pageSize,
  orderBy: 'createDate',
  orderDir: 'desc',
  roleCode: '',
  userName: ''
}


import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { treeDepts } from "@/api/common/listAllTree";
import checkPermission from "@/utils/permission";

export default {
  name: 'roleList',
  components: { Treeselect },
  data() {
    return {
      menuOptions: [],
      option: Model.tableOption,
      model: Model,
      listQuery: {
        orderBy: 'createDate',
        orderDir: 'desc'
      },
      entityFormLoading: false,
      menus: [],
      userId: '',
      textMap: {
        update: '编辑',
        create: '创建'
      },
      entityForm: { ...userForm },
      dialogStatus: 'create',
      dialogFormVisible: false,
      dialogVisible: true,
      paneType: 'basic',
      actions: [
        {
          config: {
            type: 'primary',
            plain: true,
            icon: 'el-icon-plus'
          },
          click: this.openDialog,
          show: 'roleList:save',
          label: '新增'
        },
        {
          config: {
            type: 'warning',
            plain: true,
            icon: 'el-icon-edit'
          },
          click: this.updateHandle,
          show: 'roleList:update',
          label: '修改'
        }
      ],
      myUserList: [],
      setUserLoading: false,
      myUserpagination: pagination,
      myUserListQuery: {
        ...myUserListQuery
      },
      userListLoading: false,
      userListpagination: pagination,
      userListQuery: {
        ...userListQuery
      },
      totalUser: null,
      addUserList: [],
      delUserList: [],
      userList: [],
      pagination: pagination,
      totalMyUser: null,
      checked: false
    }
    // model: new RoleList()
  },
  computed: {
    ...mapGetters(['perms'])
  },
  created() {
    this.loadMenuTree()
    this.setTreeDepts()
    // console.log(this.entityForm.resourceCodes)
  },
  watch: {
    // entityForm: {
    //   handler(v) {
    //     console.log(v)
    //   },
    //   deep: true
    // }
  },
  methods: {
    checkPermission,
    handleCommand(command, data) {
      function getResourceIds(node) {
        let ids = [...node.attributes.menu.resources.map(v => v.code)];
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => {
            ids = ids.concat(getResourceIds(child));
          });
        }
        return ids;
      }

      const resourceIds = getResourceIds(data)
      switch (command) {
        case 'select':
          this.entityForm.resourceCodes = [...this.entityForm.resourceCodes, ...resourceIds]
          break
        case 'cancel':
          this.entityForm.resourceCodes = this.entityForm.resourceCodes.filter((v) => !resourceIds.includes(v))
          break
      }
    },


    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.code,
        label: node.label,
        children: node.children
      }
    },

    async setTreeDepts() {
      try {
        const { data } = await treeDepts()
        this.menuOptions = data
      } catch (e) {
      }
    },

    // 修改
    async updateHandle(list) {
      let row = {}
      if (Array.isArray(list)) {
        if (list.length !== 1) {
          this.$message({ type: 'warning', message: '请选择且仅选择一条记录操作' })
          return false
        }
        row = list[0]
      } else {
        row = { ...list }
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.dialogVisible = true
      this.paneType = 'basic'
      this.entityForm = { ...row }
      getById(row.id)
        .then((response) => {
          this.userId = response.data.id
          this.myUserListQuery.roleCode = response.data.code
          this.userListQuery.roleCode = response.data.code
          this.entityForm = { ...response.data }
          delete this.entityForm.ext
        })
        .catch((e) => {
          this.$message({ type: 'info', message: '编辑操作异常' })
        })
    },
    getList() {
      this.$refs.roleList.$emit('refresh')
    },
    openDialog() {
      this.dialogStatus = 'create'
      this.paneType = 'basic'
      this.dialogVisible = true
      this.dialogFormVisible = true
    },
    // 关闭弹窗
    handleClose() {
      this.resetVarbiant()
    },
    /**
     * 重置数据
     */
    resetVarbiant() {
      this.entityFormLoading = false
      this.paneType = 'basic'
      this.userId = ''
      this.dialogFormVisible = false
      this.userListQuery = { ...userListQuery }
      this.myUserListQuery = { ...myUserListQuery }
      this.entityForm = { ...userForm }
    },
    async loadMenuTree() {
      let res = await pageMenu()
      this.menus = res.data
      // console.log(this.menus, 1)
    },
    /**
     * 切换pane
     * */
    switchPane(tab) {
      this.paneType = tab.paneName
      if (tab.paneName === 'setUser') {
        // 股东pane
        this.setUserList()
      }
    },
    /*
     * 设置用户列表
     * */
    setUserList() {
      this.getRoleUserList()
      this.getNotRoleUserList()
    },
    /*
     * 获取角色用户列表(右侧的当前用户列表)
     * */
    async getRoleUserList() {
      this.setUserLoading = true
      const res = await Func.fetch(findByRoleCode, this.myUserListQuery)
      this.setUserLoading = false
      if (res) {
        this.myUserList = res.data
        this.totalMyUser = res.data.length || 0
      }
    },
    /*
     * 获取不是角色用户列表(左侧的全部用户列表)
     * */
    async getNotRoleUserList() {
      this.userListLoading = true
      const res = await Func.fetch(findNotInRoleCode, this.userListQuery)
      this.userListLoading = false
      if (res) {
        this.userList = res.data
        this.totalUser = res.data.length || 0
      }
    },
    cancelHandler() {
      this.entityForm.resourceCodes = []
    },
    handleBatchSelected(flag, c1) {
      const newCodes = []
      if (c1.children) {
        c1.children.forEach((c2) => {
          c2.attributes.menu.resources.forEach((c3) => {
            newCodes.push(c3.code)
          })
        })
        if (flag) {
          this.entityForm.resourceCodes = [...new Set([...this.entityForm.resourceCodes, ...newCodes])]
        } else {
          const newResourceCodes = this.entityForm.resourceCodes.filter((val) => !newCodes.includes(val))
          this.entityForm.resourceCodes = newResourceCodes
        }
      }
    },
    totalHandler() {
      const middleCollection = []
      this.menus.forEach((v) => {
        const {
          attributes: {
            menu: { resources: gather = [] }
          }
        } = v
        if (gather.length) {
          gather.forEach((_v) => {
            middleCollection.push(_v.code)
          })
        }
        if (v.children && v.children.length) {
          v.children.forEach((_v) => {
            const {
              attributes: {
                menu: { resources: gather = [] }
              }
            } = _v
            if (gather.length) {
              gather.forEach((__v) => {
                middleCollection.push(__v.code)
              })
            }
            if (_v.children && _v.children.length) {
              _v.children.forEach((__v) => {
                const {
                  attributes: {
                    menu: { resources: gather = [] }
                  }
                } = __v
                if (gather.length) {
                  gather.forEach((___v) => {
                    middleCollection.push(___v.code)
                  })
                }

                if (__v.children && __v.children.length) {
                  __v.children.forEach((value) => {
                    if (value.attributes.menu.resources && value.attributes.menu.resources.length) {
                      const {
                        attributes: {
                          menu: { resources: gather = [] }
                        }
                      } = value

                      if (gather.length) {
                        gather.forEach((_value) => {
                          middleCollection.push(_value.code)
                        })
                      }
                    }
                  })
                }
              })
            }
          })
        }
      })
      this.entityForm.resourceCodes = middleCollection
    },
    async saveEntityForm(formName) {
      const valid = await this.$refs[formName].validate()
      if (valid) {
        this.entityFormLoading = true
        const res = await Func.fetch(saveRole, this.entityForm)
        this.entityFormLoading = false
        if (res) {
          this.getList()
          this.myUserListQuery.roleCode = res.data.code
          this.userListQuery.roleCode = res.data.code
          this.userId = res.data.id
          this.dialogFormVisible = false
          this.$message({ showClose: true, message: '操作成功,请刷新页面', type: 'success' })
        }
      }
    },
    handleAddSelectionChange(val) {
      this.addUserList = val.map((v) => {
        return v.code
      })
    },
    handleNotMyUserSizeChange(val) {
      this.userListQuery.size = val
      this.getNotRoleUserList()
    },
    handleNotMyUserCurrentChange(val) {
      this.userListQuery.current = val
      this.getNotRoleUserList()
    },
    async saveUserList(type) {
      let res
      this.setUserLoading = true
      this.userListLoading = true
      if (type === 'add') {
        res = await Func.fetch(addRoleUser, {
          roleCode: this.myUserListQuery.roleCode,
          userCodeList: this.addUserList
        })
      } else {
        res = await Func.fetch(delRoleUser, {
          roleCode: this.myUserListQuery.roleCode,
          userCodeList: this.delUserList
        })
      }
      this.setUserLoading = false
      this.userListLoading = false
      if (res) {
        this.getRoleUserList()
        this.getNotRoleUserList()
      }
    },
    handleDelSelectionChange(val) {
      this.delUserList = val.map((v) => {
        return v.code
      })
    },
    handleMyUserSizeChange(val) {
      this.myUserListQuery.size = val
      this.getRoleUserList()
    },
    handleMyUserCurrentChange(val) {
      this.myUserListQuery.current = val
      this.getRoleUserList()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  width: 980px;
}

::v-deep .top-filter {
  // display: none;
  margin-top: 10px;
}

.resources-list {
  height: 360px;
  overflow: auto;

  .name_label {
    font-size: 16px !important;
  }

  .root {
    font-size: 16px;
    font-weight: 700;
    // margin-bottom: 10px;
  }

  .name {
    display: flex;

    .label {
      width: 160px;
      font-size: 14px;
    }

    .resource {
      flex: 1;

      label {
        float: left;
      }
    }
  }
}

.small-space {
  margin-top: 15px;
}

.form-edit-content {
  padding-bottom: 0px;

  .left-flex,
  .right-form {
    border: 1px solid #f1f1f2;
  }
}

.totalHandlerBtn {
  text-align: right;
}

.margin-r-35 {
  margin-right: 35px;
}

.filter-item {
  display: inline-table;
}
/* 隐藏打印按钮 */
.print-button {
  display: none !important;
}

/* 隐藏设置按钮 */
.settings-button {
  display: none !important;
}
</style>
