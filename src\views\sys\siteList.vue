<template>
  <div class="app-container">
    <div class="menu-tree" style="flex: 1;height: 100%; min-width: 300px; overflow: auto">
      <div class="search-bar">
        <el-input placeholder="输入关键字过滤" v-model="filterText">
        </el-input>
        <el-button type="primary" icon="el-icon-search" @click="getMenuTree" plain v-loading="loading"></el-button>
        <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="addMenu">增加
        </el-button>
      </div>
      <el-tree :data="menuTree" :props="defaultProps" :filter-node-method="filterNode" class="margin-top-10" default-expand-all
               node-key="id" :expand-on-click-node="true" ref="menuTree" :render-content="renderContent"></el-tree>
      <div class="grid-content bg-purple"></div>
    </div>
    <div style="flex: 4; height: 100%; position: relative;">
      <div class="btn-view">
        <el-button type="primary" :loading="menuFormLoading" @click="saveMenuForm('menuForm')">提交
        </el-button>
      </div>
      <!--<el-tabs type="Card">-->
      <el-card style="position: relative;height: 90%;">
        <div slot="header" style="font-weight: bold;font-size: 16px;">
          <span>基本信息</span>
        </div>
        <el-form :show-message="true" :status-icon="true" ref="menuForm" :model="menuForm" label-width="110px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="名称" prop="name" :rules="[{required:true, message:'请输入名称'}]">
                <el-input v-model="menuForm.name" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="部门代号" prop="name" :rules="[{required:true, message:'部门代号'}]">
                <el-input v-model="menuForm.no" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="上级机构" prop="parentCode" :rules="[{required:true, message:'请输入上级机构'}]">
                <div class="flex-row-center">
                  <site-select v-model="menuForm.parentCode" :name="menuForm.parentName" :isTopMenu="true"></site-select>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="联系人" prop="linkman">
                <el-input v-model="menuForm.linkman" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="电话" prop="phone">
                <div class="flex-row-center">
                  <el-input :isTopMenu="true" v-model="menuForm.phone"></el-input>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="手机" prop="mobile">
                <div class="flex-row-center">
                  <el-input :isTopMenu="true" v-model="menuForm.mobile"></el-input>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <!--<el-col :span="8">-->
            <!--<el-form-item label="省市区" prop="regionList" :rules="[{required:true, message:'请选择省市区'}]">-->
            <!--<region-select v-model="menuForm.regionList"></region-select>-->
            <!--</el-form-item>-->
            <!--</el-col>-->
            <el-col :span="24">
              <el-form-item label="地址" prop="address" :rules="[{required:true, message:'请输入地址'}]">
                <div class="flex-row-center">
                  <el-input v-model="menuForm.address"></el-input>
                  <!--<el-button style="height: 28px;float: right; margin-left: 2px;flex: 1"-->
                  <!--@click="addMap">选点-->
                  <!--</el-button>-->
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div id="map-container" style="display: none"></div>
      </el-card>
    </div>
  </div>
</template>
<script>
import Func from '@/utils/func'
import { saveSite, pageSites } from '@/api/siteList'

const menuForm = {
  id: '', // 行政区域编码
  code: '', // 编码
  parentCode: '', // 父级网点编码
  parentCodes: '', // 所有父级网点编码
  name: '', // 名称
  no: '',
  pinyin: '', // 拼音
  jianpin: '', // 简拼
  province: '',
  regionList: [],
  provinceName: '', // 省名称
  provinceCode: '', // 省名称
  cityCode: '', // 城市编码
  cityName: '', // 城市名称
  countyCode: '', // 区县编码
  countyName: '', // 区县名称
  areaCode: '', // 区域编码
  areaName: '', // 区域名称
  address: '', // 地址
  linkman: '', // 联系人
  phone: '', // 电话
  mobile: '', // 手机
  sort: '', // 排序
  remarks: '', // 备注
}
export default {
  name: 'siteList',
  data() {
    return {
      filterText: '',
      loading: false,
      menuFormLoading: false,
      menuTree: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      isShowPrivilegeTree: true, // 是否展示自定义授权树
      defaultPrivilegeExpandedKeys: [], // 自定义配置默认展开键
      defaultPrivilegeCheckedKeys: [], // 自定义配置默认选中键
      privilegeTreeData: [],
      privilegeGather: [],
      // privilegeTreeVisible: false,
      dialogTableVisible: false,
      menuForm: { ...menuForm },
      // isGainDataLoading: false
    }
  },
  watch: {
    filterText(val) {
      this.$refs.menuTree.filter(val)
    },
    menuForm(val) {},
  },
  methods: {
    // savePrivilege() {
    //     if (!this.privilegeGather.length) {
    //         this.$message({showClose: true, message: '至少选中一项', type: 'warning'})
    //         return false
    //     }
    //     this.privilegeTreeVisible = false
    // },
    /**
     * 选中权限
     * */
    // privilegeCheckedHandler(val, binding) {
    //     const {checkedNodes} = binding
    //     this.privilegeGather = checkedNodes
    //     // console.log(this.privilegeGather)
    // },
    // async privilege(val) {
    //     if (val) {
    //         // this.isGainDataLoading = true
    //         let res = await Func.fetch(findAllWithTreeFormatted)
    //         // this.isGainDataLoading = false
    //         if (res) {
    //             this.privilegeTreeData = res.data
    //             // console.log(res.data)
    //             // debugger
    //             if (this.privilegeTreeData.length > 0) {
    //                 this.defaultPrivilegeExpandedKeys = this.defaultPrivilegeCheckedKeys = this.menuForm.privilege.split(',')
    //                 this.privilegeGather = this.menuForm.privilege.split(',').map(v => ({code: v}))
    //             } else {
    //                 this.defaultPrivilegeExpandedKeys = this.defaultPrivilegeCheckedKeys = this.privilegeGather.map(v => v.code)
    //             }
    //         } else {
    //             this.defaultPrivilegeExpandedKeys = this.defaultPrivilegeCheckedKeys = this.privilegeGather.map(v => v.code)
    //         }
    //     } else {
    //         this.privilegeTreeData = []
    //     }
    // },
    // createPrivilege() {
    //     // this.privilegeTreeVisible = true
    //     this.privilege('update')
    // }, // 配置
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    getAdress(e) {},
    async getMenuTree() {
      this.loading = true
      const res = await Func.fetch(pageSites, {})
      this.loading = false
      if (res) {
        this.menuTree = res.data || []
      }
    },
    regionInfo(val) {
      this.$nextTick(() => {
        this.menuForm = { ...this.menuForm, ...val }
      })
    },
    addressInfoHandler(val) {
      this.menuForm.address = val
      let addressArray = {}
      addressArray = this.getArea(val)
      this.region[1] = addressArray.City
      this.region[0] = addressArray.Province
      this.region[2] = addressArray.Country
    },

    // handleCloseDialog1() {
    //     this.dialogTableVisible = false
    // },
    renderContent(h, { node, data }) {
      const menu = data.attributes.site || data.attributes.resource
      return h(
        'span',
        {
          style: {
            flex: 1,
            display: 'flex',
            'align-items': 'center',
            'justify-content': 'space-between',
            'padding-right': '8px',
          },
        },
        [
          h(
            'span',
            {
              class: {
                'el-tree-node__label': true,
              },
              // style: this.entityForm.siteCode === menu.code ? {
              //     'color': '#5d80e1',
              //     'font-weight': '600'
              // } : {}
            },
            data.label
          ),
          h('span', [
            h(
              'i',
              {
                class: {
                  'el-icon-search': true,
                },
                style: {
                  padding: '2px 4px',
                },
                on: {
                  click: (event) => {
                    event.stopPropagation()
                    this.editMenu(node.parent, menu)
                  },
                },
              },
              ''
            ),
            h(
              'i',
              {
                class: {
                  'el-icon-plus': true,
                },
                style: {
                  padding: '2px 4px',
                },
                on: {
                  click: (event) => {
                    event.stopPropagation()
                    this.addSubMenu(menu)
                  },
                },
              },
              ''
            ),
          ]),
        ]
      )
    }, // 左边tree样式
    async addSubMenu(menu) {
      this.isEditor = false
      this.menuFormInit()
      this.menuForm.parentCode = menu.code
      this.menuForm.parentName = menu.name
    },
    async editMenu(parent, menu) {
      // 左边tree编辑按钮
      // console.log(menu)
      this.menuForm.name = menu.name
      this.menuForm.code = menu.code
      this.menuForm.no = menu.no
      this.menuForm.parentCode = menu.parentCode
      this.menuForm.parentName = parent.data.label
      this.menuForm.regionList = [menu.provinceCode, menu.cityCode, menu.countyCode]
      this.menuForm.linkman = menu.linkman
      this.menuForm.mobile = menu.mobile
      this.menuForm.phone = menu.phone
      this.menuForm.id = menu.id
      this.menuForm.privilegeType = menu.privilegeType
      this.menuForm.privilege = menu.privilege
      this.menuForm.address = menu.address
      // this.privilege('update')
    },
    addMenu() {
      // 添加
      this.menuFormInit()
      this.menuForm.parentCode = 'root'
    },
    menuFormInit() {
      this.menuForm = { ...menuForm }
    },
    addressHandlerDot(val = '') {
      // 省市区
      this.$refs['mapComponent'].clearStatusHandler()
      this.menuForm.address = val
      this.menuForm.latitude = ''
      this.menuForm.longitude = ''
    },
    addressHandler(val = '') {
      this.$refs['mapComponent'].clearStatusHandler()
      this.menuForm.address = val
      this.menuForm.latitude = ''
      this.menuForm.longitude = ''
    },
    addressSelectHandlerDot(item) {
      const { address, adname, name, lat, lng } = item
      this.menuForm.address = address.length ? address + '(' + name + ')' : adname + '(' + name + ')'
      this.menuForm.latitude = lat
      this.menuForm.longitude = lng
      this.$refs['mapComponent'].setCentre(lng, lat)
    },
    async saveMenuForm(formName) {
      let privilege = void 0
      this.menuForm.provinceCode = this.menuForm.regionList[0]
      this.menuForm.cityCode = this.menuForm.regionList[1]
      this.menuForm.countyCode = this.menuForm.regionList[2]
      this.menuForm.areaCode = this.menuForm.regionList[2]
      const valid = await this.$refs[formName].validate()
      if (this.menuForm.privilegeType === 'CUSTOMER') {
        privilege = this.privilegeGather.map((v) => v.code).join(',')
      }
      const data = { ...this.menuForm, privilege }
      if (data.parentCode === 'root') {
        data.parentCode = ''
      }
      if (valid) {
        this.menuFormLoading = true
        const res = await Func.fetch(saveSite, data)
        this.menuFormLoading = false
        if (res) {
          this.getMenuTree()
          this.$message({ showClose: true, message: '提交成功', type: 'success' })
        }
      }
    },
  },
  created() {
    this.getMenuTree()
  },
}
</script>
<style lang="scss" scoped>
html,
body {
  height: 100%;
}

.app-container {
  display: flex;
  flex-direction: row;
  height: 100%;
}

.menu-tree {
  flex: 1;
  width: 300px;
  height: 100%;
  padding: 10px;
  margin: 0 5px;
  background: #ffffff;
  border: 1px solid #dcdfe6;
}

.parentName {
  font-size: 14px;
  color: red;
}

.el-form {
  padding: 10px 10px 0 10px;
  background: #ffffff;
  margin-bottom: 2px;
}

.el-tree {
  background: transparent;
}

.search-bar {
  display: flex;

  .el-input {
    flex: 1;
  }

  button {
    margin-left: 10px;
  }
}

.btn-view {
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 10px;
  text-align: right;
}

.flex-row-center {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.tab-content-dot {
  padding-right: 20px;
}

#map-container {
  width: 100px;
  height: 100px;
}

.margin-top-10 {
  margin-top: 10px;
}
</style>
