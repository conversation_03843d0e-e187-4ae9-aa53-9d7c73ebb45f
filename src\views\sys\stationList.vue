<template>
    <div class="app-container">
        <s-curd @selectItem="selectItem" ref="stationList" name="stationList" :model="model" :actions="actions"
                :list-query="listQuery"
                title="角色管理">
            <el-table-column label="岗位名称" slot="name" prop="name" width="180px">
                <template slot-scope="scope">
                    <el-button type="text" @click="handleEdit(scope.row)">{{scope.row.name}}</el-button>
                </template>
            </el-table-column>
        </s-curd>
        <el-dialog ref="dialogStatus" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
                   :before-close="handleClose"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   fullscreen
        >
            <el-tabs type="border-card" v-model="paneType" @tab-click="switchPane">
                <el-tab-pane name="basic">
                    <span slot="label"><i class="el-icon-date"></i> 基本信息</span>
                    <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm"
                             label-width="90px"
                             v-if="dialogFormVisible">
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="岗位名称" prop="name" label-width="140px"
                                              :rules="[{required:true, message:'请输入岗位名称'}]">
                                    <el-input v-model="entityForm.name" clearable></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="备注信息" prop="remarks" label-width="140px">
                                    <el-input v-model="entityForm.remarks" type="textarea" clearable></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item label="化验指标">
                            <el-table ref="testAssay" :data="testAssayList" border v-if="dialogFormVisible"
                                      @selection-change="handleSelectionChange">
                                <el-table-column
                                        type="selection"
                                        width="55">
                                </el-table-column>
                                <el-table-column
                                        prop="categoryName"
                                        label="类别"
                                        width="300">
                                </el-table-column>
                                <el-table-column
                                        prop="name"
                                        label="名称"
                                >
                                </el-table-column>
                                <el-table-column
                                        prop="mark"
                                        label="符号"
                                >
                                </el-table-column>
                                <el-table-column
                                        prop="unit"
                                        label="单位"
                                >
                                </el-table-column>
                            </el-table>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>

                <el-tab-pane name="setUser" v-if="userId">
                    <span slot="label"><i class="el-icon-date"></i> 设置用户</span>
                    <div class="form-edit-content">
                        <div class="left-flex">
                            <h3 class="content-title">全部用户列表</h3>
                            <div class="content">
                                <div class="filter-container">
                                    <el-input @keyup.enter.native="getNotStationUserList" style="width: 200px;"
                                              class="filter-item"
                                              placeholder="用户名称"
                                              v-model="userListQuery.userName">
                                    </el-input>
                                    <el-button class="filter-item" type="primary" icon="el-icon-search"
                                               @click="getNotStationUserList">查询
                                    </el-button>
                                </div>
                                <el-table
                                        :data="userList"
                                        height="65vh"
                                        v-loading="userListLoading"
                                        element-loading-text="加载中..."
                                        stripe
                                        border
                                        highlight-current-row
                                        @selection-change="handleAddSelectionChange"
                                >
                                    <el-table-column
                                            type="selection"
                                            width="40">
                                    </el-table-column>
                                    <el-table-column label="用户名称" prop="name" width="120"></el-table-column>
                                    <el-table-column label="登录名" prop="loginName" width="120"></el-table-column>
                                    <el-table-column label="手机号码" prop="mobile"></el-table-column>
                                </el-table>
                                <div v-show="!userListLoading" class="pagination-container">
                                    <el-pagination @size-change="handleNotMyUserSizeChange"
                                                   @current-change="handleNotMyUserCurrentChange"
                                                   :current-page.sync="userListQuery.page"
                                                   :layout="pagination.layout"
                                                   :page-sizes="pagination.pageSizes"
                                                   :page-size="userListQuery.size"
                                                   :total="totalUser">
                                    </el-pagination>
                                </div>
                            </div>
                        </div>
                        <div class="mid-btn">
                            <el-button type="primary" icon="el-icon-arrow-right" style="margin: 10px 0"
                                       :disabled="setUserLoading || addUserList.length === 0 "
                                       @click="saveUserList('add')"
                            ></el-button>
                            <br>
                            <el-button type="danger" icon="el-icon-arrow-left"
                                       :disabled="setUserLoading || delUserList.length === 0 "
                                       @click="saveUserList('del')"
                            ></el-button>
                        </div>
                        <div class="right-form">
                            <h3 class="content-title">当前用户列表</h3>
                            <div class="content">
                                <div class="filter-container">
                                    <el-input @keyup.enter.native="getStationUserList" style="width: 200px;"
                                              class="filter-item"
                                              placeholder="用户名称"
                                              v-model="myUserListQuery.userName">
                                    </el-input>
                                    <el-button class="filter-item" type="primary" icon="el-icon-search"
                                               @click="getStationUserList">查询
                                    </el-button>
                                </div>
                                <el-table
                                        :data="myUserList"
                                        height="65vh"
                                        v-loading="setUserLoading"
                                        element-loading-text="加载中..."
                                        stripe
                                        border
                                        highlight-current-row
                                        @selection-change="handleDelSelectionChange"
                                >
                                    <el-table-column
                                            type="selection"
                                            width="40">
                                    </el-table-column>
                                    <el-table-column label="用户名称" prop="name" width="120"></el-table-column>
                                    <el-table-column label="登录名" prop="loginName" width="120"></el-table-column>
                                    <el-table-column label="手机号码" prop="mobile"></el-table-column>
                                </el-table>
                                <div v-show="!setUserLoading" class="pagination-container">
                                    <el-pagination @size-change="handleMyUserSizeChange"
                                                   @current-change="handleMyUserCurrentChange"
                                                   :current-page.sync="myUserListQuery.page"
                                                   :layout="pagination.layout"
                                                   :page-sizes="pagination.pageSizes"
                                                   :page-size="myUserListQuery.size"
                                                   :total="totalMyUser">
                                    </el-pagination>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" :loading="entityFormLoading" @click="saveEntityForm('entityForm')">保存
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {mapGetters} from 'vuex'
    import {pagination} from '@/const'
    import Model from '@/model/sys/stationList'
    import Cache from '@/utils/cache'
    import ActModel from '@/model/sys/actualTest'

    const userForm = {
        'id': '',
        'name': '',
        'remarks': '',
        'resourceCodes': []
    }
    const myUserListQuery = {
        current: 1,
        size: pagination.pageSize,
        orderBy: 'createDate',
        orderDir: 'desc',
        stationCode: '',
        userName: ''
    }
    const userListQuery = {
        current: 1,
        size: pagination.pageSize,
        orderBy: 'createDate',
        orderDir: 'desc',
        stationCode: '',
        userName: ''
    }
    export default {
        name: 'stationList',
        data() {
            return {
                option: Model.tableOption,
                model: Model,
                assayDict: [],
                assayList: [],
                listQuery: {
                    orderBy: 'createDate',
                    orderDir: 'desc'
                },
                entityFormLoading: false,
                menus: [],
                testAssayList: [],
                userId: '',
                textMap: {
                    update: '编辑',
                    create: '创建'
                },
                entityForm: {...userForm},
                dialogStatus: 'create',
                dialogFormVisible: false,
                dialogVisible: true,
                paneType: 'basic',
                actions: [
                    {
                        config: {
                            type: 'primary',
                            plain: true,
                            icon: 'el-icon-plus'
                        },
                        show: 'stationList:save',
                        click: this.handleCreate,
                        label: '新增'
                    },
                    {
                        config: {
                            type: 'danger',
                            plain: true,
                            icon: 'el-icon-delete'
                        },
                        show: 'stationList:save',
                        click: this.handleDelete,
                        label: '删除'
                    }
                ],
                myUserList: [],
                setUserLoading: false,
                myUserpagination: pagination,
                myUserListQuery: {
                    ...myUserListQuery
                },
                userListLoading: false,
                userListpagination: pagination,
                userListQuery: {
                    ...userListQuery
                },
                totalUser: null,
                addUserList: [],
                delUserList: [],
                userList: [],
                selectList: [],
                pagination: pagination,
                selectAssayList: [],
                totalMyUser: null
            }
            // model: new RoleList()
        },
        computed: {
            ...mapGetters([
                'perms'
            ])
        },
        created() {
            this.loadMenuTree()
        },
        methods: {
            selectItem(list) {
                this.selectList = list
            },
            async handleDelete() {
                if (this.selectList.length !== 1) {
                    this.$notify.error('请勾选一条记录')
                    return false
                }
                try {
                    await this.$confirm('确认删除?')
                    const id = this.selectList[0].id
                    const res = await this.model.delById(id)
                    if (res) {
                        this.$notify.success('删除成功')
                        this.getList()
                    }
                } catch (e) {
                }
            },
            // 化验指标
            handleSecondLevel(e) {
            },
            handleEdit(row) {
                // this.loadMenuTree()
                this.dialogStatus = 'update'
                this.paneType = 'basic'
                Model.getById(row.id).then(res => {
                    if (res) {
                        this.dialogFormVisible = true
                        this.userId = res.data.id
                        this.myUserListQuery.stationCode = res.data.code
                        this.userListQuery.stationCode = res.data.code
                        this.entityForm = {...res.data}
                        let list = JSON.parse(JSON.stringify(res.data.indexMetaList))
                        list.forEach(item => {
                            this.testAssayList.map(asc => {
                                if (item.id === asc.id) {
                                    asc.select = 'Y'
                                }
                            })
                        })
                        this.checkboxInit()
                    }
                }).catch(e => {
                    this.$message({type: 'info', message: '编辑操作异常'})
                })
            },
            checkboxInit() {
                let that = this
                that.$nextTick(() => {
                    if (that.$refs.testAssay) {
                        that.$refs.testAssay.clearSelection()
                    }
                    that.testAssayList.forEach(row => {
                        if (row.select === 'Y') {
                            that.$refs.testAssay.toggleRowSelection(
                                row,
                                true
                            )
                        }
                    })
                })
            },
            getList() {
                this.$refs.stationList.$emit('refresh')
            },
            handleCreate() {
                this.dialogStatus = 'create'
                this.paneType = 'basic'
                this.dialogVisible = true
                this.dialogFormVisible = true
                this.loadMenuTree()
            },
            // 关闭弹窗
            handleClose() {
                this.resetVarbiant()
            },
            handleAddSelectionChange(val) {
                this.addUserList = val.map(v => {
                    return v.code
                })
            },
            // 右边翻页操作
            handleNotMyUserSizeChange(val) {
                this.userListQuery.size = val
                this.getNotStationUserList()
            },
            handleNotMyUserCurrentChange(val) {
                this.userListQuery.current = val
                this.getNotStationUserList()
            },
            checkNone() {
                this.testAssayList.map(item => {
                    item.select = 'N'
                    return item
                })
            },
            /**
             * 重置数据
             */
            resetVarbiant() {
                this.checkNone()
                this.entityFormLoading = false
                this.paneType = 'basic'
                this.userId = ''
                this.dialogFormVisible = false
                this.userListQuery = {...userListQuery}
                this.myUserListQuery = {...myUserListQuery}
                this.entityForm = {...userForm}
            },
            // async loadMenuTree() {
            //     this.assayDict = Cache.get('dict')['s_assay_type']
            //     let res = await entrustOrder.getItemApi()
            //     if (res.data) {
            //         let list = res.data
            //         list.map(item => {
            //             item.select = 'N'
            //             return item
            //         })
            //         this.assayList = list
            //     }
            // },
            /**
             * 切换pane
             * */
            switchPane(tab) {
                this.paneType = tab.paneName
                if (tab.paneName === 'setUser') {
                    this.setUserList()
                }
            },
            /*
            * 设置用户列表
            * */
            setUserList() {
                this.getStationUserList()
                this.getNotStationUserList()
            },
            /*
           * 获取角色用户列表(右侧的当前用户列表)
           * */
            async getStationUserList() {
                this.setUserLoading = true
                const res = await Model.findByCode(this.myUserListQuery)
                this.setUserLoading = false
                if (res) {
                    this.myUserList = res.data
                    this.totalMyUser = res.data.length || 0
                }
            },
            /*
            * 获取不是角色用户列表(左侧的全部用户列表)
            * */
            async getNotStationUserList() {
                this.userListLoading = true
                const res = await Model.findNotInCode(this.userListQuery)
                this.userListLoading = false
                if (res) {
                    this.userList = res.data
                    this.totalUser = res.data.length || 0
                }
            },
            cancelHandler() {
                this.entityForm.resourceCodes = []
            },
            async saveEntityForm() {
                let res = await Model.savePage({...this.entityForm, indexMetaList: this.selectAssayList})
                if (res.data) {
                    this.userId = res.data.id
                    this.getList()
                    this.$message.success('保存信息成功')
                }
            },
            async saveUserList(type) {
                let res
                this.setUserLoading = true
                this.userListLoading = true
                if (type === 'add') {
                    res = await Model.addUser({
                        stationCode: this.myUserListQuery.stationCode,
                        userCodeList: this.addUserList
                    })
                } else {
                    res = await Model.delUser({
                        stationCode: this.myUserListQuery.stationCode,
                        userCodeList: this.delUserList
                    })
                }
                this.setUserLoading = false
                this.userListLoading = false
                if (res) {
                    this.getStationUserList()
                    this.getNotStationUserList()
                }
            },
            handleDelSelectionChange(val) {
                this.delUserList = val.map(v => {
                    return v.code
                })
            },
            handleMyUserSizeChange(val) {
                this.myUserListQuery.size = val
                this.getStationUserList()
            },
            handleMyUserCurrentChange(val) {
                this.myUserListQuery.current = val
                this.getStationUserList()
            },
            // 加载el-table数据
            async loadMenuTree() {
                this.assayDict = Cache.get('dict')['s_assay_type']
                let res = await ActModel.pageList()
                if (res.data) {
                    let list = res.data
                    list.map(item => {
                        item.select = 'N'
                        return item
                    })
                    this.testAssayList = list
                }
            },
            // 勾选table的数据
            handleSelectionChange(list) {
                this.selectAssayList = [...list]
            }
        }
        // }
    }
</script>
<style lang="scss" scoped>
    .mid-btn {
        padding-top: 40vh;
        width: 48px;
        text-align: center;
    }

    .resources-list {
        min-height: 360px;
        overflow: auto;

        .root {
            font-size: 16px;
            font-weight: 700;
        }

        .name {
            display: flex;

            .label {
                width: 140px;
                font-size: 14px;
            }

            .resource {
                flex: 1;

                label {
                    float: left;
                }
            }
        }
    }

    .small-space {
        margin-top: 15px;
    }

    .form-edit-content {
        padding-bottom: 0px;
    }

    .totalHandlerBtn {
        text-align: right;
    }

    .margin-r-35 {
        margin-right: 35px;
    }

    ::v-deep .el-dialog__body {
        padding: 5px 20px
    }

    ::v-deep .el-dialog__footer {
        padding: 5px 20px
    }
</style>
