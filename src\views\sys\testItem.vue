<template>
    <div class="app-container">
        <s-curd ref="actualTest" @selectItem="selectItem" name="actualTest" :model="model" :list-query="listQuery"
                :actions="actions"
                title="实际化验项">
            <el-table-column label="名称" slot="name" prop="name" width="300px">
                <template slot-scope="scope">
                    <el-button type="text" @click="handleEdit(scope.row)">{{scope.row.name}}</el-button>
                </template>
            </el-table-column>
        </s-curd>
        <el-dialog ref="dialogStatus" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
                   :before-close="handleClose"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   width="60%"
        >
            <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm"
                     label-width="90px"
                     v-if="dialogFormVisible">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="名称" prop="name" :rules="[{required:true, message:'请输入名称'}]">
                            <el-input v-model="entityForm.name" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="类型" prop="type" :rules="[{required:true, message:'请选择类型'}]">
                            <dict-select v-model="entityForm.type" clearable type="s_assay_type"></dict-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="排序" prop="sort" :rules="[{required:true, message:'请输入排序'}]">
                            <el-input v-model="entityForm.sort" type="number"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注信息" prop="remarks">
                            <el-input v-model="entityForm.remarks" type="textarea" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" :loading="entityFormLoading" @click="handleSave('entityForm')">保存
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {mapGetters} from 'vuex'
    import Model from '@/model/sys/testItem'
    import DictSelect from '@/components/DictSelect/index'

    export default {
        name: 'actualTest',
        components: {DictSelect},
        data() {
            return {
                dialogFormVisible: false,
                entityForm: {},
                textMap: {
                    update: '编辑',
                    create: '创建'
                },
                selectList: [],
                dialogStatus: '',
                entityFormLoading: false,
                actions: [
                    {
                        config: {
                            type: 'primary',
                            plain: true,
                            icon: 'el-icon-plus'
                        },
                        click: this.handleCreate,
                        show: 'testItem:save',
                        label: '新增'
                    },
                    {
                        config: {
                            type: 'danger',
                            plain: true,
                            icon: 'el-icon-delete'
                        },
                        click: this.handleDelete,
                        show: 'testItem:delete',
                        label: '删除'
                    }
                ],
                model: Model,
                listQuery: {
                    orderBy: 'createDate',
                    orderDir: 'desc'
                }
            }
        },
        computed: {
            ...mapGetters([
                'perms'
            ])
        },
        methods: {
            selectItem(list) {
                this.selectList = list
            },
            // 删除信息
            async handleDelete() {
                if (this.selectList.length === 0) {
                    this.$notify.error('请选择一条记录')
                    return false
                }
                try {
                    await this.$confirm('确认删除?')
                    const ids = this.selectList.map(val => val.id)
                    const res = await this.model.delete(ids)
                    if (res) {
                        this.$notify.success('删除成功')
                        this.getList()
                    }
                } catch (e) {
                }
            },
            // 刷新页面
            getList() {
                this.$refs.actualTest.$emit('refresh')
            },
            // 新建
            handleCreate() {
                this.dialogStatus = 'create'
                this.dialogFormVisible = true
            },
            // 保存
            async handleSave() {
                this.entityFormLoading = true
                let res = await Model.save(this.entityForm)
                this.entityFormLoading = false
                if (res.data) {
                    this.$message.success('保存信息成功')
                    this.getList()
                    this.handleClose()
                }
            },
            // 修改
            handleEdit(row) {
                this.entityForm = {...row}
                this.dialogFormVisible = true
                this.dialogStatus = 'update'
            },
            // 关闭弹窗
            handleClose() {
                this.entityForm = {}
                this.dialogFormVisible = false
            }
        }
    }
</script>
