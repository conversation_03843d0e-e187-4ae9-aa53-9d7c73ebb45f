<template>
  <div class="app-container">
    <s-curd @selectItem="selectItem" ref="userList" name="userList" :model="model" :list-query="listQuery" :actions="actions"
            otherHeight="140" title="人员管理">
      <el-table-column label="姓名" slot="name" prop="name" align="center" width="180px">
        <template slot-scope="scope">
          <el-button type="text" v-if="checkPermission(['userList:update'])" @click="handleEdit(scope.row)">
            {{ scope.row.name }}
          </el-button>
          <span v-else>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="煤岩时间" prop="coalRockDate" align="center" width="150px">
        <template slot-scope="scope">
          {{ scope.row.coalRockDate }}
        </template>
      </el-table-column>
    </s-curd>
    <el-dialog v-if="dialogFormVisible" ref="dialogStatus" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
               :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false" width="60%">
      <el-form :show-message="true" :status-icon="true" ref="entityForm" :model="entityForm" label-width="120px"
               style="margin-top: 20px" :rules="rules">
        <el-row>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="entityForm.name" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工厂" prop="factoryCode">
              <el-select v-model="entityForm.factoryCodeList" multiple @change="handleFactoryListChange">
                <el-option v-for="item in factoryNameList" :key="item.id" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色" prop="roleCodes">
              <SnSimpleSelect v-model="entityForm.roleCodes" multiple :props="{
                  label: 'name',
                  value: 'code',
                }" v-bind="{ list: roleList }" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门" prop="deptId">
              <Treeselect style="height: 28px" v-model="entityForm.deptId" :options="menuOptions" :normalizer="normalizer"
                          :show-count="true" placeholder="选择上级菜单" />
              <!--                            <el-input v-model="entityForm.deptId" clearable></el-input>-->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="登录名" prop="loginName">
              <el-input v-model="entityForm.loginName" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input v-model="entityForm.password" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止日期" prop="expireDate">
              <date-select v-model="entityForm.expireDate" clearable></date-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工号" prop="no">
              <el-input v-model="entityForm.no" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="phone">
              <el-input v-model="entityForm.phone" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="煤岩时间" prop="coalRockDate">
              <date-select v-model="entityForm.coalRockDate" clearable></date-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机" prop="mobile">
              <el-input v-model="entityForm.mobile" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="entityForm.email" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="入职时间" prop="hiredate">
              <date-select v-model="entityForm.hiredate" clearable></date-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="离职时间" prop="dimissionDate">
              <date-select v-model="entityForm.dimissionDate" clearable></date-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
                      <el-form-item label="状态" prop="state">
                        <dict-select v-model="entityForm.state" type="s_user_state" name="switch" clearable></dict-select>
                      </el-form-item>
                    </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="住址" prop="address">
              <el-input v-model="entityForm.address" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应组" prop="groupName">
              <!-- <el-select v-model="headSupplyGroupEntity.active" placeholder="供应组" filterable clearable
                                       @change="handleSupplyGroup" style="width:100%" :disabled="dialogStatus==='review'?true:false">
                              <el-option v-for="item in headSupplyGroupEntity.options" :key="item.value" :label="item.label" :value="item">
                              </el-option>
                            </el-select> -->
              <el-autocomplete v-model="entityForm.groupName" clearable ref="autocomplete" value-key="label" style="width: 100%"
                               @clear="blurForBug" :fetch-suggestions="querySearch" placeholder="请输入用供应组" @input="handleInput"
                               @select="handlechangeInput"></el-autocomplete>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息" prop="remarks">
              <el-input v-model="entityForm.remarks" type="textarea" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="entityFormLoading" @click="handleSave('entityForm')">保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import Model from "@/model/sys/userList";
import DictSelect from "@/components/DictSelect/index";
import SupplyGroup from "@/model/SupplyGroup/SupplyGroup";
import { treeDepts } from "@/api/common/listAllTree";
import { listRole, listFactoryName } from "@/api/common/listAllSimple";
import SnSimpleSelect from "@/components/Common/SnSimpleSelect/index.vue";
import checkPermission from "@/utils/permission";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: "roleList",
  components: { SnSimpleSelect, DictSelect, Treeselect },
  data() {
    return {
      entityForm: {
        state: "ZC",
        delFlag: "N",
        // name: "",
        // state: "ZC",
        // delFlag: "N",
        // roleCodes: [],
        // deptId: "",
        factoryCodeList: [],
        factoryNameList: [],
        coalRockDate: "",
        // password: "",
        // loginName: "",
        // no: "",
        // phone: "",
        // mobile: "",
        // email: "",
        // hiredate: "",
        // hiredate: "",
        // address: "",
        // groupName: "",
        // groupId: "",
        // remarks: "",
      },
      factoryNameList: [],
      dialogFormVisible: false,
      // entityForm: {
      //   state: "ZC",
      // },
      selectList: [],
      rules: {
        factoryCodeList: [
          {
            type: "array", // 确保验证类型为数组
            required: true,
            message: "请至少选择一个工厂",
            trigger: "change",
          },
        ],
        name: { required: true, message: "请输入姓名", trigger: "change" },
        // roleCodes: { required: true, message: "请选择角色", trigger: "change" },
        roleCodes: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个角色",
            trigger: "change",
          },
        ],
        deptId: { required: true, message: "请选择部门", trigger: "change" },
        loginName: {
          required: true,
          message: "请输入登录名",
          trigger: "change",
        },
        password: { required: true, message: "请输入密码", trigger: "change" },
        expireDate: { required: true, message: "请输入截止日期", trigger: "change" },
      },
      textMap: {
        update: "编辑",
        create: "创建",
      },
      dialogStatus: "",
      entityFormLoading: false,
      headSupplyGroupEntity: { options: [], active: [] },
      model: Model,
      listQuery: {
        orderBy: "createDate",
        orderDir: "desc",
      },
      menuOptions: [],
      roleList: [],
    };
  },
  computed: {
    ...mapGetters(["perms"]),
    actions() {
      return checkPermission(["userList:save"])
        ? [
          {
            config: {
              type: "primary",
              plain: true,
              icon: "el-icon-plus",
            },
            click: this.handleCreate,
            show: "userList:save",
            label: "新增",
          },
          {
            config: {
              type: "primary",
              plain: true,
              icon: "el-icon-delete",
            },
            click: this.handleDelete,
            show: "userList:delete",
            label: "删除",
          },
        ]
        : [];
    },
  },
  created() {
    //获取供应组列表
    this.getSupplyGroup();
    this.setTreeDepts();
    this.setRoleList();
    this.setFactoryNameList();
  },
  methods: {
    // getDefaultEntityForm() {
    //   return {
    //     name: "",
    //     state: "ZC",
    //     delFlag: "N",
    //     roleCodes: [],
    //     deptId: "",
    //     factoryCodeList: [], // 改为数组形式
    //     factoryNameList: [], // 同步改为数组
    //     password: "",
    //     loginName: "",
    //     no: "",
    //     phone: "",
    //     mobile: "",
    //     email: "",
    //     hiredate: "",
    //     hiredate: "",
    //     address: "",
    //     groupName: "",
    //     groupId: "",
    //     remarks: "",
    //   };
    // },
    resetForm() {
      this.entityForm = {
        factoryCodeList: [], // 默认空数组
        factoryNameList: [],
        coalRockDate: "",
        state: "ZC",
        delFlag: "N"
      };
    },
    handleFactoryListChange(selectedCodes) {
      // 根据选中的codes匹配名称数组
      this.entityForm.factoryNameList = this.factoryNameList
        .filter((item) => selectedCodes.includes(item.code))
        .map((item) => item.name);
    },
    checkPermission,
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.code,
        label: node.label,
        children: node.children,
      };
    },
    async setTreeDepts() {
      try {
        const { data } = await treeDepts();
        this.menuOptions = data;
      } catch (e) { }
    },
    async setRoleList() {
      try {
        const { data } = await listRole();
        // this.roleList = data;
        this.roleList = data.map((item) => ({
          code: item.code,
          name: item.name,
        }));
      } catch (e) { }
    },
    async setFactoryNameList() {
      try {
        const params = {
          current: 1,
          size: 1000,
          // total: 0,
        };
        const { data } = await listFactoryName(params);
        console.log("工厂", data);
        this.factoryNameList = data.records.map((item) => ({
          code: item.code,
          name: item.name,
        }));
        console.log("factoryNameList", this.factoryNameList);
      } catch (e) {
        console.error("工厂数据加载失败:", e);
        // this.factoryNameList = [];
      }
    },
    async handlechangeInput(val) {
      this.entityForm.groupName = val.label;
      this.entityForm.groupId = val.value;
    },
    handleInput(val) {
      if (val) {
        this.entityForm.groupName = val;
        this.entityForm.groupId = "";
      }
    },
    querySearch(queryString, cb) {
      let list = this.headSupplyGroupEntity.options;
      let results = queryString
        ? list.filter(this.createFilter(queryString))
        : list;
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) =>
        restaurant.label.toString().includes(queryString.toString());
    },
    blurForBug() {
      this.entityForm.groupId = "";
    },
    async getSupplyGroup() {
      try {
        let options = [];
        const { data } = await SupplyGroup.page({ size: 1000 });
        if (data.records.length) {
          data.records.forEach((item) => {
            const { name: label, id: value } = item;
            options.push({ label, value });
          });
        }
        console.log(options);
        this.headSupplyGroupEntity.options = options;
      } catch (e) { }
    },
    selectItem(list) {
      this.selectList = list;
    },
    // 删除信息
    async handleDelete() {
      if (this.selectList.length === 0) {
        this.$notify.error("请选择一条记录");
        return false;
      }
      try {
        await this.$confirm("确认删除?");
        const ids = this.selectList.map((val) => val.id);
        const res = await this.model.delete(...ids);
        if (res) {
          this.$notify.success("删除成功");
          this.getList();
        }
      } catch (e) { }
    },
    getSite(e) {
      this.entityForm.siteName = e.siteName;
      this.entityForm.siteCode = e.siteCode;
    },
    // 刷新页面
    getList() {
      this.$refs.userList.$emit("refresh");
    },
    // 新建
    handleCreate() {
      this.dialogStatus = "create";
      this.dialogFormVisible = true;
    },
    // 保存
    async handleSave() {
      try {
        this.entityFormLoading = true;
        await this.$refs.entityForm.validate();
        let res = await Model.save(this.entityForm);
        if (res.data) {
          this.$message.success("保存信息成功");
          this.getList();
          this.handleClose();
        }
      } catch (e) {
      } finally {
        this.entityFormLoading = false;
      }
    },
    // 修改
    handleEdit(row) {
      const roleCodes = row.roleCodes.split(",").filter((val) => val);
      this.entityForm = {
        ...row,
        factoryCodeList: row.factoryCode ? row.factoryCode.split(",") : [], // 假设后端返回的是逗号分隔的字符串
        roleCodes: row.roleCodes ? row.roleCodes.split(",") : [], // 转换角色代码为数组
      };
      this.dialogFormVisible = true;
      this.dialogStatus = "update";
    },

    // 关闭弹窗
    handleClose() {
      this.entityForm = { state: "ZC", delFlag: "N" };
      this.dialogFormVisible = false;
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep .top-filter {
  // display: none;
  margin-top: 10px;
}
</style>