const isDev = process.env.NODE_ENV === 'development'

function configureWebpackConfig() {
    return isDev
        ? {devtool: 'source-map'}
        : {
            optimization: {
                minimizer: []
            }
        }
}


module.exports = {
    publicPath: './',
    productionSourceMap: process.env.NODE_ENV !== 'production',
    css: {
        sourceMap: false
    },
    devServer: {
        allowedHosts: ['zl.tpt365.com', 'admin.tpt365.cn'],
        port: 4004,
        overlay: {
            warnings: false,
            errors: false
        }
    },
    // 是否使用 `autoDLLPlugin` 分割供应的包？
    // 也可以是一个在 DLL 包中引入的依赖的显性的数组。
    // 查阅 https://github.com/vuejs/vue-doc-zh-cn/vue-cli/cli-service.md#dll-模式
    lintOnSave: process.env.NODE_ENV !== 'production' ? 'error' : false,
    configureWebpack: {
        ...configureWebpackConfig(),
        // devtool: 'source-map',
        devtool: '',
        externals: {
            // echarts: 'echarts'
        },
        // devtool: 'source-map',
        resolve: {
            // .mjs needed for https://github.com/graphql/graphql-js/issues/1272
            extensions: ['*', '.mjs', '.js', '.vue', '.json', '.gql', '.graphql']
        },
        module: {
            rules: [
                // fixes https://github.com/graphql/graphql-js/issues/1272
                {
                    test: /\.mjs$/,
                    include: /node_modules/,
                    type: 'javascript/auto'
                }
            ]
        }
    },
    // pwa: {
    //     name: '365TMS',
    //     themeColor: '#444444',
    //     msTileColor: '#000000',
    //     appleMobileWebAppCapable: 'yes',
    //     appleMobileWebAppStatusBarStyle: 'black',
    //     workboxPluginMode: 'InjectManifest',
    //     workboxOptions: {
    //         swSrc: './src/sw.js',
    //         importWorkboxFrom: 'local',
    //         exclude: [/^manifest.*\.js(?:on)?$/, /\.html$/]
    //     }
    // },
    chainWebpack(config) {//预加载的，去掉的话，会加载很多js
        config.plugins.delete('preload') // TODO: need test
        config.plugins.delete('prefetch') // TODO: need test
    }
}
